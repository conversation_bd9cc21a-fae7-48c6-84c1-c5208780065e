<?xml version="1.0" encoding="UTF-8"?><!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Strict//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-strict.dtd"><html xmlns="http://www.w3.org/1999/xhtml" lang="en"><head><meta http-equiv="Content-Type" content="text/html;charset=UTF-8"/><link rel="stylesheet" href="../jacoco-resources/report.css" type="text/css"/><link rel="shortcut icon" href="../jacoco-resources/report.gif" type="image/gif"/><title>WordConversionConfig</title><script type="text/javascript" src="../jacoco-resources/sort.js"></script></head><body onload="initialSort(['breadcrumb'])"><div class="breadcrumb" id="breadcrumb"><span class="info"><a href="../jacoco-sessions.html" class="el_session">Sessions</a></span><a href="../index.html" class="el_report">dms-svc</a> &gt; <a href="index.html" class="el_package">com.ascentbusiness.dms_svc.config</a> &gt; <span class="el_class">WordConversionConfig</span></div><h1>WordConversionConfig</h1><table class="coverage" cellspacing="0" id="coveragetable"><thead><tr><td class="sortable" id="a" onclick="toggleSort(this)">Element</td><td class="down sortable bar" id="b" onclick="toggleSort(this)">Missed Instructions</td><td class="sortable ctr2" id="c" onclick="toggleSort(this)">Cov.</td><td class="sortable bar" id="d" onclick="toggleSort(this)">Missed Branches</td><td class="sortable ctr2" id="e" onclick="toggleSort(this)">Cov.</td><td class="sortable ctr1" id="f" onclick="toggleSort(this)">Missed</td><td class="sortable ctr2" id="g" onclick="toggleSort(this)">Cxty</td><td class="sortable ctr1" id="h" onclick="toggleSort(this)">Missed</td><td class="sortable ctr2" id="i" onclick="toggleSort(this)">Lines</td><td class="sortable ctr1" id="j" onclick="toggleSort(this)">Missed</td><td class="sortable ctr2" id="k" onclick="toggleSort(this)">Methods</td></tr></thead><tfoot><tr><td>Total</td><td class="bar">168 of 299</td><td class="ctr2">43%</td><td class="bar">35 of 38</td><td class="ctr2">7%</td><td class="ctr1">23</td><td class="ctr2">37</td><td class="ctr1">7</td><td class="ctr2">14</td><td class="ctr1">4</td><td class="ctr2">18</td></tr></tfoot><tbody><tr><td id="a1"><a href="WordConversionConfig.java.html#L11" class="el_method">equals(Object)</a></td><td class="bar" id="b0"><img src="../jacoco-resources/redbar.gif" width="120" height="10" title="84" alt="84"/></td><td class="ctr2" id="c14">0%</td><td class="bar" id="d0"><img src="../jacoco-resources/redbar.gif" width="120" height="10" title="26" alt="26"/></td><td class="ctr2" id="e1">0%</td><td class="ctr1" id="f0">14</td><td class="ctr2" id="g0">14</td><td class="ctr1" id="h1">1</td><td class="ctr2" id="i2">1</td><td class="ctr1" id="j0">1</td><td class="ctr2" id="k0">1</td></tr><tr><td id="a4"><a href="WordConversionConfig.java.html#L58" class="el_method">getMaxFileSizeFormatted()</a></td><td class="bar" id="b1"><img src="../jacoco-resources/redbar.gif" width="87" height="10" title="61" alt="61"/></td><td class="ctr2" id="c15">0%</td><td class="bar" id="d1"><img src="../jacoco-resources/redbar.gif" width="27" height="10" title="6" alt="6"/></td><td class="ctr2" id="e2">0%</td><td class="ctr1" id="f1">4</td><td class="ctr2" id="g1">4</td><td class="ctr1" id="h0">7</td><td class="ctr2" id="i0">7</td><td class="ctr1" id="j1">1</td><td class="ctr2" id="k1">1</td></tr><tr><td id="a16"><a href="WordConversionConfig.java.html#L11" class="el_method">toString()</a></td><td class="bar" id="b2"><img src="../jacoco-resources/redbar.gif" width="21" height="10" title="15" alt="15"/></td><td class="ctr2" id="c16">0%</td><td class="bar" id="d3"/><td class="ctr2" id="e3">n/a</td><td class="ctr1" id="f3">1</td><td class="ctr2" id="g3">1</td><td class="ctr1" id="h2">1</td><td class="ctr2" id="i3">1</td><td class="ctr1" id="j2">1</td><td class="ctr2" id="k2">1</td></tr><tr><td id="a8"><a href="WordConversionConfig.java.html#L11" class="el_method">hashCode()</a></td><td class="bar" id="b3"><img src="../jacoco-resources/redbar.gif" width="7" height="10" title="5" alt="5"/><img src="../jacoco-resources/greenbar.gif" width="97" height="10" title="68" alt="68"/></td><td class="ctr2" id="c13">93%</td><td class="bar" id="d2"><img src="../jacoco-resources/redbar.gif" width="13" height="10" title="3" alt="3"/><img src="../jacoco-resources/greenbar.gif" width="13" height="10" title="3" alt="3"/></td><td class="ctr2" id="e0">50%</td><td class="ctr1" id="f2">3</td><td class="ctr2" id="g2">4</td><td class="ctr1" id="h4">0</td><td class="ctr2" id="i4">1</td><td class="ctr1" id="j4">0</td><td class="ctr2" id="k3">1</td></tr><tr><td id="a0"><a href="WordConversionConfig.java.html#L11" class="el_method">canEqual(Object)</a></td><td class="bar" id="b4"><img src="../jacoco-resources/redbar.gif" width="4" height="10" title="3" alt="3"/></td><td class="ctr2" id="c17">0%</td><td class="bar" id="d4"/><td class="ctr2" id="e4">n/a</td><td class="ctr1" id="f4">1</td><td class="ctr2" id="g4">1</td><td class="ctr1" id="h3">1</td><td class="ctr2" id="i5">1</td><td class="ctr1" id="j3">1</td><td class="ctr2" id="k4">1</td></tr><tr><td id="a17"><a href="WordConversionConfig.java.html#L11" class="el_method">WordConversionConfig()</a></td><td class="bar" id="b5"><img src="../jacoco-resources/greenbar.gif" width="30" height="10" title="21" alt="21"/></td><td class="ctr2" id="c0">100%</td><td class="bar" id="d5"/><td class="ctr2" id="e5">n/a</td><td class="ctr1" id="f5">0</td><td class="ctr2" id="g5">1</td><td class="ctr1" id="h5">0</td><td class="ctr2" id="i1">7</td><td class="ctr1" id="j5">0</td><td class="ctr2" id="k5">1</td></tr><tr><td id="a12"><a href="WordConversionConfig.java.html#L11" class="el_method">setMaxFileSize(long)</a></td><td class="bar" id="b6"><img src="../jacoco-resources/greenbar.gif" width="5" height="10" title="4" alt="4"/></td><td class="ctr2" id="c1">100%</td><td class="bar" id="d6"/><td class="ctr2" id="e6">n/a</td><td class="ctr1" id="f6">0</td><td class="ctr2" id="g6">1</td><td class="ctr1" id="h6">0</td><td class="ctr2" id="i6">1</td><td class="ctr1" id="j6">0</td><td class="ctr2" id="k6">1</td></tr><tr><td id="a14"><a href="WordConversionConfig.java.html#L11" class="el_method">setTimeoutSeconds(int)</a></td><td class="bar" id="b7"><img src="../jacoco-resources/greenbar.gif" width="5" height="10" title="4" alt="4"/></td><td class="ctr2" id="c2">100%</td><td class="bar" id="d7"/><td class="ctr2" id="e7">n/a</td><td class="ctr1" id="f7">0</td><td class="ctr2" id="g7">1</td><td class="ctr1" id="h7">0</td><td class="ctr2" id="i7">1</td><td class="ctr1" id="j7">0</td><td class="ctr2" id="k7">1</td></tr><tr><td id="a15"><a href="WordConversionConfig.java.html#L11" class="el_method">setVirusScanner(VirusScannerType)</a></td><td class="bar" id="b8"><img src="../jacoco-resources/greenbar.gif" width="5" height="10" title="4" alt="4"/></td><td class="ctr2" id="c3">100%</td><td class="bar" id="d8"/><td class="ctr2" id="e8">n/a</td><td class="ctr1" id="f8">0</td><td class="ctr2" id="g8">1</td><td class="ctr1" id="h8">0</td><td class="ctr2" id="i8">1</td><td class="ctr1" id="j8">0</td><td class="ctr2" id="k8">1</td></tr><tr><td id="a11"><a href="WordConversionConfig.java.html#L11" class="el_method">setEnabled(boolean)</a></td><td class="bar" id="b9"><img src="../jacoco-resources/greenbar.gif" width="5" height="10" title="4" alt="4"/></td><td class="ctr2" id="c4">100%</td><td class="bar" id="d9"/><td class="ctr2" id="e9">n/a</td><td class="ctr1" id="f9">0</td><td class="ctr2" id="g9">1</td><td class="ctr1" id="h9">0</td><td class="ctr2" id="i9">1</td><td class="ctr1" id="j9">0</td><td class="ctr2" id="k9">1</td></tr><tr><td id="a13"><a href="WordConversionConfig.java.html#L11" class="el_method">setTempDirectory(String)</a></td><td class="bar" id="b10"><img src="../jacoco-resources/greenbar.gif" width="5" height="10" title="4" alt="4"/></td><td class="ctr2" id="c5">100%</td><td class="bar" id="d10"/><td class="ctr2" id="e10">n/a</td><td class="ctr1" id="f10">0</td><td class="ctr2" id="g10">1</td><td class="ctr1" id="h10">0</td><td class="ctr2" id="i10">1</td><td class="ctr1" id="j10">0</td><td class="ctr2" id="k10">1</td></tr><tr><td id="a10"><a href="WordConversionConfig.java.html#L11" class="el_method">setCleanupAfterHours(int)</a></td><td class="bar" id="b11"><img src="../jacoco-resources/greenbar.gif" width="5" height="10" title="4" alt="4"/></td><td class="ctr2" id="c6">100%</td><td class="bar" id="d11"/><td class="ctr2" id="e11">n/a</td><td class="ctr1" id="f11">0</td><td class="ctr2" id="g11">1</td><td class="ctr1" id="h11">0</td><td class="ctr2" id="i11">1</td><td class="ctr1" id="j11">0</td><td class="ctr2" id="k11">1</td></tr><tr><td id="a3"><a href="WordConversionConfig.java.html#L20" class="el_method">getMaxFileSize()</a></td><td class="bar" id="b12"><img src="../jacoco-resources/greenbar.gif" width="4" height="10" title="3" alt="3"/></td><td class="ctr2" id="c7">100%</td><td class="bar" id="d12"/><td class="ctr2" id="e12">n/a</td><td class="ctr1" id="f12">0</td><td class="ctr2" id="g12">1</td><td class="ctr1" id="h12">0</td><td class="ctr2" id="i12">1</td><td class="ctr1" id="j12">0</td><td class="ctr2" id="k12">1</td></tr><tr><td id="a6"><a href="WordConversionConfig.java.html#L26" class="el_method">getTimeoutSeconds()</a></td><td class="bar" id="b13"><img src="../jacoco-resources/greenbar.gif" width="4" height="10" title="3" alt="3"/></td><td class="ctr2" id="c8">100%</td><td class="bar" id="d13"/><td class="ctr2" id="e13">n/a</td><td class="ctr1" id="f13">0</td><td class="ctr2" id="g13">1</td><td class="ctr1" id="h13">0</td><td class="ctr2" id="i13">1</td><td class="ctr1" id="j13">0</td><td class="ctr2" id="k13">1</td></tr><tr><td id="a7"><a href="WordConversionConfig.java.html#L32" class="el_method">getVirusScanner()</a></td><td class="bar" id="b14"><img src="../jacoco-resources/greenbar.gif" width="4" height="10" title="3" alt="3"/></td><td class="ctr2" id="c9">100%</td><td class="bar" id="d14"/><td class="ctr2" id="e14">n/a</td><td class="ctr1" id="f14">0</td><td class="ctr2" id="g14">1</td><td class="ctr1" id="h14">0</td><td class="ctr2" id="i14">1</td><td class="ctr1" id="j14">0</td><td class="ctr2" id="k14">1</td></tr><tr><td id="a9"><a href="WordConversionConfig.java.html#L38" class="el_method">isEnabled()</a></td><td class="bar" id="b15"><img src="../jacoco-resources/greenbar.gif" width="4" height="10" title="3" alt="3"/></td><td class="ctr2" id="c10">100%</td><td class="bar" id="d15"/><td class="ctr2" id="e15">n/a</td><td class="ctr1" id="f15">0</td><td class="ctr2" id="g15">1</td><td class="ctr1" id="h15">0</td><td class="ctr2" id="i15">1</td><td class="ctr1" id="j15">0</td><td class="ctr2" id="k15">1</td></tr><tr><td id="a5"><a href="WordConversionConfig.java.html#L44" class="el_method">getTempDirectory()</a></td><td class="bar" id="b16"><img src="../jacoco-resources/greenbar.gif" width="4" height="10" title="3" alt="3"/></td><td class="ctr2" id="c11">100%</td><td class="bar" id="d16"/><td class="ctr2" id="e16">n/a</td><td class="ctr1" id="f16">0</td><td class="ctr2" id="g16">1</td><td class="ctr1" id="h16">0</td><td class="ctr2" id="i16">1</td><td class="ctr1" id="j16">0</td><td class="ctr2" id="k16">1</td></tr><tr><td id="a2"><a href="WordConversionConfig.java.html#L50" class="el_method">getCleanupAfterHours()</a></td><td class="bar" id="b17"><img src="../jacoco-resources/greenbar.gif" width="4" height="10" title="3" alt="3"/></td><td class="ctr2" id="c12">100%</td><td class="bar" id="d17"/><td class="ctr2" id="e17">n/a</td><td class="ctr1" id="f17">0</td><td class="ctr2" id="g17">1</td><td class="ctr1" id="h17">0</td><td class="ctr2" id="i17">1</td><td class="ctr1" id="j17">0</td><td class="ctr2" id="k17">1</td></tr></tbody></table><div class="footer"><span class="right">Created with <a href="http://www.jacoco.org/jacoco">JaCoCo</a> 0.8.10.202304240956</span></div></body></html>