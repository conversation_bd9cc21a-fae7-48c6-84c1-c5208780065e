<?xml version="1.0" encoding="UTF-8"?><!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Strict//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-strict.dtd"><html xmlns="http://www.w3.org/1999/xhtml" lang="en"><head><meta http-equiv="Content-Type" content="text/html;charset=UTF-8"/><link rel="stylesheet" href="../jacoco-resources/report.css" type="text/css"/><link rel="shortcut icon" href="../jacoco-resources/report.gif" type="image/gif"/><title>RetentionProcessingService.java</title><link rel="stylesheet" href="../jacoco-resources/prettify.css" type="text/css"/><script type="text/javascript" src="../jacoco-resources/prettify.js"></script></head><body onload="window['PR_TAB_WIDTH']=4;prettyPrint()"><div class="breadcrumb" id="breadcrumb"><span class="info"><a href="../jacoco-sessions.html" class="el_session">Sessions</a></span><a href="../index.html" class="el_report">dms-svc</a> &gt; <a href="index.source.html" class="el_package">com.ascentbusiness.dms_svc.service</a> &gt; <span class="el_source">RetentionProcessingService.java</span></div><h1>RetentionProcessingService.java</h1><pre class="source lang-java linenums">package com.ascentbusiness.dms_svc.service;

import com.ascentbusiness.dms_svc.entity.Document;
import com.ascentbusiness.dms_svc.entity.RetentionPolicy;
import com.ascentbusiness.dms_svc.enums.AuditAction;
import com.ascentbusiness.dms_svc.enums.DispositionStatus;
import com.ascentbusiness.dms_svc.enums.DocumentStatus;
import com.ascentbusiness.dms_svc.repository.DocumentRepository;
import com.ascentbusiness.dms_svc.util.CorrelationIdUtil;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.util.Optional;

/**
 * Service for processing document retention policies
 */
@Service
@Transactional
<span class="fc" id="L28">public class RetentionProcessingService {</span>
    
<span class="fc" id="L30">    private static final Logger logger = LoggerFactory.getLogger(RetentionProcessingService.class);</span>
    private static final int BATCH_SIZE = 100;
    
    @Autowired
    private DocumentRepository documentRepository;
    
    @Autowired
    private RetentionPolicyService retentionPolicyService;
    
    @Autowired
    private AuditService auditService;
    
    @Autowired
    private SecurityService securityService;
    
    /**
     * Scheduled task to process document retention
     * Runs daily at 2 AM
     */
    @Scheduled(cron = &quot;0 0 2 * * *&quot;)
    public void processDocumentRetention() {
<span class="nc" id="L51">        String correlationId = CorrelationIdUtil.generateCorrelationId();</span>
<span class="nc" id="L52">        CorrelationIdUtil.setCorrelationId(correlationId);</span>
        
<span class="nc" id="L54">        logger.info(&quot;Starting scheduled document retention processing [{}]&quot;, correlationId);</span>
        
<span class="nc bnc" id="L56" title="All 2 branches missed.">        if (!securityService.isFeatureEnabled(&quot;ENABLE_RETENTION_PROCESSING&quot;)) {</span>
<span class="nc" id="L57">            logger.debug(&quot;Document retention processing is disabled [{}]&quot;, correlationId);</span>
<span class="nc" id="L58">            return;</span>
        }
        
        try {
<span class="nc" id="L62">            auditService.logAudit(AuditAction.RETENTION_PROCESSING_STARTED, null, &quot;system&quot;,</span>
                    &quot;Started scheduled document retention processing&quot;);
            
<span class="nc" id="L65">            int totalProcessed = 0;</span>
<span class="nc" id="L66">            int totalAssigned = 0;</span>
<span class="nc" id="L67">            int totalExpired = 0;</span>
            
            // Step 1: Assign retention policies to documents without policies
<span class="nc" id="L70">            totalAssigned = assignRetentionPolicies();</span>
            
            // Step 2: Calculate retention expiry dates
<span class="nc" id="L73">            calculateRetentionExpiryDates();</span>
            
            // Step 3: Process expired documents
<span class="nc" id="L76">            totalExpired = processExpiredDocuments();</span>
            
<span class="nc" id="L78">            totalProcessed = totalAssigned + totalExpired;</span>
            
<span class="nc" id="L80">            auditService.logAudit(AuditAction.RETENTION_PROCESSING_COMPLETED, null, &quot;system&quot;,</span>
<span class="nc" id="L81">                    String.format(&quot;Completed retention processing: %d documents processed, %d policies assigned, %d expired documents processed&quot;,</span>
<span class="nc" id="L82">                            totalProcessed, totalAssigned, totalExpired));</span>
            
<span class="nc" id="L84">            logger.info(&quot;Document retention processing completed: {} documents processed, {} policies assigned, {} expired [{}]&quot;,</span>
<span class="nc" id="L85">                    totalProcessed, totalAssigned, totalExpired, correlationId);</span>
            
<span class="nc" id="L87">        } catch (Exception e) {</span>
<span class="nc" id="L88">            logger.error(&quot;Error during document retention processing [{}]&quot;, correlationId, e);</span>
<span class="nc" id="L89">            auditService.logAudit(AuditAction.RETENTION_PROCESSING_FAILED, null, &quot;system&quot;,</span>
<span class="nc" id="L90">                    &quot;Document retention processing failed: &quot; + e.getMessage());</span>
        } finally {
<span class="nc" id="L92">            CorrelationIdUtil.clearCorrelationId();</span>
        }
<span class="nc" id="L94">    }</span>
    
    /**
     * Assign retention policies to documents that don't have them
     */
    private int assignRetentionPolicies() {
<span class="nc" id="L100">        logger.info(&quot;Assigning retention policies to documents without policies&quot;);</span>
        
<span class="nc" id="L102">        int totalAssigned = 0;</span>
<span class="nc" id="L103">        int pageNumber = 0;</span>
        Page&lt;Document&gt; documentsPage;
        
        do {
<span class="nc" id="L107">            Pageable pageable = PageRequest.of(pageNumber, BATCH_SIZE);</span>
<span class="nc" id="L108">            documentsPage = documentRepository.findDocumentsWithoutRetentionPolicy(pageable);</span>
            
<span class="nc bnc" id="L110" title="All 2 branches missed.">            for (Document document : documentsPage.getContent()) {</span>
                try {
<span class="nc" id="L112">                    Optional&lt;RetentionPolicy&gt; matchingPolicy = retentionPolicyService.findBestMatchingPolicy(document);</span>
                    
<span class="nc bnc" id="L114" title="All 2 branches missed.">                    if (matchingPolicy.isPresent()) {</span>
<span class="nc" id="L115">                        document.setRetentionPolicy(matchingPolicy.get());</span>
<span class="nc" id="L116">                        document.setRetentionExpiryDate(document.calculateRetentionExpiryDate());</span>
<span class="nc" id="L117">                        documentRepository.save(document);</span>
                        
<span class="nc" id="L119">                        auditService.logAudit(AuditAction.RETENTION_POLICY_ASSIGNED, document.getId(), &quot;system&quot;,</span>
<span class="nc" id="L120">                                String.format(&quot;Assigned retention policy '%s' to document '%s'&quot;,</span>
<span class="nc" id="L121">                                        matchingPolicy.get().getName(), document.getName()));</span>
                        
<span class="nc" id="L123">                        totalAssigned++;</span>
<span class="nc" id="L124">                        logger.debug(&quot;Assigned retention policy '{}' to document ID: {}&quot;, </span>
<span class="nc" id="L125">                                matchingPolicy.get().getName(), document.getId());</span>
                    }
<span class="nc" id="L127">                } catch (Exception e) {</span>
<span class="nc" id="L128">                    logger.error(&quot;Error assigning retention policy to document ID: {}&quot;, document.getId(), e);</span>
<span class="nc" id="L129">                }</span>
<span class="nc" id="L130">            }</span>
            
<span class="nc" id="L132">            pageNumber++;</span>
<span class="nc bnc" id="L133" title="All 2 branches missed.">        } while (documentsPage.hasNext());</span>
        
<span class="nc" id="L135">        logger.info(&quot;Assigned retention policies to {} documents&quot;, totalAssigned);</span>
<span class="nc" id="L136">        return totalAssigned;</span>
    }
    
    /**
     * Calculate retention expiry dates for documents with policies but no expiry date
     */
    private void calculateRetentionExpiryDates() {
<span class="nc" id="L143">        logger.info(&quot;Calculating retention expiry dates&quot;);</span>
        
<span class="nc" id="L145">        int pageNumber = 0;</span>
        Page&lt;Document&gt; documentsPage;
        
        do {
<span class="nc" id="L149">            Pageable pageable = PageRequest.of(pageNumber, BATCH_SIZE);</span>
<span class="nc" id="L150">            documentsPage = documentRepository.findDocumentsWithPolicyButNoExpiryDate(pageable);</span>
            
<span class="nc bnc" id="L152" title="All 2 branches missed.">            for (Document document : documentsPage.getContent()) {</span>
                try {
<span class="nc" id="L154">                    LocalDateTime expiryDate = document.calculateRetentionExpiryDate();</span>
<span class="nc bnc" id="L155" title="All 2 branches missed.">                    if (expiryDate != null) {</span>
<span class="nc" id="L156">                        document.setRetentionExpiryDate(expiryDate);</span>
<span class="nc" id="L157">                        documentRepository.save(document);</span>
                        
<span class="nc" id="L159">                        auditService.logAudit(AuditAction.RETENTION_EXPIRY_CALCULATED, document.getId(), &quot;system&quot;,</span>
<span class="nc" id="L160">                                String.format(&quot;Calculated retention expiry date: %s for document '%s'&quot;,</span>
<span class="nc" id="L161">                                        expiryDate, document.getName()));</span>
                        
<span class="nc" id="L163">                        logger.debug(&quot;Calculated retention expiry date for document ID: {} - {}&quot;, </span>
<span class="nc" id="L164">                                document.getId(), expiryDate);</span>
                    }
<span class="nc" id="L166">                } catch (Exception e) {</span>
<span class="nc" id="L167">                    logger.error(&quot;Error calculating retention expiry date for document ID: {}&quot;, document.getId(), e);</span>
<span class="nc" id="L168">                }</span>
<span class="nc" id="L169">            }</span>
            
<span class="nc" id="L171">            pageNumber++;</span>
<span class="nc bnc" id="L172" title="All 2 branches missed.">        } while (documentsPage.hasNext());</span>
<span class="nc" id="L173">    }</span>
    
    /**
     * Process documents that have expired retention periods
     */
    private int processExpiredDocuments() {
<span class="nc" id="L179">        logger.info(&quot;Processing expired documents&quot;);</span>
        
<span class="nc" id="L181">        int totalProcessed = 0;</span>
<span class="nc" id="L182">        LocalDateTime now = LocalDateTime.now();</span>
        
<span class="nc" id="L184">        int pageNumber = 0;</span>
        Page&lt;Document&gt; expiredDocumentsPage;
        
        do {
<span class="nc" id="L188">            Pageable pageable = PageRequest.of(pageNumber, BATCH_SIZE);</span>
<span class="nc" id="L189">            expiredDocumentsPage = documentRepository.findExpiredDocuments(now, pageable);</span>
            
<span class="nc bnc" id="L191" title="All 2 branches missed.">            for (Document document : expiredDocumentsPage.getContent()) {</span>
                try {
<span class="nc" id="L193">                    processExpiredDocument(document);</span>
<span class="nc" id="L194">                    totalProcessed++;</span>
<span class="nc" id="L195">                } catch (Exception e) {</span>
<span class="nc" id="L196">                    logger.error(&quot;Error processing expired document ID: {}&quot;, document.getId(), e);</span>
<span class="nc" id="L197">                }</span>
<span class="nc" id="L198">            }</span>
            
<span class="nc" id="L200">            pageNumber++;</span>
<span class="nc bnc" id="L201" title="All 2 branches missed.">        } while (expiredDocumentsPage.hasNext());</span>
        
<span class="nc" id="L203">        logger.info(&quot;Processed {} expired documents&quot;, totalProcessed);</span>
<span class="nc" id="L204">        return totalProcessed;</span>
    }
    
    /**
     * Process a single expired document based on its retention policy
     */
    private void processExpiredDocument(Document document) {
<span class="nc bnc" id="L211" title="All 2 branches missed.">        if (document.isUnderLegalHold()) {</span>
<span class="nc" id="L212">            logger.debug(&quot;Document ID: {} is under legal hold, skipping disposition&quot;, document.getId());</span>
<span class="nc" id="L213">            return;</span>
        }
        
<span class="nc" id="L216">        RetentionPolicy policy = document.getRetentionPolicy();</span>
<span class="nc bnc" id="L217" title="All 2 branches missed.">        if (policy == null) {</span>
<span class="nc" id="L218">            logger.warn(&quot;Document ID: {} has no retention policy, cannot process&quot;, document.getId());</span>
<span class="nc" id="L219">            return;</span>
        }
        
<span class="nc bnc" id="L222" title="All 6 branches missed.">        switch (policy.getDispositionAction()) {</span>
            case DELETE:
<span class="nc" id="L224">                processDocumentDeletion(document);</span>
<span class="nc" id="L225">                break;</span>
            case ARCHIVE:
<span class="nc" id="L227">                processDocumentArchival(document);</span>
<span class="nc" id="L228">                break;</span>
            case REVIEW:
<span class="nc" id="L230">                markDocumentForReview(document);</span>
<span class="nc" id="L231">                break;</span>
            case EXTEND:
<span class="nc" id="L233">                extendDocumentRetention(document);</span>
<span class="nc" id="L234">                break;</span>
            case TRANSFER:
<span class="nc" id="L236">                markDocumentForTransfer(document);</span>
<span class="nc" id="L237">                break;</span>
            default:
<span class="nc" id="L239">                logger.warn(&quot;Unknown disposition action: {} for document ID: {}&quot;, </span>
<span class="nc" id="L240">                        policy.getDispositionAction(), document.getId());</span>
        }
<span class="nc" id="L242">    }</span>
    
    private void processDocumentDeletion(Document document) {
<span class="nc" id="L245">        document.setStatus(DocumentStatus.DELETED);</span>
<span class="nc" id="L246">        document.setDispositionStatus(DispositionStatus.DISPOSED);</span>
<span class="nc" id="L247">        documentRepository.save(document);</span>
        
<span class="nc" id="L249">        auditService.logAudit(AuditAction.DOCUMENT_DISPOSED, document.getId(), &quot;system&quot;,</span>
<span class="nc" id="L250">                String.format(&quot;Document '%s' disposed via deletion due to retention policy expiry&quot;, document.getName()));</span>
        
<span class="nc" id="L252">        logger.info(&quot;Document ID: {} disposed via deletion&quot;, document.getId());</span>
<span class="nc" id="L253">    }</span>
    
    private void processDocumentArchival(Document document) {
<span class="nc" id="L256">        document.setStatus(DocumentStatus.HISTORICAL);</span>
<span class="nc" id="L257">        document.setDispositionStatus(DispositionStatus.DISPOSED);</span>
<span class="nc" id="L258">        documentRepository.save(document);</span>
        
<span class="nc" id="L260">        auditService.logAudit(AuditAction.DOCUMENT_ARCHIVED, document.getId(), &quot;system&quot;,</span>
<span class="nc" id="L261">                String.format(&quot;Document '%s' archived due to retention policy expiry&quot;, document.getName()));</span>
        
<span class="nc" id="L263">        logger.info(&quot;Document ID: {} archived&quot;, document.getId());</span>
<span class="nc" id="L264">    }</span>
    
    private void markDocumentForReview(Document document) {
<span class="nc" id="L267">        document.setDispositionStatus(DispositionStatus.REVIEW_REQUIRED);</span>
<span class="nc" id="L268">        document.setDispositionReviewDate(LocalDateTime.now());</span>
<span class="nc" id="L269">        documentRepository.save(document);</span>
        
<span class="nc" id="L271">        auditService.logAudit(AuditAction.DISPOSITION_REVIEW_REQUIRED, document.getId(), &quot;system&quot;,</span>
<span class="nc" id="L272">                String.format(&quot;Document '%s' marked for review due to retention policy expiry&quot;, document.getName()));</span>
        
<span class="nc" id="L274">        logger.info(&quot;Document ID: {} marked for review&quot;, document.getId());</span>
<span class="nc" id="L275">    }</span>
    
    private void extendDocumentRetention(Document document) {
        // Extend retention by the same period
<span class="nc" id="L279">        RetentionPolicy policy = document.getRetentionPolicy();</span>
<span class="nc" id="L280">        LocalDateTime newExpiryDate = policy.calculateExpiryDate(document.getRetentionExpiryDate());</span>
        
<span class="nc" id="L282">        document.setRetentionExpiryDate(newExpiryDate);</span>
<span class="nc" id="L283">        documentRepository.save(document);</span>
        
<span class="nc" id="L285">        auditService.logAudit(AuditAction.RETENTION_EXPIRY_CALCULATED, document.getId(), &quot;system&quot;,</span>
<span class="nc" id="L286">                String.format(&quot;Document '%s' retention extended to %s&quot;, document.getName(), newExpiryDate));</span>
        
<span class="nc" id="L288">        logger.info(&quot;Document ID: {} retention extended to {}&quot;, document.getId(), newExpiryDate);</span>
<span class="nc" id="L289">    }</span>
    
    private void markDocumentForTransfer(Document document) {
<span class="nc" id="L292">        document.setDispositionStatus(DispositionStatus.PENDING);</span>
<span class="nc" id="L293">        document.setDispositionNotes(&quot;Marked for transfer due to retention policy expiry&quot;);</span>
<span class="nc" id="L294">        documentRepository.save(document);</span>
        
<span class="nc" id="L296">        auditService.logAudit(AuditAction.DISPOSITION_REVIEW_REQUIRED, document.getId(), &quot;system&quot;,</span>
<span class="nc" id="L297">                String.format(&quot;Document '%s' marked for transfer due to retention policy expiry&quot;, document.getName()));</span>
        
<span class="nc" id="L299">        logger.info(&quot;Document ID: {} marked for transfer&quot;, document.getId());</span>
<span class="nc" id="L300">    }</span>
}
</pre><div class="footer"><span class="right">Created with <a href="http://www.jacoco.org/jacoco">JaCoCo</a> 0.8.10.202304240956</span></div></body></html>