/*
 * Copyright (c) 2024 Ascent Business Technology
 * All rights reserved.
 */

package com.ascentbusiness.dms_svc.util;

import org.springframework.web.multipart.MultipartFile;

import java.io.ByteArrayInputStream;
import java.io.File;
import java.io.IOException;
import java.io.InputStream;

/**
 * A MultipartFile implementation that wraps a byte array.
 * 
 * <p>This utility class allows creating MultipartFile instances from byte arrays,
 * which is useful when downloading files from URLs or reading from other sources
 * and then processing them through the standard document upload pipeline.
 * 
 * <p>This implementation is read-only and does not support file transfer operations
 * that require writing to disk.
 * 
 * <AUTHOR> Development Team
 * @version 1.0.0
 * @since 1.0.0
 */
public class ByteArrayMultipartFile implements MultipartFile {
    
    private final byte[] content;
    private final String name;
    private final String originalFilename;
    private final String contentType;
    
    /**
     * Create a ByteArrayMultipartFile with content and filename.
     * 
     * @param content the file content as byte array
     * @param originalFilename the original filename
     */
    public ByteArrayMultipartFile(byte[] content, String originalFilename) {
        this(content, "file", originalFilename, null);
    }
    
    /**
     * Create a ByteArrayMultipartFile with content, filename, and content type.
     * 
     * @param content the file content as byte array
     * @param originalFilename the original filename
     * @param contentType the MIME content type
     */
    public ByteArrayMultipartFile(byte[] content, String originalFilename, String contentType) {
        this(content, "file", originalFilename, contentType);
    }
    
    /**
     * Create a ByteArrayMultipartFile with all parameters.
     * 
     * @param content the file content as byte array
     * @param name the form field name
     * @param originalFilename the original filename
     * @param contentType the MIME content type
     */
    public ByteArrayMultipartFile(byte[] content, String name, String originalFilename, String contentType) {
        this.content = content != null ? content.clone() : new byte[0];
        this.name = name;
        this.originalFilename = originalFilename;
        this.contentType = contentType != null ? contentType : "application/octet-stream";
    }
    
    @Override
    public String getName() {
        return name;
    }
    
    @Override
    public String getOriginalFilename() {
        return originalFilename;
    }
    
    @Override
    public String getContentType() {
        return contentType;
    }
    
    @Override
    public boolean isEmpty() {
        return content.length == 0;
    }
    
    @Override
    public long getSize() {
        return content.length;
    }
    
    @Override
    public byte[] getBytes() throws IOException {
        return content.clone();
    }
    
    @Override
    public InputStream getInputStream() throws IOException {
        return new ByteArrayInputStream(content);
    }
    
    @Override
    public void transferTo(File dest) throws IOException, IllegalStateException {
        throw new UnsupportedOperationException("ByteArrayMultipartFile does not support transferTo operation");
    }
    
    /**
     * Get the content as a new byte array.
     * This is a convenience method that's equivalent to getBytes().
     * 
     * @return a copy of the content byte array
     */
    public byte[] getContent() {
        return content.clone();
    }
    
    /**
     * Create a ByteArrayMultipartFile from an InputStream.
     * 
     * @param inputStream the input stream to read from
     * @param originalFilename the original filename
     * @return a new ByteArrayMultipartFile instance
     * @throws IOException if reading from the stream fails
     */
    public static ByteArrayMultipartFile fromInputStream(InputStream inputStream, String originalFilename) 
            throws IOException {
        byte[] content = inputStream.readAllBytes();
        return new ByteArrayMultipartFile(content, originalFilename);
    }
    
    /**
     * Create a ByteArrayMultipartFile from an InputStream with content type.
     * 
     * @param inputStream the input stream to read from
     * @param originalFilename the original filename
     * @param contentType the MIME content type
     * @return a new ByteArrayMultipartFile instance
     * @throws IOException if reading from the stream fails
     */
    public static ByteArrayMultipartFile fromInputStream(InputStream inputStream, String originalFilename, 
            String contentType) throws IOException {
        byte[] content = inputStream.readAllBytes();
        return new ByteArrayMultipartFile(content, originalFilename, contentType);
    }
    
    @Override
    public String toString() {
        return "ByteArrayMultipartFile{" +
                "name='" + name + '\'' +
                ", originalFilename='" + originalFilename + '\'' +
                ", contentType='" + contentType + '\'' +
                ", size=" + content.length +
                '}';
    }
}