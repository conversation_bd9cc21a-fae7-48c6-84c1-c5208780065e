<?xml version="1.0" encoding="UTF-8"?><!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Strict//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-strict.dtd"><html xmlns="http://www.w3.org/1999/xhtml" lang="en"><head><meta http-equiv="Content-Type" content="text/html;charset=UTF-8"/><link rel="stylesheet" href="../jacoco-resources/report.css" type="text/css"/><link rel="shortcut icon" href="../jacoco-resources/report.gif" type="image/gif"/><title>DocumentShareResolver.java</title><link rel="stylesheet" href="../jacoco-resources/prettify.css" type="text/css"/><script type="text/javascript" src="../jacoco-resources/prettify.js"></script></head><body onload="window['PR_TAB_WIDTH']=4;prettyPrint()"><div class="breadcrumb" id="breadcrumb"><span class="info"><a href="../jacoco-sessions.html" class="el_session">Sessions</a></span><a href="../index.html" class="el_report">dms-svc</a> &gt; <a href="index.source.html" class="el_package">com.ascentbusiness.dms_svc.resolver</a> &gt; <span class="el_source">DocumentShareResolver.java</span></div><h1>DocumentShareResolver.java</h1><pre class="source lang-java linenums">package com.ascentbusiness.dms_svc.resolver;

import com.ascentbusiness.dms_svc.annotation.RateLimit;
import com.ascentbusiness.dms_svc.dto.*;
import com.ascentbusiness.dms_svc.entity.BulkShareItem;
import com.ascentbusiness.dms_svc.entity.BulkShareOperation;
import com.ascentbusiness.dms_svc.entity.Document;
import com.ascentbusiness.dms_svc.entity.DocumentShareLink;
import com.ascentbusiness.dms_svc.service.BulkShareService;
import com.ascentbusiness.dms_svc.service.DocumentService;
import com.ascentbusiness.dms_svc.service.DocumentShareService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.graphql.data.method.annotation.Argument;
import org.springframework.graphql.data.method.annotation.MutationMapping;
import org.springframework.graphql.data.method.annotation.QueryMapping;
import org.springframework.graphql.data.method.annotation.SchemaMapping;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.stereotype.Controller;

import java.util.List;
import java.util.stream.Collectors;

/**
 * GraphQL resolver for document sharing operations.
 * 
 * This resolver provides GraphQL endpoints for:
 * - Creating and managing document share links
 * - Bulk sharing operations
 * - Accessing shared documents
 * - Share link management and revocation
 * 
 * <AUTHOR> Development Team
 * @version 1.0.0
 * @since 1.0.0
 */
@Controller
<span class="fc" id="L39">@RequiredArgsConstructor</span>
<span class="fc" id="L40">@Slf4j</span>
public class DocumentShareResolver {
    
    private final DocumentShareService documentShareService;
    private final BulkShareService bulkShareService;
    private final DocumentService documentService;
    
    @Value(&quot;${dms.application.base-url:http://localhost:9093}&quot;)
    private String baseUrl;
    
    // ===== QUERY OPERATIONS =====
    
    /**
     * Get share links for a document
     */
    @QueryMapping
    @PreAuthorize(&quot;isAuthenticated()&quot;)
    public List&lt;DocumentShareLink&gt; documentShareLinks(@Argument Long documentId) {
<span class="nc" id="L58">        log.info(&quot;Fetching share links for document: {}&quot;, documentId);</span>
<span class="nc" id="L59">        return documentShareService.getShareLinksForDocument(documentId);</span>
    }
    
    /**
     * Get a specific share link
     */
    @QueryMapping
    @PreAuthorize(&quot;isAuthenticated()&quot;)
    public DocumentShareLink shareLink(@Argument String linkId) {
<span class="nc" id="L68">        log.info(&quot;Fetching share link: {}&quot;, linkId);</span>
<span class="nc" id="L69">        return documentShareService.getShareLink(linkId);</span>
    }
    
    /**
     * Get all bulk share operations for the current user
     */
    @QueryMapping
    @PreAuthorize(&quot;isAuthenticated()&quot;)
    public List&lt;BulkShareOperation&gt; myBulkShareOperations() {
<span class="nc" id="L78">        log.info(&quot;Fetching bulk share operations for current user&quot;);</span>
<span class="nc" id="L79">        return bulkShareService.getMyBulkShareOperations();</span>
    }
    
    /**
     * Get a specific bulk share operation
     */
    @QueryMapping
    @PreAuthorize(&quot;isAuthenticated()&quot;)
    public BulkShareOperation bulkShareOperation(@Argument String operationId) {
<span class="nc" id="L88">        log.info(&quot;Fetching bulk share operation: {}&quot;, operationId);</span>
<span class="nc" id="L89">        return bulkShareService.getBulkShareOperation(operationId);</span>
    }
    
    /**
     * Access a shared document via link (public endpoint)
     */
    @QueryMapping
    public AccessSharedDocumentResponse accessSharedDocument(@Argument AccessSharedDocumentInput input) {
        try {
<span class="nc" id="L98">            log.info(&quot;Accessing shared document via link: {}&quot;, input.getLinkId());</span>
            
<span class="nc" id="L100">            input.validate();</span>
            
            // Check if password is required but not provided
<span class="nc" id="L103">            DocumentShareLink shareLink = documentShareService.getShareLink(input.getLinkId());</span>
<span class="nc" id="L104">            boolean requiresPassword = shareLink.requiresPassword();</span>
            
<span class="nc bnc" id="L106" title="All 4 branches missed.">            if (requiresPassword &amp;&amp; !input.hasPassword()) {</span>
<span class="nc" id="L107">                return AccessSharedDocumentResponse.passwordRequired();</span>
            }
            
            // Access document
<span class="nc" id="L111">            Document document = documentShareService.accessDocumentViaShareLink(</span>
<span class="nc" id="L112">                input.getLinkId(), input.getPassword());</span>
            
<span class="nc" id="L114">            return AccessSharedDocumentResponse.success(document, shareLink.getPermission());</span>
            
<span class="nc" id="L116">        } catch (Exception e) {</span>
<span class="nc" id="L117">            log.error(&quot;Error accessing shared document: {}&quot;, e.getMessage(), e);</span>
<span class="nc" id="L118">            return AccessSharedDocumentResponse.error(&quot;Error accessing document: &quot; + e.getMessage());</span>
        }
    }
    
    // ===== MUTATION OPERATIONS =====
    
    /**
     * Create a share link for a document
     */
    @MutationMapping
    @PreAuthorize(&quot;isAuthenticated()&quot;)
    @RateLimit(value = 10, window = 300, type = RateLimit.RateLimitType.DOCUMENT_OPERATION,
               message = &quot;Share link creation rate limit exceeded. Please wait before creating more links.&quot;)
    public ShareLinkResponse createDocumentShareLink(@Argument Long documentId, 
                                                    @Argument CreateShareLinkInput input) {
        try {
<span class="nc" id="L134">            log.info(&quot;Creating share link for document {} with permission {}&quot;, </span>
<span class="nc" id="L135">                    documentId, input.getPermission());</span>
            
<span class="nc" id="L137">            input.validate();</span>
            
<span class="nc" id="L139">            DocumentShareLink shareLink = documentShareService.createShareLink(documentId, input);</span>
            
<span class="nc" id="L141">            return ShareLinkResponse.created(shareLink);</span>
            
<span class="nc" id="L143">        } catch (Exception e) {</span>
<span class="nc" id="L144">            log.error(&quot;Error creating share link: {}&quot;, e.getMessage(), e);</span>
<span class="nc" id="L145">            return ShareLinkResponse.error(&quot;Failed to create share link: &quot; + e.getMessage());</span>
        }
    }
    
    /**
     * Revoke a share link
     */
    @MutationMapping
    @PreAuthorize(&quot;isAuthenticated()&quot;)
    @RateLimit(value = 15, window = 300, type = RateLimit.RateLimitType.DOCUMENT_OPERATION,
               message = &quot;Share link revocation rate limit exceeded.&quot;)
    public ShareLinkResponse revokeDocumentShareLink(@Argument String linkId) {
        try {
<span class="nc" id="L158">            log.info(&quot;Revoking share link: {}&quot;, linkId);</span>
            
<span class="nc" id="L160">            documentShareService.revokeShareLink(linkId);</span>
            
<span class="nc" id="L162">            return ShareLinkResponse.revoked();</span>
            
<span class="nc" id="L164">        } catch (Exception e) {</span>
<span class="nc" id="L165">            log.error(&quot;Error revoking share link: {}&quot;, e.getMessage(), e);</span>
<span class="nc" id="L166">            return ShareLinkResponse.error(&quot;Failed to revoke share link: &quot; + e.getMessage());</span>
        }
    }
    
    /**
     * Bulk share documents
     */
    @MutationMapping
    @PreAuthorize(&quot;isAuthenticated()&quot;)
    @RateLimit(value = 5, window = 600, type = RateLimit.RateLimitType.BULK_OPERATION,
               message = &quot;Bulk share rate limit exceeded. Please wait before starting another bulk operation.&quot;)
    public BulkShareResponse bulkShareDocuments(@Argument BulkShareInput input) {
        try {
<span class="nc" id="L179">            log.info(&quot;Starting bulk share operation for {} documents with {} recipients&quot;, </span>
<span class="nc" id="L180">                    input.getTotalDocuments(), input.getTotalRecipients());</span>
            
            // Validate input
<span class="nc" id="L183">            input.validate();</span>
            
<span class="nc bnc" id="L185" title="All 2 branches missed.">            if (input.getExpectedShareLinkCount() &gt; 1000) {</span>
<span class="nc" id="L186">                return BulkShareResponse.error(&quot;Bulk operation too large. Maximum 1000 share links per operation.&quot;);</span>
            }
            
            // Process bulk share operation
<span class="nc" id="L190">            BulkShareService.BulkShareResult result = bulkShareService.bulkShareDocuments(input);</span>
            
            // Convert to response
<span class="nc" id="L193">            List&lt;BulkShareResponse.BulkShareItemResult&gt; items = result.getItems().stream()</span>
<span class="nc" id="L194">                .map(itemResult -&gt; BulkShareResponse.BulkShareItemResult.builder()</span>
<span class="nc" id="L195">                    .documentId(itemResult.getDocumentId())</span>
<span class="nc" id="L196">                    .recipientId(itemResult.getRecipientId())</span>
<span class="nc" id="L197">                    .isRole(itemResult.getIsRole())</span>
<span class="nc" id="L198">                    .isSuccessful(itemResult.getIsSuccessful())</span>
<span class="nc" id="L199">                    .errorMessage(itemResult.getErrorMessage())</span>
<span class="nc" id="L200">                    .shareLinkId(itemResult.getShareLinkId())</span>
<span class="nc" id="L201">                    .shareUrl(itemResult.getShareUrl())</span>
<span class="nc" id="L202">                    .build())</span>
<span class="nc" id="L203">                .collect(Collectors.toList());</span>
            
<span class="nc" id="L205">            return BulkShareResponse.completed(result.getOperation(), items);</span>
            
<span class="nc" id="L207">        } catch (Exception e) {</span>
<span class="nc" id="L208">            log.error(&quot;Error processing bulk share operation: {}&quot;, e.getMessage(), e);</span>
<span class="nc" id="L209">            return BulkShareResponse.error(&quot;Failed to process bulk share operation: &quot; + e.getMessage());</span>
        }
    }
    
    // ===== SCHEMA MAPPING RESOLVERS =====
    
    /**
     * Resolver for shareUrl field in DocumentShareLink
     */
    @SchemaMapping(typeName = &quot;DocumentShareLink&quot;, field = &quot;shareUrl&quot;)
    public String getShareUrl(DocumentShareLink shareLink) {
<span class="nc" id="L220">        return documentShareService.generateShareUrl(shareLink);</span>
    }
    
    /**
     * Resolver for hasPassword field in DocumentShareLink
     */
    @SchemaMapping(typeName = &quot;DocumentShareLink&quot;, field = &quot;hasPassword&quot;)
    public boolean getHasPassword(DocumentShareLink shareLink) {
<span class="nc" id="L228">        return shareLink.requiresPassword();</span>
    }
    
    /**
     * Resolver for items field in BulkShareOperation
     */
    @SchemaMapping(typeName = &quot;BulkShareOperation&quot;, field = &quot;items&quot;)
    public List&lt;BulkShareItem&gt; getBulkShareItems(BulkShareOperation operation) {
<span class="nc" id="L236">        return bulkShareService.getBulkShareItems(operation.getId());</span>
    }
    
    /**
     * Resolver for document field in BulkShareItem
     */
    @SchemaMapping(typeName = &quot;BulkShareItem&quot;, field = &quot;document&quot;)
    public Document getDocument(BulkShareItem item) {
        try {
<span class="nc" id="L245">            return documentService.getDocumentById(item.getDocumentId());</span>
<span class="nc" id="L246">        } catch (Exception e) {</span>
<span class="nc" id="L247">            log.warn(&quot;Could not resolve document for bulk share item: {}&quot;, e.getMessage());</span>
<span class="nc" id="L248">            return null;</span>
        }
    }
    
    /**
     * Resolver for shareLinks field in Document
     */
    @SchemaMapping(typeName = &quot;Document&quot;, field = &quot;shareLinks&quot;)
    public List&lt;DocumentShareLink&gt; getDocumentShareLinks(Document document) {
        try {
<span class="nc" id="L258">            return documentShareService.getShareLinksForDocument(document.getId());</span>
<span class="nc" id="L259">        } catch (Exception e) {</span>
<span class="nc" id="L260">            log.warn(&quot;Could not resolve share links for document: {}&quot;, e.getMessage());</span>
<span class="nc" id="L261">            return List.of();</span>
        }
    }
}
</pre><div class="footer"><span class="right">Created with <a href="http://www.jacoco.org/jacoco">JaCoCo</a> 0.8.10.202304240956</span></div></body></html>