--liquibase formatted sql

--changeset system:057-create-document-compliance-mapping-collection-tables
--comment: Create missing collection tables for DocumentComplianceMapping entity

-- Create collection table for document compliance mapping data subjects
CREATE TABLE document_data_subjects (
    mapping_id BIGINT NOT NULL,
    data_subject_category VARCHAR(50) NOT NULL,
    PRIMARY KEY (mapping_id, data_subject_category),
    FOREIGN KEY (mapping_id) REFERENCES document_compliance_mappings(id) ON DELETE CASCADE
);

-- Create collection table for document compliance mapping access regions
CREATE TABLE document_access_regions (
    mapping_id BIGINT NOT NULL,
    region VARCHAR(50) NOT NULL,
    PRIMARY KEY (mapping_id, region),
    FOREIG<PERSON> KEY (mapping_id) REFERENCES document_compliance_mappings(id) ON DELETE CASCADE
);

-- Add indexes for better performance
CREATE INDEX idx_document_data_subjects_mapping_id ON document_data_subjects(mapping_id);
CREATE INDEX idx_document_access_regions_mapping_id ON document_access_regions(mapping_id);

-- Add comments for documentation
ALTER TABLE document_data_subjects COMMENT = 'Maps document compliance mappings to data subject categories';
ALTER TABLE document_access_regions COMMENT = 'Maps document compliance mappings to allowed access regions';

--rollback DROP TABLE document_access_regions;
--rollback DROP TABLE document_data_subjects;