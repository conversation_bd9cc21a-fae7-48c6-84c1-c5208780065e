package com.ascentbusiness.dms_svc.service;

import com.ascentbusiness.dms_svc.dto.DocumentEx;
import com.ascentbusiness.dms_svc.dto.UploadDocumentFromPathExInput;
import com.ascentbusiness.dms_svc.enums.ProcessingStrategy;
import com.ascentbusiness.dms_svc.enums.ProcessingStatus;
import com.ascentbusiness.dms_svc.enums.StorageProvider;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.security.authentication.UsernamePasswordAuthenticationToken;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.security.core.authority.SimpleGrantedAuthority;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.transaction.annotation.Transactional;

import java.util.Collections;

import java.io.IOException;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;

import static org.junit.jupiter.api.Assertions.*;

@SpringBootTest
@ActiveProfiles("test")
@Transactional
public class LargeFileUploadTest {

    @Autowired
    private DocumentService documentService;

    @BeforeEach
    void setUp() {
        // Set up authentication context for testing
        UsernamePasswordAuthenticationToken authentication =
            new UsernamePasswordAuthenticationToken(
                "test-user",
                "password",
                Collections.singletonList(new SimpleGrantedAuthority("ROLE_USER"))
            );
        SecurityContextHolder.getContext().setAuthentication(authentication);
    }

    @Test
    public void testLargeFileUploadWithChunkedProcessing() throws IOException {
        // Create a test file path - using the test file we created
        String testFilePath = "temp/test-large-file.txt";
        
        // Check if file exists
        Path filePath = Paths.get(testFilePath);
        if (!Files.exists(filePath)) {
            System.out.println("Test file not found at: " + testFilePath);
            System.out.println("Please update the testFilePath variable with the actual path to your 61MB file");
            return; // Skip test if file doesn't exist
        }

        // Create input for the upload
        UploadDocumentFromPathExInput input = new UploadDocumentFromPathExInput();
        input.setSourceFilePath(testFilePath);
        input.setName("Test Large File Upload - 61MB");
        input.setDescription("Testing large file upload with chunked processing strategy");
        input.setStorageProvider(StorageProvider.LOCAL);
        input.setOverrideFile(true);

        System.out.println("Starting large file upload test...");
        System.out.println("File path: " + testFilePath);
        System.out.println("File size: " + input.getFileSize() + " bytes");
        System.out.println("Filename: " + input.getFilename());

        try {
            // Call the upload method
            DocumentEx result = documentService.uploadDocumentFromPathEx(input);

            // Verify the result
            assertNotNull(result, "Upload result should not be null");
            assertEquals(ProcessingStrategy.CHUNKED, result.getProcessingStrategy(),
                "Should use chunked processing strategy for large files");
            assertEquals(ProcessingStatus.COMPLETED, result.getProcessingStatus(),
                "Processing should be completed");

            // Verify document properties (DocumentEx has direct fields, not getDocument())
            assertEquals(input.getName(), result.getName());
            assertEquals(input.getFileSize(), result.getFileSize());
            assertNotNull(result.getStoragePath(), "Storage path should be set");
            assertNotNull(result.getId(), "Document ID should be set");

            System.out.println("✅ Large file upload test PASSED!");
            System.out.println("Document ID: " + result.getId());
            System.out.println("Processing Strategy: " + result.getProcessingStrategy());
            System.out.println("Processing Status: " + result.getProcessingStatus());
            System.out.println("Storage Path: " + result.getStoragePath());

        } catch (Exception e) {
            System.err.println("❌ Large file upload test FAILED!");
            System.err.println("Error: " + e.getMessage());
            e.printStackTrace();
            fail("Large file upload failed: " + e.getMessage());
        }
    }

    @Test
    public void testFileProcessingStrategySelection() {
        // Test that the system correctly selects chunked processing for large files
        long largeFileSize = 64 * 1024 * 1024; // 64MB
        
        // This would normally be tested by calling determineProcessingStrategy
        // but since it's private, we test it indirectly through the upload method
        
        System.out.println("Testing processing strategy selection for file size: " + largeFileSize + " bytes");
        
        // For files larger than the threshold, it should use CHUNKED strategy
        // This is tested indirectly in the main upload test above
        assertTrue(largeFileSize > 10 * 1024 * 1024, "Test file should be larger than 10MB to trigger chunked processing");
    }
}