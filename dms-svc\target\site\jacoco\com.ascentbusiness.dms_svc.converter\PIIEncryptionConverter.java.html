<?xml version="1.0" encoding="UTF-8"?><!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Strict//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-strict.dtd"><html xmlns="http://www.w3.org/1999/xhtml" lang="en"><head><meta http-equiv="Content-Type" content="text/html;charset=UTF-8"/><link rel="stylesheet" href="../jacoco-resources/report.css" type="text/css"/><link rel="shortcut icon" href="../jacoco-resources/report.gif" type="image/gif"/><title>PIIEncryptionConverter.java</title><link rel="stylesheet" href="../jacoco-resources/prettify.css" type="text/css"/><script type="text/javascript" src="../jacoco-resources/prettify.js"></script></head><body onload="window['PR_TAB_WIDTH']=4;prettyPrint()"><div class="breadcrumb" id="breadcrumb"><span class="info"><a href="../jacoco-sessions.html" class="el_session">Sessions</a></span><a href="../index.html" class="el_report">dms-svc</a> &gt; <a href="index.source.html" class="el_package">com.ascentbusiness.dms_svc.converter</a> &gt; <span class="el_source">PIIEncryptionConverter.java</span></div><h1>PIIEncryptionConverter.java</h1><pre class="source lang-java linenums">package com.ascentbusiness.dms_svc.converter;

import com.ascentbusiness.dms_svc.service.PIIEncryptionService;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import jakarta.persistence.AttributeConverter;
import jakarta.persistence.Converter;
import java.util.HashMap;
import java.util.Map;

/**
 * JPA converter for automatic PII encryption/decryption
 * Stores encrypted data as JSON with metadata
 */
@Converter
@Component
<span class="fc" id="L22">public class PIIEncryptionConverter implements AttributeConverter&lt;String, String&gt; {</span>
    
<span class="fc" id="L24">    private static final Logger logger = LoggerFactory.getLogger(PIIEncryptionConverter.class);</span>
    
    @Autowired
    private PIIEncryptionService piiEncryptionService;
    
    @Autowired
    private ObjectMapper objectMapper;
    
<span class="fc" id="L32">    private String currentFieldName = &quot;unknown&quot;; // Will be set by annotation processor</span>
    
    @Override
    public String convertToDatabaseColumn(String attribute) {
<span class="nc bnc" id="L36" title="All 4 branches missed.">        if (attribute == null || attribute.trim().isEmpty()) {</span>
<span class="nc" id="L37">            return attribute;</span>
        }
        
        try {
<span class="nc" id="L41">            PIIEncryptionService.EncryptedPIIData encryptedData = </span>
<span class="nc" id="L42">                piiEncryptionService.encryptIfPII(attribute, currentFieldName);</span>
            
<span class="nc bnc" id="L44" title="All 2 branches missed.">            if (!encryptedData.isEncrypted()) {</span>
<span class="nc" id="L45">                return attribute; // Return as-is if not encrypted</span>
            }
            
            // Store as JSON with encryption metadata
<span class="nc" id="L49">            Map&lt;String, Object&gt; encryptionWrapper = new HashMap&lt;&gt;();</span>
<span class="nc" id="L50">            encryptionWrapper.put(&quot;encrypted&quot;, true);</span>
<span class="nc" id="L51">            encryptionWrapper.put(&quot;data&quot;, encryptedData.getData());</span>
<span class="nc" id="L52">            encryptionWrapper.put(&quot;iv&quot;, encryptedData.getIv());</span>
<span class="nc" id="L53">            encryptionWrapper.put(&quot;keyId&quot;, encryptedData.getKeyId());</span>
<span class="nc" id="L54">            encryptionWrapper.put(&quot;fieldName&quot;, encryptedData.getFieldName());</span>
<span class="nc" id="L55">            encryptionWrapper.put(&quot;version&quot;, &quot;1.0&quot;);</span>
            
<span class="nc" id="L57">            return objectMapper.writeValueAsString(encryptionWrapper);</span>
            
<span class="nc" id="L59">        } catch (JsonProcessingException e) {</span>
<span class="nc" id="L60">            logger.error(&quot;Failed to serialize encrypted PII data for field: {}&quot;, currentFieldName, e);</span>
<span class="nc" id="L61">            return attribute; // Return original data as fallback</span>
<span class="nc" id="L62">        } catch (Exception e) {</span>
<span class="nc" id="L63">            logger.error(&quot;Failed to encrypt PII data for field: {}&quot;, currentFieldName, e);</span>
<span class="nc" id="L64">            return attribute; // Return original data as fallback</span>
        }
    }
    
    @Override
    public String convertToEntityAttribute(String dbData) {
<span class="nc bnc" id="L70" title="All 4 branches missed.">        if (dbData == null || dbData.trim().isEmpty()) {</span>
<span class="nc" id="L71">            return dbData;</span>
        }
        
        // Check if data is encrypted (JSON format)
<span class="nc bnc" id="L75" title="All 2 branches missed.">        if (!dbData.trim().startsWith(&quot;{&quot;)) {</span>
<span class="nc" id="L76">            return dbData; // Return as-is if not JSON (unencrypted)</span>
        }
        
        try {
            @SuppressWarnings(&quot;unchecked&quot;)
<span class="nc" id="L81">            Map&lt;String, Object&gt; encryptionWrapper = objectMapper.readValue(dbData, Map.class);</span>
            
<span class="nc" id="L83">            Boolean encrypted = (Boolean) encryptionWrapper.get(&quot;encrypted&quot;);</span>
<span class="nc bnc" id="L84" title="All 4 branches missed.">            if (encrypted == null || !encrypted) {</span>
<span class="nc" id="L85">                return dbData; // Return as-is if not marked as encrypted</span>
            }
            
<span class="nc" id="L88">            String encryptedDataStr = (String) encryptionWrapper.get(&quot;data&quot;);</span>
<span class="nc" id="L89">            String iv = (String) encryptionWrapper.get(&quot;iv&quot;);</span>
<span class="nc" id="L90">            String keyId = (String) encryptionWrapper.get(&quot;keyId&quot;);</span>
<span class="nc" id="L91">            String fieldName = (String) encryptionWrapper.get(&quot;fieldName&quot;);</span>
            
<span class="nc" id="L93">            PIIEncryptionService.EncryptedPIIData encryptedData = </span>
                new PIIEncryptionService.EncryptedPIIData(encryptedDataStr, iv, keyId, true, fieldName);
            
<span class="nc" id="L96">            return piiEncryptionService.decrypt(encryptedData);</span>
            
<span class="nc" id="L98">        } catch (JsonProcessingException e) {</span>
<span class="nc" id="L99">            logger.error(&quot;Failed to deserialize encrypted PII data: {}&quot;, e.getMessage());</span>
<span class="nc" id="L100">            return &quot;[ENCRYPTED_PII_PARSE_ERROR]&quot;;</span>
<span class="nc" id="L101">        } catch (Exception e) {</span>
<span class="nc" id="L102">            logger.error(&quot;Failed to decrypt PII data: {}&quot;, e.getMessage());</span>
<span class="nc" id="L103">            return &quot;[ENCRYPTED_PII_DECRYPT_ERROR]&quot;;</span>
        }
    }
    
    /**
     * Set the current field name for context-aware encryption
     */
    public void setCurrentFieldName(String fieldName) {
<span class="nc" id="L111">        this.currentFieldName = fieldName;</span>
<span class="nc" id="L112">    }</span>
}


</pre><div class="footer"><span class="right">Created with <a href="http://www.jacoco.org/jacoco">JaCoCo</a> 0.8.10.202304240956</span></div></body></html>