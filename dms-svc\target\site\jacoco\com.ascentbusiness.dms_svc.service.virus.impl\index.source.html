<?xml version="1.0" encoding="UTF-8"?><!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Strict//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-strict.dtd"><html xmlns="http://www.w3.org/1999/xhtml" lang="en"><head><meta http-equiv="Content-Type" content="text/html;charset=UTF-8"/><link rel="stylesheet" href="../jacoco-resources/report.css" type="text/css"/><link rel="shortcut icon" href="../jacoco-resources/report.gif" type="image/gif"/><title>com.ascentbusiness.dms_svc.service.virus.impl</title><script type="text/javascript" src="../jacoco-resources/sort.js"></script></head><body onload="initialSort(['breadcrumb', 'coveragetable'])"><div class="breadcrumb" id="breadcrumb"><span class="info"><a href="index.html" class="el_class">Classes</a><a href="../jacoco-sessions.html" class="el_session">Sessions</a></span><a href="../index.html" class="el_report">dms-svc</a> &gt; <span class="el_package">com.ascentbusiness.dms_svc.service.virus.impl</span></div><h1>com.ascentbusiness.dms_svc.service.virus.impl</h1><table class="coverage" cellspacing="0" id="coveragetable"><thead><tr><td class="sortable" id="a" onclick="toggleSort(this)">Element</td><td class="down sortable bar" id="b" onclick="toggleSort(this)">Missed Instructions</td><td class="sortable ctr2" id="c" onclick="toggleSort(this)">Cov.</td><td class="sortable bar" id="d" onclick="toggleSort(this)">Missed Branches</td><td class="sortable ctr2" id="e" onclick="toggleSort(this)">Cov.</td><td class="sortable ctr1" id="f" onclick="toggleSort(this)">Missed</td><td class="sortable ctr2" id="g" onclick="toggleSort(this)">Cxty</td><td class="sortable ctr1" id="h" onclick="toggleSort(this)">Missed</td><td class="sortable ctr2" id="i" onclick="toggleSort(this)">Lines</td><td class="sortable ctr1" id="j" onclick="toggleSort(this)">Missed</td><td class="sortable ctr2" id="k" onclick="toggleSort(this)">Methods</td><td class="sortable ctr1" id="l" onclick="toggleSort(this)">Missed</td><td class="sortable ctr2" id="m" onclick="toggleSort(this)">Classes</td></tr></thead><tfoot><tr><td>Total</td><td class="bar">977 of 1,052</td><td class="ctr2">7%</td><td class="bar">75 of 75</td><td class="ctr2">0%</td><td class="ctr1">75</td><td class="ctr2">79</td><td class="ctr1">203</td><td class="ctr2">211</td><td class="ctr1">37</td><td class="ctr2">41</td><td class="ctr1">2</td><td class="ctr2">3</td></tr></tfoot><tbody><tr><td id="a2"><a href="WindowsDefenderVirusScanner.java.html" class="el_source">WindowsDefenderVirusScanner.java</a></td><td class="bar" id="b0"><img src="../jacoco-resources/redbar.gif" width="120" height="10" title="468" alt="468"/></td><td class="ctr2" id="c1">0%</td><td class="bar" id="d0"><img src="../jacoco-resources/redbar.gif" width="120" height="10" title="37" alt="37"/></td><td class="ctr2" id="e0">0%</td><td class="ctr1" id="f0">34</td><td class="ctr2" id="g0">34</td><td class="ctr1" id="h0">87</td><td class="ctr2" id="i0">87</td><td class="ctr1" id="j0">15</td><td class="ctr2" id="k0">15</td><td class="ctr1" id="l0">1</td><td class="ctr2" id="m0">1</td></tr><tr><td id="a0"><a href="ClamAvVirusScanner.java.html" class="el_source">ClamAvVirusScanner.java</a></td><td class="bar" id="b1"><img src="../jacoco-resources/redbar.gif" width="87" height="10" title="343" alt="343"/></td><td class="ctr2" id="c2">0%</td><td class="bar" id="d1"><img src="../jacoco-resources/redbar.gif" width="77" height="10" title="24" alt="24"/></td><td class="ctr2" id="e1">0%</td><td class="ctr1" id="f1">25</td><td class="ctr2" id="g1">25</td><td class="ctr1" id="h1">81</td><td class="ctr2" id="i1">81</td><td class="ctr1" id="j1">13</td><td class="ctr2" id="k1">13</td><td class="ctr1" id="l1">1</td><td class="ctr2" id="m1">1</td></tr><tr><td id="a1"><a href="MockVirusScanner.java.html" class="el_source">MockVirusScanner.java</a></td><td class="bar" id="b2"><img src="../jacoco-resources/redbar.gif" width="42" height="10" title="166" alt="166"/><img src="../jacoco-resources/greenbar.gif" width="19" height="10" title="75" alt="75"/></td><td class="ctr2" id="c0">31%</td><td class="bar" id="d2"><img src="../jacoco-resources/redbar.gif" width="45" height="10" title="14" alt="14"/></td><td class="ctr2" id="e2">0%</td><td class="ctr1" id="f2">16</td><td class="ctr2" id="g2">20</td><td class="ctr1" id="h2">35</td><td class="ctr2" id="i2">43</td><td class="ctr1" id="j2">9</td><td class="ctr2" id="k2">13</td><td class="ctr1" id="l2">0</td><td class="ctr2" id="m2">1</td></tr></tbody></table><div class="footer"><span class="right">Created with <a href="http://www.jacoco.org/jacoco">JaCoCo</a> 0.8.10.202304240956</span></div></body></html>