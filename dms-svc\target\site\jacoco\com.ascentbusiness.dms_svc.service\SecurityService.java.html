<?xml version="1.0" encoding="UTF-8"?><!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Strict//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-strict.dtd"><html xmlns="http://www.w3.org/1999/xhtml" lang="en"><head><meta http-equiv="Content-Type" content="text/html;charset=UTF-8"/><link rel="stylesheet" href="../jacoco-resources/report.css" type="text/css"/><link rel="shortcut icon" href="../jacoco-resources/report.gif" type="image/gif"/><title>SecurityService.java</title><link rel="stylesheet" href="../jacoco-resources/prettify.css" type="text/css"/><script type="text/javascript" src="../jacoco-resources/prettify.js"></script></head><body onload="window['PR_TAB_WIDTH']=4;prettyPrint()"><div class="breadcrumb" id="breadcrumb"><span class="info"><a href="../jacoco-sessions.html" class="el_session">Sessions</a></span><a href="../index.html" class="el_report">dms-svc</a> &gt; <a href="index.source.html" class="el_package">com.ascentbusiness.dms_svc.service</a> &gt; <span class="el_source">SecurityService.java</span></div><h1>SecurityService.java</h1><pre class="source lang-java linenums">package com.ascentbusiness.dms_svc.service;

import com.ascentbusiness.dms_svc.entity.SecurityConfig;
import com.ascentbusiness.dms_svc.entity.SecurityViolation;

import com.ascentbusiness.dms_svc.enums.SecurityViolationType;
import com.ascentbusiness.dms_svc.enums.ViolationSeverity;
import com.ascentbusiness.dms_svc.repository.SecurityConfigRepository;
import com.ascentbusiness.dms_svc.repository.SecurityViolationRepository;
import com.ascentbusiness.dms_svc.util.CorrelationIdUtil;
import jakarta.servlet.http.HttpServletRequest;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;

import java.util.Optional;

/**
 * Service class for comprehensive security management and configuration in the DMS system.
 *
 * &lt;p&gt;This service provides centralized security functionality including configuration management,
 * security violation tracking, and security policy enforcement. It supports dynamic security
 * configuration, violation monitoring, and comprehensive audit logging for compliance.&lt;/p&gt;
 *
 * &lt;p&gt;Key features:&lt;/p&gt;
 * &lt;ul&gt;
 *   &lt;li&gt;&lt;strong&gt;Dynamic Configuration&lt;/strong&gt;: Runtime security configuration management&lt;/li&gt;
 *   &lt;li&gt;&lt;strong&gt;Violation Tracking&lt;/strong&gt;: Comprehensive security violation logging and monitoring&lt;/li&gt;
 *   &lt;li&gt;&lt;strong&gt;Threshold Management&lt;/strong&gt;: Configurable violation thresholds and enforcement&lt;/li&gt;
 *   &lt;li&gt;&lt;strong&gt;Caching Support&lt;/strong&gt;: Cached configuration values for performance&lt;/li&gt;
 *   &lt;li&gt;&lt;strong&gt;Correlation Tracking&lt;/strong&gt;: Full correlation ID support for security events&lt;/li&gt;
 *   &lt;li&gt;&lt;strong&gt;IP Address Capture&lt;/strong&gt;: Client IP detection with proxy header support&lt;/li&gt;
 * &lt;/ul&gt;
 *
 * &lt;p&gt;Security configuration types supported:&lt;/p&gt;
 * &lt;ul&gt;
 *   &lt;li&gt;&lt;strong&gt;Boolean Flags&lt;/strong&gt;: Feature toggles and security switches&lt;/li&gt;
 *   &lt;li&gt;&lt;strong&gt;Numeric Thresholds&lt;/strong&gt;: Rate limits, violation counts, timeouts&lt;/li&gt;
 *   &lt;li&gt;&lt;strong&gt;String Values&lt;/strong&gt;: Security policies, encryption keys, endpoints&lt;/li&gt;
 * &lt;/ul&gt;
 *
 * &lt;p&gt;The service automatically captures contextual information for security events including
 * user identification, IP addresses, user agents, and correlation IDs for comprehensive
 * security monitoring and compliance reporting.&lt;/p&gt;
 *
 * <AUTHOR> Development Team
 * @version 1.0.0
 * @since 1.0.0
 * @see SecurityConfig
 * @see SecurityViolation
 * @see SecurityViolationType
 * @see ViolationSeverity
 */
@Service
@Transactional
<span class="fc" id="L61">public class SecurityService {</span>

<span class="fc" id="L63">    private static final Logger logger = LoggerFactory.getLogger(SecurityService.class);</span>

    @Autowired
    private SecurityConfigRepository securityConfigRepository;

    @Autowired
    private SecurityViolationRepository securityViolationRepository;



    /**
     * Check if a security feature is enabled
     */
    @Cacheable(&quot;securityConfig&quot;)
    public boolean isFeatureEnabled(String configKey) {
<span class="nc" id="L78">        return getBooleanConfigValue(configKey, false);</span>
    }

    /**
     * Get boolean configuration value
     */
    @Cacheable(&quot;securityConfig&quot;)
    public boolean getBooleanConfigValue(String configKey, boolean defaultValue) {
        try {
<span class="nc" id="L87">            Optional&lt;SecurityConfig&gt; config = securityConfigRepository.findByConfigKeyAndIsActive(configKey, true);</span>
<span class="nc bnc" id="L88" title="All 2 branches missed.">            if (config.isPresent()) {</span>
<span class="nc" id="L89">                return config.get().getBooleanValue();</span>
            }
<span class="nc" id="L91">        } catch (Exception e) {</span>
<span class="nc" id="L92">            logger.warn(&quot;Error getting boolean config for key: {}, using default: {}&quot;, configKey, defaultValue, e);</span>
<span class="nc" id="L93">        }</span>
<span class="nc" id="L94">        return defaultValue;</span>
    }

    /**
     * Get integer configuration value
     */
    @Cacheable(&quot;securityConfig&quot;)
    public int getIntConfigValue(String configKey, int defaultValue) {
        try {
<span class="nc" id="L103">            Optional&lt;SecurityConfig&gt; config = securityConfigRepository.findByConfigKeyAndIsActive(configKey, true);</span>
<span class="nc bnc" id="L104" title="All 2 branches missed.">            if (config.isPresent()) {</span>
<span class="nc" id="L105">                Integer value = config.get().getIntegerValue();</span>
<span class="nc bnc" id="L106" title="All 2 branches missed.">                return value != null ? value : defaultValue;</span>
            }
<span class="nc" id="L108">        } catch (Exception e) {</span>
<span class="nc" id="L109">            logger.warn(&quot;Error getting int config for key: {}, using default: {}&quot;, configKey, defaultValue, e);</span>
<span class="nc" id="L110">        }</span>
<span class="nc" id="L111">        return defaultValue;</span>
    }

    /**
     * Get long configuration value
     */
    @Cacheable(&quot;securityConfig&quot;)
    public long getLongConfigValue(String configKey, long defaultValue) {
        try {
<span class="nc" id="L120">            Optional&lt;SecurityConfig&gt; config = securityConfigRepository.findByConfigKeyAndIsActive(configKey, true);</span>
<span class="nc bnc" id="L121" title="All 2 branches missed.">            if (config.isPresent()) {</span>
<span class="nc" id="L122">                Long value = config.get().getLongValue();</span>
<span class="nc bnc" id="L123" title="All 2 branches missed.">                return value != null ? value : defaultValue;</span>
            }
<span class="nc" id="L125">        } catch (Exception e) {</span>
<span class="nc" id="L126">            logger.warn(&quot;Error getting long config for key: {}, using default: {}&quot;, configKey, defaultValue, e);</span>
<span class="nc" id="L127">        }</span>
<span class="nc" id="L128">        return defaultValue;</span>
    }

    /**
     * Get string configuration value
     */
    @Cacheable(&quot;securityConfig&quot;)
    public String getStringConfigValue(String configKey, String defaultValue) {
        try {
<span class="nc" id="L137">            Optional&lt;SecurityConfig&gt; config = securityConfigRepository.findByConfigKeyAndIsActive(configKey, true);</span>
<span class="nc bnc" id="L138" title="All 2 branches missed.">            if (config.isPresent()) {</span>
<span class="nc" id="L139">                return config.get().getConfigValue();</span>
            }
<span class="nc" id="L141">        } catch (Exception e) {</span>
<span class="nc" id="L142">            logger.warn(&quot;Error getting string config for key: {}, using default: {}&quot;, configKey, defaultValue, e);</span>
<span class="nc" id="L143">        }</span>
<span class="nc" id="L144">        return defaultValue;</span>
    }

    /**
     * Log security violation
     */
    public void logSecurityViolation(String userId, SecurityViolationType violationType, 
                                   ViolationSeverity severity, Long documentId, 
                                   String attemptedAction, String details) {
        try {
<span class="nc" id="L154">            HttpServletRequest request = getCurrentRequest();</span>

            // Capture correlation ID for traceability
<span class="nc" id="L157">            String correlationId = CorrelationIdUtil.getCurrentCorrelationId();</span>

<span class="nc" id="L159">            SecurityViolation violation = SecurityViolation.builder()</span>
<span class="nc" id="L160">                    .userId(userId)</span>
<span class="nc" id="L161">                    .violationType(violationType)</span>
<span class="nc" id="L162">                    .severity(severity)</span>
<span class="nc" id="L163">                    .attemptedAction(attemptedAction)</span>
<span class="nc" id="L164">                    .violationDetails(details)</span>
<span class="nc" id="L165">                    .correlationId(correlationId)</span>
<span class="nc bnc" id="L166" title="All 2 branches missed.">                    .ipAddress(request != null ? getClientIpAddress(request) : null)</span>
<span class="nc bnc" id="L167" title="All 2 branches missed.">                    .userAgent(request != null ? request.getHeader(&quot;User-Agent&quot;) : null)</span>
<span class="nc" id="L168">                    .build();</span>
            
<span class="nc bnc" id="L170" title="All 2 branches missed.">            if (documentId != null) {</span>
                // Set document reference if needed
                // violation.setDocument(documentRepository.findById(documentId).orElse(null));
            }
            
<span class="nc" id="L175">            securityViolationRepository.save(violation);</span>

<span class="nc" id="L177">            logger.warn(&quot;Security violation logged for user {}: {} - {} (Correlation ID: {})&quot;,</span>
                    userId, violationType, details, correlationId);
            
<span class="nc" id="L180">        } catch (Exception e) {</span>
<span class="nc" id="L181">            logger.error(&quot;Error logging security violation for user: &quot; + userId, e);</span>
<span class="nc" id="L182">        }</span>
<span class="nc" id="L183">    }</span>

    /**
     * Check if user has exceeded violation threshold
     */
    public boolean hasExceededViolationThreshold(String userId) {
<span class="nc" id="L189">        int threshold = getIntConfigValue(&quot;SECURITY_VIOLATION_THRESHOLD&quot;, 5);</span>
<span class="nc" id="L190">        int violationCount = securityViolationRepository.countUnresolvedByUserId(userId);</span>
<span class="nc bnc" id="L191" title="All 2 branches missed.">        return violationCount &gt;= threshold;</span>
    }

    /**
     * Resolve security violation
     */
    public void resolveViolation(Long violationId, String resolvedBy) {
        try {
<span class="nc" id="L199">            Optional&lt;SecurityViolation&gt; violation = securityViolationRepository.findById(violationId);</span>
<span class="nc bnc" id="L200" title="All 2 branches missed.">            if (violation.isPresent()) {</span>
<span class="nc" id="L201">                violation.get().resolveViolation(resolvedBy);</span>
<span class="nc" id="L202">                securityViolationRepository.save(violation.get());</span>

<span class="nc" id="L204">                logger.info(&quot;Security violation {} resolved by {}&quot;, violationId, resolvedBy);</span>
            }
<span class="nc" id="L206">        } catch (Exception e) {</span>
<span class="nc" id="L207">            logger.error(&quot;Error resolving security violation: &quot; + violationId, e);</span>
<span class="nc" id="L208">        }</span>
<span class="nc" id="L209">    }</span>

    /**
     * Update security configuration
     */
    public void updateSecurityConfig(String configKey, String configValue, String updatedBy) {
        try {
<span class="nc" id="L216">            Optional&lt;SecurityConfig&gt; existingConfig = securityConfigRepository.findByConfigKey(configKey);</span>
            
            SecurityConfig config;
<span class="nc bnc" id="L219" title="All 2 branches missed.">            if (existingConfig.isPresent()) {</span>
<span class="nc" id="L220">                config = existingConfig.get();</span>
<span class="nc" id="L221">                config.setConfigValue(configValue);</span>
            } else {
<span class="nc" id="L223">                config = SecurityConfig.builder()</span>
<span class="nc" id="L224">                        .configKey(configKey)</span>
<span class="nc" id="L225">                        .configValue(configValue)</span>
<span class="nc" id="L226">                        .description(&quot;Runtime configuration&quot;)</span>
<span class="nc" id="L227">                        .build();</span>
            }
            
<span class="nc" id="L230">            securityConfigRepository.save(config);</span>
            
<span class="nc" id="L232">            logger.info(&quot;Security config updated: {} = {} by {}&quot;, configKey, configValue, updatedBy);</span>
            
<span class="nc" id="L234">        } catch (Exception e) {</span>
<span class="nc" id="L235">            logger.error(&quot;Error updating security config: &quot; + configKey, e);</span>
<span class="nc" id="L236">        }</span>
<span class="nc" id="L237">    }</span>

    /**
     * Get current HTTP request
     */
    private HttpServletRequest getCurrentRequest() {
        try {
<span class="nc" id="L244">            ServletRequestAttributes attributes = (ServletRequestAttributes) RequestContextHolder.getRequestAttributes();</span>
<span class="nc bnc" id="L245" title="All 2 branches missed.">            return attributes != null ? attributes.getRequest() : null;</span>
<span class="nc" id="L246">        } catch (Exception e) {</span>
<span class="nc" id="L247">            return null;</span>
        }
    }

    /**
     * Get client IP address from request
     */
    private String getClientIpAddress(HttpServletRequest request) {
<span class="nc" id="L255">        String xForwardedFor = request.getHeader(&quot;X-Forwarded-For&quot;);</span>
<span class="nc bnc" id="L256" title="All 6 branches missed.">        if (xForwardedFor != null &amp;&amp; !xForwardedFor.isEmpty() &amp;&amp; !&quot;unknown&quot;.equalsIgnoreCase(xForwardedFor)) {</span>
<span class="nc" id="L257">            return xForwardedFor.split(&quot;,&quot;)[0].trim();</span>
        }
        
<span class="nc" id="L260">        String xRealIP = request.getHeader(&quot;X-Real-IP&quot;);</span>
<span class="nc bnc" id="L261" title="All 6 branches missed.">        if (xRealIP != null &amp;&amp; !xRealIP.isEmpty() &amp;&amp; !&quot;unknown&quot;.equalsIgnoreCase(xRealIP)) {</span>
<span class="nc" id="L262">            return xRealIP;</span>
        }
        
<span class="nc" id="L265">        return request.getRemoteAddr();</span>
    }

    /**
     * Validate JWT token with enhanced security
     */
    public boolean validateTokenStrict(String token) {
<span class="nc bnc" id="L272" title="All 2 branches missed.">        if (!isFeatureEnabled(&quot;TOKEN_VALIDATION_STRICT_MODE&quot;)) {</span>
<span class="nc" id="L273">            return true; // Default validation</span>
        }
        
        // Enhanced token validation logic would go here
        // For now, return true but this can be expanded
<span class="nc" id="L278">        return true;</span>
    }

    /**
     * Check rate limit for permission operations
     * @deprecated Use RedisRateLimitService with @RateLimit annotation instead
     */
    @Deprecated
    public boolean isWithinRateLimit(String userId, String operation) {
<span class="nc bnc" id="L287" title="All 2 branches missed.">        if (!isFeatureEnabled(&quot;RATE_LIMIT_PERMISSION_OPERATIONS&quot;)) {</span>
<span class="nc" id="L288">            return true;</span>
        }

        // This method is deprecated in favor of the new Redis-based rate limiting
        // Rate limiting is now handled by RedisRateLimitService and @RateLimit annotations
<span class="nc" id="L293">        logger.debug(&quot;Legacy rate limit check for user: {}, operation: {} - using new @RateLimit annotations instead&quot;, userId, operation);</span>
<span class="nc" id="L294">        return true;</span>
    }
}
</pre><div class="footer"><span class="right">Created with <a href="http://www.jacoco.org/jacoco">JaCoCo</a> 0.8.10.202304240956</span></div></body></html>