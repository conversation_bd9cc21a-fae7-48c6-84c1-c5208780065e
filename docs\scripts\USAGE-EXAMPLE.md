# Quick Usage Examples

## For Windows Users (Recommended)

### Run the complete rebuild and push process:
```powershell
.\local-docker-rebuild.ps1
```

### Run without pushing to Docker Hub:
```powershell
.\local-docker-rebuild.ps1 -SkipPush
```

### Use a different Docker Hub username:
```powershell
.\local-docker-rebuild.ps1 -DockerUsername "yourusername"
```

## For Linux/macOS/WSL Users

### Make the script executable and run:
```bash
chmod +x local-docker-rebuild.sh
./local-docker-rebuild.sh
```

## What to Expect

1. **Confirmation Prompt**: The script will ask if you want to continue
2. **Docker Cleanup**: All existing containers, images, and volumes will be removed
3. **Image Building**: Both DMS and Notification services will be built from scratch
4. **Service Startup**: All infrastructure and application services will start
5. **Health Checks**: Script will wait for services to be healthy
6. **Docker Hub Push** (Optional): You'll be asked if you want to push to Docker Hub

## After Successful Completion

Your services will be available at:
- DMS Service: http://localhost:9093/actuator/health
- Notification Service: http://localhost:9091/actuator/health
- Grafana: http://localhost:3000 (admin/admin)
- RabbitMQ: http://localhost:15672 (admin/admin123)

## Docker Hub Images

After pushing, anyone can use your images:
```bash
docker pull anurag98cs/grc-dms-svc:latest
docker pull anurag98cs/grc-notification-svc:latest
```

## Stopping Services

To stop all services:
```bash
docker-compose -f docker-compose.services.yml --env-file .env.local-dev down
docker-compose -f docker-compose.shared.yml --env-file .env.local-dev down