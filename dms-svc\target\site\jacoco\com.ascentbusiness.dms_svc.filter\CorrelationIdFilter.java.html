<?xml version="1.0" encoding="UTF-8"?><!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Strict//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-strict.dtd"><html xmlns="http://www.w3.org/1999/xhtml" lang="en"><head><meta http-equiv="Content-Type" content="text/html;charset=UTF-8"/><link rel="stylesheet" href="../jacoco-resources/report.css" type="text/css"/><link rel="shortcut icon" href="../jacoco-resources/report.gif" type="image/gif"/><title>CorrelationIdFilter.java</title><link rel="stylesheet" href="../jacoco-resources/prettify.css" type="text/css"/><script type="text/javascript" src="../jacoco-resources/prettify.js"></script></head><body onload="window['PR_TAB_WIDTH']=4;prettyPrint()"><div class="breadcrumb" id="breadcrumb"><span class="info"><a href="../jacoco-sessions.html" class="el_session">Sessions</a></span><a href="../index.html" class="el_report">dms-svc</a> &gt; <a href="index.source.html" class="el_package">com.ascentbusiness.dms_svc.filter</a> &gt; <span class="el_source">CorrelationIdFilter.java</span></div><h1>CorrelationIdFilter.java</h1><pre class="source lang-java linenums">package com.ascentbusiness.dms_svc.filter;

import com.ascentbusiness.dms_svc.util.TracingUtil;
import io.micrometer.tracing.Tracer;
import jakarta.servlet.FilterChain;
import jakarta.servlet.ServletException;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.slf4j.MDC;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.lang.NonNull;
import org.springframework.util.StringUtils;
import org.springframework.web.filter.OncePerRequestFilter;

import java.io.IOException;
import java.util.UUID;

/**
 * Filter to handle correlation ID processing for request tracing.
 * 
 * This filter:
 * 1. Reads correlation ID from client request headers
 * 2. Sets correlation ID in MDC for logging
 * 3. Adds correlation ID to response headers
 * 4. Cleans up MDC after request processing
 * 
 * The correlation ID is expected to be provided by the client in the request header.
 * If no correlation ID is provided, a new one will be generated for internal tracing.
 */
<span class="fc" id="L33">public class CorrelationIdFilter extends OncePerRequestFilter {</span>

<span class="fc" id="L35">    private static final Logger logger = LoggerFactory.getLogger(CorrelationIdFilter.class);</span>

    private static final String MDC_CORRELATION_ID_KEY = &quot;correlationId&quot;;
    private static final String RESPONSE_HEADER_SERVICE_NAME = &quot;X-Service-Name&quot;;
    private static final String RESPONSE_HEADER_SERVICE_VERSION = &quot;X-Service-Version&quot;;
    private static final String SERVICE_NAME = &quot;dms-service&quot;;
    private static final String SERVICE_VERSION = &quot;1.0.0&quot;;

    @Value(&quot;${dms.correlation.header-name:X-Correlation-ID}&quot;)
    private String correlationIdHeaderName;

    @Autowired(required = false)
    private Tracer tracer;

    @Autowired(required = false)
    private TracingUtil tracingUtil;

    @Override
    protected void doFilterInternal(@NonNull HttpServletRequest request,
                                  @NonNull HttpServletResponse response,
                                  @NonNull FilterChain filterChain)
            throws ServletException, IOException {

        // Extract correlation ID from request header
<span class="nc" id="L59">        String correlationId = extractCorrelationId(request);</span>

        // DEBUG: Log that the filter is being invoked
<span class="nc" id="L62">        logger.info(&quot;CorrelationIdFilter invoked for {} {} with correlationId: {}&quot;,</span>
<span class="nc" id="L63">                request.getMethod(), request.getRequestURI(), correlationId);</span>

        // Set correlation ID in MDC for logging
<span class="nc" id="L66">        MDC.put(MDC_CORRELATION_ID_KEY, correlationId);</span>

        // DEBUG: Verify MDC was set
<span class="nc" id="L69">        logger.info(&quot;MDC correlationId set to: {}&quot;, MDC.get(MDC_CORRELATION_ID_KEY));</span>

        // Update MDC with tracing information if tracing is available
<span class="nc bnc" id="L72" title="All 2 branches missed.">        if (tracingUtil != null) {</span>
<span class="nc" id="L73">            tracingUtil.updateMDCWithCurrentTrace();</span>
        }

        // Add correlation ID and service metadata to response headers
<span class="nc" id="L77">        addResponseHeaders(response, correlationId);</span>

        // Add request context to current span if tracing is available
<span class="nc bnc" id="L80" title="All 2 branches missed.">        if (tracingUtil != null) {</span>
<span class="nc" id="L81">            tracingUtil.addTagToCurrentSpan(&quot;http.method&quot;, request.getMethod());</span>
<span class="nc" id="L82">            tracingUtil.addTagToCurrentSpan(&quot;http.url&quot;, request.getRequestURI());</span>
<span class="nc" id="L83">            tracingUtil.addTagToCurrentSpan(&quot;correlation.id&quot;, correlationId);</span>

<span class="nc" id="L85">            String userAgent = request.getHeader(&quot;User-Agent&quot;);</span>
<span class="nc bnc" id="L86" title="All 2 branches missed.">            if (userAgent != null) {</span>
<span class="nc" id="L87">                tracingUtil.addTagToCurrentSpan(&quot;http.user_agent&quot;, userAgent);</span>
            }

<span class="nc" id="L90">            String clientIp = getClientIpAddress(request);</span>
<span class="nc bnc" id="L91" title="All 2 branches missed.">            if (clientIp != null) {</span>
<span class="nc" id="L92">                tracingUtil.addTagToCurrentSpan(&quot;http.client_ip&quot;, clientIp);</span>
            }
        }

<span class="nc" id="L96">        logger.debug(&quot;Correlation ID set for request: {} - {}&quot;,</span>
<span class="nc" id="L97">                request.getMethod(), request.getRequestURI());</span>

        try {
            // Continue with the filter chain
<span class="nc" id="L101">            filterChain.doFilter(request, response);</span>
        } finally {
            // Clean up MDC after the entire request is processed
            // This ensures correlationId is available throughout the request lifecycle
            // but gets cleaned up to prevent memory leaks
            //
            // Note: We delay the cleanup slightly to ensure all logging from the request
            // processing is complete, including any async operations that might still
            // be using the MDC context
<span class="nc" id="L110">            cleanupMDC();</span>
        }
<span class="nc" id="L112">    }</span>

    /**
     * Clean up MDC context with proper timing to ensure all logging is complete
     */
    private void cleanupMDC() {
        // Clean up correlation ID and tracing-specific MDC entries
<span class="nc" id="L119">        MDC.remove(MDC_CORRELATION_ID_KEY);</span>

<span class="nc bnc" id="L121" title="All 2 branches missed.">        if (tracingUtil != null) {</span>
<span class="nc" id="L122">            tracingUtil.clearTraceFromMDC();</span>
        }

<span class="nc" id="L125">        logger.debug(&quot;Correlation ID and tracing information cleaned up after request completion&quot;);</span>
<span class="nc" id="L126">    }</span>

    /**
     * Extract correlation ID from request header.
     * If not provided by client, generate a new one for internal tracing.
     */
    private String extractCorrelationId(HttpServletRequest request) {
<span class="nc" id="L133">        String correlationId = request.getHeader(correlationIdHeaderName);</span>
        
<span class="nc bnc" id="L135" title="All 2 branches missed.">        if (StringUtils.hasText(correlationId)) {</span>
<span class="nc" id="L136">            logger.debug(&quot;Using client-provided correlation ID: {}&quot;, correlationId);</span>
<span class="nc" id="L137">            return correlationId.trim();</span>
        } else {
            // Generate new correlation ID for internal tracing if client didn't provide one
<span class="nc" id="L140">            String generatedId = UUID.randomUUID().toString();</span>
<span class="nc" id="L141">            logger.debug(&quot;Generated new correlation ID (client didn't provide): {}&quot;, generatedId);</span>
<span class="nc" id="L142">            return generatedId;</span>
        }
    }

    /**
     * Add correlation ID and service metadata to response headers.
     */
    private void addResponseHeaders(HttpServletResponse response, String correlationId) {
        // Echo correlation ID back to client
<span class="nc" id="L151">        response.setHeader(correlationIdHeaderName, correlationId);</span>
        
        // Add service metadata for debugging and monitoring
<span class="nc" id="L154">        response.setHeader(RESPONSE_HEADER_SERVICE_NAME, SERVICE_NAME);</span>
<span class="nc" id="L155">        response.setHeader(RESPONSE_HEADER_SERVICE_VERSION, SERVICE_VERSION);</span>
        
<span class="nc" id="L157">        logger.debug(&quot;Added response headers - Correlation ID: {}, Service: {}&quot;, </span>
                correlationId, SERVICE_NAME);
<span class="nc" id="L159">    }</span>

    /**
     * Get client IP address from request, considering proxy headers
     */
    private String getClientIpAddress(HttpServletRequest request) {
<span class="nc" id="L165">        String xForwardedFor = request.getHeader(&quot;X-Forwarded-For&quot;);</span>
<span class="nc bnc" id="L166" title="All 2 branches missed.">        if (StringUtils.hasText(xForwardedFor)) {</span>
<span class="nc" id="L167">            return xForwardedFor.split(&quot;,&quot;)[0].trim();</span>
        }

<span class="nc" id="L170">        String xRealIp = request.getHeader(&quot;X-Real-IP&quot;);</span>
<span class="nc bnc" id="L171" title="All 2 branches missed.">        if (StringUtils.hasText(xRealIp)) {</span>
<span class="nc" id="L172">            return xRealIp.trim();</span>
        }

<span class="nc" id="L175">        return request.getRemoteAddr();</span>
    }

    /**
     * Skip filter for static resources and health check endpoints to improve performance.
     */
    @Override
    protected boolean shouldNotFilter(@NonNull HttpServletRequest request) {
<span class="nc" id="L183">        String path = request.getRequestURI();</span>

        // Skip for static resources
<span class="nc bnc" id="L186" title="All 2 branches missed.">        if (path.startsWith(&quot;/static/&quot;) ||</span>
<span class="nc bnc" id="L187" title="All 2 branches missed.">            path.startsWith(&quot;/css/&quot;) ||</span>
<span class="nc bnc" id="L188" title="All 2 branches missed.">            path.startsWith(&quot;/js/&quot;) ||</span>
<span class="nc bnc" id="L189" title="All 2 branches missed.">            path.startsWith(&quot;/images/&quot;) ||</span>
<span class="nc bnc" id="L190" title="All 2 branches missed.">            path.endsWith(&quot;.ico&quot;)) {</span>
<span class="nc" id="L191">            return true;</span>
        }

        // Skip for basic health checks (but not detailed actuator endpoints)
<span class="nc bnc" id="L195" title="All 4 branches missed.">        if (path.equals(&quot;/health&quot;) || path.equals(&quot;/ping&quot;)) {</span>
<span class="nc" id="L196">            return true;</span>
        }

<span class="nc" id="L199">        return false;</span>
    }
}

</pre><div class="footer"><span class="right">Created with <a href="http://www.jacoco.org/jacoco">JaCoCo</a> 0.8.10.202304240956</span></div></body></html>