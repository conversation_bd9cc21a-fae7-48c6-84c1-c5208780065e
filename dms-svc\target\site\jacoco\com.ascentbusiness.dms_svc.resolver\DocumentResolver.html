<?xml version="1.0" encoding="UTF-8"?><!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Strict//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-strict.dtd"><html xmlns="http://www.w3.org/1999/xhtml" lang="en"><head><meta http-equiv="Content-Type" content="text/html;charset=UTF-8"/><link rel="stylesheet" href="../jacoco-resources/report.css" type="text/css"/><link rel="shortcut icon" href="../jacoco-resources/report.gif" type="image/gif"/><title>DocumentResolver</title><script type="text/javascript" src="../jacoco-resources/sort.js"></script></head><body onload="initialSort(['breadcrumb'])"><div class="breadcrumb" id="breadcrumb"><span class="info"><a href="../jacoco-sessions.html" class="el_session">Sessions</a></span><a href="../index.html" class="el_report">dms-svc</a> &gt; <a href="index.html" class="el_package">com.ascentbusiness.dms_svc.resolver</a> &gt; <span class="el_class">DocumentResolver</span></div><h1>DocumentResolver</h1><table class="coverage" cellspacing="0" id="coveragetable"><thead><tr><td class="sortable" id="a" onclick="toggleSort(this)">Element</td><td class="down sortable bar" id="b" onclick="toggleSort(this)">Missed Instructions</td><td class="sortable ctr2" id="c" onclick="toggleSort(this)">Cov.</td><td class="sortable bar" id="d" onclick="toggleSort(this)">Missed Branches</td><td class="sortable ctr2" id="e" onclick="toggleSort(this)">Cov.</td><td class="sortable ctr1" id="f" onclick="toggleSort(this)">Missed</td><td class="sortable ctr2" id="g" onclick="toggleSort(this)">Cxty</td><td class="sortable ctr1" id="h" onclick="toggleSort(this)">Missed</td><td class="sortable ctr2" id="i" onclick="toggleSort(this)">Lines</td><td class="sortable ctr1" id="j" onclick="toggleSort(this)">Missed</td><td class="sortable ctr2" id="k" onclick="toggleSort(this)">Methods</td></tr></thead><tfoot><tr><td>Total</td><td class="bar">2,457 of 2,647</td><td class="ctr2">7%</td><td class="bar">196 of 207</td><td class="ctr2">5%</td><td class="ctr1">207</td><td class="ctr2">213</td><td class="ctr1">617</td><td class="ctr2">664</td><td class="ctr1">104</td><td class="ctr2">108</td></tr></tfoot><tbody><tr><td id="a98"><a href="DocumentResolver.java.html#L550" class="el_method">uploadDocumentEnhanced(EnhancedDocumentUploadInput)</a></td><td class="bar" id="b0"><img src="../jacoco-resources/redbar.gif" width="100" height="10" title="199" alt="199"/></td><td class="ctr2" id="c4">0%</td><td class="bar" id="d1"><img src="../jacoco-resources/redbar.gif" width="94" height="10" title="22" alt="22"/></td><td class="ctr2" id="e2">0%</td><td class="ctr1" id="f1">12</td><td class="ctr2" id="g1">12</td><td class="ctr1" id="h1">63</td><td class="ctr2" id="i1">63</td><td class="ctr1" id="j0">1</td><td class="ctr2" id="k0">1</td></tr><tr><td id="a107"><a href="DocumentResolver.java.html#L339" class="el_method">validateFile(MultipartFile, FileValidationOptionsInput)</a></td><td class="bar" id="b1"><img src="../jacoco-resources/redbar.gif" width="96" height="10" title="192" alt="192"/></td><td class="ctr2" id="c5">0%</td><td class="bar" id="d0"><img src="../jacoco-resources/redbar.gif" width="120" height="10" title="28" alt="28"/></td><td class="ctr2" id="e3">0%</td><td class="ctr1" id="f0">15</td><td class="ctr2" id="g0">15</td><td class="ctr1" id="h0">66</td><td class="ctr2" id="i0">66</td><td class="ctr1" id="j1">1</td><td class="ctr2" id="k1">1</td></tr><tr><td id="a97"><a href="DocumentResolver.java.html#L934" class="el_method">uploadChunk(ChunkUploadInput)</a></td><td class="bar" id="b2"><img src="../jacoco-resources/redbar.gif" width="52" height="10" title="104" alt="104"/></td><td class="ctr2" id="c6">0%</td><td class="bar" id="d3"><img src="../jacoco-resources/redbar.gif" width="34" height="10" title="8" alt="8"/></td><td class="ctr2" id="e4">0%</td><td class="ctr1" id="f4">5</td><td class="ctr2" id="g6">5</td><td class="ctr1" id="h2">23</td><td class="ctr2" id="i3">23</td><td class="ctr1" id="j2">1</td><td class="ctr2" id="k2">1</td></tr><tr><td id="a49"><a href="DocumentResolver.java.html#L511" class="el_method">getActiveUploadSessions(String)</a></td><td class="bar" id="b3"><img src="../jacoco-resources/redbar.gif" width="41" height="10" title="82" alt="82"/></td><td class="ctr2" id="c7">0%</td><td class="bar" id="d20"><img src="../jacoco-resources/redbar.gif" width="8" height="10" title="2" alt="2"/></td><td class="ctr2" id="e5">0%</td><td class="ctr1" id="f20">2</td><td class="ctr2" id="g20">2</td><td class="ctr1" id="h4">21</td><td class="ctr2" id="i5">21</td><td class="ctr1" id="j3">1</td><td class="ctr2" id="k3">1</td></tr><tr><td id="a91"><a href="DocumentResolver.java.html#L1097" class="el_method">searchDocumentsByClassification(String, String, String, String)</a></td><td class="bar" id="b4"><img src="../jacoco-resources/redbar.gif" width="40" height="10" title="81" alt="81"/></td><td class="ctr2" id="c8">0%</td><td class="bar" id="d4"><img src="../jacoco-resources/redbar.gif" width="34" height="10" title="8" alt="8"/></td><td class="ctr2" id="e6">0%</td><td class="ctr1" id="f5">5</td><td class="ctr2" id="g7">5</td><td class="ctr1" id="h10">15</td><td class="ctr2" id="i10">15</td><td class="ctr1" id="j4">1</td><td class="ctr2" id="k4">1</td></tr><tr><td id="a93"><a href="DocumentResolver.java.html#L1130" class="el_method">searchDocumentsByOwnership(String, String, String, Boolean)</a></td><td class="bar" id="b5"><img src="../jacoco-resources/redbar.gif" width="40" height="10" title="81" alt="81"/></td><td class="ctr2" id="c9">0%</td><td class="bar" id="d5"><img src="../jacoco-resources/redbar.gif" width="34" height="10" title="8" alt="8"/></td><td class="ctr2" id="e7">0%</td><td class="ctr1" id="f6">5</td><td class="ctr2" id="g8">5</td><td class="ctr1" id="h11">15</td><td class="ctr2" id="i11">15</td><td class="ctr1" id="j5">1</td><td class="ctr2" id="k5">1</td></tr><tr><td id="a92"><a href="DocumentResolver.java.html#L1163" class="el_method">searchDocumentsByCompliance(String, String, String, String)</a></td><td class="bar" id="b6"><img src="../jacoco-resources/redbar.gif" width="40" height="10" title="81" alt="81"/></td><td class="ctr2" id="c10">0%</td><td class="bar" id="d6"><img src="../jacoco-resources/redbar.gif" width="34" height="10" title="8" alt="8"/></td><td class="ctr2" id="e8">0%</td><td class="ctr1" id="f7">5</td><td class="ctr2" id="g9">5</td><td class="ctr1" id="h12">15</td><td class="ctr2" id="i12">15</td><td class="ctr1" id="j6">1</td><td class="ctr2" id="k6">1</td></tr><tr><td id="a102"><a href="DocumentResolver.java.html#L725" class="el_method">uploadDocumentFromPathOrUrl(UploadFromPathOrUrlInput)</a></td><td class="bar" id="b7"><img src="../jacoco-resources/redbar.gif" width="37" height="10" title="74" alt="74"/><img src="../jacoco-resources/greenbar.gif" width="82" height="10" title="164" alt="164"/></td><td class="ctr2" id="c2">68%</td><td class="bar" id="d19"><img src="../jacoco-resources/redbar.gif" width="12" height="10" title="3" alt="3"/><img src="../jacoco-resources/greenbar.gif" width="30" height="10" title="7" alt="7"/></td><td class="ctr2" id="e0">70%</td><td class="ctr1" id="f12">3</td><td class="ctr2" id="g2">6</td><td class="ctr1" id="h8">16</td><td class="ctr2" id="i2">55</td><td class="ctr1" id="j104">0</td><td class="ctr2" id="k7">1</td></tr><tr><td id="a51"><a href="DocumentResolver.java.html#L306" class="el_method">getChunkedUploadSession(String)</a></td><td class="bar" id="b8"><img src="../jacoco-resources/redbar.gif" width="36" height="10" title="73" alt="73"/></td><td class="ctr2" id="c11">0%</td><td class="bar" id="d21"><img src="../jacoco-resources/redbar.gif" width="8" height="10" title="2" alt="2"/></td><td class="ctr2" id="e9">0%</td><td class="ctr1" id="f21">2</td><td class="ctr2" id="g21">2</td><td class="ctr1" id="h5">19</td><td class="ctr2" id="i6">19</td><td class="ctr1" id="j7">1</td><td class="ctr2" id="k8">1</td></tr><tr><td id="a61"><a href="DocumentResolver.java.html#L265" class="el_method">getUploadProgress(String)</a></td><td class="bar" id="b9"><img src="../jacoco-resources/redbar.gif" width="34" height="10" title="68" alt="68"/></td><td class="ctr2" id="c12">0%</td><td class="bar" id="d11"><img src="../jacoco-resources/redbar.gif" width="25" height="10" title="6" alt="6"/></td><td class="ctr2" id="e10">0%</td><td class="ctr1" id="f11">4</td><td class="ctr2" id="g12">4</td><td class="ctr1" id="h3">22</td><td class="ctr2" id="i4">22</td><td class="ctr1" id="j8">1</td><td class="ctr2" id="k9">1</td></tr><tr><td id="a41"><a href="DocumentResolver.java.html#L222" class="el_method">documentProcessingStatus(String)</a></td><td class="bar" id="b10"><img src="../jacoco-resources/redbar.gif" width="32" height="10" title="64" alt="64"/></td><td class="ctr2" id="c13">0%</td><td class="bar" id="d12"><img src="../jacoco-resources/redbar.gif" width="17" height="10" title="4" alt="4"/></td><td class="ctr2" id="e11">0%</td><td class="ctr1" id="f13">3</td><td class="ctr2" id="g13">3</td><td class="ctr1" id="h13">13</td><td class="ctr2" id="i13">13</td><td class="ctr1" id="j9">1</td><td class="ctr2" id="k10">1</td></tr><tr><td id="a29"><a href="DocumentResolver.java.html#L448" class="el_method">createFileInfo(String, Long, String, boolean)</a></td><td class="bar" id="b11"><img src="../jacoco-resources/redbar.gif" width="32" height="10" title="64" alt="64"/></td><td class="ctr2" id="c14">0%</td><td class="bar" id="d2"><img src="../jacoco-resources/redbar.gif" width="42" height="10" title="10" alt="10"/></td><td class="ctr2" id="e12">0%</td><td class="ctr1" id="f2">6</td><td class="ctr2" id="g3">6</td><td class="ctr1" id="h6">19</td><td class="ctr2" id="i7">19</td><td class="ctr1" id="j10">1</td><td class="ctr2" id="k11">1</td></tr><tr><td id="a28"><a href="DocumentResolver.java.html#L648" class="el_method">convertToLegacyInput(EnhancedDocumentUploadInput)</a></td><td class="bar" id="b12"><img src="../jacoco-resources/redbar.gif" width="31" height="10" title="62" alt="62"/></td><td class="ctr2" id="c15">0%</td><td class="bar" id="d22"><img src="../jacoco-resources/redbar.gif" width="8" height="10" title="2" alt="2"/></td><td class="ctr2" id="e13">0%</td><td class="ctr1" id="f22">2</td><td class="ctr2" id="g22">2</td><td class="ctr1" id="h7">17</td><td class="ctr2" id="i8">17</td><td class="ctr1" id="j11">1</td><td class="ctr2" id="k12">1</td></tr><tr><td id="a62"><a href="DocumentResolver.java.html#L482" class="el_method">getUploadStatistics(OffsetDateTime, OffsetDateTime, String)</a></td><td class="bar" id="b13"><img src="../jacoco-resources/redbar.gif" width="28" height="10" title="56" alt="56"/></td><td class="ctr2" id="c16">0%</td><td class="bar" id="d39"/><td class="ctr2" id="e39">n/a</td><td class="ctr1" id="f39">1</td><td class="ctr2" id="g39">1</td><td class="ctr1" id="h9">16</td><td class="ctr2" id="i9">16</td><td class="ctr1" id="j12">1</td><td class="ctr2" id="k13">1</td></tr><tr><td id="a100"><a href="DocumentResolver.java.html#L678" class="el_method">uploadDocumentFromPath(UploadDocumentFromPathInput)</a></td><td class="bar" id="b14"><img src="../jacoco-resources/redbar.gif" width="23" height="10" title="46" alt="46"/></td><td class="ctr2" id="c17">0%</td><td class="bar" id="d40"/><td class="ctr2" id="e40">n/a</td><td class="ctr1" id="f40">1</td><td class="ctr2" id="g40">1</td><td class="ctr1" id="h14">11</td><td class="ctr2" id="i15">11</td><td class="ctr1" id="j13">1</td><td class="ctr2" id="k14">1</td></tr><tr><td id="a30"><a href="DocumentResolver.java.html#L1311" class="el_method">createPageable(PaginationInput)</a></td><td class="bar" id="b15"><img src="../jacoco-resources/redbar.gif" width="20" height="10" title="41" alt="41"/></td><td class="ctr2" id="c18">0%</td><td class="bar" id="d13"><img src="../jacoco-resources/redbar.gif" width="17" height="10" title="4" alt="4"/></td><td class="ctr2" id="e14">0%</td><td class="ctr1" id="f14">3</td><td class="ctr2" id="g14">3</td><td class="ctr1" id="h15">9</td><td class="ctr2" id="i16">9</td><td class="ctr1" id="j14">1</td><td class="ctr2" id="k15">1</td></tr><tr><td id="a82"><a href="DocumentResolver.java.html#L1327" class="el_method">mapToDocumentPage(Page)</a></td><td class="bar" id="b16"><img src="../jacoco-resources/redbar.gif" width="20" height="10" title="40" alt="40"/></td><td class="ctr2" id="c19">0%</td><td class="bar" id="d41"/><td class="ctr2" id="e41">n/a</td><td class="ctr1" id="f41">1</td><td class="ctr2" id="g41">1</td><td class="ctr1" id="h16">9</td><td class="ctr2" id="i17">9</td><td class="ctr1" id="j15">1</td><td class="ctr2" id="k16">1</td></tr><tr><td id="a25"><a href="DocumentResolver.java.html#L1352" class="el_method">classificationMetadataTagsKeywords(DocumentClassificationMetadata)</a></td><td class="bar" id="b17"><img src="../jacoco-resources/redbar.gif" width="18" height="10" title="37" alt="37"/></td><td class="ctr2" id="c20">0%</td><td class="bar" id="d14"><img src="../jacoco-resources/redbar.gif" width="17" height="10" title="4" alt="4"/></td><td class="ctr2" id="e15">0%</td><td class="ctr1" id="f15">3</td><td class="ctr2" id="g15">3</td><td class="ctr1" id="h24">7</td><td class="ctr2" id="i25">7</td><td class="ctr1" id="j16">1</td><td class="ctr2" id="k17">1</td></tr><tr><td id="a33"><a href="DocumentResolver.java.html#L1244" class="el_method">documentAccessRoles(Document)</a></td><td class="bar" id="b18"><img src="../jacoco-resources/redbar.gif" width="17" height="10" title="35" alt="35"/></td><td class="ctr2" id="c21">0%</td><td class="bar" id="d15"><img src="../jacoco-resources/redbar.gif" width="17" height="10" title="4" alt="4"/></td><td class="ctr2" id="e16">0%</td><td class="ctr1" id="f16">3</td><td class="ctr2" id="g16">3</td><td class="ctr1" id="h17">9</td><td class="ctr2" id="i18">9</td><td class="ctr1" id="j17">1</td><td class="ctr2" id="k18">1</td></tr><tr><td id="a52"><a href="DocumentResolver.java.html#L104" class="el_method">getCurrentUserId()</a></td><td class="bar" id="b19"><img src="../jacoco-resources/redbar.gif" width="17" height="10" title="34" alt="34"/></td><td class="ctr2" id="c22">0%</td><td class="bar" id="d7"><img src="../jacoco-resources/redbar.gif" width="34" height="10" title="8" alt="8"/></td><td class="ctr2" id="e17">0%</td><td class="ctr1" id="f8">5</td><td class="ctr2" id="g10">5</td><td class="ctr1" id="h21">8</td><td class="ctr2" id="i22">8</td><td class="ctr1" id="j18">1</td><td class="ctr2" id="k19">1</td></tr><tr><td id="a48"><a href="DocumentResolver.java.html#L161" class="el_method">downloadDocument(String)</a></td><td class="bar" id="b20"><img src="../jacoco-resources/redbar.gif" width="15" height="10" title="31" alt="31"/></td><td class="ctr2" id="c23">0%</td><td class="bar" id="d42"/><td class="ctr2" id="e42">n/a</td><td class="ctr1" id="f42">1</td><td class="ctr2" id="g42">1</td><td class="ctr1" id="h18">9</td><td class="ctr2" id="i19">9</td><td class="ctr1" id="j19">1</td><td class="ctr2" id="k20">1</td></tr><tr><td id="a83"><a href="DocumentResolver.java.html#L1339" class="el_method">mapToDocumentVersion(Document)</a></td><td class="bar" id="b21"><img src="../jacoco-resources/redbar.gif" width="15" height="10" title="31" alt="31"/></td><td class="ctr2" id="c24">0%</td><td class="bar" id="d43"/><td class="ctr2" id="e43">n/a</td><td class="ctr1" id="f43">1</td><td class="ctr2" id="g43">1</td><td class="ctr1" id="h22">8</td><td class="ctr2" id="i23">8</td><td class="ctr1" id="j20">1</td><td class="ctr2" id="k21">1</td></tr><tr><td id="a63"><a href="DocumentResolver.java.html#L982" class="el_method">initializeChunkedUpload(ChunkedUploadInitInput)</a></td><td class="bar" id="b22"><img src="../jacoco-resources/redbar.gif" width="14" height="10" title="28" alt="28"/></td><td class="ctr2" id="c25">0%</td><td class="bar" id="d23"><img src="../jacoco-resources/redbar.gif" width="8" height="10" title="2" alt="2"/></td><td class="ctr2" id="e18">0%</td><td class="ctr1" id="f23">2</td><td class="ctr2" id="g23">2</td><td class="ctr1" id="h25">7</td><td class="ctr2" id="i26">7</td><td class="ctr1" id="j21">1</td><td class="ctr2" id="k22">1</td></tr><tr><td id="a16"><a href="DocumentResolver.java.html#L844" class="el_method">cancelUpload(String)</a></td><td class="bar" id="b23"><img src="../jacoco-resources/redbar.gif" width="13" height="10" title="26" alt="26"/></td><td class="ctr2" id="c26">0%</td><td class="bar" id="d16"><img src="../jacoco-resources/redbar.gif" width="17" height="10" title="4" alt="4"/></td><td class="ctr2" id="e19">0%</td><td class="ctr1" id="f17">3</td><td class="ctr2" id="g17">3</td><td class="ctr1" id="h23">8</td><td class="ctr2" id="i24">8</td><td class="ctr1" id="j22">1</td><td class="ctr2" id="k23">1</td></tr><tr><td id="a26"><a href="DocumentResolver.java.html#L1000" class="el_method">completeChunkedUpload(CompleteChunkedUploadInput)</a></td><td class="bar" id="b24"><img src="../jacoco-resources/redbar.gif" width="13" height="10" title="26" alt="26"/></td><td class="ctr2" id="c27">0%</td><td class="bar" id="d44"/><td class="ctr2" id="e44">n/a</td><td class="ctr1" id="f44">1</td><td class="ctr2" id="g44">1</td><td class="ctr1" id="h19">9</td><td class="ctr2" id="i20">9</td><td class="ctr1" id="j23">1</td><td class="ctr2" id="k24">1</td></tr><tr><td id="a34"><a href="DocumentResolver.java.html#L1278" class="el_method">documentClassificationMetadata(Document)</a></td><td class="bar" id="b25"><img src="../jacoco-resources/redbar.gif" width="13" height="10" title="26" alt="26"/></td><td class="ctr2" id="c28">0%</td><td class="bar" id="d24"><img src="../jacoco-resources/redbar.gif" width="8" height="10" title="2" alt="2"/></td><td class="ctr2" id="e20">0%</td><td class="ctr1" id="f24">2</td><td class="ctr2" id="g24">2</td><td class="ctr1" id="h40">4</td><td class="ctr2" id="i40">4</td><td class="ctr1" id="j24">1</td><td class="ctr2" id="k25">1</td></tr><tr><td id="a39"><a href="DocumentResolver.java.html#L1286" class="el_method">documentOwnershipMetadata(Document)</a></td><td class="bar" id="b26"><img src="../jacoco-resources/redbar.gif" width="13" height="10" title="26" alt="26"/></td><td class="ctr2" id="c29">0%</td><td class="bar" id="d25"><img src="../jacoco-resources/redbar.gif" width="8" height="10" title="2" alt="2"/></td><td class="ctr2" id="e21">0%</td><td class="ctr1" id="f25">2</td><td class="ctr2" id="g25">2</td><td class="ctr1" id="h41">4</td><td class="ctr2" id="i41">4</td><td class="ctr1" id="j25">1</td><td class="ctr2" id="k26">1</td></tr><tr><td id="a35"><a href="DocumentResolver.java.html#L1294" class="el_method">documentComplianceMetadata(Document)</a></td><td class="bar" id="b27"><img src="../jacoco-resources/redbar.gif" width="13" height="10" title="26" alt="26"/></td><td class="ctr2" id="c30">0%</td><td class="bar" id="d26"><img src="../jacoco-resources/redbar.gif" width="8" height="10" title="2" alt="2"/></td><td class="ctr2" id="e22">0%</td><td class="ctr1" id="f26">2</td><td class="ctr2" id="g26">2</td><td class="ctr1" id="h42">4</td><td class="ctr2" id="i42">4</td><td class="ctr1" id="j26">1</td><td class="ctr2" id="k27">1</td></tr><tr><td id="a17"><a href="DocumentResolver.java.html#L83" class="el_method">checkAuthentication()</a></td><td class="bar" id="b28"><img src="../jacoco-resources/redbar.gif" width="12" height="10" title="25" alt="25"/><img src="../jacoco-resources/greenbar.gif" width="9" height="10" title="19" alt="19"/></td><td class="ctr2" id="c3">43%</td><td class="bar" id="d10"><img src="../jacoco-resources/redbar.gif" width="25" height="10" title="6" alt="6"/><img src="../jacoco-resources/greenbar.gif" width="17" height="10" title="4" alt="4"/></td><td class="ctr2" id="e1">40%</td><td class="ctr1" id="f9">5</td><td class="ctr2" id="g4">6</td><td class="ctr1" id="h26">7</td><td class="ctr2" id="i14">13</td><td class="ctr1" id="j105">0</td><td class="ctr2" id="k28">1</td></tr><tr><td id="a21"><a href="DocumentResolver.java.html#L1406" class="el_method">chunkedUploadSessionStatus(ChunkedUploadSession)</a></td><td class="bar" id="b29"><img src="../jacoco-resources/redbar.gif" width="11" height="10" title="23" alt="23"/></td><td class="ctr2" id="c31">0%</td><td class="bar" id="d9"><img src="../jacoco-resources/redbar.gif" width="30" height="10" title="7" alt="7"/></td><td class="ctr2" id="e23">0%</td><td class="ctr1" id="f3">6</td><td class="ctr2" id="g5">6</td><td class="ctr1" id="h20">9</td><td class="ctr2" id="i21">9</td><td class="ctr1" id="j27">1</td><td class="ctr2" id="k29">1</td></tr><tr><td id="a72"><a href="DocumentResolver.java.html#L1259" class="el_method">lambda$18(DocumentPermission)</a></td><td class="bar" id="b30"><img src="../jacoco-resources/redbar.gif" width="11" height="10" title="23" alt="23"/></td><td class="ctr2" id="c32">0%</td><td class="bar" id="d17"><img src="../jacoco-resources/redbar.gif" width="17" height="10" title="4" alt="4"/></td><td class="ctr2" id="e24">0%</td><td class="ctr1" id="f18">3</td><td class="ctr2" id="g18">3</td><td class="ctr1" id="h27">7</td><td class="ctr2" id="i27">7</td><td class="ctr1" id="j28">1</td><td class="ctr2" id="k30">1</td></tr><tr><td id="a105"><a href="DocumentResolver.java.html#L708" class="el_method">uploadDocumentNewVersionFromPath(UploadNewVersionFromPathInput)</a></td><td class="bar" id="b31"><img src="../jacoco-resources/redbar.gif" width="10" height="10" title="21" alt="21"/></td><td class="ctr2" id="c33">0%</td><td class="bar" id="d27"><img src="../jacoco-resources/redbar.gif" width="8" height="10" title="2" alt="2"/></td><td class="ctr2" id="e25">0%</td><td class="ctr1" id="f27">2</td><td class="ctr2" id="g27">2</td><td class="ctr1" id="h28">5</td><td class="ctr2" id="i28">5</td><td class="ctr1" id="j29">1</td><td class="ctr2" id="k31">1</td></tr><tr><td id="a87"><a href="DocumentResolver.java.html#L1041" class="el_method">saveDocumentClassificationMetadata(String, DocumentClassificationMetadataInput)</a></td><td class="bar" id="b32"><img src="../jacoco-resources/redbar.gif" width="10" height="10" title="21" alt="21"/></td><td class="ctr2" id="c34">0%</td><td class="bar" id="d45"/><td class="ctr2" id="e45">n/a</td><td class="ctr1" id="f45">1</td><td class="ctr2" id="g45">1</td><td class="ctr1" id="h29">5</td><td class="ctr2" id="i29">5</td><td class="ctr1" id="j30">1</td><td class="ctr2" id="k32">1</td></tr><tr><td id="a96"><a href="DocumentResolver.java.html#L1052" class="el_method">updateClassificationMetadata(String, DocumentClassificationMetadataInput)</a></td><td class="bar" id="b33"><img src="../jacoco-resources/redbar.gif" width="10" height="10" title="21" alt="21"/></td><td class="ctr2" id="c35">0%</td><td class="bar" id="d46"/><td class="ctr2" id="e46">n/a</td><td class="ctr1" id="f46">1</td><td class="ctr2" id="g46">1</td><td class="ctr1" id="h30">5</td><td class="ctr2" id="i30">5</td><td class="ctr1" id="j31">1</td><td class="ctr2" id="k33">1</td></tr><tr><td id="a89"><a href="DocumentResolver.java.html#L1063" class="el_method">saveDocumentOwnershipMetadata(String, DocumentOwnershipMetadataInput)</a></td><td class="bar" id="b34"><img src="../jacoco-resources/redbar.gif" width="10" height="10" title="21" alt="21"/></td><td class="ctr2" id="c36">0%</td><td class="bar" id="d47"/><td class="ctr2" id="e47">n/a</td><td class="ctr1" id="f47">1</td><td class="ctr2" id="g47">1</td><td class="ctr1" id="h31">5</td><td class="ctr2" id="i31">5</td><td class="ctr1" id="j32">1</td><td class="ctr2" id="k34">1</td></tr><tr><td id="a88"><a href="DocumentResolver.java.html#L1074" class="el_method">saveDocumentComplianceMetadata(String, DocumentComplianceMetadataInput)</a></td><td class="bar" id="b35"><img src="../jacoco-resources/redbar.gif" width="10" height="10" title="21" alt="21"/></td><td class="ctr2" id="c37">0%</td><td class="bar" id="d48"/><td class="ctr2" id="e48">n/a</td><td class="ctr1" id="f48">1</td><td class="ctr2" id="g48">1</td><td class="ctr1" id="h32">5</td><td class="ctr2" id="i32">5</td><td class="ctr1" id="j33">1</td><td class="ctr2" id="k35">1</td></tr><tr><td id="a13"><a href="DocumentResolver.java.html#L1460" class="el_method">bulkUploadOverallStatus(BulkUploadResult)</a></td><td class="bar" id="b36"><img src="../jacoco-resources/redbar.gif" width="10" height="10" title="20" alt="20"/></td><td class="ctr2" id="c38">0%</td><td class="bar" id="d8"><img src="../jacoco-resources/redbar.gif" width="34" height="10" title="8" alt="8"/></td><td class="ctr2" id="e26">0%</td><td class="ctr1" id="f10">5</td><td class="ctr2" id="g11">5</td><td class="ctr1" id="h33">5</td><td class="ctr2" id="i33">5</td><td class="ctr1" id="j34">1</td><td class="ctr2" id="k36">1</td></tr><tr><td id="a85"><a href="DocumentResolver.java.html#L870" class="el_method">pauseUpload(String)</a></td><td class="bar" id="b37"><img src="../jacoco-resources/redbar.gif" width="9" height="10" title="19" alt="19"/></td><td class="ctr2" id="c39">0%</td><td class="bar" id="d28"><img src="../jacoco-resources/redbar.gif" width="8" height="10" title="2" alt="2"/></td><td class="ctr2" id="e27">0%</td><td class="ctr1" id="f28">2</td><td class="ctr2" id="g28">2</td><td class="ctr1" id="h34">5</td><td class="ctr2" id="i34">5</td><td class="ctr1" id="j35">1</td><td class="ctr2" id="k37">1</td></tr><tr><td id="a86"><a href="DocumentResolver.java.html#L888" class="el_method">resumeUpload(String)</a></td><td class="bar" id="b38"><img src="../jacoco-resources/redbar.gif" width="9" height="10" title="19" alt="19"/></td><td class="ctr2" id="c40">0%</td><td class="bar" id="d29"><img src="../jacoco-resources/redbar.gif" width="8" height="10" title="2" alt="2"/></td><td class="ctr2" id="e28">0%</td><td class="ctr1" id="f29">2</td><td class="ctr2" id="g29">2</td><td class="ctr1" id="h35">5</td><td class="ctr2" id="i35">5</td><td class="ctr1" id="j36">1</td><td class="ctr2" id="k38">1</td></tr><tr><td id="a81"><a href="DocumentResolver.java.html#L136" class="el_method">listDocumentVersions(String)</a></td><td class="bar" id="b39"><img src="../jacoco-resources/redbar.gif" width="9" height="10" title="18" alt="18"/></td><td class="ctr2" id="c41">0%</td><td class="bar" id="d49"/><td class="ctr2" id="e49">n/a</td><td class="ctr1" id="f49">1</td><td class="ctr2" id="g49">1</td><td class="ctr1" id="h36">5</td><td class="ctr2" id="i36">5</td><td class="ctr1" id="j37">1</td><td class="ctr2" id="k39">1</td></tr><tr><td id="a58"><a href="DocumentResolver.java.html#L1192" class="el_method">getDocumentsExpiringBefore(OffsetDateTime)</a></td><td class="bar" id="b40"><img src="../jacoco-resources/redbar.gif" width="9" height="10" title="18" alt="18"/></td><td class="ctr2" id="c42">0%</td><td class="bar" id="d50"/><td class="ctr2" id="e50">n/a</td><td class="ctr1" id="f50">1</td><td class="ctr2" id="g50">1</td><td class="ctr1" id="h43">4</td><td class="ctr2" id="i43">4</td><td class="ctr1" id="j38">1</td><td class="ctr2" id="k40">1</td></tr><tr><td id="a59"><a href="DocumentResolver.java.html#L1200" class="el_method">getDocumentsForRenewalReminder(OffsetDateTime)</a></td><td class="bar" id="b41"><img src="../jacoco-resources/redbar.gif" width="9" height="10" title="18" alt="18"/></td><td class="ctr2" id="c43">0%</td><td class="bar" id="d51"/><td class="ctr2" id="e51">n/a</td><td class="ctr1" id="f51">1</td><td class="ctr2" id="g51">1</td><td class="ctr1" id="h44">4</td><td class="ctr2" id="i44">4</td><td class="ctr1" id="j39">1</td><td class="ctr2" id="k41">1</td></tr><tr><td id="a1"><a href="DocumentResolver.java.html#L828" class="el_method">bulkUploadDocuments(BulkUploadInput)</a></td><td class="bar" id="b42"><img src="../jacoco-resources/redbar.gif" width="8" height="10" title="17" alt="17"/></td><td class="ctr2" id="c44">0%</td><td class="bar" id="d52"/><td class="ctr2" id="e52">n/a</td><td class="ctr1" id="f52">1</td><td class="ctr2" id="g52">1</td><td class="ctr1" id="h37">5</td><td class="ctr2" id="i37">5</td><td class="ctr1" id="j40">1</td><td class="ctr2" id="k42">1</td></tr><tr><td id="a32"><a href="DocumentResolver.java.html#L1083" class="el_method">deleteDocumentMetadata(String)</a></td><td class="bar" id="b43"><img src="../jacoco-resources/redbar.gif" width="8" height="10" title="17" alt="17"/></td><td class="ctr2" id="c45">0%</td><td class="bar" id="d53"/><td class="ctr2" id="e53">n/a</td><td class="ctr1" id="f53">1</td><td class="ctr2" id="g53">1</td><td class="ctr1" id="h38">5</td><td class="ctr2" id="i38">5</td><td class="ctr1" id="j41">1</td><td class="ctr2" id="k43">1</td></tr><tr><td id="a90"><a href="DocumentResolver.java.html#L145" class="el_method">searchDocuments(DocumentSearchInput, PaginationInput)</a></td><td class="bar" id="b44"><img src="../jacoco-resources/redbar.gif" width="8" height="10" title="16" alt="16"/></td><td class="ctr2" id="c46">0%</td><td class="bar" id="d54"/><td class="ctr2" id="e54">n/a</td><td class="ctr1" id="f54">1</td><td class="ctr2" id="g54">1</td><td class="ctr1" id="h45">4</td><td class="ctr2" id="i45">4</td><td class="ctr1" id="j42">1</td><td class="ctr2" id="k44">1</td></tr><tr><td id="a23"><a href="DocumentResolver.java.html#L248" class="el_method">chunkedUploadStatus(String)</a></td><td class="bar" id="b45"><img src="../jacoco-resources/redbar.gif" width="8" height="10" title="16" alt="16"/></td><td class="ctr2" id="c47">0%</td><td class="bar" id="d55"/><td class="ctr2" id="e55">n/a</td><td class="ctr1" id="f55">1</td><td class="ctr2" id="g55">1</td><td class="ctr1" id="h39">5</td><td class="ctr2" id="i39">5</td><td class="ctr1" id="j43">1</td><td class="ctr2" id="k45">1</td></tr><tr><td id="a94"><a href="DocumentResolver.java.html#L200" class="el_method">searchSuggestions(String, Integer)</a></td><td class="bar" id="b46"><img src="../jacoco-resources/redbar.gif" width="7" height="10" title="15" alt="15"/></td><td class="ctr2" id="c48">0%</td><td class="bar" id="d30"><img src="../jacoco-resources/redbar.gif" width="8" height="10" title="2" alt="2"/></td><td class="ctr2" id="e29">0%</td><td class="ctr1" id="f30">2</td><td class="ctr2" id="g30">2</td><td class="ctr1" id="h48">3</td><td class="ctr2" id="i48">3</td><td class="ctr1" id="j44">1</td><td class="ctr2" id="k46">1</td></tr><tr><td id="a99"><a href="DocumentResolver.java.html#L908" class="el_method">uploadDocumentEx(UploadDocumentExInput)</a></td><td class="bar" id="b47"><img src="../jacoco-resources/redbar.gif" width="7" height="10" title="15" alt="15"/></td><td class="ctr2" id="c49">0%</td><td class="bar" id="d56"/><td class="ctr2" id="e56">n/a</td><td class="ctr1" id="f56">1</td><td class="ctr2" id="g56">1</td><td class="ctr1" id="h46">4</td><td class="ctr2" id="i46">4</td><td class="ctr1" id="j45">1</td><td class="ctr2" id="k47">1</td></tr><tr><td id="a101"><a href="DocumentResolver.java.html#L921" class="el_method">uploadDocumentFromPathEx(UploadDocumentFromPathExInput)</a></td><td class="bar" id="b48"><img src="../jacoco-resources/redbar.gif" width="7" height="10" title="15" alt="15"/></td><td class="ctr2" id="c50">0%</td><td class="bar" id="d57"/><td class="ctr2" id="e57">n/a</td><td class="ctr1" id="f57">1</td><td class="ctr2" id="g57">1</td><td class="ctr1" id="h47">4</td><td class="ctr2" id="i47">4</td><td class="ctr1" id="j46">1</td><td class="ctr2" id="k48">1</td></tr><tr><td id="a55"><a href="DocumentResolver.java.html#L1017" class="el_method">getDocumentClassificationMetadata(String)</a></td><td class="bar" id="b49"><img src="../jacoco-resources/redbar.gif" width="7" height="10" title="14" alt="14"/></td><td class="ctr2" id="c51">0%</td><td class="bar" id="d58"/><td class="ctr2" id="e58">n/a</td><td class="ctr1" id="f58">1</td><td class="ctr2" id="g58">1</td><td class="ctr1" id="h49">3</td><td class="ctr2" id="i49">3</td><td class="ctr1" id="j47">1</td><td class="ctr2" id="k49">1</td></tr><tr><td id="a57"><a href="DocumentResolver.java.html#L1024" class="el_method">getDocumentOwnershipMetadata(String)</a></td><td class="bar" id="b50"><img src="../jacoco-resources/redbar.gif" width="7" height="10" title="14" alt="14"/></td><td class="ctr2" id="c52">0%</td><td class="bar" id="d59"/><td class="ctr2" id="e59">n/a</td><td class="ctr1" id="f59">1</td><td class="ctr2" id="g59">1</td><td class="ctr1" id="h50">3</td><td class="ctr2" id="i50">3</td><td class="ctr1" id="j48">1</td><td class="ctr2" id="k50">1</td></tr><tr><td id="a56"><a href="DocumentResolver.java.html#L1031" class="el_method">getDocumentComplianceMetadata(String)</a></td><td class="bar" id="b51"><img src="../jacoco-resources/redbar.gif" width="7" height="10" title="14" alt="14"/></td><td class="ctr2" id="c53">0%</td><td class="bar" id="d60"/><td class="ctr2" id="e60">n/a</td><td class="ctr1" id="f60">1</td><td class="ctr2" id="g60">1</td><td class="ctr1" id="h51">3</td><td class="ctr2" id="i51">3</td><td class="ctr1" id="j49">1</td><td class="ctr2" id="k51">1</td></tr><tr><td id="a7"><a href="DocumentResolver.java.html#L1529" class="el_method">bulkUploadItemResultMessage(BulkUploadItemResult)</a></td><td class="bar" id="b52"><img src="../jacoco-resources/redbar.gif" width="7" height="10" title="14" alt="14"/></td><td class="ctr2" id="c54">0%</td><td class="bar" id="d18"><img src="../jacoco-resources/redbar.gif" width="17" height="10" title="4" alt="4"/></td><td class="ctr2" id="e30">0%</td><td class="ctr1" id="f19">3</td><td class="ctr2" id="g19">3</td><td class="ctr1" id="h52">3</td><td class="ctr2" id="i52">3</td><td class="ctr1" id="j50">1</td><td class="ctr2" id="k52">1</td></tr><tr><td id="a0"><a href="DocumentResolver.java.html#L191" class="el_method">advancedSearch(AdvancedSearchInput, PaginationInput)</a></td><td class="bar" id="b53"><img src="../jacoco-resources/redbar.gif" width="6" height="10" title="13" alt="13"/></td><td class="ctr2" id="c55">0%</td><td class="bar" id="d61"/><td class="ctr2" id="e61">n/a</td><td class="ctr1" id="f61">1</td><td class="ctr2" id="g61">1</td><td class="ctr1" id="h53">3</td><td class="ctr2" id="i53">3</td><td class="ctr1" id="j51">1</td><td class="ctr2" id="k53">1</td></tr><tr><td id="a54"><a href="DocumentResolver.java.html#L121" class="el_method">getDocumentById(String)</a></td><td class="bar" id="b54"><img src="../jacoco-resources/redbar.gif" width="5" height="10" title="11" alt="11"/></td><td class="ctr2" id="c56">0%</td><td class="bar" id="d62"/><td class="ctr2" id="e62">n/a</td><td class="ctr1" id="f62">1</td><td class="ctr2" id="g62">1</td><td class="ctr1" id="h54">3</td><td class="ctr2" id="i54">3</td><td class="ctr1" id="j52">1</td><td class="ctr2" id="k54">1</td></tr><tr><td id="a53"><a href="DocumentResolver.java.html#L129" class="el_method">getDocument(String)</a></td><td class="bar" id="b55"><img src="../jacoco-resources/redbar.gif" width="5" height="10" title="11" alt="11"/></td><td class="ctr2" id="c57">0%</td><td class="bar" id="d63"/><td class="ctr2" id="e63">n/a</td><td class="ctr1" id="f63">1</td><td class="ctr2" id="g63">1</td><td class="ctr1" id="h55">3</td><td class="ctr2" id="i55">3</td><td class="ctr1" id="j53">1</td><td class="ctr2" id="k55">1</td></tr><tr><td id="a103"><a href="DocumentResolver.java.html#L640" class="el_method">uploadDocumentLegacy(UploadDocumentInput)</a></td><td class="bar" id="b56"><img src="../jacoco-resources/redbar.gif" width="5" height="10" title="10" alt="10"/></td><td class="ctr2" id="c58">0%</td><td class="bar" id="d64"/><td class="ctr2" id="e64">n/a</td><td class="ctr1" id="f64">1</td><td class="ctr2" id="g64">1</td><td class="ctr1" id="h58">2</td><td class="ctr2" id="i58">2</td><td class="ctr1" id="j54">1</td><td class="ctr2" id="k56">1</td></tr><tr><td id="a31"><a href="DocumentResolver.java.html#L817" class="el_method">deleteDocument(String)</a></td><td class="bar" id="b57"><img src="../jacoco-resources/redbar.gif" width="5" height="10" title="10" alt="10"/></td><td class="ctr2" id="c59">0%</td><td class="bar" id="d65"/><td class="ctr2" id="e65">n/a</td><td class="ctr1" id="f65">1</td><td class="ctr2" id="g65">1</td><td class="ctr1" id="h59">2</td><td class="ctr2" id="i59">2</td><td class="ctr1" id="j55">1</td><td class="ctr2" id="k57">1</td></tr><tr><td id="a44"><a href="DocumentResolver.java.html#L1302" class="el_method">documentTemplateId(Document)</a></td><td class="bar" id="b58"><img src="../jacoco-resources/redbar.gif" width="5" height="10" title="10" alt="10"/></td><td class="ctr2" id="c60">0%</td><td class="bar" id="d31"><img src="../jacoco-resources/redbar.gif" width="8" height="10" title="2" alt="2"/></td><td class="ctr2" id="e31">0%</td><td class="ctr1" id="f31">2</td><td class="ctr2" id="g31">2</td><td class="ctr1" id="h64">1</td><td class="ctr2" id="i64">1</td><td class="ctr1" id="j56">1</td><td class="ctr2" id="k58">1</td></tr><tr><td id="a19"><a href="DocumentResolver.java.html#L1388" class="el_method">chunkedUploadSessionExpiresAt(ChunkedUploadSession)</a></td><td class="bar" id="b59"><img src="../jacoco-resources/redbar.gif" width="5" height="10" title="10" alt="10"/></td><td class="ctr2" id="c61">0%</td><td class="bar" id="d32"><img src="../jacoco-resources/redbar.gif" width="8" height="10" title="2" alt="2"/></td><td class="ctr2" id="e32">0%</td><td class="ctr1" id="f32">2</td><td class="ctr2" id="g32">2</td><td class="ctr1" id="h60">2</td><td class="ctr2" id="i60">2</td><td class="ctr1" id="j57">1</td><td class="ctr2" id="k59">1</td></tr><tr><td id="a20"><a href="DocumentResolver.java.html#L1394" class="el_method">chunkedUploadSessionLastActivityAt(ChunkedUploadSession)</a></td><td class="bar" id="b60"><img src="../jacoco-resources/redbar.gif" width="5" height="10" title="10" alt="10"/></td><td class="ctr2" id="c62">0%</td><td class="bar" id="d33"><img src="../jacoco-resources/redbar.gif" width="8" height="10" title="2" alt="2"/></td><td class="ctr2" id="e33">0%</td><td class="ctr1" id="f33">2</td><td class="ctr2" id="g33">2</td><td class="ctr1" id="h61">2</td><td class="ctr2" id="i61">2</td><td class="ctr1" id="j58">1</td><td class="ctr2" id="k60">1</td></tr><tr><td id="a22"><a href="DocumentResolver.java.html#L1400" class="el_method">chunkedUploadSessionUploadedChunks(ChunkedUploadSession)</a></td><td class="bar" id="b61"><img src="../jacoco-resources/redbar.gif" width="5" height="10" title="10" alt="10"/></td><td class="ctr2" id="c63">0%</td><td class="bar" id="d34"><img src="../jacoco-resources/redbar.gif" width="8" height="10" title="2" alt="2"/></td><td class="ctr2" id="e34">0%</td><td class="ctr1" id="f34">2</td><td class="ctr2" id="g34">2</td><td class="ctr1" id="h65">1</td><td class="ctr2" id="i65">1</td><td class="ctr1" id="j59">1</td><td class="ctr2" id="k61">1</td></tr><tr><td id="a11"><a href="DocumentResolver.java.html#L1549" class="el_method">bulkUploadItemResultUploadedAt(BulkUploadItemResult)</a></td><td class="bar" id="b62"><img src="../jacoco-resources/redbar.gif" width="5" height="10" title="10" alt="10"/></td><td class="ctr2" id="c64">0%</td><td class="bar" id="d35"><img src="../jacoco-resources/redbar.gif" width="8" height="10" title="2" alt="2"/></td><td class="ctr2" id="e35">0%</td><td class="ctr1" id="f35">2</td><td class="ctr2" id="g35">2</td><td class="ctr1" id="h56">3</td><td class="ctr2" id="i56">3</td><td class="ctr1" id="j60">1</td><td class="ctr2" id="k62">1</td></tr><tr><td id="a45"><a href="DocumentResolver.java.html#L1307" class="el_method">documentTemplateName(Document)</a></td><td class="bar" id="b63"><img src="../jacoco-resources/redbar.gif" width="4" height="10" title="9" alt="9"/></td><td class="ctr2" id="c65">0%</td><td class="bar" id="d36"><img src="../jacoco-resources/redbar.gif" width="8" height="10" title="2" alt="2"/></td><td class="ctr2" id="e36">0%</td><td class="ctr1" id="f36">2</td><td class="ctr2" id="g36">2</td><td class="ctr1" id="h66">1</td><td class="ctr2" id="i66">1</td><td class="ctr1" id="j61">1</td><td class="ctr2" id="k63">1</td></tr><tr><td id="a8"><a href="DocumentResolver.java.html#L1519" class="el_method">bulkUploadItemResultProcessingStatus(BulkUploadItemResult)</a></td><td class="bar" id="b64"><img src="../jacoco-resources/redbar.gif" width="4" height="10" title="8" alt="8"/></td><td class="ctr2" id="c66">0%</td><td class="bar" id="d37"><img src="../jacoco-resources/redbar.gif" width="8" height="10" title="2" alt="2"/></td><td class="ctr2" id="e37">0%</td><td class="ctr1" id="f37">2</td><td class="ctr2" id="g37">2</td><td class="ctr1" id="h57">3</td><td class="ctr2" id="i57">3</td><td class="ctr1" id="j62">1</td><td class="ctr2" id="k64">1</td></tr><tr><td id="a73"><a href="DocumentResolver.java.html#L403" class="el_method">lambda$2(FileValidationError)</a></td><td class="bar" id="b65"><img src="../jacoco-resources/redbar.gif" width="4" height="10" title="8" alt="8"/></td><td class="ctr2" id="c67">0%</td><td class="bar" id="d38"><img src="../jacoco-resources/redbar.gif" width="8" height="10" title="2" alt="2"/></td><td class="ctr2" id="e38">0%</td><td class="ctr1" id="f38">2</td><td class="ctr2" id="g38">2</td><td class="ctr1" id="h67">1</td><td class="ctr2" id="i67">1</td><td class="ctr1" id="j63">1</td><td class="ctr2" id="k65">1</td></tr><tr><td id="a60"><a href="DocumentResolver.java.html#L209" class="el_method">getSearchFacets(AdvancedSearchInput)</a></td><td class="bar" id="b66"><img src="../jacoco-resources/redbar.gif" width="3" height="10" title="7" alt="7"/></td><td class="ctr2" id="c68">0%</td><td class="bar" id="d66"/><td class="ctr2" id="e66">n/a</td><td class="ctr1" id="f66">1</td><td class="ctr2" id="g66">1</td><td class="ctr1" id="h62">2</td><td class="ctr2" id="i62">2</td><td class="ctr1" id="j64">1</td><td class="ctr2" id="k66">1</td></tr><tr><td id="a106"><a href="DocumentResolver.java.html#L439" class="el_method">validateFile(MultipartFile)</a></td><td class="bar" id="b67"><img src="../jacoco-resources/redbar.gif" width="3" height="10" title="7" alt="7"/></td><td class="ctr2" id="c69">0%</td><td class="bar" id="d67"/><td class="ctr2" id="e67">n/a</td><td class="ctr1" id="f67">1</td><td class="ctr2" id="g67">1</td><td class="ctr1" id="h63">2</td><td class="ctr2" id="i63">2</td><td class="ctr1" id="j65">1</td><td class="ctr2" id="k67">1</td></tr><tr><td id="a47"><a href="DocumentResolver.java.html#L1229" class="el_method">documentVersions(Document)</a></td><td class="bar" id="b68"><img src="../jacoco-resources/redbar.gif" width="3" height="10" title="6" alt="6"/></td><td class="ctr2" id="c70">0%</td><td class="bar" id="d68"/><td class="ctr2" id="e68">n/a</td><td class="ctr1" id="f68">1</td><td class="ctr2" id="g68">1</td><td class="ctr1" id="h68">1</td><td class="ctr2" id="i68">1</td><td class="ctr1" id="j66">1</td><td class="ctr2" id="k68">1</td></tr><tr><td id="a104"><a href="DocumentResolver.java.html#L700" class="el_method">uploadDocumentNewVersion(UploadNewVersionInput)</a></td><td class="bar" id="b69"><img src="../jacoco-resources/redbar.gif" width="2" height="10" title="5" alt="5"/></td><td class="ctr2" id="c71">0%</td><td class="bar" id="d69"/><td class="ctr2" id="e69">n/a</td><td class="ctr1" id="f69">1</td><td class="ctr2" id="g69">1</td><td class="ctr1" id="h69">1</td><td class="ctr2" id="i69">1</td><td class="ctr1" id="j67">1</td><td class="ctr2" id="k69">1</td></tr><tr><td id="a24"><a href="DocumentResolver.java.html#L1367" class="el_method">classificationMetadataDocumentId(DocumentClassificationMetadata)</a></td><td class="bar" id="b70"><img src="../jacoco-resources/redbar.gif" width="2" height="10" title="5" alt="5"/></td><td class="ctr2" id="c72">0%</td><td class="bar" id="d70"/><td class="ctr2" id="e70">n/a</td><td class="ctr1" id="f70">1</td><td class="ctr2" id="g70">1</td><td class="ctr1" id="h70">1</td><td class="ctr2" id="i70">1</td><td class="ctr1" id="j68">1</td><td class="ctr2" id="k70">1</td></tr><tr><td id="a84"><a href="DocumentResolver.java.html#L1372" class="el_method">ownershipMetadataDocumentId(DocumentOwnershipMetadata)</a></td><td class="bar" id="b71"><img src="../jacoco-resources/redbar.gif" width="2" height="10" title="5" alt="5"/></td><td class="ctr2" id="c73">0%</td><td class="bar" id="d71"/><td class="ctr2" id="e71">n/a</td><td class="ctr1" id="f71">1</td><td class="ctr2" id="g71">1</td><td class="ctr1" id="h71">1</td><td class="ctr2" id="i71">1</td><td class="ctr1" id="j69">1</td><td class="ctr2" id="k71">1</td></tr><tr><td id="a27"><a href="DocumentResolver.java.html#L1377" class="el_method">complianceMetadataDocumentId(DocumentComplianceMetadata)</a></td><td class="bar" id="b72"><img src="../jacoco-resources/redbar.gif" width="2" height="10" title="5" alt="5"/></td><td class="ctr2" id="c74">0%</td><td class="bar" id="d72"/><td class="ctr2" id="e72">n/a</td><td class="ctr1" id="f72">1</td><td class="ctr2" id="g72">1</td><td class="ctr1" id="h72">1</td><td class="ctr2" id="i72">1</td><td class="ctr1" id="j70">1</td><td class="ctr2" id="k72">1</td></tr><tr><td id="a71"><a href="DocumentResolver.java.html#L1255" class="el_method">lambda$17(DocumentPermission)</a></td><td class="bar" id="b73"><img src="../jacoco-resources/redbar.gif" width="2" height="10" title="4" alt="4"/></td><td class="ctr2" id="c75">0%</td><td class="bar" id="d73"/><td class="ctr2" id="e73">n/a</td><td class="ctr1" id="f73">1</td><td class="ctr2" id="g73">1</td><td class="ctr1" id="h73">1</td><td class="ctr2" id="i73">1</td><td class="ctr1" id="j71">1</td><td class="ctr2" id="k73">1</td></tr><tr><td id="a43"><a href="DocumentResolver.java.html#L1209" class="el_method">documentTags(Document)</a></td><td class="bar" id="b74"><img src="../jacoco-resources/redbar.gif" width="1" height="10" title="3" alt="3"/></td><td class="ctr2" id="c76">0%</td><td class="bar" id="d74"/><td class="ctr2" id="e74">n/a</td><td class="ctr1" id="f74">1</td><td class="ctr2" id="g74">1</td><td class="ctr1" id="h74">1</td><td class="ctr2" id="i74">1</td><td class="ctr1" id="j72">1</td><td class="ctr2" id="k74">1</td></tr><tr><td id="a46"><a href="DocumentResolver.java.html#L1214" class="el_method">documentUserId(Document)</a></td><td class="bar" id="b75"><img src="../jacoco-resources/redbar.gif" width="1" height="10" title="3" alt="3"/></td><td class="ctr2" id="c77">0%</td><td class="bar" id="d75"/><td class="ctr2" id="e75">n/a</td><td class="ctr1" id="f75">1</td><td class="ctr2" id="g75">1</td><td class="ctr1" id="h75">1</td><td class="ctr2" id="i75">1</td><td class="ctr1" id="j73">1</td><td class="ctr2" id="k75">1</td></tr><tr><td id="a36"><a href="DocumentResolver.java.html#L1219" class="el_method">documentCreatedDate(Document)</a></td><td class="bar" id="b76"><img src="../jacoco-resources/redbar.gif" width="1" height="10" title="3" alt="3"/></td><td class="ctr2" id="c78">0%</td><td class="bar" id="d76"/><td class="ctr2" id="e76">n/a</td><td class="ctr1" id="f76">1</td><td class="ctr2" id="g76">1</td><td class="ctr1" id="h76">1</td><td class="ctr2" id="i76">1</td><td class="ctr1" id="j74">1</td><td class="ctr2" id="k76">1</td></tr><tr><td id="a38"><a href="DocumentResolver.java.html#L1224" class="el_method">documentLastModifiedDate(Document)</a></td><td class="bar" id="b77"><img src="../jacoco-resources/redbar.gif" width="1" height="10" title="3" alt="3"/></td><td class="ctr2" id="c79">0%</td><td class="bar" id="d77"/><td class="ctr2" id="e77">n/a</td><td class="ctr1" id="f77">1</td><td class="ctr2" id="g77">1</td><td class="ctr1" id="h77">1</td><td class="ctr2" id="i77">1</td><td class="ctr1" id="j75">1</td><td class="ctr2" id="k77">1</td></tr><tr><td id="a37"><a href="DocumentResolver.java.html#L1234" class="el_method">documentCurrentVersion(Document)</a></td><td class="bar" id="b78"><img src="../jacoco-resources/redbar.gif" width="1" height="10" title="3" alt="3"/></td><td class="ctr2" id="c80">0%</td><td class="bar" id="d78"/><td class="ctr2" id="e78">n/a</td><td class="ctr1" id="f78">1</td><td class="ctr2" id="g78">1</td><td class="ctr1" id="h78">1</td><td class="ctr2" id="i78">1</td><td class="ctr1" id="j76">1</td><td class="ctr2" id="k78">1</td></tr><tr><td id="a40"><a href="DocumentResolver.java.html#L1239" class="el_method">documentParentDocument(Document)</a></td><td class="bar" id="b79"><img src="../jacoco-resources/redbar.gif" width="1" height="10" title="3" alt="3"/></td><td class="ctr2" id="c81">0%</td><td class="bar" id="d79"/><td class="ctr2" id="e79">n/a</td><td class="ctr1" id="f79">1</td><td class="ctr2" id="g79">1</td><td class="ctr1" id="h79">1</td><td class="ctr2" id="i79">1</td><td class="ctr1" id="j77">1</td><td class="ctr2" id="k79">1</td></tr><tr><td id="a18"><a href="DocumentResolver.java.html#L1383" class="el_method">chunkedUploadSessionCreatedAt(ChunkedUploadSession)</a></td><td class="bar" id="b80"><img src="../jacoco-resources/redbar.gif" width="1" height="10" title="3" alt="3"/></td><td class="ctr2" id="c82">0%</td><td class="bar" id="d80"/><td class="ctr2" id="e80">n/a</td><td class="ctr1" id="f80">1</td><td class="ctr2" id="g80">1</td><td class="ctr1" id="h80">1</td><td class="ctr2" id="i80">1</td><td class="ctr1" id="j78">1</td><td class="ctr2" id="k80">1</td></tr><tr><td id="a15"><a href="DocumentResolver.java.html#L1433" class="el_method">bulkUploadSuccessfulUploads(BulkUploadResult)</a></td><td class="bar" id="b81"><img src="../jacoco-resources/redbar.gif" width="1" height="10" title="3" alt="3"/></td><td class="ctr2" id="c83">0%</td><td class="bar" id="d81"/><td class="ctr2" id="e81">n/a</td><td class="ctr1" id="f81">1</td><td class="ctr2" id="g81">1</td><td class="ctr1" id="h81">1</td><td class="ctr2" id="i81">1</td><td class="ctr1" id="j79">1</td><td class="ctr2" id="k81">1</td></tr><tr><td id="a2"><a href="DocumentResolver.java.html#L1442" class="el_method">bulkUploadFailedUploads(BulkUploadResult)</a></td><td class="bar" id="b82"><img src="../jacoco-resources/redbar.gif" width="1" height="10" title="3" alt="3"/></td><td class="ctr2" id="c84">0%</td><td class="bar" id="d82"/><td class="ctr2" id="e82">n/a</td><td class="ctr1" id="f82">1</td><td class="ctr2" id="g82">1</td><td class="ctr1" id="h82">1</td><td class="ctr2" id="i82">1</td><td class="ctr1" id="j80">1</td><td class="ctr2" id="k82">1</td></tr><tr><td id="a14"><a href="DocumentResolver.java.html#L1451" class="el_method">bulkUploadProcessingTimeMs(BulkUploadResult)</a></td><td class="bar" id="b83"><img src="../jacoco-resources/redbar.gif" width="1" height="10" title="3" alt="3"/></td><td class="ctr2" id="c85">0%</td><td class="bar" id="d83"/><td class="ctr2" id="e83">n/a</td><td class="ctr1" id="f83">1</td><td class="ctr2" id="g83">1</td><td class="ctr1" id="h83">1</td><td class="ctr2" id="i83">1</td><td class="ctr1" id="j81">1</td><td class="ctr2" id="k83">1</td></tr><tr><td id="a10"><a href="DocumentResolver.java.html#L1478" class="el_method">bulkUploadItemResultSuccess(BulkUploadItemResult)</a></td><td class="bar" id="b84"><img src="../jacoco-resources/redbar.gif" width="1" height="10" title="3" alt="3"/></td><td class="ctr2" id="c86">0%</td><td class="bar" id="d84"/><td class="ctr2" id="e84">n/a</td><td class="ctr1" id="f84">1</td><td class="ctr2" id="g84">1</td><td class="ctr1" id="h84">1</td><td class="ctr2" id="i84">1</td><td class="ctr1" id="j82">1</td><td class="ctr2" id="k84">1</td></tr><tr><td id="a12"><a href="DocumentResolver.java.html#L1487" class="el_method">bulkUploadItemResultUploadId(BulkUploadItemResult)</a></td><td class="bar" id="b85"><img src="../jacoco-resources/redbar.gif" width="1" height="10" title="3" alt="3"/></td><td class="ctr2" id="c87">0%</td><td class="bar" id="d85"/><td class="ctr2" id="e85">n/a</td><td class="ctr1" id="f85">1</td><td class="ctr2" id="g85">1</td><td class="ctr1" id="h85">1</td><td class="ctr2" id="i85">1</td><td class="ctr1" id="j83">1</td><td class="ctr2" id="k85">1</td></tr><tr><td id="a5"><a href="DocumentResolver.java.html#L1495" class="el_method">bulkUploadItemResultFileName(BulkUploadItemResult)</a></td><td class="bar" id="b86"><img src="../jacoco-resources/redbar.gif" width="1" height="10" title="3" alt="3"/></td><td class="ctr2" id="c88">0%</td><td class="bar" id="d86"/><td class="ctr2" id="e86">n/a</td><td class="ctr1" id="f86">1</td><td class="ctr2" id="g86">1</td><td class="ctr1" id="h86">1</td><td class="ctr2" id="i86">1</td><td class="ctr1" id="j84">1</td><td class="ctr2" id="k86">1</td></tr><tr><td id="a6"><a href="DocumentResolver.java.html#L1503" class="el_method">bulkUploadItemResultFileSize(BulkUploadItemResult)</a></td><td class="bar" id="b87"><img src="../jacoco-resources/redbar.gif" width="1" height="10" title="3" alt="3"/></td><td class="ctr2" id="c89">0%</td><td class="bar" id="d87"/><td class="ctr2" id="e87">n/a</td><td class="ctr1" id="f87">1</td><td class="ctr2" id="g87">1</td><td class="ctr1" id="h87">1</td><td class="ctr2" id="i87">1</td><td class="ctr1" id="j85">1</td><td class="ctr2" id="k87">1</td></tr><tr><td id="a4"><a href="DocumentResolver.java.html#L1541" class="el_method">bulkUploadItemResultErrorMessage(BulkUploadItemResult)</a></td><td class="bar" id="b88"><img src="../jacoco-resources/redbar.gif" width="1" height="10" title="3" alt="3"/></td><td class="ctr2" id="c90">0%</td><td class="bar" id="d88"/><td class="ctr2" id="e88">n/a</td><td class="ctr1" id="f88">1</td><td class="ctr2" id="g88">1</td><td class="ctr1" id="h88">1</td><td class="ctr2" id="i88">1</td><td class="ctr1" id="j86">1</td><td class="ctr2" id="k88">1</td></tr><tr><td id="a3"><a href="DocumentResolver.java.html#L1560" class="el_method">bulkUploadItemResultDocument(BulkUploadItemResult)</a></td><td class="bar" id="b89"><img src="../jacoco-resources/redbar.gif" width="1" height="10" title="3" alt="3"/></td><td class="ctr2" id="c91">0%</td><td class="bar" id="d89"/><td class="ctr2" id="e89">n/a</td><td class="ctr1" id="f89">1</td><td class="ctr2" id="g89">1</td><td class="ctr1" id="h89">1</td><td class="ctr2" id="i89">1</td><td class="ctr1" id="j87">1</td><td class="ctr2" id="k89">1</td></tr><tr><td id="a74"><a href="DocumentResolver.java.html#L1103" class="el_method">lambda$3(DocumentClassificationMetadata)</a></td><td class="bar" id="b90"><img src="../jacoco-resources/redbar.gif" width="1" height="10" title="3" alt="3"/></td><td class="ctr2" id="c92">0%</td><td class="bar" id="d90"/><td class="ctr2" id="e90">n/a</td><td class="ctr1" id="f90">1</td><td class="ctr2" id="g90">1</td><td class="ctr1" id="h90">1</td><td class="ctr2" id="i90">1</td><td class="ctr1" id="j88">1</td><td class="ctr2" id="k90">1</td></tr><tr><td id="a75"><a href="DocumentResolver.java.html#L1108" class="el_method">lambda$4(DocumentClassificationMetadata)</a></td><td class="bar" id="b91"><img src="../jacoco-resources/redbar.gif" width="1" height="10" title="3" alt="3"/></td><td class="ctr2" id="c93">0%</td><td class="bar" id="d91"/><td class="ctr2" id="e91">n/a</td><td class="ctr1" id="f91">1</td><td class="ctr2" id="g91">1</td><td class="ctr1" id="h91">1</td><td class="ctr2" id="i91">1</td><td class="ctr1" id="j89">1</td><td class="ctr2" id="k91">1</td></tr><tr><td id="a76"><a href="DocumentResolver.java.html#L1113" class="el_method">lambda$5(DocumentClassificationMetadata)</a></td><td class="bar" id="b92"><img src="../jacoco-resources/redbar.gif" width="1" height="10" title="3" alt="3"/></td><td class="ctr2" id="c94">0%</td><td class="bar" id="d92"/><td class="ctr2" id="e92">n/a</td><td class="ctr1" id="f92">1</td><td class="ctr2" id="g92">1</td><td class="ctr1" id="h92">1</td><td class="ctr2" id="i92">1</td><td class="ctr1" id="j90">1</td><td class="ctr2" id="k92">1</td></tr><tr><td id="a77"><a href="DocumentResolver.java.html#L1118" class="el_method">lambda$6(DocumentClassificationMetadata)</a></td><td class="bar" id="b93"><img src="../jacoco-resources/redbar.gif" width="1" height="10" title="3" alt="3"/></td><td class="ctr2" id="c95">0%</td><td class="bar" id="d93"/><td class="ctr2" id="e93">n/a</td><td class="ctr1" id="f93">1</td><td class="ctr2" id="g93">1</td><td class="ctr1" id="h93">1</td><td class="ctr2" id="i93">1</td><td class="ctr1" id="j91">1</td><td class="ctr2" id="k93">1</td></tr><tr><td id="a78"><a href="DocumentResolver.java.html#L1136" class="el_method">lambda$7(DocumentOwnershipMetadata)</a></td><td class="bar" id="b94"><img src="../jacoco-resources/redbar.gif" width="1" height="10" title="3" alt="3"/></td><td class="ctr2" id="c96">0%</td><td class="bar" id="d94"/><td class="ctr2" id="e94">n/a</td><td class="ctr1" id="f94">1</td><td class="ctr2" id="g94">1</td><td class="ctr1" id="h94">1</td><td class="ctr2" id="i94">1</td><td class="ctr1" id="j92">1</td><td class="ctr2" id="k94">1</td></tr><tr><td id="a79"><a href="DocumentResolver.java.html#L1141" class="el_method">lambda$8(DocumentOwnershipMetadata)</a></td><td class="bar" id="b95"><img src="../jacoco-resources/redbar.gif" width="1" height="10" title="3" alt="3"/></td><td class="ctr2" id="c97">0%</td><td class="bar" id="d95"/><td class="ctr2" id="e95">n/a</td><td class="ctr1" id="f95">1</td><td class="ctr2" id="g95">1</td><td class="ctr1" id="h95">1</td><td class="ctr2" id="i95">1</td><td class="ctr1" id="j93">1</td><td class="ctr2" id="k95">1</td></tr><tr><td id="a80"><a href="DocumentResolver.java.html#L1146" class="el_method">lambda$9(DocumentOwnershipMetadata)</a></td><td class="bar" id="b96"><img src="../jacoco-resources/redbar.gif" width="1" height="10" title="3" alt="3"/></td><td class="ctr2" id="c98">0%</td><td class="bar" id="d96"/><td class="ctr2" id="e96">n/a</td><td class="ctr1" id="f96">1</td><td class="ctr2" id="g96">1</td><td class="ctr1" id="h96">1</td><td class="ctr2" id="i96">1</td><td class="ctr1" id="j94">1</td><td class="ctr2" id="k96">1</td></tr><tr><td id="a64"><a href="DocumentResolver.java.html#L1151" class="el_method">lambda$10(DocumentOwnershipMetadata)</a></td><td class="bar" id="b97"><img src="../jacoco-resources/redbar.gif" width="1" height="10" title="3" alt="3"/></td><td class="ctr2" id="c99">0%</td><td class="bar" id="d97"/><td class="ctr2" id="e97">n/a</td><td class="ctr1" id="f97">1</td><td class="ctr2" id="g97">1</td><td class="ctr1" id="h97">1</td><td class="ctr2" id="i97">1</td><td class="ctr1" id="j95">1</td><td class="ctr2" id="k97">1</td></tr><tr><td id="a65"><a href="DocumentResolver.java.html#L1169" class="el_method">lambda$11(DocumentComplianceMetadata)</a></td><td class="bar" id="b98"><img src="../jacoco-resources/redbar.gif" width="1" height="10" title="3" alt="3"/></td><td class="ctr2" id="c100">0%</td><td class="bar" id="d98"/><td class="ctr2" id="e98">n/a</td><td class="ctr1" id="f98">1</td><td class="ctr2" id="g98">1</td><td class="ctr1" id="h98">1</td><td class="ctr2" id="i98">1</td><td class="ctr1" id="j96">1</td><td class="ctr2" id="k98">1</td></tr><tr><td id="a66"><a href="DocumentResolver.java.html#L1174" class="el_method">lambda$12(DocumentComplianceMetadata)</a></td><td class="bar" id="b99"><img src="../jacoco-resources/redbar.gif" width="1" height="10" title="3" alt="3"/></td><td class="ctr2" id="c101">0%</td><td class="bar" id="d99"/><td class="ctr2" id="e99">n/a</td><td class="ctr1" id="f99">1</td><td class="ctr2" id="g99">1</td><td class="ctr1" id="h99">1</td><td class="ctr2" id="i99">1</td><td class="ctr1" id="j97">1</td><td class="ctr2" id="k99">1</td></tr><tr><td id="a67"><a href="DocumentResolver.java.html#L1179" class="el_method">lambda$13(DocumentComplianceMetadata)</a></td><td class="bar" id="b100"><img src="../jacoco-resources/redbar.gif" width="1" height="10" title="3" alt="3"/></td><td class="ctr2" id="c102">0%</td><td class="bar" id="d100"/><td class="ctr2" id="e100">n/a</td><td class="ctr1" id="f100">1</td><td class="ctr2" id="g100">1</td><td class="ctr1" id="h100">1</td><td class="ctr2" id="i100">1</td><td class="ctr1" id="j98">1</td><td class="ctr2" id="k100">1</td></tr><tr><td id="a68"><a href="DocumentResolver.java.html#L1184" class="el_method">lambda$14(DocumentComplianceMetadata)</a></td><td class="bar" id="b101"><img src="../jacoco-resources/redbar.gif" width="1" height="10" title="3" alt="3"/></td><td class="ctr2" id="c103">0%</td><td class="bar" id="d101"/><td class="ctr2" id="e101">n/a</td><td class="ctr1" id="f101">1</td><td class="ctr2" id="g101">1</td><td class="ctr1" id="h101">1</td><td class="ctr2" id="i101">1</td><td class="ctr1" id="j99">1</td><td class="ctr2" id="k101">1</td></tr><tr><td id="a69"><a href="DocumentResolver.java.html#L1195" class="el_method">lambda$15(DocumentOwnershipMetadata)</a></td><td class="bar" id="b102"><img src="../jacoco-resources/redbar.gif" width="1" height="10" title="3" alt="3"/></td><td class="ctr2" id="c104">0%</td><td class="bar" id="d102"/><td class="ctr2" id="e102">n/a</td><td class="ctr1" id="f102">1</td><td class="ctr2" id="g102">1</td><td class="ctr1" id="h102">1</td><td class="ctr2" id="i102">1</td><td class="ctr1" id="j100">1</td><td class="ctr2" id="k102">1</td></tr><tr><td id="a70"><a href="DocumentResolver.java.html#L1203" class="el_method">lambda$16(DocumentOwnershipMetadata)</a></td><td class="bar" id="b103"><img src="../jacoco-resources/redbar.gif" width="1" height="10" title="3" alt="3"/></td><td class="ctr2" id="c105">0%</td><td class="bar" id="d103"/><td class="ctr2" id="e103">n/a</td><td class="ctr1" id="f103">1</td><td class="ctr2" id="g103">1</td><td class="ctr1" id="h103">1</td><td class="ctr2" id="i103">1</td><td class="ctr1" id="j101">1</td><td class="ctr2" id="k103">1</td></tr><tr><td id="a50"><a href="DocumentResolver.java.html#L155" class="el_method">getAuditLogsByFilter(DocumentSearchInput)</a></td><td class="bar" id="b104"><img src="../jacoco-resources/redbar.gif" width="1" height="10" title="2" alt="2"/></td><td class="ctr2" id="c106">0%</td><td class="bar" id="d104"/><td class="ctr2" id="e104">n/a</td><td class="ctr1" id="f104">1</td><td class="ctr2" id="g104">1</td><td class="ctr1" id="h104">1</td><td class="ctr2" id="i104">1</td><td class="ctr1" id="j102">1</td><td class="ctr2" id="k104">1</td></tr><tr><td id="a9"><a href="DocumentResolver.java.html#L1511" class="el_method">bulkUploadItemResultProcessingStrategy(BulkUploadItemResult)</a></td><td class="bar" id="b105"><img src="../jacoco-resources/redbar.gif" width="1" height="10" title="2" alt="2"/></td><td class="ctr2" id="c107">0%</td><td class="bar" id="d105"/><td class="ctr2" id="e105">n/a</td><td class="ctr1" id="f105">1</td><td class="ctr2" id="g105">1</td><td class="ctr1" id="h105">1</td><td class="ctr2" id="i105">1</td><td class="ctr1" id="j103">1</td><td class="ctr2" id="k105">1</td></tr><tr><td id="a95"><a href="DocumentResolver.java.html#L53" class="el_method">static {...}</a></td><td class="bar" id="b106"><img src="../jacoco-resources/greenbar.gif" width="2" height="10" title="4" alt="4"/></td><td class="ctr2" id="c0">100%</td><td class="bar" id="d106"/><td class="ctr2" id="e106">n/a</td><td class="ctr1" id="f106">0</td><td class="ctr2" id="g106">1</td><td class="ctr1" id="h106">0</td><td class="ctr2" id="i106">1</td><td class="ctr1" id="j106">0</td><td class="ctr2" id="k106">1</td></tr><tr><td id="a42"><a href="DocumentResolver.java.html#L51" class="el_method">DocumentResolver()</a></td><td class="bar" id="b107"><img src="../jacoco-resources/greenbar.gif" width="1" height="10" title="3" alt="3"/></td><td class="ctr2" id="c1">100%</td><td class="bar" id="d107"/><td class="ctr2" id="e107">n/a</td><td class="ctr1" id="f107">0</td><td class="ctr2" id="g107">1</td><td class="ctr1" id="h107">0</td><td class="ctr2" id="i107">1</td><td class="ctr1" id="j107">0</td><td class="ctr2" id="k107">1</td></tr></tbody></table><div class="footer"><span class="right">Created with <a href="http://www.jacoco.org/jacoco">JaCoCo</a> 0.8.10.202304240956</span></div></body></html>