<?xml version="1.0" encoding="UTF-8"?><!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Strict//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-strict.dtd"><html xmlns="http://www.w3.org/1999/xhtml" lang="en"><head><meta http-equiv="Content-Type" content="text/html;charset=UTF-8"/><link rel="stylesheet" href="../jacoco-resources/report.css" type="text/css"/><link rel="shortcut icon" href="../jacoco-resources/report.gif" type="image/gif"/><title>com.ascentbusiness.dms_svc.controller</title><script type="text/javascript" src="../jacoco-resources/sort.js"></script></head><body onload="initialSort(['breadcrumb', 'coveragetable'])"><div class="breadcrumb" id="breadcrumb"><span class="info"><a href="index.source.html" class="el_source">Source Files</a><a href="../jacoco-sessions.html" class="el_session">Sessions</a></span><a href="../index.html" class="el_report">dms-svc</a> &gt; <span class="el_package">com.ascentbusiness.dms_svc.controller</span></div><h1>com.ascentbusiness.dms_svc.controller</h1><table class="coverage" cellspacing="0" id="coveragetable"><thead><tr><td class="sortable" id="a" onclick="toggleSort(this)">Element</td><td class="down sortable bar" id="b" onclick="toggleSort(this)">Missed Instructions</td><td class="sortable ctr2" id="c" onclick="toggleSort(this)">Cov.</td><td class="sortable bar" id="d" onclick="toggleSort(this)">Missed Branches</td><td class="sortable ctr2" id="e" onclick="toggleSort(this)">Cov.</td><td class="sortable ctr1" id="f" onclick="toggleSort(this)">Missed</td><td class="sortable ctr2" id="g" onclick="toggleSort(this)">Cxty</td><td class="sortable ctr1" id="h" onclick="toggleSort(this)">Missed</td><td class="sortable ctr2" id="i" onclick="toggleSort(this)">Lines</td><td class="sortable ctr1" id="j" onclick="toggleSort(this)">Missed</td><td class="sortable ctr2" id="k" onclick="toggleSort(this)">Methods</td><td class="sortable ctr1" id="l" onclick="toggleSort(this)">Missed</td><td class="sortable ctr2" id="m" onclick="toggleSort(this)">Classes</td></tr></thead><tfoot><tr><td>Total</td><td class="bar">1,707 of 1,740</td><td class="ctr2">1%</td><td class="bar">40 of 40</td><td class="ctr2">0%</td><td class="ctr1">111</td><td class="ctr2">117</td><td class="ctr1">406</td><td class="ctr2">412</td><td class="ctr1">91</td><td class="ctr2">97</td><td class="ctr1">6</td><td class="ctr2">9</td></tr></tfoot><tbody><tr><td id="a4"><a href="GraphQLMetricsController.html" class="el_class">GraphQLMetricsController</a></td><td class="bar" id="b0"><img src="../jacoco-resources/redbar.gif" width="117" height="10" title="624" alt="624"/><img src="../jacoco-resources/greenbar.gif" width="2" height="10" title="13" alt="13"/></td><td class="ctr2" id="c2">2%</td><td class="bar" id="d0"><img src="../jacoco-resources/redbar.gif" width="120" height="10" title="16" alt="16"/></td><td class="ctr2" id="e0">0%</td><td class="ctr1" id="f0">35</td><td class="ctr2" id="g0">37</td><td class="ctr1" id="h0">142</td><td class="ctr2" id="i0">144</td><td class="ctr1" id="j0">27</td><td class="ctr2" id="k0">29</td><td class="ctr1" id="l6">0</td><td class="ctr2" id="m0">1</td></tr><tr><td id="a3"><a href="DocumentDownloadController.html" class="el_class">DocumentDownloadController</a></td><td class="bar" id="b1"><img src="../jacoco-resources/redbar.gif" width="112" height="10" title="596" alt="596"/><img src="../jacoco-resources/greenbar.gif" width="2" height="10" title="13" alt="13"/></td><td class="ctr2" id="c1">2%</td><td class="bar" id="d1"><img src="../jacoco-resources/redbar.gif" width="105" height="10" title="14" alt="14"/></td><td class="ctr2" id="e1">0%</td><td class="ctr1" id="f1">16</td><td class="ctr2" id="g1">18</td><td class="ctr1" id="h1">132</td><td class="ctr2" id="i1">134</td><td class="ctr1" id="j3">9</td><td class="ctr2" id="k1">11</td><td class="ctr1" id="l7">0</td><td class="ctr2" id="m1">1</td></tr><tr><td id="a0"><a href="AuthController.html" class="el_class">AuthController</a></td><td class="bar" id="b2"><img src="../jacoco-resources/redbar.gif" width="39" height="10" title="208" alt="208"/><img src="../jacoco-resources/greenbar.gif" width="1" height="10" title="7" alt="7"/></td><td class="ctr2" id="c0">3%</td><td class="bar" id="d2"><img src="../jacoco-resources/redbar.gif" width="75" height="10" title="10" alt="10"/></td><td class="ctr2" id="e2">0%</td><td class="ctr1" id="f4">9</td><td class="ctr2" id="g2">11</td><td class="ctr1" id="h2">51</td><td class="ctr2" id="i2">53</td><td class="ctr1" id="j8">4</td><td class="ctr2" id="k7">6</td><td class="ctr1" id="l8">0</td><td class="ctr2" id="m2">1</td></tr><tr><td id="a8"><a href="GraphQLMetricsController$GraphQLMetricsResponse$Builder.html" class="el_class">GraphQLMetricsController.GraphQLMetricsResponse.Builder</a></td><td class="bar" id="b3"><img src="../jacoco-resources/redbar.gif" width="11" height="10" title="63" alt="63"/></td><td class="ctr2" id="c3">0%</td><td class="bar" id="d3"/><td class="ctr2" id="e3">n/a</td><td class="ctr1" id="f2">10</td><td class="ctr2" id="g3">10</td><td class="ctr1" id="h7">10</td><td class="ctr2" id="i7">10</td><td class="ctr1" id="j1">10</td><td class="ctr2" id="k2">10</td><td class="ctr1" id="l0">1</td><td class="ctr2" id="m3">1</td></tr><tr><td id="a7"><a href="GraphQLMetricsController$GraphQLMetricsResponse.html" class="el_class">GraphQLMetricsController.GraphQLMetricsResponse</a></td><td class="bar" id="b4"><img src="../jacoco-resources/redbar.gif" width="10" height="10" title="55" alt="55"/></td><td class="ctr2" id="c4">0%</td><td class="bar" id="d4"/><td class="ctr2" id="e4">n/a</td><td class="ctr1" id="f3">10</td><td class="ctr2" id="g4">10</td><td class="ctr1" id="h3">19</td><td class="ctr2" id="i3">19</td><td class="ctr1" id="j2">10</td><td class="ctr2" id="k3">10</td><td class="ctr1" id="l1">1</td><td class="ctr2" id="m4">1</td></tr><tr><td id="a6"><a href="GraphQLMetricsController$GraphQLHealthResponse$Builder.html" class="el_class">GraphQLMetricsController.GraphQLHealthResponse.Builder</a></td><td class="bar" id="b5"><img src="../jacoco-resources/redbar.gif" width="9" height="10" title="49" alt="49"/></td><td class="ctr2" id="c5">0%</td><td class="bar" id="d5"/><td class="ctr2" id="e5">n/a</td><td class="ctr1" id="f6">8</td><td class="ctr2" id="g6">8</td><td class="ctr1" id="h8">8</td><td class="ctr2" id="i8">8</td><td class="ctr1" id="j5">8</td><td class="ctr2" id="k5">8</td><td class="ctr1" id="l2">1</td><td class="ctr2" id="m5">1</td></tr><tr><td id="a5"><a href="GraphQLMetricsController$GraphQLHealthResponse.html" class="el_class">GraphQLMetricsController.GraphQLHealthResponse</a></td><td class="bar" id="b6"><img src="../jacoco-resources/redbar.gif" width="8" height="10" title="43" alt="43"/></td><td class="ctr2" id="c6">0%</td><td class="bar" id="d6"/><td class="ctr2" id="e6">n/a</td><td class="ctr1" id="f7">8</td><td class="ctr2" id="g7">8</td><td class="ctr1" id="h5">15</td><td class="ctr2" id="i5">15</td><td class="ctr1" id="j6">8</td><td class="ctr2" id="k6">8</td><td class="ctr1" id="l3">1</td><td class="ctr2" id="m6">1</td></tr><tr><td id="a1"><a href="AuthController$JwtResponse.html" class="el_class">AuthController.JwtResponse</a></td><td class="bar" id="b7"><img src="../jacoco-resources/redbar.gif" width="8" height="10" title="43" alt="43"/></td><td class="ctr2" id="c7">0%</td><td class="bar" id="d7"/><td class="ctr2" id="e7">n/a</td><td class="ctr1" id="f5">9</td><td class="ctr2" id="g5">9</td><td class="ctr1" id="h4">18</td><td class="ctr2" id="i4">18</td><td class="ctr1" id="j4">9</td><td class="ctr2" id="k4">9</td><td class="ctr1" id="l4">1</td><td class="ctr2" id="m7">1</td></tr><tr><td id="a2"><a href="AuthController$LoginRequest.html" class="el_class">AuthController.LoginRequest</a></td><td class="bar" id="b8"><img src="../jacoco-resources/redbar.gif" width="4" height="10" title="26" alt="26"/></td><td class="ctr2" id="c8">0%</td><td class="bar" id="d8"/><td class="ctr2" id="e8">n/a</td><td class="ctr1" id="f8">6</td><td class="ctr2" id="g8">6</td><td class="ctr1" id="h6">11</td><td class="ctr2" id="i6">11</td><td class="ctr1" id="j7">6</td><td class="ctr2" id="k8">6</td><td class="ctr1" id="l5">1</td><td class="ctr2" id="m8">1</td></tr></tbody></table><div class="footer"><span class="right">Created with <a href="http://www.jacoco.org/jacoco">JaCoCo</a> 0.8.10.202304240956</span></div></body></html>