# Notification Service Properties Simplification Guide

## Overview
This document provides a comprehensive guide to simplify the Notification Service properties configuration, following the same successful approach used for the DMS Service. The goal is to minimize complexity, reduce duplication, and improve maintainability.

## Current State Analysis

### Existing Files Structure
The Notification Service currently has **4 properties files** with significant duplication and complexity:

1. **`application.properties`** (123 lines) - Base configuration with hardcoded values
2. **`application-local-dev.properties`** (178 lines) - Local development overrides
3. **`application-docker.properties`** (110 lines) - Docker environment overrides  
4. **`application-aws-ec2.properties`** (179 lines) - AWS EC2 production overrides

**Total: 590 lines** with extensive duplication across files.

### Key Issues Identified

#### 1. **Massive Duplication**
- Database configuration repeated in all files
- JPA/Hibernate settings duplicated across environments
- Security configurations repeated with minor variations
- Actuator settings copied in multiple files
- Redis/RabbitMQ configurations duplicated

#### 2. **Inconsistent Environment Variable Usage**
- Some configurations use environment variables, others are hardcoded
- Inconsistent naming patterns for environment variables
- Missing defaults for many configurations

#### 3. **Security Concerns**
- Hardcoded credentials in base file (lines 82-83 in application.properties)
- JWT secrets visible in configuration files
- Email passwords exposed in properties

#### 4. **Poor Organization**
- Mixed environment-specific and common configurations
- No clear separation of concerns
- Inconsistent commenting and documentation

#### 5. **Maintenance Complexity**
- Changes require updates in multiple files
- Risk of configuration drift between environments
- Difficult to track which settings are environment-specific

## Proposed Simplification Strategy

### 1. **Consolidated Base Configuration**
Create a streamlined `application.properties` with:
- All common configurations with environment variable support
- Sensible defaults for development
- Well-organized sections with clear documentation
- Complete externalization of sensitive values

### 2. **Environment-Specific Overrides Only**
Transform environment files to contain only overrides:
- **`application-dev.properties`** - Development-specific settings only
- **`application-uat.properties`** - UAT-specific settings only (rename from docker)
- **`application-prod.properties`** - Production-specific settings only (rename from aws-ec2)

### 3. **Standardized Environment Variables**
Implement consistent environment variable patterns:
- `DB_*` for database configurations
- `REDIS_*` for Redis configurations
- `RABBITMQ_*` for RabbitMQ configurations
- `MAIL_*` for email configurations
- `JWT_*` for JWT configurations

## Detailed Implementation Plan

### Phase 1: Base Configuration Consolidation

#### A. Database Configuration
```properties
# Database Configuration
spring.datasource.url=${DB_URL:*******************************************************************************************************************}
spring.datasource.username=${DB_USERNAME:root}
spring.datasource.password=${DB_PASSWORD:root}
spring.datasource.driver-class-name=com.mysql.cj.jdbc.Driver

# HikariCP Connection Pool Configuration
spring.datasource.hikari.maximum-pool-size=${DB_POOL_MAX_SIZE:10}
spring.datasource.hikari.minimum-idle=${DB_POOL_MIN_IDLE:2}
spring.datasource.hikari.connection-timeout=${DB_POOL_CONNECTION_TIMEOUT:30000}
spring.datasource.hikari.idle-timeout=${DB_POOL_IDLE_TIMEOUT:300000}
spring.datasource.hikari.max-lifetime=${DB_POOL_MAX_LIFETIME:900000}
```

#### B. JPA Configuration
```properties
# JPA Configuration
spring.jpa.hibernate.ddl-auto=${JPA_DDL_AUTO:update}
spring.jpa.show-sql=${JPA_SHOW_SQL:false}
spring.jpa.properties.hibernate.format_sql=${HIBERNATE_FORMAT_SQL:false}
spring.jpa.properties.hibernate.dialect=org.hibernate.dialect.MySQL8Dialect
spring.jpa.properties.hibernate.enable_lazy_load_no_trans=true

# Hibernate Performance Optimizations
spring.jpa.properties.hibernate.jdbc.batch_size=${HIBERNATE_BATCH_SIZE:25}
spring.jpa.properties.hibernate.order_inserts=true
spring.jpa.properties.hibernate.order_updates=true
spring.jpa.properties.hibernate.jdbc.batch_versioned_data=true
```

#### C. Security Configuration
```properties
# JWT Configuration - NEVER hardcode secrets
jwt.issuer=${JWT_ISSUER:notification-service}
jwt.audience=${JWT_AUDIENCE:notification-clients}
jwt.secret=${JWT_SECRET:}
jwt.expiration=${JWT_EXPIRATION:3600000}

# Security Configuration
security.jwt.enabled=${JWT_ENABLED:true}
security.cors.allowed-origins=${CORS_ALLOWED_ORIGINS:}
security.cors.allowed-methods=${CORS_ALLOWED_METHODS:GET,POST,PUT,DELETE,OPTIONS}
security.cors.allowed-headers=${CORS_ALLOWED_HEADERS:Content-Type,Authorization,X-Correlation-ID}
security.cors.allow-credentials=${CORS_ALLOW_CREDENTIALS:true}
```

#### D. External Services Configuration
```properties
# RabbitMQ Configuration
spring.rabbitmq.host=${RABBITMQ_HOST:localhost}
spring.rabbitmq.port=${RABBITMQ_PORT:5672}
spring.rabbitmq.username=${RABBITMQ_USERNAME:guest}
spring.rabbitmq.password=${RABBITMQ_PASSWORD:guest}
spring.rabbitmq.virtual-host=${RABBITMQ_VIRTUAL_HOST:/}

# Redis Configuration
spring.redis.host=${REDIS_HOST:localhost}
spring.redis.port=${REDIS_PORT:6379}
spring.redis.password=${REDIS_PASSWORD:}
spring.redis.database=${REDIS_DATABASE:0}
spring.redis.timeout=${REDIS_TIMEOUT:5000ms}
spring.redis.connect-timeout=${REDIS_CONNECT_TIMEOUT:3000ms}

# Email Configuration - NEVER hardcode credentials
spring.mail.host=${MAIL_HOST:}
spring.mail.port=${MAIL_PORT:587}
spring.mail.username=${MAIL_USERNAME:}
spring.mail.password=${MAIL_PASSWORD:}
spring.mail.properties.mail.smtp.auth=${MAIL_SMTP_AUTH:true}
spring.mail.properties.mail.smtp.starttls.enable=${MAIL_SMTP_STARTTLS:true}
```

### Phase 2: Environment-Specific Overrides

#### A. Development Environment (`application-dev.properties`)
```properties
# Development Environment Overrides
ENVIRONMENT=development

# Database Configuration - Enable SQL logging
spring.jpa.show-sql=true
spring.jpa.properties.hibernate.format_sql=true

# Logging Configuration - Debug levels
logging.level.com.ascentbusiness.notification_svc=DEBUG
logging.level.org.springframework.security=DEBUG
logging.level.org.hibernate.SQL=DEBUG

# GraphQL Configuration - Enable GraphiQL
spring.graphql.graphiql.enabled=true

# Security Configuration - Relaxed for development
security.cors.allowed-origins=http://localhost:4200,http://localhost:3000,http://localhost:8080

# Cache Configuration - Shorter TTL
spring.cache.redis.time-to-live=300
spring.cache.redis.key-prefix=notification:dev:

# Notification Configuration - Mock email
notification.email.mock=true

# Actuator Configuration - More endpoints
management.endpoints.web.exposure.include=health,info,metrics,prometheus,env,configprops
```

#### B. UAT Environment (`application-uat.properties`)
```properties
# UAT Environment Overrides
ENVIRONMENT=uat

# Database Configuration - Validate schema
spring.jpa.hibernate.ddl-auto=validate

# Logging Configuration - Info level
logging.level.com.ascentbusiness.notification_svc=INFO
logging.level.org.springframework.security=WARN

# GraphQL Configuration - Disable GraphiQL
spring.graphql.graphiql.enabled=false

# Security Configuration - Stricter CORS
security.cors.allowed-origins=${CORS_ALLOWED_ORIGINS}

# Cache Configuration - Standard TTL
spring.cache.redis.key-prefix=notification:uat:

# Notification Configuration - Real email
notification.email.mock=false

# Actuator Configuration - Limited exposure
management.endpoints.web.exposure.include=health,info,metrics,prometheus
management.endpoint.health.show-details=when-authorized
```

#### C. Production Environment (`application-prod.properties`)
```properties
# Production Environment Overrides
ENVIRONMENT=production

# Server Configuration - Production port
server.port=${SERVER_PORT:8080}

# Database Configuration - Strict validation
spring.jpa.hibernate.ddl-auto=validate
spring.jpa.show-sql=false

# Connection Pool Configuration - Production optimized
spring.datasource.hikari.maximum-pool-size=${DB_POOL_SIZE:20}
spring.datasource.hikari.minimum-idle=${DB_POOL_MIN_IDLE:5}

# Logging Configuration - Production level
logging.level.com.ascentbusiness.notification_svc=INFO
logging.level.org.springframework.security=WARN

# GraphQL Configuration - Disabled
spring.graphql.graphiql.enabled=false

# Security Configuration - Strict CORS
security.cors.allowed-origins=${CORS_ALLOWED_ORIGINS}

# Cache Configuration - Production TTL
spring.cache.redis.key-prefix=notification:prod:

# Actuator Configuration - Minimal exposure
management.endpoints.web.exposure.include=health,metrics
management.endpoint.health.show-details=never

# Performance Configuration - Production optimized
spring.jpa.properties.hibernate.jdbc.batch_size=50
server.compression.enabled=true
server.tomcat.max-connections=8192
server.tomcat.threads.max=200

# Notification Configuration - Production settings
notification.processing.batch-size=100
notification.template.cache-ttl=3600
```

### Phase 3: Security Enhancements

#### A. Remove Hardcoded Credentials
- Remove all hardcoded passwords, secrets, and API keys
- Replace with environment variables
- Add validation for required environment variables

#### B. Implement Secure Defaults
- Use empty strings for sensitive defaults
- Add clear documentation for required environment variables
- Implement startup validation for critical configurations

### Phase 4: Documentation and Validation

#### A. Create Comprehensive Documentation
- Document all environment variables
- Provide deployment guides for each environment
- Include security best practices

#### B. Validate Configuration
- Test each environment profile
- Verify all features work correctly
- Ensure no functionality is lost

## Expected Benefits

### 1. **Reduced Complexity**
- **Before**: 4 files with 590 lines and extensive duplication
- **After**: 4 files with ~400 lines (32% reduction) and minimal duplication

### 2. **Improved Security**
- All sensitive values externalized
- No hardcoded credentials
- Environment-specific security configurations

### 3. **Better Maintainability**
- Single source of truth for common configurations
- Environment files contain only overrides
- Clear separation of concerns

### 4. **Enhanced Flexibility**
- All configurations customizable via environment variables
- Easy to add new environments
- Consistent configuration patterns

## Implementation Checklist

### Pre-Implementation
- [ ] Backup existing configuration files
- [ ] Document current environment variable usage
- [ ] Identify all hardcoded sensitive values

### Implementation Steps
- [ ] Create consolidated `application.properties`
- [ ] Create streamlined `application-dev.properties`
- [ ] Create streamlined `application-uat.properties` (rename from docker)
- [ ] Create streamlined `application-prod.properties` (rename from aws-ec2)
- [ ] Remove old `application-local-dev.properties`
- [ ] Create comprehensive documentation

### Post-Implementation
- [ ] Test development environment
- [ ] Test UAT environment  
- [ ] Test production environment
- [ ] Validate all features work correctly
- [ ] Update deployment scripts and documentation

## Environment Variables Reference

### Database
- `DB_URL` - Database connection URL
- `DB_USERNAME` - Database username
- `DB_PASSWORD` - Database password
- `DB_POOL_MAX_SIZE` - Maximum connection pool size
- `DB_POOL_MIN_IDLE` - Minimum idle connections

### Security
- `JWT_SECRET` - JWT signing secret (REQUIRED)
- `JWT_EXPIRATION` - JWT token expiration time
- `CORS_ALLOWED_ORIGINS` - Allowed CORS origins

### External Services
- `REDIS_HOST`, `REDIS_PORT`, `REDIS_PASSWORD` - Redis configuration
- `RABBITMQ_HOST`, `RABBITMQ_PORT`, `RABBITMQ_USERNAME`, `RABBITMQ_PASSWORD` - RabbitMQ configuration
- `MAIL_HOST`, `MAIL_PORT`, `MAIL_USERNAME`, `MAIL_PASSWORD` - Email configuration

### Application
- `SERVER_PORT` - Server port
- `ENVIRONMENT` - Environment identifier
- `GRAPHIQL_ENABLED` - Enable/disable GraphiQL interface

## Migration Notes

### Breaking Changes
- Environment profile names will change:
  - `local-dev` → `dev`
  - `docker` → `uat`
  - `aws-ec2` → `prod`

### Backward Compatibility
- All existing environment variables will continue to work
- New environment variables added with sensible defaults
- No functional changes to application behavior

## Success Criteria

1. **Reduced file size by at least 30%**
2. **Eliminated all hardcoded sensitive values**
3. **All environments tested and functional**
4. **Comprehensive documentation created**
5. **Zero functionality regression**

## Timeline

- **Phase 1**: Base configuration consolidation (2-3 hours)
- **Phase 2**: Environment-specific overrides (2-3 hours)
- **Phase 3**: Security enhancements (1-2 hours)
- **Phase 4**: Documentation and validation (2-3 hours)

**Total Estimated Time**: 7-11 hours

## Risk Mitigation

1. **Backup Strategy**: Keep original files as `.backup` until validation complete
2. **Incremental Testing**: Test each environment after changes
3. **Rollback Plan**: Quick rollback to original configuration if issues arise
4. **Documentation**: Comprehensive change log for troubleshooting

This guide provides a complete roadmap for simplifying the Notification Service properties configuration while maintaining all functionality and improving security, maintainability, and clarity.