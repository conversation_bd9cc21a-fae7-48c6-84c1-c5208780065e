# Test script to check correlation ID logging
Write-Host "Testing Correlation ID with value: 12345678888888"

# Make a simple API call with correlation ID header
$headers = @{
    "X-Correlation-ID" = "12345678888888"
    "Content-Type" = "application/json"
}

$body = @{
    query = "query { __typename }"
} | ConvertTo-Json

try {
    $response = Invoke-RestMethod -Uri "http://localhost:9093/graphql" -Method POST -Headers $headers -Body $body
    Write-Host "Response received: $($response | ConvertTo-Json)"
} catch {
    Write-Host "Error: $($_.Exception.Message)"
    Write-Host "Response: $($_.Exception.Response)"
}

Write-Host "Check the application logs for correlation ID: 12345678888888"
