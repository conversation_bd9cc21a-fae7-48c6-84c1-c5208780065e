-- Add performance indexes for notification service
-- This migration adds critical indexes to optimize common query patterns

-- ============================================================================
-- NOTIFICATIONS TABLE INDEXES
-- ============================================================================

-- Index for sender-based queries (finding notifications by sender)
CREATE INDEX IF NOT EXISTS idx_notifications_sender_id 
ON notifications(sender_id);

-- Index for notification type filtering (EMAIL vs IN_APP)
CREATE INDEX IF NOT EXISTS idx_notifications_type 
ON notifications(type);

-- Index for created date ordering and filtering
CREATE INDEX IF NOT EXISTS idx_notifications_created_date 
ON notifications(created_date DESC);

-- Index for message type filtering
CREATE INDEX IF NOT EXISTS idx_notifications_message_type 
ON notifications(message_type);

-- Index for importance level filtering
CREATE INDEX IF NOT EXISTS idx_notifications_importance 
ON notifications(importance);

-- Index for template name lookups
CREATE INDEX IF NOT EXISTS idx_notifications_template_name 
ON notifications(template_name(255));

-- Composite index for sender + type + created_date (common query pattern)
CREATE INDEX IF NOT EXISTS idx_notifications_sender_type_created 
ON notifications(sender_id, type, created_date DESC);

-- Composite index for type + message_type + importance (filtering combinations)
CREATE INDEX IF NOT EXISTS idx_notifications_type_message_importance 
ON notifications(type, message_type, importance);

-- ============================================================================
-- NOTIFICATION_RECIPIENTS TABLE INDEXES
-- ============================================================================

-- Primary index for recipient-based queries (most common pattern)
CREATE INDEX IF NOT EXISTS idx_notification_recipients_recipient_id 
ON notification_recipients(recipient_id);

-- Index for read status filtering
CREATE INDEX IF NOT EXISTS idx_notification_recipients_is_read 
ON notification_recipients(is_read);

-- Index for delivery status filtering
CREATE INDEX IF NOT EXISTS idx_notification_recipients_delivered 
ON notification_recipients(delivered);

-- Index for recipient type filtering (TO, CC, BCC)
CREATE INDEX IF NOT EXISTS idx_notification_recipients_type 
ON notification_recipients(recipient_type);

-- Index for created date ordering
CREATE INDEX IF NOT EXISTS idx_notification_recipients_created_date 
ON notification_recipients(created_date DESC);

-- Foreign key index for notification lookups
CREATE INDEX IF NOT EXISTS idx_notification_recipients_notification_id 
ON notification_recipients(notification_id);

-- ============================================================================
-- COMPOSITE INDEXES FOR COMMON QUERY PATTERNS
-- ============================================================================

-- Most common query: recipient + read status + created date ordering
-- Used by: findByRecipientIdOrderByNotificationCreatedDateDesc
CREATE INDEX IF NOT EXISTS idx_recipients_recipient_created 
ON notification_recipients(recipient_id, created_date DESC);

-- Unread notifications query: recipient + read status
-- Used by: findByRecipientIdAndIsReadFalse
CREATE INDEX IF NOT EXISTS idx_recipients_recipient_unread 
ON notification_recipients(recipient_id, is_read);

-- Notification type specific queries with recipient and read status
-- Used by: findByRecipientIdAndIsReadFalseAndNotificationTypeOrderByNotificationCreatedDateDesc
CREATE INDEX IF NOT EXISTS idx_recipients_complex_query 
ON notification_recipients(recipient_id, is_read, created_date DESC);

-- Notification lookup with recipient (for marking as read)
-- Used by: findByNotificationIdAndRecipientId
CREATE INDEX IF NOT EXISTS idx_recipients_notification_recipient 
ON notification_recipients(notification_id, recipient_id);

-- Delivery status with recipient for status updates
CREATE INDEX IF NOT EXISTS idx_recipients_recipient_delivered 
ON notification_recipients(recipient_id, delivered);

-- Read status with delivery status for comprehensive filtering
CREATE INDEX IF NOT EXISTS idx_recipients_read_delivered 
ON notification_recipients(is_read, delivered);

-- ============================================================================
-- NOTIFICATION_TEMPLATES TABLE INDEXES
-- ============================================================================

-- Index for template alias lookups (already has UNIQUE constraint, but explicit index for performance)
CREATE INDEX IF NOT EXISTS idx_notification_templates_alias 
ON notification_templates(alias);

-- Index for template created date
CREATE INDEX IF NOT EXISTS idx_notification_templates_created_date 
ON notification_templates(created_date DESC);

-- ============================================================================
-- COVERING INDEXES FOR HIGH-PERFORMANCE QUERIES
-- ============================================================================

-- Covering index for recipient notification list queries
-- Includes all columns needed for the most common recipient queries
CREATE INDEX IF NOT EXISTS idx_recipients_covering_list 
ON notification_recipients(recipient_id, is_read, created_date DESC, notification_id, delivered, recipient_type);

-- Covering index for notification details with recipient info
-- Useful for joined queries between notifications and recipients
CREATE INDEX IF NOT EXISTS idx_notifications_covering_details 
ON notifications(id, sender_id, type, created_date DESC, content, template_name, message_type, importance);

-- ============================================================================
-- PERFORMANCE COMMENTS AND DOCUMENTATION
-- ============================================================================

-- Index usage patterns:
-- 1. Single column indexes support basic filtering and sorting
-- 2. Composite indexes support complex WHERE clauses and ORDER BY combinations
-- 3. Covering indexes include all needed columns to avoid table lookups
-- 4. DESC ordering on created_date matches common query patterns (newest first)

-- Query optimization targets:
-- - User notification retrieval (by recipient_id)
-- - Unread notification filtering (is_read = false)
-- - Notification type filtering (EMAIL vs IN_APP)
-- - Date-based sorting and filtering
-- - Sender-based queries
-- - Template lookups
-- - Delivery status tracking

-- Maintenance notes:
-- - Monitor index usage with EXPLAIN ANALYZE
-- - Consider partitioning for very large datasets
-- - Regular ANALYZE TABLE to update statistics
-- - Monitor index size vs performance benefits