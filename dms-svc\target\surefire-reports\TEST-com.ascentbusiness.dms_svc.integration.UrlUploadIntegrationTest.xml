<?xml version="1.0" encoding="UTF-8"?>
<testsuite xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:noNamespaceSchemaLocation="https://maven.apache.org/surefire/maven-surefire-plugin/xsd/surefire-test-report-3.0.xsd" version="3.0" name="com.ascentbusiness.dms_svc.integration.UrlUploadIntegrationTest" time="74.351" tests="13" errors="0" skipped="0" failures="0">
  <properties>
    <property name="java.specification.version" value="21"/>
    <property name="sun.cpu.isalist" value="amd64"/>
    <property name="sun.jnu.encoding" value="Cp1252"/>
    <property name="java.class.path" value="D:\grc-platform-v4\dms-svc\target\test-classes;D:\grc-platform-v4\dms-svc\target\classes;C:\Users\<USER>\.m2\repository\org\springframework\boot\spring-boot-starter-data-jpa\3.5.0\spring-boot-starter-data-jpa-3.5.0.jar;C:\Users\<USER>\.m2\repository\org\springframework\boot\spring-boot-starter\3.5.0\spring-boot-starter-3.5.0.jar;C:\Users\<USER>\.m2\repository\org\springframework\boot\spring-boot\3.5.0\spring-boot-3.5.0.jar;C:\Users\<USER>\.m2\repository\org\springframework\boot\spring-boot-autoconfigure\3.5.0\spring-boot-autoconfigure-3.5.0.jar;C:\Users\<USER>\.m2\repository\org\springframework\boot\spring-boot-starter-logging\3.5.0\spring-boot-starter-logging-3.5.0.jar;C:\Users\<USER>\.m2\repository\ch\qos\logback\logback-classic\1.5.18\logback-classic-1.5.18.jar;C:\Users\<USER>\.m2\repository\ch\qos\logback\logback-core\1.5.18\logback-core-1.5.18.jar;C:\Users\<USER>\.m2\repository\org\apache\logging\log4j\log4j-to-slf4j\2.24.3\log4j-to-slf4j-2.24.3.jar;C:\Users\<USER>\.m2\repository\org\slf4j\jul-to-slf4j\2.0.17\jul-to-slf4j-2.0.17.jar;C:\Users\<USER>\.m2\repository\org\springframework\boot\spring-boot-starter-jdbc\3.5.0\spring-boot-starter-jdbc-3.5.0.jar;C:\Users\<USER>\.m2\repository\com\zaxxer\HikariCP\6.3.0\HikariCP-6.3.0.jar;C:\Users\<USER>\.m2\repository\org\springframework\spring-jdbc\6.2.7\spring-jdbc-6.2.7.jar;C:\Users\<USER>\.m2\repository\org\hibernate\orm\hibernate-core\6.6.15.Final\hibernate-core-6.6.15.Final.jar;C:\Users\<USER>\.m2\repository\jakarta\persistence\jakarta.persistence-api\3.1.0\jakarta.persistence-api-3.1.0.jar;C:\Users\<USER>\.m2\repository\jakarta\transaction\jakarta.transaction-api\2.0.1\jakarta.transaction-api-2.0.1.jar;C:\Users\<USER>\.m2\repository\org\jboss\logging\jboss-logging\3.6.1.Final\jboss-logging-3.6.1.Final.jar;C:\Users\<USER>\.m2\repository\org\hibernate\common\hibernate-commons-annotations\7.0.3.Final\hibernate-commons-annotations-7.0.3.Final.jar;C:\Users\<USER>\.m2\repository\io\smallrye\jandex\3.2.0\jandex-3.2.0.jar;C:\Users\<USER>\.m2\repository\com\fasterxml\classmate\1.7.0\classmate-1.7.0.jar;C:\Users\<USER>\.m2\repository\net\bytebuddy\byte-buddy\1.17.5\byte-buddy-1.17.5.jar;C:\Users\<USER>\.m2\repository\org\glassfish\jaxb\jaxb-runtime\4.0.5\jaxb-runtime-4.0.5.jar;C:\Users\<USER>\.m2\repository\org\glassfish\jaxb\jaxb-core\4.0.5\jaxb-core-4.0.5.jar;C:\Users\<USER>\.m2\repository\org\eclipse\angus\angus-activation\2.0.2\angus-activation-2.0.2.jar;C:\Users\<USER>\.m2\repository\org\glassfish\jaxb\txw2\4.0.5\txw2-4.0.5.jar;C:\Users\<USER>\.m2\repository\com\sun\istack\istack-commons-runtime\4.1.2\istack-commons-runtime-4.1.2.jar;C:\Users\<USER>\.m2\repository\jakarta\inject\jakarta.inject-api\2.0.1\jakarta.inject-api-2.0.1.jar;C:\Users\<USER>\.m2\repository\org\antlr\antlr4-runtime\4.13.0\antlr4-runtime-4.13.0.jar;C:\Users\<USER>\.m2\repository\org\springframework\data\spring-data-jpa\3.5.0\spring-data-jpa-3.5.0.jar;C:\Users\<USER>\.m2\repository\org\springframework\data\spring-data-commons\3.5.0\spring-data-commons-3.5.0.jar;C:\Users\<USER>\.m2\repository\org\springframework\spring-orm\6.2.7\spring-orm-6.2.7.jar;C:\Users\<USER>\.m2\repository\org\springframework\spring-tx\6.2.7\spring-tx-6.2.7.jar;C:\Users\<USER>\.m2\repository\org\springframework\spring-beans\6.2.7\spring-beans-6.2.7.jar;C:\Users\<USER>\.m2\repository\org\springframework\spring-aspects\6.2.7\spring-aspects-6.2.7.jar;C:\Users\<USER>\.m2\repository\org\springframework\boot\spring-boot-starter-graphql\3.5.0\spring-boot-starter-graphql-3.5.0.jar;C:\Users\<USER>\.m2\repository\org\springframework\boot\spring-boot-starter-json\3.5.0\spring-boot-starter-json-3.5.0.jar;C:\Users\<USER>\.m2\repository\com\fasterxml\jackson\datatype\jackson-datatype-jdk8\2.19.0\jackson-datatype-jdk8-2.19.0.jar;C:\Users\<USER>\.m2\repository\com\fasterxml\jackson\module\jackson-module-parameter-names\2.19.0\jackson-module-parameter-names-2.19.0.jar;C:\Users\<USER>\.m2\repository\org\springframework\graphql\spring-graphql\1.4.0\spring-graphql-1.4.0.jar;C:\Users\<USER>\.m2\repository\org\springframework\boot\spring-boot-starter-web\3.5.0\spring-boot-starter-web-3.5.0.jar;C:\Users\<USER>\.m2\repository\org\springframework\boot\spring-boot-starter-tomcat\3.5.0\spring-boot-starter-tomcat-3.5.0.jar;C:\Users\<USER>\.m2\repository\org\apache\tomcat\embed\tomcat-embed-core\10.1.41\tomcat-embed-core-10.1.41.jar;C:\Users\<USER>\.m2\repository\org\apache\tomcat\embed\tomcat-embed-websocket\10.1.41\tomcat-embed-websocket-10.1.41.jar;C:\Users\<USER>\.m2\repository\org\springframework\spring-web\6.2.7\spring-web-6.2.7.jar;C:\Users\<USER>\.m2\repository\org\springframework\spring-webmvc\6.2.7\spring-webmvc-6.2.7.jar;C:\Users\<USER>\.m2\repository\org\springframework\spring-expression\6.2.7\spring-expression-6.2.7.jar;C:\Users\<USER>\.m2\repository\org\springframework\boot\spring-boot-starter-security\3.5.0\spring-boot-starter-security-3.5.0.jar;C:\Users\<USER>\.m2\repository\org\springframework\spring-aop\6.2.7\spring-aop-6.2.7.jar;C:\Users\<USER>\.m2\repository\org\springframework\security\spring-security-config\6.5.0\spring-security-config-6.5.0.jar;C:\Users\<USER>\.m2\repository\org\springframework\security\spring-security-web\6.5.0\spring-security-web-6.5.0.jar;C:\Users\<USER>\.m2\repository\org\springframework\boot\spring-boot-starter-data-redis\3.5.0\spring-boot-starter-data-redis-3.5.0.jar;C:\Users\<USER>\.m2\repository\io\lettuce\lettuce-core\6.5.5.RELEASE\lettuce-core-6.5.5.RELEASE.jar;C:\Users\<USER>\.m2\repository\io\netty\netty-common\4.1.121.Final\netty-common-4.1.121.Final.jar;C:\Users\<USER>\.m2\repository\io\netty\netty-handler\4.1.121.Final\netty-handler-4.1.121.Final.jar;C:\Users\<USER>\.m2\repository\io\netty\netty-transport\4.1.121.Final\netty-transport-4.1.121.Final.jar;C:\Users\<USER>\.m2\repository\org\springframework\data\spring-data-redis\3.5.0\spring-data-redis-3.5.0.jar;C:\Users\<USER>\.m2\repository\org\springframework\data\spring-data-keyvalue\3.5.0\spring-data-keyvalue-3.5.0.jar;C:\Users\<USER>\.m2\repository\org\springframework\spring-oxm\6.2.7\spring-oxm-6.2.7.jar;C:\Users\<USER>\.m2\repository\org\springframework\spring-context-support\6.2.7\spring-context-support-6.2.7.jar;C:\Users\<USER>\.m2\repository\org\springframework\boot\spring-boot-starter-actuator\3.5.0\spring-boot-starter-actuator-3.5.0.jar;C:\Users\<USER>\.m2\repository\org\springframework\boot\spring-boot-actuator-autoconfigure\3.5.0\spring-boot-actuator-autoconfigure-3.5.0.jar;C:\Users\<USER>\.m2\repository\org\springframework\boot\spring-boot-actuator\3.5.0\spring-boot-actuator-3.5.0.jar;C:\Users\<USER>\.m2\repository\io\micrometer\micrometer-observation\1.15.0\micrometer-observation-1.15.0.jar;C:\Users\<USER>\.m2\repository\io\micrometer\micrometer-commons\1.15.0\micrometer-commons-1.15.0.jar;C:\Users\<USER>\.m2\repository\io\micrometer\micrometer-jakarta9\1.15.0\micrometer-jakarta9-1.15.0.jar;C:\Users\<USER>\.m2\repository\org\springframework\boot\spring-boot-starter-validation\3.5.0\spring-boot-starter-validation-3.5.0.jar;C:\Users\<USER>\.m2\repository\org\apache\tomcat\embed\tomcat-embed-el\10.1.41\tomcat-embed-el-10.1.41.jar;C:\Users\<USER>\.m2\repository\org\hibernate\validator\hibernate-validator\8.0.2.Final\hibernate-validator-8.0.2.Final.jar;C:\Users\<USER>\.m2\repository\jakarta\validation\jakarta.validation-api\3.0.2\jakarta.validation-api-3.0.2.jar;C:\Users\<USER>\.m2\repository\com\mysql\mysql-connector-j\9.2.0\mysql-connector-j-9.2.0.jar;C:\Users\<USER>\.m2\repository\io\jsonwebtoken\jjwt-api\0.12.3\jjwt-api-0.12.3.jar;C:\Users\<USER>\.m2\repository\io\jsonwebtoken\jjwt-impl\0.12.3\jjwt-impl-0.12.3.jar;C:\Users\<USER>\.m2\repository\io\jsonwebtoken\jjwt-jackson\0.12.3\jjwt-jackson-0.12.3.jar;C:\Users\<USER>\.m2\repository\org\liquibase\liquibase-core\4.31.1\liquibase-core-4.31.1.jar;C:\Users\<USER>\.m2\repository\com\opencsv\opencsv\5.9\opencsv-5.9.jar;C:\Users\<USER>\.m2\repository\javax\xml\bind\jaxb-api\2.3.1\jaxb-api-2.3.1.jar;C:\Users\<USER>\.m2\repository\org\apache\commons\commons-collections4\4.4\commons-collections4-4.4.jar;C:\Users\<USER>\.m2\repository\org\apache\commons\commons-text\1.13.0\commons-text-1.13.0.jar;C:\Users\<USER>\.m2\repository\org\apache\commons\commons-lang3\3.17.0\commons-lang3-3.17.0.jar;C:\Users\<USER>\.m2\repository\commons-io\commons-io\2.15.1\commons-io-2.15.1.jar;C:\Users\<USER>\.m2\repository\software\amazon\awssdk\s3\2.20.162\s3-2.20.162.jar;C:\Users\<USER>\.m2\repository\software\amazon\awssdk\aws-xml-protocol\2.20.162\aws-xml-protocol-2.20.162.jar;C:\Users\<USER>\.m2\repository\software\amazon\awssdk\aws-query-protocol\2.20.162\aws-query-protocol-2.20.162.jar;C:\Users\<USER>\.m2\repository\software\amazon\awssdk\protocol-core\2.20.162\protocol-core-2.20.162.jar;C:\Users\<USER>\.m2\repository\software\amazon\awssdk\arns\2.20.162\arns-2.20.162.jar;C:\Users\<USER>\.m2\repository\software\amazon\awssdk\profiles\2.20.162\profiles-2.20.162.jar;C:\Users\<USER>\.m2\repository\software\amazon\awssdk\crt-core\2.20.162\crt-core-2.20.162.jar;C:\Users\<USER>\.m2\repository\software\amazon\awssdk\sdk-core\2.20.162\sdk-core-2.20.162.jar;C:\Users\<USER>\.m2\repository\org\reactivestreams\reactive-streams\1.0.4\reactive-streams-1.0.4.jar;C:\Users\<USER>\.m2\repository\software\amazon\awssdk\http-client-spi\2.20.162\http-client-spi-2.20.162.jar;C:\Users\<USER>\.m2\repository\software\amazon\awssdk\annotations\2.20.162\annotations-2.20.162.jar;C:\Users\<USER>\.m2\repository\software\amazon\awssdk\utils\2.20.162\utils-2.20.162.jar;C:\Users\<USER>\.m2\repository\software\amazon\awssdk\aws-core\2.20.162\aws-core-2.20.162.jar;C:\Users\<USER>\.m2\repository\software\amazon\awssdk\metrics-spi\2.20.162\metrics-spi-2.20.162.jar;C:\Users\<USER>\.m2\repository\software\amazon\awssdk\json-utils\2.20.162\json-utils-2.20.162.jar;C:\Users\<USER>\.m2\repository\software\amazon\awssdk\third-party-jackson-core\2.20.162\third-party-jackson-core-2.20.162.jar;C:\Users\<USER>\.m2\repository\software\amazon\awssdk\endpoints-spi\2.20.162\endpoints-spi-2.20.162.jar;C:\Users\<USER>\.m2\repository\software\amazon\awssdk\apache-client\2.20.162\apache-client-2.20.162.jar;C:\Users\<USER>\.m2\repository\org\apache\httpcomponents\httpclient\4.5.13\httpclient-4.5.13.jar;C:\Users\<USER>\.m2\repository\org\apache\httpcomponents\httpcore\4.4.16\httpcore-4.4.16.jar;C:\Users\<USER>\.m2\repository\software\amazon\awssdk\netty-nio-client\2.20.162\netty-nio-client-2.20.162.jar;C:\Users\<USER>\.m2\repository\io\netty\netty-codec-http\4.1.121.Final\netty-codec-http-4.1.121.Final.jar;C:\Users\<USER>\.m2\repository\io\netty\netty-codec-http2\4.1.121.Final\netty-codec-http2-4.1.121.Final.jar;C:\Users\<USER>\.m2\repository\io\netty\netty-codec\4.1.121.Final\netty-codec-4.1.121.Final.jar;C:\Users\<USER>\.m2\repository\io\netty\netty-buffer\4.1.121.Final\netty-buffer-4.1.121.Final.jar;C:\Users\<USER>\.m2\repository\io\netty\netty-transport-classes-epoll\4.1.121.Final\netty-transport-classes-epoll-4.1.121.Final.jar;C:\Users\<USER>\.m2\repository\io\netty\netty-resolver\4.1.121.Final\netty-resolver-4.1.121.Final.jar;C:\Users\<USER>\.m2\repository\software\amazon\awssdk\auth\2.20.162\auth-2.20.162.jar;C:\Users\<USER>\.m2\repository\software\amazon\eventstream\eventstream\1.0.1\eventstream-1.0.1.jar;C:\Users\<USER>\.m2\repository\software\amazon\awssdk\regions\2.20.162\regions-2.20.162.jar;C:\Users\<USER>\.m2\repository\org\slf4j\slf4j-api\2.0.17\slf4j-api-2.0.17.jar;C:\Users\<USER>\.m2\repository\com\graphql-java\graphql-java-extended-scalars\22.0\graphql-java-extended-scalars-22.0.jar;C:\Users\<USER>\.m2\repository\com\graphql-java\graphql-java\24.0\graphql-java-24.0.jar;C:\Users\<USER>\.m2\repository\com\graphql-java\java-dataloader\5.0.0\java-dataloader-5.0.0.jar;C:\Users\<USER>\.m2\repository\org\jspecify\jspecify\1.0.0\jspecify-1.0.0.jar;C:\Users\<USER>\.m2\repository\com\microsoft\graph\microsoft-graph\6.15.0\microsoft-graph-6.15.0.jar;C:\Users\<USER>\.m2\repository\com\microsoft\graph\microsoft-graph-core\3.2.0\microsoft-graph-core-3.2.0.jar;C:\Users\<USER>\.m2\repository\com\microsoft\kiota\microsoft-kiota-abstractions\1.3.0\microsoft-kiota-abstractions-1.3.0.jar;C:\Users\<USER>\.m2\repository\io\github\std-uritemplate\std-uritemplate\1.0.5\std-uritemplate-1.0.5.jar;C:\Users\<USER>\.m2\repository\com\google\code\gson\gson\2.13.1\gson-2.13.1.jar;C:\Users\<USER>\.m2\repository\com\squareup\okhttp3\okhttp\4.12.0\okhttp-4.12.0.jar;C:\Users\<USER>\.m2\repository\com\squareup\okio\okio\3.6.0\okio-3.6.0.jar;C:\Users\<USER>\.m2\repository\com\squareup\okio\okio-jvm\3.6.0\okio-jvm-3.6.0.jar;C:\Users\<USER>\.m2\repository\org\jetbrains\kotlin\kotlin-stdlib-common\1.9.25\kotlin-stdlib-common-1.9.25.jar;C:\Users\<USER>\.m2\repository\org\jetbrains\kotlin\kotlin-stdlib-jdk8\1.9.25\kotlin-stdlib-jdk8-1.9.25.jar;C:\Users\<USER>\.m2\repository\org\jetbrains\kotlin\kotlin-stdlib\1.9.25\kotlin-stdlib-1.9.25.jar;C:\Users\<USER>\.m2\repository\org\jetbrains\kotlin\kotlin-stdlib-jdk7\1.9.25\kotlin-stdlib-jdk7-1.9.25.jar;C:\Users\<USER>\.m2\repository\jakarta\annotation\jakarta.annotation-api\2.1.1\jakarta.annotation-api-2.1.1.jar;C:\Users\<USER>\.m2\repository\com\microsoft\kiota\microsoft-kiota-authentication-azure\1.3.0\microsoft-kiota-authentication-azure-1.3.0.jar;C:\Users\<USER>\.m2\repository\io\opentelemetry\opentelemetry-context\1.49.0\opentelemetry-context-1.49.0.jar;C:\Users\<USER>\.m2\repository\com\microsoft\kiota\microsoft-kiota-http-okHttp\1.3.0\microsoft-kiota-http-okHttp-1.3.0.jar;C:\Users\<USER>\.m2\repository\com\microsoft\kiota\microsoft-kiota-serialization-json\1.3.0\microsoft-kiota-serialization-json-1.3.0.jar;C:\Users\<USER>\.m2\repository\com\microsoft\kiota\microsoft-kiota-serialization-text\1.3.0\microsoft-kiota-serialization-text-1.3.0.jar;C:\Users\<USER>\.m2\repository\com\microsoft\kiota\microsoft-kiota-serialization-form\1.3.0\microsoft-kiota-serialization-form-1.3.0.jar;C:\Users\<USER>\.m2\repository\com\microsoft\kiota\microsoft-kiota-serialization-multipart\1.3.0\microsoft-kiota-serialization-multipart-1.3.0.jar;C:\Users\<USER>\.m2\repository\com\azure\azure-identity\1.11.4\azure-identity-1.11.4.jar;C:\Users\<USER>\.m2\repository\com\azure\azure-core-http-netty\1.14.1\azure-core-http-netty-1.14.1.jar;C:\Users\<USER>\.m2\repository\io\netty\netty-handler-proxy\4.1.121.Final\netty-handler-proxy-4.1.121.Final.jar;C:\Users\<USER>\.m2\repository\io\netty\netty-codec-socks\4.1.121.Final\netty-codec-socks-4.1.121.Final.jar;C:\Users\<USER>\.m2\repository\io\netty\netty-transport-native-unix-common\4.1.121.Final\netty-transport-native-unix-common-4.1.121.Final.jar;C:\Users\<USER>\.m2\repository\io\netty\netty-transport-native-epoll\4.1.121.Final\netty-transport-native-epoll-4.1.121.Final-linux-x86_64.jar;C:\Users\<USER>\.m2\repository\io\netty\netty-transport-native-kqueue\4.1.121.Final\netty-transport-native-kqueue-4.1.121.Final-osx-x86_64.jar;C:\Users\<USER>\.m2\repository\io\netty\netty-transport-classes-kqueue\4.1.121.Final\netty-transport-classes-kqueue-4.1.121.Final.jar;C:\Users\<USER>\.m2\repository\io\netty\netty-tcnative-boringssl-static\2.0.70.Final\netty-tcnative-boringssl-static-2.0.70.Final.jar;C:\Users\<USER>\.m2\repository\io\netty\netty-tcnative-classes\2.0.70.Final\netty-tcnative-classes-2.0.70.Final.jar;C:\Users\<USER>\.m2\repository\io\netty\netty-tcnative-boringssl-static\2.0.70.Final\netty-tcnative-boringssl-static-2.0.70.Final-linux-x86_64.jar;C:\Users\<USER>\.m2\repository\io\netty\netty-tcnative-boringssl-static\2.0.70.Final\netty-tcnative-boringssl-static-2.0.70.Final-linux-aarch_64.jar;C:\Users\<USER>\.m2\repository\io\netty\netty-tcnative-boringssl-static\2.0.70.Final\netty-tcnative-boringssl-static-2.0.70.Final-osx-x86_64.jar;C:\Users\<USER>\.m2\repository\io\netty\netty-tcnative-boringssl-static\2.0.70.Final\netty-tcnative-boringssl-static-2.0.70.Final-osx-aarch_64.jar;C:\Users\<USER>\.m2\repository\io\netty\netty-tcnative-boringssl-static\2.0.70.Final\netty-tcnative-boringssl-static-2.0.70.Final-windows-x86_64.jar;C:\Users\<USER>\.m2\repository\io\projectreactor\netty\reactor-netty-http\1.2.6\reactor-netty-http-1.2.6.jar;C:\Users\<USER>\.m2\repository\io\netty\netty-resolver-dns\4.1.121.Final\netty-resolver-dns-4.1.121.Final.jar;C:\Users\<USER>\.m2\repository\io\netty\netty-codec-dns\4.1.121.Final\netty-codec-dns-4.1.121.Final.jar;C:\Users\<USER>\.m2\repository\io\netty\netty-resolver-dns-native-macos\4.1.121.Final\netty-resolver-dns-native-macos-4.1.121.Final-osx-x86_64.jar;C:\Users\<USER>\.m2\repository\io\netty\netty-resolver-dns-classes-macos\4.1.121.Final\netty-resolver-dns-classes-macos-4.1.121.Final.jar;C:\Users\<USER>\.m2\repository\io\projectreactor\netty\reactor-netty-core\1.2.6\reactor-netty-core-1.2.6.jar;C:\Users\<USER>\.m2\repository\com\microsoft\azure\msal4j\1.14.3\msal4j-1.14.3.jar;C:\Users\<USER>\.m2\repository\com\nimbusds\oauth2-oidc-sdk\11.9.1\oauth2-oidc-sdk-11.9.1.jar;C:\Users\<USER>\.m2\repository\com\github\stephenc\jcip\jcip-annotations\1.0-1\jcip-annotations-1.0-1.jar;C:\Users\<USER>\.m2\repository\com\nimbusds\content-type\2.3\content-type-2.3.jar;C:\Users\<USER>\.m2\repository\com\nimbusds\lang-tag\1.7\lang-tag-1.7.jar;C:\Users\<USER>\.m2\repository\com\microsoft\azure\msal4j-persistence-extension\1.2.0\msal4j-persistence-extension-1.2.0.jar;C:\Users\<USER>\.m2\repository\net\java\dev\jna\jna\5.13.0\jna-5.13.0.jar;C:\Users\<USER>\.m2\repository\net\java\dev\jna\jna-platform\5.6.0\jna-platform-5.6.0.jar;C:\Users\<USER>\.m2\repository\com\azure\azure-core\1.46.0\azure-core-1.46.0.jar;C:\Users\<USER>\.m2\repository\com\azure\azure-json\1.1.0\azure-json-1.1.0.jar;C:\Users\<USER>\.m2\repository\io\projectreactor\reactor-core\3.7.6\reactor-core-3.7.6.jar;C:\Users\<USER>\.m2\repository\org\springframework\boot\spring-boot-starter-oauth2-client\3.5.0\spring-boot-starter-oauth2-client-3.5.0.jar;C:\Users\<USER>\.m2\repository\org\springframework\security\spring-security-core\6.5.0\spring-security-core-6.5.0.jar;C:\Users\<USER>\.m2\repository\org\springframework\security\spring-security-crypto\6.5.0\spring-security-crypto-6.5.0.jar;C:\Users\<USER>\.m2\repository\org\springframework\security\spring-security-oauth2-client\6.5.0\spring-security-oauth2-client-6.5.0.jar;C:\Users\<USER>\.m2\repository\org\springframework\security\spring-security-oauth2-core\6.5.0\spring-security-oauth2-core-6.5.0.jar;C:\Users\<USER>\.m2\repository\org\springframework\security\spring-security-oauth2-jose\6.5.0\spring-security-oauth2-jose-6.5.0.jar;C:\Users\<USER>\.m2\repository\com\nimbusds\nimbus-jose-jwt\9.37.3\nimbus-jose-jwt-9.37.3.jar;C:\Users\<USER>\.m2\repository\io\micrometer\micrometer-registry-prometheus\1.15.0\micrometer-registry-prometheus-1.15.0.jar;C:\Users\<USER>\.m2\repository\io\micrometer\micrometer-core\1.15.0\micrometer-core-1.15.0.jar;C:\Users\<USER>\.m2\repository\org\hdrhistogram\HdrHistogram\2.2.2\HdrHistogram-2.2.2.jar;C:\Users\<USER>\.m2\repository\org\latencyutils\LatencyUtils\2.0.3\LatencyUtils-2.0.3.jar;C:\Users\<USER>\.m2\repository\io\prometheus\prometheus-metrics-core\1.3.6\prometheus-metrics-core-1.3.6.jar;C:\Users\<USER>\.m2\repository\io\prometheus\prometheus-metrics-model\1.3.6\prometheus-metrics-model-1.3.6.jar;C:\Users\<USER>\.m2\repository\io\prometheus\prometheus-metrics-config\1.3.6\prometheus-metrics-config-1.3.6.jar;C:\Users\<USER>\.m2\repository\io\prometheus\prometheus-metrics-tracer-common\1.3.6\prometheus-metrics-tracer-common-1.3.6.jar;C:\Users\<USER>\.m2\repository\io\prometheus\prometheus-metrics-exposition-formats\1.3.6\prometheus-metrics-exposition-formats-1.3.6.jar;C:\Users\<USER>\.m2\repository\io\prometheus\prometheus-metrics-exposition-textformats\1.3.6\prometheus-metrics-exposition-textformats-1.3.6.jar;C:\Users\<USER>\.m2\repository\net\logstash\logback\logstash-logback-encoder\7.4\logstash-logback-encoder-7.4.jar;C:\Users\<USER>\.m2\repository\io\micrometer\micrometer-tracing-bridge-brave\1.5.0\micrometer-tracing-bridge-brave-1.5.0.jar;C:\Users\<USER>\.m2\repository\io\zipkin\brave\brave\6.1.0\brave-6.1.0.jar;C:\Users\<USER>\.m2\repository\io\zipkin\brave\brave-context-slf4j\6.1.0\brave-context-slf4j-6.1.0.jar;C:\Users\<USER>\.m2\repository\io\zipkin\brave\brave-instrumentation-http\6.1.0\brave-instrumentation-http-6.1.0.jar;C:\Users\<USER>\.m2\repository\io\zipkin\aws\brave-propagation-aws\1.3.0\brave-propagation-aws-1.3.0.jar;C:\Users\<USER>\.m2\repository\io\zipkin\contrib\brave-propagation-w3c\brave-propagation-tracecontext\0.2.0\brave-propagation-tracecontext-0.2.0.jar;C:\Users\<USER>\.m2\repository\io\zipkin\reporter2\zipkin-reporter-brave\3.5.0\zipkin-reporter-brave-3.5.0.jar;C:\Users\<USER>\.m2\repository\io\zipkin\reporter2\zipkin-reporter\3.5.0\zipkin-reporter-3.5.0.jar;C:\Users\<USER>\.m2\repository\io\micrometer\micrometer-tracing\1.5.0\micrometer-tracing-1.5.0.jar;C:\Users\<USER>\.m2\repository\io\micrometer\context-propagation\1.1.3\context-propagation-1.1.3.jar;C:\Users\<USER>\.m2\repository\aopalliance\aopalliance\1.0\aopalliance-1.0.jar;C:\Users\<USER>\.m2\repository\io\opentelemetry\instrumentation\opentelemetry-spring-boot-starter\2.10.0\opentelemetry-spring-boot-starter-2.10.0.jar;C:\Users\<USER>\.m2\repository\io\opentelemetry\instrumentation\opentelemetry-spring-boot-autoconfigure\2.10.0\opentelemetry-spring-boot-autoconfigure-2.10.0.jar;C:\Users\<USER>\.m2\repository\io\opentelemetry\instrumentation\opentelemetry-instrumentation-api\2.10.0\opentelemetry-instrumentation-api-2.10.0.jar;C:\Users\<USER>\.m2\repository\io\opentelemetry\opentelemetry-api-incubator\1.44.1-alpha\opentelemetry-api-incubator-1.44.1-alpha.jar;C:\Users\<USER>\.m2\repository\io\opentelemetry\instrumentation\opentelemetry-instrumentation-api-incubator\2.10.0-alpha\opentelemetry-instrumentation-api-incubator-2.10.0-alpha.jar;C:\Users\<USER>\.m2\repository\javax\validation\validation-api\2.0.1.Final\validation-api-2.0.1.Final.jar;C:\Users\<USER>\.m2\repository\io\opentelemetry\instrumentation\opentelemetry-instrumentation-annotations-support\2.10.0-alpha\opentelemetry-instrumentation-annotations-support-2.10.0-alpha.jar;C:\Users\<USER>\.m2\repository\io\opentelemetry\semconv\opentelemetry-semconv-incubating\1.28.0-alpha\opentelemetry-semconv-incubating-1.28.0-alpha.jar;C:\Users\<USER>\.m2\repository\io\opentelemetry\instrumentation\opentelemetry-kafka-clients-2.6\2.10.0-alpha\opentelemetry-kafka-clients-2.6-2.10.0-alpha.jar;C:\Users\<USER>\.m2\repository\io\opentelemetry\instrumentation\opentelemetry-kafka-clients-common\2.10.0-alpha\opentelemetry-kafka-clients-common-2.10.0-alpha.jar;C:\Users\<USER>\.m2\repository\io\opentelemetry\instrumentation\opentelemetry-mongo-3.1\2.10.0-alpha\opentelemetry-mongo-3.1-2.10.0-alpha.jar;C:\Users\<USER>\.m2\repository\io\opentelemetry\instrumentation\opentelemetry-spring-kafka-2.7\2.10.0-alpha\opentelemetry-spring-kafka-2.7-2.10.0-alpha.jar;C:\Users\<USER>\.m2\repository\io\opentelemetry\instrumentation\opentelemetry-spring-web-3.1\2.10.0-alpha\opentelemetry-spring-web-3.1-2.10.0-alpha.jar;C:\Users\<USER>\.m2\repository\io\opentelemetry\instrumentation\opentelemetry-spring-webmvc-5.3\2.10.0-alpha\opentelemetry-spring-webmvc-5.3-2.10.0-alpha.jar;C:\Users\<USER>\.m2\repository\io\opentelemetry\instrumentation\opentelemetry-spring-webflux-5.3\2.10.0-alpha\opentelemetry-spring-webflux-5.3-2.10.0-alpha.jar;C:\Users\<USER>\.m2\repository\io\opentelemetry\instrumentation\opentelemetry-reactor-3.1\2.10.0-alpha\opentelemetry-reactor-3.1-2.10.0-alpha.jar;C:\Users\<USER>\.m2\repository\io\opentelemetry\instrumentation\opentelemetry-micrometer-1.5\2.10.0-alpha\opentelemetry-micrometer-1.5-2.10.0-alpha.jar;C:\Users\<USER>\.m2\repository\io\opentelemetry\instrumentation\opentelemetry-log4j-appender-2.17\2.10.0-alpha\opentelemetry-log4j-appender-2.17-2.10.0-alpha.jar;C:\Users\<USER>\.m2\repository\io\opentelemetry\instrumentation\opentelemetry-log4j-context-data-2.17-autoconfigure\2.10.0-alpha\opentelemetry-log4j-context-data-2.17-autoconfigure-2.10.0-alpha.jar;C:\Users\<USER>\.m2\repository\io\opentelemetry\instrumentation\opentelemetry-logback-appender-1.0\2.10.0-alpha\opentelemetry-logback-appender-1.0-2.10.0-alpha.jar;C:\Users\<USER>\.m2\repository\io\opentelemetry\instrumentation\opentelemetry-logback-mdc-1.0\2.10.0-alpha\opentelemetry-logback-mdc-1.0-2.10.0-alpha.jar;C:\Users\<USER>\.m2\repository\io\opentelemetry\instrumentation\opentelemetry-jdbc\2.10.0-alpha\opentelemetry-jdbc-2.10.0-alpha.jar;C:\Users\<USER>\.m2\repository\io\opentelemetry\opentelemetry-sdk-extension-autoconfigure\1.49.0\opentelemetry-sdk-extension-autoconfigure-1.49.0.jar;C:\Users\<USER>\.m2\repository\io\opentelemetry\instrumentation\opentelemetry-sdk-autoconfigure-support\2.10.0-alpha\opentelemetry-sdk-autoconfigure-support-2.10.0-alpha.jar;C:\Users\<USER>\.m2\repository\io\opentelemetry\instrumentation\opentelemetry-spring-webmvc-6.0\2.10.0-alpha\opentelemetry-spring-webmvc-6.0-2.10.0-alpha.jar;C:\Users\<USER>\.m2\repository\io\opentelemetry\instrumentation\opentelemetry-instrumentation-annotations\2.10.0\opentelemetry-instrumentation-annotations-2.10.0.jar;C:\Users\<USER>\.m2\repository\io\opentelemetry\opentelemetry-sdk-extension-autoconfigure-spi\1.49.0\opentelemetry-sdk-extension-autoconfigure-spi-1.49.0.jar;C:\Users\<USER>\.m2\repository\io\opentelemetry\opentelemetry-api\1.49.0\opentelemetry-api-1.49.0.jar;C:\Users\<USER>\.m2\repository\io\opentelemetry\opentelemetry-exporter-logging\1.49.0\opentelemetry-exporter-logging-1.49.0.jar;C:\Users\<USER>\.m2\repository\io\opentelemetry\opentelemetry-exporter-otlp\1.49.0\opentelemetry-exporter-otlp-1.49.0.jar;C:\Users\<USER>\.m2\repository\io\opentelemetry\opentelemetry-sdk-trace\1.49.0\opentelemetry-sdk-trace-1.49.0.jar;C:\Users\<USER>\.m2\repository\io\opentelemetry\opentelemetry-sdk-metrics\1.49.0\opentelemetry-sdk-metrics-1.49.0.jar;C:\Users\<USER>\.m2\repository\io\opentelemetry\opentelemetry-sdk-logs\1.49.0\opentelemetry-sdk-logs-1.49.0.jar;C:\Users\<USER>\.m2\repository\io\opentelemetry\opentelemetry-exporter-otlp-common\1.49.0\opentelemetry-exporter-otlp-common-1.49.0.jar;C:\Users\<USER>\.m2\repository\io\opentelemetry\opentelemetry-exporter-sender-okhttp\1.49.0\opentelemetry-exporter-sender-okhttp-1.49.0.jar;C:\Users\<USER>\.m2\repository\io\opentelemetry\opentelemetry-sdk\1.49.0\opentelemetry-sdk-1.49.0.jar;C:\Users\<USER>\.m2\repository\io\opentelemetry\opentelemetry-sdk-common\1.49.0\opentelemetry-sdk-common-1.49.0.jar;C:\Users\<USER>\.m2\repository\io\opentelemetry\instrumentation\opentelemetry-resources\2.10.0-alpha\opentelemetry-resources-2.10.0-alpha.jar;C:\Users\<USER>\.m2\repository\io\opentelemetry\semconv\opentelemetry-semconv\1.28.0-alpha\opentelemetry-semconv-1.28.0-alpha.jar;C:\Users\<USER>\.m2\repository\io\opentelemetry\opentelemetry-sdk-extension-incubator\1.44.1-alpha\opentelemetry-sdk-extension-incubator-1.44.1-alpha.jar;C:\Users\<USER>\.m2\repository\org\snakeyaml\snakeyaml-engine\2.8\snakeyaml-engine-2.8.jar;C:\Users\<USER>\.m2\repository\com\fasterxml\jackson\dataformat\jackson-dataformat-yaml\2.19.0\jackson-dataformat-yaml-2.19.0.jar;C:\Users\<USER>\.m2\repository\io\opentelemetry\contrib\opentelemetry-aws-resources\1.40.0-alpha\opentelemetry-aws-resources-1.40.0-alpha.jar;C:\Users\<USER>\.m2\repository\io\opentelemetry\contrib\opentelemetry-gcp-resources\1.40.0-alpha\opentelemetry-gcp-resources-1.40.0-alpha.jar;C:\Users\<USER>\.m2\repository\com\google\cloud\opentelemetry\detector-resources-support\0.33.0\detector-resources-support-0.33.0.jar;C:\Users\<USER>\.m2\repository\io\opentelemetry\contrib\opentelemetry-baggage-processor\1.40.0-alpha\opentelemetry-baggage-processor-1.40.0-alpha.jar;C:\Users\<USER>\.m2\repository\io\opentelemetry\opentelemetry-exporter-zipkin\1.44.1\opentelemetry-exporter-zipkin-1.44.1.jar;C:\Users\<USER>\.m2\repository\io\opentelemetry\opentelemetry-exporter-common\1.49.0\opentelemetry-exporter-common-1.49.0.jar;C:\Users\<USER>\.m2\repository\io\zipkin\reporter2\zipkin-sender-okhttp3\3.5.0\zipkin-sender-okhttp3-3.5.0.jar;C:\Users\<USER>\.m2\repository\io\zipkin\zipkin2\zipkin\2.27.1\zipkin-2.27.1.jar;C:\Users\<USER>\.m2\repository\org\springframework\boot\spring-boot-starter-aop\3.5.0\spring-boot-starter-aop-3.5.0.jar;C:\Users\<USER>\.m2\repository\org\aspectj\aspectjweaver\1.9.24\aspectjweaver-1.9.24.jar;C:\Users\<USER>\.m2\repository\com\fasterxml\jackson\core\jackson-core\2.19.0\jackson-core-2.19.0.jar;C:\Users\<USER>\.m2\repository\com\fasterxml\jackson\core\jackson-databind\2.19.0\jackson-databind-2.19.0.jar;C:\Users\<USER>\.m2\repository\com\fasterxml\jackson\core\jackson-annotations\2.19.0\jackson-annotations-2.19.0.jar;C:\Users\<USER>\.m2\repository\com\fasterxml\jackson\datatype\jackson-datatype-jsr310\2.19.0\jackson-datatype-jsr310-2.19.0.jar;C:\Users\<USER>\.m2\repository\org\projectlombok\lombok\1.18.38\lombok-1.18.38.jar;C:\Users\<USER>\.m2\repository\org\springframework\boot\spring-boot-starter-test\3.5.0\spring-boot-starter-test-3.5.0.jar;C:\Users\<USER>\.m2\repository\org\springframework\boot\spring-boot-test\3.5.0\spring-boot-test-3.5.0.jar;C:\Users\<USER>\.m2\repository\org\springframework\boot\spring-boot-test-autoconfigure\3.5.0\spring-boot-test-autoconfigure-3.5.0.jar;C:\Users\<USER>\.m2\repository\com\jayway\jsonpath\json-path\2.9.0\json-path-2.9.0.jar;C:\Users\<USER>\.m2\repository\jakarta\xml\bind\jakarta.xml.bind-api\4.0.2\jakarta.xml.bind-api-4.0.2.jar;C:\Users\<USER>\.m2\repository\jakarta\activation\jakarta.activation-api\2.1.3\jakarta.activation-api-2.1.3.jar;C:\Users\<USER>\.m2\repository\net\minidev\json-smart\2.5.2\json-smart-2.5.2.jar;C:\Users\<USER>\.m2\repository\net\minidev\accessors-smart\2.5.2\accessors-smart-2.5.2.jar;C:\Users\<USER>\.m2\repository\org\assertj\assertj-core\3.27.3\assertj-core-3.27.3.jar;C:\Users\<USER>\.m2\repository\org\hamcrest\hamcrest\3.0\hamcrest-3.0.jar;C:\Users\<USER>\.m2\repository\org\junit\jupiter\junit-jupiter\5.12.2\junit-jupiter-5.12.2.jar;C:\Users\<USER>\.m2\repository\org\junit\jupiter\junit-jupiter-api\5.12.2\junit-jupiter-api-5.12.2.jar;C:\Users\<USER>\.m2\repository\org\junit\jupiter\junit-jupiter-params\5.12.2\junit-jupiter-params-5.12.2.jar;C:\Users\<USER>\.m2\repository\org\junit\jupiter\junit-jupiter-engine\5.12.2\junit-jupiter-engine-5.12.2.jar;C:\Users\<USER>\.m2\repository\org\mockito\mockito-core\5.17.0\mockito-core-5.17.0.jar;C:\Users\<USER>\.m2\repository\net\bytebuddy\byte-buddy-agent\1.17.5\byte-buddy-agent-1.17.5.jar;C:\Users\<USER>\.m2\repository\org\objenesis\objenesis\3.3\objenesis-3.3.jar;C:\Users\<USER>\.m2\repository\org\mockito\mockito-junit-jupiter\5.17.0\mockito-junit-jupiter-5.17.0.jar;C:\Users\<USER>\.m2\repository\org\skyscreamer\jsonassert\1.5.3\jsonassert-1.5.3.jar;C:\Users\<USER>\.m2\repository\com\vaadin\external\google\android-json\0.0.20131108.vaadin1\android-json-0.0.20131108.vaadin1.jar;C:\Users\<USER>\.m2\repository\org\springframework\spring-core\6.2.7\spring-core-6.2.7.jar;C:\Users\<USER>\.m2\repository\org\springframework\spring-jcl\6.2.7\spring-jcl-6.2.7.jar;C:\Users\<USER>\.m2\repository\org\springframework\spring-test\6.2.7\spring-test-6.2.7.jar;C:\Users\<USER>\.m2\repository\org\xmlunit\xmlunit-core\2.10.1\xmlunit-core-2.10.1.jar;C:\Users\<USER>\.m2\repository\org\springframework\graphql\spring-graphql-test\1.4.0\spring-graphql-test-1.4.0.jar;C:\Users\<USER>\.m2\repository\org\springframework\spring-context\6.2.7\spring-context-6.2.7.jar;C:\Users\<USER>\.m2\repository\org\testcontainers\junit-jupiter\1.21.0\junit-jupiter-1.21.0.jar;C:\Users\<USER>\.m2\repository\org\testcontainers\testcontainers\1.21.0\testcontainers-1.21.0.jar;C:\Users\<USER>\.m2\repository\junit\junit\4.13.2\junit-4.13.2.jar;C:\Users\<USER>\.m2\repository\org\rnorth\duct-tape\duct-tape\1.0.8\duct-tape-1.0.8.jar;C:\Users\<USER>\.m2\repository\org\jetbrains\annotations\17.0.0\annotations-17.0.0.jar;C:\Users\<USER>\.m2\repository\com\github\docker-java\docker-java-api\3.4.2\docker-java-api-3.4.2.jar;C:\Users\<USER>\.m2\repository\com\github\docker-java\docker-java-transport-zerodep\3.4.2\docker-java-transport-zerodep-3.4.2.jar;C:\Users\<USER>\.m2\repository\com\github\docker-java\docker-java-transport\3.4.2\docker-java-transport-3.4.2.jar;C:\Users\<USER>\.m2\repository\org\testcontainers\mysql\1.21.0\mysql-1.21.0.jar;C:\Users\<USER>\.m2\repository\org\testcontainers\jdbc\1.21.0\jdbc-1.21.0.jar;C:\Users\<USER>\.m2\repository\org\testcontainers\database-commons\1.21.0\database-commons-1.21.0.jar;C:\Users\<USER>\.m2\repository\com\h2database\h2\2.3.232\h2-2.3.232.jar;C:\Users\<USER>\.m2\repository\org\awaitility\awaitility\4.3.0\awaitility-4.3.0.jar;C:\Users\<USER>\.m2\repository\com\github\tomakehurst\wiremock-jre8\2.35.0\wiremock-jre8-2.35.0.jar;C:\Users\<USER>\.m2\repository\org\eclipse\jetty\jetty-server\12.0.21\jetty-server-12.0.21.jar;C:\Users\<USER>\.m2\repository\org\eclipse\jetty\jetty-http\12.0.21\jetty-http-12.0.21.jar;C:\Users\<USER>\.m2\repository\org\eclipse\jetty\jetty-io\12.0.21\jetty-io-12.0.21.jar;C:\Users\<USER>\.m2\repository\org\eclipse\jetty\jetty-servlet\9.4.49.v20220914\jetty-servlet-9.4.49.v20220914.jar;C:\Users\<USER>\.m2\repository\org\eclipse\jetty\jetty-security\12.0.21\jetty-security-12.0.21.jar;C:\Users\<USER>\.m2\repository\org\eclipse\jetty\jetty-util-ajax\12.0.21\jetty-util-ajax-12.0.21.jar;C:\Users\<USER>\.m2\repository\org\eclipse\jetty\jetty-servlets\9.4.49.v20220914\jetty-servlets-9.4.49.v20220914.jar;C:\Users\<USER>\.m2\repository\org\eclipse\jetty\jetty-continuation\9.4.49.v20220914\jetty-continuation-9.4.49.v20220914.jar;C:\Users\<USER>\.m2\repository\org\eclipse\jetty\jetty-util\12.0.21\jetty-util-12.0.21.jar;C:\Users\<USER>\.m2\repository\org\eclipse\jetty\jetty-webapp\9.4.49.v20220914\jetty-webapp-9.4.49.v20220914.jar;C:\Users\<USER>\.m2\repository\org\eclipse\jetty\jetty-xml\12.0.21\jetty-xml-12.0.21.jar;C:\Users\<USER>\.m2\repository\org\eclipse\jetty\jetty-proxy\12.0.21\jetty-proxy-12.0.21.jar;C:\Users\<USER>\.m2\repository\org\eclipse\jetty\jetty-client\12.0.21\jetty-client-12.0.21.jar;C:\Users\<USER>\.m2\repository\org\eclipse\jetty\http2\http2-server\9.4.49.v20220914\http2-server-9.4.49.v20220914.jar;C:\Users\<USER>\.m2\repository\org\eclipse\jetty\http2\http2-common\9.4.49.v20220914\http2-common-9.4.49.v20220914.jar;C:\Users\<USER>\.m2\repository\org\eclipse\jetty\http2\http2-hpack\9.4.49.v20220914\http2-hpack-9.4.49.v20220914.jar;C:\Users\<USER>\.m2\repository\org\eclipse\jetty\jetty-alpn-server\12.0.21\jetty-alpn-server-12.0.21.jar;C:\Users\<USER>\.m2\repository\org\eclipse\jetty\jetty-alpn-java-server\12.0.21\jetty-alpn-java-server-12.0.21.jar;C:\Users\<USER>\.m2\repository\org\eclipse\jetty\jetty-alpn-openjdk8-server\9.4.49.v20220914\jetty-alpn-openjdk8-server-9.4.49.v20220914.jar;C:\Users\<USER>\.m2\repository\org\eclipse\jetty\jetty-alpn-java-client\12.0.21\jetty-alpn-java-client-12.0.21.jar;C:\Users\<USER>\.m2\repository\org\eclipse\jetty\jetty-alpn-client\12.0.21\jetty-alpn-client-12.0.21.jar;C:\Users\<USER>\.m2\repository\org\eclipse\jetty\jetty-alpn-openjdk8-client\9.4.49.v20220914\jetty-alpn-openjdk8-client-9.4.49.v20220914.jar;C:\Users\<USER>\.m2\repository\com\google\guava\guava\31.1-jre\guava-31.1-jre.jar;C:\Users\<USER>\.m2\repository\com\google\guava\failureaccess\1.0.1\failureaccess-1.0.1.jar;C:\Users\<USER>\.m2\repository\com\google\guava\listenablefuture\9999.0-empty-to-avoid-conflict-with-guava\listenablefuture-9999.0-empty-to-avoid-conflict-with-guava.jar;C:\Users\<USER>\.m2\repository\com\google\code\findbugs\jsr305\3.0.2\jsr305-3.0.2.jar;C:\Users\<USER>\.m2\repository\org\checkerframework\checker-qual\3.12.0\checker-qual-3.12.0.jar;C:\Users\<USER>\.m2\repository\com\google\errorprone\error_prone_annotations\2.11.0\error_prone_annotations-2.11.0.jar;C:\Users\<USER>\.m2\repository\com\google\j2objc\j2objc-annotations\1.3\j2objc-annotations-1.3.jar;C:\Users\<USER>\.m2\repository\org\xmlunit\xmlunit-legacy\2.10.1\xmlunit-legacy-2.10.1.jar;C:\Users\<USER>\.m2\repository\org\xmlunit\xmlunit-placeholders\2.10.1\xmlunit-placeholders-2.10.1.jar;C:\Users\<USER>\.m2\repository\net\javacrumbs\json-unit\json-unit-core\2.36.0\json-unit-core-2.36.0.jar;C:\Users\<USER>\.m2\repository\org\hamcrest\hamcrest-core\3.0\hamcrest-core-3.0.jar;C:\Users\<USER>\.m2\repository\org\ow2\asm\asm\9.4\asm-9.4.jar;C:\Users\<USER>\.m2\repository\net\sf\jopt-simple\jopt-simple\5.0.4\jopt-simple-5.0.4.jar;C:\Users\<USER>\.m2\repository\com\github\jknack\handlebars\4.3.1\handlebars-4.3.1.jar;C:\Users\<USER>\.m2\repository\com\github\jknack\handlebars-helpers\4.3.1\handlebars-helpers-4.3.1.jar;C:\Users\<USER>\.m2\repository\commons-fileupload\commons-fileupload\1.4\commons-fileupload-1.4.jar;C:\Users\<USER>\.m2\repository\org\springframework\security\spring-security-test\6.5.0\spring-security-test-6.5.0.jar;C:\Users\<USER>\.m2\repository\org\junit\platform\junit-platform-launcher\1.12.2\junit-platform-launcher-1.12.2.jar;C:\Users\<USER>\.m2\repository\org\junit\platform\junit-platform-engine\1.12.2\junit-platform-engine-1.12.2.jar;C:\Users\<USER>\.m2\repository\org\opentest4j\opentest4j\1.3.0\opentest4j-1.3.0.jar;C:\Users\<USER>\.m2\repository\org\junit\platform\junit-platform-commons\1.12.2\junit-platform-commons-1.12.2.jar;C:\Users\<USER>\.m2\repository\org\apiguardian\apiguardian-api\1.1.2\apiguardian-api-1.1.2.jar;C:\Users\<USER>\.m2\repository\org\springframework\boot\spring-boot-starter-data-elasticsearch\3.5.0\spring-boot-starter-data-elasticsearch-3.5.0.jar;C:\Users\<USER>\.m2\repository\org\springframework\data\spring-data-elasticsearch\5.5.0\spring-data-elasticsearch-5.5.0.jar;C:\Users\<USER>\.m2\repository\co\elastic\clients\elasticsearch-java\8.18.1\elasticsearch-java-8.18.1.jar;C:\Users\<USER>\.m2\repository\jakarta\json\jakarta.json-api\2.1.3\jakarta.json-api-2.1.3.jar;C:\Users\<USER>\.m2\repository\org\eclipse\parsson\parsson\1.0.5\parsson-1.0.5.jar;C:\Users\<USER>\.m2\repository\org\elasticsearch\client\elasticsearch-rest-client\8.18.1\elasticsearch-rest-client-8.18.1.jar;C:\Users\<USER>\.m2\repository\org\apache\httpcomponents\httpasyncclient\4.1.5\httpasyncclient-4.1.5.jar;C:\Users\<USER>\.m2\repository\org\apache\httpcomponents\httpcore-nio\4.4.16\httpcore-nio-4.4.16.jar;C:\Users\<USER>\.m2\repository\org\apache\tika\tika-core\2.9.1\tika-core-2.9.1.jar;C:\Users\<USER>\.m2\repository\org\apache\tika\tika-parsers-standard-package\2.9.1\tika-parsers-standard-package-2.9.1.jar;C:\Users\<USER>\.m2\repository\org\apache\tika\tika-parser-apple-module\2.9.1\tika-parser-apple-module-2.9.1.jar;C:\Users\<USER>\.m2\repository\org\apache\tika\tika-parser-zip-commons\2.9.1\tika-parser-zip-commons-2.9.1.jar;C:\Users\<USER>\.m2\repository\com\googlecode\plist\dd-plist\1.27\dd-plist-1.27.jar;C:\Users\<USER>\.m2\repository\org\apache\tika\tika-parser-audiovideo-module\2.9.1\tika-parser-audiovideo-module-2.9.1.jar;C:\Users\<USER>\.m2\repository\com\drewnoakes\metadata-extractor\2.18.0\metadata-extractor-2.18.0.jar;C:\Users\<USER>\.m2\repository\com\adobe\xmp\xmpcore\6.1.11\xmpcore-6.1.11.jar;C:\Users\<USER>\.m2\repository\org\apache\tika\tika-parser-cad-module\2.9.1\tika-parser-cad-module-2.9.1.jar;C:\Users\<USER>\.m2\repository\org\apache\tika\tika-parser-code-module\2.9.1\tika-parser-code-module-2.9.1.jar;C:\Users\<USER>\.m2\repository\org\codelibs\jhighlight\1.1.0\jhighlight-1.1.0.jar;C:\Users\<USER>\.m2\repository\org\ccil\cowan\tagsoup\tagsoup\1.2.1\tagsoup-1.2.1.jar;C:\Users\<USER>\.m2\repository\com\epam\parso\2.0.14\parso-2.0.14.jar;C:\Users\<USER>\.m2\repository\org\tallison\jmatio\1.5\jmatio-1.5.jar;C:\Users\<USER>\.m2\repository\org\apache\tika\tika-parser-crypto-module\2.9.1\tika-parser-crypto-module-2.9.1.jar;C:\Users\<USER>\.m2\repository\org\bouncycastle\bcmail-jdk18on\1.76\bcmail-jdk18on-1.76.jar;C:\Users\<USER>\.m2\repository\org\bouncycastle\bcprov-jdk18on\1.76\bcprov-jdk18on-1.76.jar;C:\Users\<USER>\.m2\repository\org\apache\tika\tika-parser-digest-commons\2.9.1\tika-parser-digest-commons-2.9.1.jar;C:\Users\<USER>\.m2\repository\org\apache\tika\tika-parser-font-module\2.9.1\tika-parser-font-module-2.9.1.jar;C:\Users\<USER>\.m2\repository\org\apache\tika\tika-parser-html-module\2.9.1\tika-parser-html-module-2.9.1.jar;C:\Users\<USER>\.m2\repository\org\apache\tika\tika-parser-image-module\2.9.1\tika-parser-image-module-2.9.1.jar;C:\Users\<USER>\.m2\repository\com\github\jai-imageio\jai-imageio-core\1.4.0\jai-imageio-core-1.4.0.jar;C:\Users\<USER>\.m2\repository\org\apache\pdfbox\jbig2-imageio\3.0.4\jbig2-imageio-3.0.4.jar;C:\Users\<USER>\.m2\repository\org\apache\tika\tika-parser-mail-module\2.9.1\tika-parser-mail-module-2.9.1.jar;C:\Users\<USER>\.m2\repository\org\apache\tika\tika-parser-mail-commons\2.9.1\tika-parser-mail-commons-2.9.1.jar;C:\Users\<USER>\.m2\repository\org\apache\james\apache-mime4j-core\0.8.9\apache-mime4j-core-0.8.9.jar;C:\Users\<USER>\.m2\repository\org\apache\james\apache-mime4j-dom\0.8.9\apache-mime4j-dom-0.8.9.jar;C:\Users\<USER>\.m2\repository\org\apache\tika\tika-parser-microsoft-module\2.9.1\tika-parser-microsoft-module-2.9.1.jar;C:\Users\<USER>\.m2\repository\com\pff\java-libpst\0.9.3\java-libpst-0.9.3.jar;C:\Users\<USER>\.m2\repository\com\healthmarketscience\jackcess\jackcess\4.0.5\jackcess-4.0.5.jar;C:\Users\<USER>\.m2\repository\com\healthmarketscience\jackcess\jackcess-encrypt\4.0.2\jackcess-encrypt-4.0.2.jar;C:\Users\<USER>\.m2\repository\org\slf4j\jcl-over-slf4j\2.0.17\jcl-over-slf4j-2.0.17.jar;C:\Users\<USER>\.m2\repository\org\apache\tika\tika-parser-miscoffice-module\2.9.1\tika-parser-miscoffice-module-2.9.1.jar;C:\Users\<USER>\.m2\repository\org\apache\tika\tika-parser-news-module\2.9.1\tika-parser-news-module-2.9.1.jar;C:\Users\<USER>\.m2\repository\com\rometools\rome\2.1.0\rome-2.1.0.jar;C:\Users\<USER>\.m2\repository\com\rometools\rome-utils\2.1.0\rome-utils-2.1.0.jar;C:\Users\<USER>\.m2\repository\org\jdom\jdom2\2.0.6.1\jdom2-2.0.6.1.jar;C:\Users\<USER>\.m2\repository\org\apache\tika\tika-parser-ocr-module\2.9.1\tika-parser-ocr-module-2.9.1.jar;C:\Users\<USER>\.m2\repository\org\apache\commons\commons-exec\1.3\commons-exec-1.3.jar;C:\Users\<USER>\.m2\repository\org\apache\tika\tika-parser-pdf-module\2.9.1\tika-parser-pdf-module-2.9.1.jar;C:\Users\<USER>\.m2\repository\org\apache\pdfbox\pdfbox-tools\2.0.29\pdfbox-tools-2.0.29.jar;C:\Users\<USER>\.m2\repository\org\apache\pdfbox\jempbox\1.8.17\jempbox-1.8.17.jar;C:\Users\<USER>\.m2\repository\org\apache\tika\tika-parser-pkg-module\2.9.1\tika-parser-pkg-module-2.9.1.jar;C:\Users\<USER>\.m2\repository\org\tukaani\xz\1.9\xz-1.9.jar;C:\Users\<USER>\.m2\repository\org\brotli\dec\0.1.2\dec-0.1.2.jar;C:\Users\<USER>\.m2\repository\com\github\junrar\junrar\7.5.5\junrar-7.5.5.jar;C:\Users\<USER>\.m2\repository\org\apache\tika\tika-parser-text-module\2.9.1\tika-parser-text-module-2.9.1.jar;C:\Users\<USER>\.m2\repository\com\github\albfernandez\juniversalchardet\2.4.0\juniversalchardet-2.4.0.jar;C:\Users\<USER>\.m2\repository\org\apache\commons\commons-csv\1.10.0\commons-csv-1.10.0.jar;C:\Users\<USER>\.m2\repository\org\apache\tika\tika-parser-webarchive-module\2.9.1\tika-parser-webarchive-module-2.9.1.jar;C:\Users\<USER>\.m2\repository\org\netpreserve\jwarc\0.28.3\jwarc-0.28.3.jar;C:\Users\<USER>\.m2\repository\org\apache\tika\tika-parser-xml-module\2.9.1\tika-parser-xml-module-2.9.1.jar;C:\Users\<USER>\.m2\repository\xerces\xercesImpl\2.12.2\xercesImpl-2.12.2.jar;C:\Users\<USER>\.m2\repository\xml-apis\xml-apis\1.4.01\xml-apis-1.4.01.jar;C:\Users\<USER>\.m2\repository\org\apache\tika\tika-parser-xmp-commons\2.9.1\tika-parser-xmp-commons-2.9.1.jar;C:\Users\<USER>\.m2\repository\org\apache\pdfbox\xmpbox\2.0.29\xmpbox-2.0.29.jar;C:\Users\<USER>\.m2\repository\org\gagravarr\vorbis-java-tika\0.8\vorbis-java-tika-0.8.jar;C:\Users\<USER>\.m2\repository\org\gagravarr\vorbis-java-core\0.8\vorbis-java-core-0.8.jar;C:\Users\<USER>\.m2\repository\org\apache\pdfbox\pdfbox\3.0.1\pdfbox-3.0.1.jar;C:\Users\<USER>\.m2\repository\org\apache\pdfbox\pdfbox-io\3.0.1\pdfbox-io-3.0.1.jar;C:\Users\<USER>\.m2\repository\org\apache\pdfbox\fontbox\3.0.1\fontbox-3.0.1.jar;C:\Users\<USER>\.m2\repository\commons-logging\commons-logging\1.2\commons-logging-1.2.jar;C:\Users\<USER>\.m2\repository\com\itextpdf\itextpdf\5.5.13.3\itextpdf-5.5.13.3.jar;C:\Users\<USER>\.m2\repository\org\apache\poi\poi\5.2.5\poi-5.2.5.jar;C:\Users\<USER>\.m2\repository\commons-codec\commons-codec\1.18.0\commons-codec-1.18.0.jar;C:\Users\<USER>\.m2\repository\org\apache\commons\commons-math3\3.6.1\commons-math3-3.6.1.jar;C:\Users\<USER>\.m2\repository\com\zaxxer\SparseBitSet\1.3\SparseBitSet-1.3.jar;C:\Users\<USER>\.m2\repository\org\apache\logging\log4j\log4j-api\2.24.3\log4j-api-2.24.3.jar;C:\Users\<USER>\.m2\repository\org\apache\poi\poi-ooxml\5.2.5\poi-ooxml-5.2.5.jar;C:\Users\<USER>\.m2\repository\org\apache\poi\poi-ooxml-lite\5.2.5\poi-ooxml-lite-5.2.5.jar;C:\Users\<USER>\.m2\repository\org\apache\xmlbeans\xmlbeans\5.2.0\xmlbeans-5.2.0.jar;C:\Users\<USER>\.m2\repository\org\apache\commons\commons-compress\1.25.0\commons-compress-1.25.0.jar;C:\Users\<USER>\.m2\repository\com\github\virtuald\curvesapi\1.08\curvesapi-1.08.jar;C:\Users\<USER>\.m2\repository\org\apache\poi\poi-scratchpad\5.2.5\poi-scratchpad-5.2.5.jar;C:\Users\<USER>\.m2\repository\org\testcontainers\elasticsearch\1.21.0\elasticsearch-1.21.0.jar;C:\Users\<USER>\.m2\repository\io\kubernetes\client-java\19.0.0\client-java-19.0.0.jar;C:\Users\<USER>\.m2\repository\io\prometheus\simpleclient\0.16.0\simpleclient-0.16.0.jar;C:\Users\<USER>\.m2\repository\io\prometheus\simpleclient_tracer_otel\0.16.0\simpleclient_tracer_otel-0.16.0.jar;C:\Users\<USER>\.m2\repository\io\prometheus\simpleclient_tracer_common\0.16.0\simpleclient_tracer_common-0.16.0.jar;C:\Users\<USER>\.m2\repository\io\prometheus\simpleclient_tracer_otel_agent\0.16.0\simpleclient_tracer_otel_agent-0.16.0.jar;C:\Users\<USER>\.m2\repository\io\prometheus\simpleclient_httpserver\0.16.0\simpleclient_httpserver-0.16.0.jar;C:\Users\<USER>\.m2\repository\io\prometheus\simpleclient_common\0.16.0\simpleclient_common-0.16.0.jar;C:\Users\<USER>\.m2\repository\io\kubernetes\client-java-api\19.0.0\client-java-api-19.0.0.jar;C:\Users\<USER>\.m2\repository\javax\annotation\javax.annotation-api\1.3.2\javax.annotation-api-1.3.2.jar;C:\Users\<USER>\.m2\repository\io\swagger\swagger-annotations\1.6.11\swagger-annotations-1.6.11.jar;C:\Users\<USER>\.m2\repository\com\squareup\okhttp3\logging-interceptor\4.11.0\logging-interceptor-4.11.0.jar;C:\Users\<USER>\.m2\repository\io\gsonfire\gson-fire\1.8.5\gson-fire-1.8.5.jar;C:\Users\<USER>\.m2\repository\io\kubernetes\client-java-proto\19.0.0\client-java-proto-19.0.0.jar;C:\Users\<USER>\.m2\repository\org\bouncycastle\bcpkix-jdk18on\1.76\bcpkix-jdk18on-1.76.jar;C:\Users\<USER>\.m2\repository\org\bouncycastle\bcutil-jdk18on\1.76\bcutil-jdk18on-1.76.jar;C:\Users\<USER>\.m2\repository\com\google\protobuf\protobuf-java\3.24.3\protobuf-java-3.24.3.jar;C:\Users\<USER>\.m2\repository\org\bitbucket\b_c\jose4j\0.9.3\jose4j-0.9.3.jar;C:\Users\<USER>\.m2\repository\org\apache\httpcomponents\client5\httpclient5\5.4.4\httpclient5-5.4.4.jar;C:\Users\<USER>\.m2\repository\org\apache\httpcomponents\core5\httpcore5\5.3.4\httpcore5-5.3.4.jar;C:\Users\<USER>\.m2\repository\org\apache\httpcomponents\core5\httpcore5-h2\5.3.4\httpcore5-h2-5.3.4.jar;C:\Users\<USER>\.m2\repository\org\yaml\snakeyaml\2.4\snakeyaml-2.4.jar;"/>
    <property name="java.vm.vendor" value="Oracle Corporation"/>
    <property name="otel.instrumentation.spring-boot.enabled" value="false"/>
    <property name="sun.arch.data.model" value="64"/>
    <property name="user.variant" value=""/>
    <property name="java.vendor.url" value="https://java.oracle.com/"/>
    <property name="catalina.useNaming" value="false"/>
    <property name="user.timezone" value="Asia/Calcutta"/>
    <property name="org.jboss.logging.provider" value="slf4j"/>
    <property name="java.vm.specification.version" value="21"/>
    <property name="os.name" value="Windows 10"/>
    <property name="APPLICATION_NAME" value="dms-svc"/>
    <property name="sun.java.launcher" value="SUN_STANDARD"/>
    <property name="user.country" value="IN"/>
    <property name="sun.boot.library.path" value="C:\Program Files\Java\jdk-21\bin"/>
    <property name="sun.java.command" value="C:\Users\<USER>\AppData\Local\Temp\surefire7646089020984470424\surefirebooter-20250722231242388_3.jar C:\Users\<USER>\AppData\Local\Temp\surefire7646089020984470424 2025-07-22T23-12-42_129-jvmRun1 surefire-20250722231242388_1tmp surefire_0-20250722231242388_2tmp"/>
    <property name="jdk.debug" value="release"/>
    <property name="test" value="UrlUploadIntegrationTest"/>
    <property name="surefire.test.class.path" value="D:\grc-platform-v4\dms-svc\target\test-classes;D:\grc-platform-v4\dms-svc\target\classes;C:\Users\<USER>\.m2\repository\org\springframework\boot\spring-boot-starter-data-jpa\3.5.0\spring-boot-starter-data-jpa-3.5.0.jar;C:\Users\<USER>\.m2\repository\org\springframework\boot\spring-boot-starter\3.5.0\spring-boot-starter-3.5.0.jar;C:\Users\<USER>\.m2\repository\org\springframework\boot\spring-boot\3.5.0\spring-boot-3.5.0.jar;C:\Users\<USER>\.m2\repository\org\springframework\boot\spring-boot-autoconfigure\3.5.0\spring-boot-autoconfigure-3.5.0.jar;C:\Users\<USER>\.m2\repository\org\springframework\boot\spring-boot-starter-logging\3.5.0\spring-boot-starter-logging-3.5.0.jar;C:\Users\<USER>\.m2\repository\ch\qos\logback\logback-classic\1.5.18\logback-classic-1.5.18.jar;C:\Users\<USER>\.m2\repository\ch\qos\logback\logback-core\1.5.18\logback-core-1.5.18.jar;C:\Users\<USER>\.m2\repository\org\apache\logging\log4j\log4j-to-slf4j\2.24.3\log4j-to-slf4j-2.24.3.jar;C:\Users\<USER>\.m2\repository\org\slf4j\jul-to-slf4j\2.0.17\jul-to-slf4j-2.0.17.jar;C:\Users\<USER>\.m2\repository\org\springframework\boot\spring-boot-starter-jdbc\3.5.0\spring-boot-starter-jdbc-3.5.0.jar;C:\Users\<USER>\.m2\repository\com\zaxxer\HikariCP\6.3.0\HikariCP-6.3.0.jar;C:\Users\<USER>\.m2\repository\org\springframework\spring-jdbc\6.2.7\spring-jdbc-6.2.7.jar;C:\Users\<USER>\.m2\repository\org\hibernate\orm\hibernate-core\6.6.15.Final\hibernate-core-6.6.15.Final.jar;C:\Users\<USER>\.m2\repository\jakarta\persistence\jakarta.persistence-api\3.1.0\jakarta.persistence-api-3.1.0.jar;C:\Users\<USER>\.m2\repository\jakarta\transaction\jakarta.transaction-api\2.0.1\jakarta.transaction-api-2.0.1.jar;C:\Users\<USER>\.m2\repository\org\jboss\logging\jboss-logging\3.6.1.Final\jboss-logging-3.6.1.Final.jar;C:\Users\<USER>\.m2\repository\org\hibernate\common\hibernate-commons-annotations\7.0.3.Final\hibernate-commons-annotations-7.0.3.Final.jar;C:\Users\<USER>\.m2\repository\io\smallrye\jandex\3.2.0\jandex-3.2.0.jar;C:\Users\<USER>\.m2\repository\com\fasterxml\classmate\1.7.0\classmate-1.7.0.jar;C:\Users\<USER>\.m2\repository\net\bytebuddy\byte-buddy\1.17.5\byte-buddy-1.17.5.jar;C:\Users\<USER>\.m2\repository\org\glassfish\jaxb\jaxb-runtime\4.0.5\jaxb-runtime-4.0.5.jar;C:\Users\<USER>\.m2\repository\org\glassfish\jaxb\jaxb-core\4.0.5\jaxb-core-4.0.5.jar;C:\Users\<USER>\.m2\repository\org\eclipse\angus\angus-activation\2.0.2\angus-activation-2.0.2.jar;C:\Users\<USER>\.m2\repository\org\glassfish\jaxb\txw2\4.0.5\txw2-4.0.5.jar;C:\Users\<USER>\.m2\repository\com\sun\istack\istack-commons-runtime\4.1.2\istack-commons-runtime-4.1.2.jar;C:\Users\<USER>\.m2\repository\jakarta\inject\jakarta.inject-api\2.0.1\jakarta.inject-api-2.0.1.jar;C:\Users\<USER>\.m2\repository\org\antlr\antlr4-runtime\4.13.0\antlr4-runtime-4.13.0.jar;C:\Users\<USER>\.m2\repository\org\springframework\data\spring-data-jpa\3.5.0\spring-data-jpa-3.5.0.jar;C:\Users\<USER>\.m2\repository\org\springframework\data\spring-data-commons\3.5.0\spring-data-commons-3.5.0.jar;C:\Users\<USER>\.m2\repository\org\springframework\spring-orm\6.2.7\spring-orm-6.2.7.jar;C:\Users\<USER>\.m2\repository\org\springframework\spring-tx\6.2.7\spring-tx-6.2.7.jar;C:\Users\<USER>\.m2\repository\org\springframework\spring-beans\6.2.7\spring-beans-6.2.7.jar;C:\Users\<USER>\.m2\repository\org\springframework\spring-aspects\6.2.7\spring-aspects-6.2.7.jar;C:\Users\<USER>\.m2\repository\org\springframework\boot\spring-boot-starter-graphql\3.5.0\spring-boot-starter-graphql-3.5.0.jar;C:\Users\<USER>\.m2\repository\org\springframework\boot\spring-boot-starter-json\3.5.0\spring-boot-starter-json-3.5.0.jar;C:\Users\<USER>\.m2\repository\com\fasterxml\jackson\datatype\jackson-datatype-jdk8\2.19.0\jackson-datatype-jdk8-2.19.0.jar;C:\Users\<USER>\.m2\repository\com\fasterxml\jackson\module\jackson-module-parameter-names\2.19.0\jackson-module-parameter-names-2.19.0.jar;C:\Users\<USER>\.m2\repository\org\springframework\graphql\spring-graphql\1.4.0\spring-graphql-1.4.0.jar;C:\Users\<USER>\.m2\repository\org\springframework\boot\spring-boot-starter-web\3.5.0\spring-boot-starter-web-3.5.0.jar;C:\Users\<USER>\.m2\repository\org\springframework\boot\spring-boot-starter-tomcat\3.5.0\spring-boot-starter-tomcat-3.5.0.jar;C:\Users\<USER>\.m2\repository\org\apache\tomcat\embed\tomcat-embed-core\10.1.41\tomcat-embed-core-10.1.41.jar;C:\Users\<USER>\.m2\repository\org\apache\tomcat\embed\tomcat-embed-websocket\10.1.41\tomcat-embed-websocket-10.1.41.jar;C:\Users\<USER>\.m2\repository\org\springframework\spring-web\6.2.7\spring-web-6.2.7.jar;C:\Users\<USER>\.m2\repository\org\springframework\spring-webmvc\6.2.7\spring-webmvc-6.2.7.jar;C:\Users\<USER>\.m2\repository\org\springframework\spring-expression\6.2.7\spring-expression-6.2.7.jar;C:\Users\<USER>\.m2\repository\org\springframework\boot\spring-boot-starter-security\3.5.0\spring-boot-starter-security-3.5.0.jar;C:\Users\<USER>\.m2\repository\org\springframework\spring-aop\6.2.7\spring-aop-6.2.7.jar;C:\Users\<USER>\.m2\repository\org\springframework\security\spring-security-config\6.5.0\spring-security-config-6.5.0.jar;C:\Users\<USER>\.m2\repository\org\springframework\security\spring-security-web\6.5.0\spring-security-web-6.5.0.jar;C:\Users\<USER>\.m2\repository\org\springframework\boot\spring-boot-starter-data-redis\3.5.0\spring-boot-starter-data-redis-3.5.0.jar;C:\Users\<USER>\.m2\repository\io\lettuce\lettuce-core\6.5.5.RELEASE\lettuce-core-6.5.5.RELEASE.jar;C:\Users\<USER>\.m2\repository\io\netty\netty-common\4.1.121.Final\netty-common-4.1.121.Final.jar;C:\Users\<USER>\.m2\repository\io\netty\netty-handler\4.1.121.Final\netty-handler-4.1.121.Final.jar;C:\Users\<USER>\.m2\repository\io\netty\netty-transport\4.1.121.Final\netty-transport-4.1.121.Final.jar;C:\Users\<USER>\.m2\repository\org\springframework\data\spring-data-redis\3.5.0\spring-data-redis-3.5.0.jar;C:\Users\<USER>\.m2\repository\org\springframework\data\spring-data-keyvalue\3.5.0\spring-data-keyvalue-3.5.0.jar;C:\Users\<USER>\.m2\repository\org\springframework\spring-oxm\6.2.7\spring-oxm-6.2.7.jar;C:\Users\<USER>\.m2\repository\org\springframework\spring-context-support\6.2.7\spring-context-support-6.2.7.jar;C:\Users\<USER>\.m2\repository\org\springframework\boot\spring-boot-starter-actuator\3.5.0\spring-boot-starter-actuator-3.5.0.jar;C:\Users\<USER>\.m2\repository\org\springframework\boot\spring-boot-actuator-autoconfigure\3.5.0\spring-boot-actuator-autoconfigure-3.5.0.jar;C:\Users\<USER>\.m2\repository\org\springframework\boot\spring-boot-actuator\3.5.0\spring-boot-actuator-3.5.0.jar;C:\Users\<USER>\.m2\repository\io\micrometer\micrometer-observation\1.15.0\micrometer-observation-1.15.0.jar;C:\Users\<USER>\.m2\repository\io\micrometer\micrometer-commons\1.15.0\micrometer-commons-1.15.0.jar;C:\Users\<USER>\.m2\repository\io\micrometer\micrometer-jakarta9\1.15.0\micrometer-jakarta9-1.15.0.jar;C:\Users\<USER>\.m2\repository\org\springframework\boot\spring-boot-starter-validation\3.5.0\spring-boot-starter-validation-3.5.0.jar;C:\Users\<USER>\.m2\repository\org\apache\tomcat\embed\tomcat-embed-el\10.1.41\tomcat-embed-el-10.1.41.jar;C:\Users\<USER>\.m2\repository\org\hibernate\validator\hibernate-validator\8.0.2.Final\hibernate-validator-8.0.2.Final.jar;C:\Users\<USER>\.m2\repository\jakarta\validation\jakarta.validation-api\3.0.2\jakarta.validation-api-3.0.2.jar;C:\Users\<USER>\.m2\repository\com\mysql\mysql-connector-j\9.2.0\mysql-connector-j-9.2.0.jar;C:\Users\<USER>\.m2\repository\io\jsonwebtoken\jjwt-api\0.12.3\jjwt-api-0.12.3.jar;C:\Users\<USER>\.m2\repository\io\jsonwebtoken\jjwt-impl\0.12.3\jjwt-impl-0.12.3.jar;C:\Users\<USER>\.m2\repository\io\jsonwebtoken\jjwt-jackson\0.12.3\jjwt-jackson-0.12.3.jar;C:\Users\<USER>\.m2\repository\org\liquibase\liquibase-core\4.31.1\liquibase-core-4.31.1.jar;C:\Users\<USER>\.m2\repository\com\opencsv\opencsv\5.9\opencsv-5.9.jar;C:\Users\<USER>\.m2\repository\javax\xml\bind\jaxb-api\2.3.1\jaxb-api-2.3.1.jar;C:\Users\<USER>\.m2\repository\org\apache\commons\commons-collections4\4.4\commons-collections4-4.4.jar;C:\Users\<USER>\.m2\repository\org\apache\commons\commons-text\1.13.0\commons-text-1.13.0.jar;C:\Users\<USER>\.m2\repository\org\apache\commons\commons-lang3\3.17.0\commons-lang3-3.17.0.jar;C:\Users\<USER>\.m2\repository\commons-io\commons-io\2.15.1\commons-io-2.15.1.jar;C:\Users\<USER>\.m2\repository\software\amazon\awssdk\s3\2.20.162\s3-2.20.162.jar;C:\Users\<USER>\.m2\repository\software\amazon\awssdk\aws-xml-protocol\2.20.162\aws-xml-protocol-2.20.162.jar;C:\Users\<USER>\.m2\repository\software\amazon\awssdk\aws-query-protocol\2.20.162\aws-query-protocol-2.20.162.jar;C:\Users\<USER>\.m2\repository\software\amazon\awssdk\protocol-core\2.20.162\protocol-core-2.20.162.jar;C:\Users\<USER>\.m2\repository\software\amazon\awssdk\arns\2.20.162\arns-2.20.162.jar;C:\Users\<USER>\.m2\repository\software\amazon\awssdk\profiles\2.20.162\profiles-2.20.162.jar;C:\Users\<USER>\.m2\repository\software\amazon\awssdk\crt-core\2.20.162\crt-core-2.20.162.jar;C:\Users\<USER>\.m2\repository\software\amazon\awssdk\sdk-core\2.20.162\sdk-core-2.20.162.jar;C:\Users\<USER>\.m2\repository\org\reactivestreams\reactive-streams\1.0.4\reactive-streams-1.0.4.jar;C:\Users\<USER>\.m2\repository\software\amazon\awssdk\http-client-spi\2.20.162\http-client-spi-2.20.162.jar;C:\Users\<USER>\.m2\repository\software\amazon\awssdk\annotations\2.20.162\annotations-2.20.162.jar;C:\Users\<USER>\.m2\repository\software\amazon\awssdk\utils\2.20.162\utils-2.20.162.jar;C:\Users\<USER>\.m2\repository\software\amazon\awssdk\aws-core\2.20.162\aws-core-2.20.162.jar;C:\Users\<USER>\.m2\repository\software\amazon\awssdk\metrics-spi\2.20.162\metrics-spi-2.20.162.jar;C:\Users\<USER>\.m2\repository\software\amazon\awssdk\json-utils\2.20.162\json-utils-2.20.162.jar;C:\Users\<USER>\.m2\repository\software\amazon\awssdk\third-party-jackson-core\2.20.162\third-party-jackson-core-2.20.162.jar;C:\Users\<USER>\.m2\repository\software\amazon\awssdk\endpoints-spi\2.20.162\endpoints-spi-2.20.162.jar;C:\Users\<USER>\.m2\repository\software\amazon\awssdk\apache-client\2.20.162\apache-client-2.20.162.jar;C:\Users\<USER>\.m2\repository\org\apache\httpcomponents\httpclient\4.5.13\httpclient-4.5.13.jar;C:\Users\<USER>\.m2\repository\org\apache\httpcomponents\httpcore\4.4.16\httpcore-4.4.16.jar;C:\Users\<USER>\.m2\repository\software\amazon\awssdk\netty-nio-client\2.20.162\netty-nio-client-2.20.162.jar;C:\Users\<USER>\.m2\repository\io\netty\netty-codec-http\4.1.121.Final\netty-codec-http-4.1.121.Final.jar;C:\Users\<USER>\.m2\repository\io\netty\netty-codec-http2\4.1.121.Final\netty-codec-http2-4.1.121.Final.jar;C:\Users\<USER>\.m2\repository\io\netty\netty-codec\4.1.121.Final\netty-codec-4.1.121.Final.jar;C:\Users\<USER>\.m2\repository\io\netty\netty-buffer\4.1.121.Final\netty-buffer-4.1.121.Final.jar;C:\Users\<USER>\.m2\repository\io\netty\netty-transport-classes-epoll\4.1.121.Final\netty-transport-classes-epoll-4.1.121.Final.jar;C:\Users\<USER>\.m2\repository\io\netty\netty-resolver\4.1.121.Final\netty-resolver-4.1.121.Final.jar;C:\Users\<USER>\.m2\repository\software\amazon\awssdk\auth\2.20.162\auth-2.20.162.jar;C:\Users\<USER>\.m2\repository\software\amazon\eventstream\eventstream\1.0.1\eventstream-1.0.1.jar;C:\Users\<USER>\.m2\repository\software\amazon\awssdk\regions\2.20.162\regions-2.20.162.jar;C:\Users\<USER>\.m2\repository\org\slf4j\slf4j-api\2.0.17\slf4j-api-2.0.17.jar;C:\Users\<USER>\.m2\repository\com\graphql-java\graphql-java-extended-scalars\22.0\graphql-java-extended-scalars-22.0.jar;C:\Users\<USER>\.m2\repository\com\graphql-java\graphql-java\24.0\graphql-java-24.0.jar;C:\Users\<USER>\.m2\repository\com\graphql-java\java-dataloader\5.0.0\java-dataloader-5.0.0.jar;C:\Users\<USER>\.m2\repository\org\jspecify\jspecify\1.0.0\jspecify-1.0.0.jar;C:\Users\<USER>\.m2\repository\com\microsoft\graph\microsoft-graph\6.15.0\microsoft-graph-6.15.0.jar;C:\Users\<USER>\.m2\repository\com\microsoft\graph\microsoft-graph-core\3.2.0\microsoft-graph-core-3.2.0.jar;C:\Users\<USER>\.m2\repository\com\microsoft\kiota\microsoft-kiota-abstractions\1.3.0\microsoft-kiota-abstractions-1.3.0.jar;C:\Users\<USER>\.m2\repository\io\github\std-uritemplate\std-uritemplate\1.0.5\std-uritemplate-1.0.5.jar;C:\Users\<USER>\.m2\repository\com\google\code\gson\gson\2.13.1\gson-2.13.1.jar;C:\Users\<USER>\.m2\repository\com\squareup\okhttp3\okhttp\4.12.0\okhttp-4.12.0.jar;C:\Users\<USER>\.m2\repository\com\squareup\okio\okio\3.6.0\okio-3.6.0.jar;C:\Users\<USER>\.m2\repository\com\squareup\okio\okio-jvm\3.6.0\okio-jvm-3.6.0.jar;C:\Users\<USER>\.m2\repository\org\jetbrains\kotlin\kotlin-stdlib-common\1.9.25\kotlin-stdlib-common-1.9.25.jar;C:\Users\<USER>\.m2\repository\org\jetbrains\kotlin\kotlin-stdlib-jdk8\1.9.25\kotlin-stdlib-jdk8-1.9.25.jar;C:\Users\<USER>\.m2\repository\org\jetbrains\kotlin\kotlin-stdlib\1.9.25\kotlin-stdlib-1.9.25.jar;C:\Users\<USER>\.m2\repository\org\jetbrains\kotlin\kotlin-stdlib-jdk7\1.9.25\kotlin-stdlib-jdk7-1.9.25.jar;C:\Users\<USER>\.m2\repository\jakarta\annotation\jakarta.annotation-api\2.1.1\jakarta.annotation-api-2.1.1.jar;C:\Users\<USER>\.m2\repository\com\microsoft\kiota\microsoft-kiota-authentication-azure\1.3.0\microsoft-kiota-authentication-azure-1.3.0.jar;C:\Users\<USER>\.m2\repository\io\opentelemetry\opentelemetry-context\1.49.0\opentelemetry-context-1.49.0.jar;C:\Users\<USER>\.m2\repository\com\microsoft\kiota\microsoft-kiota-http-okHttp\1.3.0\microsoft-kiota-http-okHttp-1.3.0.jar;C:\Users\<USER>\.m2\repository\com\microsoft\kiota\microsoft-kiota-serialization-json\1.3.0\microsoft-kiota-serialization-json-1.3.0.jar;C:\Users\<USER>\.m2\repository\com\microsoft\kiota\microsoft-kiota-serialization-text\1.3.0\microsoft-kiota-serialization-text-1.3.0.jar;C:\Users\<USER>\.m2\repository\com\microsoft\kiota\microsoft-kiota-serialization-form\1.3.0\microsoft-kiota-serialization-form-1.3.0.jar;C:\Users\<USER>\.m2\repository\com\microsoft\kiota\microsoft-kiota-serialization-multipart\1.3.0\microsoft-kiota-serialization-multipart-1.3.0.jar;C:\Users\<USER>\.m2\repository\com\azure\azure-identity\1.11.4\azure-identity-1.11.4.jar;C:\Users\<USER>\.m2\repository\com\azure\azure-core-http-netty\1.14.1\azure-core-http-netty-1.14.1.jar;C:\Users\<USER>\.m2\repository\io\netty\netty-handler-proxy\4.1.121.Final\netty-handler-proxy-4.1.121.Final.jar;C:\Users\<USER>\.m2\repository\io\netty\netty-codec-socks\4.1.121.Final\netty-codec-socks-4.1.121.Final.jar;C:\Users\<USER>\.m2\repository\io\netty\netty-transport-native-unix-common\4.1.121.Final\netty-transport-native-unix-common-4.1.121.Final.jar;C:\Users\<USER>\.m2\repository\io\netty\netty-transport-native-epoll\4.1.121.Final\netty-transport-native-epoll-4.1.121.Final-linux-x86_64.jar;C:\Users\<USER>\.m2\repository\io\netty\netty-transport-native-kqueue\4.1.121.Final\netty-transport-native-kqueue-4.1.121.Final-osx-x86_64.jar;C:\Users\<USER>\.m2\repository\io\netty\netty-transport-classes-kqueue\4.1.121.Final\netty-transport-classes-kqueue-4.1.121.Final.jar;C:\Users\<USER>\.m2\repository\io\netty\netty-tcnative-boringssl-static\2.0.70.Final\netty-tcnative-boringssl-static-2.0.70.Final.jar;C:\Users\<USER>\.m2\repository\io\netty\netty-tcnative-classes\2.0.70.Final\netty-tcnative-classes-2.0.70.Final.jar;C:\Users\<USER>\.m2\repository\io\netty\netty-tcnative-boringssl-static\2.0.70.Final\netty-tcnative-boringssl-static-2.0.70.Final-linux-x86_64.jar;C:\Users\<USER>\.m2\repository\io\netty\netty-tcnative-boringssl-static\2.0.70.Final\netty-tcnative-boringssl-static-2.0.70.Final-linux-aarch_64.jar;C:\Users\<USER>\.m2\repository\io\netty\netty-tcnative-boringssl-static\2.0.70.Final\netty-tcnative-boringssl-static-2.0.70.Final-osx-x86_64.jar;C:\Users\<USER>\.m2\repository\io\netty\netty-tcnative-boringssl-static\2.0.70.Final\netty-tcnative-boringssl-static-2.0.70.Final-osx-aarch_64.jar;C:\Users\<USER>\.m2\repository\io\netty\netty-tcnative-boringssl-static\2.0.70.Final\netty-tcnative-boringssl-static-2.0.70.Final-windows-x86_64.jar;C:\Users\<USER>\.m2\repository\io\projectreactor\netty\reactor-netty-http\1.2.6\reactor-netty-http-1.2.6.jar;C:\Users\<USER>\.m2\repository\io\netty\netty-resolver-dns\4.1.121.Final\netty-resolver-dns-4.1.121.Final.jar;C:\Users\<USER>\.m2\repository\io\netty\netty-codec-dns\4.1.121.Final\netty-codec-dns-4.1.121.Final.jar;C:\Users\<USER>\.m2\repository\io\netty\netty-resolver-dns-native-macos\4.1.121.Final\netty-resolver-dns-native-macos-4.1.121.Final-osx-x86_64.jar;C:\Users\<USER>\.m2\repository\io\netty\netty-resolver-dns-classes-macos\4.1.121.Final\netty-resolver-dns-classes-macos-4.1.121.Final.jar;C:\Users\<USER>\.m2\repository\io\projectreactor\netty\reactor-netty-core\1.2.6\reactor-netty-core-1.2.6.jar;C:\Users\<USER>\.m2\repository\com\microsoft\azure\msal4j\1.14.3\msal4j-1.14.3.jar;C:\Users\<USER>\.m2\repository\com\nimbusds\oauth2-oidc-sdk\11.9.1\oauth2-oidc-sdk-11.9.1.jar;C:\Users\<USER>\.m2\repository\com\github\stephenc\jcip\jcip-annotations\1.0-1\jcip-annotations-1.0-1.jar;C:\Users\<USER>\.m2\repository\com\nimbusds\content-type\2.3\content-type-2.3.jar;C:\Users\<USER>\.m2\repository\com\nimbusds\lang-tag\1.7\lang-tag-1.7.jar;C:\Users\<USER>\.m2\repository\com\microsoft\azure\msal4j-persistence-extension\1.2.0\msal4j-persistence-extension-1.2.0.jar;C:\Users\<USER>\.m2\repository\net\java\dev\jna\jna\5.13.0\jna-5.13.0.jar;C:\Users\<USER>\.m2\repository\net\java\dev\jna\jna-platform\5.6.0\jna-platform-5.6.0.jar;C:\Users\<USER>\.m2\repository\com\azure\azure-core\1.46.0\azure-core-1.46.0.jar;C:\Users\<USER>\.m2\repository\com\azure\azure-json\1.1.0\azure-json-1.1.0.jar;C:\Users\<USER>\.m2\repository\io\projectreactor\reactor-core\3.7.6\reactor-core-3.7.6.jar;C:\Users\<USER>\.m2\repository\org\springframework\boot\spring-boot-starter-oauth2-client\3.5.0\spring-boot-starter-oauth2-client-3.5.0.jar;C:\Users\<USER>\.m2\repository\org\springframework\security\spring-security-core\6.5.0\spring-security-core-6.5.0.jar;C:\Users\<USER>\.m2\repository\org\springframework\security\spring-security-crypto\6.5.0\spring-security-crypto-6.5.0.jar;C:\Users\<USER>\.m2\repository\org\springframework\security\spring-security-oauth2-client\6.5.0\spring-security-oauth2-client-6.5.0.jar;C:\Users\<USER>\.m2\repository\org\springframework\security\spring-security-oauth2-core\6.5.0\spring-security-oauth2-core-6.5.0.jar;C:\Users\<USER>\.m2\repository\org\springframework\security\spring-security-oauth2-jose\6.5.0\spring-security-oauth2-jose-6.5.0.jar;C:\Users\<USER>\.m2\repository\com\nimbusds\nimbus-jose-jwt\9.37.3\nimbus-jose-jwt-9.37.3.jar;C:\Users\<USER>\.m2\repository\io\micrometer\micrometer-registry-prometheus\1.15.0\micrometer-registry-prometheus-1.15.0.jar;C:\Users\<USER>\.m2\repository\io\micrometer\micrometer-core\1.15.0\micrometer-core-1.15.0.jar;C:\Users\<USER>\.m2\repository\org\hdrhistogram\HdrHistogram\2.2.2\HdrHistogram-2.2.2.jar;C:\Users\<USER>\.m2\repository\org\latencyutils\LatencyUtils\2.0.3\LatencyUtils-2.0.3.jar;C:\Users\<USER>\.m2\repository\io\prometheus\prometheus-metrics-core\1.3.6\prometheus-metrics-core-1.3.6.jar;C:\Users\<USER>\.m2\repository\io\prometheus\prometheus-metrics-model\1.3.6\prometheus-metrics-model-1.3.6.jar;C:\Users\<USER>\.m2\repository\io\prometheus\prometheus-metrics-config\1.3.6\prometheus-metrics-config-1.3.6.jar;C:\Users\<USER>\.m2\repository\io\prometheus\prometheus-metrics-tracer-common\1.3.6\prometheus-metrics-tracer-common-1.3.6.jar;C:\Users\<USER>\.m2\repository\io\prometheus\prometheus-metrics-exposition-formats\1.3.6\prometheus-metrics-exposition-formats-1.3.6.jar;C:\Users\<USER>\.m2\repository\io\prometheus\prometheus-metrics-exposition-textformats\1.3.6\prometheus-metrics-exposition-textformats-1.3.6.jar;C:\Users\<USER>\.m2\repository\net\logstash\logback\logstash-logback-encoder\7.4\logstash-logback-encoder-7.4.jar;C:\Users\<USER>\.m2\repository\io\micrometer\micrometer-tracing-bridge-brave\1.5.0\micrometer-tracing-bridge-brave-1.5.0.jar;C:\Users\<USER>\.m2\repository\io\zipkin\brave\brave\6.1.0\brave-6.1.0.jar;C:\Users\<USER>\.m2\repository\io\zipkin\brave\brave-context-slf4j\6.1.0\brave-context-slf4j-6.1.0.jar;C:\Users\<USER>\.m2\repository\io\zipkin\brave\brave-instrumentation-http\6.1.0\brave-instrumentation-http-6.1.0.jar;C:\Users\<USER>\.m2\repository\io\zipkin\aws\brave-propagation-aws\1.3.0\brave-propagation-aws-1.3.0.jar;C:\Users\<USER>\.m2\repository\io\zipkin\contrib\brave-propagation-w3c\brave-propagation-tracecontext\0.2.0\brave-propagation-tracecontext-0.2.0.jar;C:\Users\<USER>\.m2\repository\io\zipkin\reporter2\zipkin-reporter-brave\3.5.0\zipkin-reporter-brave-3.5.0.jar;C:\Users\<USER>\.m2\repository\io\zipkin\reporter2\zipkin-reporter\3.5.0\zipkin-reporter-3.5.0.jar;C:\Users\<USER>\.m2\repository\io\micrometer\micrometer-tracing\1.5.0\micrometer-tracing-1.5.0.jar;C:\Users\<USER>\.m2\repository\io\micrometer\context-propagation\1.1.3\context-propagation-1.1.3.jar;C:\Users\<USER>\.m2\repository\aopalliance\aopalliance\1.0\aopalliance-1.0.jar;C:\Users\<USER>\.m2\repository\io\opentelemetry\instrumentation\opentelemetry-spring-boot-starter\2.10.0\opentelemetry-spring-boot-starter-2.10.0.jar;C:\Users\<USER>\.m2\repository\io\opentelemetry\instrumentation\opentelemetry-spring-boot-autoconfigure\2.10.0\opentelemetry-spring-boot-autoconfigure-2.10.0.jar;C:\Users\<USER>\.m2\repository\io\opentelemetry\instrumentation\opentelemetry-instrumentation-api\2.10.0\opentelemetry-instrumentation-api-2.10.0.jar;C:\Users\<USER>\.m2\repository\io\opentelemetry\opentelemetry-api-incubator\1.44.1-alpha\opentelemetry-api-incubator-1.44.1-alpha.jar;C:\Users\<USER>\.m2\repository\io\opentelemetry\instrumentation\opentelemetry-instrumentation-api-incubator\2.10.0-alpha\opentelemetry-instrumentation-api-incubator-2.10.0-alpha.jar;C:\Users\<USER>\.m2\repository\javax\validation\validation-api\2.0.1.Final\validation-api-2.0.1.Final.jar;C:\Users\<USER>\.m2\repository\io\opentelemetry\instrumentation\opentelemetry-instrumentation-annotations-support\2.10.0-alpha\opentelemetry-instrumentation-annotations-support-2.10.0-alpha.jar;C:\Users\<USER>\.m2\repository\io\opentelemetry\semconv\opentelemetry-semconv-incubating\1.28.0-alpha\opentelemetry-semconv-incubating-1.28.0-alpha.jar;C:\Users\<USER>\.m2\repository\io\opentelemetry\instrumentation\opentelemetry-kafka-clients-2.6\2.10.0-alpha\opentelemetry-kafka-clients-2.6-2.10.0-alpha.jar;C:\Users\<USER>\.m2\repository\io\opentelemetry\instrumentation\opentelemetry-kafka-clients-common\2.10.0-alpha\opentelemetry-kafka-clients-common-2.10.0-alpha.jar;C:\Users\<USER>\.m2\repository\io\opentelemetry\instrumentation\opentelemetry-mongo-3.1\2.10.0-alpha\opentelemetry-mongo-3.1-2.10.0-alpha.jar;C:\Users\<USER>\.m2\repository\io\opentelemetry\instrumentation\opentelemetry-spring-kafka-2.7\2.10.0-alpha\opentelemetry-spring-kafka-2.7-2.10.0-alpha.jar;C:\Users\<USER>\.m2\repository\io\opentelemetry\instrumentation\opentelemetry-spring-web-3.1\2.10.0-alpha\opentelemetry-spring-web-3.1-2.10.0-alpha.jar;C:\Users\<USER>\.m2\repository\io\opentelemetry\instrumentation\opentelemetry-spring-webmvc-5.3\2.10.0-alpha\opentelemetry-spring-webmvc-5.3-2.10.0-alpha.jar;C:\Users\<USER>\.m2\repository\io\opentelemetry\instrumentation\opentelemetry-spring-webflux-5.3\2.10.0-alpha\opentelemetry-spring-webflux-5.3-2.10.0-alpha.jar;C:\Users\<USER>\.m2\repository\io\opentelemetry\instrumentation\opentelemetry-reactor-3.1\2.10.0-alpha\opentelemetry-reactor-3.1-2.10.0-alpha.jar;C:\Users\<USER>\.m2\repository\io\opentelemetry\instrumentation\opentelemetry-micrometer-1.5\2.10.0-alpha\opentelemetry-micrometer-1.5-2.10.0-alpha.jar;C:\Users\<USER>\.m2\repository\io\opentelemetry\instrumentation\opentelemetry-log4j-appender-2.17\2.10.0-alpha\opentelemetry-log4j-appender-2.17-2.10.0-alpha.jar;C:\Users\<USER>\.m2\repository\io\opentelemetry\instrumentation\opentelemetry-log4j-context-data-2.17-autoconfigure\2.10.0-alpha\opentelemetry-log4j-context-data-2.17-autoconfigure-2.10.0-alpha.jar;C:\Users\<USER>\.m2\repository\io\opentelemetry\instrumentation\opentelemetry-logback-appender-1.0\2.10.0-alpha\opentelemetry-logback-appender-1.0-2.10.0-alpha.jar;C:\Users\<USER>\.m2\repository\io\opentelemetry\instrumentation\opentelemetry-logback-mdc-1.0\2.10.0-alpha\opentelemetry-logback-mdc-1.0-2.10.0-alpha.jar;C:\Users\<USER>\.m2\repository\io\opentelemetry\instrumentation\opentelemetry-jdbc\2.10.0-alpha\opentelemetry-jdbc-2.10.0-alpha.jar;C:\Users\<USER>\.m2\repository\io\opentelemetry\opentelemetry-sdk-extension-autoconfigure\1.49.0\opentelemetry-sdk-extension-autoconfigure-1.49.0.jar;C:\Users\<USER>\.m2\repository\io\opentelemetry\instrumentation\opentelemetry-sdk-autoconfigure-support\2.10.0-alpha\opentelemetry-sdk-autoconfigure-support-2.10.0-alpha.jar;C:\Users\<USER>\.m2\repository\io\opentelemetry\instrumentation\opentelemetry-spring-webmvc-6.0\2.10.0-alpha\opentelemetry-spring-webmvc-6.0-2.10.0-alpha.jar;C:\Users\<USER>\.m2\repository\io\opentelemetry\instrumentation\opentelemetry-instrumentation-annotations\2.10.0\opentelemetry-instrumentation-annotations-2.10.0.jar;C:\Users\<USER>\.m2\repository\io\opentelemetry\opentelemetry-sdk-extension-autoconfigure-spi\1.49.0\opentelemetry-sdk-extension-autoconfigure-spi-1.49.0.jar;C:\Users\<USER>\.m2\repository\io\opentelemetry\opentelemetry-api\1.49.0\opentelemetry-api-1.49.0.jar;C:\Users\<USER>\.m2\repository\io\opentelemetry\opentelemetry-exporter-logging\1.49.0\opentelemetry-exporter-logging-1.49.0.jar;C:\Users\<USER>\.m2\repository\io\opentelemetry\opentelemetry-exporter-otlp\1.49.0\opentelemetry-exporter-otlp-1.49.0.jar;C:\Users\<USER>\.m2\repository\io\opentelemetry\opentelemetry-sdk-trace\1.49.0\opentelemetry-sdk-trace-1.49.0.jar;C:\Users\<USER>\.m2\repository\io\opentelemetry\opentelemetry-sdk-metrics\1.49.0\opentelemetry-sdk-metrics-1.49.0.jar;C:\Users\<USER>\.m2\repository\io\opentelemetry\opentelemetry-sdk-logs\1.49.0\opentelemetry-sdk-logs-1.49.0.jar;C:\Users\<USER>\.m2\repository\io\opentelemetry\opentelemetry-exporter-otlp-common\1.49.0\opentelemetry-exporter-otlp-common-1.49.0.jar;C:\Users\<USER>\.m2\repository\io\opentelemetry\opentelemetry-exporter-sender-okhttp\1.49.0\opentelemetry-exporter-sender-okhttp-1.49.0.jar;C:\Users\<USER>\.m2\repository\io\opentelemetry\opentelemetry-sdk\1.49.0\opentelemetry-sdk-1.49.0.jar;C:\Users\<USER>\.m2\repository\io\opentelemetry\opentelemetry-sdk-common\1.49.0\opentelemetry-sdk-common-1.49.0.jar;C:\Users\<USER>\.m2\repository\io\opentelemetry\instrumentation\opentelemetry-resources\2.10.0-alpha\opentelemetry-resources-2.10.0-alpha.jar;C:\Users\<USER>\.m2\repository\io\opentelemetry\semconv\opentelemetry-semconv\1.28.0-alpha\opentelemetry-semconv-1.28.0-alpha.jar;C:\Users\<USER>\.m2\repository\io\opentelemetry\opentelemetry-sdk-extension-incubator\1.44.1-alpha\opentelemetry-sdk-extension-incubator-1.44.1-alpha.jar;C:\Users\<USER>\.m2\repository\org\snakeyaml\snakeyaml-engine\2.8\snakeyaml-engine-2.8.jar;C:\Users\<USER>\.m2\repository\com\fasterxml\jackson\dataformat\jackson-dataformat-yaml\2.19.0\jackson-dataformat-yaml-2.19.0.jar;C:\Users\<USER>\.m2\repository\io\opentelemetry\contrib\opentelemetry-aws-resources\1.40.0-alpha\opentelemetry-aws-resources-1.40.0-alpha.jar;C:\Users\<USER>\.m2\repository\io\opentelemetry\contrib\opentelemetry-gcp-resources\1.40.0-alpha\opentelemetry-gcp-resources-1.40.0-alpha.jar;C:\Users\<USER>\.m2\repository\com\google\cloud\opentelemetry\detector-resources-support\0.33.0\detector-resources-support-0.33.0.jar;C:\Users\<USER>\.m2\repository\io\opentelemetry\contrib\opentelemetry-baggage-processor\1.40.0-alpha\opentelemetry-baggage-processor-1.40.0-alpha.jar;C:\Users\<USER>\.m2\repository\io\opentelemetry\opentelemetry-exporter-zipkin\1.44.1\opentelemetry-exporter-zipkin-1.44.1.jar;C:\Users\<USER>\.m2\repository\io\opentelemetry\opentelemetry-exporter-common\1.49.0\opentelemetry-exporter-common-1.49.0.jar;C:\Users\<USER>\.m2\repository\io\zipkin\reporter2\zipkin-sender-okhttp3\3.5.0\zipkin-sender-okhttp3-3.5.0.jar;C:\Users\<USER>\.m2\repository\io\zipkin\zipkin2\zipkin\2.27.1\zipkin-2.27.1.jar;C:\Users\<USER>\.m2\repository\org\springframework\boot\spring-boot-starter-aop\3.5.0\spring-boot-starter-aop-3.5.0.jar;C:\Users\<USER>\.m2\repository\org\aspectj\aspectjweaver\1.9.24\aspectjweaver-1.9.24.jar;C:\Users\<USER>\.m2\repository\com\fasterxml\jackson\core\jackson-core\2.19.0\jackson-core-2.19.0.jar;C:\Users\<USER>\.m2\repository\com\fasterxml\jackson\core\jackson-databind\2.19.0\jackson-databind-2.19.0.jar;C:\Users\<USER>\.m2\repository\com\fasterxml\jackson\core\jackson-annotations\2.19.0\jackson-annotations-2.19.0.jar;C:\Users\<USER>\.m2\repository\com\fasterxml\jackson\datatype\jackson-datatype-jsr310\2.19.0\jackson-datatype-jsr310-2.19.0.jar;C:\Users\<USER>\.m2\repository\org\projectlombok\lombok\1.18.38\lombok-1.18.38.jar;C:\Users\<USER>\.m2\repository\org\springframework\boot\spring-boot-starter-test\3.5.0\spring-boot-starter-test-3.5.0.jar;C:\Users\<USER>\.m2\repository\org\springframework\boot\spring-boot-test\3.5.0\spring-boot-test-3.5.0.jar;C:\Users\<USER>\.m2\repository\org\springframework\boot\spring-boot-test-autoconfigure\3.5.0\spring-boot-test-autoconfigure-3.5.0.jar;C:\Users\<USER>\.m2\repository\com\jayway\jsonpath\json-path\2.9.0\json-path-2.9.0.jar;C:\Users\<USER>\.m2\repository\jakarta\xml\bind\jakarta.xml.bind-api\4.0.2\jakarta.xml.bind-api-4.0.2.jar;C:\Users\<USER>\.m2\repository\jakarta\activation\jakarta.activation-api\2.1.3\jakarta.activation-api-2.1.3.jar;C:\Users\<USER>\.m2\repository\net\minidev\json-smart\2.5.2\json-smart-2.5.2.jar;C:\Users\<USER>\.m2\repository\net\minidev\accessors-smart\2.5.2\accessors-smart-2.5.2.jar;C:\Users\<USER>\.m2\repository\org\assertj\assertj-core\3.27.3\assertj-core-3.27.3.jar;C:\Users\<USER>\.m2\repository\org\hamcrest\hamcrest\3.0\hamcrest-3.0.jar;C:\Users\<USER>\.m2\repository\org\junit\jupiter\junit-jupiter\5.12.2\junit-jupiter-5.12.2.jar;C:\Users\<USER>\.m2\repository\org\junit\jupiter\junit-jupiter-api\5.12.2\junit-jupiter-api-5.12.2.jar;C:\Users\<USER>\.m2\repository\org\junit\jupiter\junit-jupiter-params\5.12.2\junit-jupiter-params-5.12.2.jar;C:\Users\<USER>\.m2\repository\org\junit\jupiter\junit-jupiter-engine\5.12.2\junit-jupiter-engine-5.12.2.jar;C:\Users\<USER>\.m2\repository\org\mockito\mockito-core\5.17.0\mockito-core-5.17.0.jar;C:\Users\<USER>\.m2\repository\net\bytebuddy\byte-buddy-agent\1.17.5\byte-buddy-agent-1.17.5.jar;C:\Users\<USER>\.m2\repository\org\objenesis\objenesis\3.3\objenesis-3.3.jar;C:\Users\<USER>\.m2\repository\org\mockito\mockito-junit-jupiter\5.17.0\mockito-junit-jupiter-5.17.0.jar;C:\Users\<USER>\.m2\repository\org\skyscreamer\jsonassert\1.5.3\jsonassert-1.5.3.jar;C:\Users\<USER>\.m2\repository\com\vaadin\external\google\android-json\0.0.20131108.vaadin1\android-json-0.0.20131108.vaadin1.jar;C:\Users\<USER>\.m2\repository\org\springframework\spring-core\6.2.7\spring-core-6.2.7.jar;C:\Users\<USER>\.m2\repository\org\springframework\spring-jcl\6.2.7\spring-jcl-6.2.7.jar;C:\Users\<USER>\.m2\repository\org\springframework\spring-test\6.2.7\spring-test-6.2.7.jar;C:\Users\<USER>\.m2\repository\org\xmlunit\xmlunit-core\2.10.1\xmlunit-core-2.10.1.jar;C:\Users\<USER>\.m2\repository\org\springframework\graphql\spring-graphql-test\1.4.0\spring-graphql-test-1.4.0.jar;C:\Users\<USER>\.m2\repository\org\springframework\spring-context\6.2.7\spring-context-6.2.7.jar;C:\Users\<USER>\.m2\repository\org\testcontainers\junit-jupiter\1.21.0\junit-jupiter-1.21.0.jar;C:\Users\<USER>\.m2\repository\org\testcontainers\testcontainers\1.21.0\testcontainers-1.21.0.jar;C:\Users\<USER>\.m2\repository\junit\junit\4.13.2\junit-4.13.2.jar;C:\Users\<USER>\.m2\repository\org\rnorth\duct-tape\duct-tape\1.0.8\duct-tape-1.0.8.jar;C:\Users\<USER>\.m2\repository\org\jetbrains\annotations\17.0.0\annotations-17.0.0.jar;C:\Users\<USER>\.m2\repository\com\github\docker-java\docker-java-api\3.4.2\docker-java-api-3.4.2.jar;C:\Users\<USER>\.m2\repository\com\github\docker-java\docker-java-transport-zerodep\3.4.2\docker-java-transport-zerodep-3.4.2.jar;C:\Users\<USER>\.m2\repository\com\github\docker-java\docker-java-transport\3.4.2\docker-java-transport-3.4.2.jar;C:\Users\<USER>\.m2\repository\org\testcontainers\mysql\1.21.0\mysql-1.21.0.jar;C:\Users\<USER>\.m2\repository\org\testcontainers\jdbc\1.21.0\jdbc-1.21.0.jar;C:\Users\<USER>\.m2\repository\org\testcontainers\database-commons\1.21.0\database-commons-1.21.0.jar;C:\Users\<USER>\.m2\repository\com\h2database\h2\2.3.232\h2-2.3.232.jar;C:\Users\<USER>\.m2\repository\org\awaitility\awaitility\4.3.0\awaitility-4.3.0.jar;C:\Users\<USER>\.m2\repository\com\github\tomakehurst\wiremock-jre8\2.35.0\wiremock-jre8-2.35.0.jar;C:\Users\<USER>\.m2\repository\org\eclipse\jetty\jetty-server\12.0.21\jetty-server-12.0.21.jar;C:\Users\<USER>\.m2\repository\org\eclipse\jetty\jetty-http\12.0.21\jetty-http-12.0.21.jar;C:\Users\<USER>\.m2\repository\org\eclipse\jetty\jetty-io\12.0.21\jetty-io-12.0.21.jar;C:\Users\<USER>\.m2\repository\org\eclipse\jetty\jetty-servlet\9.4.49.v20220914\jetty-servlet-9.4.49.v20220914.jar;C:\Users\<USER>\.m2\repository\org\eclipse\jetty\jetty-security\12.0.21\jetty-security-12.0.21.jar;C:\Users\<USER>\.m2\repository\org\eclipse\jetty\jetty-util-ajax\12.0.21\jetty-util-ajax-12.0.21.jar;C:\Users\<USER>\.m2\repository\org\eclipse\jetty\jetty-servlets\9.4.49.v20220914\jetty-servlets-9.4.49.v20220914.jar;C:\Users\<USER>\.m2\repository\org\eclipse\jetty\jetty-continuation\9.4.49.v20220914\jetty-continuation-9.4.49.v20220914.jar;C:\Users\<USER>\.m2\repository\org\eclipse\jetty\jetty-util\12.0.21\jetty-util-12.0.21.jar;C:\Users\<USER>\.m2\repository\org\eclipse\jetty\jetty-webapp\9.4.49.v20220914\jetty-webapp-9.4.49.v20220914.jar;C:\Users\<USER>\.m2\repository\org\eclipse\jetty\jetty-xml\12.0.21\jetty-xml-12.0.21.jar;C:\Users\<USER>\.m2\repository\org\eclipse\jetty\jetty-proxy\12.0.21\jetty-proxy-12.0.21.jar;C:\Users\<USER>\.m2\repository\org\eclipse\jetty\jetty-client\12.0.21\jetty-client-12.0.21.jar;C:\Users\<USER>\.m2\repository\org\eclipse\jetty\http2\http2-server\9.4.49.v20220914\http2-server-9.4.49.v20220914.jar;C:\Users\<USER>\.m2\repository\org\eclipse\jetty\http2\http2-common\9.4.49.v20220914\http2-common-9.4.49.v20220914.jar;C:\Users\<USER>\.m2\repository\org\eclipse\jetty\http2\http2-hpack\9.4.49.v20220914\http2-hpack-9.4.49.v20220914.jar;C:\Users\<USER>\.m2\repository\org\eclipse\jetty\jetty-alpn-server\12.0.21\jetty-alpn-server-12.0.21.jar;C:\Users\<USER>\.m2\repository\org\eclipse\jetty\jetty-alpn-java-server\12.0.21\jetty-alpn-java-server-12.0.21.jar;C:\Users\<USER>\.m2\repository\org\eclipse\jetty\jetty-alpn-openjdk8-server\9.4.49.v20220914\jetty-alpn-openjdk8-server-9.4.49.v20220914.jar;C:\Users\<USER>\.m2\repository\org\eclipse\jetty\jetty-alpn-java-client\12.0.21\jetty-alpn-java-client-12.0.21.jar;C:\Users\<USER>\.m2\repository\org\eclipse\jetty\jetty-alpn-client\12.0.21\jetty-alpn-client-12.0.21.jar;C:\Users\<USER>\.m2\repository\org\eclipse\jetty\jetty-alpn-openjdk8-client\9.4.49.v20220914\jetty-alpn-openjdk8-client-9.4.49.v20220914.jar;C:\Users\<USER>\.m2\repository\com\google\guava\guava\31.1-jre\guava-31.1-jre.jar;C:\Users\<USER>\.m2\repository\com\google\guava\failureaccess\1.0.1\failureaccess-1.0.1.jar;C:\Users\<USER>\.m2\repository\com\google\guava\listenablefuture\9999.0-empty-to-avoid-conflict-with-guava\listenablefuture-9999.0-empty-to-avoid-conflict-with-guava.jar;C:\Users\<USER>\.m2\repository\com\google\code\findbugs\jsr305\3.0.2\jsr305-3.0.2.jar;C:\Users\<USER>\.m2\repository\org\checkerframework\checker-qual\3.12.0\checker-qual-3.12.0.jar;C:\Users\<USER>\.m2\repository\com\google\errorprone\error_prone_annotations\2.11.0\error_prone_annotations-2.11.0.jar;C:\Users\<USER>\.m2\repository\com\google\j2objc\j2objc-annotations\1.3\j2objc-annotations-1.3.jar;C:\Users\<USER>\.m2\repository\org\xmlunit\xmlunit-legacy\2.10.1\xmlunit-legacy-2.10.1.jar;C:\Users\<USER>\.m2\repository\org\xmlunit\xmlunit-placeholders\2.10.1\xmlunit-placeholders-2.10.1.jar;C:\Users\<USER>\.m2\repository\net\javacrumbs\json-unit\json-unit-core\2.36.0\json-unit-core-2.36.0.jar;C:\Users\<USER>\.m2\repository\org\hamcrest\hamcrest-core\3.0\hamcrest-core-3.0.jar;C:\Users\<USER>\.m2\repository\org\ow2\asm\asm\9.4\asm-9.4.jar;C:\Users\<USER>\.m2\repository\net\sf\jopt-simple\jopt-simple\5.0.4\jopt-simple-5.0.4.jar;C:\Users\<USER>\.m2\repository\com\github\jknack\handlebars\4.3.1\handlebars-4.3.1.jar;C:\Users\<USER>\.m2\repository\com\github\jknack\handlebars-helpers\4.3.1\handlebars-helpers-4.3.1.jar;C:\Users\<USER>\.m2\repository\commons-fileupload\commons-fileupload\1.4\commons-fileupload-1.4.jar;C:\Users\<USER>\.m2\repository\org\springframework\security\spring-security-test\6.5.0\spring-security-test-6.5.0.jar;C:\Users\<USER>\.m2\repository\org\junit\platform\junit-platform-launcher\1.12.2\junit-platform-launcher-1.12.2.jar;C:\Users\<USER>\.m2\repository\org\junit\platform\junit-platform-engine\1.12.2\junit-platform-engine-1.12.2.jar;C:\Users\<USER>\.m2\repository\org\opentest4j\opentest4j\1.3.0\opentest4j-1.3.0.jar;C:\Users\<USER>\.m2\repository\org\junit\platform\junit-platform-commons\1.12.2\junit-platform-commons-1.12.2.jar;C:\Users\<USER>\.m2\repository\org\apiguardian\apiguardian-api\1.1.2\apiguardian-api-1.1.2.jar;C:\Users\<USER>\.m2\repository\org\springframework\boot\spring-boot-starter-data-elasticsearch\3.5.0\spring-boot-starter-data-elasticsearch-3.5.0.jar;C:\Users\<USER>\.m2\repository\org\springframework\data\spring-data-elasticsearch\5.5.0\spring-data-elasticsearch-5.5.0.jar;C:\Users\<USER>\.m2\repository\co\elastic\clients\elasticsearch-java\8.18.1\elasticsearch-java-8.18.1.jar;C:\Users\<USER>\.m2\repository\jakarta\json\jakarta.json-api\2.1.3\jakarta.json-api-2.1.3.jar;C:\Users\<USER>\.m2\repository\org\eclipse\parsson\parsson\1.0.5\parsson-1.0.5.jar;C:\Users\<USER>\.m2\repository\org\elasticsearch\client\elasticsearch-rest-client\8.18.1\elasticsearch-rest-client-8.18.1.jar;C:\Users\<USER>\.m2\repository\org\apache\httpcomponents\httpasyncclient\4.1.5\httpasyncclient-4.1.5.jar;C:\Users\<USER>\.m2\repository\org\apache\httpcomponents\httpcore-nio\4.4.16\httpcore-nio-4.4.16.jar;C:\Users\<USER>\.m2\repository\org\apache\tika\tika-core\2.9.1\tika-core-2.9.1.jar;C:\Users\<USER>\.m2\repository\org\apache\tika\tika-parsers-standard-package\2.9.1\tika-parsers-standard-package-2.9.1.jar;C:\Users\<USER>\.m2\repository\org\apache\tika\tika-parser-apple-module\2.9.1\tika-parser-apple-module-2.9.1.jar;C:\Users\<USER>\.m2\repository\org\apache\tika\tika-parser-zip-commons\2.9.1\tika-parser-zip-commons-2.9.1.jar;C:\Users\<USER>\.m2\repository\com\googlecode\plist\dd-plist\1.27\dd-plist-1.27.jar;C:\Users\<USER>\.m2\repository\org\apache\tika\tika-parser-audiovideo-module\2.9.1\tika-parser-audiovideo-module-2.9.1.jar;C:\Users\<USER>\.m2\repository\com\drewnoakes\metadata-extractor\2.18.0\metadata-extractor-2.18.0.jar;C:\Users\<USER>\.m2\repository\com\adobe\xmp\xmpcore\6.1.11\xmpcore-6.1.11.jar;C:\Users\<USER>\.m2\repository\org\apache\tika\tika-parser-cad-module\2.9.1\tika-parser-cad-module-2.9.1.jar;C:\Users\<USER>\.m2\repository\org\apache\tika\tika-parser-code-module\2.9.1\tika-parser-code-module-2.9.1.jar;C:\Users\<USER>\.m2\repository\org\codelibs\jhighlight\1.1.0\jhighlight-1.1.0.jar;C:\Users\<USER>\.m2\repository\org\ccil\cowan\tagsoup\tagsoup\1.2.1\tagsoup-1.2.1.jar;C:\Users\<USER>\.m2\repository\com\epam\parso\2.0.14\parso-2.0.14.jar;C:\Users\<USER>\.m2\repository\org\tallison\jmatio\1.5\jmatio-1.5.jar;C:\Users\<USER>\.m2\repository\org\apache\tika\tika-parser-crypto-module\2.9.1\tika-parser-crypto-module-2.9.1.jar;C:\Users\<USER>\.m2\repository\org\bouncycastle\bcmail-jdk18on\1.76\bcmail-jdk18on-1.76.jar;C:\Users\<USER>\.m2\repository\org\bouncycastle\bcprov-jdk18on\1.76\bcprov-jdk18on-1.76.jar;C:\Users\<USER>\.m2\repository\org\apache\tika\tika-parser-digest-commons\2.9.1\tika-parser-digest-commons-2.9.1.jar;C:\Users\<USER>\.m2\repository\org\apache\tika\tika-parser-font-module\2.9.1\tika-parser-font-module-2.9.1.jar;C:\Users\<USER>\.m2\repository\org\apache\tika\tika-parser-html-module\2.9.1\tika-parser-html-module-2.9.1.jar;C:\Users\<USER>\.m2\repository\org\apache\tika\tika-parser-image-module\2.9.1\tika-parser-image-module-2.9.1.jar;C:\Users\<USER>\.m2\repository\com\github\jai-imageio\jai-imageio-core\1.4.0\jai-imageio-core-1.4.0.jar;C:\Users\<USER>\.m2\repository\org\apache\pdfbox\jbig2-imageio\3.0.4\jbig2-imageio-3.0.4.jar;C:\Users\<USER>\.m2\repository\org\apache\tika\tika-parser-mail-module\2.9.1\tika-parser-mail-module-2.9.1.jar;C:\Users\<USER>\.m2\repository\org\apache\tika\tika-parser-mail-commons\2.9.1\tika-parser-mail-commons-2.9.1.jar;C:\Users\<USER>\.m2\repository\org\apache\james\apache-mime4j-core\0.8.9\apache-mime4j-core-0.8.9.jar;C:\Users\<USER>\.m2\repository\org\apache\james\apache-mime4j-dom\0.8.9\apache-mime4j-dom-0.8.9.jar;C:\Users\<USER>\.m2\repository\org\apache\tika\tika-parser-microsoft-module\2.9.1\tika-parser-microsoft-module-2.9.1.jar;C:\Users\<USER>\.m2\repository\com\pff\java-libpst\0.9.3\java-libpst-0.9.3.jar;C:\Users\<USER>\.m2\repository\com\healthmarketscience\jackcess\jackcess\4.0.5\jackcess-4.0.5.jar;C:\Users\<USER>\.m2\repository\com\healthmarketscience\jackcess\jackcess-encrypt\4.0.2\jackcess-encrypt-4.0.2.jar;C:\Users\<USER>\.m2\repository\org\slf4j\jcl-over-slf4j\2.0.17\jcl-over-slf4j-2.0.17.jar;C:\Users\<USER>\.m2\repository\org\apache\tika\tika-parser-miscoffice-module\2.9.1\tika-parser-miscoffice-module-2.9.1.jar;C:\Users\<USER>\.m2\repository\org\apache\tika\tika-parser-news-module\2.9.1\tika-parser-news-module-2.9.1.jar;C:\Users\<USER>\.m2\repository\com\rometools\rome\2.1.0\rome-2.1.0.jar;C:\Users\<USER>\.m2\repository\com\rometools\rome-utils\2.1.0\rome-utils-2.1.0.jar;C:\Users\<USER>\.m2\repository\org\jdom\jdom2\2.0.6.1\jdom2-2.0.6.1.jar;C:\Users\<USER>\.m2\repository\org\apache\tika\tika-parser-ocr-module\2.9.1\tika-parser-ocr-module-2.9.1.jar;C:\Users\<USER>\.m2\repository\org\apache\commons\commons-exec\1.3\commons-exec-1.3.jar;C:\Users\<USER>\.m2\repository\org\apache\tika\tika-parser-pdf-module\2.9.1\tika-parser-pdf-module-2.9.1.jar;C:\Users\<USER>\.m2\repository\org\apache\pdfbox\pdfbox-tools\2.0.29\pdfbox-tools-2.0.29.jar;C:\Users\<USER>\.m2\repository\org\apache\pdfbox\jempbox\1.8.17\jempbox-1.8.17.jar;C:\Users\<USER>\.m2\repository\org\apache\tika\tika-parser-pkg-module\2.9.1\tika-parser-pkg-module-2.9.1.jar;C:\Users\<USER>\.m2\repository\org\tukaani\xz\1.9\xz-1.9.jar;C:\Users\<USER>\.m2\repository\org\brotli\dec\0.1.2\dec-0.1.2.jar;C:\Users\<USER>\.m2\repository\com\github\junrar\junrar\7.5.5\junrar-7.5.5.jar;C:\Users\<USER>\.m2\repository\org\apache\tika\tika-parser-text-module\2.9.1\tika-parser-text-module-2.9.1.jar;C:\Users\<USER>\.m2\repository\com\github\albfernandez\juniversalchardet\2.4.0\juniversalchardet-2.4.0.jar;C:\Users\<USER>\.m2\repository\org\apache\commons\commons-csv\1.10.0\commons-csv-1.10.0.jar;C:\Users\<USER>\.m2\repository\org\apache\tika\tika-parser-webarchive-module\2.9.1\tika-parser-webarchive-module-2.9.1.jar;C:\Users\<USER>\.m2\repository\org\netpreserve\jwarc\0.28.3\jwarc-0.28.3.jar;C:\Users\<USER>\.m2\repository\org\apache\tika\tika-parser-xml-module\2.9.1\tika-parser-xml-module-2.9.1.jar;C:\Users\<USER>\.m2\repository\xerces\xercesImpl\2.12.2\xercesImpl-2.12.2.jar;C:\Users\<USER>\.m2\repository\xml-apis\xml-apis\1.4.01\xml-apis-1.4.01.jar;C:\Users\<USER>\.m2\repository\org\apache\tika\tika-parser-xmp-commons\2.9.1\tika-parser-xmp-commons-2.9.1.jar;C:\Users\<USER>\.m2\repository\org\apache\pdfbox\xmpbox\2.0.29\xmpbox-2.0.29.jar;C:\Users\<USER>\.m2\repository\org\gagravarr\vorbis-java-tika\0.8\vorbis-java-tika-0.8.jar;C:\Users\<USER>\.m2\repository\org\gagravarr\vorbis-java-core\0.8\vorbis-java-core-0.8.jar;C:\Users\<USER>\.m2\repository\org\apache\pdfbox\pdfbox\3.0.1\pdfbox-3.0.1.jar;C:\Users\<USER>\.m2\repository\org\apache\pdfbox\pdfbox-io\3.0.1\pdfbox-io-3.0.1.jar;C:\Users\<USER>\.m2\repository\org\apache\pdfbox\fontbox\3.0.1\fontbox-3.0.1.jar;C:\Users\<USER>\.m2\repository\commons-logging\commons-logging\1.2\commons-logging-1.2.jar;C:\Users\<USER>\.m2\repository\com\itextpdf\itextpdf\5.5.13.3\itextpdf-5.5.13.3.jar;C:\Users\<USER>\.m2\repository\org\apache\poi\poi\5.2.5\poi-5.2.5.jar;C:\Users\<USER>\.m2\repository\commons-codec\commons-codec\1.18.0\commons-codec-1.18.0.jar;C:\Users\<USER>\.m2\repository\org\apache\commons\commons-math3\3.6.1\commons-math3-3.6.1.jar;C:\Users\<USER>\.m2\repository\com\zaxxer\SparseBitSet\1.3\SparseBitSet-1.3.jar;C:\Users\<USER>\.m2\repository\org\apache\logging\log4j\log4j-api\2.24.3\log4j-api-2.24.3.jar;C:\Users\<USER>\.m2\repository\org\apache\poi\poi-ooxml\5.2.5\poi-ooxml-5.2.5.jar;C:\Users\<USER>\.m2\repository\org\apache\poi\poi-ooxml-lite\5.2.5\poi-ooxml-lite-5.2.5.jar;C:\Users\<USER>\.m2\repository\org\apache\xmlbeans\xmlbeans\5.2.0\xmlbeans-5.2.0.jar;C:\Users\<USER>\.m2\repository\org\apache\commons\commons-compress\1.25.0\commons-compress-1.25.0.jar;C:\Users\<USER>\.m2\repository\com\github\virtuald\curvesapi\1.08\curvesapi-1.08.jar;C:\Users\<USER>\.m2\repository\org\apache\poi\poi-scratchpad\5.2.5\poi-scratchpad-5.2.5.jar;C:\Users\<USER>\.m2\repository\org\testcontainers\elasticsearch\1.21.0\elasticsearch-1.21.0.jar;C:\Users\<USER>\.m2\repository\io\kubernetes\client-java\19.0.0\client-java-19.0.0.jar;C:\Users\<USER>\.m2\repository\io\prometheus\simpleclient\0.16.0\simpleclient-0.16.0.jar;C:\Users\<USER>\.m2\repository\io\prometheus\simpleclient_tracer_otel\0.16.0\simpleclient_tracer_otel-0.16.0.jar;C:\Users\<USER>\.m2\repository\io\prometheus\simpleclient_tracer_common\0.16.0\simpleclient_tracer_common-0.16.0.jar;C:\Users\<USER>\.m2\repository\io\prometheus\simpleclient_tracer_otel_agent\0.16.0\simpleclient_tracer_otel_agent-0.16.0.jar;C:\Users\<USER>\.m2\repository\io\prometheus\simpleclient_httpserver\0.16.0\simpleclient_httpserver-0.16.0.jar;C:\Users\<USER>\.m2\repository\io\prometheus\simpleclient_common\0.16.0\simpleclient_common-0.16.0.jar;C:\Users\<USER>\.m2\repository\io\kubernetes\client-java-api\19.0.0\client-java-api-19.0.0.jar;C:\Users\<USER>\.m2\repository\javax\annotation\javax.annotation-api\1.3.2\javax.annotation-api-1.3.2.jar;C:\Users\<USER>\.m2\repository\io\swagger\swagger-annotations\1.6.11\swagger-annotations-1.6.11.jar;C:\Users\<USER>\.m2\repository\com\squareup\okhttp3\logging-interceptor\4.11.0\logging-interceptor-4.11.0.jar;C:\Users\<USER>\.m2\repository\io\gsonfire\gson-fire\1.8.5\gson-fire-1.8.5.jar;C:\Users\<USER>\.m2\repository\io\kubernetes\client-java-proto\19.0.0\client-java-proto-19.0.0.jar;C:\Users\<USER>\.m2\repository\org\bouncycastle\bcpkix-jdk18on\1.76\bcpkix-jdk18on-1.76.jar;C:\Users\<USER>\.m2\repository\org\bouncycastle\bcutil-jdk18on\1.76\bcutil-jdk18on-1.76.jar;C:\Users\<USER>\.m2\repository\com\google\protobuf\protobuf-java\3.24.3\protobuf-java-3.24.3.jar;C:\Users\<USER>\.m2\repository\org\bitbucket\b_c\jose4j\0.9.3\jose4j-0.9.3.jar;C:\Users\<USER>\.m2\repository\org\apache\httpcomponents\client5\httpclient5\5.4.4\httpclient5-5.4.4.jar;C:\Users\<USER>\.m2\repository\org\apache\httpcomponents\core5\httpcore5\5.3.4\httpcore5-5.3.4.jar;C:\Users\<USER>\.m2\repository\org\apache\httpcomponents\core5\httpcore5-h2\5.3.4\httpcore5-h2-5.3.4.jar;C:\Users\<USER>\.m2\repository\org\yaml\snakeyaml\2.4\snakeyaml-2.4.jar;"/>
    <property name="sun.cpu.endian" value="little"/>
    <property name="user.home" value="C:\Users\<USER>\Program Files\Java\jdk-21"/>
    <property name="spring.profiles.active" value="test"/>
    <property name="file.separator" value="\"/>
    <property name="basedir" value="D:\grc-platform-v4\dms-svc"/>
    <property name="java.vm.compressedOopsMode" value="Zero based"/>
    <property name="line.separator" value="&#10;"/>
    <property name="java.vm.specification.vendor" value="Oracle Corporation"/>
    <property name="java.specification.name" value="Java Platform API Specification"/>
    <property name="FILE_LOG_CHARSET" value="UTF-8"/>
    <property name="java.awt.headless" value="true"/>
    <property name="surefire.real.class.path" value="C:\Users\<USER>\AppData\Local\Temp\surefire7646089020984470424\surefirebooter-20250722231242388_3.jar"/>
    <property name="jdk.net.URLClassPath.disableClassPathURLCheck" value="true"/>
    <property name="user.script" value=""/>
    <property name="sun.management.compiler" value="HotSpot 64-Bit Tiered Compilers"/>
    <property name="jdk.instrument.traceUsage" value="false"/>
    <property name="java.runtime.version" value="21.0.7+8-LTS-245"/>
    <property name="user.name" value="AnuragVerma"/>
    <property name="stdout.encoding" value="Cp1252"/>
    <property name="path.separator" value=";"/>
    <property name="os.version" value="10.0"/>
    <property name="java.runtime.name" value="Java(TM) SE Runtime Environment"/>
    <property name="file.encoding" value="UTF-8"/>
    <property name="FILE_LOG_PATTERN" value="%d{yyyy-MM-dd HH:mm:ss.SSS} [%thread] %-5level [%X{correlationId}] %logger{36} - %msg%n"/>
    <property name="java.vm.name" value="Java HotSpot(TM) 64-Bit Server VM"/>
    <property name="localRepository" value="C:\Users\<USER>\.m2\repository"/>
    <property name="java.vendor.url.bug" value="https://bugreport.java.com/bugreport/"/>
    <property name="java.io.tmpdir" value="C:\Users\<USER>\AppData\Local\Temp\"/>
    <property name="catalina.home" value="C:\Users\<USER>\AppData\Local\Temp\tomcat.0.16411085993272287150"/>
    <property name="java.version" value="21.0.7"/>
    <property name="user.dir" value="D:\grc-platform-v4\dms-svc"/>
    <property name="os.arch" value="amd64"/>
    <property name="java.vm.specification.name" value="Java Virtual Machine Specification"/>
    <property name="PID" value="21460"/>
    <property name="sun.os.patch.level" value=""/>
    <property name="CONSOLE_LOG_CHARSET" value="UTF-8"/>
    <property name="catalina.base" value="C:\Users\<USER>\AppData\Local\Temp\tomcat.0.16411085993272287150"/>
    <property name="native.encoding" value="Cp1252"/>
    <property name="java.library.path" value="C:\Program Files\Java\jdk-21\bin;C:\Windows\Sun\Java\bin;C:\Windows\system32;C:\Windows;C:\Python313\Scripts\;C:\Python313\;C:\Windows\system32;C:\Windows;C:\Windows\System32\Wbem;C:\Windows\System32\WindowsPowerShell\v1.0\;C:\Windows\System32\OpenSSH\;C:\Program Files\dotnet\;c:\Users\<USER>\AppData\Local\Programs\cursor\resources\app\bin;C:\ProgramData\chocolatey\bin;C:\Program Files\Git\cmd;C:\Program Files\nodejs\;C:\Program Files\Docker\Docker\resources\bin;C:\Program Files\Amazon\AWSCLIV2\;C:\Program Files\Redis\;C:\Program Files\Pandoc\;C:\Program Files (x86)\Microsoft SQL Server\160\DTS\Binn\;.;C:\Program Files\Java\jdk-21\bin;C:\Users\<USER>\AppData\Roaming\Python\Python313\Scripts;C:\Users\<USER>\AppData\Local\Programs\cursor\resources\app\bin;C:\Program Files\RabbitMQ Server\rabbitmq_server-4.1.0\sbin;C:\Users\<USER>\AppData\Local\Programs\Microsoft VS Code\bin;C:\Program Files\apache-maven-3.9.9\bin;C:\texlive\2025\bin\windows;c:\Users\<USER>\AppData\Roaming\Code\User\globalStorage\github.copilot-chat\debugCommand;."/>
    <property name="java.vm.info" value="mixed mode, sharing"/>
    <property name="stderr.encoding" value="Cp1252"/>
    <property name="java.vendor" value="Oracle Corporation"/>
    <property name="java.vm.version" value="21.0.7+8-LTS-245"/>
    <property name="sun.io.unicode.encoding" value="UnicodeLittle"/>
    <property name="java.class.version" value="65.0"/>
    <property name="CONSOLE_LOG_PATTERN" value="%d{HH:mm:ss.SSS} [%thread] %-5level [%X{correlationId}] %logger{36} - %msg%n"/>
    <property name="LOGGED_APPLICATION_NAME" value="[dms-svc] "/>
  </properties>
  <testcase name="testUploadFromPathOrUrl_FilenameExtraction" classname="com.ascentbusiness.dms_svc.integration.UrlUploadIntegrationTest" time="0.271">
    <system-out><![CDATA[23:12:45.956 [main] INFO  [] o.s.t.c.s.AnnotationConfigContextLoaderUtils - Could not detect default configuration classes for test class [com.ascentbusiness.dms_svc.integration.UrlUploadIntegrationTest]: UrlUploadIntegrationTest does not declare any static, non-private, non-final, nested classes annotated with @Configuration.
23:12:46.463 [main] INFO  [] o.s.b.t.c.SpringBootTestContextBootstrapper - Found @SpringBootConfiguration com.ascentbusiness.dms_svc.DmsSvcApplication for test class com.ascentbusiness.dms_svc.integration.UrlUploadIntegrationTest

  .   ____          _            __ _ _
 /\\ / ___'_ __ _ _(_)_ __  __ _ \ \ \ \
( ( )\___ | '_ | '_| | '_ \/ _` | \ \ \ \
 \\/  ___)| |_)| | | | | || (_| |  ) ) ) )
  '  |____| .__|_| |_|_| |_\__, | / / / /
 =========|_|==============|___/=/_/_/_/

 :: Spring Boot ::                (v3.5.0)

23:12:47.734 [background-preinit] INFO  [] o.h.validator.internal.util.Version - HV000001: Hibernate Validator 8.0.2.Final
23:12:47.869 [main] INFO  [] c.a.d.i.UrlUploadIntegrationTest - Starting UrlUploadIntegrationTest using Java 21.0.7 with PID 21460 (started by AnuragVerma in D:\grc-platform-v4\dms-svc)
23:12:47.870 [main] DEBUG [] c.a.d.i.UrlUploadIntegrationTest - Running with Spring Boot v3.5.0, Spring v6.2.7
23:12:47.872 [main] INFO  [] c.a.d.i.UrlUploadIntegrationTest - The following 1 profile is active: "test"
23:12:51.360 [main] INFO  [] o.s.d.r.c.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode
23:12:51.364 [main] INFO  [] o.s.d.r.c.RepositoryConfigurationDelegate - Bootstrapping Spring Data JPA repositories in DEFAULT mode.
23:12:51.994 [main] INFO  [] o.s.d.r.c.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 611 ms. Found 37 JPA repository interfaces.
23:12:57.441 [main] INFO  [] o.s.b.w.e.tomcat.TomcatWebServer - Tomcat initialized with port 0 (http)
23:12:57.477 [main] INFO  [] o.a.coyote.http11.Http11NioProtocol - Initializing ProtocolHandler ["http-nio-auto-1"]
23:12:57.481 [main] INFO  [] o.a.catalina.core.StandardService - Starting service [Tomcat]
23:12:57.482 [main] INFO  [] o.a.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/10.1.41]
23:12:57.760 [main] INFO  [] o.a.c.c.C.[Tomcat].[localhost].[/] - Initializing Spring embedded WebApplicationContext
23:12:57.762 [main] INFO  [] o.s.b.w.s.c.ServletWebServerApplicationContext - Root WebApplicationContext: initialization completed in 9840 ms
23:12:59.226 [main] DEBUG [] c.a.d.s.JwtAuthenticationFilter - Filter 'jwtAuthenticationFilter' configured for use
23:12:59.226 [main] DEBUG [] c.a.d.filter.CorrelationIdFilter - Filter 'correlationIdFilter' configured for use
23:12:59.228 [main] INFO  [] c.a.d.filter.SecurityHeadersFilter - SecurityHeadersFilter initialized
23:12:59.772 [main] INFO  [] o.h.jpa.internal.util.LogHelper - HHH000204: Processing PersistenceUnitInfo [name: default]
23:13:00.011 [main] INFO  [] org.hibernate.Version - HHH000412: Hibernate ORM core version 6.6.15.Final
23:13:00.133 [main] INFO  [] o.h.c.i.RegionFactoryInitiator - HHH000026: Second-level cache disabled
23:13:00.527 [main] INFO  [] o.s.o.j.p.SpringPersistenceUnitInfo - No LoadTimeWeaver setup: ignoring JPA class transformer
23:13:00.607 [main] INFO  [] com.zaxxer.hikari.HikariDataSource - DmsHikariPool - Starting...
23:13:01.537 [main] INFO  [] com.zaxxer.hikari.pool.HikariPool - DmsHikariPool - Added connection conn0: url=jdbc:h2:mem:testdb user=SA
23:13:01.543 [main] INFO  [] com.zaxxer.hikari.HikariDataSource - DmsHikariPool - Start completed.
23:13:01.616 [main] WARN  [] org.hibernate.orm.deprecation - HHH90000025: H2Dialect does not need to be specified explicitly using 'hibernate.dialect' (remove the property setting and it will be selected by default)
23:13:01.682 [main] INFO  [] o.hibernate.orm.connections.pooling - HHH10001005: Database info:
	Database JDBC URL [Connecting through datasource 'HikariDataSource (DmsHikariPool)']
	Database driver: undefined/unknown
	Database version: 2.3.232
	Autocommit mode: undefined/unknown
	Isolation level: undefined/unknown
	Minimum pool size: undefined/unknown
	Maximum pool size: undefined/unknown
23:13:07.037 [main] INFO  [] o.h.e.t.j.p.i.JtaPlatformInitiator - HHH000489: No JTA platform available (set 'hibernate.transaction.jta.platform' to enable JTA platform integration)
23:13:07.382 [main] INFO  [] o.s.o.j.LocalContainerEntityManagerFactoryBean - Initialized JPA EntityManagerFactory for persistence unit 'default'
23:13:09.049 [main] INFO  [] o.s.d.j.r.query.QueryEnhancerFactory - Hibernate is in classpath; If applicable, HQL parser will be used.
23:13:13.205 [main] INFO  [] c.a.d.config.GraphQLMonitoringConfig - GraphQL monitoring metrics initialized
23:13:13.266 [main] INFO  [] c.a.d.s.PandocConversionService - Checking Pandoc availability [null]
23:13:13.346 [main] INFO  [] c.a.d.s.PandocConversionService - Pandoc is available: pandoc ******* [null]
23:13:13.405 [main] INFO  [] c.a.d.s.virus.VirusScannerFactory - Virus scanner factory initialized with 1 scanners: [MOCK]
23:13:13.406 [main] INFO  [] c.a.d.s.virus.VirusScannerFactory - Scanner MOCK (MockVirusScanner$$SpringCGLIB$$0) - Available: true
23:13:24.395 [main] INFO  [] c.a.dms_svc.config.GraphQlConfig - Configuring GraphQL performance monitoring instrumentation with automatic metrics collection
23:13:24.400 [main] INFO  [] c.a.dms_svc.config.GraphQlConfig - Configuring GraphQL security instrumentation
23:13:24.406 [main] INFO  [] c.a.dms_svc.config.GraphQlConfig - Configuring chained GraphQL instrumentation with 3 instrumentations
23:13:26.209 [main] INFO  [] o.s.g.e.DefaultSchemaResourceGraphQlSourceBuilder - Loaded 11 resource(s) in the GraphQL schema.
23:13:27.260 [main] INFO  [] o.s.b.a.g.GraphQlAutoConfiguration - GraphQL schema inspection:
	Unmapped fields: {Document=[contentHash, checksumSha256, checksumMd5, lastAccessedDate, accessCount, documentMetrics, securityClassification], AuditLog=[metadata, entityType, entityId, oldValue, newValue, errorMessage, processingTimeMs], AdvancedSearchResult=[searchMetrics, relatedDocuments, searchSuggestions], DocumentPermission=[documentId], RetentionPolicyPage=[content, totalElements, totalPages, number, first, last], Query=[getRetentionPolicyByName, getRetentionPoliciesByScope, getActiveWorkflowForDocument, getWorkflowInstancesDueSoon, getWorkflowTask, getTasksAssignedToUser, getActiveTasksForUser, getTasksByRole, getTasksByDepartment, getOverdueTasks, getTasksDueSoon, getActiveWebhookEndpoints, getWebhookEndpointsByCreator, getWebhookEndpointsForEvent, getWebhookDelivery, getAuditExportStatus, getUserActivitySummary, searchAuditLogs, getStorageProviderDiagnostics, getStorageProviderHealth, getDocumentMetrics, getDocumentSecurityClassification, searchDocumentsByContent, getDocumentRecommendations, getDocumentAnalytics, getTemplateUsageHistory, getTemplateUsageStatistics], WorkflowStatistics=[averageCompletionTime], StatusCount=[status, count], PriorityCount=[priority, count], EventStatistics=[totalEvents, pendingEvents, completedEvents, failedEvents], EventTypeCount=[eventType, count], EventCategoryCount=[eventCategory, count], WebhookStatistics=[totalEndpoints, verifiedEndpoints, successRate], VirusScanResponse=[scanResult, scanTimestamp, isClean], ConversionTrendData=[conversionCount, successRate], FormatPopularity=[fromFormat, toFormat], ConversionJob=[estimatedCompletion, progress], CacheStatistics=[missRate, maxSize], SystemPerformance=[cpu, memory, disk, network, jvm, database, application], HealthSummary=[trends], BulkShareItem=[shareUrl], DocumentTemplate=[canEdit, canDelete, canPublish, canApprove, recentUsage, previewUrl, downloadUrl, validationStatus, validationErrors, healthScore], TemplateUsageHistory=[processingTimeMs, documentCreated], TestCaseOverview=[totalCategories, totalTestCases, categories, availableCategories], TestCaseResponse=[testSteps, expectedResults, priority, status], TestCaseCollection=[totalCount], TestCaseSummary=[totalTestCases, categoriesCount, categories], TestCaseSearchResult=[query, totalResults, testCases], TestCaseHealthStatus=[status, service, timestamp, availableCategories, categories], TracingResult=[message, traceId, spanId, timestamp, status, tags, duration], NestedSpanResult=[message, traceId, parentSpanId, childSpans, totalSpans, executionTime], Mutation=[uploadDocument, rejectDisposition, completeWorkflow, advanceWorkflowToNextStage, delegateTask, cancelTask, publishSystemEvent, retryWebhookDelivery, cancelWebhookDelivery, resolveTamperingDetection, cancelAuditExport, createAuditEntry, purgeOldAuditLogs, clearDiagnosticHistory, resetHealthMetrics, clearPerformanceHistory, optimizeSystemPerformance, cleanupExpiredSessions, cleanupFailedUploads, updateDocumentMetrics, setSecurityClassification, bulkUpdateDocuments, submitDocumentForReview, unpublishTemplate, activateTemplate, exportTemplate, importTemplate, validateTemplateContent, optimizeTemplate, recordTemplateUsage, startCustomTrace, endCustomTrace], MarkdownVirusScanResponse=[scanTimestamp], AuditVerificationResponse=[integrityHash, previousHash, verificationDetails], DocumentUploadResult=[processingJobId, chunkUploadUrl], BulkUploadResult=[results, startedAt, completedAt, errors]}
	Unmapped registrations: {Query.searchTemplatesByName=DocumentTemplateResolver#searchTemplatesByName[1 args], Query.getEventsBySourceEntity=SystemEventResolver#getEventsBySourceEntity[2 args], Query.getCurrentTracingContext=TracingGraphQLResolver#getCurrentTracingContext[0 args], Query.getWebhookEndpointsWithHighFailureRate=WebhookEndpointResolver#getWebhookEndpointsWithHighFailureRate[1 args], Query.getUnusedWebhookEndpoints=WebhookEndpointResolver#getUnusedWebhookEndpoints[1 args], Query.searchWebhookEndpoints=WebhookEndpointResolver#searchWebhookEndpoints[1 args], Query.getAllActiveWebhookEndpoints=WebhookEndpointResolver#getAllActiveWebhookEndpoints[0 args], Query.getWebhookEndpointsForEventType=WebhookEndpointResolver#getWebhookEndpointsForEventType[1 args], Query.getActiveWorkflowInstances=WorkflowInstanceResolver#getActiveWorkflowInstances[0 args], Query.getWorkflowInstancesForDefinition=WorkflowInstanceResolver#getWorkflowInstancesForDefinition[1 args], Query.getWorkflowInstancesDueWithin=WorkflowInstanceResolver#getWorkflowInstancesDueWithin[1 args], Query.getWorkflowInstances=WorkflowInstanceResolver#getWorkflowInstances[1 args], Mutation.uploadDocumentLegacy=DocumentResolver#uploadDocumentLegacy[1 args], Mutation.validateFile=DocumentResolver#validateFile[1 args], Mutation.markEventAsProcessing=SystemEventResolver#markEventAsProcessing[1 args], Mutation.createSystemEvent=SystemEventResolver#createSystemEvent[1 args], Mutation.testTracingPerformance=TracingGraphQLResolver#testTracingPerformance[1 args], Mutation.createCustomSpan=TracingGraphQLResolver#createCustomSpan[3 args], Mutation.suspendWorkflow=WorkflowInstanceResolver#suspendWorkflow[2 args], Mutation.reassignTask=WorkflowInstanceResolver#reassignTask[2 args], Mutation.resumeWorkflow=WorkflowInstanceResolver#resumeWorkflow[2 args], Document.userId=DocumentResolver#documentUserId[1 args], Document.tags=DocumentResolver#documentTags[1 args], Document.accessRoles=DocumentResolver#documentAccessRoles[1 args], Document.parentDocument=DocumentResolver#documentParentDocument[1 args], Document.currentVersion=DocumentResolver#documentCurrentVersion[1 args], Document.versions=DocumentResolver#documentVersions[1 args]}
	Unmapped arguments: {SystemEventResolver#getSystemEvents[2 args]=[filter], TestCaseGraphQLResolver#getTestCase[2 args]=[testId], TestCaseGraphQLResolver#searchTestCases[1 args]=[query], WorkflowInstanceResolver#cancelWorkflow[2 args]=[reason, workflowInstanceId]}
	Skipped types: [DocumentClassificationMetadata, DocumentOwnershipMetadata, DocumentComplianceMetadata]
23:13:27.310 [main] INFO  [] o.s.b.a.g.s.GraphQlWebMvcAutoConfiguration - GraphQL endpoint HTTP POST /graphql
23:13:27.639 [main] INFO  [] o.s.b.a.e.web.EndpointLinksResolver - Exposing 3 endpoints beneath base path '/actuator'
DMS CORS: Using allowedOrigins with specific domains: []
DMS CORS: allowCredentials set to true
23:13:29.373 [main] INFO  [] o.s.b.a.h.H2ConsoleAutoConfiguration - H2 console available at '/h2-console'. Database available at 'jdbc:h2:mem:testdb'
23:13:29.675 [main] INFO  [] o.a.coyote.http11.Http11NioProtocol - Starting ProtocolHandler ["http-nio-auto-1"]
23:13:29.693 [main] INFO  [] o.s.b.w.e.tomcat.TomcatWebServer - Tomcat started on port 56551 (http) with context path '/'
23:13:29.719 [main] INFO  [] c.a.d.i.UrlUploadIntegrationTest - Started UrlUploadIntegrationTest in 42.79 seconds (process running for 47.069)
23:13:29.742 [scheduling-1] INFO  [] c.a.d.s.StorageConfigurationService - Starting scheduled health checks [null]
23:13:29.879 [main] INFO  [] c.a.d.s.StorageConfigurationInitializer - Initializing storage configuration [null]
23:13:29.880 [main] INFO  [] c.a.d.s.StorageConfigurationService - Getting all storage configurations for admin [null]
23:13:30.028 [scheduling-1] INFO  [] PERFORMANCE - Performance: Database Operation completed in 285ms with status SUCCESS - Repository: $Proxy273.findConfigurationsNeedingHealthCheck
23:13:30.028 [main] INFO  [] PERFORMANCE - Performance: Database Operation completed in 148ms with status SUCCESS - Repository: $Proxy273.findAllOrderByPriorityAndDate
23:13:30.029 [scheduling-1] INFO  [] c.a.d.s.StorageConfigurationService - Completed health checks for 0 configurations [null]
23:13:30.029 [main] INFO  [] c.a.d.s.StorageConfigurationInitializer - Found 0 existing storage configurations [null]
23:13:30.031 [main] INFO  [] c.a.d.service.AuditEncryptionService - Audit log encryption is disabled
23:13:30.031 [main] INFO  [] c.a.d.config.AuditEncryptionConfig - Audit encryption service initialized successfully
23:13:30.031 [main] INFO  [] c.a.d.service.PIIEncryptionService - PII encryption is disabled
23:13:30.031 [main] INFO  [] c.a.d.config.PIIEncryptionConfig - PII encryption service initialized successfully
23:13:30.054 [scheduling-1] INFO  [] PERFORMANCE - Performance: Database Operation completed in 13ms with status SUCCESS - Repository: $Proxy252.findUnverifiedAuditLogs
]]></system-out>
  </testcase>
  <testcase name="testUploadFromFileUri_Success" classname="com.ascentbusiness.dms_svc.integration.UrlUploadIntegrationTest" time="0.088">
    <system-out><![CDATA[23:13:30.377 [main] DEBUG [] c.a.dms_svc.aspect.RateLimitAspect - Rate limit check passed for type: DOCUMENT_UPLOAD, count: 1/10, window: 300s
23:13:30.379 [main] INFO  [] c.a.d.resolver.DocumentResolver - GraphQL uploadDocumentFromPathOrUrl called with source: file://C:\Users\<USER>\AppData\Local\Temp\file-uri-test1395684237671712232.txt [null]
23:13:30.379 [main] INFO  [] c.a.d.resolver.DocumentResolver - Reading file from URI path: C:\Users\<USER>\AppData\Local\Temp\file-uri-test1395684237671712232.txt [null]
23:13:30.379 [main] INFO  [] c.a.d.service.UrlDownloadService - Reading file from path: C:\Users\<USER>\AppData\Local\Temp\file-uri-test1395684237671712232.txt [null]
23:13:30.385 [main] INFO  [] c.a.d.service.UrlDownloadService - Successfully read file from path: C:\Users\<USER>\AppData\Local\Temp\file-uri-test1395684237671712232.txt -> 21 bytes [null]
23:13:30.392 [main] INFO  [] c.a.d.resolver.DocumentResolver - Successfully uploaded document from source: file://C:\Users\<USER>\AppData\Local\Temp\file-uri-test1395684237671712232.txt -> Document ID: 2 [null]
]]></system-out>
  </testcase>
  <testcase name="testUploadFromUrl_ValidationFailure_DomainNotAllowed" classname="com.ascentbusiness.dms_svc.integration.UrlUploadIntegrationTest" time="0.03"/>
  <testcase name="testUrlUploadConfig_PortValidation" classname="com.ascentbusiness.dms_svc.integration.UrlUploadIntegrationTest" time="0.02"/>
  <testcase name="testUploadFromLocalPath_FileNotFound" classname="com.ascentbusiness.dms_svc.integration.UrlUploadIntegrationTest" time="0.032">
    <system-out><![CDATA[23:13:30.467 [main] DEBUG [] c.a.dms_svc.aspect.RateLimitAspect - Rate limit check passed for type: DOCUMENT_UPLOAD, count: 2/10, window: 300s
23:13:30.467 [main] INFO  [] c.a.d.resolver.DocumentResolver - GraphQL uploadDocumentFromPathOrUrl called with source: /path/that/does/not/exist.txt [null]
23:13:30.468 [main] INFO  [] c.a.d.resolver.DocumentResolver - Attempting to read file from path (fallback): /path/that/does/not/exist.txt [null]
23:13:30.468 [main] INFO  [] c.a.d.service.UrlDownloadService - Reading file from path: /path/that/does/not/exist.txt [null]
23:13:30.471 [main] ERROR [] c.a.d.aspect.CorrelationIdAspect - CorrelationIdAspect: Error in SERVICE layer for UrlDownloadService.downloadFromPath with correlation ID null: File not found: /path/that/does/not/exist.txt
com.ascentbusiness.dms_svc.exception.DmsBusinessException: File not found: /path/that/does/not/exist.txt
	at com.ascentbusiness.dms_svc.service.UrlDownloadService.downloadFromPath(UrlDownloadService.java:125)
	at java.base/jdk.internal.reflect.DirectMethodHandleAccessor.invoke(DirectMethodHandleAccessor.java:103)
	at java.base/java.lang.reflect.Method.invoke(Method.java:580)
	at org.springframework.aop.support.AopUtils.invokeJoinpointUsingReflection(AopUtils.java:359)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.invokeJoinpoint(ReflectiveMethodInvocation.java:196)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:163)
	at org.springframework.aop.aspectj.MethodInvocationProceedingJoinPoint.proceed(MethodInvocationProceedingJoinPoint.java:89)
	at com.ascentbusiness.dms_svc.aspect.CorrelationIdAspect.ensureCorrelationIdContext(CorrelationIdAspect.java:97)
	at com.ascentbusiness.dms_svc.aspect.CorrelationIdAspect.ensureCorrelationIdInServices(CorrelationIdAspect.java:37)
	at java.base/jdk.internal.reflect.DirectMethodHandleAccessor.invoke(DirectMethodHandleAccessor.java:103)
	at java.base/java.lang.reflect.Method.invoke(Method.java:580)
	at org.springframework.aop.aspectj.AbstractAspectJAdvice.invokeAdviceMethodWithGivenArgs(AbstractAspectJAdvice.java:642)
	at org.springframework.aop.aspectj.AbstractAspectJAdvice.invokeAdviceMethod(AbstractAspectJAdvice.java:632)
	at org.springframework.aop.aspectj.AspectJAroundAdvice.invoke(AspectJAroundAdvice.java:71)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:184)
	at org.springframework.aop.interceptor.ExposeInvocationInterceptor.invoke(ExposeInvocationInterceptor.java:97)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:184)
	at org.springframework.aop.framework.CglibAopProxy$DynamicAdvisedInterceptor.intercept(CglibAopProxy.java:728)
	at com.ascentbusiness.dms_svc.service.UrlDownloadService$$SpringCGLIB$$0.downloadFromPath(<generated>)
	at com.ascentbusiness.dms_svc.resolver.DocumentResolver.uploadDocumentFromPathOrUrl(DocumentResolver.java:765)
	at java.base/jdk.internal.reflect.DirectMethodHandleAccessor.invoke(DirectMethodHandleAccessor.java:103)
	at java.base/java.lang.reflect.Method.invoke(Method.java:580)
	at org.springframework.aop.support.AopUtils.invokeJoinpointUsingReflection(AopUtils.java:359)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.invokeJoinpoint(ReflectiveMethodInvocation.java:196)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:163)
	at org.springframework.aop.aspectj.MethodInvocationProceedingJoinPoint.proceed(MethodInvocationProceedingJoinPoint.java:89)
	at com.ascentbusiness.dms_svc.aspect.RateLimitAspect.rateLimit(RateLimitAspect.java:85)
	at java.base/jdk.internal.reflect.DirectMethodHandleAccessor.invoke(DirectMethodHandleAccessor.java:103)
	at java.base/java.lang.reflect.Method.invoke(Method.java:580)
	at org.springframework.aop.aspectj.AbstractAspectJAdvice.invokeAdviceMethodWithGivenArgs(AbstractAspectJAdvice.java:642)
	at org.springframework.aop.aspectj.AbstractAspectJAdvice.invokeAdviceMethod(AbstractAspectJAdvice.java:632)
	at org.springframework.aop.aspectj.AspectJAroundAdvice.invoke(AspectJAroundAdvice.java:71)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:173)
	at org.springframework.aop.aspectj.MethodInvocationProceedingJoinPoint.proceed(MethodInvocationProceedingJoinPoint.java:89)
	at com.ascentbusiness.dms_svc.aspect.CorrelationIdAspect.ensureCorrelationIdContext(CorrelationIdAspect.java:97)
	at com.ascentbusiness.dms_svc.aspect.CorrelationIdAspect.ensureCorrelationIdInResolvers(CorrelationIdAspect.java:45)
	at java.base/jdk.internal.reflect.DirectMethodHandleAccessor.invoke(DirectMethodHandleAccessor.java:103)
	at java.base/java.lang.reflect.Method.invoke(Method.java:580)
	at org.springframework.aop.aspectj.AbstractAspectJAdvice.invokeAdviceMethodWithGivenArgs(AbstractAspectJAdvice.java:642)
	at org.springframework.aop.aspectj.AbstractAspectJAdvice.invokeAdviceMethod(AbstractAspectJAdvice.java:632)
	at org.springframework.aop.aspectj.AspectJAroundAdvice.invoke(AspectJAroundAdvice.java:71)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:184)
	at org.springframework.aop.interceptor.ExposeInvocationInterceptor.invoke(ExposeInvocationInterceptor.java:97)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:184)
	at org.springframework.aop.framework.CglibAopProxy$DynamicAdvisedInterceptor.intercept(CglibAopProxy.java:728)
	at com.ascentbusiness.dms_svc.resolver.DocumentResolver$$SpringCGLIB$$0.uploadDocumentFromPathOrUrl(<generated>)
	at com.ascentbusiness.dms_svc.integration.UrlUploadIntegrationTest.lambda$0(UrlUploadIntegrationTest.java:142)
	at org.junit.jupiter.api.AssertThrows.assertThrows(AssertThrows.java:53)
	at org.junit.jupiter.api.AssertThrows.assertThrows(AssertThrows.java:35)
	at org.junit.jupiter.api.Assertions.assertThrows(Assertions.java:3128)
	at com.ascentbusiness.dms_svc.integration.UrlUploadIntegrationTest.testUploadFromLocalPath_FileNotFound(UrlUploadIntegrationTest.java:141)
	at java.base/jdk.internal.reflect.DirectMethodHandleAccessor.invoke(DirectMethodHandleAccessor.java:103)
	at java.base/java.lang.reflect.Method.invoke(Method.java:580)
	at org.junit.platform.commons.util.ReflectionUtils.invokeMethod(ReflectionUtils.java:775)
	at org.junit.platform.commons.support.ReflectionSupport.invokeMethod(ReflectionSupport.java:479)
	at org.junit.jupiter.engine.execution.MethodInvocation.proceed(MethodInvocation.java:60)
	at org.junit.jupiter.engine.execution.InvocationInterceptorChain$ValidatingInvocation.proceed(InvocationInterceptorChain.java:131)
	at org.junit.jupiter.engine.extension.TimeoutExtension.intercept(TimeoutExtension.java:161)
	at org.junit.jupiter.engine.extension.TimeoutExtension.interceptTestableMethod(TimeoutExtension.java:152)
	at org.junit.jupiter.engine.extension.TimeoutExtension.interceptTestMethod(TimeoutExtension.java:91)
	at org.junit.jupiter.engine.execution.InterceptingExecutableInvoker$ReflectiveInterceptorCall.lambda$ofVoidMethod$0(InterceptingExecutableInvoker.java:112)
	at org.junit.jupiter.engine.execution.InterceptingExecutableInvoker.lambda$invoke$0(InterceptingExecutableInvoker.java:94)
	at org.junit.jupiter.engine.execution.InvocationInterceptorChain$InterceptedInvocation.proceed(InvocationInterceptorChain.java:106)
	at org.junit.jupiter.engine.execution.InvocationInterceptorChain.proceed(InvocationInterceptorChain.java:64)
	at org.junit.jupiter.engine.execution.InvocationInterceptorChain.chainAndInvoke(InvocationInterceptorChain.java:45)
	at org.junit.jupiter.engine.execution.InvocationInterceptorChain.invoke(InvocationInterceptorChain.java:37)
	at org.junit.jupiter.engine.execution.InterceptingExecutableInvoker.invoke(InterceptingExecutableInvoker.java:93)
	at org.junit.jupiter.engine.execution.InterceptingExecutableInvoker.invoke(InterceptingExecutableInvoker.java:87)
	at org.junit.jupiter.engine.descriptor.TestMethodTestDescriptor.lambda$invokeTestMethod$7(TestMethodTestDescriptor.java:216)
	at org.junit.platform.engine.support.hierarchical.ThrowableCollector.execute(ThrowableCollector.java:73)
	at org.junit.jupiter.engine.descriptor.TestMethodTestDescriptor.invokeTestMethod(TestMethodTestDescriptor.java:212)
	at org.junit.jupiter.engine.descriptor.TestMethodTestDescriptor.execute(TestMethodTestDescriptor.java:137)
	at org.junit.jupiter.engine.descriptor.TestMethodTestDescriptor.execute(TestMethodTestDescriptor.java:69)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.lambda$executeRecursively$6(NodeTestTask.java:156)
	at org.junit.platform.engine.support.hierarchical.ThrowableCollector.execute(ThrowableCollector.java:73)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.lambda$executeRecursively$8(NodeTestTask.java:146)
	at org.junit.platform.engine.support.hierarchical.Node.around(Node.java:137)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.lambda$executeRecursively$9(NodeTestTask.java:144)
	at org.junit.platform.engine.support.hierarchical.ThrowableCollector.execute(ThrowableCollector.java:73)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.executeRecursively(NodeTestTask.java:143)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.execute(NodeTestTask.java:100)
	at java.base/java.util.ArrayList.forEach(ArrayList.java:1596)
	at org.junit.platform.engine.support.hierarchical.SameThreadHierarchicalTestExecutorService.invokeAll(SameThreadHierarchicalTestExecutorService.java:41)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.lambda$executeRecursively$6(NodeTestTask.java:160)
	at org.junit.platform.engine.support.hierarchical.ThrowableCollector.execute(ThrowableCollector.java:73)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.lambda$executeRecursively$8(NodeTestTask.java:146)
	at org.junit.platform.engine.support.hierarchical.Node.around(Node.java:137)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.lambda$executeRecursively$9(NodeTestTask.java:144)
	at org.junit.platform.engine.support.hierarchical.ThrowableCollector.execute(ThrowableCollector.java:73)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.executeRecursively(NodeTestTask.java:143)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.execute(NodeTestTask.java:100)
	at java.base/java.util.ArrayList.forEach(ArrayList.java:1596)
	at org.junit.platform.engine.support.hierarchical.SameThreadHierarchicalTestExecutorService.invokeAll(SameThreadHierarchicalTestExecutorService.java:41)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.lambda$executeRecursively$6(NodeTestTask.java:160)
	at org.junit.platform.engine.support.hierarchical.ThrowableCollector.execute(ThrowableCollector.java:73)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.lambda$executeRecursively$8(NodeTestTask.java:146)
	at org.junit.platform.engine.support.hierarchical.Node.around(Node.java:137)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.lambda$executeRecursively$9(NodeTestTask.java:144)
	at org.junit.platform.engine.support.hierarchical.ThrowableCollector.execute(ThrowableCollector.java:73)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.executeRecursively(NodeTestTask.java:143)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.execute(NodeTestTask.java:100)
	at org.junit.platform.engine.support.hierarchical.SameThreadHierarchicalTestExecutorService.submit(SameThreadHierarchicalTestExecutorService.java:35)
	at org.junit.platform.engine.support.hierarchical.HierarchicalTestExecutor.execute(HierarchicalTestExecutor.java:57)
	at org.junit.platform.engine.support.hierarchical.HierarchicalTestEngine.execute(HierarchicalTestEngine.java:54)
	at org.junit.platform.launcher.core.EngineExecutionOrchestrator.execute(EngineExecutionOrchestrator.java:201)
	at org.junit.platform.launcher.core.EngineExecutionOrchestrator.execute(EngineExecutionOrchestrator.java:170)
	at org.junit.platform.launcher.core.EngineExecutionOrchestrator.execute(EngineExecutionOrchestrator.java:94)
	at org.junit.platform.launcher.core.EngineExecutionOrchestrator.lambda$execute$0(EngineExecutionOrchestrator.java:59)
	at org.junit.platform.launcher.core.EngineExecutionOrchestrator.withInterceptedStreams(EngineExecutionOrchestrator.java:142)
	at org.junit.platform.launcher.core.EngineExecutionOrchestrator.execute(EngineExecutionOrchestrator.java:58)
	at org.junit.platform.launcher.core.DefaultLauncher.execute(DefaultLauncher.java:103)
	at org.junit.platform.launcher.core.DefaultLauncher.execute(DefaultLauncher.java:85)
	at org.junit.platform.launcher.core.DelegatingLauncher.execute(DelegatingLauncher.java:47)
	at org.junit.platform.launcher.core.InterceptingLauncher.lambda$execute$1(InterceptingLauncher.java:39)
	at org.junit.platform.launcher.core.ClasspathAlignmentCheckingLauncherInterceptor.intercept(ClasspathAlignmentCheckingLauncherInterceptor.java:25)
	at org.junit.platform.launcher.core.InterceptingLauncher.execute(InterceptingLauncher.java:38)
	at org.junit.platform.launcher.core.DelegatingLauncher.execute(DelegatingLauncher.java:47)
	at org.apache.maven.surefire.junitplatform.LazyLauncher.execute(LazyLauncher.java:56)
	at org.apache.maven.surefire.junitplatform.JUnitPlatformProvider.execute(JUnitPlatformProvider.java:184)
	at org.apache.maven.surefire.junitplatform.JUnitPlatformProvider.invokeAllTests(JUnitPlatformProvider.java:148)
	at org.apache.maven.surefire.junitplatform.JUnitPlatformProvider.invoke(JUnitPlatformProvider.java:122)
	at org.apache.maven.surefire.booter.ForkedBooter.runSuitesInProcess(ForkedBooter.java:385)
	at org.apache.maven.surefire.booter.ForkedBooter.execute(ForkedBooter.java:162)
	at org.apache.maven.surefire.booter.ForkedBooter.run(ForkedBooter.java:507)
	at org.apache.maven.surefire.booter.ForkedBooter.main(ForkedBooter.java:495)
23:13:30.477 [main] ERROR [] c.a.d.resolver.DocumentResolver - Business error uploading document from source: /path/that/does/not/exist.txt [null]
com.ascentbusiness.dms_svc.exception.DmsBusinessException: Unable to access file from source: /path/that/does/not/exist.txt. Ensure the file exists and is accessible, or provide a valid URL.
	at com.ascentbusiness.dms_svc.resolver.DocumentResolver.uploadDocumentFromPathOrUrl(DocumentResolver.java:767)
	at java.base/jdk.internal.reflect.DirectMethodHandleAccessor.invoke(DirectMethodHandleAccessor.java:103)
	at java.base/java.lang.reflect.Method.invoke(Method.java:580)
	at org.springframework.aop.support.AopUtils.invokeJoinpointUsingReflection(AopUtils.java:359)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.invokeJoinpoint(ReflectiveMethodInvocation.java:196)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:163)
	at org.springframework.aop.aspectj.MethodInvocationProceedingJoinPoint.proceed(MethodInvocationProceedingJoinPoint.java:89)
	at com.ascentbusiness.dms_svc.aspect.RateLimitAspect.rateLimit(RateLimitAspect.java:85)
	at java.base/jdk.internal.reflect.DirectMethodHandleAccessor.invoke(DirectMethodHandleAccessor.java:103)
	at java.base/java.lang.reflect.Method.invoke(Method.java:580)
	at org.springframework.aop.aspectj.AbstractAspectJAdvice.invokeAdviceMethodWithGivenArgs(AbstractAspectJAdvice.java:642)
	at org.springframework.aop.aspectj.AbstractAspectJAdvice.invokeAdviceMethod(AbstractAspectJAdvice.java:632)
	at org.springframework.aop.aspectj.AspectJAroundAdvice.invoke(AspectJAroundAdvice.java:71)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:173)
	at org.springframework.aop.aspectj.MethodInvocationProceedingJoinPoint.proceed(MethodInvocationProceedingJoinPoint.java:89)
	at com.ascentbusiness.dms_svc.aspect.CorrelationIdAspect.ensureCorrelationIdContext(CorrelationIdAspect.java:97)
	at com.ascentbusiness.dms_svc.aspect.CorrelationIdAspect.ensureCorrelationIdInResolvers(CorrelationIdAspect.java:45)
	at java.base/jdk.internal.reflect.DirectMethodHandleAccessor.invoke(DirectMethodHandleAccessor.java:103)
	at java.base/java.lang.reflect.Method.invoke(Method.java:580)
	at org.springframework.aop.aspectj.AbstractAspectJAdvice.invokeAdviceMethodWithGivenArgs(AbstractAspectJAdvice.java:642)
	at org.springframework.aop.aspectj.AbstractAspectJAdvice.invokeAdviceMethod(AbstractAspectJAdvice.java:632)
	at org.springframework.aop.aspectj.AspectJAroundAdvice.invoke(AspectJAroundAdvice.java:71)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:184)
	at org.springframework.aop.interceptor.ExposeInvocationInterceptor.invoke(ExposeInvocationInterceptor.java:97)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:184)
	at org.springframework.aop.framework.CglibAopProxy$DynamicAdvisedInterceptor.intercept(CglibAopProxy.java:728)
	at com.ascentbusiness.dms_svc.resolver.DocumentResolver$$SpringCGLIB$$0.uploadDocumentFromPathOrUrl(<generated>)
	at com.ascentbusiness.dms_svc.integration.UrlUploadIntegrationTest.lambda$0(UrlUploadIntegrationTest.java:142)
	at org.junit.jupiter.api.AssertThrows.assertThrows(AssertThrows.java:53)
	at org.junit.jupiter.api.AssertThrows.assertThrows(AssertThrows.java:35)
	at org.junit.jupiter.api.Assertions.assertThrows(Assertions.java:3128)
	at com.ascentbusiness.dms_svc.integration.UrlUploadIntegrationTest.testUploadFromLocalPath_FileNotFound(UrlUploadIntegrationTest.java:141)
	at java.base/jdk.internal.reflect.DirectMethodHandleAccessor.invoke(DirectMethodHandleAccessor.java:103)
	at java.base/java.lang.reflect.Method.invoke(Method.java:580)
	at org.junit.platform.commons.util.ReflectionUtils.invokeMethod(ReflectionUtils.java:775)
	at org.junit.platform.commons.support.ReflectionSupport.invokeMethod(ReflectionSupport.java:479)
	at org.junit.jupiter.engine.execution.MethodInvocation.proceed(MethodInvocation.java:60)
	at org.junit.jupiter.engine.execution.InvocationInterceptorChain$ValidatingInvocation.proceed(InvocationInterceptorChain.java:131)
	at org.junit.jupiter.engine.extension.TimeoutExtension.intercept(TimeoutExtension.java:161)
	at org.junit.jupiter.engine.extension.TimeoutExtension.interceptTestableMethod(TimeoutExtension.java:152)
	at org.junit.jupiter.engine.extension.TimeoutExtension.interceptTestMethod(TimeoutExtension.java:91)
	at org.junit.jupiter.engine.execution.InterceptingExecutableInvoker$ReflectiveInterceptorCall.lambda$ofVoidMethod$0(InterceptingExecutableInvoker.java:112)
	at org.junit.jupiter.engine.execution.InterceptingExecutableInvoker.lambda$invoke$0(InterceptingExecutableInvoker.java:94)
	at org.junit.jupiter.engine.execution.InvocationInterceptorChain$InterceptedInvocation.proceed(InvocationInterceptorChain.java:106)
	at org.junit.jupiter.engine.execution.InvocationInterceptorChain.proceed(InvocationInterceptorChain.java:64)
	at org.junit.jupiter.engine.execution.InvocationInterceptorChain.chainAndInvoke(InvocationInterceptorChain.java:45)
	at org.junit.jupiter.engine.execution.InvocationInterceptorChain.invoke(InvocationInterceptorChain.java:37)
	at org.junit.jupiter.engine.execution.InterceptingExecutableInvoker.invoke(InterceptingExecutableInvoker.java:93)
	at org.junit.jupiter.engine.execution.InterceptingExecutableInvoker.invoke(InterceptingExecutableInvoker.java:87)
	at org.junit.jupiter.engine.descriptor.TestMethodTestDescriptor.lambda$invokeTestMethod$7(TestMethodTestDescriptor.java:216)
	at org.junit.platform.engine.support.hierarchical.ThrowableCollector.execute(ThrowableCollector.java:73)
	at org.junit.jupiter.engine.descriptor.TestMethodTestDescriptor.invokeTestMethod(TestMethodTestDescriptor.java:212)
	at org.junit.jupiter.engine.descriptor.TestMethodTestDescriptor.execute(TestMethodTestDescriptor.java:137)
	at org.junit.jupiter.engine.descriptor.TestMethodTestDescriptor.execute(TestMethodTestDescriptor.java:69)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.lambda$executeRecursively$6(NodeTestTask.java:156)
	at org.junit.platform.engine.support.hierarchical.ThrowableCollector.execute(ThrowableCollector.java:73)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.lambda$executeRecursively$8(NodeTestTask.java:146)
	at org.junit.platform.engine.support.hierarchical.Node.around(Node.java:137)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.lambda$executeRecursively$9(NodeTestTask.java:144)
	at org.junit.platform.engine.support.hierarchical.ThrowableCollector.execute(ThrowableCollector.java:73)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.executeRecursively(NodeTestTask.java:143)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.execute(NodeTestTask.java:100)
	at java.base/java.util.ArrayList.forEach(ArrayList.java:1596)
	at org.junit.platform.engine.support.hierarchical.SameThreadHierarchicalTestExecutorService.invokeAll(SameThreadHierarchicalTestExecutorService.java:41)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.lambda$executeRecursively$6(NodeTestTask.java:160)
	at org.junit.platform.engine.support.hierarchical.ThrowableCollector.execute(ThrowableCollector.java:73)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.lambda$executeRecursively$8(NodeTestTask.java:146)
	at org.junit.platform.engine.support.hierarchical.Node.around(Node.java:137)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.lambda$executeRecursively$9(NodeTestTask.java:144)
	at org.junit.platform.engine.support.hierarchical.ThrowableCollector.execute(ThrowableCollector.java:73)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.executeRecursively(NodeTestTask.java:143)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.execute(NodeTestTask.java:100)
	at java.base/java.util.ArrayList.forEach(ArrayList.java:1596)
	at org.junit.platform.engine.support.hierarchical.SameThreadHierarchicalTestExecutorService.invokeAll(SameThreadHierarchicalTestExecutorService.java:41)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.lambda$executeRecursively$6(NodeTestTask.java:160)
	at org.junit.platform.engine.support.hierarchical.ThrowableCollector.execute(ThrowableCollector.java:73)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.lambda$executeRecursively$8(NodeTestTask.java:146)
	at org.junit.platform.engine.support.hierarchical.Node.around(Node.java:137)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.lambda$executeRecursively$9(NodeTestTask.java:144)
	at org.junit.platform.engine.support.hierarchical.ThrowableCollector.execute(ThrowableCollector.java:73)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.executeRecursively(NodeTestTask.java:143)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.execute(NodeTestTask.java:100)
	at org.junit.platform.engine.support.hierarchical.SameThreadHierarchicalTestExecutorService.submit(SameThreadHierarchicalTestExecutorService.java:35)
	at org.junit.platform.engine.support.hierarchical.HierarchicalTestExecutor.execute(HierarchicalTestExecutor.java:57)
	at org.junit.platform.engine.support.hierarchical.HierarchicalTestEngine.execute(HierarchicalTestEngine.java:54)
	at org.junit.platform.launcher.core.EngineExecutionOrchestrator.execute(EngineExecutionOrchestrator.java:201)
	at org.junit.platform.launcher.core.EngineExecutionOrchestrator.execute(EngineExecutionOrchestrator.java:170)
	at org.junit.platform.launcher.core.EngineExecutionOrchestrator.execute(EngineExecutionOrchestrator.java:94)
	at org.junit.platform.launcher.core.EngineExecutionOrchestrator.lambda$execute$0(EngineExecutionOrchestrator.java:59)
	at org.junit.platform.launcher.core.EngineExecutionOrchestrator.withInterceptedStreams(EngineExecutionOrchestrator.java:142)
	at org.junit.platform.launcher.core.EngineExecutionOrchestrator.execute(EngineExecutionOrchestrator.java:58)
	at org.junit.platform.launcher.core.DefaultLauncher.execute(DefaultLauncher.java:103)
	at org.junit.platform.launcher.core.DefaultLauncher.execute(DefaultLauncher.java:85)
	at org.junit.platform.launcher.core.DelegatingLauncher.execute(DelegatingLauncher.java:47)
	at org.junit.platform.launcher.core.InterceptingLauncher.lambda$execute$1(InterceptingLauncher.java:39)
	at org.junit.platform.launcher.core.ClasspathAlignmentCheckingLauncherInterceptor.intercept(ClasspathAlignmentCheckingLauncherInterceptor.java:25)
	at org.junit.platform.launcher.core.InterceptingLauncher.execute(InterceptingLauncher.java:38)
	at org.junit.platform.launcher.core.DelegatingLauncher.execute(DelegatingLauncher.java:47)
	at org.apache.maven.surefire.junitplatform.LazyLauncher.execute(LazyLauncher.java:56)
	at org.apache.maven.surefire.junitplatform.JUnitPlatformProvider.execute(JUnitPlatformProvider.java:184)
	at org.apache.maven.surefire.junitplatform.JUnitPlatformProvider.invokeAllTests(JUnitPlatformProvider.java:148)
	at org.apache.maven.surefire.junitplatform.JUnitPlatformProvider.invoke(JUnitPlatformProvider.java:122)
	at org.apache.maven.surefire.booter.ForkedBooter.runSuitesInProcess(ForkedBooter.java:385)
	at org.apache.maven.surefire.booter.ForkedBooter.execute(ForkedBooter.java:162)
	at org.apache.maven.surefire.booter.ForkedBooter.run(ForkedBooter.java:507)
	at org.apache.maven.surefire.booter.ForkedBooter.main(ForkedBooter.java:495)
23:13:30.479 [main] ERROR [] c.a.d.aspect.CorrelationIdAspect - CorrelationIdAspect: Error in RESOLVER layer for DocumentResolver.uploadDocumentFromPathOrUrl with correlation ID null: Unable to access file from source: /path/that/does/not/exist.txt. Ensure the file exists and is accessible, or provide a valid URL.
com.ascentbusiness.dms_svc.exception.DmsBusinessException: Unable to access file from source: /path/that/does/not/exist.txt. Ensure the file exists and is accessible, or provide a valid URL.
	at com.ascentbusiness.dms_svc.resolver.DocumentResolver.uploadDocumentFromPathOrUrl(DocumentResolver.java:767)
	at java.base/jdk.internal.reflect.DirectMethodHandleAccessor.invoke(DirectMethodHandleAccessor.java:103)
	at java.base/java.lang.reflect.Method.invoke(Method.java:580)
	at org.springframework.aop.support.AopUtils.invokeJoinpointUsingReflection(AopUtils.java:359)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.invokeJoinpoint(ReflectiveMethodInvocation.java:196)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:163)
	at org.springframework.aop.aspectj.MethodInvocationProceedingJoinPoint.proceed(MethodInvocationProceedingJoinPoint.java:89)
	at com.ascentbusiness.dms_svc.aspect.RateLimitAspect.rateLimit(RateLimitAspect.java:85)
	at java.base/jdk.internal.reflect.DirectMethodHandleAccessor.invoke(DirectMethodHandleAccessor.java:103)
	at java.base/java.lang.reflect.Method.invoke(Method.java:580)
	at org.springframework.aop.aspectj.AbstractAspectJAdvice.invokeAdviceMethodWithGivenArgs(AbstractAspectJAdvice.java:642)
	at org.springframework.aop.aspectj.AbstractAspectJAdvice.invokeAdviceMethod(AbstractAspectJAdvice.java:632)
	at org.springframework.aop.aspectj.AspectJAroundAdvice.invoke(AspectJAroundAdvice.java:71)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:173)
	at org.springframework.aop.aspectj.MethodInvocationProceedingJoinPoint.proceed(MethodInvocationProceedingJoinPoint.java:89)
	at com.ascentbusiness.dms_svc.aspect.CorrelationIdAspect.ensureCorrelationIdContext(CorrelationIdAspect.java:97)
	at com.ascentbusiness.dms_svc.aspect.CorrelationIdAspect.ensureCorrelationIdInResolvers(CorrelationIdAspect.java:45)
	at java.base/jdk.internal.reflect.DirectMethodHandleAccessor.invoke(DirectMethodHandleAccessor.java:103)
	at java.base/java.lang.reflect.Method.invoke(Method.java:580)
	at org.springframework.aop.aspectj.AbstractAspectJAdvice.invokeAdviceMethodWithGivenArgs(AbstractAspectJAdvice.java:642)
	at org.springframework.aop.aspectj.AbstractAspectJAdvice.invokeAdviceMethod(AbstractAspectJAdvice.java:632)
	at org.springframework.aop.aspectj.AspectJAroundAdvice.invoke(AspectJAroundAdvice.java:71)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:184)
	at org.springframework.aop.interceptor.ExposeInvocationInterceptor.invoke(ExposeInvocationInterceptor.java:97)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:184)
	at org.springframework.aop.framework.CglibAopProxy$DynamicAdvisedInterceptor.intercept(CglibAopProxy.java:728)
	at com.ascentbusiness.dms_svc.resolver.DocumentResolver$$SpringCGLIB$$0.uploadDocumentFromPathOrUrl(<generated>)
	at com.ascentbusiness.dms_svc.integration.UrlUploadIntegrationTest.lambda$0(UrlUploadIntegrationTest.java:142)
	at org.junit.jupiter.api.AssertThrows.assertThrows(AssertThrows.java:53)
	at org.junit.jupiter.api.AssertThrows.assertThrows(AssertThrows.java:35)
	at org.junit.jupiter.api.Assertions.assertThrows(Assertions.java:3128)
	at com.ascentbusiness.dms_svc.integration.UrlUploadIntegrationTest.testUploadFromLocalPath_FileNotFound(UrlUploadIntegrationTest.java:141)
	at java.base/jdk.internal.reflect.DirectMethodHandleAccessor.invoke(DirectMethodHandleAccessor.java:103)
	at java.base/java.lang.reflect.Method.invoke(Method.java:580)
	at org.junit.platform.commons.util.ReflectionUtils.invokeMethod(ReflectionUtils.java:775)
	at org.junit.platform.commons.support.ReflectionSupport.invokeMethod(ReflectionSupport.java:479)
	at org.junit.jupiter.engine.execution.MethodInvocation.proceed(MethodInvocation.java:60)
	at org.junit.jupiter.engine.execution.InvocationInterceptorChain$ValidatingInvocation.proceed(InvocationInterceptorChain.java:131)
	at org.junit.jupiter.engine.extension.TimeoutExtension.intercept(TimeoutExtension.java:161)
	at org.junit.jupiter.engine.extension.TimeoutExtension.interceptTestableMethod(TimeoutExtension.java:152)
	at org.junit.jupiter.engine.extension.TimeoutExtension.interceptTestMethod(TimeoutExtension.java:91)
	at org.junit.jupiter.engine.execution.InterceptingExecutableInvoker$ReflectiveInterceptorCall.lambda$ofVoidMethod$0(InterceptingExecutableInvoker.java:112)
	at org.junit.jupiter.engine.execution.InterceptingExecutableInvoker.lambda$invoke$0(InterceptingExecutableInvoker.java:94)
	at org.junit.jupiter.engine.execution.InvocationInterceptorChain$InterceptedInvocation.proceed(InvocationInterceptorChain.java:106)
	at org.junit.jupiter.engine.execution.InvocationInterceptorChain.proceed(InvocationInterceptorChain.java:64)
	at org.junit.jupiter.engine.execution.InvocationInterceptorChain.chainAndInvoke(InvocationInterceptorChain.java:45)
	at org.junit.jupiter.engine.execution.InvocationInterceptorChain.invoke(InvocationInterceptorChain.java:37)
	at org.junit.jupiter.engine.execution.InterceptingExecutableInvoker.invoke(InterceptingExecutableInvoker.java:93)
	at org.junit.jupiter.engine.execution.InterceptingExecutableInvoker.invoke(InterceptingExecutableInvoker.java:87)
	at org.junit.jupiter.engine.descriptor.TestMethodTestDescriptor.lambda$invokeTestMethod$7(TestMethodTestDescriptor.java:216)
	at org.junit.platform.engine.support.hierarchical.ThrowableCollector.execute(ThrowableCollector.java:73)
	at org.junit.jupiter.engine.descriptor.TestMethodTestDescriptor.invokeTestMethod(TestMethodTestDescriptor.java:212)
	at org.junit.jupiter.engine.descriptor.TestMethodTestDescriptor.execute(TestMethodTestDescriptor.java:137)
	at org.junit.jupiter.engine.descriptor.TestMethodTestDescriptor.execute(TestMethodTestDescriptor.java:69)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.lambda$executeRecursively$6(NodeTestTask.java:156)
	at org.junit.platform.engine.support.hierarchical.ThrowableCollector.execute(ThrowableCollector.java:73)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.lambda$executeRecursively$8(NodeTestTask.java:146)
	at org.junit.platform.engine.support.hierarchical.Node.around(Node.java:137)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.lambda$executeRecursively$9(NodeTestTask.java:144)
	at org.junit.platform.engine.support.hierarchical.ThrowableCollector.execute(ThrowableCollector.java:73)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.executeRecursively(NodeTestTask.java:143)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.execute(NodeTestTask.java:100)
	at java.base/java.util.ArrayList.forEach(ArrayList.java:1596)
	at org.junit.platform.engine.support.hierarchical.SameThreadHierarchicalTestExecutorService.invokeAll(SameThreadHierarchicalTestExecutorService.java:41)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.lambda$executeRecursively$6(NodeTestTask.java:160)
	at org.junit.platform.engine.support.hierarchical.ThrowableCollector.execute(ThrowableCollector.java:73)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.lambda$executeRecursively$8(NodeTestTask.java:146)
	at org.junit.platform.engine.support.hierarchical.Node.around(Node.java:137)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.lambda$executeRecursively$9(NodeTestTask.java:144)
	at org.junit.platform.engine.support.hierarchical.ThrowableCollector.execute(ThrowableCollector.java:73)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.executeRecursively(NodeTestTask.java:143)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.execute(NodeTestTask.java:100)
	at java.base/java.util.ArrayList.forEach(ArrayList.java:1596)
	at org.junit.platform.engine.support.hierarchical.SameThreadHierarchicalTestExecutorService.invokeAll(SameThreadHierarchicalTestExecutorService.java:41)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.lambda$executeRecursively$6(NodeTestTask.java:160)
	at org.junit.platform.engine.support.hierarchical.ThrowableCollector.execute(ThrowableCollector.java:73)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.lambda$executeRecursively$8(NodeTestTask.java:146)
	at org.junit.platform.engine.support.hierarchical.Node.around(Node.java:137)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.lambda$executeRecursively$9(NodeTestTask.java:144)
	at org.junit.platform.engine.support.hierarchical.ThrowableCollector.execute(ThrowableCollector.java:73)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.executeRecursively(NodeTestTask.java:143)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.execute(NodeTestTask.java:100)
	at org.junit.platform.engine.support.hierarchical.SameThreadHierarchicalTestExecutorService.submit(SameThreadHierarchicalTestExecutorService.java:35)
	at org.junit.platform.engine.support.hierarchical.HierarchicalTestExecutor.execute(HierarchicalTestExecutor.java:57)
	at org.junit.platform.engine.support.hierarchical.HierarchicalTestEngine.execute(HierarchicalTestEngine.java:54)
	at org.junit.platform.launcher.core.EngineExecutionOrchestrator.execute(EngineExecutionOrchestrator.java:201)
	at org.junit.platform.launcher.core.EngineExecutionOrchestrator.execute(EngineExecutionOrchestrator.java:170)
	at org.junit.platform.launcher.core.EngineExecutionOrchestrator.execute(EngineExecutionOrchestrator.java:94)
	at org.junit.platform.launcher.core.EngineExecutionOrchestrator.lambda$execute$0(EngineExecutionOrchestrator.java:59)
	at org.junit.platform.launcher.core.EngineExecutionOrchestrator.withInterceptedStreams(EngineExecutionOrchestrator.java:142)
	at org.junit.platform.launcher.core.EngineExecutionOrchestrator.execute(EngineExecutionOrchestrator.java:58)
	at org.junit.platform.launcher.core.DefaultLauncher.execute(DefaultLauncher.java:103)
	at org.junit.platform.launcher.core.DefaultLauncher.execute(DefaultLauncher.java:85)
	at org.junit.platform.launcher.core.DelegatingLauncher.execute(DelegatingLauncher.java:47)
	at org.junit.platform.launcher.core.InterceptingLauncher.lambda$execute$1(InterceptingLauncher.java:39)
	at org.junit.platform.launcher.core.ClasspathAlignmentCheckingLauncherInterceptor.intercept(ClasspathAlignmentCheckingLauncherInterceptor.java:25)
	at org.junit.platform.launcher.core.InterceptingLauncher.execute(InterceptingLauncher.java:38)
	at org.junit.platform.launcher.core.DelegatingLauncher.execute(DelegatingLauncher.java:47)
	at org.apache.maven.surefire.junitplatform.LazyLauncher.execute(LazyLauncher.java:56)
	at org.apache.maven.surefire.junitplatform.JUnitPlatformProvider.execute(JUnitPlatformProvider.java:184)
	at org.apache.maven.surefire.junitplatform.JUnitPlatformProvider.invokeAllTests(JUnitPlatformProvider.java:148)
	at org.apache.maven.surefire.junitplatform.JUnitPlatformProvider.invoke(JUnitPlatformProvider.java:122)
	at org.apache.maven.surefire.booter.ForkedBooter.runSuitesInProcess(ForkedBooter.java:385)
	at org.apache.maven.surefire.booter.ForkedBooter.execute(ForkedBooter.java:162)
	at org.apache.maven.surefire.booter.ForkedBooter.run(ForkedBooter.java:507)
	at org.apache.maven.surefire.booter.ForkedBooter.main(ForkedBooter.java:495)
]]></system-out>
  </testcase>
  <testcase name="testUploadFromUrl_ValidationFailure_InvalidProtocol" classname="com.ascentbusiness.dms_svc.integration.UrlUploadIntegrationTest" time="0.032">
    <system-out><![CDATA[23:13:30.502 [main] DEBUG [] c.a.dms_svc.aspect.RateLimitAspect - Rate limit check passed for type: DOCUMENT_UPLOAD, count: 3/10, window: 300s
23:13:30.502 [main] INFO  [] c.a.d.resolver.DocumentResolver - GraphQL uploadDocumentFromPathOrUrl called with source: ftp://example.com/test.pdf [null]
23:13:30.506 [main] INFO  [] c.a.d.resolver.DocumentResolver - Attempting to read file from path (fallback): ftp://example.com/test.pdf [null]
23:13:30.506 [main] INFO  [] c.a.d.service.UrlDownloadService - Reading file from path: ftp://example.com/test.pdf [null]
23:13:30.506 [main] ERROR [] c.a.d.aspect.CorrelationIdAspect - CorrelationIdAspect: Error in SERVICE layer for UrlDownloadService.downloadFromPath with correlation ID null: Illegal char <:> at index 3: ftp://example.com/test.pdf
java.nio.file.InvalidPathException: Illegal char <:> at index 3: ftp://example.com/test.pdf
	at java.base/sun.nio.fs.WindowsPathParser.normalize(WindowsPathParser.java:204)
	at java.base/sun.nio.fs.WindowsPathParser.parse(WindowsPathParser.java:175)
	at java.base/sun.nio.fs.WindowsPathParser.parse(WindowsPathParser.java:77)
	at java.base/sun.nio.fs.WindowsPath.parse(WindowsPath.java:92)
	at java.base/sun.nio.fs.WindowsFileSystem.getPath(WindowsFileSystem.java:231)
	at java.base/java.nio.file.Path.of(Path.java:148)
	at java.base/java.nio.file.Paths.get(Paths.java:69)
	at com.ascentbusiness.dms_svc.service.UrlDownloadService.downloadFromPath(UrlDownloadService.java:119)
	at java.base/jdk.internal.reflect.DirectMethodHandleAccessor.invoke(DirectMethodHandleAccessor.java:103)
	at java.base/java.lang.reflect.Method.invoke(Method.java:580)
	at org.springframework.aop.support.AopUtils.invokeJoinpointUsingReflection(AopUtils.java:359)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.invokeJoinpoint(ReflectiveMethodInvocation.java:196)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:163)
	at org.springframework.aop.aspectj.MethodInvocationProceedingJoinPoint.proceed(MethodInvocationProceedingJoinPoint.java:89)
	at com.ascentbusiness.dms_svc.aspect.CorrelationIdAspect.ensureCorrelationIdContext(CorrelationIdAspect.java:97)
	at com.ascentbusiness.dms_svc.aspect.CorrelationIdAspect.ensureCorrelationIdInServices(CorrelationIdAspect.java:37)
	at java.base/jdk.internal.reflect.DirectMethodHandleAccessor.invoke(DirectMethodHandleAccessor.java:103)
	at java.base/java.lang.reflect.Method.invoke(Method.java:580)
	at org.springframework.aop.aspectj.AbstractAspectJAdvice.invokeAdviceMethodWithGivenArgs(AbstractAspectJAdvice.java:642)
	at org.springframework.aop.aspectj.AbstractAspectJAdvice.invokeAdviceMethod(AbstractAspectJAdvice.java:632)
	at org.springframework.aop.aspectj.AspectJAroundAdvice.invoke(AspectJAroundAdvice.java:71)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:184)
	at org.springframework.aop.interceptor.ExposeInvocationInterceptor.invoke(ExposeInvocationInterceptor.java:97)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:184)
	at org.springframework.aop.framework.CglibAopProxy$DynamicAdvisedInterceptor.intercept(CglibAopProxy.java:728)
	at com.ascentbusiness.dms_svc.service.UrlDownloadService$$SpringCGLIB$$0.downloadFromPath(<generated>)
	at com.ascentbusiness.dms_svc.resolver.DocumentResolver.uploadDocumentFromPathOrUrl(DocumentResolver.java:765)
	at java.base/jdk.internal.reflect.DirectMethodHandleAccessor.invoke(DirectMethodHandleAccessor.java:103)
	at java.base/java.lang.reflect.Method.invoke(Method.java:580)
	at org.springframework.aop.support.AopUtils.invokeJoinpointUsingReflection(AopUtils.java:359)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.invokeJoinpoint(ReflectiveMethodInvocation.java:196)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:163)
	at org.springframework.aop.aspectj.MethodInvocationProceedingJoinPoint.proceed(MethodInvocationProceedingJoinPoint.java:89)
	at com.ascentbusiness.dms_svc.aspect.RateLimitAspect.rateLimit(RateLimitAspect.java:85)
	at java.base/jdk.internal.reflect.DirectMethodHandleAccessor.invoke(DirectMethodHandleAccessor.java:103)
	at java.base/java.lang.reflect.Method.invoke(Method.java:580)
	at org.springframework.aop.aspectj.AbstractAspectJAdvice.invokeAdviceMethodWithGivenArgs(AbstractAspectJAdvice.java:642)
	at org.springframework.aop.aspectj.AbstractAspectJAdvice.invokeAdviceMethod(AbstractAspectJAdvice.java:632)
	at org.springframework.aop.aspectj.AspectJAroundAdvice.invoke(AspectJAroundAdvice.java:71)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:173)
	at org.springframework.aop.aspectj.MethodInvocationProceedingJoinPoint.proceed(MethodInvocationProceedingJoinPoint.java:89)
	at com.ascentbusiness.dms_svc.aspect.CorrelationIdAspect.ensureCorrelationIdContext(CorrelationIdAspect.java:97)
	at com.ascentbusiness.dms_svc.aspect.CorrelationIdAspect.ensureCorrelationIdInResolvers(CorrelationIdAspect.java:45)
	at java.base/jdk.internal.reflect.DirectMethodHandleAccessor.invoke(DirectMethodHandleAccessor.java:103)
	at java.base/java.lang.reflect.Method.invoke(Method.java:580)
	at org.springframework.aop.aspectj.AbstractAspectJAdvice.invokeAdviceMethodWithGivenArgs(AbstractAspectJAdvice.java:642)
	at org.springframework.aop.aspectj.AbstractAspectJAdvice.invokeAdviceMethod(AbstractAspectJAdvice.java:632)
	at org.springframework.aop.aspectj.AspectJAroundAdvice.invoke(AspectJAroundAdvice.java:71)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:184)
	at org.springframework.aop.interceptor.ExposeInvocationInterceptor.invoke(ExposeInvocationInterceptor.java:97)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:184)
	at org.springframework.aop.framework.CglibAopProxy$DynamicAdvisedInterceptor.intercept(CglibAopProxy.java:728)
	at com.ascentbusiness.dms_svc.resolver.DocumentResolver$$SpringCGLIB$$0.uploadDocumentFromPathOrUrl(<generated>)
	at com.ascentbusiness.dms_svc.integration.UrlUploadIntegrationTest.lambda$1(UrlUploadIntegrationTest.java:175)
	at org.junit.jupiter.api.AssertThrows.assertThrows(AssertThrows.java:53)
	at org.junit.jupiter.api.AssertThrows.assertThrows(AssertThrows.java:35)
	at org.junit.jupiter.api.Assertions.assertThrows(Assertions.java:3128)
	at com.ascentbusiness.dms_svc.integration.UrlUploadIntegrationTest.testUploadFromUrl_ValidationFailure_InvalidProtocol(UrlUploadIntegrationTest.java:174)
	at java.base/jdk.internal.reflect.DirectMethodHandleAccessor.invoke(DirectMethodHandleAccessor.java:103)
	at java.base/java.lang.reflect.Method.invoke(Method.java:580)
	at org.junit.platform.commons.util.ReflectionUtils.invokeMethod(ReflectionUtils.java:775)
	at org.junit.platform.commons.support.ReflectionSupport.invokeMethod(ReflectionSupport.java:479)
	at org.junit.jupiter.engine.execution.MethodInvocation.proceed(MethodInvocation.java:60)
	at org.junit.jupiter.engine.execution.InvocationInterceptorChain$ValidatingInvocation.proceed(InvocationInterceptorChain.java:131)
	at org.junit.jupiter.engine.extension.TimeoutExtension.intercept(TimeoutExtension.java:161)
	at org.junit.jupiter.engine.extension.TimeoutExtension.interceptTestableMethod(TimeoutExtension.java:152)
	at org.junit.jupiter.engine.extension.TimeoutExtension.interceptTestMethod(TimeoutExtension.java:91)
	at org.junit.jupiter.engine.execution.InterceptingExecutableInvoker$ReflectiveInterceptorCall.lambda$ofVoidMethod$0(InterceptingExecutableInvoker.java:112)
	at org.junit.jupiter.engine.execution.InterceptingExecutableInvoker.lambda$invoke$0(InterceptingExecutableInvoker.java:94)
	at org.junit.jupiter.engine.execution.InvocationInterceptorChain$InterceptedInvocation.proceed(InvocationInterceptorChain.java:106)
	at org.junit.jupiter.engine.execution.InvocationInterceptorChain.proceed(InvocationInterceptorChain.java:64)
	at org.junit.jupiter.engine.execution.InvocationInterceptorChain.chainAndInvoke(InvocationInterceptorChain.java:45)
	at org.junit.jupiter.engine.execution.InvocationInterceptorChain.invoke(InvocationInterceptorChain.java:37)
	at org.junit.jupiter.engine.execution.InterceptingExecutableInvoker.invoke(InterceptingExecutableInvoker.java:93)
	at org.junit.jupiter.engine.execution.InterceptingExecutableInvoker.invoke(InterceptingExecutableInvoker.java:87)
	at org.junit.jupiter.engine.descriptor.TestMethodTestDescriptor.lambda$invokeTestMethod$7(TestMethodTestDescriptor.java:216)
	at org.junit.platform.engine.support.hierarchical.ThrowableCollector.execute(ThrowableCollector.java:73)
	at org.junit.jupiter.engine.descriptor.TestMethodTestDescriptor.invokeTestMethod(TestMethodTestDescriptor.java:212)
	at org.junit.jupiter.engine.descriptor.TestMethodTestDescriptor.execute(TestMethodTestDescriptor.java:137)
	at org.junit.jupiter.engine.descriptor.TestMethodTestDescriptor.execute(TestMethodTestDescriptor.java:69)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.lambda$executeRecursively$6(NodeTestTask.java:156)
	at org.junit.platform.engine.support.hierarchical.ThrowableCollector.execute(ThrowableCollector.java:73)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.lambda$executeRecursively$8(NodeTestTask.java:146)
	at org.junit.platform.engine.support.hierarchical.Node.around(Node.java:137)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.lambda$executeRecursively$9(NodeTestTask.java:144)
	at org.junit.platform.engine.support.hierarchical.ThrowableCollector.execute(ThrowableCollector.java:73)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.executeRecursively(NodeTestTask.java:143)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.execute(NodeTestTask.java:100)
	at java.base/java.util.ArrayList.forEach(ArrayList.java:1596)
	at org.junit.platform.engine.support.hierarchical.SameThreadHierarchicalTestExecutorService.invokeAll(SameThreadHierarchicalTestExecutorService.java:41)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.lambda$executeRecursively$6(NodeTestTask.java:160)
	at org.junit.platform.engine.support.hierarchical.ThrowableCollector.execute(ThrowableCollector.java:73)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.lambda$executeRecursively$8(NodeTestTask.java:146)
	at org.junit.platform.engine.support.hierarchical.Node.around(Node.java:137)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.lambda$executeRecursively$9(NodeTestTask.java:144)
	at org.junit.platform.engine.support.hierarchical.ThrowableCollector.execute(ThrowableCollector.java:73)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.executeRecursively(NodeTestTask.java:143)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.execute(NodeTestTask.java:100)
	at java.base/java.util.ArrayList.forEach(ArrayList.java:1596)
	at org.junit.platform.engine.support.hierarchical.SameThreadHierarchicalTestExecutorService.invokeAll(SameThreadHierarchicalTestExecutorService.java:41)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.lambda$executeRecursively$6(NodeTestTask.java:160)
	at org.junit.platform.engine.support.hierarchical.ThrowableCollector.execute(ThrowableCollector.java:73)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.lambda$executeRecursively$8(NodeTestTask.java:146)
	at org.junit.platform.engine.support.hierarchical.Node.around(Node.java:137)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.lambda$executeRecursively$9(NodeTestTask.java:144)
	at org.junit.platform.engine.support.hierarchical.ThrowableCollector.execute(ThrowableCollector.java:73)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.executeRecursively(NodeTestTask.java:143)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.execute(NodeTestTask.java:100)
	at org.junit.platform.engine.support.hierarchical.SameThreadHierarchicalTestExecutorService.submit(SameThreadHierarchicalTestExecutorService.java:35)
	at org.junit.platform.engine.support.hierarchical.HierarchicalTestExecutor.execute(HierarchicalTestExecutor.java:57)
	at org.junit.platform.engine.support.hierarchical.HierarchicalTestEngine.execute(HierarchicalTestEngine.java:54)
	at org.junit.platform.launcher.core.EngineExecutionOrchestrator.execute(EngineExecutionOrchestrator.java:201)
	at org.junit.platform.launcher.core.EngineExecutionOrchestrator.execute(EngineExecutionOrchestrator.java:170)
	at org.junit.platform.launcher.core.EngineExecutionOrchestrator.execute(EngineExecutionOrchestrator.java:94)
	at org.junit.platform.launcher.core.EngineExecutionOrchestrator.lambda$execute$0(EngineExecutionOrchestrator.java:59)
	at org.junit.platform.launcher.core.EngineExecutionOrchestrator.withInterceptedStreams(EngineExecutionOrchestrator.java:142)
	at org.junit.platform.launcher.core.EngineExecutionOrchestrator.execute(EngineExecutionOrchestrator.java:58)
	at org.junit.platform.launcher.core.DefaultLauncher.execute(DefaultLauncher.java:103)
	at org.junit.platform.launcher.core.DefaultLauncher.execute(DefaultLauncher.java:85)
	at org.junit.platform.launcher.core.DelegatingLauncher.execute(DelegatingLauncher.java:47)
	at org.junit.platform.launcher.core.InterceptingLauncher.lambda$execute$1(InterceptingLauncher.java:39)
	at org.junit.platform.launcher.core.ClasspathAlignmentCheckingLauncherInterceptor.intercept(ClasspathAlignmentCheckingLauncherInterceptor.java:25)
	at org.junit.platform.launcher.core.InterceptingLauncher.execute(InterceptingLauncher.java:38)
	at org.junit.platform.launcher.core.DelegatingLauncher.execute(DelegatingLauncher.java:47)
	at org.apache.maven.surefire.junitplatform.LazyLauncher.execute(LazyLauncher.java:56)
	at org.apache.maven.surefire.junitplatform.JUnitPlatformProvider.execute(JUnitPlatformProvider.java:184)
	at org.apache.maven.surefire.junitplatform.JUnitPlatformProvider.invokeAllTests(JUnitPlatformProvider.java:148)
	at org.apache.maven.surefire.junitplatform.JUnitPlatformProvider.invoke(JUnitPlatformProvider.java:122)
	at org.apache.maven.surefire.booter.ForkedBooter.runSuitesInProcess(ForkedBooter.java:385)
	at org.apache.maven.surefire.booter.ForkedBooter.execute(ForkedBooter.java:162)
	at org.apache.maven.surefire.booter.ForkedBooter.run(ForkedBooter.java:507)
	at org.apache.maven.surefire.booter.ForkedBooter.main(ForkedBooter.java:495)
23:13:30.508 [main] ERROR [] c.a.d.resolver.DocumentResolver - Business error uploading document from source: ftp://example.com/test.pdf [null]
com.ascentbusiness.dms_svc.exception.DmsBusinessException: Unable to access file from source: ftp://example.com/test.pdf. Ensure the file exists and is accessible, or provide a valid URL.
	at com.ascentbusiness.dms_svc.resolver.DocumentResolver.uploadDocumentFromPathOrUrl(DocumentResolver.java:767)
	at java.base/jdk.internal.reflect.DirectMethodHandleAccessor.invoke(DirectMethodHandleAccessor.java:103)
	at java.base/java.lang.reflect.Method.invoke(Method.java:580)
	at org.springframework.aop.support.AopUtils.invokeJoinpointUsingReflection(AopUtils.java:359)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.invokeJoinpoint(ReflectiveMethodInvocation.java:196)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:163)
	at org.springframework.aop.aspectj.MethodInvocationProceedingJoinPoint.proceed(MethodInvocationProceedingJoinPoint.java:89)
	at com.ascentbusiness.dms_svc.aspect.RateLimitAspect.rateLimit(RateLimitAspect.java:85)
	at java.base/jdk.internal.reflect.DirectMethodHandleAccessor.invoke(DirectMethodHandleAccessor.java:103)
	at java.base/java.lang.reflect.Method.invoke(Method.java:580)
	at org.springframework.aop.aspectj.AbstractAspectJAdvice.invokeAdviceMethodWithGivenArgs(AbstractAspectJAdvice.java:642)
	at org.springframework.aop.aspectj.AbstractAspectJAdvice.invokeAdviceMethod(AbstractAspectJAdvice.java:632)
	at org.springframework.aop.aspectj.AspectJAroundAdvice.invoke(AspectJAroundAdvice.java:71)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:173)
	at org.springframework.aop.aspectj.MethodInvocationProceedingJoinPoint.proceed(MethodInvocationProceedingJoinPoint.java:89)
	at com.ascentbusiness.dms_svc.aspect.CorrelationIdAspect.ensureCorrelationIdContext(CorrelationIdAspect.java:97)
	at com.ascentbusiness.dms_svc.aspect.CorrelationIdAspect.ensureCorrelationIdInResolvers(CorrelationIdAspect.java:45)
	at java.base/jdk.internal.reflect.DirectMethodHandleAccessor.invoke(DirectMethodHandleAccessor.java:103)
	at java.base/java.lang.reflect.Method.invoke(Method.java:580)
	at org.springframework.aop.aspectj.AbstractAspectJAdvice.invokeAdviceMethodWithGivenArgs(AbstractAspectJAdvice.java:642)
	at org.springframework.aop.aspectj.AbstractAspectJAdvice.invokeAdviceMethod(AbstractAspectJAdvice.java:632)
	at org.springframework.aop.aspectj.AspectJAroundAdvice.invoke(AspectJAroundAdvice.java:71)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:184)
	at org.springframework.aop.interceptor.ExposeInvocationInterceptor.invoke(ExposeInvocationInterceptor.java:97)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:184)
	at org.springframework.aop.framework.CglibAopProxy$DynamicAdvisedInterceptor.intercept(CglibAopProxy.java:728)
	at com.ascentbusiness.dms_svc.resolver.DocumentResolver$$SpringCGLIB$$0.uploadDocumentFromPathOrUrl(<generated>)
	at com.ascentbusiness.dms_svc.integration.UrlUploadIntegrationTest.lambda$1(UrlUploadIntegrationTest.java:175)
	at org.junit.jupiter.api.AssertThrows.assertThrows(AssertThrows.java:53)
	at org.junit.jupiter.api.AssertThrows.assertThrows(AssertThrows.java:35)
	at org.junit.jupiter.api.Assertions.assertThrows(Assertions.java:3128)
	at com.ascentbusiness.dms_svc.integration.UrlUploadIntegrationTest.testUploadFromUrl_ValidationFailure_InvalidProtocol(UrlUploadIntegrationTest.java:174)
	at java.base/jdk.internal.reflect.DirectMethodHandleAccessor.invoke(DirectMethodHandleAccessor.java:103)
	at java.base/java.lang.reflect.Method.invoke(Method.java:580)
	at org.junit.platform.commons.util.ReflectionUtils.invokeMethod(ReflectionUtils.java:775)
	at org.junit.platform.commons.support.ReflectionSupport.invokeMethod(ReflectionSupport.java:479)
	at org.junit.jupiter.engine.execution.MethodInvocation.proceed(MethodInvocation.java:60)
	at org.junit.jupiter.engine.execution.InvocationInterceptorChain$ValidatingInvocation.proceed(InvocationInterceptorChain.java:131)
	at org.junit.jupiter.engine.extension.TimeoutExtension.intercept(TimeoutExtension.java:161)
	at org.junit.jupiter.engine.extension.TimeoutExtension.interceptTestableMethod(TimeoutExtension.java:152)
	at org.junit.jupiter.engine.extension.TimeoutExtension.interceptTestMethod(TimeoutExtension.java:91)
	at org.junit.jupiter.engine.execution.InterceptingExecutableInvoker$ReflectiveInterceptorCall.lambda$ofVoidMethod$0(InterceptingExecutableInvoker.java:112)
	at org.junit.jupiter.engine.execution.InterceptingExecutableInvoker.lambda$invoke$0(InterceptingExecutableInvoker.java:94)
	at org.junit.jupiter.engine.execution.InvocationInterceptorChain$InterceptedInvocation.proceed(InvocationInterceptorChain.java:106)
	at org.junit.jupiter.engine.execution.InvocationInterceptorChain.proceed(InvocationInterceptorChain.java:64)
	at org.junit.jupiter.engine.execution.InvocationInterceptorChain.chainAndInvoke(InvocationInterceptorChain.java:45)
	at org.junit.jupiter.engine.execution.InvocationInterceptorChain.invoke(InvocationInterceptorChain.java:37)
	at org.junit.jupiter.engine.execution.InterceptingExecutableInvoker.invoke(InterceptingExecutableInvoker.java:93)
	at org.junit.jupiter.engine.execution.InterceptingExecutableInvoker.invoke(InterceptingExecutableInvoker.java:87)
	at org.junit.jupiter.engine.descriptor.TestMethodTestDescriptor.lambda$invokeTestMethod$7(TestMethodTestDescriptor.java:216)
	at org.junit.platform.engine.support.hierarchical.ThrowableCollector.execute(ThrowableCollector.java:73)
	at org.junit.jupiter.engine.descriptor.TestMethodTestDescriptor.invokeTestMethod(TestMethodTestDescriptor.java:212)
	at org.junit.jupiter.engine.descriptor.TestMethodTestDescriptor.execute(TestMethodTestDescriptor.java:137)
	at org.junit.jupiter.engine.descriptor.TestMethodTestDescriptor.execute(TestMethodTestDescriptor.java:69)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.lambda$executeRecursively$6(NodeTestTask.java:156)
	at org.junit.platform.engine.support.hierarchical.ThrowableCollector.execute(ThrowableCollector.java:73)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.lambda$executeRecursively$8(NodeTestTask.java:146)
	at org.junit.platform.engine.support.hierarchical.Node.around(Node.java:137)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.lambda$executeRecursively$9(NodeTestTask.java:144)
	at org.junit.platform.engine.support.hierarchical.ThrowableCollector.execute(ThrowableCollector.java:73)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.executeRecursively(NodeTestTask.java:143)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.execute(NodeTestTask.java:100)
	at java.base/java.util.ArrayList.forEach(ArrayList.java:1596)
	at org.junit.platform.engine.support.hierarchical.SameThreadHierarchicalTestExecutorService.invokeAll(SameThreadHierarchicalTestExecutorService.java:41)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.lambda$executeRecursively$6(NodeTestTask.java:160)
	at org.junit.platform.engine.support.hierarchical.ThrowableCollector.execute(ThrowableCollector.java:73)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.lambda$executeRecursively$8(NodeTestTask.java:146)
	at org.junit.platform.engine.support.hierarchical.Node.around(Node.java:137)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.lambda$executeRecursively$9(NodeTestTask.java:144)
	at org.junit.platform.engine.support.hierarchical.ThrowableCollector.execute(ThrowableCollector.java:73)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.executeRecursively(NodeTestTask.java:143)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.execute(NodeTestTask.java:100)
	at java.base/java.util.ArrayList.forEach(ArrayList.java:1596)
	at org.junit.platform.engine.support.hierarchical.SameThreadHierarchicalTestExecutorService.invokeAll(SameThreadHierarchicalTestExecutorService.java:41)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.lambda$executeRecursively$6(NodeTestTask.java:160)
	at org.junit.platform.engine.support.hierarchical.ThrowableCollector.execute(ThrowableCollector.java:73)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.lambda$executeRecursively$8(NodeTestTask.java:146)
	at org.junit.platform.engine.support.hierarchical.Node.around(Node.java:137)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.lambda$executeRecursively$9(NodeTestTask.java:144)
	at org.junit.platform.engine.support.hierarchical.ThrowableCollector.execute(ThrowableCollector.java:73)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.executeRecursively(NodeTestTask.java:143)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.execute(NodeTestTask.java:100)
	at org.junit.platform.engine.support.hierarchical.SameThreadHierarchicalTestExecutorService.submit(SameThreadHierarchicalTestExecutorService.java:35)
	at org.junit.platform.engine.support.hierarchical.HierarchicalTestExecutor.execute(HierarchicalTestExecutor.java:57)
	at org.junit.platform.engine.support.hierarchical.HierarchicalTestEngine.execute(HierarchicalTestEngine.java:54)
	at org.junit.platform.launcher.core.EngineExecutionOrchestrator.execute(EngineExecutionOrchestrator.java:201)
	at org.junit.platform.launcher.core.EngineExecutionOrchestrator.execute(EngineExecutionOrchestrator.java:170)
	at org.junit.platform.launcher.core.EngineExecutionOrchestrator.execute(EngineExecutionOrchestrator.java:94)
	at org.junit.platform.launcher.core.EngineExecutionOrchestrator.lambda$execute$0(EngineExecutionOrchestrator.java:59)
	at org.junit.platform.launcher.core.EngineExecutionOrchestrator.withInterceptedStreams(EngineExecutionOrchestrator.java:142)
	at org.junit.platform.launcher.core.EngineExecutionOrchestrator.execute(EngineExecutionOrchestrator.java:58)
	at org.junit.platform.launcher.core.DefaultLauncher.execute(DefaultLauncher.java:103)
	at org.junit.platform.launcher.core.DefaultLauncher.execute(DefaultLauncher.java:85)
	at org.junit.platform.launcher.core.DelegatingLauncher.execute(DelegatingLauncher.java:47)
	at org.junit.platform.launcher.core.InterceptingLauncher.lambda$execute$1(InterceptingLauncher.java:39)
	at org.junit.platform.launcher.core.ClasspathAlignmentCheckingLauncherInterceptor.intercept(ClasspathAlignmentCheckingLauncherInterceptor.java:25)
	at org.junit.platform.launcher.core.InterceptingLauncher.execute(InterceptingLauncher.java:38)
	at org.junit.platform.launcher.core.DelegatingLauncher.execute(DelegatingLauncher.java:47)
	at org.apache.maven.surefire.junitplatform.LazyLauncher.execute(LazyLauncher.java:56)
	at org.apache.maven.surefire.junitplatform.JUnitPlatformProvider.execute(JUnitPlatformProvider.java:184)
	at org.apache.maven.surefire.junitplatform.JUnitPlatformProvider.invokeAllTests(JUnitPlatformProvider.java:148)
	at org.apache.maven.surefire.junitplatform.JUnitPlatformProvider.invoke(JUnitPlatformProvider.java:122)
	at org.apache.maven.surefire.booter.ForkedBooter.runSuitesInProcess(ForkedBooter.java:385)
	at org.apache.maven.surefire.booter.ForkedBooter.execute(ForkedBooter.java:162)
	at org.apache.maven.surefire.booter.ForkedBooter.run(ForkedBooter.java:507)
	at org.apache.maven.surefire.booter.ForkedBooter.main(ForkedBooter.java:495)
23:13:30.510 [main] ERROR [] c.a.d.aspect.CorrelationIdAspect - CorrelationIdAspect: Error in RESOLVER layer for DocumentResolver.uploadDocumentFromPathOrUrl with correlation ID null: Unable to access file from source: ftp://example.com/test.pdf. Ensure the file exists and is accessible, or provide a valid URL.
com.ascentbusiness.dms_svc.exception.DmsBusinessException: Unable to access file from source: ftp://example.com/test.pdf. Ensure the file exists and is accessible, or provide a valid URL.
	at com.ascentbusiness.dms_svc.resolver.DocumentResolver.uploadDocumentFromPathOrUrl(DocumentResolver.java:767)
	at java.base/jdk.internal.reflect.DirectMethodHandleAccessor.invoke(DirectMethodHandleAccessor.java:103)
	at java.base/java.lang.reflect.Method.invoke(Method.java:580)
	at org.springframework.aop.support.AopUtils.invokeJoinpointUsingReflection(AopUtils.java:359)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.invokeJoinpoint(ReflectiveMethodInvocation.java:196)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:163)
	at org.springframework.aop.aspectj.MethodInvocationProceedingJoinPoint.proceed(MethodInvocationProceedingJoinPoint.java:89)
	at com.ascentbusiness.dms_svc.aspect.RateLimitAspect.rateLimit(RateLimitAspect.java:85)
	at java.base/jdk.internal.reflect.DirectMethodHandleAccessor.invoke(DirectMethodHandleAccessor.java:103)
	at java.base/java.lang.reflect.Method.invoke(Method.java:580)
	at org.springframework.aop.aspectj.AbstractAspectJAdvice.invokeAdviceMethodWithGivenArgs(AbstractAspectJAdvice.java:642)
	at org.springframework.aop.aspectj.AbstractAspectJAdvice.invokeAdviceMethod(AbstractAspectJAdvice.java:632)
	at org.springframework.aop.aspectj.AspectJAroundAdvice.invoke(AspectJAroundAdvice.java:71)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:173)
	at org.springframework.aop.aspectj.MethodInvocationProceedingJoinPoint.proceed(MethodInvocationProceedingJoinPoint.java:89)
	at com.ascentbusiness.dms_svc.aspect.CorrelationIdAspect.ensureCorrelationIdContext(CorrelationIdAspect.java:97)
	at com.ascentbusiness.dms_svc.aspect.CorrelationIdAspect.ensureCorrelationIdInResolvers(CorrelationIdAspect.java:45)
	at java.base/jdk.internal.reflect.DirectMethodHandleAccessor.invoke(DirectMethodHandleAccessor.java:103)
	at java.base/java.lang.reflect.Method.invoke(Method.java:580)
	at org.springframework.aop.aspectj.AbstractAspectJAdvice.invokeAdviceMethodWithGivenArgs(AbstractAspectJAdvice.java:642)
	at org.springframework.aop.aspectj.AbstractAspectJAdvice.invokeAdviceMethod(AbstractAspectJAdvice.java:632)
	at org.springframework.aop.aspectj.AspectJAroundAdvice.invoke(AspectJAroundAdvice.java:71)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:184)
	at org.springframework.aop.interceptor.ExposeInvocationInterceptor.invoke(ExposeInvocationInterceptor.java:97)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:184)
	at org.springframework.aop.framework.CglibAopProxy$DynamicAdvisedInterceptor.intercept(CglibAopProxy.java:728)
	at com.ascentbusiness.dms_svc.resolver.DocumentResolver$$SpringCGLIB$$0.uploadDocumentFromPathOrUrl(<generated>)
	at com.ascentbusiness.dms_svc.integration.UrlUploadIntegrationTest.lambda$1(UrlUploadIntegrationTest.java:175)
	at org.junit.jupiter.api.AssertThrows.assertThrows(AssertThrows.java:53)
	at org.junit.jupiter.api.AssertThrows.assertThrows(AssertThrows.java:35)
	at org.junit.jupiter.api.Assertions.assertThrows(Assertions.java:3128)
	at com.ascentbusiness.dms_svc.integration.UrlUploadIntegrationTest.testUploadFromUrl_ValidationFailure_InvalidProtocol(UrlUploadIntegrationTest.java:174)
	at java.base/jdk.internal.reflect.DirectMethodHandleAccessor.invoke(DirectMethodHandleAccessor.java:103)
	at java.base/java.lang.reflect.Method.invoke(Method.java:580)
	at org.junit.platform.commons.util.ReflectionUtils.invokeMethod(ReflectionUtils.java:775)
	at org.junit.platform.commons.support.ReflectionSupport.invokeMethod(ReflectionSupport.java:479)
	at org.junit.jupiter.engine.execution.MethodInvocation.proceed(MethodInvocation.java:60)
	at org.junit.jupiter.engine.execution.InvocationInterceptorChain$ValidatingInvocation.proceed(InvocationInterceptorChain.java:131)
	at org.junit.jupiter.engine.extension.TimeoutExtension.intercept(TimeoutExtension.java:161)
	at org.junit.jupiter.engine.extension.TimeoutExtension.interceptTestableMethod(TimeoutExtension.java:152)
	at org.junit.jupiter.engine.extension.TimeoutExtension.interceptTestMethod(TimeoutExtension.java:91)
	at org.junit.jupiter.engine.execution.InterceptingExecutableInvoker$ReflectiveInterceptorCall.lambda$ofVoidMethod$0(InterceptingExecutableInvoker.java:112)
	at org.junit.jupiter.engine.execution.InterceptingExecutableInvoker.lambda$invoke$0(InterceptingExecutableInvoker.java:94)
	at org.junit.jupiter.engine.execution.InvocationInterceptorChain$InterceptedInvocation.proceed(InvocationInterceptorChain.java:106)
	at org.junit.jupiter.engine.execution.InvocationInterceptorChain.proceed(InvocationInterceptorChain.java:64)
	at org.junit.jupiter.engine.execution.InvocationInterceptorChain.chainAndInvoke(InvocationInterceptorChain.java:45)
	at org.junit.jupiter.engine.execution.InvocationInterceptorChain.invoke(InvocationInterceptorChain.java:37)
	at org.junit.jupiter.engine.execution.InterceptingExecutableInvoker.invoke(InterceptingExecutableInvoker.java:93)
	at org.junit.jupiter.engine.execution.InterceptingExecutableInvoker.invoke(InterceptingExecutableInvoker.java:87)
	at org.junit.jupiter.engine.descriptor.TestMethodTestDescriptor.lambda$invokeTestMethod$7(TestMethodTestDescriptor.java:216)
	at org.junit.platform.engine.support.hierarchical.ThrowableCollector.execute(ThrowableCollector.java:73)
	at org.junit.jupiter.engine.descriptor.TestMethodTestDescriptor.invokeTestMethod(TestMethodTestDescriptor.java:212)
	at org.junit.jupiter.engine.descriptor.TestMethodTestDescriptor.execute(TestMethodTestDescriptor.java:137)
	at org.junit.jupiter.engine.descriptor.TestMethodTestDescriptor.execute(TestMethodTestDescriptor.java:69)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.lambda$executeRecursively$6(NodeTestTask.java:156)
	at org.junit.platform.engine.support.hierarchical.ThrowableCollector.execute(ThrowableCollector.java:73)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.lambda$executeRecursively$8(NodeTestTask.java:146)
	at org.junit.platform.engine.support.hierarchical.Node.around(Node.java:137)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.lambda$executeRecursively$9(NodeTestTask.java:144)
	at org.junit.platform.engine.support.hierarchical.ThrowableCollector.execute(ThrowableCollector.java:73)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.executeRecursively(NodeTestTask.java:143)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.execute(NodeTestTask.java:100)
	at java.base/java.util.ArrayList.forEach(ArrayList.java:1596)
	at org.junit.platform.engine.support.hierarchical.SameThreadHierarchicalTestExecutorService.invokeAll(SameThreadHierarchicalTestExecutorService.java:41)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.lambda$executeRecursively$6(NodeTestTask.java:160)
	at org.junit.platform.engine.support.hierarchical.ThrowableCollector.execute(ThrowableCollector.java:73)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.lambda$executeRecursively$8(NodeTestTask.java:146)
	at org.junit.platform.engine.support.hierarchical.Node.around(Node.java:137)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.lambda$executeRecursively$9(NodeTestTask.java:144)
	at org.junit.platform.engine.support.hierarchical.ThrowableCollector.execute(ThrowableCollector.java:73)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.executeRecursively(NodeTestTask.java:143)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.execute(NodeTestTask.java:100)
	at java.base/java.util.ArrayList.forEach(ArrayList.java:1596)
	at org.junit.platform.engine.support.hierarchical.SameThreadHierarchicalTestExecutorService.invokeAll(SameThreadHierarchicalTestExecutorService.java:41)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.lambda$executeRecursively$6(NodeTestTask.java:160)
	at org.junit.platform.engine.support.hierarchical.ThrowableCollector.execute(ThrowableCollector.java:73)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.lambda$executeRecursively$8(NodeTestTask.java:146)
	at org.junit.platform.engine.support.hierarchical.Node.around(Node.java:137)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.lambda$executeRecursively$9(NodeTestTask.java:144)
	at org.junit.platform.engine.support.hierarchical.ThrowableCollector.execute(ThrowableCollector.java:73)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.executeRecursively(NodeTestTask.java:143)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.execute(NodeTestTask.java:100)
	at org.junit.platform.engine.support.hierarchical.SameThreadHierarchicalTestExecutorService.submit(SameThreadHierarchicalTestExecutorService.java:35)
	at org.junit.platform.engine.support.hierarchical.HierarchicalTestExecutor.execute(HierarchicalTestExecutor.java:57)
	at org.junit.platform.engine.support.hierarchical.HierarchicalTestEngine.execute(HierarchicalTestEngine.java:54)
	at org.junit.platform.launcher.core.EngineExecutionOrchestrator.execute(EngineExecutionOrchestrator.java:201)
	at org.junit.platform.launcher.core.EngineExecutionOrchestrator.execute(EngineExecutionOrchestrator.java:170)
	at org.junit.platform.launcher.core.EngineExecutionOrchestrator.execute(EngineExecutionOrchestrator.java:94)
	at org.junit.platform.launcher.core.EngineExecutionOrchestrator.lambda$execute$0(EngineExecutionOrchestrator.java:59)
	at org.junit.platform.launcher.core.EngineExecutionOrchestrator.withInterceptedStreams(EngineExecutionOrchestrator.java:142)
	at org.junit.platform.launcher.core.EngineExecutionOrchestrator.execute(EngineExecutionOrchestrator.java:58)
	at org.junit.platform.launcher.core.DefaultLauncher.execute(DefaultLauncher.java:103)
	at org.junit.platform.launcher.core.DefaultLauncher.execute(DefaultLauncher.java:85)
	at org.junit.platform.launcher.core.DelegatingLauncher.execute(DelegatingLauncher.java:47)
	at org.junit.platform.launcher.core.InterceptingLauncher.lambda$execute$1(InterceptingLauncher.java:39)
	at org.junit.platform.launcher.core.ClasspathAlignmentCheckingLauncherInterceptor.intercept(ClasspathAlignmentCheckingLauncherInterceptor.java:25)
	at org.junit.platform.launcher.core.InterceptingLauncher.execute(InterceptingLauncher.java:38)
	at org.junit.platform.launcher.core.DelegatingLauncher.execute(DelegatingLauncher.java:47)
	at org.apache.maven.surefire.junitplatform.LazyLauncher.execute(LazyLauncher.java:56)
	at org.apache.maven.surefire.junitplatform.JUnitPlatformProvider.execute(JUnitPlatformProvider.java:184)
	at org.apache.maven.surefire.junitplatform.JUnitPlatformProvider.invokeAllTests(JUnitPlatformProvider.java:148)
	at org.apache.maven.surefire.junitplatform.JUnitPlatformProvider.invoke(JUnitPlatformProvider.java:122)
	at org.apache.maven.surefire.booter.ForkedBooter.runSuitesInProcess(ForkedBooter.java:385)
	at org.apache.maven.surefire.booter.ForkedBooter.execute(ForkedBooter.java:162)
	at org.apache.maven.surefire.booter.ForkedBooter.run(ForkedBooter.java:507)
	at org.apache.maven.surefire.booter.ForkedBooter.main(ForkedBooter.java:495)
]]></system-out>
  </testcase>
  <testcase name="testUrlUploadConfig_ContentTypeValidation" classname="com.ascentbusiness.dms_svc.integration.UrlUploadIntegrationTest" time="0.029"/>
  <testcase name="testUploadFromPathOrUrl_SourceTypeDetection" classname="com.ascentbusiness.dms_svc.integration.UrlUploadIntegrationTest" time="28.601"/>
  <testcase name="testUrlUploadConfig_DomainValidation" classname="com.ascentbusiness.dms_svc.integration.UrlUploadIntegrationTest" time="0.011"/>
  <testcase name="testUploadFromLocalPath_FileTooLarge" classname="com.ascentbusiness.dms_svc.integration.UrlUploadIntegrationTest" time="0.009"/>
  <testcase name="testUploadFromLocalPath_Success" classname="com.ascentbusiness.dms_svc.integration.UrlUploadIntegrationTest" time="0.012">
    <system-out><![CDATA[23:13:59.186 [main] DEBUG [] c.a.dms_svc.aspect.RateLimitAspect - Rate limit check passed for type: DOCUMENT_UPLOAD, count: 4/10, window: 300s
23:13:59.186 [main] INFO  [] c.a.d.resolver.DocumentResolver - GraphQL uploadDocumentFromPathOrUrl called with source: C:\Users\<USER>\AppData\Local\Temp\integration-test140227448552942050.txt [null]
23:13:59.186 [main] INFO  [] c.a.d.resolver.DocumentResolver - Reading file from local path: C:\Users\<USER>\AppData\Local\Temp\integration-test140227448552942050.txt [null]
23:13:59.187 [main] INFO  [] c.a.d.service.UrlDownloadService - Reading file from path: C:\Users\<USER>\AppData\Local\Temp\integration-test140227448552942050.txt [null]
23:13:59.188 [main] INFO  [] c.a.d.service.UrlDownloadService - Successfully read file from path: C:\Users\<USER>\AppData\Local\Temp\integration-test140227448552942050.txt -> 52 bytes [null]
23:13:59.189 [main] INFO  [] c.a.d.resolver.DocumentResolver - Successfully uploaded document from source: C:\Users\<USER>\AppData\Local\Temp\integration-test140227448552942050.txt -> Document ID: 1 [null]
]]></system-out>
  </testcase>
  <testcase name="testUploadFromUrl_ValidationFailure_UrlDisabled" classname="com.ascentbusiness.dms_svc.integration.UrlUploadIntegrationTest" time="0.007"/>
  <testcase name="testUploadFromPathOrUrl_InputValidation" classname="com.ascentbusiness.dms_svc.integration.UrlUploadIntegrationTest" time="0.01"/>
</testsuite>