<?xml version="1.0" encoding="UTF-8"?><!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Strict//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-strict.dtd"><html xmlns="http://www.w3.org/1999/xhtml" lang="en"><head><meta http-equiv="Content-Type" content="text/html;charset=UTF-8"/><link rel="stylesheet" href="../jacoco-resources/report.css" type="text/css"/><link rel="shortcut icon" href="../jacoco-resources/report.gif" type="image/gif"/><title>WorkflowInstanceResolver.java</title><link rel="stylesheet" href="../jacoco-resources/prettify.css" type="text/css"/><script type="text/javascript" src="../jacoco-resources/prettify.js"></script></head><body onload="window['PR_TAB_WIDTH']=4;prettyPrint()"><div class="breadcrumb" id="breadcrumb"><span class="info"><a href="../jacoco-sessions.html" class="el_session">Sessions</a></span><a href="../index.html" class="el_report">dms-svc</a> &gt; <a href="index.source.html" class="el_package">com.ascentbusiness.dms_svc.resolver</a> &gt; <span class="el_source">WorkflowInstanceResolver.java</span></div><h1>WorkflowInstanceResolver.java</h1><pre class="source lang-java linenums">package com.ascentbusiness.dms_svc.resolver;

import com.ascentbusiness.dms_svc.entity.WorkflowInstance;
import com.ascentbusiness.dms_svc.entity.WorkflowTask;
import com.ascentbusiness.dms_svc.entity.WorkflowHistory;
import com.ascentbusiness.dms_svc.entity.WorkflowDefinition;
import com.ascentbusiness.dms_svc.entity.WorkflowStage;
import com.ascentbusiness.dms_svc.entity.Document;
import com.ascentbusiness.dms_svc.enums.WorkflowStatus;
import com.ascentbusiness.dms_svc.enums.WorkflowAction;
import com.ascentbusiness.dms_svc.service.WorkflowInstanceService;
import com.ascentbusiness.dms_svc.security.UserContext;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Sort;
import org.springframework.graphql.data.method.annotation.Argument;
import org.springframework.graphql.data.method.annotation.MutationMapping;
import org.springframework.graphql.data.method.annotation.QueryMapping;
import org.springframework.graphql.data.method.annotation.SchemaMapping;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.stereotype.Controller;

import java.time.LocalDateTime;
import java.util.List;

/**
 * GraphQL resolver for WorkflowInstance operations
 */
@Controller
<span class="fc" id="L33">@RequiredArgsConstructor</span>
<span class="fc" id="L34">@Slf4j</span>
public class WorkflowInstanceResolver {

    private final WorkflowInstanceService workflowInstanceService;
    private final UserContext userContext;

    // ===== QUERIES =====

    @QueryMapping
    @PreAuthorize(&quot;hasAnyRole('ADMIN', 'MANAGER', 'USER')&quot;)
    public WorkflowInstance getWorkflowInstance(@Argument Long id) {
<span class="nc" id="L45">        log.info(&quot;Getting workflow instance with ID: {}&quot;, id);</span>
<span class="nc" id="L46">        return workflowInstanceService.getWorkflowInstanceById(id);</span>
    }

    @QueryMapping
    @PreAuthorize(&quot;hasAnyRole('ADMIN', 'MANAGER', 'USER')&quot;)
    public WorkflowInstancePage getWorkflowInstances(@Argument WorkflowPaginationInput pagination) {
<span class="nc" id="L52">        log.info(&quot;Getting workflow instances with pagination: {}&quot;, pagination);</span>
        
<span class="nc" id="L54">        Pageable pageable = createPageable(pagination);</span>
<span class="nc" id="L55">        Page&lt;WorkflowInstance&gt; page = workflowInstanceService.getWorkflowInstances(pageable);</span>
        
<span class="nc" id="L57">        return WorkflowInstancePage.builder()</span>
<span class="nc" id="L58">                .content(page.getContent())</span>
<span class="nc" id="L59">                .totalElements((int) page.getTotalElements())</span>
<span class="nc" id="L60">                .totalPages(page.getTotalPages())</span>
<span class="nc" id="L61">                .size(page.getSize())</span>
<span class="nc" id="L62">                .number(page.getNumber())</span>
<span class="nc" id="L63">                .first(page.isFirst())</span>
<span class="nc" id="L64">                .last(page.isLast())</span>
<span class="nc" id="L65">                .build();</span>
    }

    @QueryMapping
    @PreAuthorize(&quot;hasAnyRole('ADMIN', 'MANAGER', 'USER')&quot;)
    public List&lt;WorkflowInstance&gt; getActiveWorkflowInstances() {
<span class="nc" id="L71">        log.info(&quot;Getting active workflow instances&quot;);</span>
<span class="nc" id="L72">        return workflowInstanceService.getActiveWorkflowInstances();</span>
    }

    @QueryMapping
    @PreAuthorize(&quot;hasAnyRole('ADMIN', 'MANAGER', 'USER')&quot;)
    public List&lt;WorkflowInstance&gt; getWorkflowInstancesByStatus(@Argument WorkflowStatus status) {
<span class="nc" id="L78">        log.info(&quot;Getting workflow instances by status: {}&quot;, status);</span>
<span class="nc" id="L79">        return workflowInstanceService.getWorkflowInstancesByStatus(status);</span>
    }

    @QueryMapping
    @PreAuthorize(&quot;hasAnyRole('ADMIN', 'MANAGER', 'USER')&quot;)
    public WorkflowInstancePage getWorkflowInstancesByInitiator(@Argument String initiatorUserId, @Argument WorkflowPaginationInput pagination) {
<span class="nc" id="L85">        log.info(&quot;Getting workflow instances by initiator: {}&quot;, initiatorUserId);</span>
<span class="nc bnc" id="L86" title="All 2 branches missed.">        String actualInitiator = initiatorUserId != null ? initiatorUserId : userContext.getUserId();</span>
<span class="nc" id="L87">        Pageable pageable = createPageable(pagination);</span>
<span class="nc" id="L88">        Page&lt;WorkflowInstance&gt; page = workflowInstanceService.getWorkflowInstancesByInitiator(actualInitiator, pageable);</span>
        
<span class="nc" id="L90">        return WorkflowInstancePage.builder()</span>
<span class="nc" id="L91">                .content(page.getContent())</span>
<span class="nc" id="L92">                .totalElements((int) page.getTotalElements())</span>
<span class="nc" id="L93">                .totalPages(page.getTotalPages())</span>
<span class="nc" id="L94">                .size(page.getSize())</span>
<span class="nc" id="L95">                .number(page.getNumber())</span>
<span class="nc" id="L96">                .first(page.isFirst())</span>
<span class="nc" id="L97">                .last(page.isLast())</span>
<span class="nc" id="L98">                .build();</span>
    }

    @QueryMapping
    @PreAuthorize(&quot;hasAnyRole('ADMIN', 'MANAGER', 'USER')&quot;)
    public List&lt;WorkflowInstance&gt; getWorkflowInstancesForDefinition(@Argument Long workflowDefinitionId) {
<span class="nc" id="L104">        log.info(&quot;Getting workflow instances for definition: {}&quot;, workflowDefinitionId);</span>
<span class="nc" id="L105">        return workflowInstanceService.getWorkflowInstancesForDefinition(workflowDefinitionId);</span>
    }

    @QueryMapping
    @PreAuthorize(&quot;hasAnyRole('ADMIN', 'MANAGER', 'USER')&quot;)
    public List&lt;WorkflowInstance&gt; getWorkflowInstancesForDocument(@Argument Long documentId) {
<span class="nc" id="L111">        log.info(&quot;Getting workflow instances for document: {}&quot;, documentId);</span>
<span class="nc" id="L112">        return workflowInstanceService.getWorkflowInstancesForDocument(documentId);</span>
    }

    @QueryMapping
    @PreAuthorize(&quot;hasAnyRole('ADMIN', 'MANAGER', 'USER')&quot;)
    public List&lt;WorkflowInstance&gt; getOverdueWorkflowInstances() {
<span class="nc" id="L118">        log.info(&quot;Getting overdue workflow instances&quot;);</span>
<span class="nc" id="L119">        return workflowInstanceService.getOverdueWorkflowInstances();</span>
    }

    @QueryMapping
    @PreAuthorize(&quot;hasAnyRole('ADMIN', 'MANAGER', 'USER')&quot;)
    public List&lt;WorkflowInstance&gt; getWorkflowInstancesDueWithin(@Argument Integer hours) {
<span class="nc" id="L125">        log.info(&quot;Getting workflow instances due within {} hours&quot;, hours);</span>
<span class="nc bnc" id="L126" title="All 2 branches missed.">        int actualHours = hours != null ? hours : 24;</span>
<span class="nc" id="L127">        return workflowInstanceService.getWorkflowInstancesDueWithin(actualHours);</span>
    }

    @QueryMapping
    @PreAuthorize(&quot;hasAnyRole('ADMIN', 'MANAGER')&quot;)
    public WorkflowInstanceService.WorkflowStatistics getWorkflowStatistics() {
<span class="nc" id="L133">        log.info(&quot;Getting workflow statistics&quot;);</span>
<span class="nc" id="L134">        return workflowInstanceService.getWorkflowStatistics();</span>
    }

    // ===== MUTATIONS =====

    @MutationMapping
    @PreAuthorize(&quot;hasAnyRole('ADMIN', 'MANAGER', 'USER')&quot;)
    public WorkflowInstance startWorkflow(@Argument StartWorkflowInput input) {
<span class="nc" id="L142">        log.info(&quot;Starting workflow for document: {} with definition: {}&quot;, input.getDocumentId(), input.getWorkflowDefinitionId());</span>
        
<span class="nc" id="L144">        return workflowInstanceService.startWorkflow(</span>
<span class="nc" id="L145">                input.getDocumentId(),</span>
<span class="nc" id="L146">                input.getWorkflowDefinitionId(),</span>
<span class="nc" id="L147">                userContext.getUserId(),</span>
<span class="nc" id="L148">                input.getPriority(),</span>
<span class="nc" id="L149">                input.getDueDate(),</span>
<span class="nc" id="L150">                input.getComments()</span>
        );
    }

    @MutationMapping
    @PreAuthorize(&quot;hasAnyRole('ADMIN', 'MANAGER', 'USER')&quot;)
    public WorkflowInstance completeTask(@Argument CompleteTaskInput input) {
<span class="nc" id="L157">        log.info(&quot;Completing task: {} with action: {}&quot;, input.getTaskId(), input.getAction());</span>
        
<span class="nc" id="L159">        return workflowInstanceService.completeTask(</span>
<span class="nc" id="L160">                input.getTaskId(),</span>
<span class="nc" id="L161">                userContext.getUserId(),</span>
<span class="nc" id="L162">                input.getAction(),</span>
<span class="nc" id="L163">                input.getComments(),</span>
<span class="nc" id="L164">                input.getDelegateToUserId()</span>
        );
    }

    @MutationMapping
    @PreAuthorize(&quot;hasAnyRole('ADMIN', 'MANAGER', 'USER')&quot;)
    public WorkflowInstance cancelWorkflow(@Argument Long workflowInstanceId, @Argument String reason) {
<span class="nc" id="L171">        log.info(&quot;Cancelling workflow instance: {} with reason: {}&quot;, workflowInstanceId, reason);</span>
<span class="nc" id="L172">        return workflowInstanceService.cancelWorkflowByUser(workflowInstanceId, userContext.getUserId(), reason);</span>
    }

    @MutationMapping
    @PreAuthorize(&quot;hasAnyRole('ADMIN', 'MANAGER')&quot;)
    public WorkflowInstance suspendWorkflow(@Argument Long workflowInstanceId, @Argument String reason) {
<span class="nc" id="L178">        log.info(&quot;Suspending workflow instance: {} with reason: {}&quot;, workflowInstanceId, reason);</span>
<span class="nc" id="L179">        return workflowInstanceService.suspendWorkflow(workflowInstanceId, userContext.getUserId(), reason);</span>
    }

    @MutationMapping
    @PreAuthorize(&quot;hasAnyRole('ADMIN', 'MANAGER')&quot;)
    public WorkflowInstance resumeWorkflow(@Argument Long workflowInstanceId, @Argument String reason) {
<span class="nc" id="L185">        log.info(&quot;Resuming workflow instance: {} with reason: {}&quot;, workflowInstanceId, reason);</span>
<span class="nc" id="L186">        return workflowInstanceService.resumeWorkflow(workflowInstanceId, userContext.getUserId(), reason);</span>
    }

    @MutationMapping
    @PreAuthorize(&quot;hasAnyRole('ADMIN', 'MANAGER', 'USER')&quot;)
    public WorkflowTask reassignTask(@Argument Long taskId, @Argument String newAssigneeUserId) {
<span class="nc" id="L192">        log.info(&quot;Reassigning task: {} to user: {}&quot;, taskId, newAssigneeUserId);</span>
<span class="nc" id="L193">        return workflowInstanceService.reassignTask(taskId, newAssigneeUserId, userContext.getUserId());</span>
    }

    @MutationMapping
    @PreAuthorize(&quot;hasAnyRole('ADMIN', 'MANAGER', 'USER')&quot;)
    public WorkflowTask escalateTask(@Argument Long taskId, @Argument String escalateToUserId, @Argument String reason) {
<span class="nc" id="L199">        log.info(&quot;Escalating task: {} to user: {} with reason: {}&quot;, taskId, escalateToUserId, reason);</span>
<span class="nc" id="L200">        return workflowInstanceService.escalateTask(taskId, escalateToUserId, userContext.getUserId(), reason);</span>
    }

    // ===== FIELD RESOLVERS =====

    @SchemaMapping(typeName = &quot;WorkflowInstance&quot;, field = &quot;workflowDefinition&quot;)
    public WorkflowDefinition getWorkflowDefinition(WorkflowInstance workflowInstance) {
<span class="nc" id="L207">        return workflowInstance.getWorkflowDefinition();</span>
    }

    @SchemaMapping(typeName = &quot;WorkflowInstance&quot;, field = &quot;document&quot;)
    public Document getDocument(WorkflowInstance workflowInstance) {
<span class="nc" id="L212">        return workflowInstance.getDocument();</span>
    }

    @SchemaMapping(typeName = &quot;WorkflowInstance&quot;, field = &quot;currentStage&quot;)
    public WorkflowStage getCurrentStage(WorkflowInstance workflowInstance) {
<span class="nc" id="L217">        return workflowInstance.getCurrentStage();</span>
    }

    @SchemaMapping(typeName = &quot;WorkflowInstance&quot;, field = &quot;tasks&quot;)
    public List&lt;WorkflowTask&gt; getTasks(WorkflowInstance workflowInstance) {
<span class="nc bnc" id="L222" title="All 2 branches missed.">        return workflowInstance.getTasks() != null ? </span>
<span class="nc" id="L223">               workflowInstance.getTasks().stream().toList() : List.of();</span>
    }

    @SchemaMapping(typeName = &quot;WorkflowInstance&quot;, field = &quot;history&quot;)
    public List&lt;WorkflowHistory&gt; getHistory(WorkflowInstance workflowInstance) {
<span class="nc bnc" id="L228" title="All 2 branches missed.">        return workflowInstance.getHistory() != null ? </span>
<span class="nc" id="L229">               workflowInstance.getHistory().stream().toList() : List.of();</span>
    }

    @SchemaMapping(typeName = &quot;WorkflowInstance&quot;, field = &quot;isActive&quot;)
    public Boolean getIsActive(WorkflowInstance workflowInstance) {
<span class="nc" id="L234">        return workflowInstance.isActive();</span>
    }

    @SchemaMapping(typeName = &quot;WorkflowInstance&quot;, field = &quot;isCompleted&quot;)
    public Boolean getIsCompleted(WorkflowInstance workflowInstance) {
<span class="nc" id="L239">        return workflowInstance.isCompleted();</span>
    }

    @SchemaMapping(typeName = &quot;WorkflowInstance&quot;, field = &quot;isOverdue&quot;)
    public Boolean getIsOverdue(WorkflowInstance workflowInstance) {
<span class="nc" id="L244">        return workflowInstance.isOverdue();</span>
    }

    @SchemaMapping(typeName = &quot;WorkflowInstance&quot;, field = &quot;durationHours&quot;)
    public Long getDurationHours(WorkflowInstance workflowInstance) {
<span class="nc" id="L249">        return workflowInstance.getDurationHours();</span>
    }

    @SchemaMapping(typeName = &quot;WorkflowInstance&quot;, field = &quot;pendingTaskCount&quot;)
    public Long getPendingTaskCount(WorkflowInstance workflowInstance) {
<span class="nc" id="L254">        return workflowInstance.getPendingTaskCount();</span>
    }

    @SchemaMapping(typeName = &quot;WorkflowInstance&quot;, field = &quot;completedTaskCount&quot;)
    public Long getCompletedTaskCount(WorkflowInstance workflowInstance) {
<span class="nc" id="L259">        return workflowInstance.getCompletedTaskCount();</span>
    }

    @SchemaMapping(typeName = &quot;WorkflowInstance&quot;, field = &quot;completionPercentage&quot;)
    public Double getCompletionPercentage(WorkflowInstance workflowInstance) {
<span class="nc" id="L264">        return workflowInstance.getCompletionPercentage();</span>
    }

    // ===== HELPER METHODS =====

    private Pageable createPageable(WorkflowPaginationInput pagination) {
<span class="nc bnc" id="L270" title="All 2 branches missed.">        if (pagination == null) {</span>
<span class="nc" id="L271">            return PageRequest.of(0, 10, Sort.by(Sort.Direction.DESC, &quot;startedDate&quot;));</span>
        }
        
<span class="nc bnc" id="L274" title="All 2 branches missed.">        Sort.Direction direction = &quot;ASC&quot;.equalsIgnoreCase(pagination.getSortDirection()) ? </span>
<span class="nc" id="L275">                                  Sort.Direction.ASC : Sort.Direction.DESC;</span>
<span class="nc bnc" id="L276" title="All 2 branches missed.">        Sort sort = Sort.by(direction, pagination.getSortBy() != null ? pagination.getSortBy() : &quot;startedDate&quot;);</span>
        
<span class="nc" id="L278">        return PageRequest.of(</span>
<span class="nc bnc" id="L279" title="All 2 branches missed.">                pagination.getPage() != null ? pagination.getPage() : 0,</span>
<span class="nc bnc" id="L280" title="All 2 branches missed.">                pagination.getSize() != null ? pagination.getSize() : 10,</span>
                sort
        );
    }

    // ===== INPUT/OUTPUT CLASSES =====

<span class="nc" id="L287">    public static class StartWorkflowInput {</span>
        private Long documentId;
        private Long workflowDefinitionId;
        private String priority;
        private LocalDateTime dueDate;
        private String comments;
        
        // Getters and setters
<span class="nc" id="L295">        public Long getDocumentId() { return documentId; }</span>
<span class="nc" id="L296">        public void setDocumentId(Long documentId) { this.documentId = documentId; }</span>
<span class="nc" id="L297">        public Long getWorkflowDefinitionId() { return workflowDefinitionId; }</span>
<span class="nc" id="L298">        public void setWorkflowDefinitionId(Long workflowDefinitionId) { this.workflowDefinitionId = workflowDefinitionId; }</span>
<span class="nc" id="L299">        public String getPriority() { return priority; }</span>
<span class="nc" id="L300">        public void setPriority(String priority) { this.priority = priority; }</span>
<span class="nc" id="L301">        public LocalDateTime getDueDate() { return dueDate; }</span>
<span class="nc" id="L302">        public void setDueDate(LocalDateTime dueDate) { this.dueDate = dueDate; }</span>
<span class="nc" id="L303">        public String getComments() { return comments; }</span>
<span class="nc" id="L304">        public void setComments(String comments) { this.comments = comments; }</span>
    }

<span class="nc" id="L307">    public static class CompleteTaskInput {</span>
        private Long taskId;
        private WorkflowAction action;
        private String comments;
        private String delegateToUserId;
        
        // Getters and setters
<span class="nc" id="L314">        public Long getTaskId() { return taskId; }</span>
<span class="nc" id="L315">        public void setTaskId(Long taskId) { this.taskId = taskId; }</span>
<span class="nc" id="L316">        public WorkflowAction getAction() { return action; }</span>
<span class="nc" id="L317">        public void setAction(WorkflowAction action) { this.action = action; }</span>
<span class="nc" id="L318">        public String getComments() { return comments; }</span>
<span class="nc" id="L319">        public void setComments(String comments) { this.comments = comments; }</span>
<span class="nc" id="L320">        public String getDelegateToUserId() { return delegateToUserId; }</span>
<span class="nc" id="L321">        public void setDelegateToUserId(String delegateToUserId) { this.delegateToUserId = delegateToUserId; }</span>
    }

<span class="nc" id="L324">    public static class WorkflowPaginationInput {</span>
        private Integer page;
        private Integer size;
        private String sortBy;
        private String sortDirection;
        
        // Getters and setters
<span class="nc" id="L331">        public Integer getPage() { return page; }</span>
<span class="nc" id="L332">        public void setPage(Integer page) { this.page = page; }</span>
<span class="nc" id="L333">        public Integer getSize() { return size; }</span>
<span class="nc" id="L334">        public void setSize(Integer size) { this.size = size; }</span>
<span class="nc" id="L335">        public String getSortBy() { return sortBy; }</span>
<span class="nc" id="L336">        public void setSortBy(String sortBy) { this.sortBy = sortBy; }</span>
<span class="nc" id="L337">        public String getSortDirection() { return sortDirection; }</span>
<span class="nc" id="L338">        public void setSortDirection(String sortDirection) { this.sortDirection = sortDirection; }</span>
    }

<span class="nc" id="L341">    public static class WorkflowInstancePage {</span>
        private List&lt;WorkflowInstance&gt; content;
        private Integer totalElements;
        private Integer totalPages;
        private Integer size;
        private Integer number;
        private Boolean first;
        private Boolean last;

        public static WorkflowInstancePageBuilder builder() {
<span class="nc" id="L351">            return new WorkflowInstancePageBuilder();</span>
        }

<span class="nc" id="L354">        public static class WorkflowInstancePageBuilder {</span>
            private List&lt;WorkflowInstance&gt; content;
            private Integer totalElements;
            private Integer totalPages;
            private Integer size;
            private Integer number;
            private Boolean first;
            private Boolean last;

            public WorkflowInstancePageBuilder content(List&lt;WorkflowInstance&gt; content) {
<span class="nc" id="L364">                this.content = content;</span>
<span class="nc" id="L365">                return this;</span>
            }

            public WorkflowInstancePageBuilder totalElements(Integer totalElements) {
<span class="nc" id="L369">                this.totalElements = totalElements;</span>
<span class="nc" id="L370">                return this;</span>
            }

            public WorkflowInstancePageBuilder totalPages(Integer totalPages) {
<span class="nc" id="L374">                this.totalPages = totalPages;</span>
<span class="nc" id="L375">                return this;</span>
            }

            public WorkflowInstancePageBuilder size(Integer size) {
<span class="nc" id="L379">                this.size = size;</span>
<span class="nc" id="L380">                return this;</span>
            }

            public WorkflowInstancePageBuilder number(Integer number) {
<span class="nc" id="L384">                this.number = number;</span>
<span class="nc" id="L385">                return this;</span>
            }

            public WorkflowInstancePageBuilder first(Boolean first) {
<span class="nc" id="L389">                this.first = first;</span>
<span class="nc" id="L390">                return this;</span>
            }

            public WorkflowInstancePageBuilder last(Boolean last) {
<span class="nc" id="L394">                this.last = last;</span>
<span class="nc" id="L395">                return this;</span>
            }

            public WorkflowInstancePage build() {
<span class="nc" id="L399">                WorkflowInstancePage page = new WorkflowInstancePage();</span>
<span class="nc" id="L400">                page.content = this.content;</span>
<span class="nc" id="L401">                page.totalElements = this.totalElements;</span>
<span class="nc" id="L402">                page.totalPages = this.totalPages;</span>
<span class="nc" id="L403">                page.size = this.size;</span>
<span class="nc" id="L404">                page.number = this.number;</span>
<span class="nc" id="L405">                page.first = this.first;</span>
<span class="nc" id="L406">                page.last = this.last;</span>
<span class="nc" id="L407">                return page;</span>
            }
        }

        // Getters
<span class="nc" id="L412">        public List&lt;WorkflowInstance&gt; getContent() { return content; }</span>
<span class="nc" id="L413">        public Integer getTotalElements() { return totalElements; }</span>
<span class="nc" id="L414">        public Integer getTotalPages() { return totalPages; }</span>
<span class="nc" id="L415">        public Integer getSize() { return size; }</span>
<span class="nc" id="L416">        public Integer getNumber() { return number; }</span>
<span class="nc" id="L417">        public Boolean getFirst() { return first; }</span>
<span class="nc" id="L418">        public Boolean getLast() { return last; }</span>
    }
}
</pre><div class="footer"><span class="right">Created with <a href="http://www.jacoco.org/jacoco">JaCoCo</a> 0.8.10.202304240956</span></div></body></html>