# GRC Platform Services Docker Compose
# This file contains the application services that use shared infrastructure
version: '3.8'

services:
  # DMS Application Service
  dms-svc:
    build:
      context: ./dms-svc
      dockerfile: Dockerfile
      target: runtime
    container_name: dms-svc
    ports:
      - "${DMS_PORT:-9093}:9093"
      - "${DMS_METRICS_PORT:-9464}:9464"  # Prometheus metrics endpoint
    environment:
      - SPRING_PROFILES_ACTIVE=dev
      - SERVER_PORT=${SERVER_PORT:-9093}
      - DB_URL=**********************************************************************************************************************************************
      - DB_USERNAME=${DB_USERNAME:-dms_dev_user}
      - DB_PASSWORD=${DB_PASSWORD:-dev_password}
      - REDIS_HOST=grc-redis-shared
      - REDIS_PORT=6379
      - REDIS_PASSWORD=${REDIS_PASSWORD:-}
      - STORAGE_PROVIDER=LOCAL
      - STORAGE_PATH=/app/storage
      - JWT_SECRET=${JWT_SECRET:-devSecretKey123456789012345678901234567890ABCDEFGHIJKLMNOPQRSTUVWXYZ}
      - GRAPHIQL_ENABLED=true
      - CORS_ALLOWED_ORIGINS=${CORS_ALLOWED_ORIGINS:-http://localhost:3000,http://localhost:8080,http://localhost:9093}
      - CORS_ALLOWED_METHODS=${CORS_ALLOWED_METHODS:-GET,POST,PUT,DELETE,OPTIONS,PATCH}
      - CORS_ALLOWED_HEADERS=${CORS_ALLOWED_HEADERS:-Content-Type,Authorization,X-Correlation-ID,Accept}
      - CORS_ALLOW_CREDENTIALS=${CORS_ALLOW_CREDENTIALS:-true}
      - LOG_LEVEL_DMS=DEBUG
      - LOG_LEVEL_SECURITY=DEBUG
      - CACHE_TYPE=redis
      - CACHE_TTL=300000
      - CACHE_KEY_PREFIX=dms:dev:
      - ELASTICSEARCH_ENABLED=true
      - ELASTICSEARCH_HOST=grc-elasticsearch-shared
      - ELASTICSEARCH_PORT=9200
      - VIRUS_SCANNING_ENABLED=false
      - VIRUS_SCANNING_DEFAULT_SCANNER=MOCK
      - RATE_LIMIT_ENABLED=false
      - OTEL_SERVICE_NAME=dms-svc-dev
      - OTEL_SERVICE_VERSION=1.0.0-dev
      - OTEL_TRACES_SAMPLER_ARG=1.0
      - OTEL_TRACES_EXPORTER=zipkin
      - OTEL_EXPORTER_ZIPKIN_ENDPOINT=http://grc-zipkin-shared:9411/api/v2/spans
      - OTEL_METRICS_EXPORTER=console
      - OTEL_LOGS_EXPORTER=console
      - DMS_BASE_URL=${DMS_BASE_URL:-http://localhost:9093}
      - ENVIRONMENT=development
    volumes:
      - dms_storage:/app/storage
      - dms_logs:/app/logs
      - C:/temp:/app/temp  # Bind mount to Windows path
    depends_on:
      grc-mysql-shared:
        condition: service_healthy
      grc-redis-shared:
        condition: service_healthy
      grc-elasticsearch-shared:
        condition: service_healthy
    networks:
      - grc-shared-network
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:9093/actuator/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 60s

  # Notification Service
  notification-svc:
    build:
      context: ./notification-svc
      dockerfile: Dockerfile
    container_name: notification-svc
    ports:
      - "${NOTIFICATION_PORT:-9091}:9091"
    environment:
      - SPRING_PROFILES_ACTIVE=${NOTIFICATION_PROFILE:-dev}
      - SERVER_PORT=9091
      - SPRING_DATASOURCE_URL=**************************************************************************************************************************
      - SPRING_DATASOURCE_USERNAME=${NOTIFICATION_DB_USER:-notification_user}
      - SPRING_DATASOURCE_PASSWORD=${NOTIFICATION_DB_PASSWORD:-notification_password}
      - SPRING_RABBITMQ_HOST=grc-rabbitmq-shared
      - SPRING_RABBITMQ_PORT=5672
      - SPRING_RABBITMQ_USERNAME=${RABBITMQ_USER:-admin}
      - SPRING_RABBITMQ_PASSWORD=${RABBITMQ_PASSWORD:-admin123}
      - SPRING_RABBITMQ_VIRTUAL_HOST=${RABBITMQ_VHOST:-/}
      - SPRING_REDIS_HOST=grc-redis-shared
      - SPRING_REDIS_PORT=6379
      - SPRING_REDIS_PASSWORD=${REDIS_PASSWORD:-redis_password}
      - JAVA_OPTS=-Xmx1g -Xms512m
      - SECURITY_JWT_ENABLED=true
      - JWT_ISSUER=notification-service
      - JWT_AUDIENCE=notification-clients
    volumes:
      - notification_logs:/app/logs
      - ./notification-svc/keys:/app/keys:ro
      - C:/temp:/app/temp  # Bind mount to Windows path
    depends_on:
      grc-mysql-shared:
        condition: service_healthy
      grc-rabbitmq-shared:
        condition: service_healthy
      grc-redis-shared:
        condition: service_healthy
    networks:
      - grc-shared-network
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:9091/actuator/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 60s

volumes:
  dms_storage:
    driver: local
  dms_logs:
    driver: local
  notification_logs:
    driver: local

networks:
  grc-shared-network:
    external: true