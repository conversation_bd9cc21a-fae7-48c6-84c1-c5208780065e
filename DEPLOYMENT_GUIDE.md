# GRC Platform Deployment Guide

## Overview

This guide provides simplified deployment instructions for the GRC Platform (DMS and Notification services) across development and UAT environments. The deployment process has been streamlined to use a single unified script with environment-specific configurations.

## Quick Start

### Development Environment
```bash
./deploy.sh dev
```

### UAT Environment
```bash
./deploy.sh uat --clean
```

## Architecture

The GRC Platform consists of:
- **DMS Service**: Document Management System (Port 9093)
- **Notification Service**: Email and notification handling (Port 9091)
- **Shared Infrastructure**: MySQL, Redis, RabbitMQ, Elasticsearch, Zipkin, Prometheus, Grafana

## Configuration Structure

### Simplified Configuration Files

```
├── .env.dev                           # Development environment variables
├── .env.uat                           # UAT environment variables
├── deploy.sh                          # Unified deployment script
├── docker-compose.shared.yml          # Shared infrastructure services
├── docker-compose.services.yml        # Development services
├── docker-compose.uat.yml             # UAT services
└── dms-svc/src/main/resources/
    ├── application.properties          # Base configuration
    ├── application-dev.properties      # Development overrides
    └── application-uat.properties      # UAT overrides
```

### Benefits of New Structure
- **70% fewer configuration files** (reduced from 8+ to 3)
- **Single deployment script** (reduced from 12+ scripts to 1)
- **Environment variable driven** configuration
- **Consistent deployment process** across environments
- **Easier maintenance** and troubleshooting

## Deployment Script Usage

### Basic Commands

```bash
# Development deployment
./deploy.sh dev

# UAT deployment
./deploy.sh uat

# Clean deployment (removes old containers/images)
./deploy.sh dev --clean

# Force rebuild and deploy
./deploy.sh uat --clean --build

# Help
./deploy.sh --help
```

### Script Features

- **Automatic environment detection**
- **Health checks** for all services
- **Colored output** for better visibility
- **Error handling** and rollback capabilities
- **Service status reporting**
- **Comprehensive logging**

## Environment Configuration

### Development Environment (.env.dev)

Key variables for development:
```bash
# Database
DB_URL=***********************************
DB_USERNAME=dms_dev_user
DB_PASSWORD=dev_password

# Security
JWT_SECRET=devSecretKey123...
GRAPHIQL_ENABLED=true

# CORS (permissive for development)
CORS_ALLOWED_ORIGINS=http://localhost:3000,http://localhost:8080,http://localhost:9093

# Logging (debug level)
LOG_LEVEL_DMS=DEBUG
LOG_LEVEL_SECURITY=DEBUG
```

### UAT Environment (.env.uat)

Key variables for UAT:
```bash
# Database
DB_URL=***************************************
DB_USERNAME=dms_uat_user
DB_PASSWORD=${UAT_DB_PASSWORD}

# Security
JWT_SECRET=${UAT_JWT_SECRET}
GRAPHIQL_ENABLED=false

# CORS (restrictive for UAT)
CORS_ALLOWED_ORIGINS=${UAT_CORS_ALLOWED_ORIGINS}

# Logging (info level)
LOG_LEVEL_DMS=INFO
LOG_LEVEL_SECURITY=WARN
```

## Service URLs

### Development Environment
- **DMS Service**: http://localhost:9093
- **DMS GraphiQL**: http://localhost:9093/graphiql
- **Notification Service**: http://localhost:9091
- **Grafana Dashboard**: http://localhost:3000 (admin/admin)
- **Prometheus**: http://localhost:9090
- **Zipkin Tracing**: http://localhost:9411
- **RabbitMQ Management**: http://localhost:15672 (admin/admin123)

### UAT Environment
- **DMS Service**: http://[PUBLIC_IP]:9093
- **Notification Service**: http://[PUBLIC_IP]:9091
- **Grafana Dashboard**: http://[PUBLIC_IP]:3000
- **Prometheus**: http://[PUBLIC_IP]:9090
- **Zipkin Tracing**: http://[PUBLIC_IP]:9411
- **RabbitMQ Management**: http://[PUBLIC_IP]:15672

## Docker Compose Structure

### Shared Infrastructure (docker-compose.shared.yml)
- MySQL 8.0 with health checks
- Redis 7 with persistence
- RabbitMQ with management interface
- Elasticsearch 8.11 for search
- Zipkin for distributed tracing
- Prometheus for metrics
- Grafana for dashboards

### Development Services (docker-compose.services.yml)
- DMS Service with development profile
- Notification Service with development profile
- Debug logging enabled
- GraphiQL interface enabled
- Permissive CORS settings

### UAT Services (docker-compose.uat.yml)
- DMS Service with UAT profile
- Notification Service with UAT profile
- Production-like logging
- GraphiQL interface disabled
- Restrictive CORS settings
- Nginx reverse proxy

## Health Checks

The deployment script automatically performs health checks:

1. **Infrastructure Services**
   - MySQL connectivity
   - Redis ping test
   - RabbitMQ diagnostics

2. **Application Services**
   - DMS service `/actuator/health`
   - Notification service `/actuator/health`

3. **Service Status**
   - Container status
   - Port accessibility
   - Log analysis

## Troubleshooting

### Common Issues

#### 1. Port Conflicts
```bash
# Check what's using the ports
netstat -tulpn | grep :9093
netstat -tulpn | grep :9091

# Stop conflicting services
./deploy.sh dev --clean
```

#### 2. Database Connection Issues
```bash
# Check MySQL container
docker exec grc-mysql-shared mysqladmin ping -h localhost -u root -p

# View database logs
docker compose --env-file .env.dev -f docker-compose.shared.yml logs grc-mysql-shared
```

#### 3. Configuration Issues
```bash
# Verify environment variables are loaded
docker compose --env-file .env.dev -f docker-compose.services.yml config

# Check application logs
docker compose --env-file .env.dev -f docker-compose.services.yml logs dms-svc
```

#### 4. Network Issues
```bash
# Recreate network
docker network rm grc-shared-network
docker network create grc-shared-network

# Restart with clean network
./deploy.sh dev --clean
```

### Useful Commands

```bash
# View all service logs
docker compose --env-file .env.dev -f docker-compose.shared.yml -f docker-compose.services.yml logs -f

# View specific service logs
docker compose --env-file .env.dev -f docker-compose.services.yml logs -f dms-svc

# Check service status
docker compose --env-file .env.dev -f docker-compose.services.yml ps

# Stop all services
docker compose --env-file .env.dev -f docker-compose.shared.yml -f docker-compose.services.yml down

# Remove all data (destructive)
docker compose --env-file .env.dev -f docker-compose.shared.yml down -v
```

## Security Considerations

### Development Environment
- Permissive CORS settings for local development
- Debug logging enabled
- GraphiQL interface accessible
- Default passwords acceptable

### UAT Environment
- Restrictive CORS settings
- Production-level logging
- GraphiQL interface disabled
- Strong passwords required
- SSL/TLS enabled where applicable

## Performance Tuning

### Resource Allocation

**Development:**
- MySQL: 1GB RAM, 1 CPU
- Redis: 256MB RAM
- DMS Service: 1GB RAM, 1 CPU
- Notification Service: 512MB RAM, 0.5 CPU

**UAT:**
- MySQL: 2GB RAM, 2 CPU
- Redis: 512MB RAM
- DMS Service: 3GB RAM, 2 CPU
- Notification Service: 2GB RAM, 1 CPU

### Monitoring

- **Prometheus**: Metrics collection
- **Grafana**: Dashboard visualization
- **Zipkin**: Distributed tracing
- **Application logs**: Structured logging with correlation IDs

## Migration from Old Scripts

### Removed Scripts
The following scripts have been replaced by the unified `deploy.sh`:
- `backup-uat.sh`
- `clean-docker.sh`
- `clean-uat.sh`
- `deploy-dev.sh`
- `deploy-standalone.sh`
- `deploy-uat.sh`
- `fix-cors-and-redeploy.sh`
- `fix-line-endings.sh`
- `local-docker-rebuild.ps1`
- `local-docker-rebuild.sh`
- `test-cors-fix.js`
- `test-cors-quick.sh`

### Migration Steps
1. Use `./deploy.sh dev` instead of `deploy-dev.sh`
2. Use `./deploy.sh uat --clean` instead of `fix-cors-and-redeploy.sh`
3. Environment variables are now in `.env.dev` and `.env.uat`
4. Configuration is now in `application-dev.properties` and `application-uat.properties`

## Support

For issues or questions:
1. Check the troubleshooting section above
2. Review application logs
3. Verify environment configuration
4. Check Docker container status
5. Consult the configuration guide: `dms-svc/CONFIG_GUIDE.md`

## Changelog

### v2.0 (Current)
- ✅ Unified deployment script
- ✅ Simplified configuration structure
- ✅ Environment variable driven configuration
- ✅ Improved health checks
- ✅ Better error handling
- ✅ Comprehensive documentation

### v1.0 (Legacy)
- ❌ Multiple deployment scripts
- ❌ Complex configuration files
- ❌ Manual CORS fixes
- ❌ Inconsistent deployment process