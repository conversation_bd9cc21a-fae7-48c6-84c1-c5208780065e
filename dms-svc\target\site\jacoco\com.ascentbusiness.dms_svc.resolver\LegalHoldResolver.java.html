<?xml version="1.0" encoding="UTF-8"?><!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Strict//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-strict.dtd"><html xmlns="http://www.w3.org/1999/xhtml" lang="en"><head><meta http-equiv="Content-Type" content="text/html;charset=UTF-8"/><link rel="stylesheet" href="../jacoco-resources/report.css" type="text/css"/><link rel="shortcut icon" href="../jacoco-resources/report.gif" type="image/gif"/><title>LegalHoldResolver.java</title><link rel="stylesheet" href="../jacoco-resources/prettify.css" type="text/css"/><script type="text/javascript" src="../jacoco-resources/prettify.js"></script></head><body onload="window['PR_TAB_WIDTH']=4;prettyPrint()"><div class="breadcrumb" id="breadcrumb"><span class="info"><a href="../jacoco-sessions.html" class="el_session">Sessions</a></span><a href="../index.html" class="el_report">dms-svc</a> &gt; <a href="index.source.html" class="el_package">com.ascentbusiness.dms_svc.resolver</a> &gt; <span class="el_source">LegalHoldResolver.java</span></div><h1>LegalHoldResolver.java</h1><pre class="source lang-java linenums">package com.ascentbusiness.dms_svc.resolver;

import com.ascentbusiness.dms_svc.dto.LegalHoldInput;
import com.ascentbusiness.dms_svc.entity.Document;
import com.ascentbusiness.dms_svc.enums.LegalHoldStatus;
import com.ascentbusiness.dms_svc.repository.DocumentRepository;
import com.ascentbusiness.dms_svc.service.LegalHoldService;
import com.ascentbusiness.dms_svc.util.CorrelationIdUtil;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Sort;
import org.springframework.graphql.data.method.annotation.Argument;
import org.springframework.graphql.data.method.annotation.MutationMapping;
import org.springframework.graphql.data.method.annotation.QueryMapping;
import org.springframework.stereotype.Controller;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

/**
 * GraphQL resolver for legal hold operations
 */
@Controller
<span class="fc" id="L29">public class LegalHoldResolver {</span>
    
<span class="fc" id="L31">    private static final Logger logger = LoggerFactory.getLogger(LegalHoldResolver.class);</span>
    
    @Autowired
    private LegalHoldService legalHoldService;
    
    @Autowired
    private DocumentRepository documentRepository;
    
    // Query resolvers
    
    @QueryMapping
    public Map&lt;String, Object&gt; getDocumentsUnderLegalHold(@Argument Map&lt;String, Object&gt; pagination) {
<span class="nc" id="L43">        String correlationId = CorrelationIdUtil.getCurrentCorrelationId();</span>
<span class="nc" id="L44">        logger.info(&quot;GraphQL query: getDocumentsUnderLegalHold [{}]&quot;, correlationId);</span>
        
<span class="nc" id="L46">        Pageable pageable = createPageable(pagination);</span>
<span class="nc" id="L47">        Page&lt;Document&gt; documentPage = legalHoldService.getDocumentsUnderLegalHold(pageable);</span>
        
<span class="nc" id="L49">        return Map.of(</span>
<span class="nc" id="L50">            &quot;content&quot;, documentPage.getContent(),</span>
<span class="nc" id="L51">            &quot;totalElements&quot;, documentPage.getTotalElements(),</span>
<span class="nc" id="L52">            &quot;totalPages&quot;, documentPage.getTotalPages(),</span>
<span class="nc" id="L53">            &quot;size&quot;, documentPage.getSize(),</span>
<span class="nc" id="L54">            &quot;number&quot;, documentPage.getNumber(),</span>
<span class="nc" id="L55">            &quot;first&quot;, documentPage.isFirst(),</span>
<span class="nc" id="L56">            &quot;last&quot;, documentPage.isLast()</span>
        );
    }
    
    @QueryMapping
    public Boolean isDocumentUnderLegalHold(@Argument Long documentId) {
<span class="nc" id="L62">        String correlationId = CorrelationIdUtil.getCurrentCorrelationId();</span>
<span class="nc" id="L63">        logger.info(&quot;GraphQL query: isDocumentUnderLegalHold(documentId: {}) [{}]&quot;, documentId, correlationId);</span>
        
<span class="nc" id="L65">        return legalHoldService.isDocumentUnderLegalHold(documentId);</span>
    }
    
    @QueryMapping
    public LegalHoldStatus getDocumentLegalHoldStatus(@Argument Long documentId) {
<span class="nc" id="L70">        String correlationId = CorrelationIdUtil.getCurrentCorrelationId();</span>
<span class="nc" id="L71">        logger.info(&quot;GraphQL query: getDocumentLegalHoldStatus(documentId: {}) [{}]&quot;, documentId, correlationId);</span>
        
<span class="nc" id="L73">        return legalHoldService.getLegalHoldStatus(documentId);</span>
    }
    
    // Mutation resolvers
    
    @MutationMapping
    public List&lt;Document&gt; applyLegalHold(@Argument LegalHoldInput input) {
<span class="nc" id="L80">        String correlationId = CorrelationIdUtil.getCurrentCorrelationId();</span>
<span class="nc" id="L81">        logger.info(&quot;GraphQL mutation: applyLegalHold(documentIds: {}, reason: {}) [{}]&quot;, </span>
<span class="nc" id="L82">                input.getDocumentIds().size(), input.getReason(), correlationId);</span>
        
<span class="nc" id="L84">        List&lt;Document&gt; updatedDocuments = new ArrayList&lt;&gt;();</span>
        
<span class="nc bnc" id="L86" title="All 2 branches missed.">        for (Long documentId : input.getDocumentIds()) {</span>
            try {
<span class="nc" id="L88">                Document updatedDocument = legalHoldService.applyLegalHold(documentId, input.getReason());</span>
<span class="nc" id="L89">                updatedDocuments.add(updatedDocument);</span>
<span class="nc" id="L90">            } catch (Exception e) {</span>
<span class="nc" id="L91">                logger.error(&quot;Failed to apply legal hold to document ID: {} [{}]&quot;, documentId, correlationId, e);</span>
                // Continue with other documents
<span class="nc" id="L93">            }</span>
<span class="nc" id="L94">        }</span>
        
<span class="nc" id="L96">        return updatedDocuments;</span>
    }
    
    @MutationMapping
    public List&lt;Document&gt; releaseLegalHold(@Argument List&lt;Long&gt; documentIds) {
<span class="nc" id="L101">        String correlationId = CorrelationIdUtil.getCurrentCorrelationId();</span>
<span class="nc" id="L102">        logger.info(&quot;GraphQL mutation: releaseLegalHold(documentIds: {}) [{}]&quot;, documentIds.size(), correlationId);</span>
        
<span class="nc" id="L104">        List&lt;Document&gt; updatedDocuments = new ArrayList&lt;&gt;();</span>
        
<span class="nc bnc" id="L106" title="All 2 branches missed.">        for (Long documentId : documentIds) {</span>
            try {
<span class="nc" id="L108">                Document updatedDocument = legalHoldService.releaseLegalHold(documentId);</span>
<span class="nc" id="L109">                updatedDocuments.add(updatedDocument);</span>
<span class="nc" id="L110">            } catch (Exception e) {</span>
<span class="nc" id="L111">                logger.error(&quot;Failed to release legal hold from document ID: {} [{}]&quot;, documentId, correlationId, e);</span>
                // Continue with other documents
<span class="nc" id="L113">            }</span>
<span class="nc" id="L114">        }</span>
        
<span class="nc" id="L116">        return updatedDocuments;</span>
    }
    
    @MutationMapping
    public Document updateLegalHoldReason(@Argument Long documentId, @Argument String reason) {
<span class="nc" id="L121">        String correlationId = CorrelationIdUtil.getCurrentCorrelationId();</span>
<span class="nc" id="L122">        logger.info(&quot;GraphQL mutation: updateLegalHoldReason(documentId: {}, reason: {}) [{}]&quot;, </span>
                documentId, reason, correlationId);
        
<span class="nc" id="L125">        return legalHoldService.updateLegalHoldReason(documentId, reason);</span>
    }
    
    // Helper methods
    
    private Pageable createPageable(Map&lt;String, Object&gt; pagination) {
<span class="nc bnc" id="L131" title="All 2 branches missed.">        if (pagination == null) {</span>
<span class="nc" id="L132">            return PageRequest.of(0, 10, Sort.by(Sort.Direction.DESC, &quot;legalHoldAppliedDate&quot;));</span>
        }
        
<span class="nc" id="L135">        int page = (Integer) pagination.getOrDefault(&quot;page&quot;, 0);</span>
<span class="nc" id="L136">        int size = (Integer) pagination.getOrDefault(&quot;size&quot;, 10);</span>
<span class="nc" id="L137">        String sortBy = (String) pagination.getOrDefault(&quot;sortBy&quot;, &quot;legalHoldAppliedDate&quot;);</span>
<span class="nc" id="L138">        String sortDirection = (String) pagination.getOrDefault(&quot;sortDirection&quot;, &quot;DESC&quot;);</span>
        
<span class="nc bnc" id="L140" title="All 2 branches missed.">        Sort.Direction direction = &quot;ASC&quot;.equalsIgnoreCase(sortDirection) ? Sort.Direction.ASC : Sort.Direction.DESC;</span>
        
<span class="nc" id="L142">        return PageRequest.of(page, size, Sort.by(direction, sortBy));</span>
    }
}
</pre><div class="footer"><span class="right">Created with <a href="http://www.jacoco.org/jacoco">JaCoCo</a> 0.8.10.202304240956</span></div></body></html>