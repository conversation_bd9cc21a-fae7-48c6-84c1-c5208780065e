<?xml version="1.0" encoding="UTF-8"?><!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Strict//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-strict.dtd"><html xmlns="http://www.w3.org/1999/xhtml" lang="en"><head><meta http-equiv="Content-Type" content="text/html;charset=UTF-8"/><link rel="stylesheet" href="../jacoco-resources/report.css" type="text/css"/><link rel="shortcut icon" href="../jacoco-resources/report.gif" type="image/gif"/><title>WorkflowHistoryService.java</title><link rel="stylesheet" href="../jacoco-resources/prettify.css" type="text/css"/><script type="text/javascript" src="../jacoco-resources/prettify.js"></script></head><body onload="window['PR_TAB_WIDTH']=4;prettyPrint()"><div class="breadcrumb" id="breadcrumb"><span class="info"><a href="../jacoco-sessions.html" class="el_session">Sessions</a></span><a href="../index.html" class="el_report">dms-svc</a> &gt; <a href="index.source.html" class="el_package">com.ascentbusiness.dms_svc.service</a> &gt; <span class="el_source">WorkflowHistoryService.java</span></div><h1>WorkflowHistoryService.java</h1><pre class="source lang-java linenums">package com.ascentbusiness.dms_svc.service;

import com.ascentbusiness.dms_svc.entity.WorkflowHistory;
import com.ascentbusiness.dms_svc.entity.WorkflowInstance;
import com.ascentbusiness.dms_svc.entity.WorkflowStage;
import com.ascentbusiness.dms_svc.entity.WorkflowTask;
import com.ascentbusiness.dms_svc.repository.WorkflowHistoryRepository;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.util.List;

/**
 * Service for managing workflow history
 */
@Service
<span class="fc" id="L20">@RequiredArgsConstructor</span>
<span class="fc" id="L21">@Slf4j</span>
@Transactional
public class WorkflowHistoryService {

    private final WorkflowHistoryRepository workflowHistoryRepository;

    /**
     * Log workflow started event
     */
    public WorkflowHistory logWorkflowStarted(WorkflowInstance workflowInstance, String actorUserId) {
<span class="nc" id="L31">        log.debug(&quot;Logging workflow started for instance: {} by user: {}&quot;, workflowInstance.getId(), actorUserId);</span>
        
<span class="nc" id="L33">        WorkflowHistory history = WorkflowHistory.builder()</span>
<span class="nc" id="L34">                .workflowInstance(workflowInstance)</span>
<span class="nc" id="L35">                .eventType(&quot;WORKFLOW_STARTED&quot;)</span>
<span class="nc" id="L36">                .eventDescription(&quot;Workflow started&quot;)</span>
<span class="nc" id="L37">                .actorUserId(actorUserId)</span>
<span class="nc" id="L38">                .newStatus(workflowInstance.getStatus().name())</span>
<span class="nc" id="L39">                .eventTimestamp(LocalDateTime.now())</span>
<span class="nc" id="L40">                .build();</span>
        
<span class="nc" id="L42">        return workflowHistoryRepository.save(history);</span>
    }

    /**
     * Log workflow completed event
     */
    public WorkflowHistory logWorkflowCompleted(WorkflowInstance workflowInstance, String actorUserId, String reason) {
<span class="nc" id="L49">        log.debug(&quot;Logging workflow completed for instance: {} by user: {}&quot;, workflowInstance.getId(), actorUserId);</span>
        
<span class="nc" id="L51">        WorkflowHistory history = WorkflowHistory.builder()</span>
<span class="nc" id="L52">                .workflowInstance(workflowInstance)</span>
<span class="nc" id="L53">                .eventType(&quot;WORKFLOW_COMPLETED&quot;)</span>
<span class="nc" id="L54">                .eventDescription(&quot;Workflow completed&quot;)</span>
<span class="nc" id="L55">                .actorUserId(actorUserId)</span>
<span class="nc" id="L56">                .oldStatus(&quot;IN_PROGRESS&quot;)</span>
<span class="nc" id="L57">                .newStatus(&quot;COMPLETED&quot;)</span>
<span class="nc" id="L58">                .comments(reason)</span>
<span class="nc" id="L59">                .eventTimestamp(LocalDateTime.now())</span>
<span class="nc" id="L60">                .build();</span>
        
<span class="nc" id="L62">        return workflowHistoryRepository.save(history);</span>
    }

    /**
     * Log workflow cancelled event
     */
    public WorkflowHistory logWorkflowCancelled(WorkflowInstance workflowInstance, String actorUserId, String reason) {
<span class="nc" id="L69">        log.debug(&quot;Logging workflow cancelled for instance: {} by user: {}&quot;, workflowInstance.getId(), actorUserId);</span>
        
<span class="nc" id="L71">        WorkflowHistory history = WorkflowHistory.builder()</span>
<span class="nc" id="L72">                .workflowInstance(workflowInstance)</span>
<span class="nc" id="L73">                .eventType(&quot;WORKFLOW_CANCELLED&quot;)</span>
<span class="nc" id="L74">                .eventDescription(&quot;Workflow cancelled&quot;)</span>
<span class="nc" id="L75">                .actorUserId(actorUserId)</span>
<span class="nc" id="L76">                .oldStatus(workflowInstance.getStatus().name())</span>
<span class="nc" id="L77">                .newStatus(&quot;CANCELLED&quot;)</span>
<span class="nc" id="L78">                .comments(reason)</span>
<span class="nc" id="L79">                .eventTimestamp(LocalDateTime.now())</span>
<span class="nc" id="L80">                .build();</span>
        
<span class="nc" id="L82">        return workflowHistoryRepository.save(history);</span>
    }

    /**
     * Log workflow suspended event
     */
    public WorkflowHistory logWorkflowSuspended(WorkflowInstance workflowInstance, String actorUserId, String reason) {
<span class="nc" id="L89">        log.debug(&quot;Logging workflow suspended for instance: {} by user: {}&quot;, workflowInstance.getId(), actorUserId);</span>
        
<span class="nc" id="L91">        WorkflowHistory history = WorkflowHistory.builder()</span>
<span class="nc" id="L92">                .workflowInstance(workflowInstance)</span>
<span class="nc" id="L93">                .eventType(&quot;WORKFLOW_SUSPENDED&quot;)</span>
<span class="nc" id="L94">                .eventDescription(&quot;Workflow suspended&quot;)</span>
<span class="nc" id="L95">                .actorUserId(actorUserId)</span>
<span class="nc" id="L96">                .oldStatus(&quot;IN_PROGRESS&quot;)</span>
<span class="nc" id="L97">                .newStatus(&quot;SUSPENDED&quot;)</span>
<span class="nc" id="L98">                .comments(reason)</span>
<span class="nc" id="L99">                .eventTimestamp(LocalDateTime.now())</span>
<span class="nc" id="L100">                .build();</span>
        
<span class="nc" id="L102">        return workflowHistoryRepository.save(history);</span>
    }

    /**
     * Log workflow resumed event
     */
    public WorkflowHistory logWorkflowResumed(WorkflowInstance workflowInstance, String actorUserId, String reason) {
<span class="nc" id="L109">        log.debug(&quot;Logging workflow resumed for instance: {} by user: {}&quot;, workflowInstance.getId(), actorUserId);</span>
        
<span class="nc" id="L111">        WorkflowHistory history = WorkflowHistory.builder()</span>
<span class="nc" id="L112">                .workflowInstance(workflowInstance)</span>
<span class="nc" id="L113">                .eventType(&quot;WORKFLOW_RESUMED&quot;)</span>
<span class="nc" id="L114">                .eventDescription(&quot;Workflow resumed&quot;)</span>
<span class="nc" id="L115">                .actorUserId(actorUserId)</span>
<span class="nc" id="L116">                .oldStatus(&quot;SUSPENDED&quot;)</span>
<span class="nc" id="L117">                .newStatus(&quot;IN_PROGRESS&quot;)</span>
<span class="nc" id="L118">                .comments(reason)</span>
<span class="nc" id="L119">                .eventTimestamp(LocalDateTime.now())</span>
<span class="nc" id="L120">                .build();</span>
        
<span class="nc" id="L122">        return workflowHistoryRepository.save(history);</span>
    }

    /**
     * Log stage advanced event
     */
    public WorkflowHistory logStageAdvanced(WorkflowInstance workflowInstance, WorkflowStage newStage, String actorUserId) {
<span class="nc" id="L129">        log.debug(&quot;Logging stage advanced for instance: {} to stage: {} by user: {}&quot;, </span>
<span class="nc" id="L130">                 workflowInstance.getId(), newStage.getStageName(), actorUserId);</span>
        
<span class="nc" id="L132">        WorkflowHistory history = WorkflowHistory.builder()</span>
<span class="nc" id="L133">                .workflowInstance(workflowInstance)</span>
<span class="nc" id="L134">                .eventType(&quot;STAGE_ADVANCED&quot;)</span>
<span class="nc" id="L135">                .eventDescription(&quot;Advanced to stage: &quot; + newStage.getStageName())</span>
<span class="nc" id="L136">                .actorUserId(actorUserId)</span>
<span class="nc" id="L137">                .stageName(newStage.getStageName())</span>
<span class="nc" id="L138">                .eventTimestamp(LocalDateTime.now())</span>
<span class="nc" id="L139">                .build();</span>
        
<span class="nc" id="L141">        return workflowHistoryRepository.save(history);</span>
    }

    /**
     * Log task completed event
     */
    public WorkflowHistory logTaskCompleted(WorkflowTask task, String actorUserId, String action) {
<span class="nc" id="L148">        log.debug(&quot;Logging task completed for task: {} by user: {} with action: {}&quot;, </span>
<span class="nc" id="L149">                 task.getId(), actorUserId, action);</span>
        
<span class="nc" id="L151">        WorkflowHistory history = WorkflowHistory.builder()</span>
<span class="nc" id="L152">                .workflowInstance(task.getWorkflowInstance())</span>
<span class="nc" id="L153">                .workflowTask(task)</span>
<span class="nc" id="L154">                .eventType(&quot;TASK_COMPLETED&quot;)</span>
<span class="nc" id="L155">                .eventDescription(&quot;Task completed: &quot; + task.getTaskName())</span>
<span class="nc" id="L156">                .actorUserId(actorUserId)</span>
<span class="nc" id="L157">                .taskName(task.getTaskName())</span>
<span class="nc" id="L158">                .actionTaken(action)</span>
<span class="nc" id="L159">                .oldStatus(&quot;PENDING&quot;)</span>
<span class="nc" id="L160">                .newStatus(&quot;COMPLETED&quot;)</span>
<span class="nc" id="L161">                .comments(task.getComments())</span>
<span class="nc" id="L162">                .eventTimestamp(LocalDateTime.now())</span>
<span class="nc" id="L163">                .build();</span>
        
<span class="nc" id="L165">        return workflowHistoryRepository.save(history);</span>
    }

    /**
     * Log task assigned event
     */
    public WorkflowHistory logTaskAssigned(WorkflowTask task, String assignedTo, String actorUserId) {
<span class="nc" id="L172">        log.debug(&quot;Logging task assigned for task: {} to user: {} by user: {}&quot;, </span>
<span class="nc" id="L173">                 task.getId(), assignedTo, actorUserId);</span>
        
<span class="nc" id="L175">        WorkflowHistory history = WorkflowHistory.builder()</span>
<span class="nc" id="L176">                .workflowInstance(task.getWorkflowInstance())</span>
<span class="nc" id="L177">                .workflowTask(task)</span>
<span class="nc" id="L178">                .eventType(&quot;TASK_ASSIGNED&quot;)</span>
<span class="nc" id="L179">                .eventDescription(&quot;Task assigned to: &quot; + assignedTo)</span>
<span class="nc" id="L180">                .actorUserId(actorUserId)</span>
<span class="nc" id="L181">                .taskName(task.getTaskName())</span>
<span class="nc" id="L182">                .comments(&quot;Assigned to: &quot; + assignedTo)</span>
<span class="nc" id="L183">                .eventTimestamp(LocalDateTime.now())</span>
<span class="nc" id="L184">                .build();</span>
        
<span class="nc" id="L186">        return workflowHistoryRepository.save(history);</span>
    }

    /**
     * Log task escalated event
     */
    public WorkflowHistory logTaskEscalated(WorkflowTask task, String escalatedTo, String actorUserId, String reason) {
<span class="nc" id="L193">        log.debug(&quot;Logging task escalated for task: {} to user: {} by user: {}&quot;, </span>
<span class="nc" id="L194">                 task.getId(), escalatedTo, actorUserId);</span>
        
<span class="nc" id="L196">        WorkflowHistory history = WorkflowHistory.builder()</span>
<span class="nc" id="L197">                .workflowInstance(task.getWorkflowInstance())</span>
<span class="nc" id="L198">                .workflowTask(task)</span>
<span class="nc" id="L199">                .eventType(&quot;TASK_ESCALATED&quot;)</span>
<span class="nc" id="L200">                .eventDescription(&quot;Task escalated to: &quot; + escalatedTo)</span>
<span class="nc" id="L201">                .actorUserId(actorUserId)</span>
<span class="nc" id="L202">                .taskName(task.getTaskName())</span>
<span class="nc" id="L203">                .actionTaken(&quot;ESCALATE&quot;)</span>
<span class="nc" id="L204">                .comments(reason)</span>
<span class="nc" id="L205">                .eventTimestamp(LocalDateTime.now())</span>
<span class="nc" id="L206">                .build();</span>
        
<span class="nc" id="L208">        return workflowHistoryRepository.save(history);</span>
    }

    /**
     * Get workflow history for an instance
     */
    @Transactional(readOnly = true)
    public List&lt;WorkflowHistory&gt; getWorkflowHistory(Long workflowInstanceId) {
<span class="nc" id="L216">        return workflowHistoryRepository.findByWorkflowInstanceIdOrderByEventTimestampDesc(workflowInstanceId);</span>
    }

    /**
     * Get workflow history for a task
     */
    @Transactional(readOnly = true)
    public List&lt;WorkflowHistory&gt; getTaskHistory(Long taskId) {
<span class="nc" id="L224">        return workflowHistoryRepository.findByWorkflowTaskIdOrderByEventTimestampDesc(taskId);</span>
    }

    /**
     * Get recent workflow events
     */
    @Transactional(readOnly = true)
    public List&lt;WorkflowHistory&gt; getRecentWorkflowEvents(int hoursBack) {
<span class="nc" id="L232">        LocalDateTime cutoffTime = LocalDateTime.now().minusHours(hoursBack);</span>
<span class="nc" id="L233">        return workflowHistoryRepository.findByEventTimestampAfterOrderByEventTimestampDesc(cutoffTime);</span>
    }

    /**
     * Get workflow events by actor
     */
    @Transactional(readOnly = true)
    public List&lt;WorkflowHistory&gt; getWorkflowEventsByActor(String actorUserId) {
<span class="nc" id="L241">        return workflowHistoryRepository.findByActorUserIdOrderByEventTimestampDesc(actorUserId);</span>
    }
}
</pre><div class="footer"><span class="right">Created with <a href="http://www.jacoco.org/jacoco">JaCoCo</a> 0.8.10.202304240956</span></div></body></html>