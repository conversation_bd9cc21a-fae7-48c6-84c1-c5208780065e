# ========================================
# DEVELOPMENT ENVIRONMENT OVERRIDES
# ========================================
# This file contains only development-specific overrides.
# All other configurations are inherited from application.properties

# Environment Identifier
ENVIRONMENT=development

# ========================================
# DEVELOPMENT-SPECIFIC OVERRIDES
# ========================================

# Database Configuration - Enable SQL logging for development
spring.jpa.show-sql=true
spring.jpa.properties.hibernate.format_sql=true
spring.jpa.properties.hibernate.use_sql_comments=true

# Logging Configuration - Debug level for development
logging.level.com.ascentbusiness.dms_svc=DEBUG
logging.level.org.springframework.security=DEBUG
logging.level.org.hibernate.SQL=DEBUG
logging.level.org.hibernate.type.descriptor.sql.BasicBinder=TRACE

# GraphQL Configuration - Enable GraphiQL for development
spring.graphql.graphiql.enabled=true

# Security Configuration - Relaxed for development
dms.security.headers.csp.report-only=true
dms.security.headers.hsts.enabled=false

# Cache Configuration - Shorter TTL for development
spring.cache.redis.time-to-live=300000
spring.cache.redis.key-prefix=dms:dev:

# OpenTelemetry Configuration - Full sampling for development
otel.traces.sampler.arg=1.0

# Development Features
spring.devtools.restart.enabled=true
spring.devtools.livereload.enabled=true

# File Upload Configuration - Smaller limits for dev
spring.servlet.multipart.max-file-size=50MB
spring.servlet.multipart.max-request-size=50MB

# Actuator Configuration - More endpoints exposed in dev
management.endpoints.web.exposure.include=health,info,metrics,prometheus,env,configprops
management.endpoint.health.show-details=always

# Rate Limiting - Disabled for development
dms.rate-limit.enabled=false

# Virus Scanning - Mock for development
dms.virus-scanning.default-scanner=MOCK
dms.virus-scanner.mock.enabled=true

# Storage Configuration - Local storage for development
dms.storage.provider=LOCAL
dms.storage.local.base-path=./storage/documents

# Elasticsearch - Enabled for development
elasticsearch.enabled=true

# Redis health check - Enabled for development
management.health.redis.enabled=true