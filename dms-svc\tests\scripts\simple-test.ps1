# Simple test for Word to PDF conversion
$wordFile = "manual-test-input.docx"

if (-not (Test-Path $wordFile)) {
    Write-Host "Word file not found: $wordFile" -ForegroundColor Red
    exit 1
}

Write-Host "Testing Word to PDF conversion..." -ForegroundColor Yellow

# Use curl to test the REST endpoint instead
$url = "http://localhost:9093/api/word-conversion/convert-to-pdf"

try {
    $response = Invoke-RestMethod -Uri $url -Method POST -InFile $wordFile -ContentType "application/vnd.openxmlformats-officedocument.wordprocessingml.document"
    Write-Host "Success! Response: $response" -ForegroundColor Green
} catch {
    Write-Host "Error: $($_.Exception.Message)" -ForegroundColor Red
    
    # Try the GraphQL endpoint with a simple query
    Write-Host "Trying GraphQL endpoint..." -ForegroundColor Yellow
    
    $graphqlUrl = "http://localhost:9093/graphql"
    $simpleQuery = @{
        query = "query { __typename }"
    } | ConvertTo-Json
    
    try {
        $graphqlResponse = Invoke-RestMethod -Uri $graphqlUrl -Method POST -Body $simpleQuery -ContentType "application/json"
        Write-Host "GraphQL endpoint is working: $($graphqlResponse.data.__typename)" -ForegroundColor Green
    } catch {
        Write-Host "GraphQL endpoint error: $($_.Exception.Message)" -ForegroundColor Red
    }
}
