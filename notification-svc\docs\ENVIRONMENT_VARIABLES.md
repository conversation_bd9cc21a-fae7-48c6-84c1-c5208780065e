# Notification Service Environment Variables Reference

This document provides a comprehensive reference for all environment variables used in the Notification Service configuration.

## Database Configuration

| Variable | Default | Description | Required |
|----------|---------|-------------|----------|
| `DB_URL` | `*******************************************************************************************************************` | Database connection URL | No |
| `DB_USERNAME` | `root` | Database username | No |
| `DB_PASSWORD` | `root` | Database password | **Yes (Production)** |
| `DB_POOL_MAX_SIZE` | `10` | Maximum connection pool size | No |
| `DB_POOL_MIN_IDLE` | `2` | Minimum idle connections | No |
| `DB_POOL_CONNECTION_TIMEOUT` | `30000` | Connection timeout in milliseconds | No |
| `DB_POOL_IDLE_TIMEOUT` | `300000` | Idle timeout in milliseconds | No |
| `DB_POOL_MAX_LIFETIME` | `900000` | Maximum connection lifetime in milliseconds | No |

## Security Configuration

| Variable | Default | Description | Required |
|----------|---------|-------------|----------|
| `JWT_SECRET` | *(empty)* | JWT signing secret | **Yes** |
| `JWT_ISSUER` | `notification-service` | JWT token issuer | No |
| `JWT_AUDIENCE` | `notification-clients` | JWT token audience | No |
| `JWT_EXPIRATION` | `3600000` | JWT token expiration in milliseconds | No |
| `JWT_TOKEN_EXPIRATION` | `3600` | JWT token expiration in seconds | No |
| `JWT_ENABLED` | `true` | Enable/disable JWT authentication | No |
| `CORS_ALLOWED_ORIGINS` | *(empty)* | Comma-separated list of allowed CORS origins | **Yes (Production)** |
| `CORS_ALLOWED_METHODS` | `GET,POST,PUT,DELETE,OPTIONS` | Allowed HTTP methods | No |
| `CORS_ALLOWED_HEADERS` | `Content-Type,Authorization,X-Correlation-ID,Accept` | Allowed HTTP headers | No |
| `CORS_ALLOW_CREDENTIALS` | `true` | Allow credentials in CORS requests | No |
| `CORS_MAX_AGE` | `3600` | CORS preflight cache duration | No |

## External Services

### Redis Configuration
| Variable | Default | Description | Required |
|----------|---------|-------------|----------|
| `REDIS_HOST` | `localhost` | Redis server hostname | No |
| `REDIS_PORT` | `6379` | Redis server port | No |
| `REDIS_PASSWORD` | *(empty)* | Redis server password | **Yes (Production)** |
| `REDIS_DATABASE` | `0` | Redis database number | No |
| `REDIS_TIMEOUT` | `5000ms` | Redis connection timeout | No |
| `REDIS_CONNECT_TIMEOUT` | `3000ms` | Redis connect timeout | No |
| `REDIS_POOL_MAX_ACTIVE` | `10` | Maximum active connections | No |
| `REDIS_POOL_MAX_IDLE` | `5` | Maximum idle connections | No |
| `REDIS_POOL_MIN_IDLE` | `1` | Minimum idle connections | No |
| `REDIS_POOL_MAX_WAIT` | `2000ms` | Maximum wait time for connection | No |
| `REDIS_POOL_EVICTION_RUNS` | `30s` | Time between eviction runs | No |

### RabbitMQ Configuration
| Variable | Default | Description | Required |
|----------|---------|-------------|----------|
| `RABBITMQ_HOST` | `localhost` | RabbitMQ server hostname | No |
| `RABBITMQ_PORT` | `5672` | RabbitMQ server port | No |
| `RABBITMQ_USERNAME` | `guest` | RabbitMQ username | **Yes (Production)** |
| `RABBITMQ_PASSWORD` | `guest` | RabbitMQ password | **Yes (Production)** |
| `RABBITMQ_VIRTUAL_HOST` | `/` | RabbitMQ virtual host | No |
| `RABBITMQ_CONNECTION_TIMEOUT` | `30000` | Connection timeout in milliseconds | No |
| `RABBITMQ_HEARTBEAT` | `60` | Heartbeat interval in seconds | No |
| `RABBITMQ_PUBLISHER_CONFIRM` | `correlated` | Publisher confirmation type | No |
| `RABBITMQ_PUBLISHER_RETURNS` | `true` | Enable publisher returns | No |

### Email Configuration
| Variable | Default | Description | Required |
|----------|---------|-------------|----------|
| `MAIL_HOST` | *(empty)* | SMTP server hostname | **Yes** |
| `MAIL_PORT` | `587` | SMTP server port | No |
| `MAIL_USERNAME` | *(empty)* | SMTP username | **Yes** |
| `MAIL_PASSWORD` | *(empty)* | SMTP password | **Yes** |
| `MAIL_SMTP_AUTH` | `true` | Enable SMTP authentication | No |
| `MAIL_SMTP_STARTTLS` | `true` | Enable STARTTLS | No |
| `MAIL_SMTP_STARTTLS_REQUIRED` | `true` | Require STARTTLS | No |
| `MAIL_SMTP_SSL_TRUST` | `*` | SSL trust configuration | No |

## Application Configuration

| Variable | Default | Description | Required |
|----------|---------|-------------|----------|
| `SERVER_PORT` | `9091` | Server port number | No |
| `ENVIRONMENT` | *(varies by profile)* | Environment identifier | No |
| `VIRTUAL_THREADS_ENABLED` | `true` | Enable Java 21 virtual threads | No |
| `ALLOW_BEAN_DEFINITION_OVERRIDING` | `true` | Allow Spring bean overriding | No |

## Notification Configuration

| Variable | Default | Description | Required |
|----------|---------|-------------|----------|
| `NOTIFICATION_FROM_EMAIL` | `noreply@localhost` | Default sender email address | No |
| `EMAIL_ENABLED` | `true` | Enable email notifications | No |
| `EMAIL_MOCK` | `false` | Use mock email sending | No |
| `NOTIFICATION_BATCH_SIZE` | `25` | Batch size for notification processing | No |
| `NOTIFICATION_RETRY_ATTEMPTS` | `3` | Number of retry attempts | No |
| `NOTIFICATION_RETRY_DELAY` | `5000` | Retry delay in milliseconds | No |

## Cache Configuration

| Variable | Default | Description | Required |
|----------|---------|-------------|----------|
| `CACHE_TTL` | `3600` | Cache time-to-live in seconds | No |
| `CACHE_NULL_VALUES` | `false` | Cache null values | No |
| `CACHE_USE_KEY_PREFIX` | `true` | Use key prefix for cache | No |
| `CACHE_KEY_PREFIX` | `notification:` | Cache key prefix | No |

## Template Configuration

| Variable | Default | Description | Required |
|----------|---------|-------------|----------|
| `TEMPLATE_CACHE_ENABLED` | `true` | Enable template caching | No |
| `TEMPLATE_CACHE_TTL` | `3600` | Template cache TTL in seconds | No |

## Webhook Configuration

| Variable | Default | Description | Required |
|----------|---------|-------------|----------|
| `WEBHOOK_ENABLED` | `true` | Enable webhook functionality | No |
| `WEBHOOK_TIMEOUT` | `30000` | Webhook timeout in milliseconds | No |
| `WEBHOOK_RETRY_ATTEMPTS` | `3` | Number of webhook retry attempts | No |

## GraphQL Configuration

| Variable | Default | Description | Required |
|----------|---------|-------------|----------|
| `GRAPHIQL_ENABLED` | `false` | Enable GraphiQL interface | No |
| `GRAPHQL_SCHEMA_PRINTER_ENABLED` | `false` | Enable schema printer | No |
| `GRAPHQL_CORS_ALLOWED_ORIGINS` | *(empty)* | GraphQL-specific CORS origins | No |
| `GRAPHQL_CORS_ALLOWED_METHODS` | `GET,POST,OPTIONS` | GraphQL-specific CORS methods | No |
| `GRAPHQL_CORS_ALLOWED_HEADERS` | `Content-Type,Authorization,X-Correlation-ID,Accept` | GraphQL-specific CORS headers | No |
| `GRAPHQL_CORS_ALLOW_CREDENTIALS` | `true` | Allow credentials for GraphQL CORS | No |
| `GRAPHQL_CORS_MAX_AGE` | `3600` | GraphQL CORS preflight cache duration | No |

## JPA/Hibernate Configuration

| Variable | Default | Description | Required |
|----------|---------|-------------|----------|
| `JPA_DDL_AUTO` | `update` | Hibernate DDL auto mode | No |
| `JPA_SHOW_SQL` | `false` | Show SQL queries in logs | No |
| `HIBERNATE_FORMAT_SQL` | `false` | Format SQL queries in logs | No |
| `HIBERNATE_BATCH_SIZE` | `25` | Hibernate batch size | No |

## Flyway Configuration

| Variable | Default | Description | Required |
|----------|---------|-------------|----------|
| `FLYWAY_ENABLED` | `false` | Enable Flyway migrations | No |

## Actuator Configuration

| Variable | Default | Description | Required |
|----------|---------|-------------|----------|
| `ACTUATOR_ENDPOINTS` | `health,info,metrics,prometheus` | Exposed actuator endpoints | No |
| `ACTUATOR_HEALTH_SHOW_DETAILS` | `when-authorized` | Health endpoint detail level | No |
| `ACTUATOR_HEALTH_SHOW_COMPONENTS` | `when-authorized` | Health endpoint component level | No |
| `HEALTH_DB_ENABLED` | `true` | Enable database health indicator | No |
| `HEALTH_DISKSPACE_ENABLED` | `true` | Enable disk space health indicator | No |
| `HEALTH_REDIS_ENABLED` | `true` | Enable Redis health indicator | No |
| `HEALTH_RABBITMQ_ENABLED` | `true` | Enable RabbitMQ health indicator | No |
| `HEALTH_PING_ENABLED` | `true` | Enable ping health indicator | No |
| `HEALTH_LIVENESS_ENABLED` | `true` | Enable liveness health indicator | No |
| `HEALTH_READINESS_ENABLED` | `true` | Enable readiness health indicator | No |
| `METRICS_PROMETHEUS_ENABLED` | `true` | Enable Prometheus metrics | No |
| `METRICS_PERCENTILES_HISTOGRAM` | `false` | Enable percentiles histogram | No |

## Logging Configuration

| Variable | Default | Description | Required |
|----------|---------|-------------|----------|
| `LOG_LEVEL_APP` | `INFO` | Application log level | No |
| `LOG_LEVEL_SPRING_WEB` | `INFO` | Spring Web log level | No |
| `LOG_LEVEL_HIBERNATE` | `WARN` | Hibernate log level | No |
| `LOG_LEVEL_SPRING_SECURITY` | `WARN` | Spring Security log level | No |
| `LOG_LEVEL_SPRING_AMQP` | `INFO` | Spring AMQP log level | No |
| `LOG_PATTERN_CONSOLE` | `%d{yyyy-MM-dd HH:mm:ss.SSS} [%thread] %-5level [%X{correlationId}] %logger{36} - %msg%n` | Console log pattern | No |
| `LOG_PATTERN_FILE` | `%d{yyyy-MM-dd HH:mm:ss.SSS} [%thread] %-5level [%X{correlationId}] %logger{36} - %msg%n` | File log pattern | No |

## Server Configuration

| Variable | Default | Description | Required |
|----------|---------|-------------|----------|
| `SERVER_COMPRESSION_ENABLED` | `false` | Enable server compression | No |
| `SERVER_COMPRESSION_MIME_TYPES` | `text/html,text/xml,text/plain,text/css,text/javascript,application/javascript,application/json` | Compression MIME types | No |
| `SERVER_COMPRESSION_MIN_SIZE` | `1024` | Minimum response size for compression | No |
| `SERVER_TOMCAT_CONNECTION_TIMEOUT` | `20000` | Tomcat connection timeout | No |
| `SERVER_TOMCAT_MAX_CONNECTIONS` | `200` | Maximum Tomcat connections | No |
| `SERVER_TOMCAT_THREADS_MAX` | `50` | Maximum Tomcat threads | No |
| `SERVER_TOMCAT_THREADS_MIN_SPARE` | `5` | Minimum spare Tomcat threads | No |

## Async Configuration

| Variable | Default | Description | Required |
|----------|---------|-------------|----------|
| `ASYNC_POOL_CORE_SIZE` | `5` | Async thread pool core size | No |
| `ASYNC_POOL_MAX_SIZE` | `10` | Async thread pool maximum size | No |
| `ASYNC_POOL_QUEUE_CAPACITY` | `50` | Async thread pool queue capacity | No |
| `ASYNC_THREAD_NAME_PREFIX` | `notification-async-` | Async thread name prefix | No |

## Cross-Service Communication

| Variable | Default | Description | Required |
|----------|---------|-------------|----------|
| `DMS_SERVICE_URL` | `http://localhost:9093` | DMS service URL | No |

## Development Features

| Variable | Default | Description | Required |
|----------|---------|-------------|----------|
| `DEVTOOLS_RESTART_ENABLED` | `false` | Enable Spring DevTools restart | No |
| `DEVTOOLS_LIVERELOAD_ENABLED` | `false` | Enable Spring DevTools live reload | No |

## Environment-Specific Defaults

### Development Environment
- `GRAPHIQL_ENABLED=true`
- `JPA_SHOW_SQL=true`
- `HIBERNATE_FORMAT_SQL=true`
- `EMAIL_MOCK=true`
- `LOG_LEVEL_APP=DEBUG`
- `DEVTOOLS_RESTART_ENABLED=true`
- `DEVTOOLS_LIVERELOAD_ENABLED=true`

### UAT Environment
- `JPA_DDL_AUTO=validate`
- `EMAIL_MOCK=false`
- `GRAPHIQL_ENABLED=true`
- `LOG_LEVEL_APP=INFO`

### Production Environment
- `SERVER_PORT=8080`
- `JPA_DDL_AUTO=validate`
- `JPA_SHOW_SQL=false`
- `GRAPHIQL_ENABLED=false`
- `EMAIL_MOCK=false`
- `LOG_LEVEL_APP=INFO`
- `SERVER_COMPRESSION_ENABLED=true`
- `ALLOW_BEAN_DEFINITION_OVERRIDING=false`

## Security Best Practices

1. **Never hardcode sensitive values** - Always use environment variables for:
   - Database passwords
   - JWT secrets
   - Email credentials
   - Redis passwords
   - RabbitMQ credentials

2. **Use strong, unique secrets** for production:
   - JWT secrets should be at least 256 bits (32 characters)
   - Database passwords should be complex and unique
   - Email credentials should use app-specific passwords when possible

3. **Restrict CORS origins** in production:
   - Never use wildcards (`*`) with credentials enabled
   - Specify exact domain names
   - Use HTTPS origins only in production

4. **Limit actuator endpoints** in production:
   - Only expose essential endpoints (`health`, `metrics`)
   - Never show detailed health information in production
   - Secure actuator endpoints with authentication

## Example Environment Files

### Development (.env.dev)
```bash
# Database
DB_USERNAME=dev_user
DB_PASSWORD=dev_password

# Security
JWT_SECRET=dev_secret_key_at_least_32_characters_long
CORS_ALLOWED_ORIGINS=http://localhost:4200,http://localhost:3000

# Email (Mock)
EMAIL_MOCK=true
NOTIFICATION_FROM_EMAIL=dev@localhost

# Redis
REDIS_PASSWORD=dev_redis_password
```

### Production (.env.prod)
```bash
# Database
DB_URL=*****************************************************
DB_USERNAME=notification_prod_user
DB_PASSWORD=super_secure_production_password

# Security
JWT_SECRET=production_jwt_secret_at_least_32_characters_very_secure
CORS_ALLOWED_ORIGINS=https://app.yourdomain.com,https://admin.yourdomain.com

# Email
MAIL_HOST=smtp.yourdomain.com
MAIL_USERNAME=<EMAIL>
MAIL_PASSWORD=secure_email_password
NOTIFICATION_FROM_EMAIL=<EMAIL>

# Redis
REDIS_HOST=prod-redis
REDIS_PASSWORD=secure_redis_password

# RabbitMQ
RABBITMQ_HOST=prod-rabbitmq
RABBITMQ_USERNAME=notification_user
RABBITMQ_PASSWORD=secure_rabbitmq_password
```

This comprehensive reference ensures all environment variables are properly documented and provides clear guidance for secure configuration across all environments.