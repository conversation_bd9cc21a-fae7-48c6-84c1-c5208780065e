# DMS Service Properties Configuration

## Overview
The DMS service properties have been simplified and reorganized to minimize complexity and reduce duplication. The configuration now follows a hierarchical approach with a base configuration and environment-specific overrides.

## File Structure

### 1. `application.properties` (Base Configuration)
- **Size**: 334 lines (reduced from 466 lines)
- **Purpose**: Contains all common configurations shared across environments
- **Features**:
  - Comprehensive environment variable support with sensible defaults
  - Well-organized sections with clear documentation
  - All configuration options available for customization via environment variables

### 2. `application-dev.properties` (Development Overrides)
- **Size**: 58 lines (reduced from 61 lines)
- **Purpose**: Development-specific overrides only
- **Key Features**:
  - SQL logging enabled
  - Debug logging levels
  - GraphiQL enabled
  - Relaxed security headers
  - Local storage configuration
  - Rate limiting disabled
  - Mock virus scanning

### 3. `application-uat.properties` (UAT Overrides)
- **Size**: 48 lines (reduced from 61 lines)
- **Purpose**: UAT-specific overrides only
- **Key Features**:
  - Schema validation mode
  - Info-level logging
  - GraphiQL disabled
  - Stricter security headers
  - Real virus scanning (ClamAV)
  - Rate limiting enabled

### 4. `application-prod.properties` (Production Overrides)
- **Size**: 82 lines (replaced template with 71 lines)
- **Purpose**: Production-specific overrides only
- **Key Features**:
  - SSL configuration
  - Minimal actuator exposure
  - Production-optimized connection pools
  - Encryption enabled
  - Strict security headers
  - Production storage providers (S3/SharePoint)

## Key Improvements

### 1. **Reduced Complexity**
- **Before**: 4 files with 659 total lines and significant duplication
- **After**: 4 files with 522 total lines (21% reduction) with minimal duplication

### 2. **Eliminated Redundancy**
- Removed duplicate configurations across environment files
- Environment files now contain only overrides, not repetitions
- Base configuration provides all defaults with environment variable support

### 3. **Improved Maintainability**
- Single source of truth for common configurations
- Environment-specific files are focused and concise
- Clear separation of concerns between base and environment-specific settings

### 4. **Enhanced Documentation**
- Well-organized sections in base configuration
- Clear comments explaining purpose of each section
- Environment-specific files clearly document their overrides

### 5. **Better Environment Variable Support**
- Consistent use of environment variables with sensible defaults
- All sensitive configurations externalized
- Production-ready configuration without hardcoded values

## Usage

### Running with Different Profiles

```bash
# Development
java -jar dms-svc.jar --spring.profiles.active=dev

# UAT
java -jar dms-svc.jar --spring.profiles.active=uat

# Production
java -jar dms-svc.jar --spring.profiles.active=prod
```

### Environment Variables

All configurations can be overridden using environment variables. Key variables include:

#### Database
- `DB_URL`, `DB_USERNAME`, `DB_PASSWORD`
- `DB_POOL_MAX_SIZE`, `DB_POOL_MIN_IDLE`

#### Security
- `JWT_SECRET`, `JWT_EXPIRATION`
- `CORS_ALLOWED_ORIGINS`

#### Storage
- `STORAGE_PROVIDER` (LOCAL, S3, SHAREPOINT)
- `S3_BUCKET_NAME`, `S3_ACCESS_KEY`, `S3_SECRET_KEY`
- `SHAREPOINT_CLIENT_ID`, `SHAREPOINT_CLIENT_SECRET`, `SHAREPOINT_TENANT_ID`

#### Observability
- `ELASTICSEARCH_ENABLED`, `ELASTICSEARCH_HOST`
- `REDIS_HOST`, `REDIS_PORT`, `REDIS_PASSWORD`

## Migration Notes

### What Changed
1. **Removed**: `application-prod.properties.template`
2. **Simplified**: All environment files now contain only overrides
3. **Consolidated**: Common configurations moved to base file
4. **Standardized**: Consistent environment variable usage

### Backward Compatibility
- All existing environment variables continue to work
- No breaking changes to application functionality
- Same configuration options available, just better organized

## Best Practices

1. **Never commit sensitive values** to version control
2. **Use environment variables** for all environment-specific values
3. **Test configuration changes** in development first
4. **Document any new environment variables** added to the system
5. **Keep environment files minimal** - only add overrides when necessary

## Validation

The simplified configuration maintains full functionality while reducing complexity:
- ✅ All original features preserved
- ✅ Environment-specific behaviors maintained
- ✅ Security configurations intact
- ✅ Performance optimizations retained
- ✅ Observability features functional