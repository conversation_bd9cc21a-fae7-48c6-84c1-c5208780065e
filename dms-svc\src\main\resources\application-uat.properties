# ========================================
# UAT ENVIRONMENT OVERRIDES
# ========================================
# This file contains only UAT-specific overrides.
# All other configurations are inherited from application.properties

# Environment Identifier
ENVIRONMENT=uat

# ========================================
# UAT-SPECIFIC OVERRIDES
# ========================================

# Database Configuration - Validate schema in UAT
spring.jpa.hibernate.ddl-auto=validate

# Logging Configuration - Info level for UAT
logging.level.com.ascentbusiness.dms_svc=INFO
logging.level.org.springframework.security=WARN
logging.level.org.hibernate=WARN

# GraphQL Configuration - Enable GraphiQL for UAT testing
spring.graphql.graphiql.enabled=true
spring.graphql.schema.printer.enabled=false

# Security Configuration - Stricter for UAT
dms.security.headers.csp.report-only=false
dms.security.headers.hsts.enabled=true

# Cache Configuration - Standard TTL for UAT
spring.cache.redis.time-to-live=1800000
spring.cache.redis.key-prefix=dms:uat:

# OpenTelemetry Configuration - Reduced sampling for UAT
otel.traces.sampler.arg=0.3

# Development Features - Disabled for UAT
spring.devtools.restart.enabled=false
spring.devtools.livereload.enabled=false

# File Upload Configuration - Standard limits for UAT
spring.servlet.multipart.max-file-size=100MB
spring.servlet.multipart.max-request-size=100MB

# Actuator Configuration - Limited exposure for UAT
management.endpoints.web.exposure.include=health,info,metrics,prometheus
management.endpoint.health.show-details=when-authorized

# Rate Limiting - Enabled for UAT
dms.rate-limit.enabled=true

# Virus Scanning - Real scanning for UAT
dms.virus-scanning.default-scanner=CLAMAV
dms.virus-scanner.clamav.enabled=true

# Storage Configuration - Environment specific
dms.storage.provider=${STORAGE_PROVIDER:LOCAL}

# Elasticsearch - Enabled for UAT
elasticsearch.enabled=true

# Redis health check - Enabled for UAT
management.health.redis.enabled=true

# Connection Pool Configuration - UAT optimized
spring.datasource.hikari.maximum-pool-size=15
spring.datasource.hikari.minimum-idle=3

# Performance Configuration - UAT optimized
server.tomcat.max-connections=4096
server.tomcat.threads.max=100
server.tomcat.threads.min-spare=8