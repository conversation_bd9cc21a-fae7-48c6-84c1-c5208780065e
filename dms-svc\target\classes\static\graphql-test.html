<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>DMS GraphQL Test</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        .status {
            padding: 10px;
            margin: 10px 0;
            border-radius: 4px;
        }
        .success { background-color: #d4edda; color: #155724; border: 1px solid #c3e6cb; }
        .error { background-color: #f8d7da; color: #721c24; border: 1px solid #f5c6cb; }
        .info { background-color: #d1ecf1; color: #0c5460; border: 1px solid #bee5eb; }
        button {
            background-color: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover { background-color: #0056b3; }
        textarea {
            width: 100%;
            height: 200px;
            font-family: monospace;
            padding: 10px;
            border: 1px solid #ddd;
            border-radius: 4px;
        }
        pre {
            background-color: #f8f9fa;
            padding: 10px;
            border-radius: 4px;
            overflow-x: auto;
            white-space: pre-wrap;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>DMS GraphQL Connectivity Test</h1>
        
        <div id="status" class="status info">
            Ready to test GraphQL connectivity...
        </div>

        <h3>Test GraphQL Endpoint</h3>
        <button onclick="testGraphQLEndpoint()">Test /graphql Endpoint</button>
        <button onclick="testGraphiQLEndpoint()">Test /graphiql Endpoint</button>
        <button onclick="testIntrospectionQuery()">Test Schema Introspection</button>
        <button onclick="clearResults()">Clear Results</button>

        <h3>Custom Query Test</h3>
        <textarea id="customQuery" placeholder="Enter your GraphQL query here...">
query {
  __schema {
    types {
      name
    }
  }
}
        </textarea>
        <br>
        <button onclick="testCustomQuery()">Execute Custom Query</button>

        <h3>Results</h3>
        <pre id="results">No tests run yet...</pre>
    </div>

    <script>
        function updateStatus(message, type = 'info') {
            const statusDiv = document.getElementById('status');
            statusDiv.textContent = message;
            statusDiv.className = `status ${type}`;
        }

        function updateResults(content) {
            document.getElementById('results').textContent = content;
        }

        function appendResults(content) {
            const resultsDiv = document.getElementById('results');
            resultsDiv.textContent += '\n\n' + content;
        }

        async function testGraphQLEndpoint() {
            updateStatus('Testing GraphQL endpoint...', 'info');
            
            try {
                const response = await fetch('/graphql', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({
                        query: '{ __typename }'
                    })
                });

                const result = await response.json();
                
                if (response.ok) {
                    updateStatus('GraphQL endpoint is working!', 'success');
                    updateResults(`GraphQL Endpoint Test - SUCCESS\nStatus: ${response.status}\nResponse: ${JSON.stringify(result, null, 2)}`);
                } else {
                    updateStatus('GraphQL endpoint returned an error', 'error');
                    updateResults(`GraphQL Endpoint Test - ERROR\nStatus: ${response.status}\nResponse: ${JSON.stringify(result, null, 2)}`);
                }
            } catch (error) {
                updateStatus('Failed to connect to GraphQL endpoint', 'error');
                updateResults(`GraphQL Endpoint Test - FAILED\nError: ${error.message}`);
            }
        }

        async function testGraphiQLEndpoint() {
            updateStatus('Testing GraphiQL endpoint...', 'info');
            
            try {
                const response = await fetch('/graphiql', {
                    method: 'GET',
                    headers: {
                        'Accept': 'text/html',
                    }
                });

                if (response.ok) {
                    const html = await response.text();
                    updateStatus('GraphiQL endpoint is accessible!', 'success');
                    appendResults(`GraphiQL Endpoint Test - SUCCESS\nStatus: ${response.status}\nContent-Type: ${response.headers.get('content-type')}\nHTML Length: ${html.length} characters`);
                } else {
                    updateStatus('GraphiQL endpoint returned an error', 'error');
                    appendResults(`GraphiQL Endpoint Test - ERROR\nStatus: ${response.status}\nStatus Text: ${response.statusText}`);
                }
            } catch (error) {
                updateStatus('Failed to connect to GraphiQL endpoint', 'error');
                appendResults(`GraphiQL Endpoint Test - FAILED\nError: ${error.message}`);
            }
        }

        async function testIntrospectionQuery() {
            updateStatus('Testing GraphQL schema introspection...', 'info');
            
            const introspectionQuery = `
                query IntrospectionQuery {
                    __schema {
                        queryType { name }
                        mutationType { name }
                        subscriptionType { name }
                        types {
                            ...FullType
                        }
                    }
                }
                
                fragment FullType on __Type {
                    kind
                    name
                    description
                }
            `;

            try {
                const response = await fetch('/graphql', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({
                        query: introspectionQuery
                    })
                });

                const result = await response.json();
                
                if (response.ok && !result.errors) {
                    updateStatus('Schema introspection successful!', 'success');
                    const typeCount = result.data?.__schema?.types?.length || 0;
                    appendResults(`Schema Introspection Test - SUCCESS\nTypes found: ${typeCount}\nQuery Type: ${result.data?.__schema?.queryType?.name}\nMutation Type: ${result.data?.__schema?.mutationType?.name}`);
                } else {
                    updateStatus('Schema introspection failed', 'error');
                    appendResults(`Schema Introspection Test - ERROR\nStatus: ${response.status}\nErrors: ${JSON.stringify(result.errors || result, null, 2)}`);
                }
            } catch (error) {
                updateStatus('Schema introspection request failed', 'error');
                appendResults(`Schema Introspection Test - FAILED\nError: ${error.message}`);
            }
        }

        async function testCustomQuery() {
            const query = document.getElementById('customQuery').value.trim();
            if (!query) {
                updateStatus('Please enter a query', 'error');
                return;
            }

            updateStatus('Executing custom query...', 'info');
            
            try {
                const response = await fetch('/graphql', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({
                        query: query
                    })
                });

                const result = await response.json();
                
                if (response.ok) {
                    updateStatus('Custom query executed!', 'success');
                    appendResults(`Custom Query Test - SUCCESS\nQuery: ${query}\nResponse: ${JSON.stringify(result, null, 2)}`);
                } else {
                    updateStatus('Custom query failed', 'error');
                    appendResults(`Custom Query Test - ERROR\nStatus: ${response.status}\nResponse: ${JSON.stringify(result, null, 2)}`);
                }
            } catch (error) {
                updateStatus('Custom query request failed', 'error');
                appendResults(`Custom Query Test - FAILED\nError: ${error.message}`);
            }
        }

        function clearResults() {
            updateResults('Results cleared...');
            updateStatus('Ready to test GraphQL connectivity...', 'info');
        }

        // Auto-test on page load
        window.addEventListener('load', function() {
            setTimeout(() => {
                testGraphQLEndpoint();
            }, 1000);
        });
    </script>
</body>
</html>
