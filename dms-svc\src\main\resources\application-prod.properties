# ========================================
# PRODUCTION ENVIRONMENT OVERRIDES
# ========================================
# This file contains only production-specific overrides.
# All other configurations are inherited from application.properties
# 
# IMPORTANT: All sensitive values MUST be provided via environment variables
# DO NOT commit sensitive values to version control

# Environment Identifier
ENVIRONMENT=production

# ========================================
# PRODUCTION-SPECIFIC OVERRIDES
# ========================================

# Server Configuration - Production port
server.port=${SERVER_PORT:8080}

# Database Configuration - Strict validation for production
spring.jpa.hibernate.ddl-auto=validate
spring.jpa.show-sql=false
spring.jpa.properties.hibernate.format_sql=false

# Connection Pool Configuration - Production optimized
spring.datasource.hikari.maximum-pool-size=${DB_POOL_SIZE:30}
spring.datasource.hikari.minimum-idle=${DB_POOL_MIN_IDLE:10}
spring.datasource.hikari.connection-timeout=${DB_CONNECTION_TIMEOUT:30000}
spring.datasource.hikari.idle-timeout=${DB_IDLE_TIMEOUT:600000}
spring.datasource.hikari.max-lifetime=${DB_MAX_LIFETIME:1800000}

# Logging Configuration - Production level
logging.level.com.ascentbusiness.dms_svc=INFO
logging.level.org.springframework.security=WARN
logging.level.org.hibernate.SQL=WARN

# GraphQL Configuration - Disabled for production
spring.graphql.graphiql.enabled=false

# Security Configuration - Strict for production
dms.security.headers.csp.report-only=false
dms.security.headers.hsts.enabled=true
dms.security.headers.hsts.max-age=31536000
dms.security.headers.hsts.include-subdomains=true
dms.security.headers.hsts.preload=true

# SSL Configuration - Required for production
server.ssl.enabled=${SSL_ENABLED:true}
server.ssl.key-store=${SSL_KEYSTORE_PATH}
server.ssl.key-store-password=${SSL_KEYSTORE_PASSWORD}
server.ssl.key-store-type=${SSL_KEYSTORE_TYPE:PKCS12}

# Cache Configuration - Production TTL
spring.cache.redis.key-prefix=dms:prod:

# OpenTelemetry Configuration - Minimal sampling for production
otel.traces.sampler.arg=0.01

# Actuator Configuration - Minimal exposure for production
management.endpoints.web.exposure.include=health,metrics
management.endpoint.health.show-details=never
management.endpoints.web.base-path=/actuator
management.server.port=${MANAGEMENT_PORT:8081}

# Rate Limiting - Enabled for production
dms.rate-limit.enabled=true

# Virus Scanning - Production scanner
dms.virus-scanning.default-scanner=${VIRUS_SCANNING_DEFAULT_SCANNER:CLAMAV}
dms.virus-scanner.clamav.enabled=true
dms.virus-scanner.mock.enabled=false

# Storage Configuration - Production storage (S3 or SharePoint)
dms.storage.provider=${STORAGE_PROVIDER:S3}

# File Upload Configuration - Production limits
spring.servlet.multipart.max-file-size=${MAX_FILE_SIZE:200MB}
spring.servlet.multipart.max-request-size=${MAX_REQUEST_SIZE:200MB}

# Elasticsearch - Enabled for production
elasticsearch.enabled=true
elasticsearch.protocol=https

# Redis health check - Enabled for production
management.health.redis.enabled=true

# Performance Configuration - Production optimized
spring.jpa.properties.hibernate.jdbc.batch_size=50
spring.jpa.properties.hibernate.order_inserts=true
spring.jpa.properties.hibernate.order_updates=true

# Encryption - Enabled for production
dms.audit.encryption.enabled=true
dms.pii.encryption.enabled=true