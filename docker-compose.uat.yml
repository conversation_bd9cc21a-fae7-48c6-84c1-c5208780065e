# UAT Environment Docker Compose for AWS EC2
# This file extends the shared infrastructure for UAT deployment
version: '3.8'

services:
  # DMS Application Service - UAT Configuration (matching dev environment)
  dms-svc:
    image: ${DMS_IMAGE:-dms-service:latest}
    container_name: dms-svc-uat
    ports:
      - "${DMS_PORT:-9093}:9093"
      - "${DMS_METRICS_PORT:-9464}:9464"
    environment:
      - SPRING_PROFILES_ACTIVE=uat
      - SERVER_PORT=${SERVER_PORT:-9093}
      - DB_URL=*********************************************************************************************************************************************
      - DB_USERNAME=${DB_USERNAME:-dms_uat_user}
      - DB_PASSWORD=${DB_PASSWORD}
      - REDIS_HOST=grc-redis-shared
      - REDIS_PORT=6379
      - REDIS_PASSWORD=${REDIS_PASSWORD}
      - STORAGE_PROVIDER=${STORAGE_PROVIDER:-LOCAL}
      - STORAGE_PATH=/app/storage
      - S3_BUCKET_NAME=${S3_BUCKET_NAME}
      - S3_REGION=${S3_REGION:-us-east-1}
      - S3_ACCESS_KEY=${S3_ACCESS_KEY}
      - S3_SECRET_KEY=${S3_SECRET_KEY}
      - JWT_SECRET=${JWT_SECRET}
      - GRAPHIQL_ENABLED=false
      - CORS_ALLOWED_ORIGINS=${CORS_ALLOWED_ORIGINS}
      - CORS_ALLOWED_METHODS=${CORS_ALLOWED_METHODS:-GET,POST,PUT,DELETE,OPTIONS}
      - CORS_ALLOWED_HEADERS=${CORS_ALLOWED_HEADERS:-Content-Type,Authorization,X-Correlation-ID,Accept}
      - CORS_ALLOW_CREDENTIALS=${CORS_ALLOW_CREDENTIALS:-true}
      - LOG_LEVEL_DMS=INFO
      - LOG_LEVEL_SECURITY=WARN
      - CACHE_TYPE=redis
      - CACHE_TTL=1800000
      - CACHE_KEY_PREFIX=dms:uat:
      - ELASTICSEARCH_ENABLED=${ELASTICSEARCH_ENABLED:-true}
      - ELASTICSEARCH_HOST=grc-elasticsearch-shared
      - ELASTICSEARCH_PORT=9200
      - VIRUS_SCANNING_ENABLED=true
      - VIRUS_SCANNING_DEFAULT_SCANNER=CLAMAV
      - RATE_LIMIT_ENABLED=true
      - OTEL_SERVICE_NAME=dms-svc-uat
      - OTEL_SERVICE_VERSION=1.0.0-uat
      - OTEL_TRACES_SAMPLER_ARG=0.1
      - OTEL_TRACES_EXPORTER=zipkin
      - OTEL_EXPORTER_ZIPKIN_ENDPOINT=http://grc-zipkin-shared:9411/api/v2/spans
      - OTEL_METRICS_EXPORTER=console
      - OTEL_LOGS_EXPORTER=console
      - DMS_BASE_URL=${DMS_BASE_URL:-https://dms-uat.company.com}
      - ENVIRONMENT=uat
      - SECURITY_HEADERS_CSP_ENABLED=true
      - SECURITY_HEADERS_HSTS_ENABLED=true
    volumes:
      - dms_storage:/app/storage
      - dms_logs:/app/logs
    depends_on:
      grc-mysql-shared:
        condition: service_healthy
      grc-redis-shared:
        condition: service_healthy
      grc-elasticsearch-shared:
        condition: service_healthy
      grc-zipkin-shared:
        condition: service_started
    networks:
      - grc-shared-network
    restart: unless-stopped
    deploy:
      resources:
        limits:
          cpus: '2.0'
          memory: 3G
        reservations:
          cpus: '1.0'
          memory: 1G
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:9093/actuator/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 120s
    logging:
      driver: "json-file"
      options:
        max-size: "100m"
        max-file: "5"

  # Notification Service - UAT Configuration (matching dev environment)
  notification-svc:
    image: ${NOTIFICATION_IMAGE:-notification-service:latest}
    container_name: notification-svc-uat
    ports:
      - "${NOTIFICATION_PORT:-9091}:9091"
    environment:
      - SPRING_PROFILES_ACTIVE=uat
      - SERVER_PORT=9091
      - DB_URL=**************************************************************************************************************************
      - DB_USERNAME=${NOTIFICATION_DB_USER:-notification_uat_user}
      - DB_PASSWORD=${NOTIFICATION_DB_PASSWORD}
      - SPRING_RABBITMQ_HOST=grc-rabbitmq-shared
      - SPRING_RABBITMQ_PORT=5672
      - SPRING_RABBITMQ_USERNAME=${RABBITMQ_USER:-admin}
      - SPRING_RABBITMQ_PASSWORD=${RABBITMQ_PASSWORD:-admin123}
      - SPRING_RABBITMQ_VIRTUAL_HOST=${RABBITMQ_VHOST:-/}
      - REDIS_HOST=grc-redis-shared
      - REDIS_PORT=6379
      - REDIS_PASSWORD=${REDIS_PASSWORD}
      - JAVA_OPTS=-Xmx1g -Xms512m
      - SECURITY_JWT_ENABLED=true
      - JWT_ISSUER=notification-service
      - JWT_AUDIENCE=notification-clients
      - JWT_SECRET=${JWT_SECRET}
      - CORS_ALLOWED_ORIGINS=${CORS_ALLOWED_ORIGINS}
      - CORS_ALLOWED_METHODS=${CORS_ALLOWED_METHODS:-GET,POST,PUT,DELETE,OPTIONS}
      - CORS_ALLOWED_HEADERS=${CORS_ALLOWED_HEADERS:-Content-Type,Authorization,X-Correlation-ID,Accept}
      - CORS_ALLOW_CREDENTIALS=${CORS_ALLOW_CREDENTIALS:-true}
      - SPRING_MAIL_HOST=${MAIL_HOST}
      - SPRING_MAIL_PORT=${MAIL_PORT}
      - SPRING_MAIL_USERNAME=${MAIL_USERNAME}
      - SPRING_MAIL_PASSWORD=${MAIL_PASSWORD}
      - SPRING_MAIL_PROPERTIES_MAIL_SMTP_AUTH=true
      - SPRING_MAIL_PROPERTIES_MAIL_SMTP_STARTTLS_ENABLE=true
      - SPRING_MAIL_PROPERTIES_MAIL_SMTP_STARTTLS_REQUIRED=true
      - SPRING_MAIL_PROPERTIES_MAIL_SMTP_SSL_TRUST=*
      - NOTIFICATION_EMAIL_FROM=${NOTIFICATION_FROM_EMAIL}
      - EMAIL_ENABLED=${EMAIL_ENABLED}
      - EMAIL_MOCK=${EMAIL_MOCK}
      - GRAPHIQL_ENABLED=false
      - ENVIRONMENT=uat
    volumes:
      - notification_logs:/app/logs
      - ./notification-svc/keys:/app/keys:ro
    depends_on:
      grc-mysql-shared:
        condition: service_healthy
      grc-rabbitmq-shared:
        condition: service_healthy
      grc-redis-shared:
        condition: service_healthy
    networks:
      - grc-shared-network
    restart: unless-stopped
    deploy:
      resources:
        limits:
          cpus: '1.0'
          memory: 2G
        reservations:
          cpus: '0.5'
          memory: 512M
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:9091/actuator/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 120s
    logging:
      driver: "json-file"
      options:
        max-size: "100m"
        max-file: "5"

volumes:
  dms_storage:
    driver: local
  dms_logs:
    driver: local
  notification_logs:
    driver: local

networks:
  grc-shared-network:
    external: true