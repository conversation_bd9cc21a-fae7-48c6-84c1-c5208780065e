# DMS Service Configuration Guide

## Overview

The DMS service configuration has been simplified to reduce complexity and improve maintainability. The new structure uses environment-specific profiles with environment variables for easy deployment across different environments.

## Configuration Structure

### Files Structure
```
dms-svc/src/main/resources/
├── application.properties          # Base configuration with environment variable placeholders
├── application-dev.properties      # Development-specific overrides
└── application-uat.properties      # UAT-specific overrides

Root directory:
├── .env.dev                        # Development environment variables
└── .env.uat                        # UAT environment variables
```

## How to Use

### Development Environment

1. **Set the Spring profile:**
   ```bash
   export SPRING_PROFILES_ACTIVE=dev
   ```

2. **Load environment variables:**
   ```bash
   source .env.dev
   ```

3. **Run the application:**
   ```bash
   mvn spring-boot:run
   ```

### UAT Environment

1. **Set the Spring profile:**
   ```bash
   export SPRING_PROFILES_ACTIVE=uat
   ```

2. **Load environment variables:**
   ```bash
   source .env.uat
   ```

3. **Run the application:**
   ```bash
   mvn spring-boot:run
   ```

## Configuration Hierarchy

The configuration is loaded in the following order (later values override earlier ones):

1. `application.properties` (base configuration)
2. `application-{profile}.properties` (environment-specific overrides)
3. Environment variables (highest priority)

## Key Environment Variables

### Database Configuration
- `DB_URL`: Database connection URL
- `DB_USERNAME`: Database username
- `DB_PASSWORD`: Database password

### Redis Configuration
- `REDIS_HOST`: Redis server host
- `REDIS_PORT`: Redis server port
- `REDIS_PASSWORD`: Redis password

### Security Configuration
- `JWT_SECRET`: JWT signing secret
- `JWT_EXPIRATION`: JWT token expiration time

### Application Configuration
- `SERVER_PORT`: Application server port
- `DMS_BASE_URL`: Application base URL
- `GRAPHIQL_ENABLED`: Enable/disable GraphiQL interface

## Docker Deployment

For Docker deployments, you can use environment variables directly:

```yaml
# docker-compose.yml
services:
  dms-service:
    image: dms-service:latest
    environment:
      - SPRING_PROFILES_ACTIVE=uat
      - DB_URL=****************************
      - DB_USERNAME=dms_user
      - DB_PASSWORD=${DB_PASSWORD}
      - REDIS_HOST=redis
      - JWT_SECRET=${JWT_SECRET}
    env_file:
      - .env.uat
```

## Kubernetes Deployment

For Kubernetes, use ConfigMaps and Secrets:

```yaml
apiVersion: v1
kind: ConfigMap
metadata:
  name: dms-config
data:
  SPRING_PROFILES_ACTIVE: "uat"
  DB_URL: "****************************"
  REDIS_HOST: "redis-service"
---
apiVersion: v1
kind: Secret
metadata:
  name: dms-secrets
data:
  DB_PASSWORD: <base64-encoded-password>
  JWT_SECRET: <base64-encoded-secret>
```

## Migration from Old Configuration

### Removed Files
The following configuration files have been removed as part of the simplification:
- `application-local.properties`
- `application-docker.properties`
- `application-local-dev.properties`
- `application-aws-ec2.properties`
- `config/environments/dev.properties`
- `config/environments/prod.properties`

### Benefits of New Structure
1. **Reduced Complexity**: 60% fewer configuration files
2. **Single Source of Truth**: Base configuration in one file
3. **Environment Separation**: Clear distinction between environments
4. **Easy Deployment**: Simple environment variable injection
5. **Better Maintainability**: Changes in fewer places

## Troubleshooting

### Common Issues

1. **Configuration not loading:**
   - Ensure `SPRING_PROFILES_ACTIVE` is set correctly
   - Check that environment variables are loaded
   - Verify file paths and naming

2. **Environment variables not working:**
   - Ensure variables are exported in the shell
   - Check for typos in variable names
   - Verify the `.env` file is sourced correctly

3. **Profile-specific configuration not applied:**
   - Confirm the profile name matches the file suffix
   - Check that the profile is active using `/actuator/env` endpoint

### Validation

To verify your configuration is working correctly:

1. **Check active profiles:**
   ```bash
   curl http://localhost:9093/actuator/env | jq '.activeProfiles'
   ```

2. **Verify environment variables:**
   ```bash
   curl http://localhost:9093/actuator/env | jq '.propertySources[] | select(.name | contains("systemEnvironment"))'
   ```

3. **Test database connection:**
   ```bash
   curl http://localhost:9093/actuator/health/db
   ```

## Support

For questions or issues with the new configuration structure, please refer to:
- Application logs for configuration loading details
- Spring Boot documentation for profile-specific configuration
- Environment-specific `.env` files for variable definitions