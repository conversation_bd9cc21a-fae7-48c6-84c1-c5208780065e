<?xml version="1.0" encoding="UTF-8"?><!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Strict//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-strict.dtd"><html xmlns="http://www.w3.org/1999/xhtml" lang="en"><head><meta http-equiv="Content-Type" content="text/html;charset=UTF-8"/><link rel="stylesheet" href="../jacoco-resources/report.css" type="text/css"/><link rel="shortcut icon" href="../jacoco-resources/report.gif" type="image/gif"/><title>DocumentRetentionResolver.java</title><link rel="stylesheet" href="../jacoco-resources/prettify.css" type="text/css"/><script type="text/javascript" src="../jacoco-resources/prettify.js"></script></head><body onload="window['PR_TAB_WIDTH']=4;prettyPrint()"><div class="breadcrumb" id="breadcrumb"><span class="info"><a href="../jacoco-sessions.html" class="el_session">Sessions</a></span><a href="../index.html" class="el_report">dms-svc</a> &gt; <a href="index.source.html" class="el_package">com.ascentbusiness.dms_svc.resolver</a> &gt; <span class="el_source">DocumentRetentionResolver.java</span></div><h1>DocumentRetentionResolver.java</h1><pre class="source lang-java linenums">package com.ascentbusiness.dms_svc.resolver;

import com.ascentbusiness.dms_svc.dto.DispositionReviewInput;
import com.ascentbusiness.dms_svc.entity.Document;
import com.ascentbusiness.dms_svc.entity.RetentionPolicy;
import com.ascentbusiness.dms_svc.enums.DispositionStatus;
import com.ascentbusiness.dms_svc.exception.ResourceNotFoundException;
import com.ascentbusiness.dms_svc.repository.DocumentRepository;
import com.ascentbusiness.dms_svc.security.UserContext;
import com.ascentbusiness.dms_svc.service.RetentionPolicyService;
import com.ascentbusiness.dms_svc.service.AuditService;
import com.ascentbusiness.dms_svc.enums.AuditAction;
import com.ascentbusiness.dms_svc.util.CorrelationIdUtil;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Sort;
import org.springframework.graphql.data.method.annotation.Argument;
import org.springframework.graphql.data.method.annotation.MutationMapping;
import org.springframework.graphql.data.method.annotation.QueryMapping;
import org.springframework.stereotype.Controller;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;
import java.util.Optional;

/**
 * GraphQL resolver for document retention operations
 */
@Controller
<span class="fc" id="L35">public class DocumentRetentionResolver {</span>
    
<span class="fc" id="L37">    private static final Logger logger = LoggerFactory.getLogger(DocumentRetentionResolver.class);</span>
    
    @Autowired
    private DocumentRepository documentRepository;
    
    @Autowired
    private RetentionPolicyService retentionPolicyService;
    
    @Autowired
    private AuditService auditService;

    @Autowired
    private UserContext userContext;
    
    // Query resolvers
    
    @QueryMapping
    public Map&lt;String, Object&gt; getDocumentsWithoutRetentionPolicy(@Argument Map&lt;String, Object&gt; pagination) {
<span class="nc" id="L55">        String correlationId = CorrelationIdUtil.getCurrentCorrelationId();</span>
<span class="nc" id="L56">        logger.info(&quot;GraphQL query: getDocumentsWithoutRetentionPolicy [{}]&quot;, correlationId);</span>
        
<span class="nc" id="L58">        Pageable pageable = createPageable(pagination);</span>
<span class="nc" id="L59">        Page&lt;Document&gt; documentPage = documentRepository.findDocumentsWithoutRetentionPolicy(pageable);</span>
        
<span class="nc" id="L61">        return Map.of(</span>
<span class="nc" id="L62">            &quot;content&quot;, documentPage.getContent(),</span>
<span class="nc" id="L63">            &quot;totalElements&quot;, documentPage.getTotalElements(),</span>
<span class="nc" id="L64">            &quot;totalPages&quot;, documentPage.getTotalPages(),</span>
<span class="nc" id="L65">            &quot;size&quot;, documentPage.getSize(),</span>
<span class="nc" id="L66">            &quot;number&quot;, documentPage.getNumber(),</span>
<span class="nc" id="L67">            &quot;first&quot;, documentPage.isFirst(),</span>
<span class="nc" id="L68">            &quot;last&quot;, documentPage.isLast()</span>
        );
    }
    
    @QueryMapping
    public Map&lt;String, Object&gt; getDocumentsRequiringReview(@Argument Map&lt;String, Object&gt; pagination) {
<span class="nc" id="L74">        String correlationId = CorrelationIdUtil.getCurrentCorrelationId();</span>
<span class="nc" id="L75">        logger.info(&quot;GraphQL query: getDocumentsRequiringReview [{}]&quot;, correlationId);</span>
        
<span class="nc" id="L77">        Pageable pageable = createPageable(pagination);</span>
<span class="nc" id="L78">        Page&lt;Document&gt; documentPage = documentRepository.findDocumentsRequiringReview(pageable);</span>
        
<span class="nc" id="L80">        return Map.of(</span>
<span class="nc" id="L81">            &quot;content&quot;, documentPage.getContent(),</span>
<span class="nc" id="L82">            &quot;totalElements&quot;, documentPage.getTotalElements(),</span>
<span class="nc" id="L83">            &quot;totalPages&quot;, documentPage.getTotalPages(),</span>
<span class="nc" id="L84">            &quot;size&quot;, documentPage.getSize(),</span>
<span class="nc" id="L85">            &quot;number&quot;, documentPage.getNumber(),</span>
<span class="nc" id="L86">            &quot;first&quot;, documentPage.isFirst(),</span>
<span class="nc" id="L87">            &quot;last&quot;, documentPage.isLast()</span>
        );
    }
    
    @QueryMapping
    public List&lt;Document&gt; getDocumentsExpiringWithin(@Argument Integer days) {
<span class="nc" id="L93">        String correlationId = CorrelationIdUtil.getCurrentCorrelationId();</span>
<span class="nc" id="L94">        logger.info(&quot;GraphQL query: getDocumentsExpiringWithin(days: {}) [{}]&quot;, days, correlationId);</span>
        
<span class="nc" id="L96">        LocalDateTime startDate = LocalDateTime.now();</span>
<span class="nc" id="L97">        LocalDateTime endDate = startDate.plusDays(days);</span>
        
<span class="nc" id="L99">        return documentRepository.findDocumentsExpiringBetween(startDate, endDate);</span>
    }
    
    @QueryMapping
    public Map&lt;String, Object&gt; getExpiredDocuments(@Argument Map&lt;String, Object&gt; pagination) {
<span class="nc" id="L104">        String correlationId = CorrelationIdUtil.getCurrentCorrelationId();</span>
<span class="nc" id="L105">        logger.info(&quot;GraphQL query: getExpiredDocuments [{}]&quot;, correlationId);</span>
        
<span class="nc" id="L107">        Pageable pageable = createPageable(pagination);</span>
<span class="nc" id="L108">        LocalDateTime now = LocalDateTime.now();</span>
<span class="nc" id="L109">        Page&lt;Document&gt; documentPage = documentRepository.findExpiredDocuments(now, pageable);</span>
        
<span class="nc" id="L111">        return Map.of(</span>
<span class="nc" id="L112">            &quot;content&quot;, documentPage.getContent(),</span>
<span class="nc" id="L113">            &quot;totalElements&quot;, documentPage.getTotalElements(),</span>
<span class="nc" id="L114">            &quot;totalPages&quot;, documentPage.getTotalPages(),</span>
<span class="nc" id="L115">            &quot;size&quot;, documentPage.getSize(),</span>
<span class="nc" id="L116">            &quot;number&quot;, documentPage.getNumber(),</span>
<span class="nc" id="L117">            &quot;first&quot;, documentPage.isFirst(),</span>
<span class="nc" id="L118">            &quot;last&quot;, documentPage.isLast()</span>
        );
    }
    
    // Mutation resolvers
    
    @MutationMapping
    public Document assignRetentionPolicyToDocument(@Argument Long documentId, @Argument Long policyId) {
<span class="nc" id="L126">        String correlationId = CorrelationIdUtil.getCurrentCorrelationId();</span>
<span class="nc" id="L127">        String currentUserId = userContext.getCurrentUserId();</span>
        
<span class="nc" id="L129">        logger.info(&quot;GraphQL mutation: assignRetentionPolicyToDocument(documentId: {}, policyId: {}) [{}]&quot;, </span>
                documentId, policyId, correlationId);
        
<span class="nc" id="L132">        Document document = documentRepository.findById(documentId)</span>
<span class="nc" id="L133">                .orElseThrow(() -&gt; new ResourceNotFoundException(&quot;Document not found with ID: &quot; + documentId));</span>
        
<span class="nc" id="L135">        RetentionPolicy policy = retentionPolicyService.getRetentionPolicy(policyId);</span>
        
<span class="nc" id="L137">        document.setRetentionPolicy(policy);</span>
<span class="nc" id="L138">        document.setRetentionExpiryDate(document.calculateRetentionExpiryDate());</span>
        
<span class="nc" id="L140">        Document savedDocument = documentRepository.save(document);</span>
        
<span class="nc" id="L142">        auditService.logAudit(AuditAction.RETENTION_POLICY_ASSIGNED, documentId, currentUserId,</span>
<span class="nc" id="L143">                String.format(&quot;Manually assigned retention policy '%s' to document '%s'&quot;, </span>
<span class="nc" id="L144">                        policy.getName(), document.getName()));</span>
        
<span class="nc" id="L146">        return savedDocument;</span>
    }
    
    @MutationMapping
    public Document removeRetentionPolicyFromDocument(@Argument Long documentId) {
<span class="nc" id="L151">        String correlationId = CorrelationIdUtil.getCurrentCorrelationId();</span>
<span class="nc" id="L152">        String currentUserId = userContext.getCurrentUserId();</span>
        
<span class="nc" id="L154">        logger.info(&quot;GraphQL mutation: removeRetentionPolicyFromDocument(documentId: {}) [{}]&quot;, </span>
                documentId, correlationId);
        
<span class="nc" id="L157">        Document document = documentRepository.findById(documentId)</span>
<span class="nc" id="L158">                .orElseThrow(() -&gt; new ResourceNotFoundException(&quot;Document not found with ID: &quot; + documentId));</span>
        
<span class="nc bnc" id="L160" title="All 2 branches missed.">        String previousPolicyName = document.getRetentionPolicy() != null ? </span>
<span class="nc" id="L161">                document.getRetentionPolicy().getName() : &quot;None&quot;;</span>
        
<span class="nc" id="L163">        document.setRetentionPolicy(null);</span>
<span class="nc" id="L164">        document.setRetentionExpiryDate(null);</span>
<span class="nc" id="L165">        document.setDispositionStatus(DispositionStatus.ACTIVE);</span>
        
<span class="nc" id="L167">        Document savedDocument = documentRepository.save(document);</span>
        
<span class="nc" id="L169">        auditService.logAudit(AuditAction.RETENTION_POLICY_UNASSIGNED, documentId, currentUserId,</span>
<span class="nc" id="L170">                String.format(&quot;Removed retention policy '%s' from document '%s'&quot;, </span>
<span class="nc" id="L171">                        previousPolicyName, document.getName()));</span>
        
<span class="nc" id="L173">        return savedDocument;</span>
    }
    
    @MutationMapping
    public Document approveDisposition(@Argument DispositionReviewInput input) {
<span class="nc" id="L178">        String correlationId = CorrelationIdUtil.getCurrentCorrelationId();</span>
<span class="nc" id="L179">        String currentUserId = userContext.getCurrentUserId();</span>
        
<span class="nc" id="L181">        logger.info(&quot;GraphQL mutation: approveDisposition(documentId: {}, approved: {}) [{}]&quot;, </span>
<span class="nc" id="L182">                input.getDocumentId(), input.getApproved(), correlationId);</span>
        
<span class="nc" id="L184">        Document document = documentRepository.findById(input.getDocumentId())</span>
<span class="nc" id="L185">                .orElseThrow(() -&gt; new ResourceNotFoundException(&quot;Document not found with ID: &quot; + input.getDocumentId()));</span>
        
<span class="nc bnc" id="L187" title="All 2 branches missed.">        if (input.getApproved()) {</span>
<span class="nc" id="L188">            document.setDispositionStatus(DispositionStatus.ELIGIBLE);</span>
<span class="nc" id="L189">            document.setDispositionNotes(input.getNotes());</span>
            
<span class="nc" id="L191">            auditService.logAudit(AuditAction.DISPOSITION_APPROVED, input.getDocumentId(), currentUserId,</span>
<span class="nc" id="L192">                    String.format(&quot;Disposition approved for document '%s'. Notes: %s&quot;, </span>
<span class="nc" id="L193">                            document.getName(), input.getNotes()));</span>
        } else {
<span class="nc" id="L195">            document.setDispositionStatus(DispositionStatus.ACTIVE);</span>
<span class="nc" id="L196">            document.setDispositionNotes(input.getNotes());</span>
            
<span class="nc" id="L198">            auditService.logAudit(AuditAction.DISPOSITION_REJECTED, input.getDocumentId(), currentUserId,</span>
<span class="nc" id="L199">                    String.format(&quot;Disposition rejected for document '%s'. Notes: %s&quot;, </span>
<span class="nc" id="L200">                            document.getName(), input.getNotes()));</span>
        }
        
<span class="nc" id="L203">        return documentRepository.save(document);</span>
    }
    
    @MutationMapping
    public Document processRetentionForDocument(@Argument Long documentId) {
<span class="nc" id="L208">        String correlationId = CorrelationIdUtil.getCurrentCorrelationId();</span>
<span class="nc" id="L209">        String currentUserId = userContext.getCurrentUserId();</span>
        
<span class="nc" id="L211">        logger.info(&quot;GraphQL mutation: processRetentionForDocument(documentId: {}) [{}]&quot;, </span>
                documentId, correlationId);
        
<span class="nc" id="L214">        Document document = documentRepository.findById(documentId)</span>
<span class="nc" id="L215">                .orElseThrow(() -&gt; new ResourceNotFoundException(&quot;Document not found with ID: &quot; + documentId));</span>
        
        // If no retention policy, try to find and assign one
<span class="nc bnc" id="L218" title="All 2 branches missed.">        if (document.getRetentionPolicy() == null) {</span>
<span class="nc" id="L219">            Optional&lt;RetentionPolicy&gt; matchingPolicy = retentionPolicyService.findBestMatchingPolicy(document);</span>
<span class="nc bnc" id="L220" title="All 2 branches missed.">            if (matchingPolicy.isPresent()) {</span>
<span class="nc" id="L221">                document.setRetentionPolicy(matchingPolicy.get());</span>
                
<span class="nc" id="L223">                auditService.logAudit(AuditAction.RETENTION_POLICY_ASSIGNED, documentId, currentUserId,</span>
<span class="nc" id="L224">                        String.format(&quot;Auto-assigned retention policy '%s' to document '%s'&quot;, </span>
<span class="nc" id="L225">                                matchingPolicy.get().getName(), document.getName()));</span>
            }
        }
        
        // Calculate or recalculate retention expiry date
<span class="nc bnc" id="L230" title="All 2 branches missed.">        if (document.getRetentionPolicy() != null) {</span>
<span class="nc" id="L231">            LocalDateTime newExpiryDate = document.calculateRetentionExpiryDate();</span>
<span class="nc" id="L232">            document.setRetentionExpiryDate(newExpiryDate);</span>
            
<span class="nc" id="L234">            auditService.logAudit(AuditAction.RETENTION_EXPIRY_CALCULATED, documentId, currentUserId,</span>
<span class="nc" id="L235">                    String.format(&quot;Calculated retention expiry date: %s for document '%s'&quot;, </span>
<span class="nc" id="L236">                            newExpiryDate, document.getName()));</span>
        }
        
<span class="nc" id="L239">        return documentRepository.save(document);</span>
    }
    
    // Helper methods
    
    private Pageable createPageable(Map&lt;String, Object&gt; pagination) {
<span class="nc bnc" id="L245" title="All 2 branches missed.">        if (pagination == null) {</span>
<span class="nc" id="L246">            return PageRequest.of(0, 10, Sort.by(Sort.Direction.DESC, &quot;createdDate&quot;));</span>
        }
        
<span class="nc" id="L249">        int page = (Integer) pagination.getOrDefault(&quot;page&quot;, 0);</span>
<span class="nc" id="L250">        int size = (Integer) pagination.getOrDefault(&quot;size&quot;, 10);</span>
<span class="nc" id="L251">        String sortBy = (String) pagination.getOrDefault(&quot;sortBy&quot;, &quot;createdDate&quot;);</span>
<span class="nc" id="L252">        String sortDirection = (String) pagination.getOrDefault(&quot;sortDirection&quot;, &quot;DESC&quot;);</span>
        
<span class="nc bnc" id="L254" title="All 2 branches missed.">        Sort.Direction direction = &quot;ASC&quot;.equalsIgnoreCase(sortDirection) ? Sort.Direction.ASC : Sort.Direction.DESC;</span>
        
<span class="nc" id="L256">        return PageRequest.of(page, size, Sort.by(direction, sortBy));</span>
    }
}
</pre><div class="footer"><span class="right">Created with <a href="http://www.jacoco.org/jacoco">JaCoCo</a> 0.8.10.202304240956</span></div></body></html>