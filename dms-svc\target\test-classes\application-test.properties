# Test Configuration for DMS Service
# This configuration is used for unit tests

# Completely disable OpenTelemetry for tests
spring.autoconfigure.exclude=io.opentelemetry.instrumentation.spring.autoconfigure.OpenTelemetryAutoConfiguration,org.springframework.boot.autoconfigure.data.redis.RedisAutoConfiguration,org.springframework.boot.autoconfigure.data.elasticsearch.ElasticsearchDataAutoConfiguration,name.nkonev.multipart.spring.graphql.MultipartGraphQlAutoConfiguration
otel.instrumentation.spring-boot.enabled=false
otel.sdk.disabled=true
otel.traces.exporter=none
otel.metrics.exporter=none

# Disable tracing completely for tests
management.tracing.enabled=false
management.zipkin.tracing.enabled=false

# Database Configuration - H2 In-Memory Database
spring.datasource.url=jdbc:h2:mem:testdb;DB_CLOSE_DELAY=-1;DB_CLOSE_ON_EXIT=FALSE;MODE=MySQL;DATABASE_TO_LOWER=TRUE;CASE_INSENSITIVE_IDENTIFIERS=TRUE;LOCK_TIMEOUT=10000
spring.datasource.driverClassName=org.h2.Driver
spring.datasource.username=sa
spring.datasource.password=
spring.jpa.database-platform=org.hibernate.dialect.H2Dialect
spring.jpa.hibernate.ddl-auto=create-drop
spring.jpa.show-sql=false
spring.h2.console.enabled=true

# Connection Pool Configuration for better concurrency handling
spring.datasource.hikari.maximum-pool-size=10
spring.datasource.hikari.minimum-idle=2
spring.datasource.hikari.connection-timeout=30000
spring.datasource.hikari.idle-timeout=600000
spring.datasource.hikari.max-lifetime=1800000

# Disable Liquibase for performance tests to avoid configuration issues
spring.liquibase.enabled=false
spring.sql.init.mode=never
spring.sql.init.continue-on-error=false

# Elasticsearch Configuration (disabled for tests)
elasticsearch.enabled=false
spring.data.elasticsearch.repositories.enabled=false

# Redis Configuration (disabled for tests)
spring.data.redis.repositories.enabled=false

# JWT Configuration for Testing
jwt.secret=test-secret-key-for-unit-tests-only-not-for-production-use
jwt.expiration=3600000
dms.jwt.secret=test-secret-key-for-unit-tests-only-not-for-production-use
dms.jwt.expiration=3600000
dms.jwt.header=Authorization
dms.jwt.prefix=Bearer

# GraphQL Configuration
spring.graphql.graphiql.enabled=true
spring.graphql.graphiql.path=/graphiql

# Disable multipart GraphQL Upload scalar auto-registration to avoid conflicts
spring.graphql.multipart.springboot.enabled=false

# Server Configuration
server.port=0
server.servlet.context-path=/

# Logging Configuration
logging.level.com.ascentbusiness.dms_svc=DEBUG
logging.level.org.springframework.security=WARN
logging.level.org.springframework.web=WARN
logging.level.org.hibernate.SQL=WARN
logging.pattern.console=%d{HH:mm:ss.SSS} [%thread] %-5level [%X{correlationId}] %logger{36} - %msg%n

# Correlation ID Configuration
dms.correlation.header-name=X-Correlation-ID

# Cache Configuration - Simple for tests
spring.cache.type=simple

# Test Case Configuration
testcase.directory=src/test/resources/test-cases

# Storage Configuration for Tests
dms.storage.test.create-sample-configurations=false
dms.storage.test.run-tests-on-startup=false
# Disable scheduled health checks for tests
spring.task.scheduling.enabled=false
# Disable async execution for tests to avoid timing issues
spring.task.execution.pool.core-size=0
spring.task.execution.pool.max-size=1

# Default Storage Provider for Tests
dms.storage.default.provider=LOCAL
dms.storage.local.base-path=target/test-storage
dms.storage.local.create-directories=true

# Security Configuration for Tests
spring.security.user.name=testuser
spring.security.user.password=testpass
spring.security.user.roles=USER

# JWT Configuration for Testing
dms.jwt.secret=test-secret-key-for-unit-tests-only-not-for-production-use
dms.jwt.expiration=3600000
dms.jwt.header=Authorization
dms.jwt.prefix=Bearer

# Actuator Configuration
management.endpoints.web.exposure.include=health,info,metrics
management.endpoint.health.show-details=always
# Disable problematic health indicators for tests
management.health.elasticsearch.enabled=false
management.health.db.enabled=false
management.health.diskspace.enabled=false
management.health.redis.enabled=false
management.health.ping.enabled=false
management.health.livenessstate.enabled=false
management.health.readinessstate.enabled=false

# Disable rate limiting for tests
dms.rate-limit.enabled=false
dms.rate-limit.use-redis=false

# Security Headers Configuration for Tests
dms.security.headers.csp.enabled=false
dms.security.headers.hsts.enabled=false
dms.security.headers.frame-options=DENY
dms.security.headers.content-type-options=nosniff
dms.security.headers.referrer-policy=strict-origin-when-cross-origin
dms.security.headers.permissions-policy=geolocation=(), microphone=(), camera=()

# Test-specific properties
test.data.cleanup.enabled=true
test.mock.external.services=true
test.performance.enabled=false

# Compliance test settings
compliance.test.enabled=true
compliance.test.auto-create-data=true
compliance.test.validation.strict=false

# Test user context
test.user.id=test-user
test.user.name=Test User
test.user.role=TEST_ROLE

# Mock services configuration
mock.audit.enabled=true
mock.notification.enabled=true
mock.external-api.enabled=true

# Audit Configuration for Tests
# Enable audit logging for integration tests
dms.audit.enabled=true
dms.audit.async=false
dms.audit.include-request-body=false
dms.audit.include-response-body=false
dms.audit.encryption.enabled=false

# Integration test settings
integration.test.enabled=true
integration.test.timeout=30000
integration.test.retry.enabled=false

# File Processing Configuration for Tests
# Use smaller thresholds to avoid MySQL packet size issues in tests
# Direct processing: 500KB (well under MySQL 1MB packet limit)
dms.file-processing.direct-processing-threshold=512000
# Async processing: 1MB
dms.file-processing.async-processing-threshold=1048576
# Max file size: 100MB (increased for large file upload tests)
dms.file-processing.max-file-size=104857600

# Chunk upload configuration for tests
dms.file-processing.default-chunk-size=262144
dms.file-processing.max-chunk-size=1048576
dms.file-processing.min-chunk-size=65536

# Async processing configuration for tests
dms.file-processing.max-concurrent-async-jobs=2
dms.file-processing.async-job-timeout-seconds=300

# Session and cleanup configuration for tests
dms.file-processing.chunk-session-timeout-seconds=3600
dms.file-processing.cleanup-interval-seconds=300
dms.file-processing.temp-directory=target/test-temp/processing
dms.file-processing.enable-auto-cleanup=true
dms.file-processing.enable-progress-tracking=true
