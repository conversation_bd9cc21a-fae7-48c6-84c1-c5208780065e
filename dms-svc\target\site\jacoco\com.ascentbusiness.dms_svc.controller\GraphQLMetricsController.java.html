<?xml version="1.0" encoding="UTF-8"?><!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Strict//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-strict.dtd"><html xmlns="http://www.w3.org/1999/xhtml" lang="en"><head><meta http-equiv="Content-Type" content="text/html;charset=UTF-8"/><link rel="stylesheet" href="../jacoco-resources/report.css" type="text/css"/><link rel="shortcut icon" href="../jacoco-resources/report.gif" type="image/gif"/><title>GraphQLMetricsController.java</title><link rel="stylesheet" href="../jacoco-resources/prettify.css" type="text/css"/><script type="text/javascript" src="../jacoco-resources/prettify.js"></script></head><body onload="window['PR_TAB_WIDTH']=4;prettyPrint()"><div class="breadcrumb" id="breadcrumb"><span class="info"><a href="../jacoco-sessions.html" class="el_session">Sessions</a></span><a href="../index.html" class="el_report">dms-svc</a> &gt; <a href="index.source.html" class="el_package">com.ascentbusiness.dms_svc.controller</a> &gt; <span class="el_source">GraphQLMetricsController.java</span></div><h1>GraphQLMetricsController.java</h1><pre class="source lang-java linenums">/*
 * Copyright (c) 2024 Ascent Business Technology
 * All rights reserved.
 */

package com.ascentbusiness.dms_svc.controller;

import com.ascentbusiness.dms_svc.config.GraphQLMonitoringConfig;
import io.micrometer.core.instrument.MeterRegistry;
import io.micrometer.core.instrument.Meter;
import io.micrometer.core.instrument.Counter;
import io.micrometer.core.instrument.Timer;
import io.micrometer.core.instrument.Gauge;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.actuate.endpoint.annotation.Endpoint;
import org.springframework.boot.actuate.endpoint.annotation.ReadOperation;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.time.Instant;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * REST controller for exposing GraphQL metrics and monitoring data.
 * Provides endpoints for monitoring GraphQL performance, error rates, and usage statistics.
 */
@RestController
@RequestMapping(&quot;/api/graphql/metrics&quot;)
<span class="fc" id="L37">@RequiredArgsConstructor</span>
<span class="fc" id="L38">@Slf4j</span>
public class GraphQLMetricsController {

    private final GraphQLMonitoringConfig monitoringConfig;
    private final MeterRegistry meterRegistry;

    /**
     * Get comprehensive GraphQL metrics summary
     */
    @GetMapping(&quot;/summary&quot;)
    @PreAuthorize(&quot;hasAnyRole('ADMIN', 'MANAGER')&quot;)
    public ResponseEntity&lt;GraphQLMetricsResponse&gt; getGraphQLMetrics() {
<span class="nc" id="L50">        log.info(&quot;Retrieving GraphQL metrics summary&quot;);</span>
        
        try {
<span class="nc" id="L53">            GraphQLMonitoringConfig.GraphQLMetricsSummary summary = monitoringConfig.getMetricsSummary();</span>
            
<span class="nc" id="L55">            GraphQLMetricsResponse response = GraphQLMetricsResponse.builder()</span>
<span class="nc" id="L56">                    .timestamp(Instant.now())</span>
<span class="nc" id="L57">                    .activeQueries(summary.getActiveQueries())</span>
<span class="nc" id="L58">                    .totalQueries(summary.getTotalQueries())</span>
<span class="nc" id="L59">                    .totalErrors(summary.getTotalErrors())</span>
<span class="nc" id="L60">                    .errorRate(summary.getErrorRate())</span>
<span class="nc" id="L61">                    .executionMetrics(buildExecutionMetrics())</span>
<span class="nc" id="L62">                    .fieldMetrics(buildFieldMetrics())</span>
<span class="nc" id="L63">                    .errorMetrics(buildErrorMetrics())</span>
<span class="nc" id="L64">                    .build();</span>
            
<span class="nc" id="L66">            return ResponseEntity.ok(response);</span>
            
<span class="nc" id="L68">        } catch (Exception e) {</span>
<span class="nc" id="L69">            log.error(&quot;Error retrieving GraphQL metrics&quot;, e);</span>
<span class="nc" id="L70">            return ResponseEntity.internalServerError().build();</span>
        }
    }

    /**
     * Get GraphQL execution time metrics
     */
    @GetMapping(&quot;/execution&quot;)
    @PreAuthorize(&quot;hasAnyRole('ADMIN', 'MANAGER')&quot;)
    public ResponseEntity&lt;Map&lt;String, Object&gt;&gt; getExecutionMetrics() {
<span class="nc" id="L80">        log.info(&quot;Retrieving GraphQL execution metrics&quot;);</span>
        
<span class="nc" id="L82">        Map&lt;String, Object&gt; metrics = new HashMap&lt;&gt;();</span>
        
        // Get execution time metrics
<span class="nc" id="L85">        meterRegistry.getMeters().stream()</span>
<span class="nc" id="L86">                .filter(meter -&gt; meter.getId().getName().equals(&quot;graphql.execution.time&quot;))</span>
<span class="nc" id="L87">                .filter(meter -&gt; meter instanceof Timer)</span>
<span class="nc" id="L88">                .forEach(meter -&gt; {</span>
<span class="nc" id="L89">                    Timer timer = (Timer) meter;</span>
<span class="nc" id="L90">                    String operation = meter.getId().getTag(&quot;operation&quot;);</span>
<span class="nc" id="L91">                    String status = meter.getId().getTag(&quot;status&quot;);</span>
                    
<span class="nc" id="L93">                    Map&lt;String, Object&gt; timerMetrics = new HashMap&lt;&gt;();</span>
<span class="nc" id="L94">                    timerMetrics.put(&quot;count&quot;, timer.count());</span>
<span class="nc" id="L95">                    timerMetrics.put(&quot;totalTime&quot;, timer.totalTime(java.util.concurrent.TimeUnit.MILLISECONDS));</span>
<span class="nc" id="L96">                    timerMetrics.put(&quot;meanTime&quot;, timer.mean(java.util.concurrent.TimeUnit.MILLISECONDS));</span>
<span class="nc" id="L97">                    timerMetrics.put(&quot;maxTime&quot;, timer.max(java.util.concurrent.TimeUnit.MILLISECONDS));</span>
                    
<span class="nc" id="L99">                    metrics.put(String.format(&quot;%s_%s&quot;, operation, status), timerMetrics);</span>
<span class="nc" id="L100">                });</span>
        
<span class="nc" id="L102">        return ResponseEntity.ok(metrics);</span>
    }

    /**
     * Get GraphQL field-level metrics
     */
    @GetMapping(&quot;/fields&quot;)
    @PreAuthorize(&quot;hasAnyRole('ADMIN', 'MANAGER')&quot;)
    public ResponseEntity&lt;Map&lt;String, Object&gt;&gt; getFieldMetrics() {
<span class="nc" id="L111">        log.info(&quot;Retrieving GraphQL field metrics&quot;);</span>
        
<span class="nc" id="L113">        Map&lt;String, Object&gt; metrics = new HashMap&lt;&gt;();</span>
        
        // Get field fetch metrics
<span class="nc" id="L116">        meterRegistry.getMeters().stream()</span>
<span class="nc" id="L117">                .filter(meter -&gt; meter.getId().getName().equals(&quot;graphql.field.fetch&quot;))</span>
<span class="nc" id="L118">                .filter(meter -&gt; meter instanceof Counter)</span>
<span class="nc" id="L119">                .forEach(meter -&gt; {</span>
<span class="nc" id="L120">                    Counter counter = (Counter) meter;</span>
<span class="nc" id="L121">                    String field = meter.getId().getTag(&quot;field&quot;);</span>
<span class="nc" id="L122">                    String type = meter.getId().getTag(&quot;type&quot;);</span>
<span class="nc" id="L123">                    String status = meter.getId().getTag(&quot;status&quot;);</span>
                    
<span class="nc" id="L125">                    String key = String.format(&quot;%s.%s_%s&quot;, type, field, status);</span>
<span class="nc" id="L126">                    metrics.put(key, counter.count());</span>
<span class="nc" id="L127">                });</span>
        
<span class="nc" id="L129">        return ResponseEntity.ok(metrics);</span>
    }

    /**
     * Get GraphQL error metrics
     */
    @GetMapping(&quot;/errors&quot;)
    @PreAuthorize(&quot;hasAnyRole('ADMIN', 'MANAGER')&quot;)
    public ResponseEntity&lt;Map&lt;String, Object&gt;&gt; getErrorMetrics() {
<span class="nc" id="L138">        log.info(&quot;Retrieving GraphQL error metrics&quot;);</span>
        
<span class="nc" id="L140">        Map&lt;String, Object&gt; metrics = new HashMap&lt;&gt;();</span>
        
        // Get error metrics
<span class="nc" id="L143">        meterRegistry.getMeters().stream()</span>
<span class="nc" id="L144">                .filter(meter -&gt; meter.getId().getName().startsWith(&quot;graphql.errors&quot;))</span>
<span class="nc" id="L145">                .filter(meter -&gt; meter instanceof Counter)</span>
<span class="nc" id="L146">                .forEach(meter -&gt; {</span>
<span class="nc" id="L147">                    Counter counter = (Counter) meter;</span>
<span class="nc" id="L148">                    String operation = meter.getId().getTag(&quot;operation&quot;);</span>
<span class="nc" id="L149">                    String type = meter.getId().getTag(&quot;type&quot;);</span>
                    
<span class="nc bnc" id="L151" title="All 2 branches missed.">                    String key = String.format(&quot;%s_%s&quot;, operation != null ? operation : &quot;unknown&quot;, </span>
<span class="nc bnc" id="L152" title="All 2 branches missed.">                                             type != null ? type : &quot;unknown&quot;);</span>
<span class="nc" id="L153">                    metrics.put(key, counter.count());</span>
<span class="nc" id="L154">                });</span>
        
<span class="nc" id="L156">        return ResponseEntity.ok(metrics);</span>
    }

    /**
     * Get GraphQL health status
     */
    @GetMapping(&quot;/health&quot;)
    @PreAuthorize(&quot;hasAnyRole('ADMIN', 'MANAGER', 'USER')&quot;)
    public ResponseEntity&lt;GraphQLHealthResponse&gt; getGraphQLHealth() {
<span class="nc" id="L165">        log.info(&quot;Retrieving GraphQL health status&quot;);</span>
        
        try {
<span class="nc" id="L168">            GraphQLMonitoringConfig.GraphQLMetricsSummary summary = monitoringConfig.getMetricsSummary();</span>
            
            // Determine health status based on error rate and active queries
<span class="nc" id="L171">            String status = &quot;UP&quot;;</span>
<span class="nc" id="L172">            String message = &quot;GraphQL endpoint is healthy&quot;;</span>
            
<span class="nc bnc" id="L174" title="All 2 branches missed.">            if (summary.getErrorRate() &gt; 10.0) {</span>
<span class="nc" id="L175">                status = &quot;DEGRADED&quot;;</span>
<span class="nc" id="L176">                message = String.format(&quot;High error rate: %.2f%%&quot;, summary.getErrorRate());</span>
<span class="nc bnc" id="L177" title="All 2 branches missed.">            } else if (summary.getActiveQueries() &gt; 100) {</span>
<span class="nc" id="L178">                status = &quot;DEGRADED&quot;;</span>
<span class="nc" id="L179">                message = String.format(&quot;High query load: %d active queries&quot;, summary.getActiveQueries());</span>
            }
            
<span class="nc" id="L182">            GraphQLHealthResponse response = GraphQLHealthResponse.builder()</span>
<span class="nc" id="L183">                    .status(status)</span>
<span class="nc" id="L184">                    .message(message)</span>
<span class="nc" id="L185">                    .timestamp(Instant.now())</span>
<span class="nc" id="L186">                    .activeQueries(summary.getActiveQueries())</span>
<span class="nc" id="L187">                    .totalQueries(summary.getTotalQueries())</span>
<span class="nc" id="L188">                    .errorRate(summary.getErrorRate())</span>
<span class="nc" id="L189">                    .build();</span>
            
<span class="nc" id="L191">            return ResponseEntity.ok(response);</span>
            
<span class="nc" id="L193">        } catch (Exception e) {</span>
<span class="nc" id="L194">            log.error(&quot;Error retrieving GraphQL health status&quot;, e);</span>
            
<span class="nc" id="L196">            GraphQLHealthResponse response = GraphQLHealthResponse.builder()</span>
<span class="nc" id="L197">                    .status(&quot;DOWN&quot;)</span>
<span class="nc" id="L198">                    .message(&quot;Error retrieving health status: &quot; + e.getMessage())</span>
<span class="nc" id="L199">                    .timestamp(Instant.now())</span>
<span class="nc" id="L200">                    .activeQueries(0L)</span>
<span class="nc" id="L201">                    .totalQueries(0L)</span>
<span class="nc" id="L202">                    .errorRate(0.0)</span>
<span class="nc" id="L203">                    .build();</span>
            
<span class="nc" id="L205">            return ResponseEntity.ok(response);</span>
        }
    }

    // Helper methods for building metrics responses

    private Map&lt;String, Object&gt; buildExecutionMetrics() {
<span class="nc" id="L212">        Map&lt;String, Object&gt; metrics = new HashMap&lt;&gt;();</span>

<span class="nc" id="L214">        meterRegistry.getMeters().stream()</span>
<span class="nc" id="L215">                .filter(meter -&gt; meter.getId().getName().equals(&quot;graphql.execution.time&quot;))</span>
<span class="nc" id="L216">                .filter(meter -&gt; meter instanceof Timer)</span>
<span class="nc" id="L217">                .forEach(meter -&gt; {</span>
<span class="nc" id="L218">                    Timer timer = (Timer) meter;</span>
<span class="nc" id="L219">                    String operation = meter.getId().getTag(&quot;operation&quot;);</span>

<span class="nc" id="L221">                    Map&lt;String, Object&gt; timerMetrics = new HashMap&lt;&gt;();</span>
<span class="nc" id="L222">                    timerMetrics.put(&quot;count&quot;, timer.count());</span>
<span class="nc" id="L223">                    timerMetrics.put(&quot;meanTime&quot;, timer.mean(java.util.concurrent.TimeUnit.MILLISECONDS));</span>
<span class="nc" id="L224">                    timerMetrics.put(&quot;maxTime&quot;, timer.max(java.util.concurrent.TimeUnit.MILLISECONDS));</span>

<span class="nc bnc" id="L226" title="All 2 branches missed.">                    metrics.put(operation != null ? operation : &quot;unknown&quot;, timerMetrics);</span>
<span class="nc" id="L227">                });</span>

<span class="nc" id="L229">        return metrics;</span>
    }

    private Map&lt;String, Object&gt; buildFieldMetrics() {
<span class="nc" id="L233">        Map&lt;String, Object&gt; metrics = new HashMap&lt;&gt;();</span>

<span class="nc" id="L235">        meterRegistry.getMeters().stream()</span>
<span class="nc" id="L236">                .filter(meter -&gt; meter.getId().getName().equals(&quot;graphql.field.fetch&quot;))</span>
<span class="nc" id="L237">                .filter(meter -&gt; meter instanceof Counter)</span>
<span class="nc" id="L238">                .forEach(meter -&gt; {</span>
<span class="nc" id="L239">                    Counter counter = (Counter) meter;</span>
<span class="nc" id="L240">                    String field = meter.getId().getTag(&quot;field&quot;);</span>
<span class="nc" id="L241">                    String type = meter.getId().getTag(&quot;type&quot;);</span>

<span class="nc bnc" id="L243" title="All 2 branches missed.">                    String key = String.format(&quot;%s.%s&quot;, type != null ? type : &quot;unknown&quot;,</span>
<span class="nc bnc" id="L244" title="All 2 branches missed.">                                             field != null ? field : &quot;unknown&quot;);</span>
<span class="nc" id="L245">                    metrics.put(key, counter.count());</span>
<span class="nc" id="L246">                });</span>

<span class="nc" id="L248">        return metrics;</span>
    }

    private Map&lt;String, Object&gt; buildErrorMetrics() {
<span class="nc" id="L252">        Map&lt;String, Object&gt; metrics = new HashMap&lt;&gt;();</span>

<span class="nc" id="L254">        meterRegistry.getMeters().stream()</span>
<span class="nc" id="L255">                .filter(meter -&gt; meter.getId().getName().startsWith(&quot;graphql.errors&quot;))</span>
<span class="nc" id="L256">                .filter(meter -&gt; meter instanceof Counter)</span>
<span class="nc" id="L257">                .forEach(meter -&gt; {</span>
<span class="nc" id="L258">                    Counter counter = (Counter) meter;</span>
<span class="nc" id="L259">                    String operation = meter.getId().getTag(&quot;operation&quot;);</span>

<span class="nc bnc" id="L261" title="All 2 branches missed.">                    metrics.put(operation != null ? operation : &quot;unknown&quot;, counter.count());</span>
<span class="nc" id="L262">                });</span>

<span class="nc" id="L264">        return metrics;</span>
    }

    // Response DTOs
    
    public static class GraphQLMetricsResponse {
        private final Instant timestamp;
        private final long activeQueries;
        private final long totalQueries;
        private final long totalErrors;
        private final double errorRate;
        private final Map&lt;String, Object&gt; executionMetrics;
        private final Map&lt;String, Object&gt; fieldMetrics;
        private final Map&lt;String, Object&gt; errorMetrics;

        private GraphQLMetricsResponse(Instant timestamp, long activeQueries, long totalQueries,
                                      long totalErrors, double errorRate, Map&lt;String, Object&gt; executionMetrics,
<span class="nc" id="L281">                                      Map&lt;String, Object&gt; fieldMetrics, Map&lt;String, Object&gt; errorMetrics) {</span>
<span class="nc" id="L282">            this.timestamp = timestamp;</span>
<span class="nc" id="L283">            this.activeQueries = activeQueries;</span>
<span class="nc" id="L284">            this.totalQueries = totalQueries;</span>
<span class="nc" id="L285">            this.totalErrors = totalErrors;</span>
<span class="nc" id="L286">            this.errorRate = errorRate;</span>
<span class="nc" id="L287">            this.executionMetrics = executionMetrics;</span>
<span class="nc" id="L288">            this.fieldMetrics = fieldMetrics;</span>
<span class="nc" id="L289">            this.errorMetrics = errorMetrics;</span>
<span class="nc" id="L290">        }</span>

        public static Builder builder() {
<span class="nc" id="L293">            return new Builder();</span>
        }

        // Getters
<span class="nc" id="L297">        public Instant getTimestamp() { return timestamp; }</span>
<span class="nc" id="L298">        public long getActiveQueries() { return activeQueries; }</span>
<span class="nc" id="L299">        public long getTotalQueries() { return totalQueries; }</span>
<span class="nc" id="L300">        public long getTotalErrors() { return totalErrors; }</span>
<span class="nc" id="L301">        public double getErrorRate() { return errorRate; }</span>
<span class="nc" id="L302">        public Map&lt;String, Object&gt; getExecutionMetrics() { return executionMetrics; }</span>
<span class="nc" id="L303">        public Map&lt;String, Object&gt; getFieldMetrics() { return fieldMetrics; }</span>
<span class="nc" id="L304">        public Map&lt;String, Object&gt; getErrorMetrics() { return errorMetrics; }</span>

<span class="nc" id="L306">        public static class Builder {</span>
            private Instant timestamp;
            private long activeQueries;
            private long totalQueries;
            private long totalErrors;
            private double errorRate;
            private Map&lt;String, Object&gt; executionMetrics;
            private Map&lt;String, Object&gt; fieldMetrics;
            private Map&lt;String, Object&gt; errorMetrics;

<span class="nc" id="L316">            public Builder timestamp(Instant timestamp) { this.timestamp = timestamp; return this; }</span>
<span class="nc" id="L317">            public Builder activeQueries(long activeQueries) { this.activeQueries = activeQueries; return this; }</span>
<span class="nc" id="L318">            public Builder totalQueries(long totalQueries) { this.totalQueries = totalQueries; return this; }</span>
<span class="nc" id="L319">            public Builder totalErrors(long totalErrors) { this.totalErrors = totalErrors; return this; }</span>
<span class="nc" id="L320">            public Builder errorRate(double errorRate) { this.errorRate = errorRate; return this; }</span>
<span class="nc" id="L321">            public Builder executionMetrics(Map&lt;String, Object&gt; executionMetrics) { this.executionMetrics = executionMetrics; return this; }</span>
<span class="nc" id="L322">            public Builder fieldMetrics(Map&lt;String, Object&gt; fieldMetrics) { this.fieldMetrics = fieldMetrics; return this; }</span>
<span class="nc" id="L323">            public Builder errorMetrics(Map&lt;String, Object&gt; errorMetrics) { this.errorMetrics = errorMetrics; return this; }</span>

            public GraphQLMetricsResponse build() {
<span class="nc" id="L326">                return new GraphQLMetricsResponse(timestamp, activeQueries, totalQueries, totalErrors,</span>
                        errorRate, executionMetrics, fieldMetrics, errorMetrics);
            }
        }
    }

    public static class GraphQLHealthResponse {
        private final String status;
        private final String message;
        private final Instant timestamp;
        private final long activeQueries;
        private final long totalQueries;
        private final double errorRate;

        private GraphQLHealthResponse(String status, String message, Instant timestamp,
<span class="nc" id="L341">                                     long activeQueries, long totalQueries, double errorRate) {</span>
<span class="nc" id="L342">            this.status = status;</span>
<span class="nc" id="L343">            this.message = message;</span>
<span class="nc" id="L344">            this.timestamp = timestamp;</span>
<span class="nc" id="L345">            this.activeQueries = activeQueries;</span>
<span class="nc" id="L346">            this.totalQueries = totalQueries;</span>
<span class="nc" id="L347">            this.errorRate = errorRate;</span>
<span class="nc" id="L348">        }</span>

        public static Builder builder() {
<span class="nc" id="L351">            return new Builder();</span>
        }

        // Getters
<span class="nc" id="L355">        public String getStatus() { return status; }</span>
<span class="nc" id="L356">        public String getMessage() { return message; }</span>
<span class="nc" id="L357">        public Instant getTimestamp() { return timestamp; }</span>
<span class="nc" id="L358">        public long getActiveQueries() { return activeQueries; }</span>
<span class="nc" id="L359">        public long getTotalQueries() { return totalQueries; }</span>
<span class="nc" id="L360">        public double getErrorRate() { return errorRate; }</span>

<span class="nc" id="L362">        public static class Builder {</span>
            private String status;
            private String message;
            private Instant timestamp;
            private long activeQueries;
            private long totalQueries;
            private double errorRate;

<span class="nc" id="L370">            public Builder status(String status) { this.status = status; return this; }</span>
<span class="nc" id="L371">            public Builder message(String message) { this.message = message; return this; }</span>
<span class="nc" id="L372">            public Builder timestamp(Instant timestamp) { this.timestamp = timestamp; return this; }</span>
<span class="nc" id="L373">            public Builder activeQueries(long activeQueries) { this.activeQueries = activeQueries; return this; }</span>
<span class="nc" id="L374">            public Builder totalQueries(long totalQueries) { this.totalQueries = totalQueries; return this; }</span>
<span class="nc" id="L375">            public Builder errorRate(double errorRate) { this.errorRate = errorRate; return this; }</span>

            public GraphQLHealthResponse build() {
<span class="nc" id="L378">                return new GraphQLHealthResponse(status, message, timestamp, activeQueries, totalQueries, errorRate);</span>
            }
        }
    }

    /**
     * Reset GraphQL metrics (useful for testing)
     */
    @PostMapping(&quot;/reset&quot;)
    @PreAuthorize(&quot;hasRole('ADMIN')&quot;)
    public ResponseEntity&lt;Map&lt;String, String&gt;&gt; resetMetrics() {
<span class="nc" id="L389">        log.info(&quot;Resetting GraphQL metrics&quot;);</span>

        try {
<span class="nc" id="L392">            monitoringConfig.resetMetrics();</span>

<span class="nc" id="L394">            Map&lt;String, String&gt; response = new HashMap&lt;&gt;();</span>
<span class="nc" id="L395">            response.put(&quot;status&quot;, &quot;success&quot;);</span>
<span class="nc" id="L396">            response.put(&quot;message&quot;, &quot;GraphQL metrics reset successfully&quot;);</span>
<span class="nc" id="L397">            response.put(&quot;timestamp&quot;, Instant.now().toString());</span>

<span class="nc" id="L399">            return ResponseEntity.ok(response);</span>

<span class="nc" id="L401">        } catch (Exception e) {</span>
<span class="nc" id="L402">            log.error(&quot;Error resetting GraphQL metrics&quot;, e);</span>

<span class="nc" id="L404">            Map&lt;String, String&gt; response = new HashMap&lt;&gt;();</span>
<span class="nc" id="L405">            response.put(&quot;status&quot;, &quot;error&quot;);</span>
<span class="nc" id="L406">            response.put(&quot;message&quot;, &quot;Error resetting metrics: &quot; + e.getMessage());</span>
<span class="nc" id="L407">            response.put(&quot;timestamp&quot;, Instant.now().toString());</span>

<span class="nc" id="L409">            return ResponseEntity.status(500).body(response);</span>
        }
    }
}
</pre><div class="footer"><span class="right">Created with <a href="http://www.jacoco.org/jacoco">JaCoCo</a> 0.8.10.202304240956</span></div></body></html>