# RabbitMQ Setup Guide for Notification Service Testing

## Current Configuration

### Default RabbitMQ Settings in Notification Service:
```properties
# From notification-svc/src/main/resources/application.properties
spring.rabbitmq.host=${RABBITMQ_HOST:localhost}
spring.rabbitmq.port=${RABBITMQ_PORT:5672}
spring.rabbitmq.username=${RABBITMQ_USERNAME:guest}
spring.rabbitmq.password=${RABBITMQ_PASSWORD:guest}
spring.rabbitmq.virtual-host=${RABBITMQ_VIRTUAL_HOST:/}
```

## RabbitMQ Installation Options

### Option 1: Docker (Recommended - Easiest)

#### 1. Install Docker Desktop
Download and install Docker Desktop from: https://www.docker.com/products/docker-desktop

#### 2. Run RabbitMQ Container
```bash
# Run RabbitMQ with Management UI
docker run -d --name rabbitmq-dev \
  -p 5672:5672 \
  -p 15672:15672 \
  -e RABBITMQ_DEFAULT_USER=guest \
  -e RABBITMQ_DEFAULT_PASS=guest \
  rabbitmq:3-management

# Or using PowerShell:
docker run -d --name rabbitmq-dev -p 5672:5672 -p 15672:15672 -e RABBITMQ_DEFAULT_USER=guest -e RABBITMQ_DEFAULT_PASS=guest rabbitmq:3-management
```

#### 3. Verify RabbitMQ is Running
```bash
# Check container status
docker ps

# Check logs
docker logs rabbitmq-dev
```

#### 4. Access RabbitMQ Management UI
- **URL**: http://localhost:15672
- **Username**: `guest`
- **Password**: `guest`

### Option 2: Windows Installation

#### 1. Install Erlang
- Download from: https://www.erlang.org/downloads
- Install the Windows installer

#### 2. Install RabbitMQ
- Download from: https://www.rabbitmq.com/download.html
- Install the Windows installer
- RabbitMQ will start as a Windows service

#### 3. Enable Management Plugin
```cmd
# Open Command Prompt as Administrator
cd "C:\Program Files\RabbitMQ Server\rabbitmq_server-3.x.x\sbin"
rabbitmq-plugins enable rabbitmq_management
```

#### 4. Access Management UI
- **URL**: http://localhost:15672
- **Username**: `guest`
- **Password**: `guest`

## Testing RabbitMQ Connection

### 1. Start RabbitMQ
```bash
# If using Docker:
docker start rabbitmq-dev

# If using Windows service:
net start RabbitMQ
```

### 2. Verify Connection
```bash
# Test connection using PowerShell
Test-NetConnection -ComputerName localhost -Port 5672
```

### 3. Start Notification Service
```bash
cd notification-svc
$env:JWT_SECRET="test_jwt_secret_at_least_32_characters_long"
./mvnw spring-boot:run -Dspring-boot.run.profiles=dev
```

### 4. Check Service Logs
Look for successful RabbitMQ connection logs:
```
INFO o.s.a.r.c.CachingConnectionFactory - Created new connection: rabbitConnectionFactory#xxxxxxx
INFO o.s.a.r.l.SimpleMessageListenerContainer - Successfully connected to RabbitMQ
```

## Custom RabbitMQ Configuration (Optional)

### If you want to use different credentials:

#### 1. Create Custom User in RabbitMQ
```bash
# Using Docker exec
docker exec rabbitmq-dev rabbitmqctl add_user notification_user notification_pass
docker exec rabbitmq-dev rabbitmqctl set_user_tags notification_user administrator
docker exec rabbitmq-dev rabbitmqctl set_permissions -p / notification_user ".*" ".*" ".*"
```

#### 2. Update Environment Variables
```bash
$env:RABBITMQ_USERNAME="notification_user"
$env:RABBITMQ_PASSWORD="notification_pass"
./mvnw spring-boot:run -Dspring-boot.run.profiles=dev
```

## Notification Service Queue Configuration

The notification service will automatically create these queues:
- **notification.email.queue** - For email notifications
- **notification.sms.queue** - For SMS notifications (if configured)
- **notification.push.queue** - For push notifications (if configured)

## Testing Notification Flow

### 1. Send Test Notification via GraphiQL
```graphql
mutation {
  sendNotification(input: {
    sender: "<EMAIL>"
    type: EMAIL
    content: "Test notification message"
    templateName: "welcome"
    recipientDetails: [
      {
        email: "<EMAIL>"
        type: TO
        variables: [
          { key: "firstName", value: "Test User" },
          { key: "companyName", value: "Test Company" }
        ]
      }
    ]
  })
}
```

### 2. Monitor RabbitMQ Management UI
- Go to http://localhost:15672
- Check **Queues** tab to see message flow
- Check **Connections** tab to see service connection

## Troubleshooting

### Common Issues:

#### 1. Connection Refused
```
Error: java.net.ConnectException: Connection refused
```
**Solution**: Ensure RabbitMQ is running on port 5672

#### 2. Authentication Failed
```
Error: ACCESS_REFUSED - Login was refused using authentication mechanism PLAIN
```
**Solution**: Check username/password (default is guest/guest)

#### 3. Virtual Host Not Found
```
Error: NOT_FOUND - no exchange 'amq.default' in vhost '/'
```
**Solution**: Ensure virtual host '/' exists (default)

### Quick Verification Commands:

```bash
# Check if RabbitMQ port is open
netstat -an | findstr :5672

# Check if management port is open
netstat -an | findstr :15672

# Test RabbitMQ connection
telnet localhost 5672
```

## Docker Compose Alternative

Create `docker-compose.yml` for easy management:

```yaml
version: '3.8'
services:
  rabbitmq:
    image: rabbitmq:3-management
    container_name: rabbitmq-dev
    ports:
      - "5672:5672"
      - "15672:15672"
    environment:
      RABBITMQ_DEFAULT_USER: guest
      RABBITMQ_DEFAULT_PASS: guest
    volumes:
      - rabbitmq_data:/var/lib/rabbitmq

volumes:
  rabbitmq_data:
```

Run with:
```bash
docker-compose up -d
```

This setup will give you a fully functional RabbitMQ instance for testing the notification service!