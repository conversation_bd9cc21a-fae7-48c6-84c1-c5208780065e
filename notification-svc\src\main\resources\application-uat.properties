# UAT Environment Overrides
# This file contains only UAT-specific settings that override application.properties

# Environment Identifier
ENVIRONMENT=uat

# Database Configuration - Validate schema in UAT
spring.jpa.hibernate.ddl-auto=validate

# Logging Configuration - Info level for UAT
logging.level.com.ascentbusiness.notification_svc=INFO
logging.level.org.springframework.security=WARN
logging.level.org.hibernate=WARN
logging.level.org.springframework.web=INFO

# GraphQL Configuration - Enable GraphiQL for UAT testing
spring.graphql.graphiql.enabled=true
spring.graphql.schema.printer.enabled=false

# Security Configuration - Stricter CORS for UAT
security.cors.allowed-origins=${CORS_ALLOWED_ORIGINS}
spring.graphql.cors.allowed-origins=${CORS_ALLOWED_ORIGINS}

# Cache Configuration - Standard TTL for UAT
spring.cache.redis.key-prefix=notification:uat:

# Notification Configuration - Real email for UAT
notification.email.mock=false

# Actuator Configuration - Limited exposure for UAT
management.endpoints.web.exposure.include=health,info,metrics,prometheus
management.endpoint.health.show-details=when-authorized
management.endpoint.health.show-components=when-authorized

# Connection Pool Configuration - UAT optimized
spring.datasource.hikari.maximum-pool-size=15
spring.datasource.hikari.minimum-idle=3
spring.data.redis.lettuce.pool.max-active=15
spring.data.redis.lettuce.pool.max-idle=8
spring.data.redis.lettuce.pool.min-idle=2

# Server Configuration - UAT optimized
server.tomcat.max-connections=4096
server.tomcat.threads.max=100
server.tomcat.threads.min-spare=8

# Async Configuration - UAT optimized
spring.task.execution.pool.core-size=8
spring.task.execution.pool.max-size=25
spring.task.execution.pool.queue-capacity=75
spring.task.execution.thread-name-prefix=notification-async-uat-

# Notification Processing - UAT batch sizes
notification.processing.batch-size=50
spring.jpa.properties.hibernate.jdbc.batch_size=30

# Development Features - Disabled for UAT
spring.devtools.restart.enabled=false
spring.devtools.livereload.enabled=false