<?xml version="1.0" encoding="UTF-8"?><!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Strict//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-strict.dtd"><html xmlns="http://www.w3.org/1999/xhtml" lang="en"><head><meta http-equiv="Content-Type" content="text/html;charset=UTF-8"/><link rel="stylesheet" href="../jacoco-resources/report.css" type="text/css"/><link rel="shortcut icon" href="../jacoco-resources/report.gif" type="image/gif"/><title>DocumentShareResolver</title><script type="text/javascript" src="../jacoco-resources/sort.js"></script></head><body onload="initialSort(['breadcrumb'])"><div class="breadcrumb" id="breadcrumb"><span class="info"><a href="../jacoco-sessions.html" class="el_session">Sessions</a></span><a href="../index.html" class="el_report">dms-svc</a> &gt; <a href="index.html" class="el_package">com.ascentbusiness.dms_svc.resolver</a> &gt; <span class="el_class">DocumentShareResolver</span></div><h1>DocumentShareResolver</h1><table class="coverage" cellspacing="0" id="coveragetable"><thead><tr><td class="sortable" id="a" onclick="toggleSort(this)">Element</td><td class="down sortable bar" id="b" onclick="toggleSort(this)">Missed Instructions</td><td class="sortable ctr2" id="c" onclick="toggleSort(this)">Cov.</td><td class="sortable bar" id="d" onclick="toggleSort(this)">Missed Branches</td><td class="sortable ctr2" id="e" onclick="toggleSort(this)">Cov.</td><td class="sortable ctr1" id="f" onclick="toggleSort(this)">Missed</td><td class="sortable ctr2" id="g" onclick="toggleSort(this)">Cxty</td><td class="sortable ctr1" id="h" onclick="toggleSort(this)">Missed</td><td class="sortable ctr2" id="i" onclick="toggleSort(this)">Lines</td><td class="sortable ctr1" id="j" onclick="toggleSort(this)">Missed</td><td class="sortable ctr2" id="k" onclick="toggleSort(this)">Methods</td></tr></thead><tfoot><tr><td>Total</td><td class="bar">248 of 264</td><td class="ctr2">6%</td><td class="bar">6 of 6</td><td class="ctr2">0%</td><td class="ctr1">17</td><td class="ctr2">19</td><td class="ctr1">66</td><td class="ctr2">68</td><td class="ctr1">14</td><td class="ctr2">16</td></tr></tfoot><tbody><tr><td id="a1"><a href="DocumentShareResolver.java.html#L179" class="el_method">bulkShareDocuments(BulkShareInput)</a></td><td class="bar" id="b0"><img src="../jacoco-resources/redbar.gif" width="120" height="10" title="49" alt="49"/></td><td class="ctr2" id="c2">0%</td><td class="bar" id="d1"><img src="../jacoco-resources/redbar.gif" width="60" height="10" title="2" alt="2"/></td><td class="ctr2" id="e0">0%</td><td class="ctr1" id="f1">2</td><td class="ctr2" id="g1">2</td><td class="ctr1" id="h0">13</td><td class="ctr2" id="i0">13</td><td class="ctr1" id="j0">1</td><td class="ctr2" id="k0">1</td></tr><tr><td id="a0"><a href="DocumentShareResolver.java.html#L98" class="el_method">accessSharedDocument(AccessSharedDocumentInput)</a></td><td class="bar" id="b1"><img src="../jacoco-resources/redbar.gif" width="117" height="10" title="48" alt="48"/></td><td class="ctr2" id="c3">0%</td><td class="bar" id="d0"><img src="../jacoco-resources/redbar.gif" width="120" height="10" title="4" alt="4"/></td><td class="ctr2" id="e1">0%</td><td class="ctr1" id="f0">3</td><td class="ctr2" id="g0">3</td><td class="ctr1" id="h1">12</td><td class="ctr2" id="i1">12</td><td class="ctr1" id="j1">1</td><td class="ctr2" id="k1">1</td></tr><tr><td id="a3"><a href="DocumentShareResolver.java.html#L134" class="el_method">createDocumentShareLink(Long, CreateShareLinkInput)</a></td><td class="bar" id="b2"><img src="../jacoco-resources/redbar.gif" width="71" height="10" title="29" alt="29"/></td><td class="ctr2" id="c4">0%</td><td class="bar" id="d2"/><td class="ctr2" id="e2">n/a</td><td class="ctr1" id="f2">1</td><td class="ctr2" id="g2">1</td><td class="ctr1" id="h3">8</td><td class="ctr2" id="i3">8</td><td class="ctr1" id="j2">1</td><td class="ctr2" id="k2">1</td></tr><tr><td id="a11"><a href="DocumentShareResolver.java.html#L194" class="el_method">lambda$bulkShareDocuments$0(BulkShareService.BulkShareItemResult)</a></td><td class="bar" id="b3"><img src="../jacoco-resources/redbar.gif" width="58" height="10" title="24" alt="24"/></td><td class="ctr2" id="c5">0%</td><td class="bar" id="d3"/><td class="ctr2" id="e3">n/a</td><td class="ctr1" id="f3">1</td><td class="ctr2" id="g3">1</td><td class="ctr1" id="h2">9</td><td class="ctr2" id="i2">9</td><td class="ctr1" id="j3">1</td><td class="ctr2" id="k3">1</td></tr><tr><td id="a13"><a href="DocumentShareResolver.java.html#L158" class="el_method">revokeDocumentShareLink(String)</a></td><td class="bar" id="b4"><img src="../jacoco-resources/redbar.gif" width="53" height="10" title="22" alt="22"/></td><td class="ctr2" id="c6">0%</td><td class="bar" id="d4"/><td class="ctr2" id="e4">n/a</td><td class="ctr1" id="f4">1</td><td class="ctr2" id="g4">1</td><td class="ctr1" id="h4">6</td><td class="ctr2" id="i4">6</td><td class="ctr1" id="j4">1</td><td class="ctr2" id="k4">1</td></tr><tr><td id="a7"><a href="DocumentShareResolver.java.html#L245" class="el_method">getDocument(BulkShareItem)</a></td><td class="bar" id="b5"><img src="../jacoco-resources/redbar.gif" width="34" height="10" title="14" alt="14"/></td><td class="ctr2" id="c7">0%</td><td class="bar" id="d5"/><td class="ctr2" id="e5">n/a</td><td class="ctr1" id="f5">1</td><td class="ctr2" id="g5">1</td><td class="ctr1" id="h5">4</td><td class="ctr2" id="i5">4</td><td class="ctr1" id="j5">1</td><td class="ctr2" id="k5">1</td></tr><tr><td id="a8"><a href="DocumentShareResolver.java.html#L258" class="el_method">getDocumentShareLinks(Document)</a></td><td class="bar" id="b6"><img src="../jacoco-resources/redbar.gif" width="34" height="10" title="14" alt="14"/></td><td class="ctr2" id="c8">0%</td><td class="bar" id="d6"/><td class="ctr2" id="e6">n/a</td><td class="ctr1" id="f6">1</td><td class="ctr2" id="g6">1</td><td class="ctr1" id="h6">4</td><td class="ctr2" id="i6">4</td><td class="ctr1" id="j6">1</td><td class="ctr2" id="k6">1</td></tr><tr><td id="a4"><a href="DocumentShareResolver.java.html#L58" class="el_method">documentShareLinks(Long)</a></td><td class="bar" id="b7"><img src="../jacoco-resources/redbar.gif" width="22" height="10" title="9" alt="9"/></td><td class="ctr2" id="c9">0%</td><td class="bar" id="d7"/><td class="ctr2" id="e7">n/a</td><td class="ctr1" id="f7">1</td><td class="ctr2" id="g7">1</td><td class="ctr1" id="h7">2</td><td class="ctr2" id="i7">2</td><td class="ctr1" id="j7">1</td><td class="ctr2" id="k7">1</td></tr><tr><td id="a14"><a href="DocumentShareResolver.java.html#L68" class="el_method">shareLink(String)</a></td><td class="bar" id="b8"><img src="../jacoco-resources/redbar.gif" width="22" height="10" title="9" alt="9"/></td><td class="ctr2" id="c10">0%</td><td class="bar" id="d8"/><td class="ctr2" id="e8">n/a</td><td class="ctr1" id="f8">1</td><td class="ctr2" id="g8">1</td><td class="ctr1" id="h8">2</td><td class="ctr2" id="i8">2</td><td class="ctr1" id="j8">1</td><td class="ctr2" id="k8">1</td></tr><tr><td id="a2"><a href="DocumentShareResolver.java.html#L88" class="el_method">bulkShareOperation(String)</a></td><td class="bar" id="b9"><img src="../jacoco-resources/redbar.gif" width="22" height="10" title="9" alt="9"/></td><td class="ctr2" id="c11">0%</td><td class="bar" id="d9"/><td class="ctr2" id="e9">n/a</td><td class="ctr1" id="f9">1</td><td class="ctr2" id="g9">1</td><td class="ctr1" id="h9">2</td><td class="ctr2" id="i9">2</td><td class="ctr1" id="j9">1</td><td class="ctr2" id="k9">1</td></tr><tr><td id="a12"><a href="DocumentShareResolver.java.html#L78" class="el_method">myBulkShareOperations()</a></td><td class="bar" id="b10"><img src="../jacoco-resources/redbar.gif" width="17" height="10" title="7" alt="7"/></td><td class="ctr2" id="c12">0%</td><td class="bar" id="d10"/><td class="ctr2" id="e10">n/a</td><td class="ctr1" id="f10">1</td><td class="ctr2" id="g10">1</td><td class="ctr1" id="h10">2</td><td class="ctr2" id="i10">2</td><td class="ctr1" id="j10">1</td><td class="ctr2" id="k10">1</td></tr><tr><td id="a6"><a href="DocumentShareResolver.java.html#L236" class="el_method">getBulkShareItems(BulkShareOperation)</a></td><td class="bar" id="b11"><img src="../jacoco-resources/redbar.gif" width="14" height="10" title="6" alt="6"/></td><td class="ctr2" id="c13">0%</td><td class="bar" id="d11"/><td class="ctr2" id="e11">n/a</td><td class="ctr1" id="f11">1</td><td class="ctr2" id="g11">1</td><td class="ctr1" id="h11">1</td><td class="ctr2" id="i11">1</td><td class="ctr1" id="j11">1</td><td class="ctr2" id="k11">1</td></tr><tr><td id="a10"><a href="DocumentShareResolver.java.html#L220" class="el_method">getShareUrl(DocumentShareLink)</a></td><td class="bar" id="b12"><img src="../jacoco-resources/redbar.gif" width="12" height="10" title="5" alt="5"/></td><td class="ctr2" id="c14">0%</td><td class="bar" id="d12"/><td class="ctr2" id="e12">n/a</td><td class="ctr1" id="f12">1</td><td class="ctr2" id="g12">1</td><td class="ctr1" id="h12">1</td><td class="ctr2" id="i12">1</td><td class="ctr1" id="j12">1</td><td class="ctr2" id="k12">1</td></tr><tr><td id="a9"><a href="DocumentShareResolver.java.html#L228" class="el_method">getHasPassword(DocumentShareLink)</a></td><td class="bar" id="b13"><img src="../jacoco-resources/redbar.gif" width="7" height="10" title="3" alt="3"/></td><td class="ctr2" id="c15">0%</td><td class="bar" id="d13"/><td class="ctr2" id="e13">n/a</td><td class="ctr1" id="f13">1</td><td class="ctr2" id="g13">1</td><td class="ctr1" id="h13">1</td><td class="ctr2" id="i13">1</td><td class="ctr1" id="j13">1</td><td class="ctr2" id="k13">1</td></tr><tr><td id="a5"><a href="DocumentShareResolver.java.html#L39" class="el_method">DocumentShareResolver(DocumentShareService, BulkShareService, DocumentService)</a></td><td class="bar" id="b14"><img src="../jacoco-resources/greenbar.gif" width="29" height="10" title="12" alt="12"/></td><td class="ctr2" id="c0">100%</td><td class="bar" id="d14"/><td class="ctr2" id="e14">n/a</td><td class="ctr1" id="f14">0</td><td class="ctr2" id="g14">1</td><td class="ctr1" id="h14">0</td><td class="ctr2" id="i14">1</td><td class="ctr1" id="j14">0</td><td class="ctr2" id="k14">1</td></tr><tr><td id="a15"><a href="DocumentShareResolver.java.html#L40" class="el_method">static {...}</a></td><td class="bar" id="b15"><img src="../jacoco-resources/greenbar.gif" width="9" height="10" title="4" alt="4"/></td><td class="ctr2" id="c1">100%</td><td class="bar" id="d15"/><td class="ctr2" id="e15">n/a</td><td class="ctr1" id="f15">0</td><td class="ctr2" id="g15">1</td><td class="ctr1" id="h15">0</td><td class="ctr2" id="i15">1</td><td class="ctr1" id="j15">0</td><td class="ctr2" id="k15">1</td></tr></tbody></table><div class="footer"><span class="right">Created with <a href="http://www.jacoco.org/jacoco">JaCoCo</a> 0.8.10.202304240956</span></div></body></html>