@echo off
REM Async Document Processing Test Suite
REM Comprehensive testing for async processing functionality
REM Version: 1.0

echo ========================================
echo Async Document Processing Test Suite
echo ========================================
echo Version: 1.0 - Comprehensive Async Processing Tests
echo.

REM Initialize counters
set PASSED_TESTS=0
set FAILED_TESTS=0
set TOTAL_TESTS=0

REM Test 1: Async Document Processor Service Tests
echo [1/7] Running AsyncDocumentProcessor Service Tests...
call mvn test -Dtest=AsyncDocumentProcessorTest -q
if %ERRORLEVEL% EQU 0 (
    echo ✓ AsyncDocumentProcessor Tests PASSED
    set /a PASSED_TESTS+=1
) else (
    echo ✗ AsyncDocumentProcessor Tests FAILED
    set /a FAILED_TESTS+=1
)
set /a TOTAL_TESTS+=1
echo.

REM Test 2: Repository Tests
echo [2/7] Running AsyncProcessingJob Repository Tests...
call mvn test -Dtest=AsyncProcessingJobRepositoryTest -q
if %ERRORLEVEL% EQU 0 (
    echo ✓ Repository Tests PASSED
    set /a PASSED_TESTS+=1
) else (
    echo ✗ Repository Tests FAILED
    set /a FAILED_TESTS+=1
)
set /a TOTAL_TESTS+=1
echo.

REM Test 3: Entity Tests
echo [3/7] Running AsyncProcessingJob Entity Tests...
call mvn test -Dtest=AsyncProcessingJobEntityTest -q
if %ERRORLEVEL% EQU 0 (
    echo ✓ Entity Tests PASSED
    set /a PASSED_TESTS+=1
) else (
    echo ✗ Entity Tests FAILED
    set /a FAILED_TESTS+=1
)
set /a TOTAL_TESTS+=1
echo.

REM Test 4: DTO Tests
echo [4/7] Running AsyncJobStatus DTO Tests...
call mvn test -Dtest=AsyncJobStatusTest -q
if %ERRORLEVEL% EQU 0 (
    echo ✓ DTO Tests PASSED
    set /a PASSED_TESTS+=1
) else (
    echo ✗ DTO Tests FAILED
    set /a FAILED_TESTS+=1
)
set /a TOTAL_TESTS+=1
echo.

REM Test 5: Chunked Upload Manager Tests
echo [5/7] Running ChunkedUploadManager Service Tests...
call mvn test -Dtest=ChunkedUploadManagerTest -q
if %ERRORLEVEL% EQU 0 (
    echo ✓ ChunkedUploadManager Tests PASSED
    set /a PASSED_TESTS+=1
) else (
    echo ✗ ChunkedUploadManager Tests FAILED
    set /a FAILED_TESTS+=1
)
set /a TOTAL_TESTS+=1
echo.

REM Test 6: GraphQL Resolver Tests
echo [6/7] Running Async Processing GraphQL Resolver Tests...
call mvn test -Dtest=AsyncProcessingResolverTest -q
if %ERRORLEVEL% EQU 0 (
    echo ✓ GraphQL Resolver Tests PASSED
    set /a PASSED_TESTS+=1
) else (
    echo ✗ GraphQL Resolver Tests FAILED
    set /a FAILED_TESTS+=1
)
set /a TOTAL_TESTS+=1
echo.

REM Test 7: Performance Tests
echo [7/7] Running Async Processing Performance Tests...
call mvn test -Dtest=AsyncProcessingPerformanceTest -q
if %ERRORLEVEL% EQU 0 (
    echo ✓ Performance Tests PASSED
    set /a PASSED_TESTS+=1
) else (
    echo ⚠ Performance Tests FAILED (non-critical)
    set /a FAILED_TESTS+=1
)
set /a TOTAL_TESTS+=1
echo.

REM Integration Test
echo Running Async Processing Integration Tests...
call mvn test -Dtest=ExtendedFileProcessingIntegrationTest -Dtest.methods=*Async*,*Job*,*Status* -q
if %ERRORLEVEL% EQU 0 (
    echo ✓ Integration Tests PASSED
) else (
    echo ⚠ Integration Tests FAILED (non-critical)
)
echo.

REM Generate Summary
echo ========================================
echo ASYNC PROCESSING TEST SUMMARY
echo ========================================
echo Total Tests: %TOTAL_TESTS%
echo Passed: %PASSED_TESTS%
echo Failed: %FAILED_TESTS%
echo.

if %FAILED_TESTS% EQU 0 (
    echo ✓ ALL ASYNC PROCESSING TESTS PASSED!
    echo.
    echo Validated Features:
    echo - Asynchronous document processing service
    echo - Job status tracking and management
    echo - Repository operations and queries
    echo - Entity validation and transient methods
    echo - DTO mapping and serialization
    echo - Chunked upload session management
    echo - GraphQL resolver functionality
    echo - Performance under load conditions
    echo.
    exit /b 0
) else (
    echo ✗ %FAILED_TESTS% TEST(S) FAILED
    echo.
    echo Please check the following:
    echo - Database schema for async_processing_jobs table
    echo - Async processing configuration
    echo - Thread pool and executor configuration
    echo - File processing thresholds
    echo - GraphQL schema definitions
    echo.
    exit /b 1
)
