<?xml version="1.0" encoding="UTF-8"?><!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Strict//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-strict.dtd"><html xmlns="http://www.w3.org/1999/xhtml" lang="en"><head><meta http-equiv="Content-Type" content="text/html;charset=UTF-8"/><link rel="stylesheet" href="../jacoco-resources/report.css" type="text/css"/><link rel="shortcut icon" href="../jacoco-resources/report.gif" type="image/gif"/><title>UrlUploadConfig</title><script type="text/javascript" src="../jacoco-resources/sort.js"></script></head><body onload="initialSort(['breadcrumb'])"><div class="breadcrumb" id="breadcrumb"><span class="info"><a href="../jacoco-sessions.html" class="el_session">Sessions</a></span><a href="../index.html" class="el_report">dms-svc</a> &gt; <a href="index.html" class="el_package">com.ascentbusiness.dms_svc.config</a> &gt; <span class="el_class">UrlUploadConfig</span></div><h1>UrlUploadConfig</h1><table class="coverage" cellspacing="0" id="coveragetable"><thead><tr><td class="sortable" id="a" onclick="toggleSort(this)">Element</td><td class="down sortable bar" id="b" onclick="toggleSort(this)">Missed Instructions</td><td class="sortable ctr2" id="c" onclick="toggleSort(this)">Cov.</td><td class="sortable bar" id="d" onclick="toggleSort(this)">Missed Branches</td><td class="sortable ctr2" id="e" onclick="toggleSort(this)">Cov.</td><td class="sortable ctr1" id="f" onclick="toggleSort(this)">Missed</td><td class="sortable ctr2" id="g" onclick="toggleSort(this)">Cxty</td><td class="sortable ctr1" id="h" onclick="toggleSort(this)">Missed</td><td class="sortable ctr2" id="i" onclick="toggleSort(this)">Lines</td><td class="sortable ctr1" id="j" onclick="toggleSort(this)">Missed</td><td class="sortable ctr2" id="k" onclick="toggleSort(this)">Methods</td></tr></thead><tfoot><tr><td>Total</td><td class="bar">273 of 634</td><td class="ctr2">56%</td><td class="bar">76 of 102</td><td class="ctr2">25%</td><td class="ctr1">55</td><td class="ctr2">92</td><td class="ctr1">18</td><td class="ctr2">59</td><td class="ctr1">7</td><td class="ctr2">41</td></tr></tfoot><tbody><tr><td id="a1"><a href="UrlUploadConfig.java.html#L30" class="el_method">equals(Object)</a></td><td class="bar" id="b0"><img src="../jacoco-resources/redbar.gif" width="120" height="10" title="149" alt="149"/></td><td class="ctr2" id="c34">0%</td><td class="bar" id="d0"><img src="../jacoco-resources/redbar.gif" width="120" height="10" title="48" alt="48"/></td><td class="ctr2" id="e9">0%</td><td class="ctr1" id="f0">25</td><td class="ctr2" id="g0">25</td><td class="ctr1" id="h4">1</td><td class="ctr2" id="i7">1</td><td class="ctr1" id="j0">1</td><td class="ctr2" id="k0">1</td></tr><tr><td id="a39"><a href="UrlUploadConfig.java.html#L30" class="el_method">toString()</a></td><td class="bar" id="b1"><img src="../jacoco-resources/redbar.gif" width="19" height="10" title="24" alt="24"/></td><td class="ctr2" id="c35">0%</td><td class="bar" id="d12"/><td class="ctr2" id="e12">n/a</td><td class="ctr1" id="f10">1</td><td class="ctr2" id="g12">1</td><td class="ctr1" id="h5">1</td><td class="ctr2" id="i8">1</td><td class="ctr1" id="j1">1</td><td class="ctr2" id="k1">1</td></tr><tr><td id="a17"><a href="UrlUploadConfig.java.html#L163" class="el_method">isDomainAllowed(String)</a></td><td class="bar" id="b2"><img src="../jacoco-resources/redbar.gif" width="15" height="10" title="19" alt="19"/><img src="../jacoco-resources/greenbar.gif" width="8" height="10" title="10" alt="10"/></td><td class="ctr2" id="c33">34%</td><td class="bar" id="d2"><img src="../jacoco-resources/redbar.gif" width="15" height="10" title="6" alt="6"/><img src="../jacoco-resources/greenbar.gif" width="5" height="10" title="2" alt="2"/></td><td class="ctr2" id="e8">25%</td><td class="ctr1" id="f2">4</td><td class="ctr2" id="g2">5</td><td class="ctr1" id="h2">4</td><td class="ctr2" id="i2">8</td><td class="ctr1" id="j7">0</td><td class="ctr2" id="k2">1</td></tr><tr><td id="a5"><a href="UrlUploadConfig.java.html#L98" class="el_method">getAllowedDomainsSet()</a></td><td class="bar" id="b3"><img src="../jacoco-resources/redbar.gif" width="12" height="10" title="15" alt="15"/><img src="../jacoco-resources/greenbar.gif" width="8" height="10" title="10" alt="10"/></td><td class="ctr2" id="c31">40%</td><td class="bar" id="d4"><img src="../jacoco-resources/redbar.gif" width="5" height="10" title="2" alt="2"/><img src="../jacoco-resources/greenbar.gif" width="5" height="10" title="2" alt="2"/></td><td class="ctr2" id="e2">50%</td><td class="ctr1" id="f3">2</td><td class="ctr2" id="g4">3</td><td class="ctr1" id="h0">5</td><td class="ctr2" id="i3">7</td><td class="ctr1" id="j8">0</td><td class="ctr2" id="k3">1</td></tr><tr><td id="a3"><a href="UrlUploadConfig.java.html#L130" class="el_method">getAllowedContentTypesSet()</a></td><td class="bar" id="b4"><img src="../jacoco-resources/redbar.gif" width="12" height="10" title="15" alt="15"/><img src="../jacoco-resources/greenbar.gif" width="8" height="10" title="10" alt="10"/></td><td class="ctr2" id="c32">40%</td><td class="bar" id="d5"><img src="../jacoco-resources/redbar.gif" width="5" height="10" title="2" alt="2"/><img src="../jacoco-resources/greenbar.gif" width="5" height="10" title="2" alt="2"/></td><td class="ctr2" id="e3">50%</td><td class="ctr1" id="f4">2</td><td class="ctr2" id="g5">3</td><td class="ctr1" id="h1">5</td><td class="ctr2" id="i4">7</td><td class="ctr1" id="j9">0</td><td class="ctr2" id="k4">1</td></tr><tr><td id="a14"><a href="UrlUploadConfig.java.html#L30" class="el_method">hashCode()</a></td><td class="bar" id="b5"><img src="../jacoco-resources/redbar.gif" width="8" height="10" title="11" alt="11"/><img src="../jacoco-resources/greenbar.gif" width="101" height="10" title="126" alt="126"/></td><td class="ctr2" id="c27">91%</td><td class="bar" id="d1"><img src="../jacoco-resources/redbar.gif" width="15" height="10" title="6" alt="6"/><img src="../jacoco-resources/greenbar.gif" width="25" height="10" title="10" alt="10"/></td><td class="ctr2" id="e1">62%</td><td class="ctr1" id="f1">6</td><td class="ctr2" id="g1">9</td><td class="ctr1" id="h13">0</td><td class="ctr2" id="i9">1</td><td class="ctr1" id="j10">0</td><td class="ctr2" id="k5">1</td></tr><tr><td id="a16"><a href="UrlUploadConfig.java.html#L194" class="el_method">isContentTypeAllowed(String)</a></td><td class="bar" id="b6"><img src="../jacoco-resources/redbar.gif" width="6" height="10" title="8" alt="8"/><img src="../jacoco-resources/greenbar.gif" width="19" height="10" title="24" alt="24"/></td><td class="ctr2" id="c30">75%</td><td class="bar" id="d3"><img src="../jacoco-resources/redbar.gif" width="5" height="10" title="2" alt="2"/><img src="../jacoco-resources/greenbar.gif" width="10" height="10" title="4" alt="4"/></td><td class="ctr2" id="e0">66%</td><td class="ctr1" id="f5">2</td><td class="ctr2" id="g3">4</td><td class="ctr1" id="h3">2</td><td class="ctr2" id="i1">10</td><td class="ctr1" id="j11">0</td><td class="ctr2" id="k6">1</td></tr><tr><td id="a21"><a href="UrlUploadConfig.java.html#L136" class="el_method">lambda$getAllowedContentTypesSet$2(String)</a></td><td class="bar" id="b7"><img src="../jacoco-resources/redbar.gif" width="5" height="10" title="7" alt="7"/></td><td class="ctr2" id="c36">0%</td><td class="bar" id="d8"><img src="../jacoco-resources/redbar.gif" width="5" height="10" title="2" alt="2"/></td><td class="ctr2" id="e10">0%</td><td class="ctr1" id="f6">2</td><td class="ctr2" id="g8">2</td><td class="ctr1" id="h6">1</td><td class="ctr2" id="i10">1</td><td class="ctr1" id="j2">1</td><td class="ctr2" id="k7">1</td></tr><tr><td id="a22"><a href="UrlUploadConfig.java.html#L104" class="el_method">lambda$getAllowedDomainsSet$0(String)</a></td><td class="bar" id="b8"><img src="../jacoco-resources/redbar.gif" width="5" height="10" title="7" alt="7"/></td><td class="ctr2" id="c37">0%</td><td class="bar" id="d9"><img src="../jacoco-resources/redbar.gif" width="5" height="10" title="2" alt="2"/></td><td class="ctr2" id="e11">0%</td><td class="ctr1" id="f7">2</td><td class="ctr2" id="g9">2</td><td class="ctr1" id="h7">1</td><td class="ctr2" id="i11">1</td><td class="ctr1" id="j3">1</td><td class="ctr2" id="k8">1</td></tr><tr><td id="a27"><a href="UrlUploadConfig.java.html#L174" class="el_method">lambda$isDomainAllowed$4(String, String)</a></td><td class="bar" id="b9"><img src="../jacoco-resources/redbar.gif" width="4" height="10" title="5" alt="5"/></td><td class="ctr2" id="c38">0%</td><td class="bar" id="d13"/><td class="ctr2" id="e13">n/a</td><td class="ctr1" id="f11">1</td><td class="ctr2" id="g13">1</td><td class="ctr1" id="h8">1</td><td class="ctr2" id="i12">1</td><td class="ctr1" id="j4">1</td><td class="ctr2" id="k9">1</td></tr><tr><td id="a26"><a href="UrlUploadConfig.java.html#L212" class="el_method">lambda$isContentTypeAllowed$6(String, String)</a></td><td class="bar" id="b10"><img src="../jacoco-resources/redbar.gif" width="3" height="10" title="4" alt="4"/></td><td class="ctr2" id="c39">0%</td><td class="bar" id="d14"/><td class="ctr2" id="e14">n/a</td><td class="ctr1" id="f12">1</td><td class="ctr2" id="g14">1</td><td class="ctr1" id="h9">1</td><td class="ctr2" id="i13">1</td><td class="ctr1" id="j5">1</td><td class="ctr2" id="k10">1</td></tr><tr><td id="a0"><a href="UrlUploadConfig.java.html#L30" class="el_method">canEqual(Object)</a></td><td class="bar" id="b11"><img src="../jacoco-resources/redbar.gif" width="2" height="10" title="3" alt="3"/></td><td class="ctr2" id="c40">0%</td><td class="bar" id="d15"/><td class="ctr2" id="e15">n/a</td><td class="ctr1" id="f13">1</td><td class="ctr2" id="g15">1</td><td class="ctr1" id="h10">1</td><td class="ctr2" id="i14">1</td><td class="ctr1" id="j6">1</td><td class="ctr2" id="k11">1</td></tr><tr><td id="a9"><a href="UrlUploadConfig.java.html#L114" class="el_method">getBlockedPortsSet()</a></td><td class="bar" id="b12"><img src="../jacoco-resources/redbar.gif" width="1" height="10" title="2" alt="2"/><img src="../jacoco-resources/greenbar.gif" width="18" height="10" title="23" alt="23"/></td><td class="ctr2" id="c25">92%</td><td class="bar" id="d6"><img src="../jacoco-resources/redbar.gif" width="5" height="10" title="2" alt="2"/><img src="../jacoco-resources/greenbar.gif" width="5" height="10" title="2" alt="2"/></td><td class="ctr2" id="e4">50%</td><td class="ctr1" id="f8">2</td><td class="ctr2" id="g6">3</td><td class="ctr1" id="h11">1</td><td class="ctr2" id="i5">7</td><td class="ctr1" id="j12">0</td><td class="ctr2" id="k12">1</td></tr><tr><td id="a7"><a href="UrlUploadConfig.java.html#L146" class="el_method">getBlockedContentTypesSet()</a></td><td class="bar" id="b13"><img src="../jacoco-resources/redbar.gif" width="1" height="10" title="2" alt="2"/><img src="../jacoco-resources/greenbar.gif" width="18" height="10" title="23" alt="23"/></td><td class="ctr2" id="c26">92%</td><td class="bar" id="d7"><img src="../jacoco-resources/redbar.gif" width="5" height="10" title="2" alt="2"/><img src="../jacoco-resources/greenbar.gif" width="5" height="10" title="2" alt="2"/></td><td class="ctr2" id="e5">50%</td><td class="ctr1" id="f9">2</td><td class="ctr2" id="g7">3</td><td class="ctr1" id="h12">1</td><td class="ctr2" id="i6">7</td><td class="ctr1" id="j13">0</td><td class="ctr2" id="k13">1</td></tr><tr><td id="a23"><a href="UrlUploadConfig.java.html#L152" class="el_method">lambda$getBlockedContentTypesSet$3(String)</a></td><td class="bar" id="b14"><img src="../jacoco-resources/greenbar.gif" width="4" height="10" title="6" alt="6"/></td><td class="ctr2" id="c28">85%</td><td class="bar" id="d10"><img src="../jacoco-resources/redbar.gif" width="2" height="10" title="1" alt="1"/><img src="../jacoco-resources/greenbar.gif" width="2" height="10" title="1" alt="1"/></td><td class="ctr2" id="e6">50%</td><td class="ctr1" id="f14">1</td><td class="ctr2" id="g10">2</td><td class="ctr1" id="h14">0</td><td class="ctr2" id="i15">1</td><td class="ctr1" id="j14">0</td><td class="ctr2" id="k14">1</td></tr><tr><td id="a24"><a href="UrlUploadConfig.java.html#L119" class="el_method">lambda$getBlockedPortsSet$1(String)</a></td><td class="bar" id="b15"><img src="../jacoco-resources/greenbar.gif" width="4" height="10" title="6" alt="6"/></td><td class="ctr2" id="c29">85%</td><td class="bar" id="d11"><img src="../jacoco-resources/redbar.gif" width="2" height="10" title="1" alt="1"/><img src="../jacoco-resources/greenbar.gif" width="2" height="10" title="1" alt="1"/></td><td class="ctr2" id="e7">50%</td><td class="ctr1" id="f15">1</td><td class="ctr2" id="g11">2</td><td class="ctr1" id="h15">0</td><td class="ctr2" id="i16">1</td><td class="ctr1" id="j15">0</td><td class="ctr2" id="k15">1</td></tr><tr><td id="a40"><a href="UrlUploadConfig.java.html#L30" class="el_method">UrlUploadConfig()</a></td><td class="bar" id="b16"><img src="../jacoco-resources/greenbar.gif" width="28" height="10" title="36" alt="36"/></td><td class="ctr2" id="c0">100%</td><td class="bar" id="d16"/><td class="ctr2" id="e16">n/a</td><td class="ctr1" id="f16">0</td><td class="ctr2" id="g16">1</td><td class="ctr1" id="h16">0</td><td class="ctr2" id="i0">12</td><td class="ctr1" id="j16">0</td><td class="ctr2" id="k16">1</td></tr><tr><td id="a19"><a href="UrlUploadConfig.java.html#L184" class="el_method">isPortBlocked(int)</a></td><td class="bar" id="b17"><img src="../jacoco-resources/greenbar.gif" width="4" height="10" title="6" alt="6"/></td><td class="ctr2" id="c1">100%</td><td class="bar" id="d17"/><td class="ctr2" id="e17">n/a</td><td class="ctr1" id="f17">0</td><td class="ctr2" id="g17">1</td><td class="ctr1" id="h17">0</td><td class="ctr2" id="i17">1</td><td class="ctr1" id="j17">0</td><td class="ctr2" id="k17">1</td></tr><tr><td id="a33"><a href="UrlUploadConfig.java.html#L30" class="el_method">setEnabled(boolean)</a></td><td class="bar" id="b18"><img src="../jacoco-resources/greenbar.gif" width="3" height="10" title="4" alt="4"/></td><td class="ctr2" id="c2">100%</td><td class="bar" id="d18"/><td class="ctr2" id="e18">n/a</td><td class="ctr1" id="f18">0</td><td class="ctr2" id="g18">1</td><td class="ctr1" id="h18">0</td><td class="ctr2" id="i18">1</td><td class="ctr1" id="j18">0</td><td class="ctr2" id="k18">1</td></tr><tr><td id="a36"><a href="UrlUploadConfig.java.html#L30" class="el_method">setTimeoutMs(int)</a></td><td class="bar" id="b19"><img src="../jacoco-resources/greenbar.gif" width="3" height="10" title="4" alt="4"/></td><td class="ctr2" id="c3">100%</td><td class="bar" id="d19"/><td class="ctr2" id="e19">n/a</td><td class="ctr1" id="f19">0</td><td class="ctr2" id="g19">1</td><td class="ctr1" id="h19">0</td><td class="ctr2" id="i19">1</td><td class="ctr1" id="j19">0</td><td class="ctr2" id="k19">1</td></tr><tr><td id="a34"><a href="UrlUploadConfig.java.html#L30" class="el_method">setMaxFileSize(long)</a></td><td class="bar" id="b20"><img src="../jacoco-resources/greenbar.gif" width="3" height="10" title="4" alt="4"/></td><td class="ctr2" id="c4">100%</td><td class="bar" id="d20"/><td class="ctr2" id="e20">n/a</td><td class="ctr1" id="f20">0</td><td class="ctr2" id="g20">1</td><td class="ctr1" id="h20">0</td><td class="ctr2" id="i20">1</td><td class="ctr1" id="j20">0</td><td class="ctr2" id="k20">1</td></tr><tr><td id="a35"><a href="UrlUploadConfig.java.html#L30" class="el_method">setMaxRedirects(int)</a></td><td class="bar" id="b21"><img src="../jacoco-resources/greenbar.gif" width="3" height="10" title="4" alt="4"/></td><td class="ctr2" id="c5">100%</td><td class="bar" id="d21"/><td class="ctr2" id="e21">n/a</td><td class="ctr1" id="f21">0</td><td class="ctr2" id="g21">1</td><td class="ctr1" id="h21">0</td><td class="ctr2" id="i21">1</td><td class="ctr1" id="j21">0</td><td class="ctr2" id="k21">1</td></tr><tr><td id="a37"><a href="UrlUploadConfig.java.html#L30" class="el_method">setUserAgent(String)</a></td><td class="bar" id="b22"><img src="../jacoco-resources/greenbar.gif" width="3" height="10" title="4" alt="4"/></td><td class="ctr2" id="c6">100%</td><td class="bar" id="d22"/><td class="ctr2" id="e22">n/a</td><td class="ctr1" id="f22">0</td><td class="ctr2" id="g22">1</td><td class="ctr1" id="h22">0</td><td class="ctr2" id="i22">1</td><td class="ctr1" id="j22">0</td><td class="ctr2" id="k22">1</td></tr><tr><td id="a29"><a href="UrlUploadConfig.java.html#L30" class="el_method">setAllowedDomains(String)</a></td><td class="bar" id="b23"><img src="../jacoco-resources/greenbar.gif" width="3" height="10" title="4" alt="4"/></td><td class="ctr2" id="c7">100%</td><td class="bar" id="d23"/><td class="ctr2" id="e23">n/a</td><td class="ctr1" id="f23">0</td><td class="ctr2" id="g23">1</td><td class="ctr1" id="h23">0</td><td class="ctr2" id="i23">1</td><td class="ctr1" id="j23">0</td><td class="ctr2" id="k23">1</td></tr><tr><td id="a38"><a href="UrlUploadConfig.java.html#L30" class="el_method">setValidateSsl(boolean)</a></td><td class="bar" id="b24"><img src="../jacoco-resources/greenbar.gif" width="3" height="10" title="4" alt="4"/></td><td class="ctr2" id="c8">100%</td><td class="bar" id="d24"/><td class="ctr2" id="e24">n/a</td><td class="ctr1" id="f24">0</td><td class="ctr2" id="g24">1</td><td class="ctr1" id="h24">0</td><td class="ctr2" id="i24">1</td><td class="ctr1" id="j24">0</td><td class="ctr2" id="k24">1</td></tr><tr><td id="a30"><a href="UrlUploadConfig.java.html#L30" class="el_method">setAllowPrivateIps(boolean)</a></td><td class="bar" id="b25"><img src="../jacoco-resources/greenbar.gif" width="3" height="10" title="4" alt="4"/></td><td class="ctr2" id="c9">100%</td><td class="bar" id="d25"/><td class="ctr2" id="e25">n/a</td><td class="ctr1" id="f25">0</td><td class="ctr2" id="g25">1</td><td class="ctr1" id="h25">0</td><td class="ctr2" id="i25">1</td><td class="ctr1" id="j25">0</td><td class="ctr2" id="k25">1</td></tr><tr><td id="a32"><a href="UrlUploadConfig.java.html#L30" class="el_method">setBlockedPorts(String)</a></td><td class="bar" id="b26"><img src="../jacoco-resources/greenbar.gif" width="3" height="10" title="4" alt="4"/></td><td class="ctr2" id="c10">100%</td><td class="bar" id="d26"/><td class="ctr2" id="e26">n/a</td><td class="ctr1" id="f26">0</td><td class="ctr2" id="g26">1</td><td class="ctr1" id="h26">0</td><td class="ctr2" id="i26">1</td><td class="ctr1" id="j26">0</td><td class="ctr2" id="k26">1</td></tr><tr><td id="a28"><a href="UrlUploadConfig.java.html#L30" class="el_method">setAllowedContentTypes(String)</a></td><td class="bar" id="b27"><img src="../jacoco-resources/greenbar.gif" width="3" height="10" title="4" alt="4"/></td><td class="ctr2" id="c11">100%</td><td class="bar" id="d27"/><td class="ctr2" id="e27">n/a</td><td class="ctr1" id="f27">0</td><td class="ctr2" id="g27">1</td><td class="ctr1" id="h27">0</td><td class="ctr2" id="i27">1</td><td class="ctr1" id="j27">0</td><td class="ctr2" id="k27">1</td></tr><tr><td id="a31"><a href="UrlUploadConfig.java.html#L30" class="el_method">setBlockedContentTypes(String)</a></td><td class="bar" id="b28"><img src="../jacoco-resources/greenbar.gif" width="3" height="10" title="4" alt="4"/></td><td class="ctr2" id="c12">100%</td><td class="bar" id="d28"/><td class="ctr2" id="e28">n/a</td><td class="ctr1" id="f28">0</td><td class="ctr2" id="g28">1</td><td class="ctr1" id="h28">0</td><td class="ctr2" id="i28">1</td><td class="ctr1" id="j28">0</td><td class="ctr2" id="k28">1</td></tr><tr><td id="a25"><a href="UrlUploadConfig.java.html#L202" class="el_method">lambda$isContentTypeAllowed$5(String, String)</a></td><td class="bar" id="b29"><img src="../jacoco-resources/greenbar.gif" width="3" height="10" title="4" alt="4"/></td><td class="ctr2" id="c13">100%</td><td class="bar" id="d29"/><td class="ctr2" id="e29">n/a</td><td class="ctr1" id="f29">0</td><td class="ctr2" id="g29">1</td><td class="ctr1" id="h29">0</td><td class="ctr2" id="i29">1</td><td class="ctr1" id="j29">0</td><td class="ctr2" id="k29">1</td></tr><tr><td id="a18"><a href="UrlUploadConfig.java.html#L36" class="el_method">isEnabled()</a></td><td class="bar" id="b30"><img src="../jacoco-resources/greenbar.gif" width="2" height="10" title="3" alt="3"/></td><td class="ctr2" id="c14">100%</td><td class="bar" id="d30"/><td class="ctr2" id="e30">n/a</td><td class="ctr1" id="f30">0</td><td class="ctr2" id="g30">1</td><td class="ctr1" id="h30">0</td><td class="ctr2" id="i30">1</td><td class="ctr1" id="j30">0</td><td class="ctr2" id="k30">1</td></tr><tr><td id="a12"><a href="UrlUploadConfig.java.html#L41" class="el_method">getTimeoutMs()</a></td><td class="bar" id="b31"><img src="../jacoco-resources/greenbar.gif" width="2" height="10" title="3" alt="3"/></td><td class="ctr2" id="c15">100%</td><td class="bar" id="d31"/><td class="ctr2" id="e31">n/a</td><td class="ctr1" id="f31">0</td><td class="ctr2" id="g31">1</td><td class="ctr1" id="h31">0</td><td class="ctr2" id="i31">1</td><td class="ctr1" id="j31">0</td><td class="ctr2" id="k31">1</td></tr><tr><td id="a10"><a href="UrlUploadConfig.java.html#L46" class="el_method">getMaxFileSize()</a></td><td class="bar" id="b32"><img src="../jacoco-resources/greenbar.gif" width="2" height="10" title="3" alt="3"/></td><td class="ctr2" id="c16">100%</td><td class="bar" id="d32"/><td class="ctr2" id="e32">n/a</td><td class="ctr1" id="f32">0</td><td class="ctr2" id="g32">1</td><td class="ctr1" id="h32">0</td><td class="ctr2" id="i32">1</td><td class="ctr1" id="j32">0</td><td class="ctr2" id="k32">1</td></tr><tr><td id="a11"><a href="UrlUploadConfig.java.html#L51" class="el_method">getMaxRedirects()</a></td><td class="bar" id="b33"><img src="../jacoco-resources/greenbar.gif" width="2" height="10" title="3" alt="3"/></td><td class="ctr2" id="c17">100%</td><td class="bar" id="d33"/><td class="ctr2" id="e33">n/a</td><td class="ctr1" id="f33">0</td><td class="ctr2" id="g33">1</td><td class="ctr1" id="h33">0</td><td class="ctr2" id="i33">1</td><td class="ctr1" id="j33">0</td><td class="ctr2" id="k33">1</td></tr><tr><td id="a13"><a href="UrlUploadConfig.java.html#L56" class="el_method">getUserAgent()</a></td><td class="bar" id="b34"><img src="../jacoco-resources/greenbar.gif" width="2" height="10" title="3" alt="3"/></td><td class="ctr2" id="c18">100%</td><td class="bar" id="d34"/><td class="ctr2" id="e34">n/a</td><td class="ctr1" id="f34">0</td><td class="ctr2" id="g34">1</td><td class="ctr1" id="h34">0</td><td class="ctr2" id="i34">1</td><td class="ctr1" id="j34">0</td><td class="ctr2" id="k34">1</td></tr><tr><td id="a4"><a href="UrlUploadConfig.java.html#L62" class="el_method">getAllowedDomains()</a></td><td class="bar" id="b35"><img src="../jacoco-resources/greenbar.gif" width="2" height="10" title="3" alt="3"/></td><td class="ctr2" id="c19">100%</td><td class="bar" id="d35"/><td class="ctr2" id="e35">n/a</td><td class="ctr1" id="f35">0</td><td class="ctr2" id="g35">1</td><td class="ctr1" id="h35">0</td><td class="ctr2" id="i35">1</td><td class="ctr1" id="j35">0</td><td class="ctr2" id="k35">1</td></tr><tr><td id="a20"><a href="UrlUploadConfig.java.html#L67" class="el_method">isValidateSsl()</a></td><td class="bar" id="b36"><img src="../jacoco-resources/greenbar.gif" width="2" height="10" title="3" alt="3"/></td><td class="ctr2" id="c20">100%</td><td class="bar" id="d36"/><td class="ctr2" id="e36">n/a</td><td class="ctr1" id="f36">0</td><td class="ctr2" id="g36">1</td><td class="ctr1" id="h36">0</td><td class="ctr2" id="i36">1</td><td class="ctr1" id="j36">0</td><td class="ctr2" id="k36">1</td></tr><tr><td id="a15"><a href="UrlUploadConfig.java.html#L72" class="el_method">isAllowPrivateIps()</a></td><td class="bar" id="b37"><img src="../jacoco-resources/greenbar.gif" width="2" height="10" title="3" alt="3"/></td><td class="ctr2" id="c21">100%</td><td class="bar" id="d37"/><td class="ctr2" id="e37">n/a</td><td class="ctr1" id="f37">0</td><td class="ctr2" id="g37">1</td><td class="ctr1" id="h37">0</td><td class="ctr2" id="i37">1</td><td class="ctr1" id="j37">0</td><td class="ctr2" id="k37">1</td></tr><tr><td id="a8"><a href="UrlUploadConfig.java.html#L77" class="el_method">getBlockedPorts()</a></td><td class="bar" id="b38"><img src="../jacoco-resources/greenbar.gif" width="2" height="10" title="3" alt="3"/></td><td class="ctr2" id="c22">100%</td><td class="bar" id="d38"/><td class="ctr2" id="e38">n/a</td><td class="ctr1" id="f38">0</td><td class="ctr2" id="g38">1</td><td class="ctr1" id="h38">0</td><td class="ctr2" id="i38">1</td><td class="ctr1" id="j38">0</td><td class="ctr2" id="k38">1</td></tr><tr><td id="a2"><a href="UrlUploadConfig.java.html#L83" class="el_method">getAllowedContentTypes()</a></td><td class="bar" id="b39"><img src="../jacoco-resources/greenbar.gif" width="2" height="10" title="3" alt="3"/></td><td class="ctr2" id="c23">100%</td><td class="bar" id="d39"/><td class="ctr2" id="e39">n/a</td><td class="ctr1" id="f39">0</td><td class="ctr2" id="g39">1</td><td class="ctr1" id="h39">0</td><td class="ctr2" id="i39">1</td><td class="ctr1" id="j39">0</td><td class="ctr2" id="k39">1</td></tr><tr><td id="a6"><a href="UrlUploadConfig.java.html#L88" class="el_method">getBlockedContentTypes()</a></td><td class="bar" id="b40"><img src="../jacoco-resources/greenbar.gif" width="2" height="10" title="3" alt="3"/></td><td class="ctr2" id="c24">100%</td><td class="bar" id="d40"/><td class="ctr2" id="e40">n/a</td><td class="ctr1" id="f40">0</td><td class="ctr2" id="g40">1</td><td class="ctr1" id="h40">0</td><td class="ctr2" id="i40">1</td><td class="ctr1" id="j40">0</td><td class="ctr2" id="k40">1</td></tr></tbody></table><div class="footer"><span class="right">Created with <a href="http://www.jacoco.org/jacoco">JaCoCo</a> 0.8.10.202304240956</span></div></body></html>