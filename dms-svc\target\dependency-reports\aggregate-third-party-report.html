<!DOCTYPE html>
<!--
 | Generated by Apache Maven Doxia Site Renderer 1.11.1 at 2025-07-23

 | Rendered using Apache Maven Default Skin
-->
<html xmlns="http://www.w3.org/1999/xhtml" xml:lang="en" lang="en">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <meta name="generator" content="Apache Maven Doxia Site Renderer 1.11.1" />
    <title>dms-svc &#x2013; Third Parties (Aggregate mode)</title>
    <link rel="stylesheet" href="./css/maven-base.css" />
    <link rel="stylesheet" href="./css/maven-theme.css" />
    <link rel="stylesheet" href="./css/site.css" />
    <link rel="stylesheet" href="./css/print.css" media="print" />
  </head>
  <body class="composite">
    <div id="banner">
      <div class="clear">
        <hr/>
      </div>
    </div>
    <div id="breadcrumbs">
      <div class="xleft">
        <span id="publishDate">Last Published: 2025-07-23</span>
           | <span id="projectVersion">Version: 0.0.1-SNAPSHOT</span>
      </div>
      <div class="xright">      </div>
      <div class="clear">
        <hr/>
      </div>
    </div>
    <div id="leftColumn">
      <div id="navcolumn">
      <a href="http://maven.apache.org/" title="Built by Maven" class="poweredBy">
        <img class="poweredBy" alt="Built by Maven" src="./images/logos/maven-feather.png" />
      </a>
      </div>
    </div>
    <div id="bodyColumn">
      <div id="contentBox">
<section>
<h2><a name="Overview"></a>Overview</h2>
<p>This report summarizes all third-party dependencies of all modules of the project.</p>
<table border="0" class="bodyTable">
<tr class="a">
<td><figure><img src="images/icon_info_sml.gif" alt="" /></figure></td>
<td># of total dependencies</td>
<td>332</td></tr>
<tr class="b">
<td><figure><img src="images/icon_success_sml.gif" alt="" /></figure></td>
<td># of dependencies with license declared in their pom</td>
<td>332</td></tr>
<tr class="a">
<td><figure><img src="images/icon_warning_sml.gif" alt="" /></figure></td>
<td># of dependencies with no license in their pom, but filled in the third-party missing file</td>
<td>0</td></tr>
<tr class="b">
<td><figure><img src="images/icon_error_sml.gif" alt="" /></figure></td>
<td># of dependencies with no license at all</td>
<td>0</td></tr></table><section>
<h3><a name="Third-parties_list"></a>Third-parties list</h3>
<table border="0" class="bodyTable">
<tr class="a">
<th>Status</th>
<th>GroupId:ArtifactId:Version</th>
<th>Scope</th>
<th>Classifier</th>
<th>Type</th>
<th>License(s)</th></tr>
<tr class="b">
<td><figure><img src="images/icon_success_sml.gif" alt="" /></figure>
<td><a href="#aopalliance:aopalliance:1.0">aopalliance:aopalliance:1.0</a></td>
<td>compile</td>
<td></td>
<td>jar</td>
<td>Public Domain</td></tr>
<tr class="a">
<td><figure><img src="images/icon_success_sml.gif" alt="" /></figure>
<td><a href="#ch.qos.logback:logback-classic:1.5.18">ch.qos.logback:logback-classic:1.5.18</a></td>
<td>compile</td>
<td></td>
<td>jar</td>
<td>Eclipse Public License - v 1.0<br />GNU Lesser General Public License</td></tr>
<tr class="b">
<td><figure><img src="images/icon_success_sml.gif" alt="" /></figure>
<td><a href="#ch.qos.logback:logback-core:1.5.18">ch.qos.logback:logback-core:1.5.18</a></td>
<td>compile</td>
<td></td>
<td>jar</td>
<td>Eclipse Public License - v 1.0<br />GNU Lesser General Public License</td></tr>
<tr class="a">
<td><figure><img src="images/icon_success_sml.gif" alt="" /></figure>
<td><a href="#co.elastic.clients:elasticsearch-java:8.18.1">co.elastic.clients:elasticsearch-java:8.18.1</a></td>
<td>compile</td>
<td></td>
<td>jar</td>
<td>The Apache Software License, Version 2.0</td></tr>
<tr class="b">
<td><figure><img src="images/icon_success_sml.gif" alt="" /></figure>
<td><a href="#com.adobe.xmp:xmpcore:6.1.11">com.adobe.xmp:xmpcore:6.1.11</a></td>
<td>compile</td>
<td></td>
<td>bundle</td>
<td>The BSD 3-Clause License (BSD3)</td></tr>
<tr class="a">
<td><figure><img src="images/icon_success_sml.gif" alt="" /></figure>
<td><a href="#com.azure:azure-core:1.46.0">com.azure:azure-core:1.46.0</a></td>
<td>compile</td>
<td></td>
<td>jar</td>
<td>The MIT License (MIT)</td></tr>
<tr class="b">
<td><figure><img src="images/icon_success_sml.gif" alt="" /></figure>
<td><a href="#com.azure:azure-core-http-netty:1.14.1">com.azure:azure-core-http-netty:1.14.1</a></td>
<td>compile</td>
<td></td>
<td>jar</td>
<td>The MIT License (MIT)</td></tr>
<tr class="a">
<td><figure><img src="images/icon_success_sml.gif" alt="" /></figure>
<td><a href="#com.azure:azure-identity:1.11.4">com.azure:azure-identity:1.11.4</a></td>
<td>compile</td>
<td></td>
<td>jar</td>
<td>The MIT License (MIT)</td></tr>
<tr class="b">
<td><figure><img src="images/icon_success_sml.gif" alt="" /></figure>
<td><a href="#com.azure:azure-json:1.1.0">com.azure:azure-json:1.1.0</a></td>
<td>compile</td>
<td></td>
<td>jar</td>
<td>The MIT License (MIT)</td></tr>
<tr class="a">
<td><figure><img src="images/icon_success_sml.gif" alt="" /></figure>
<td><a href="#com.drewnoakes:metadata-extractor:2.18.0">com.drewnoakes:metadata-extractor:2.18.0</a></td>
<td>compile</td>
<td></td>
<td>jar</td>
<td>The Apache Software License, Version 2.0</td></tr>
<tr class="b">
<td><figure><img src="images/icon_success_sml.gif" alt="" /></figure>
<td><a href="#com.epam:parso:2.0.14">com.epam:parso:2.0.14</a></td>
<td>compile</td>
<td></td>
<td>jar</td>
<td>Apache License v2</td></tr>
<tr class="a">
<td><figure><img src="images/icon_success_sml.gif" alt="" /></figure>
<td><a href="#com.fasterxml:classmate:1.7.0">com.fasterxml:classmate:1.7.0</a></td>
<td>compile</td>
<td></td>
<td>bundle</td>
<td>Apache License, Version 2.0</td></tr>
<tr class="b">
<td><figure><img src="images/icon_success_sml.gif" alt="" /></figure>
<td><a href="#com.fasterxml.jackson.core:jackson-annotations:2.19.0">com.fasterxml.jackson.core:jackson-annotations:2.19.0</a></td>
<td>compile</td>
<td></td>
<td>jar</td>
<td>The Apache Software License, Version 2.0</td></tr>
<tr class="a">
<td><figure><img src="images/icon_success_sml.gif" alt="" /></figure>
<td><a href="#com.fasterxml.jackson.core:jackson-core:2.19.0">com.fasterxml.jackson.core:jackson-core:2.19.0</a></td>
<td>compile</td>
<td></td>
<td>jar</td>
<td>The Apache Software License, Version 2.0</td></tr>
<tr class="b">
<td><figure><img src="images/icon_success_sml.gif" alt="" /></figure>
<td><a href="#com.fasterxml.jackson.core:jackson-databind:2.19.0">com.fasterxml.jackson.core:jackson-databind:2.19.0</a></td>
<td>compile</td>
<td></td>
<td>jar</td>
<td>The Apache Software License, Version 2.0</td></tr>
<tr class="a">
<td><figure><img src="images/icon_success_sml.gif" alt="" /></figure>
<td><a href="#com.fasterxml.jackson.dataformat:jackson-dataformat-yaml:2.19.0">com.fasterxml.jackson.dataformat:jackson-dataformat-yaml:2.19.0</a></td>
<td>runtime</td>
<td></td>
<td>jar</td>
<td>The Apache Software License, Version 2.0</td></tr>
<tr class="b">
<td><figure><img src="images/icon_success_sml.gif" alt="" /></figure>
<td><a href="#com.fasterxml.jackson.datatype:jackson-datatype-jdk8:2.19.0">com.fasterxml.jackson.datatype:jackson-datatype-jdk8:2.19.0</a></td>
<td>compile</td>
<td></td>
<td>bundle</td>
<td>The Apache Software License, Version 2.0</td></tr>
<tr class="a">
<td><figure><img src="images/icon_success_sml.gif" alt="" /></figure>
<td><a href="#com.fasterxml.jackson.datatype:jackson-datatype-jsr310:2.19.0">com.fasterxml.jackson.datatype:jackson-datatype-jsr310:2.19.0</a></td>
<td>compile</td>
<td></td>
<td>bundle</td>
<td>The Apache Software License, Version 2.0</td></tr>
<tr class="b">
<td><figure><img src="images/icon_success_sml.gif" alt="" /></figure>
<td><a href="#com.fasterxml.jackson.module:jackson-module-parameter-names:2.19.0">com.fasterxml.jackson.module:jackson-module-parameter-names:2.19.0</a></td>
<td>compile</td>
<td></td>
<td>bundle</td>
<td>The Apache Software License, Version 2.0</td></tr>
<tr class="a">
<td><figure><img src="images/icon_success_sml.gif" alt="" /></figure>
<td><a href="#com.github.albfernandez:juniversalchardet:2.4.0">com.github.albfernandez:juniversalchardet:2.4.0</a></td>
<td>compile</td>
<td></td>
<td>jar</td>
<td>GENERAL PUBLIC LICENSE, version 3 (GPL-3.0)<br />GNU LESSER GENERAL PUBLIC LICENSE, version 3 (LGPL-3.0)<br />Mozilla Public License Version 1.1</td></tr>
<tr class="b">
<td><figure><img src="images/icon_success_sml.gif" alt="" /></figure>
<td><a href="#com.github.jai-imageio:jai-imageio-core:1.4.0">com.github.jai-imageio:jai-imageio-core:1.4.0</a></td>
<td>compile</td>
<td></td>
<td>bundle</td>
<td>BSD 3-clause License w/nuclear disclaimer</td></tr>
<tr class="a">
<td><figure><img src="images/icon_success_sml.gif" alt="" /></figure>
<td><a href="#com.github.junrar:junrar:7.5.5">com.github.junrar:junrar:7.5.5</a></td>
<td>compile</td>
<td></td>
<td>jar</td>
<td>UnRar License</td></tr>
<tr class="b">
<td><figure><img src="images/icon_success_sml.gif" alt="" /></figure>
<td><a href="#com.github.stephenc.jcip:jcip-annotations:1.0-1">com.github.stephenc.jcip:jcip-annotations:1.0-1</a></td>
<td>compile</td>
<td></td>
<td>jar</td>
<td>Apache License, Version 2.0</td></tr>
<tr class="a">
<td><figure><img src="images/icon_success_sml.gif" alt="" /></figure>
<td><a href="#com.github.virtuald:curvesapi:1.08">com.github.virtuald:curvesapi:1.08</a></td>
<td>compile</td>
<td></td>
<td>jar</td>
<td>BSD License</td></tr>
<tr class="b">
<td><figure><img src="images/icon_success_sml.gif" alt="" /></figure>
<td><a href="#com.google.cloud.opentelemetry:detector-resources-support:0.33.0">com.google.cloud.opentelemetry:detector-resources-support:0.33.0</a></td>
<td>runtime</td>
<td></td>
<td>jar</td>
<td>The Apache License, Version 2.0</td></tr>
<tr class="a">
<td><figure><img src="images/icon_success_sml.gif" alt="" /></figure>
<td><a href="#com.google.code.findbugs:jsr305:3.0.2">com.google.code.findbugs:jsr305:3.0.2</a></td>
<td>compile</td>
<td></td>
<td>jar</td>
<td>The Apache Software License, Version 2.0</td></tr>
<tr class="b">
<td><figure><img src="images/icon_success_sml.gif" alt="" /></figure>
<td><a href="#com.google.code.gson:gson:2.13.1">com.google.code.gson:gson:2.13.1</a></td>
<td>runtime</td>
<td></td>
<td>jar</td>
<td>Apache-2.0</td></tr>
<tr class="a">
<td><figure><img src="images/icon_success_sml.gif" alt="" /></figure>
<td><a href="#com.google.errorprone:error_prone_annotations:2.11.0">com.google.errorprone:error_prone_annotations:2.11.0</a></td>
<td>runtime</td>
<td></td>
<td>jar</td>
<td>Apache 2.0</td></tr>
<tr class="b">
<td><figure><img src="images/icon_success_sml.gif" alt="" /></figure>
<td><a href="#com.googlecode.plist:dd-plist:1.27">com.googlecode.plist:dd-plist:1.27</a></td>
<td>compile</td>
<td></td>
<td>jar</td>
<td>MIT License</td></tr>
<tr class="a">
<td><figure><img src="images/icon_success_sml.gif" alt="" /></figure>
<td><a href="#com.graphql-java:graphql-java:24.0">com.graphql-java:graphql-java:24.0</a></td>
<td>compile</td>
<td></td>
<td>jar</td>
<td>MIT</td></tr>
<tr class="b">
<td><figure><img src="images/icon_success_sml.gif" alt="" /></figure>
<td><a href="#com.graphql-java:graphql-java-extended-scalars:22.0">com.graphql-java:graphql-java-extended-scalars:22.0</a></td>
<td>compile</td>
<td></td>
<td>jar</td>
<td>MIT</td></tr>
<tr class="a">
<td><figure><img src="images/icon_success_sml.gif" alt="" /></figure>
<td><a href="#com.graphql-java:java-dataloader:5.0.0">com.graphql-java:java-dataloader:5.0.0</a></td>
<td>compile</td>
<td></td>
<td>jar</td>
<td>The Apache Software License, Version 2.0</td></tr>
<tr class="b">
<td><figure><img src="images/icon_success_sml.gif" alt="" /></figure>
<td><a href="#com.healthmarketscience.jackcess:jackcess:4.0.5">com.healthmarketscience.jackcess:jackcess:4.0.5</a></td>
<td>compile</td>
<td></td>
<td>jar</td>
<td>Apache License, Version 2.0</td></tr>
<tr class="a">
<td><figure><img src="images/icon_success_sml.gif" alt="" /></figure>
<td><a href="#com.healthmarketscience.jackcess:jackcess-encrypt:4.0.2">com.healthmarketscience.jackcess:jackcess-encrypt:4.0.2</a></td>
<td>compile</td>
<td></td>
<td>jar</td>
<td>Apache License, Version 2.0</td></tr>
<tr class="b">
<td><figure><img src="images/icon_success_sml.gif" alt="" /></figure>
<td><a href="#com.itextpdf:itextpdf:5.5.13.3">com.itextpdf:itextpdf:5.5.13.3</a></td>
<td>compile</td>
<td></td>
<td>jar</td>
<td>GNU Affero General Public License v3</td></tr>
<tr class="a">
<td><figure><img src="images/icon_success_sml.gif" alt="" /></figure>
<td><a href="#com.microsoft.azure:msal4j:1.14.3">com.microsoft.azure:msal4j:1.14.3</a></td>
<td>compile</td>
<td></td>
<td>jar</td>
<td>MIT License</td></tr>
<tr class="b">
<td><figure><img src="images/icon_success_sml.gif" alt="" /></figure>
<td><a href="#com.microsoft.azure:msal4j-persistence-extension:1.2.0">com.microsoft.azure:msal4j-persistence-extension:1.2.0</a></td>
<td>compile</td>
<td></td>
<td>jar</td>
<td>MIT License</td></tr>
<tr class="a">
<td><figure><img src="images/icon_success_sml.gif" alt="" /></figure>
<td><a href="#com.microsoft.graph:microsoft-graph:6.15.0">com.microsoft.graph:microsoft-graph:6.15.0</a></td>
<td>compile</td>
<td></td>
<td>jar</td>
<td>MIT License</td></tr>
<tr class="b">
<td><figure><img src="images/icon_success_sml.gif" alt="" /></figure>
<td><a href="#com.microsoft.graph:microsoft-graph-core:3.2.0">com.microsoft.graph:microsoft-graph-core:3.2.0</a></td>
<td>compile</td>
<td></td>
<td>jar</td>
<td>MIT License</td></tr>
<tr class="a">
<td><figure><img src="images/icon_success_sml.gif" alt="" /></figure>
<td><a href="#com.microsoft.kiota:microsoft-kiota-abstractions:1.3.0">com.microsoft.kiota:microsoft-kiota-abstractions:1.3.0</a></td>
<td>compile</td>
<td></td>
<td>jar</td>
<td>MIT License</td></tr>
<tr class="b">
<td><figure><img src="images/icon_success_sml.gif" alt="" /></figure>
<td><a href="#com.microsoft.kiota:microsoft-kiota-authentication-azure:1.3.0">com.microsoft.kiota:microsoft-kiota-authentication-azure:1.3.0</a></td>
<td>compile</td>
<td></td>
<td>jar</td>
<td>MIT License</td></tr>
<tr class="a">
<td><figure><img src="images/icon_success_sml.gif" alt="" /></figure>
<td><a href="#com.microsoft.kiota:microsoft-kiota-http-okHttp:1.3.0">com.microsoft.kiota:microsoft-kiota-http-okHttp:1.3.0</a></td>
<td>runtime</td>
<td></td>
<td>jar</td>
<td>MIT License</td></tr>
<tr class="b">
<td><figure><img src="images/icon_success_sml.gif" alt="" /></figure>
<td><a href="#com.microsoft.kiota:microsoft-kiota-serialization-form:1.3.0">com.microsoft.kiota:microsoft-kiota-serialization-form:1.3.0</a></td>
<td>runtime</td>
<td></td>
<td>jar</td>
<td>MIT License</td></tr>
<tr class="a">
<td><figure><img src="images/icon_success_sml.gif" alt="" /></figure>
<td><a href="#com.microsoft.kiota:microsoft-kiota-serialization-json:1.3.0">com.microsoft.kiota:microsoft-kiota-serialization-json:1.3.0</a></td>
<td>runtime</td>
<td></td>
<td>jar</td>
<td>MIT License</td></tr>
<tr class="b">
<td><figure><img src="images/icon_success_sml.gif" alt="" /></figure>
<td><a href="#com.microsoft.kiota:microsoft-kiota-serialization-multipart:1.3.0">com.microsoft.kiota:microsoft-kiota-serialization-multipart:1.3.0</a></td>
<td>runtime</td>
<td></td>
<td>jar</td>
<td>MIT License</td></tr>
<tr class="a">
<td><figure><img src="images/icon_success_sml.gif" alt="" /></figure>
<td><a href="#com.microsoft.kiota:microsoft-kiota-serialization-text:1.3.0">com.microsoft.kiota:microsoft-kiota-serialization-text:1.3.0</a></td>
<td>runtime</td>
<td></td>
<td>jar</td>
<td>MIT License</td></tr>
<tr class="b">
<td><figure><img src="images/icon_success_sml.gif" alt="" /></figure>
<td><a href="#com.mysql:mysql-connector-j:9.2.0">com.mysql:mysql-connector-j:9.2.0</a></td>
<td>runtime</td>
<td></td>
<td>jar</td>
<td>The GNU General Public License, v2 with Universal FOSS Exception, v1.0</td></tr>
<tr class="a">
<td><figure><img src="images/icon_success_sml.gif" alt="" /></figure>
<td><a href="#com.nimbusds:content-type:2.3">com.nimbusds:content-type:2.3</a></td>
<td>compile</td>
<td></td>
<td>jar</td>
<td>The Apache Software License, Version 2.0</td></tr>
<tr class="b">
<td><figure><img src="images/icon_success_sml.gif" alt="" /></figure>
<td><a href="#com.nimbusds:lang-tag:1.7">com.nimbusds:lang-tag:1.7</a></td>
<td>compile</td>
<td></td>
<td>jar</td>
<td>The Apache Software License, Version 2.0</td></tr>
<tr class="a">
<td><figure><img src="images/icon_success_sml.gif" alt="" /></figure>
<td><a href="#com.nimbusds:nimbus-jose-jwt:9.37.3">com.nimbusds:nimbus-jose-jwt:9.37.3</a></td>
<td>compile</td>
<td></td>
<td>jar</td>
<td>The Apache Software License, Version 2.0</td></tr>
<tr class="b">
<td><figure><img src="images/icon_success_sml.gif" alt="" /></figure>
<td><a href="#com.nimbusds:oauth2-oidc-sdk:11.9.1">com.nimbusds:oauth2-oidc-sdk:11.9.1</a></td>
<td>compile</td>
<td></td>
<td>jar</td>
<td>Apache License, version 2.0</td></tr>
<tr class="a">
<td><figure><img src="images/icon_success_sml.gif" alt="" /></figure>
<td><a href="#com.opencsv:opencsv:5.9">com.opencsv:opencsv:5.9</a></td>
<td>compile</td>
<td></td>
<td>jar</td>
<td>Apache 2</td></tr>
<tr class="b">
<td><figure><img src="images/icon_success_sml.gif" alt="" /></figure>
<td><a href="#com.pff:java-libpst:0.9.3">com.pff:java-libpst:0.9.3</a></td>
<td>compile</td>
<td></td>
<td>jar</td>
<td>The Apache Software License, Version 2.0</td></tr>
<tr class="a">
<td><figure><img src="images/icon_success_sml.gif" alt="" /></figure>
<td><a href="#com.rometools:rome:2.1.0">com.rometools:rome:2.1.0</a></td>
<td>compile</td>
<td></td>
<td>jar</td>
<td>The Apache Software License, Version 2.0</td></tr>
<tr class="b">
<td><figure><img src="images/icon_success_sml.gif" alt="" /></figure>
<td><a href="#com.rometools:rome-utils:2.1.0">com.rometools:rome-utils:2.1.0</a></td>
<td>compile</td>
<td></td>
<td>jar</td>
<td>The Apache Software License, Version 2.0</td></tr>
<tr class="a">
<td><figure><img src="images/icon_success_sml.gif" alt="" /></figure>
<td><a href="#com.squareup.okhttp3:okhttp:4.12.0">com.squareup.okhttp3:okhttp:4.12.0</a></td>
<td>compile</td>
<td></td>
<td>jar</td>
<td>The Apache Software License, Version 2.0</td></tr>
<tr class="b">
<td><figure><img src="images/icon_success_sml.gif" alt="" /></figure>
<td><a href="#com.squareup.okio:okio:3.6.0">com.squareup.okio:okio:3.6.0</a></td>
<td>compile</td>
<td></td>
<td>jar</td>
<td>The Apache Software License, Version 2.0</td></tr>
<tr class="a">
<td><figure><img src="images/icon_success_sml.gif" alt="" /></figure>
<td><a href="#com.squareup.okio:okio-jvm:3.6.0">com.squareup.okio:okio-jvm:3.6.0</a></td>
<td>compile</td>
<td></td>
<td>jar</td>
<td>The Apache Software License, Version 2.0</td></tr>
<tr class="b">
<td><figure><img src="images/icon_success_sml.gif" alt="" /></figure>
<td><a href="#com.sun.istack:istack-commons-runtime:4.1.2">com.sun.istack:istack-commons-runtime:4.1.2</a></td>
<td>compile</td>
<td></td>
<td>jar</td>
<td>Eclipse Distribution License - v 1.0</td></tr>
<tr class="a">
<td><figure><img src="images/icon_success_sml.gif" alt="" /></figure>
<td><a href="#com.zaxxer:HikariCP:6.3.0">com.zaxxer:HikariCP:6.3.0</a></td>
<td>compile</td>
<td></td>
<td>bundle</td>
<td>The Apache Software License, Version 2.0</td></tr>
<tr class="b">
<td><figure><img src="images/icon_success_sml.gif" alt="" /></figure>
<td><a href="#com.zaxxer:SparseBitSet:1.3">com.zaxxer:SparseBitSet:1.3</a></td>
<td>compile</td>
<td></td>
<td>jar</td>
<td>The Apache Software License, Version 2.0</td></tr>
<tr class="a">
<td><figure><img src="images/icon_success_sml.gif" alt="" /></figure>
<td><a href="#commons-codec:commons-codec:1.18.0">commons-codec:commons-codec:1.18.0</a></td>
<td>compile</td>
<td></td>
<td>jar</td>
<td>Apache-2.0</td></tr>
<tr class="b">
<td><figure><img src="images/icon_success_sml.gif" alt="" /></figure>
<td><a href="#commons-io:commons-io:2.15.1">commons-io:commons-io:2.15.1</a></td>
<td>compile</td>
<td></td>
<td>jar</td>
<td>Apache-2.0</td></tr>
<tr class="a">
<td><figure><img src="images/icon_success_sml.gif" alt="" /></figure>
<td><a href="#commons-logging:commons-logging:1.2">commons-logging:commons-logging:1.2</a></td>
<td>compile</td>
<td></td>
<td>jar</td>
<td>The Apache Software License, Version 2.0</td></tr>
<tr class="b">
<td><figure><img src="images/icon_success_sml.gif" alt="" /></figure>
<td><a href="#io.github.std-uritemplate:std-uritemplate:1.0.5">io.github.std-uritemplate:std-uritemplate:1.0.5</a></td>
<td>runtime</td>
<td></td>
<td>jar</td>
<td>Apache License, Version 2.0</td></tr>
<tr class="a">
<td><figure><img src="images/icon_success_sml.gif" alt="" /></figure>
<td><a href="#io.jsonwebtoken:jjwt-api:0.12.3">io.jsonwebtoken:jjwt-api:0.12.3</a></td>
<td>compile</td>
<td></td>
<td>jar</td>
<td>Apache License, Version 2.0</td></tr>
<tr class="b">
<td><figure><img src="images/icon_success_sml.gif" alt="" /></figure>
<td><a href="#io.jsonwebtoken:jjwt-impl:0.12.3">io.jsonwebtoken:jjwt-impl:0.12.3</a></td>
<td>runtime</td>
<td></td>
<td>jar</td>
<td>Apache License, Version 2.0</td></tr>
<tr class="a">
<td><figure><img src="images/icon_success_sml.gif" alt="" /></figure>
<td><a href="#io.jsonwebtoken:jjwt-jackson:0.12.3">io.jsonwebtoken:jjwt-jackson:0.12.3</a></td>
<td>runtime</td>
<td></td>
<td>jar</td>
<td>Apache License, Version 2.0</td></tr>
<tr class="b">
<td><figure><img src="images/icon_success_sml.gif" alt="" /></figure>
<td><a href="#io.lettuce:lettuce-core:6.5.5.RELEASE">io.lettuce:lettuce-core:6.5.5.RELEASE</a></td>
<td>compile</td>
<td></td>
<td>jar</td>
<td>MIT</td></tr>
<tr class="a">
<td><figure><img src="images/icon_success_sml.gif" alt="" /></figure>
<td><a href="#io.micrometer:context-propagation:1.1.3">io.micrometer:context-propagation:1.1.3</a></td>
<td>compile</td>
<td></td>
<td>jar</td>
<td>The Apache Software License, Version 2.0</td></tr>
<tr class="b">
<td><figure><img src="images/icon_success_sml.gif" alt="" /></figure>
<td><a href="#io.micrometer:micrometer-commons:1.15.0">io.micrometer:micrometer-commons:1.15.0</a></td>
<td>compile</td>
<td></td>
<td>jar</td>
<td>The Apache Software License, Version 2.0</td></tr>
<tr class="a">
<td><figure><img src="images/icon_success_sml.gif" alt="" /></figure>
<td><a href="#io.micrometer:micrometer-core:1.15.0">io.micrometer:micrometer-core:1.15.0</a></td>
<td>compile</td>
<td></td>
<td>jar</td>
<td>The Apache Software License, Version 2.0</td></tr>
<tr class="b">
<td><figure><img src="images/icon_success_sml.gif" alt="" /></figure>
<td><a href="#io.micrometer:micrometer-jakarta9:1.15.0">io.micrometer:micrometer-jakarta9:1.15.0</a></td>
<td>compile</td>
<td></td>
<td>jar</td>
<td>The Apache Software License, Version 2.0</td></tr>
<tr class="a">
<td><figure><img src="images/icon_success_sml.gif" alt="" /></figure>
<td><a href="#io.micrometer:micrometer-observation:1.15.0">io.micrometer:micrometer-observation:1.15.0</a></td>
<td>compile</td>
<td></td>
<td>jar</td>
<td>The Apache Software License, Version 2.0</td></tr>
<tr class="b">
<td><figure><img src="images/icon_success_sml.gif" alt="" /></figure>
<td><a href="#io.micrometer:micrometer-registry-prometheus:1.15.0">io.micrometer:micrometer-registry-prometheus:1.15.0</a></td>
<td>compile</td>
<td></td>
<td>jar</td>
<td>The Apache Software License, Version 2.0</td></tr>
<tr class="a">
<td><figure><img src="images/icon_success_sml.gif" alt="" /></figure>
<td><a href="#io.micrometer:micrometer-tracing:1.5.0">io.micrometer:micrometer-tracing:1.5.0</a></td>
<td>compile</td>
<td></td>
<td>jar</td>
<td>The Apache Software License, Version 2.0</td></tr>
<tr class="b">
<td><figure><img src="images/icon_success_sml.gif" alt="" /></figure>
<td><a href="#io.micrometer:micrometer-tracing-bridge-brave:1.5.0">io.micrometer:micrometer-tracing-bridge-brave:1.5.0</a></td>
<td>compile</td>
<td></td>
<td>jar</td>
<td>The Apache Software License, Version 2.0</td></tr>
<tr class="a">
<td><figure><img src="images/icon_success_sml.gif" alt="" /></figure>
<td><a href="#io.netty:netty-buffer:4.1.121.Final">io.netty:netty-buffer:4.1.121.Final</a></td>
<td>compile</td>
<td></td>
<td>jar</td>
<td>Apache License, Version 2.0</td></tr>
<tr class="b">
<td><figure><img src="images/icon_success_sml.gif" alt="" /></figure>
<td><a href="#io.netty:netty-codec:4.1.121.Final">io.netty:netty-codec:4.1.121.Final</a></td>
<td>compile</td>
<td></td>
<td>jar</td>
<td>Apache License, Version 2.0</td></tr>
<tr class="a">
<td><figure><img src="images/icon_success_sml.gif" alt="" /></figure>
<td><a href="#io.netty:netty-codec-dns:4.1.121.Final">io.netty:netty-codec-dns:4.1.121.Final</a></td>
<td>compile</td>
<td></td>
<td>jar</td>
<td>Apache License, Version 2.0</td></tr>
<tr class="b">
<td><figure><img src="images/icon_success_sml.gif" alt="" /></figure>
<td><a href="#io.netty:netty-codec-http:4.1.121.Final">io.netty:netty-codec-http:4.1.121.Final</a></td>
<td>compile</td>
<td></td>
<td>jar</td>
<td>Apache License, Version 2.0</td></tr>
<tr class="a">
<td><figure><img src="images/icon_success_sml.gif" alt="" /></figure>
<td><a href="#io.netty:netty-codec-http2:4.1.121.Final">io.netty:netty-codec-http2:4.1.121.Final</a></td>
<td>compile</td>
<td></td>
<td>jar</td>
<td>Apache License, Version 2.0</td></tr>
<tr class="b">
<td><figure><img src="images/icon_success_sml.gif" alt="" /></figure>
<td><a href="#io.netty:netty-codec-socks:4.1.121.Final">io.netty:netty-codec-socks:4.1.121.Final</a></td>
<td>compile</td>
<td></td>
<td>jar</td>
<td>Apache License, Version 2.0</td></tr>
<tr class="a">
<td><figure><img src="images/icon_success_sml.gif" alt="" /></figure>
<td><a href="#io.netty:netty-common:4.1.121.Final">io.netty:netty-common:4.1.121.Final</a></td>
<td>compile</td>
<td></td>
<td>jar</td>
<td>Apache License, Version 2.0</td></tr>
<tr class="b">
<td><figure><img src="images/icon_success_sml.gif" alt="" /></figure>
<td><a href="#io.netty:netty-handler:4.1.121.Final">io.netty:netty-handler:4.1.121.Final</a></td>
<td>compile</td>
<td></td>
<td>jar</td>
<td>Apache License, Version 2.0</td></tr>
<tr class="a">
<td><figure><img src="images/icon_success_sml.gif" alt="" /></figure>
<td><a href="#io.netty:netty-handler-proxy:4.1.121.Final">io.netty:netty-handler-proxy:4.1.121.Final</a></td>
<td>compile</td>
<td></td>
<td>jar</td>
<td>Apache License, Version 2.0</td></tr>
<tr class="b">
<td><figure><img src="images/icon_success_sml.gif" alt="" /></figure>
<td><a href="#io.netty:netty-resolver:4.1.121.Final">io.netty:netty-resolver:4.1.121.Final</a></td>
<td>compile</td>
<td></td>
<td>jar</td>
<td>Apache License, Version 2.0</td></tr>
<tr class="a">
<td><figure><img src="images/icon_success_sml.gif" alt="" /></figure>
<td><a href="#io.netty:netty-resolver-dns:4.1.121.Final">io.netty:netty-resolver-dns:4.1.121.Final</a></td>
<td>compile</td>
<td></td>
<td>jar</td>
<td>Apache License, Version 2.0</td></tr>
<tr class="b">
<td><figure><img src="images/icon_success_sml.gif" alt="" /></figure>
<td><a href="#io.netty:netty-resolver-dns-classes-macos:4.1.121.Final">io.netty:netty-resolver-dns-classes-macos:4.1.121.Final</a></td>
<td>compile</td>
<td></td>
<td>jar</td>
<td>Apache License, Version 2.0</td></tr>
<tr class="a">
<td><figure><img src="images/icon_success_sml.gif" alt="" /></figure>
<td><a href="#io.netty:netty-resolver-dns-native-macos:4.1.121.Final">io.netty:netty-resolver-dns-native-macos:4.1.121.Final</a></td>
<td>compile</td>
<td></td>
<td>jar</td>
<td>Apache License, Version 2.0</td></tr>
<tr class="b">
<td><figure><img src="images/icon_success_sml.gif" alt="" /></figure>
<td><a href="#io.netty:netty-tcnative-boringssl-static:2.0.70.Final">io.netty:netty-tcnative-boringssl-static:2.0.70.Final</a></td>
<td>compile</td>
<td></td>
<td>jar</td>
<td>The Apache Software License, Version 2.0</td></tr>
<tr class="a">
<td><figure><img src="images/icon_success_sml.gif" alt="" /></figure>
<td><a href="#io.netty:netty-tcnative-classes:2.0.70.Final">io.netty:netty-tcnative-classes:2.0.70.Final</a></td>
<td>compile</td>
<td></td>
<td>jar</td>
<td>The Apache Software License, Version 2.0</td></tr>
<tr class="b">
<td><figure><img src="images/icon_success_sml.gif" alt="" /></figure>
<td><a href="#io.netty:netty-transport:4.1.121.Final">io.netty:netty-transport:4.1.121.Final</a></td>
<td>compile</td>
<td></td>
<td>jar</td>
<td>Apache License, Version 2.0</td></tr>
<tr class="a">
<td><figure><img src="images/icon_success_sml.gif" alt="" /></figure>
<td><a href="#io.netty:netty-transport-classes-epoll:4.1.121.Final">io.netty:netty-transport-classes-epoll:4.1.121.Final</a></td>
<td>compile</td>
<td></td>
<td>jar</td>
<td>Apache License, Version 2.0</td></tr>
<tr class="b">
<td><figure><img src="images/icon_success_sml.gif" alt="" /></figure>
<td><a href="#io.netty:netty-transport-classes-kqueue:4.1.121.Final">io.netty:netty-transport-classes-kqueue:4.1.121.Final</a></td>
<td>compile</td>
<td></td>
<td>jar</td>
<td>Apache License, Version 2.0</td></tr>
<tr class="a">
<td><figure><img src="images/icon_success_sml.gif" alt="" /></figure>
<td><a href="#io.netty:netty-transport-native-epoll:4.1.121.Final">io.netty:netty-transport-native-epoll:4.1.121.Final</a></td>
<td>compile</td>
<td></td>
<td>jar</td>
<td>Apache License, Version 2.0</td></tr>
<tr class="b">
<td><figure><img src="images/icon_success_sml.gif" alt="" /></figure>
<td><a href="#io.netty:netty-transport-native-kqueue:4.1.121.Final">io.netty:netty-transport-native-kqueue:4.1.121.Final</a></td>
<td>compile</td>
<td></td>
<td>jar</td>
<td>Apache License, Version 2.0</td></tr>
<tr class="a">
<td><figure><img src="images/icon_success_sml.gif" alt="" /></figure>
<td><a href="#io.netty:netty-transport-native-unix-common:4.1.121.Final">io.netty:netty-transport-native-unix-common:4.1.121.Final</a></td>
<td>compile</td>
<td></td>
<td>jar</td>
<td>Apache License, Version 2.0</td></tr>
<tr class="b">
<td><figure><img src="images/icon_success_sml.gif" alt="" /></figure>
<td><a href="#io.opentelemetry:opentelemetry-api:1.49.0">io.opentelemetry:opentelemetry-api:1.49.0</a></td>
<td>compile</td>
<td></td>
<td>jar</td>
<td>The Apache License, Version 2.0</td></tr>
<tr class="a">
<td><figure><img src="images/icon_success_sml.gif" alt="" /></figure>
<td><a href="#io.opentelemetry:opentelemetry-api-incubator:1.44.1-alpha">io.opentelemetry:opentelemetry-api-incubator:1.44.1-alpha</a></td>
<td>runtime</td>
<td></td>
<td>jar</td>
<td>The Apache License, Version 2.0</td></tr>
<tr class="b">
<td><figure><img src="images/icon_success_sml.gif" alt="" /></figure>
<td><a href="#io.opentelemetry:opentelemetry-context:1.49.0">io.opentelemetry:opentelemetry-context:1.49.0</a></td>
<td>compile</td>
<td></td>
<td>jar</td>
<td>The Apache License, Version 2.0</td></tr>
<tr class="a">
<td><figure><img src="images/icon_success_sml.gif" alt="" /></figure>
<td><a href="#io.opentelemetry:opentelemetry-exporter-common:1.49.0">io.opentelemetry:opentelemetry-exporter-common:1.49.0</a></td>
<td>runtime</td>
<td></td>
<td>jar</td>
<td>The Apache License, Version 2.0</td></tr>
<tr class="b">
<td><figure><img src="images/icon_success_sml.gif" alt="" /></figure>
<td><a href="#io.opentelemetry:opentelemetry-exporter-logging:1.49.0">io.opentelemetry:opentelemetry-exporter-logging:1.49.0</a></td>
<td>compile</td>
<td></td>
<td>jar</td>
<td>The Apache License, Version 2.0</td></tr>
<tr class="a">
<td><figure><img src="images/icon_success_sml.gif" alt="" /></figure>
<td><a href="#io.opentelemetry:opentelemetry-exporter-otlp:1.49.0">io.opentelemetry:opentelemetry-exporter-otlp:1.49.0</a></td>
<td>compile</td>
<td></td>
<td>jar</td>
<td>The Apache License, Version 2.0</td></tr>
<tr class="b">
<td><figure><img src="images/icon_success_sml.gif" alt="" /></figure>
<td><a href="#io.opentelemetry:opentelemetry-exporter-otlp-common:1.49.0">io.opentelemetry:opentelemetry-exporter-otlp-common:1.49.0</a></td>
<td>runtime</td>
<td></td>
<td>jar</td>
<td>The Apache License, Version 2.0</td></tr>
<tr class="a">
<td><figure><img src="images/icon_success_sml.gif" alt="" /></figure>
<td><a href="#io.opentelemetry:opentelemetry-exporter-sender-okhttp:1.49.0">io.opentelemetry:opentelemetry-exporter-sender-okhttp:1.49.0</a></td>
<td>runtime</td>
<td></td>
<td>jar</td>
<td>The Apache License, Version 2.0</td></tr>
<tr class="b">
<td><figure><img src="images/icon_success_sml.gif" alt="" /></figure>
<td><a href="#io.opentelemetry:opentelemetry-exporter-zipkin:1.44.1">io.opentelemetry:opentelemetry-exporter-zipkin:1.44.1</a></td>
<td>compile</td>
<td></td>
<td>jar</td>
<td>The Apache License, Version 2.0</td></tr>
<tr class="a">
<td><figure><img src="images/icon_success_sml.gif" alt="" /></figure>
<td><a href="#io.opentelemetry:opentelemetry-sdk:1.49.0">io.opentelemetry:opentelemetry-sdk:1.49.0</a></td>
<td>compile</td>
<td></td>
<td>jar</td>
<td>The Apache License, Version 2.0</td></tr>
<tr class="b">
<td><figure><img src="images/icon_success_sml.gif" alt="" /></figure>
<td><a href="#io.opentelemetry:opentelemetry-sdk-common:1.49.0">io.opentelemetry:opentelemetry-sdk-common:1.49.0</a></td>
<td>compile</td>
<td></td>
<td>jar</td>
<td>The Apache License, Version 2.0</td></tr>
<tr class="a">
<td><figure><img src="images/icon_success_sml.gif" alt="" /></figure>
<td><a href="#io.opentelemetry:opentelemetry-sdk-extension-autoconfigure:1.49.0">io.opentelemetry:opentelemetry-sdk-extension-autoconfigure:1.49.0</a></td>
<td>runtime</td>
<td></td>
<td>jar</td>
<td>The Apache License, Version 2.0</td></tr>
<tr class="b">
<td><figure><img src="images/icon_success_sml.gif" alt="" /></figure>
<td><a href="#io.opentelemetry:opentelemetry-sdk-extension-autoconfigure-spi:1.49.0">io.opentelemetry:opentelemetry-sdk-extension-autoconfigure-spi:1.49.0</a></td>
<td>compile</td>
<td></td>
<td>jar</td>
<td>The Apache License, Version 2.0</td></tr>
<tr class="a">
<td><figure><img src="images/icon_success_sml.gif" alt="" /></figure>
<td><a href="#io.opentelemetry:opentelemetry-sdk-extension-incubator:1.44.1-alpha">io.opentelemetry:opentelemetry-sdk-extension-incubator:1.44.1-alpha</a></td>
<td>runtime</td>
<td></td>
<td>jar</td>
<td>The Apache License, Version 2.0</td></tr>
<tr class="b">
<td><figure><img src="images/icon_success_sml.gif" alt="" /></figure>
<td><a href="#io.opentelemetry:opentelemetry-sdk-logs:1.49.0">io.opentelemetry:opentelemetry-sdk-logs:1.49.0</a></td>
<td>compile</td>
<td></td>
<td>jar</td>
<td>The Apache License, Version 2.0</td></tr>
<tr class="a">
<td><figure><img src="images/icon_success_sml.gif" alt="" /></figure>
<td><a href="#io.opentelemetry:opentelemetry-sdk-metrics:1.49.0">io.opentelemetry:opentelemetry-sdk-metrics:1.49.0</a></td>
<td>compile</td>
<td></td>
<td>jar</td>
<td>The Apache License, Version 2.0</td></tr>
<tr class="b">
<td><figure><img src="images/icon_success_sml.gif" alt="" /></figure>
<td><a href="#io.opentelemetry:opentelemetry-sdk-trace:1.49.0">io.opentelemetry:opentelemetry-sdk-trace:1.49.0</a></td>
<td>compile</td>
<td></td>
<td>jar</td>
<td>The Apache License, Version 2.0</td></tr>
<tr class="a">
<td><figure><img src="images/icon_success_sml.gif" alt="" /></figure>
<td><a href="#io.opentelemetry.contrib:opentelemetry-aws-resources:1.40.0-alpha">io.opentelemetry.contrib:opentelemetry-aws-resources:1.40.0-alpha</a></td>
<td>runtime</td>
<td></td>
<td>jar</td>
<td>The Apache License, Version 2.0</td></tr>
<tr class="b">
<td><figure><img src="images/icon_success_sml.gif" alt="" /></figure>
<td><a href="#io.opentelemetry.contrib:opentelemetry-baggage-processor:1.40.0-alpha">io.opentelemetry.contrib:opentelemetry-baggage-processor:1.40.0-alpha</a></td>
<td>runtime</td>
<td></td>
<td>jar</td>
<td>The Apache License, Version 2.0</td></tr>
<tr class="a">
<td><figure><img src="images/icon_success_sml.gif" alt="" /></figure>
<td><a href="#io.opentelemetry.contrib:opentelemetry-gcp-resources:1.40.0-alpha">io.opentelemetry.contrib:opentelemetry-gcp-resources:1.40.0-alpha</a></td>
<td>runtime</td>
<td></td>
<td>jar</td>
<td>The Apache License, Version 2.0</td></tr>
<tr class="b">
<td><figure><img src="images/icon_success_sml.gif" alt="" /></figure>
<td><a href="#io.opentelemetry.instrumentation:opentelemetry-instrumentation-annotations:2.10.0">io.opentelemetry.instrumentation:opentelemetry-instrumentation-annotations:2.10.0</a></td>
<td>compile</td>
<td></td>
<td>jar</td>
<td>The Apache License, Version 2.0</td></tr>
<tr class="a">
<td><figure><img src="images/icon_success_sml.gif" alt="" /></figure>
<td><a href="#io.opentelemetry.instrumentation:opentelemetry-instrumentation-annotations-support:2.10.0-alpha">io.opentelemetry.instrumentation:opentelemetry-instrumentation-annotations-support:2.10.0-alpha</a></td>
<td>runtime</td>
<td></td>
<td>jar</td>
<td>The Apache License, Version 2.0</td></tr>
<tr class="b">
<td><figure><img src="images/icon_success_sml.gif" alt="" /></figure>
<td><a href="#io.opentelemetry.instrumentation:opentelemetry-instrumentation-api:2.10.0">io.opentelemetry.instrumentation:opentelemetry-instrumentation-api:2.10.0</a></td>
<td>compile</td>
<td></td>
<td>jar</td>
<td>The Apache License, Version 2.0</td></tr>
<tr class="a">
<td><figure><img src="images/icon_success_sml.gif" alt="" /></figure>
<td><a href="#io.opentelemetry.instrumentation:opentelemetry-instrumentation-api-incubator:2.10.0-alpha">io.opentelemetry.instrumentation:opentelemetry-instrumentation-api-incubator:2.10.0-alpha</a></td>
<td>compile</td>
<td></td>
<td>jar</td>
<td>The Apache License, Version 2.0</td></tr>
<tr class="b">
<td><figure><img src="images/icon_success_sml.gif" alt="" /></figure>
<td><a href="#io.opentelemetry.instrumentation:opentelemetry-jdbc:2.10.0-alpha">io.opentelemetry.instrumentation:opentelemetry-jdbc:2.10.0-alpha</a></td>
<td>runtime</td>
<td></td>
<td>jar</td>
<td>The Apache License, Version 2.0</td></tr>
<tr class="a">
<td><figure><img src="images/icon_success_sml.gif" alt="" /></figure>
<td><a href="#io.opentelemetry.instrumentation:opentelemetry-kafka-clients-2.6:2.10.0-alpha">io.opentelemetry.instrumentation:opentelemetry-kafka-clients-2.6:2.10.0-alpha</a></td>
<td>runtime</td>
<td></td>
<td>jar</td>
<td>The Apache License, Version 2.0</td></tr>
<tr class="b">
<td><figure><img src="images/icon_success_sml.gif" alt="" /></figure>
<td><a href="#io.opentelemetry.instrumentation:opentelemetry-kafka-clients-common:2.10.0-alpha">io.opentelemetry.instrumentation:opentelemetry-kafka-clients-common:2.10.0-alpha</a></td>
<td>runtime</td>
<td></td>
<td>jar</td>
<td>The Apache License, Version 2.0</td></tr>
<tr class="a">
<td><figure><img src="images/icon_success_sml.gif" alt="" /></figure>
<td><a href="#io.opentelemetry.instrumentation:opentelemetry-log4j-appender-2.17:2.10.0-alpha">io.opentelemetry.instrumentation:opentelemetry-log4j-appender-2.17:2.10.0-alpha</a></td>
<td>runtime</td>
<td></td>
<td>jar</td>
<td>The Apache License, Version 2.0</td></tr>
<tr class="b">
<td><figure><img src="images/icon_success_sml.gif" alt="" /></figure>
<td><a href="#io.opentelemetry.instrumentation:opentelemetry-log4j-context-data-2.17-autoconfigure:2.10.0-alpha">io.opentelemetry.instrumentation:opentelemetry-log4j-context-data-2.17-autoconfigure:2.10.0-alpha</a></td>
<td>runtime</td>
<td></td>
<td>jar</td>
<td>The Apache License, Version 2.0</td></tr>
<tr class="a">
<td><figure><img src="images/icon_success_sml.gif" alt="" /></figure>
<td><a href="#io.opentelemetry.instrumentation:opentelemetry-logback-appender-1.0:2.10.0-alpha">io.opentelemetry.instrumentation:opentelemetry-logback-appender-1.0:2.10.0-alpha</a></td>
<td>runtime</td>
<td></td>
<td>jar</td>
<td>The Apache License, Version 2.0</td></tr>
<tr class="b">
<td><figure><img src="images/icon_success_sml.gif" alt="" /></figure>
<td><a href="#io.opentelemetry.instrumentation:opentelemetry-logback-mdc-1.0:2.10.0-alpha">io.opentelemetry.instrumentation:opentelemetry-logback-mdc-1.0:2.10.0-alpha</a></td>
<td>runtime</td>
<td></td>
<td>jar</td>
<td>The Apache License, Version 2.0</td></tr>
<tr class="a">
<td><figure><img src="images/icon_success_sml.gif" alt="" /></figure>
<td><a href="#io.opentelemetry.instrumentation:opentelemetry-micrometer-1.5:2.10.0-alpha">io.opentelemetry.instrumentation:opentelemetry-micrometer-1.5:2.10.0-alpha</a></td>
<td>runtime</td>
<td></td>
<td>jar</td>
<td>The Apache License, Version 2.0</td></tr>
<tr class="b">
<td><figure><img src="images/icon_success_sml.gif" alt="" /></figure>
<td><a href="#io.opentelemetry.instrumentation:opentelemetry-mongo-3.1:2.10.0-alpha">io.opentelemetry.instrumentation:opentelemetry-mongo-3.1:2.10.0-alpha</a></td>
<td>runtime</td>
<td></td>
<td>jar</td>
<td>The Apache License, Version 2.0</td></tr>
<tr class="a">
<td><figure><img src="images/icon_success_sml.gif" alt="" /></figure>
<td><a href="#io.opentelemetry.instrumentation:opentelemetry-reactor-3.1:2.10.0-alpha">io.opentelemetry.instrumentation:opentelemetry-reactor-3.1:2.10.0-alpha</a></td>
<td>runtime</td>
<td></td>
<td>jar</td>
<td>The Apache License, Version 2.0</td></tr>
<tr class="b">
<td><figure><img src="images/icon_success_sml.gif" alt="" /></figure>
<td><a href="#io.opentelemetry.instrumentation:opentelemetry-resources:2.10.0-alpha">io.opentelemetry.instrumentation:opentelemetry-resources:2.10.0-alpha</a></td>
<td>runtime</td>
<td></td>
<td>jar</td>
<td>The Apache License, Version 2.0</td></tr>
<tr class="a">
<td><figure><img src="images/icon_success_sml.gif" alt="" /></figure>
<td><a href="#io.opentelemetry.instrumentation:opentelemetry-sdk-autoconfigure-support:2.10.0-alpha">io.opentelemetry.instrumentation:opentelemetry-sdk-autoconfigure-support:2.10.0-alpha</a></td>
<td>runtime</td>
<td></td>
<td>jar</td>
<td>The Apache License, Version 2.0</td></tr>
<tr class="b">
<td><figure><img src="images/icon_success_sml.gif" alt="" /></figure>
<td><a href="#io.opentelemetry.instrumentation:opentelemetry-spring-boot-autoconfigure:2.10.0">io.opentelemetry.instrumentation:opentelemetry-spring-boot-autoconfigure:2.10.0</a></td>
<td>compile</td>
<td></td>
<td>jar</td>
<td>The Apache License, Version 2.0</td></tr>
<tr class="a">
<td><figure><img src="images/icon_success_sml.gif" alt="" /></figure>
<td><a href="#io.opentelemetry.instrumentation:opentelemetry-spring-boot-starter:2.10.0">io.opentelemetry.instrumentation:opentelemetry-spring-boot-starter:2.10.0</a></td>
<td>compile</td>
<td></td>
<td>jar</td>
<td>The Apache License, Version 2.0</td></tr>
<tr class="b">
<td><figure><img src="images/icon_success_sml.gif" alt="" /></figure>
<td><a href="#io.opentelemetry.instrumentation:opentelemetry-spring-kafka-2.7:2.10.0-alpha">io.opentelemetry.instrumentation:opentelemetry-spring-kafka-2.7:2.10.0-alpha</a></td>
<td>runtime</td>
<td></td>
<td>jar</td>
<td>The Apache License, Version 2.0</td></tr>
<tr class="a">
<td><figure><img src="images/icon_success_sml.gif" alt="" /></figure>
<td><a href="#io.opentelemetry.instrumentation:opentelemetry-spring-web-3.1:2.10.0-alpha">io.opentelemetry.instrumentation:opentelemetry-spring-web-3.1:2.10.0-alpha</a></td>
<td>runtime</td>
<td></td>
<td>jar</td>
<td>The Apache License, Version 2.0</td></tr>
<tr class="b">
<td><figure><img src="images/icon_success_sml.gif" alt="" /></figure>
<td><a href="#io.opentelemetry.instrumentation:opentelemetry-spring-webflux-5.3:2.10.0-alpha">io.opentelemetry.instrumentation:opentelemetry-spring-webflux-5.3:2.10.0-alpha</a></td>
<td>runtime</td>
<td></td>
<td>jar</td>
<td>The Apache License, Version 2.0</td></tr>
<tr class="a">
<td><figure><img src="images/icon_success_sml.gif" alt="" /></figure>
<td><a href="#io.opentelemetry.instrumentation:opentelemetry-spring-webmvc-5.3:2.10.0-alpha">io.opentelemetry.instrumentation:opentelemetry-spring-webmvc-5.3:2.10.0-alpha</a></td>
<td>runtime</td>
<td></td>
<td>jar</td>
<td>The Apache License, Version 2.0</td></tr>
<tr class="b">
<td><figure><img src="images/icon_success_sml.gif" alt="" /></figure>
<td><a href="#io.opentelemetry.instrumentation:opentelemetry-spring-webmvc-6.0:2.10.0-alpha">io.opentelemetry.instrumentation:opentelemetry-spring-webmvc-6.0:2.10.0-alpha</a></td>
<td>runtime</td>
<td></td>
<td>jar</td>
<td>The Apache License, Version 2.0</td></tr>
<tr class="a">
<td><figure><img src="images/icon_success_sml.gif" alt="" /></figure>
<td><a href="#io.opentelemetry.semconv:opentelemetry-semconv:1.28.0-alpha">io.opentelemetry.semconv:opentelemetry-semconv:1.28.0-alpha</a></td>
<td>compile</td>
<td></td>
<td>jar</td>
<td>The Apache License, Version 2.0</td></tr>
<tr class="b">
<td><figure><img src="images/icon_success_sml.gif" alt="" /></figure>
<td><a href="#io.opentelemetry.semconv:opentelemetry-semconv-incubating:1.28.0-alpha">io.opentelemetry.semconv:opentelemetry-semconv-incubating:1.28.0-alpha</a></td>
<td>runtime</td>
<td></td>
<td>jar</td>
<td>The Apache License, Version 2.0</td></tr>
<tr class="a">
<td><figure><img src="images/icon_success_sml.gif" alt="" /></figure>
<td><a href="#io.projectreactor:reactor-core:3.7.6">io.projectreactor:reactor-core:3.7.6</a></td>
<td>compile</td>
<td></td>
<td>jar</td>
<td>Apache License, Version 2.0</td></tr>
<tr class="b">
<td><figure><img src="images/icon_success_sml.gif" alt="" /></figure>
<td><a href="#io.projectreactor.netty:reactor-netty-core:1.2.6">io.projectreactor.netty:reactor-netty-core:1.2.6</a></td>
<td>compile</td>
<td></td>
<td>jar</td>
<td>The Apache Software License, Version 2.0</td></tr>
<tr class="a">
<td><figure><img src="images/icon_success_sml.gif" alt="" /></figure>
<td><a href="#io.projectreactor.netty:reactor-netty-http:1.2.6">io.projectreactor.netty:reactor-netty-http:1.2.6</a></td>
<td>compile</td>
<td></td>
<td>jar</td>
<td>The Apache Software License, Version 2.0</td></tr>
<tr class="b">
<td><figure><img src="images/icon_success_sml.gif" alt="" /></figure>
<td><a href="#io.prometheus:prometheus-metrics-config:1.3.6">io.prometheus:prometheus-metrics-config:1.3.6</a></td>
<td>compile</td>
<td></td>
<td>bundle</td>
<td>The Apache Software License, Version 2.0</td></tr>
<tr class="a">
<td><figure><img src="images/icon_success_sml.gif" alt="" /></figure>
<td><a href="#io.prometheus:prometheus-metrics-core:1.3.6">io.prometheus:prometheus-metrics-core:1.3.6</a></td>
<td>compile</td>
<td></td>
<td>bundle</td>
<td>The Apache Software License, Version 2.0</td></tr>
<tr class="b">
<td><figure><img src="images/icon_success_sml.gif" alt="" /></figure>
<td><a href="#io.prometheus:prometheus-metrics-exposition-formats:1.3.6">io.prometheus:prometheus-metrics-exposition-formats:1.3.6</a></td>
<td>runtime</td>
<td></td>
<td>bundle</td>
<td>The Apache Software License, Version 2.0</td></tr>
<tr class="a">
<td><figure><img src="images/icon_success_sml.gif" alt="" /></figure>
<td><a href="#io.prometheus:prometheus-metrics-exposition-textformats:1.3.6">io.prometheus:prometheus-metrics-exposition-textformats:1.3.6</a></td>
<td>runtime</td>
<td></td>
<td>bundle</td>
<td>The Apache Software License, Version 2.0</td></tr>
<tr class="b">
<td><figure><img src="images/icon_success_sml.gif" alt="" /></figure>
<td><a href="#io.prometheus:prometheus-metrics-model:1.3.6">io.prometheus:prometheus-metrics-model:1.3.6</a></td>
<td>compile</td>
<td></td>
<td>bundle</td>
<td>The Apache Software License, Version 2.0</td></tr>
<tr class="a">
<td><figure><img src="images/icon_success_sml.gif" alt="" /></figure>
<td><a href="#io.prometheus:prometheus-metrics-tracer-common:1.3.6">io.prometheus:prometheus-metrics-tracer-common:1.3.6</a></td>
<td>compile</td>
<td></td>
<td>bundle</td>
<td>The Apache Software License, Version 2.0</td></tr>
<tr class="b">
<td><figure><img src="images/icon_success_sml.gif" alt="" /></figure>
<td><a href="#io.smallrye:jandex:3.2.0">io.smallrye:jandex:3.2.0</a></td>
<td>runtime</td>
<td></td>
<td>bundle</td>
<td>Apache License, Version 2.0</td></tr>
<tr class="a">
<td><figure><img src="images/icon_success_sml.gif" alt="" /></figure>
<td><a href="#io.zipkin.aws:brave-propagation-aws:1.3.0">io.zipkin.aws:brave-propagation-aws:1.3.0</a></td>
<td>compile</td>
<td></td>
<td>jar</td>
<td>The Apache Software License, Version 2.0</td></tr>
<tr class="b">
<td><figure><img src="images/icon_success_sml.gif" alt="" /></figure>
<td><a href="#io.zipkin.brave:brave:6.1.0">io.zipkin.brave:brave:6.1.0</a></td>
<td>compile</td>
<td></td>
<td>jar</td>
<td>The Apache Software License, Version 2.0</td></tr>
<tr class="a">
<td><figure><img src="images/icon_success_sml.gif" alt="" /></figure>
<td><a href="#io.zipkin.brave:brave-context-slf4j:6.1.0">io.zipkin.brave:brave-context-slf4j:6.1.0</a></td>
<td>compile</td>
<td></td>
<td>jar</td>
<td>The Apache Software License, Version 2.0</td></tr>
<tr class="b">
<td><figure><img src="images/icon_success_sml.gif" alt="" /></figure>
<td><a href="#io.zipkin.brave:brave-instrumentation-http:6.1.0">io.zipkin.brave:brave-instrumentation-http:6.1.0</a></td>
<td>compile</td>
<td></td>
<td>jar</td>
<td>The Apache Software License, Version 2.0</td></tr>
<tr class="a">
<td><figure><img src="images/icon_success_sml.gif" alt="" /></figure>
<td><a href="#io.zipkin.contrib.brave-propagation-w3c:brave-propagation-tracecontext:0.2.0">io.zipkin.contrib.brave-propagation-w3c:brave-propagation-tracecontext:0.2.0</a></td>
<td>compile</td>
<td></td>
<td>jar</td>
<td>The Apache Software License, Version 2.0</td></tr>
<tr class="b">
<td><figure><img src="images/icon_success_sml.gif" alt="" /></figure>
<td><a href="#io.zipkin.reporter2:zipkin-reporter:3.5.0">io.zipkin.reporter2:zipkin-reporter:3.5.0</a></td>
<td>compile</td>
<td></td>
<td>jar</td>
<td>The Apache Software License, Version 2.0</td></tr>
<tr class="a">
<td><figure><img src="images/icon_success_sml.gif" alt="" /></figure>
<td><a href="#io.zipkin.reporter2:zipkin-reporter-brave:3.5.0">io.zipkin.reporter2:zipkin-reporter-brave:3.5.0</a></td>
<td>compile</td>
<td></td>
<td>jar</td>
<td>The Apache Software License, Version 2.0</td></tr>
<tr class="b">
<td><figure><img src="images/icon_success_sml.gif" alt="" /></figure>
<td><a href="#io.zipkin.reporter2:zipkin-sender-okhttp3:3.5.0">io.zipkin.reporter2:zipkin-sender-okhttp3:3.5.0</a></td>
<td>runtime</td>
<td></td>
<td>jar</td>
<td>The Apache Software License, Version 2.0</td></tr>
<tr class="a">
<td><figure><img src="images/icon_success_sml.gif" alt="" /></figure>
<td><a href="#io.zipkin.zipkin2:zipkin:2.27.1">io.zipkin.zipkin2:zipkin:2.27.1</a></td>
<td>compile</td>
<td></td>
<td>jar</td>
<td>The Apache Software License, Version 2.0</td></tr>
<tr class="b">
<td><figure><img src="images/icon_success_sml.gif" alt="" /></figure>
<td><a href="#jakarta.activation:jakarta.activation-api:2.1.3">jakarta.activation:jakarta.activation-api:2.1.3</a></td>
<td>compile</td>
<td></td>
<td>jar</td>
<td>EDL 1.0</td></tr>
<tr class="a">
<td><figure><img src="images/icon_success_sml.gif" alt="" /></figure>
<td><a href="#jakarta.annotation:jakarta.annotation-api:2.1.1">jakarta.annotation:jakarta.annotation-api:2.1.1</a></td>
<td>compile</td>
<td></td>
<td>jar</td>
<td>EPL 2.0<br />GPL2 w/ CPE</td></tr>
<tr class="b">
<td><figure><img src="images/icon_success_sml.gif" alt="" /></figure>
<td><a href="#jakarta.inject:jakarta.inject-api:2.0.1">jakarta.inject:jakarta.inject-api:2.0.1</a></td>
<td>runtime</td>
<td></td>
<td>jar</td>
<td>The Apache Software License, Version 2.0</td></tr>
<tr class="a">
<td><figure><img src="images/icon_success_sml.gif" alt="" /></figure>
<td><a href="#jakarta.json:jakarta.json-api:2.1.3">jakarta.json:jakarta.json-api:2.1.3</a></td>
<td>compile</td>
<td></td>
<td>jar</td>
<td>Eclipse Public License 2.0<br />GNU General Public License, version 2 with the GNU Classpath Exception</td></tr>
<tr class="b">
<td><figure><img src="images/icon_success_sml.gif" alt="" /></figure>
<td><a href="#jakarta.persistence:jakarta.persistence-api:3.1.0">jakarta.persistence:jakarta.persistence-api:3.1.0</a></td>
<td>compile</td>
<td></td>
<td>jar</td>
<td>Eclipse Distribution License v. 1.0<br />Eclipse Public License v. 2.0</td></tr>
<tr class="a">
<td><figure><img src="images/icon_success_sml.gif" alt="" /></figure>
<td><a href="#jakarta.transaction:jakarta.transaction-api:2.0.1">jakarta.transaction:jakarta.transaction-api:2.0.1</a></td>
<td>compile</td>
<td></td>
<td>jar</td>
<td>EPL 2.0<br />GPL2 w/ CPE</td></tr>
<tr class="b">
<td><figure><img src="images/icon_success_sml.gif" alt="" /></figure>
<td><a href="#jakarta.validation:jakarta.validation-api:3.0.2">jakarta.validation:jakarta.validation-api:3.0.2</a></td>
<td>compile</td>
<td></td>
<td>jar</td>
<td>Apache License 2.0</td></tr>
<tr class="a">
<td><figure><img src="images/icon_success_sml.gif" alt="" /></figure>
<td><a href="#jakarta.xml.bind:jakarta.xml.bind-api:4.0.2">jakarta.xml.bind:jakarta.xml.bind-api:4.0.2</a></td>
<td>compile</td>
<td></td>
<td>jar</td>
<td>Eclipse Distribution License - v 1.0</td></tr>
<tr class="b">
<td><figure><img src="images/icon_success_sml.gif" alt="" /></figure>
<td><a href="#javax.validation:validation-api:2.0.1.Final">javax.validation:validation-api:2.0.1.Final</a></td>
<td>runtime</td>
<td></td>
<td>jar</td>
<td>Apache License 2.0</td></tr>
<tr class="a">
<td><figure><img src="images/icon_success_sml.gif" alt="" /></figure>
<td><a href="#javax.xml.bind:jaxb-api:2.3.1">javax.xml.bind:jaxb-api:2.3.1</a></td>
<td>compile</td>
<td></td>
<td>jar</td>
<td>CDDL 1.1<br />GPL2 w/ CPE</td></tr>
<tr class="b">
<td><figure><img src="images/icon_success_sml.gif" alt="" /></figure>
<td><a href="#net.bytebuddy:byte-buddy:1.17.5">net.bytebuddy:byte-buddy:1.17.5</a></td>
<td>runtime</td>
<td></td>
<td>jar</td>
<td>Apache License, Version 2.0</td></tr>
<tr class="a">
<td><figure><img src="images/icon_success_sml.gif" alt="" /></figure>
<td><a href="#net.java.dev.jna:jna:5.13.0">net.java.dev.jna:jna:5.13.0</a></td>
<td>compile</td>
<td></td>
<td>jar</td>
<td>Apache-2.0<br />LGPL-2.1-or-later</td></tr>
<tr class="b">
<td><figure><img src="images/icon_success_sml.gif" alt="" /></figure>
<td><a href="#net.java.dev.jna:jna-platform:5.6.0">net.java.dev.jna:jna-platform:5.6.0</a></td>
<td>compile</td>
<td></td>
<td>jar</td>
<td>Apache License v2.0<br />LGPL, version 2.1</td></tr>
<tr class="a">
<td><figure><img src="images/icon_success_sml.gif" alt="" /></figure>
<td><a href="#net.logstash.logback:logstash-logback-encoder:7.4">net.logstash.logback:logstash-logback-encoder:7.4</a></td>
<td>compile</td>
<td></td>
<td>jar</td>
<td>Apache License, Version 2.0<br />MIT License</td></tr>
<tr class="b">
<td><figure><img src="images/icon_success_sml.gif" alt="" /></figure>
<td><a href="#net.minidev:accessors-smart:2.5.2">net.minidev:accessors-smart:2.5.2</a></td>
<td>compile</td>
<td></td>
<td>bundle</td>
<td>The Apache Software License, Version 2.0</td></tr>
<tr class="a">
<td><figure><img src="images/icon_success_sml.gif" alt="" /></figure>
<td><a href="#net.minidev:json-smart:2.5.2">net.minidev:json-smart:2.5.2</a></td>
<td>compile</td>
<td></td>
<td>bundle</td>
<td>The Apache Software License, Version 2.0</td></tr>
<tr class="b">
<td><figure><img src="images/icon_success_sml.gif" alt="" /></figure>
<td><a href="#org.antlr:antlr4-runtime:4.13.0">org.antlr:antlr4-runtime:4.13.0</a></td>
<td>compile</td>
<td></td>
<td>jar</td>
<td>BSD-3-Clause</td></tr>
<tr class="a">
<td><figure><img src="images/icon_success_sml.gif" alt="" /></figure>
<td><a href="#org.apache.commons:commons-collections4:4.4">org.apache.commons:commons-collections4:4.4</a></td>
<td>compile</td>
<td></td>
<td>jar</td>
<td>Apache License, Version 2.0</td></tr>
<tr class="b">
<td><figure><img src="images/icon_success_sml.gif" alt="" /></figure>
<td><a href="#org.apache.commons:commons-compress:1.25.0">org.apache.commons:commons-compress:1.25.0</a></td>
<td>compile</td>
<td></td>
<td>jar</td>
<td>Apache-2.0</td></tr>
<tr class="a">
<td><figure><img src="images/icon_success_sml.gif" alt="" /></figure>
<td><a href="#org.apache.commons:commons-csv:1.10.0">org.apache.commons:commons-csv:1.10.0</a></td>
<td>compile</td>
<td></td>
<td>jar</td>
<td>Apache-2.0</td></tr>
<tr class="b">
<td><figure><img src="images/icon_success_sml.gif" alt="" /></figure>
<td><a href="#org.apache.commons:commons-exec:1.3">org.apache.commons:commons-exec:1.3</a></td>
<td>compile</td>
<td></td>
<td>jar</td>
<td>Apache License, Version 2.0</td></tr>
<tr class="a">
<td><figure><img src="images/icon_success_sml.gif" alt="" /></figure>
<td><a href="#org.apache.commons:commons-lang3:3.17.0">org.apache.commons:commons-lang3:3.17.0</a></td>
<td>compile</td>
<td></td>
<td>jar</td>
<td>Apache-2.0</td></tr>
<tr class="b">
<td><figure><img src="images/icon_success_sml.gif" alt="" /></figure>
<td><a href="#org.apache.commons:commons-math3:3.6.1">org.apache.commons:commons-math3:3.6.1</a></td>
<td>compile</td>
<td></td>
<td>jar</td>
<td>Apache License, Version 2.0</td></tr>
<tr class="a">
<td><figure><img src="images/icon_success_sml.gif" alt="" /></figure>
<td><a href="#org.apache.commons:commons-text:1.13.0">org.apache.commons:commons-text:1.13.0</a></td>
<td>compile</td>
<td></td>
<td>jar</td>
<td>Apache-2.0</td></tr>
<tr class="b">
<td><figure><img src="images/icon_success_sml.gif" alt="" /></figure>
<td><a href="#org.apache.httpcomponents:httpasyncclient:4.1.5">org.apache.httpcomponents:httpasyncclient:4.1.5</a></td>
<td>compile</td>
<td></td>
<td>jar</td>
<td>Apache License, Version 2.0</td></tr>
<tr class="a">
<td><figure><img src="images/icon_success_sml.gif" alt="" /></figure>
<td><a href="#org.apache.httpcomponents:httpclient:4.5.13">org.apache.httpcomponents:httpclient:4.5.13</a></td>
<td>compile</td>
<td></td>
<td>jar</td>
<td>Apache License, Version 2.0</td></tr>
<tr class="b">
<td><figure><img src="images/icon_success_sml.gif" alt="" /></figure>
<td><a href="#org.apache.httpcomponents:httpcore:4.4.16">org.apache.httpcomponents:httpcore:4.4.16</a></td>
<td>compile</td>
<td></td>
<td>jar</td>
<td>Apache License, Version 2.0</td></tr>
<tr class="a">
<td><figure><img src="images/icon_success_sml.gif" alt="" /></figure>
<td><a href="#org.apache.httpcomponents:httpcore-nio:4.4.16">org.apache.httpcomponents:httpcore-nio:4.4.16</a></td>
<td>compile</td>
<td></td>
<td>jar</td>
<td>Apache License, Version 2.0</td></tr>
<tr class="b">
<td><figure><img src="images/icon_success_sml.gif" alt="" /></figure>
<td><a href="#org.apache.james:apache-mime4j-core:0.8.9">org.apache.james:apache-mime4j-core:0.8.9</a></td>
<td>compile</td>
<td></td>
<td>bundle</td>
<td>Apache License, Version 2.0</td></tr>
<tr class="a">
<td><figure><img src="images/icon_success_sml.gif" alt="" /></figure>
<td><a href="#org.apache.james:apache-mime4j-dom:0.8.9">org.apache.james:apache-mime4j-dom:0.8.9</a></td>
<td>compile</td>
<td></td>
<td>bundle</td>
<td>Apache License, Version 2.0</td></tr>
<tr class="b">
<td><figure><img src="images/icon_success_sml.gif" alt="" /></figure>
<td><a href="#org.apache.logging.log4j:log4j-api:2.24.3">org.apache.logging.log4j:log4j-api:2.24.3</a></td>
<td>compile</td>
<td></td>
<td>jar</td>
<td>Apache-2.0</td></tr>
<tr class="a">
<td><figure><img src="images/icon_success_sml.gif" alt="" /></figure>
<td><a href="#org.apache.logging.log4j:log4j-to-slf4j:2.24.3">org.apache.logging.log4j:log4j-to-slf4j:2.24.3</a></td>
<td>compile</td>
<td></td>
<td>jar</td>
<td>Apache-2.0</td></tr>
<tr class="b">
<td><figure><img src="images/icon_success_sml.gif" alt="" /></figure>
<td><a href="#org.apache.pdfbox:fontbox:3.0.1">org.apache.pdfbox:fontbox:3.0.1</a></td>
<td>compile</td>
<td></td>
<td>bundle</td>
<td>Apache-2.0</td></tr>
<tr class="a">
<td><figure><img src="images/icon_success_sml.gif" alt="" /></figure>
<td><a href="#org.apache.pdfbox:jbig2-imageio:3.0.4">org.apache.pdfbox:jbig2-imageio:3.0.4</a></td>
<td>compile</td>
<td></td>
<td>jar</td>
<td>Apache License, Version 2.0</td></tr>
<tr class="b">
<td><figure><img src="images/icon_success_sml.gif" alt="" /></figure>
<td><a href="#org.apache.pdfbox:jempbox:1.8.17">org.apache.pdfbox:jempbox:1.8.17</a></td>
<td>compile</td>
<td></td>
<td>bundle</td>
<td>Apache License, Version 2.0</td></tr>
<tr class="a">
<td><figure><img src="images/icon_success_sml.gif" alt="" /></figure>
<td><a href="#org.apache.pdfbox:pdfbox:3.0.1">org.apache.pdfbox:pdfbox:3.0.1</a></td>
<td>compile</td>
<td></td>
<td>bundle</td>
<td>Apache-2.0</td></tr>
<tr class="b">
<td><figure><img src="images/icon_success_sml.gif" alt="" /></figure>
<td><a href="#org.apache.pdfbox:pdfbox-io:3.0.1">org.apache.pdfbox:pdfbox-io:3.0.1</a></td>
<td>compile</td>
<td></td>
<td>bundle</td>
<td>Apache-2.0</td></tr>
<tr class="a">
<td><figure><img src="images/icon_success_sml.gif" alt="" /></figure>
<td><a href="#org.apache.pdfbox:pdfbox-tools:2.0.29">org.apache.pdfbox:pdfbox-tools:2.0.29</a></td>
<td>compile</td>
<td></td>
<td>jar</td>
<td>Apache License, Version 2.0</td></tr>
<tr class="b">
<td><figure><img src="images/icon_success_sml.gif" alt="" /></figure>
<td><a href="#org.apache.pdfbox:xmpbox:2.0.29">org.apache.pdfbox:xmpbox:2.0.29</a></td>
<td>compile</td>
<td></td>
<td>bundle</td>
<td>Apache License, Version 2.0</td></tr>
<tr class="a">
<td><figure><img src="images/icon_success_sml.gif" alt="" /></figure>
<td><a href="#org.apache.poi:poi:5.2.5">org.apache.poi:poi:5.2.5</a></td>
<td>compile</td>
<td></td>
<td>jar</td>
<td>Apache License, Version 2.0</td></tr>
<tr class="b">
<td><figure><img src="images/icon_success_sml.gif" alt="" /></figure>
<td><a href="#org.apache.poi:poi-ooxml:5.2.5">org.apache.poi:poi-ooxml:5.2.5</a></td>
<td>compile</td>
<td></td>
<td>jar</td>
<td>Apache License, Version 2.0</td></tr>
<tr class="a">
<td><figure><img src="images/icon_success_sml.gif" alt="" /></figure>
<td><a href="#org.apache.poi:poi-ooxml-lite:5.2.5">org.apache.poi:poi-ooxml-lite:5.2.5</a></td>
<td>compile</td>
<td></td>
<td>jar</td>
<td>Apache License, Version 2.0</td></tr>
<tr class="b">
<td><figure><img src="images/icon_success_sml.gif" alt="" /></figure>
<td><a href="#org.apache.poi:poi-scratchpad:5.2.5">org.apache.poi:poi-scratchpad:5.2.5</a></td>
<td>compile</td>
<td></td>
<td>jar</td>
<td>Apache License, Version 2.0</td></tr>
<tr class="a">
<td><figure><img src="images/icon_success_sml.gif" alt="" /></figure>
<td><a href="#org.apache.tika:tika-core:2.9.1">org.apache.tika:tika-core:2.9.1</a></td>
<td>compile</td>
<td></td>
<td>bundle</td>
<td>Apache-2.0</td></tr>
<tr class="b">
<td><figure><img src="images/icon_success_sml.gif" alt="" /></figure>
<td><a href="#org.apache.tika:tika-parser-apple-module:2.9.1">org.apache.tika:tika-parser-apple-module:2.9.1</a></td>
<td>compile</td>
<td></td>
<td>jar</td>
<td>Apache-2.0</td></tr>
<tr class="a">
<td><figure><img src="images/icon_success_sml.gif" alt="" /></figure>
<td><a href="#org.apache.tika:tika-parser-audiovideo-module:2.9.1">org.apache.tika:tika-parser-audiovideo-module:2.9.1</a></td>
<td>compile</td>
<td></td>
<td>jar</td>
<td>Apache-2.0</td></tr>
<tr class="b">
<td><figure><img src="images/icon_success_sml.gif" alt="" /></figure>
<td><a href="#org.apache.tika:tika-parser-cad-module:2.9.1">org.apache.tika:tika-parser-cad-module:2.9.1</a></td>
<td>compile</td>
<td></td>
<td>jar</td>
<td>Apache-2.0</td></tr>
<tr class="a">
<td><figure><img src="images/icon_success_sml.gif" alt="" /></figure>
<td><a href="#org.apache.tika:tika-parser-code-module:2.9.1">org.apache.tika:tika-parser-code-module:2.9.1</a></td>
<td>compile</td>
<td></td>
<td>jar</td>
<td>Apache-2.0</td></tr>
<tr class="b">
<td><figure><img src="images/icon_success_sml.gif" alt="" /></figure>
<td><a href="#org.apache.tika:tika-parser-crypto-module:2.9.1">org.apache.tika:tika-parser-crypto-module:2.9.1</a></td>
<td>compile</td>
<td></td>
<td>jar</td>
<td>Apache-2.0</td></tr>
<tr class="a">
<td><figure><img src="images/icon_success_sml.gif" alt="" /></figure>
<td><a href="#org.apache.tika:tika-parser-digest-commons:2.9.1">org.apache.tika:tika-parser-digest-commons:2.9.1</a></td>
<td>compile</td>
<td></td>
<td>jar</td>
<td>Apache-2.0</td></tr>
<tr class="b">
<td><figure><img src="images/icon_success_sml.gif" alt="" /></figure>
<td><a href="#org.apache.tika:tika-parser-font-module:2.9.1">org.apache.tika:tika-parser-font-module:2.9.1</a></td>
<td>compile</td>
<td></td>
<td>jar</td>
<td>Apache-2.0</td></tr>
<tr class="a">
<td><figure><img src="images/icon_success_sml.gif" alt="" /></figure>
<td><a href="#org.apache.tika:tika-parser-html-module:2.9.1">org.apache.tika:tika-parser-html-module:2.9.1</a></td>
<td>compile</td>
<td></td>
<td>jar</td>
<td>Apache-2.0</td></tr>
<tr class="b">
<td><figure><img src="images/icon_success_sml.gif" alt="" /></figure>
<td><a href="#org.apache.tika:tika-parser-image-module:2.9.1">org.apache.tika:tika-parser-image-module:2.9.1</a></td>
<td>compile</td>
<td></td>
<td>jar</td>
<td>Apache-2.0</td></tr>
<tr class="a">
<td><figure><img src="images/icon_success_sml.gif" alt="" /></figure>
<td><a href="#org.apache.tika:tika-parser-mail-commons:2.9.1">org.apache.tika:tika-parser-mail-commons:2.9.1</a></td>
<td>compile</td>
<td></td>
<td>jar</td>
<td>Apache-2.0</td></tr>
<tr class="b">
<td><figure><img src="images/icon_success_sml.gif" alt="" /></figure>
<td><a href="#org.apache.tika:tika-parser-mail-module:2.9.1">org.apache.tika:tika-parser-mail-module:2.9.1</a></td>
<td>compile</td>
<td></td>
<td>jar</td>
<td>Apache-2.0</td></tr>
<tr class="a">
<td><figure><img src="images/icon_success_sml.gif" alt="" /></figure>
<td><a href="#org.apache.tika:tika-parser-microsoft-module:2.9.1">org.apache.tika:tika-parser-microsoft-module:2.9.1</a></td>
<td>compile</td>
<td></td>
<td>jar</td>
<td>Apache-2.0</td></tr>
<tr class="b">
<td><figure><img src="images/icon_success_sml.gif" alt="" /></figure>
<td><a href="#org.apache.tika:tika-parser-miscoffice-module:2.9.1">org.apache.tika:tika-parser-miscoffice-module:2.9.1</a></td>
<td>compile</td>
<td></td>
<td>jar</td>
<td>Apache-2.0</td></tr>
<tr class="a">
<td><figure><img src="images/icon_success_sml.gif" alt="" /></figure>
<td><a href="#org.apache.tika:tika-parser-news-module:2.9.1">org.apache.tika:tika-parser-news-module:2.9.1</a></td>
<td>compile</td>
<td></td>
<td>jar</td>
<td>Apache-2.0</td></tr>
<tr class="b">
<td><figure><img src="images/icon_success_sml.gif" alt="" /></figure>
<td><a href="#org.apache.tika:tika-parser-ocr-module:2.9.1">org.apache.tika:tika-parser-ocr-module:2.9.1</a></td>
<td>compile</td>
<td></td>
<td>jar</td>
<td>Apache-2.0</td></tr>
<tr class="a">
<td><figure><img src="images/icon_success_sml.gif" alt="" /></figure>
<td><a href="#org.apache.tika:tika-parser-pdf-module:2.9.1">org.apache.tika:tika-parser-pdf-module:2.9.1</a></td>
<td>compile</td>
<td></td>
<td>jar</td>
<td>Apache-2.0</td></tr>
<tr class="b">
<td><figure><img src="images/icon_success_sml.gif" alt="" /></figure>
<td><a href="#org.apache.tika:tika-parser-pkg-module:2.9.1">org.apache.tika:tika-parser-pkg-module:2.9.1</a></td>
<td>compile</td>
<td></td>
<td>jar</td>
<td>Apache-2.0</td></tr>
<tr class="a">
<td><figure><img src="images/icon_success_sml.gif" alt="" /></figure>
<td><a href="#org.apache.tika:tika-parser-text-module:2.9.1">org.apache.tika:tika-parser-text-module:2.9.1</a></td>
<td>compile</td>
<td></td>
<td>jar</td>
<td>Apache-2.0</td></tr>
<tr class="b">
<td><figure><img src="images/icon_success_sml.gif" alt="" /></figure>
<td><a href="#org.apache.tika:tika-parser-webarchive-module:2.9.1">org.apache.tika:tika-parser-webarchive-module:2.9.1</a></td>
<td>compile</td>
<td></td>
<td>jar</td>
<td>Apache-2.0</td></tr>
<tr class="a">
<td><figure><img src="images/icon_success_sml.gif" alt="" /></figure>
<td><a href="#org.apache.tika:tika-parser-xml-module:2.9.1">org.apache.tika:tika-parser-xml-module:2.9.1</a></td>
<td>compile</td>
<td></td>
<td>jar</td>
<td>Apache-2.0</td></tr>
<tr class="b">
<td><figure><img src="images/icon_success_sml.gif" alt="" /></figure>
<td><a href="#org.apache.tika:tika-parser-xmp-commons:2.9.1">org.apache.tika:tika-parser-xmp-commons:2.9.1</a></td>
<td>compile</td>
<td></td>
<td>jar</td>
<td>Apache-2.0</td></tr>
<tr class="a">
<td><figure><img src="images/icon_success_sml.gif" alt="" /></figure>
<td><a href="#org.apache.tika:tika-parser-zip-commons:2.9.1">org.apache.tika:tika-parser-zip-commons:2.9.1</a></td>
<td>compile</td>
<td></td>
<td>jar</td>
<td>Apache-2.0</td></tr>
<tr class="b">
<td><figure><img src="images/icon_success_sml.gif" alt="" /></figure>
<td><a href="#org.apache.tika:tika-parsers-standard-package:2.9.1">org.apache.tika:tika-parsers-standard-package:2.9.1</a></td>
<td>compile</td>
<td></td>
<td>jar</td>
<td>Apache-2.0</td></tr>
<tr class="a">
<td><figure><img src="images/icon_success_sml.gif" alt="" /></figure>
<td><a href="#org.apache.tomcat.embed:tomcat-embed-core:10.1.41">org.apache.tomcat.embed:tomcat-embed-core:10.1.41</a></td>
<td>compile</td>
<td></td>
<td>jar</td>
<td>Apache License, Version 2.0</td></tr>
<tr class="b">
<td><figure><img src="images/icon_success_sml.gif" alt="" /></figure>
<td><a href="#org.apache.tomcat.embed:tomcat-embed-el:10.1.41">org.apache.tomcat.embed:tomcat-embed-el:10.1.41</a></td>
<td>compile</td>
<td></td>
<td>jar</td>
<td>Apache License, Version 2.0</td></tr>
<tr class="a">
<td><figure><img src="images/icon_success_sml.gif" alt="" /></figure>
<td><a href="#org.apache.tomcat.embed:tomcat-embed-websocket:10.1.41">org.apache.tomcat.embed:tomcat-embed-websocket:10.1.41</a></td>
<td>compile</td>
<td></td>
<td>jar</td>
<td>Apache License, Version 2.0</td></tr>
<tr class="b">
<td><figure><img src="images/icon_success_sml.gif" alt="" /></figure>
<td><a href="#org.apache.xmlbeans:xmlbeans:5.2.0">org.apache.xmlbeans:xmlbeans:5.2.0</a></td>
<td>compile</td>
<td></td>
<td>jar</td>
<td>The Apache Software License, Version 2.0</td></tr>
<tr class="a">
<td><figure><img src="images/icon_success_sml.gif" alt="" /></figure>
<td><a href="#org.apiguardian:apiguardian-api:1.1.2">org.apiguardian:apiguardian-api:1.1.2</a></td>
<td>compile</td>
<td></td>
<td>jar</td>
<td>The Apache License, Version 2.0</td></tr>
<tr class="b">
<td><figure><img src="images/icon_success_sml.gif" alt="" /></figure>
<td><a href="#org.aspectj:aspectjweaver:1.9.24">org.aspectj:aspectjweaver:1.9.24</a></td>
<td>compile</td>
<td></td>
<td>jar</td>
<td>Eclipse Public License - v 2.0</td></tr>
<tr class="a">
<td><figure><img src="images/icon_success_sml.gif" alt="" /></figure>
<td><a href="#org.bouncycastle:bcmail-jdk18on:1.76">org.bouncycastle:bcmail-jdk18on:1.76</a></td>
<td>compile</td>
<td></td>
<td>jar</td>
<td>Bouncy Castle Licence</td></tr>
<tr class="b">
<td><figure><img src="images/icon_success_sml.gif" alt="" /></figure>
<td><a href="#org.bouncycastle:bcpkix-jdk18on:1.76">org.bouncycastle:bcpkix-jdk18on:1.76</a></td>
<td>compile</td>
<td></td>
<td>jar</td>
<td>Bouncy Castle Licence</td></tr>
<tr class="a">
<td><figure><img src="images/icon_success_sml.gif" alt="" /></figure>
<td><a href="#org.bouncycastle:bcprov-jdk18on:1.76">org.bouncycastle:bcprov-jdk18on:1.76</a></td>
<td>compile</td>
<td></td>
<td>jar</td>
<td>Bouncy Castle Licence</td></tr>
<tr class="b">
<td><figure><img src="images/icon_success_sml.gif" alt="" /></figure>
<td><a href="#org.bouncycastle:bcutil-jdk18on:1.76">org.bouncycastle:bcutil-jdk18on:1.76</a></td>
<td>compile</td>
<td></td>
<td>jar</td>
<td>Bouncy Castle Licence</td></tr>
<tr class="a">
<td><figure><img src="images/icon_success_sml.gif" alt="" /></figure>
<td><a href="#org.brotli:dec:0.1.2">org.brotli:dec:0.1.2</a></td>
<td>compile</td>
<td></td>
<td>jar</td>
<td>MIT License</td></tr>
<tr class="b">
<td><figure><img src="images/icon_success_sml.gif" alt="" /></figure>
<td><a href="#org.ccil.cowan.tagsoup:tagsoup:1.2.1">org.ccil.cowan.tagsoup:tagsoup:1.2.1</a></td>
<td>compile</td>
<td></td>
<td>jar</td>
<td>Apache License 2.0</td></tr>
<tr class="a">
<td><figure><img src="images/icon_success_sml.gif" alt="" /></figure>
<td><a href="#org.codelibs:jhighlight:1.1.0">org.codelibs:jhighlight:1.1.0</a></td>
<td>compile</td>
<td></td>
<td>jar</td>
<td>CDDL, v1.0<br />LGPL, v2.1 or later</td></tr>
<tr class="b">
<td><figure><img src="images/icon_success_sml.gif" alt="" /></figure>
<td><a href="#org.eclipse.angus:angus-activation:2.0.2">org.eclipse.angus:angus-activation:2.0.2</a></td>
<td>runtime</td>
<td></td>
<td>jar</td>
<td>EDL 1.0</td></tr>
<tr class="a">
<td><figure><img src="images/icon_success_sml.gif" alt="" /></figure>
<td><a href="#org.eclipse.parsson:parsson:1.0.5">org.eclipse.parsson:parsson:1.0.5</a></td>
<td>compile</td>
<td></td>
<td>jar</td>
<td>Eclipse Public License 2.0<br />GNU General Public License, version 2 with the GNU Classpath Exception</td></tr>
<tr class="b">
<td><figure><img src="images/icon_success_sml.gif" alt="" /></figure>
<td><a href="#org.elasticsearch.client:elasticsearch-rest-client:8.18.1">org.elasticsearch.client:elasticsearch-rest-client:8.18.1</a></td>
<td>compile</td>
<td></td>
<td>jar</td>
<td>The Apache Software License, Version 2.0</td></tr>
<tr class="a">
<td><figure><img src="images/icon_success_sml.gif" alt="" /></figure>
<td><a href="#org.gagravarr:vorbis-java-core:0.8">org.gagravarr:vorbis-java-core:0.8</a></td>
<td>compile</td>
<td></td>
<td>jar</td>
<td>The Apache Software License, Version 2.0</td></tr>
<tr class="b">
<td><figure><img src="images/icon_success_sml.gif" alt="" /></figure>
<td><a href="#org.gagravarr:vorbis-java-tika:0.8">org.gagravarr:vorbis-java-tika:0.8</a></td>
<td>compile</td>
<td></td>
<td>jar</td>
<td>The Apache Software License, Version 2.0</td></tr>
<tr class="a">
<td><figure><img src="images/icon_success_sml.gif" alt="" /></figure>
<td><a href="#org.glassfish.jaxb:jaxb-core:4.0.5">org.glassfish.jaxb:jaxb-core:4.0.5</a></td>
<td>compile</td>
<td></td>
<td>jar</td>
<td>Eclipse Distribution License - v 1.0</td></tr>
<tr class="b">
<td><figure><img src="images/icon_success_sml.gif" alt="" /></figure>
<td><a href="#org.glassfish.jaxb:jaxb-runtime:4.0.5">org.glassfish.jaxb:jaxb-runtime:4.0.5</a></td>
<td>compile</td>
<td></td>
<td>jar</td>
<td>Eclipse Distribution License - v 1.0</td></tr>
<tr class="a">
<td><figure><img src="images/icon_success_sml.gif" alt="" /></figure>
<td><a href="#org.glassfish.jaxb:txw2:4.0.5">org.glassfish.jaxb:txw2:4.0.5</a></td>
<td>compile</td>
<td></td>
<td>jar</td>
<td>Eclipse Distribution License - v 1.0</td></tr>
<tr class="b">
<td><figure><img src="images/icon_success_sml.gif" alt="" /></figure>
<td><a href="#org.hdrhistogram:HdrHistogram:2.2.2">org.hdrhistogram:HdrHistogram:2.2.2</a></td>
<td>runtime</td>
<td></td>
<td>bundle</td>
<td>BSD-2-Clause<br />Public Domain, per Creative Commons CC0</td></tr>
<tr class="a">
<td><figure><img src="images/icon_success_sml.gif" alt="" /></figure>
<td><a href="#org.hibernate.common:hibernate-commons-annotations:7.0.3.Final">org.hibernate.common:hibernate-commons-annotations:7.0.3.Final</a></td>
<td>runtime</td>
<td></td>
<td>jar</td>
<td>Apache License Version 2.0</td></tr>
<tr class="b">
<td><figure><img src="images/icon_success_sml.gif" alt="" /></figure>
<td><a href="#org.hibernate.orm:hibernate-core:6.6.15.Final">org.hibernate.orm:hibernate-core:6.6.15.Final</a></td>
<td>compile</td>
<td></td>
<td>jar</td>
<td>GNU Library General Public License v2.1 or later</td></tr>
<tr class="a">
<td><figure><img src="images/icon_success_sml.gif" alt="" /></figure>
<td><a href="#org.hibernate.validator:hibernate-validator:8.0.2.Final">org.hibernate.validator:hibernate-validator:8.0.2.Final</a></td>
<td>compile</td>
<td></td>
<td>jar</td>
<td>Apache License 2.0</td></tr>
<tr class="b">
<td><figure><img src="images/icon_success_sml.gif" alt="" /></figure>
<td><a href="#org.jboss.logging:jboss-logging:3.6.1.Final">org.jboss.logging:jboss-logging:3.6.1.Final</a></td>
<td>compile</td>
<td></td>
<td>jar</td>
<td>Apache License 2.0</td></tr>
<tr class="a">
<td><figure><img src="images/icon_success_sml.gif" alt="" /></figure>
<td><a href="#org.jdom:jdom2:2.0.6.1">org.jdom:jdom2:2.0.6.1</a></td>
<td>compile</td>
<td></td>
<td>jar</td>
<td>Similar to Apache License but with the acknowledgment clause removed</td></tr>
<tr class="b">
<td><figure><img src="images/icon_success_sml.gif" alt="" /></figure>
<td><a href="#org.jetbrains:annotations:17.0.0">org.jetbrains:annotations:17.0.0</a></td>
<td>compile</td>
<td></td>
<td>jar</td>
<td>The Apache Software License, Version 2.0</td></tr>
<tr class="a">
<td><figure><img src="images/icon_success_sml.gif" alt="" /></figure>
<td><a href="#org.jetbrains.kotlin:kotlin-stdlib:1.9.25">org.jetbrains.kotlin:kotlin-stdlib:1.9.25</a></td>
<td>compile</td>
<td></td>
<td>jar</td>
<td>The Apache License, Version 2.0</td></tr>
<tr class="b">
<td><figure><img src="images/icon_success_sml.gif" alt="" /></figure>
<td><a href="#org.jetbrains.kotlin:kotlin-stdlib-common:1.9.25">org.jetbrains.kotlin:kotlin-stdlib-common:1.9.25</a></td>
<td>compile</td>
<td></td>
<td>jar</td>
<td>The Apache License, Version 2.0</td></tr>
<tr class="a">
<td><figure><img src="images/icon_success_sml.gif" alt="" /></figure>
<td><a href="#org.jetbrains.kotlin:kotlin-stdlib-jdk7:1.9.25">org.jetbrains.kotlin:kotlin-stdlib-jdk7:1.9.25</a></td>
<td>compile</td>
<td></td>
<td>jar</td>
<td>The Apache License, Version 2.0</td></tr>
<tr class="b">
<td><figure><img src="images/icon_success_sml.gif" alt="" /></figure>
<td><a href="#org.jetbrains.kotlin:kotlin-stdlib-jdk8:1.9.25">org.jetbrains.kotlin:kotlin-stdlib-jdk8:1.9.25</a></td>
<td>compile</td>
<td></td>
<td>jar</td>
<td>The Apache License, Version 2.0</td></tr>
<tr class="a">
<td><figure><img src="images/icon_success_sml.gif" alt="" /></figure>
<td><a href="#org.jspecify:jspecify:1.0.0">org.jspecify:jspecify:1.0.0</a></td>
<td>compile</td>
<td></td>
<td>jar</td>
<td>The Apache License, Version 2.0</td></tr>
<tr class="b">
<td><figure><img src="images/icon_success_sml.gif" alt="" /></figure>
<td><a href="#org.junit.jupiter:junit-jupiter:5.12.2">org.junit.jupiter:junit-jupiter:5.12.2</a></td>
<td>compile</td>
<td></td>
<td>jar</td>
<td>Eclipse Public License v2.0</td></tr>
<tr class="a">
<td><figure><img src="images/icon_success_sml.gif" alt="" /></figure>
<td><a href="#org.junit.jupiter:junit-jupiter-api:5.12.2">org.junit.jupiter:junit-jupiter-api:5.12.2</a></td>
<td>compile</td>
<td></td>
<td>jar</td>
<td>Eclipse Public License v2.0</td></tr>
<tr class="b">
<td><figure><img src="images/icon_success_sml.gif" alt="" /></figure>
<td><a href="#org.junit.jupiter:junit-jupiter-engine:5.12.2">org.junit.jupiter:junit-jupiter-engine:5.12.2</a></td>
<td>runtime</td>
<td></td>
<td>jar</td>
<td>Eclipse Public License v2.0</td></tr>
<tr class="a">
<td><figure><img src="images/icon_success_sml.gif" alt="" /></figure>
<td><a href="#org.junit.jupiter:junit-jupiter-params:5.12.2">org.junit.jupiter:junit-jupiter-params:5.12.2</a></td>
<td>compile</td>
<td></td>
<td>jar</td>
<td>Eclipse Public License v2.0</td></tr>
<tr class="b">
<td><figure><img src="images/icon_success_sml.gif" alt="" /></figure>
<td><a href="#org.junit.platform:junit-platform-commons:1.12.2">org.junit.platform:junit-platform-commons:1.12.2</a></td>
<td>compile</td>
<td></td>
<td>jar</td>
<td>Eclipse Public License v2.0</td></tr>
<tr class="a">
<td><figure><img src="images/icon_success_sml.gif" alt="" /></figure>
<td><a href="#org.junit.platform:junit-platform-engine:1.12.2">org.junit.platform:junit-platform-engine:1.12.2</a></td>
<td>runtime</td>
<td></td>
<td>jar</td>
<td>Eclipse Public License v2.0</td></tr>
<tr class="b">
<td><figure><img src="images/icon_success_sml.gif" alt="" /></figure>
<td><a href="#org.latencyutils:LatencyUtils:2.0.3">org.latencyutils:LatencyUtils:2.0.3</a></td>
<td>runtime</td>
<td></td>
<td>jar</td>
<td>Public Domain, per Creative Commons CC0</td></tr>
<tr class="a">
<td><figure><img src="images/icon_success_sml.gif" alt="" /></figure>
<td><a href="#org.liquibase:liquibase-core:4.31.1">org.liquibase:liquibase-core:4.31.1</a></td>
<td>compile</td>
<td></td>
<td>jar</td>
<td>Apache License, Version 2.0</td></tr>
<tr class="b">
<td><figure><img src="images/icon_success_sml.gif" alt="" /></figure>
<td><a href="#org.netpreserve:jwarc:0.28.3">org.netpreserve:jwarc:0.28.3</a></td>
<td>compile</td>
<td></td>
<td>jar</td>
<td>Apache License, Version 2.0</td></tr>
<tr class="a">
<td><figure><img src="images/icon_success_sml.gif" alt="" /></figure>
<td><a href="#org.opentest4j:opentest4j:1.3.0">org.opentest4j:opentest4j:1.3.0</a></td>
<td>compile</td>
<td></td>
<td>jar</td>
<td>The Apache License, Version 2.0</td></tr>
<tr class="b">
<td><figure><img src="images/icon_success_sml.gif" alt="" /></figure>
<td><a href="#org.ow2.asm:asm:9.4">org.ow2.asm:asm:9.4</a></td>
<td>compile</td>
<td></td>
<td>jar</td>
<td>BSD-3-Clause</td></tr>
<tr class="a">
<td><figure><img src="images/icon_success_sml.gif" alt="" /></figure>
<td><a href="#org.projectlombok:lombok:1.18.38">org.projectlombok:lombok:1.18.38</a></td>
<td>compile</td>
<td></td>
<td>jar</td>
<td>The MIT License</td></tr>
<tr class="b">
<td><figure><img src="images/icon_success_sml.gif" alt="" /></figure>
<td><a href="#org.reactivestreams:reactive-streams:1.0.4">org.reactivestreams:reactive-streams:1.0.4</a></td>
<td>compile</td>
<td></td>
<td>jar</td>
<td>MIT-0</td></tr>
<tr class="a">
<td><figure><img src="images/icon_success_sml.gif" alt="" /></figure>
<td><a href="#org.slf4j:jcl-over-slf4j:2.0.17">org.slf4j:jcl-over-slf4j:2.0.17</a></td>
<td>compile</td>
<td></td>
<td>jar</td>
<td>Apache-2.0</td></tr>
<tr class="b">
<td><figure><img src="images/icon_success_sml.gif" alt="" /></figure>
<td><a href="#org.slf4j:jul-to-slf4j:2.0.17">org.slf4j:jul-to-slf4j:2.0.17</a></td>
<td>compile</td>
<td></td>
<td>jar</td>
<td>MIT</td></tr>
<tr class="a">
<td><figure><img src="images/icon_success_sml.gif" alt="" /></figure>
<td><a href="#org.slf4j:slf4j-api:2.0.17">org.slf4j:slf4j-api:2.0.17</a></td>
<td>compile</td>
<td></td>
<td>jar</td>
<td>MIT</td></tr>
<tr class="b">
<td><figure><img src="images/icon_success_sml.gif" alt="" /></figure>
<td><a href="#org.snakeyaml:snakeyaml-engine:2.8">org.snakeyaml:snakeyaml-engine:2.8</a></td>
<td>runtime</td>
<td></td>
<td>bundle</td>
<td>Apache License, Version 2.0</td></tr>
<tr class="a">
<td><figure><img src="images/icon_success_sml.gif" alt="" /></figure>
<td><a href="#org.springframework:spring-aop:6.2.7">org.springframework:spring-aop:6.2.7</a></td>
<td>compile</td>
<td></td>
<td>jar</td>
<td>Apache License, Version 2.0</td></tr>
<tr class="b">
<td><figure><img src="images/icon_success_sml.gif" alt="" /></figure>
<td><a href="#org.springframework:spring-aspects:6.2.7">org.springframework:spring-aspects:6.2.7</a></td>
<td>compile</td>
<td></td>
<td>jar</td>
<td>Apache License, Version 2.0</td></tr>
<tr class="a">
<td><figure><img src="images/icon_success_sml.gif" alt="" /></figure>
<td><a href="#org.springframework:spring-beans:6.2.7">org.springframework:spring-beans:6.2.7</a></td>
<td>compile</td>
<td></td>
<td>jar</td>
<td>Apache License, Version 2.0</td></tr>
<tr class="b">
<td><figure><img src="images/icon_success_sml.gif" alt="" /></figure>
<td><a href="#org.springframework:spring-context:6.2.7">org.springframework:spring-context:6.2.7</a></td>
<td>compile</td>
<td></td>
<td>jar</td>
<td>Apache License, Version 2.0</td></tr>
<tr class="a">
<td><figure><img src="images/icon_success_sml.gif" alt="" /></figure>
<td><a href="#org.springframework:spring-context-support:6.2.7">org.springframework:spring-context-support:6.2.7</a></td>
<td>compile</td>
<td></td>
<td>jar</td>
<td>Apache License, Version 2.0</td></tr>
<tr class="b">
<td><figure><img src="images/icon_success_sml.gif" alt="" /></figure>
<td><a href="#org.springframework:spring-core:6.2.7">org.springframework:spring-core:6.2.7</a></td>
<td>compile</td>
<td></td>
<td>jar</td>
<td>Apache License, Version 2.0</td></tr>
<tr class="a">
<td><figure><img src="images/icon_success_sml.gif" alt="" /></figure>
<td><a href="#org.springframework:spring-expression:6.2.7">org.springframework:spring-expression:6.2.7</a></td>
<td>compile</td>
<td></td>
<td>jar</td>
<td>Apache License, Version 2.0</td></tr>
<tr class="b">
<td><figure><img src="images/icon_success_sml.gif" alt="" /></figure>
<td><a href="#org.springframework:spring-jcl:6.2.7">org.springframework:spring-jcl:6.2.7</a></td>
<td>compile</td>
<td></td>
<td>jar</td>
<td>Apache License, Version 2.0</td></tr>
<tr class="a">
<td><figure><img src="images/icon_success_sml.gif" alt="" /></figure>
<td><a href="#org.springframework:spring-jdbc:6.2.7">org.springframework:spring-jdbc:6.2.7</a></td>
<td>compile</td>
<td></td>
<td>jar</td>
<td>Apache License, Version 2.0</td></tr>
<tr class="b">
<td><figure><img src="images/icon_success_sml.gif" alt="" /></figure>
<td><a href="#org.springframework:spring-orm:6.2.7">org.springframework:spring-orm:6.2.7</a></td>
<td>compile</td>
<td></td>
<td>jar</td>
<td>Apache License, Version 2.0</td></tr>
<tr class="a">
<td><figure><img src="images/icon_success_sml.gif" alt="" /></figure>
<td><a href="#org.springframework:spring-oxm:6.2.7">org.springframework:spring-oxm:6.2.7</a></td>
<td>compile</td>
<td></td>
<td>jar</td>
<td>Apache License, Version 2.0</td></tr>
<tr class="b">
<td><figure><img src="images/icon_success_sml.gif" alt="" /></figure>
<td><a href="#org.springframework:spring-tx:6.2.7">org.springframework:spring-tx:6.2.7</a></td>
<td>compile</td>
<td></td>
<td>jar</td>
<td>Apache License, Version 2.0</td></tr>
<tr class="a">
<td><figure><img src="images/icon_success_sml.gif" alt="" /></figure>
<td><a href="#org.springframework:spring-web:6.2.7">org.springframework:spring-web:6.2.7</a></td>
<td>compile</td>
<td></td>
<td>jar</td>
<td>Apache License, Version 2.0</td></tr>
<tr class="b">
<td><figure><img src="images/icon_success_sml.gif" alt="" /></figure>
<td><a href="#org.springframework:spring-webmvc:6.2.7">org.springframework:spring-webmvc:6.2.7</a></td>
<td>compile</td>
<td></td>
<td>jar</td>
<td>Apache License, Version 2.0</td></tr>
<tr class="a">
<td><figure><img src="images/icon_success_sml.gif" alt="" /></figure>
<td><a href="#org.springframework.data:spring-data-commons:3.5.0">org.springframework.data:spring-data-commons:3.5.0</a></td>
<td>compile</td>
<td></td>
<td>jar</td>
<td>Apache License, Version 2.0</td></tr>
<tr class="b">
<td><figure><img src="images/icon_success_sml.gif" alt="" /></figure>
<td><a href="#org.springframework.data:spring-data-elasticsearch:5.5.0">org.springframework.data:spring-data-elasticsearch:5.5.0</a></td>
<td>compile</td>
<td></td>
<td>jar</td>
<td>Apache License, Version 2.0</td></tr>
<tr class="a">
<td><figure><img src="images/icon_success_sml.gif" alt="" /></figure>
<td><a href="#org.springframework.data:spring-data-jpa:3.5.0">org.springframework.data:spring-data-jpa:3.5.0</a></td>
<td>compile</td>
<td></td>
<td>jar</td>
<td>Apache License, Version 2.0</td></tr>
<tr class="b">
<td><figure><img src="images/icon_success_sml.gif" alt="" /></figure>
<td><a href="#org.springframework.data:spring-data-keyvalue:3.5.0">org.springframework.data:spring-data-keyvalue:3.5.0</a></td>
<td>compile</td>
<td></td>
<td>jar</td>
<td>Apache License, Version 2.0</td></tr>
<tr class="a">
<td><figure><img src="images/icon_success_sml.gif" alt="" /></figure>
<td><a href="#org.springframework.data:spring-data-redis:3.5.0">org.springframework.data:spring-data-redis:3.5.0</a></td>
<td>compile</td>
<td></td>
<td>jar</td>
<td>Apache License, Version 2.0</td></tr>
<tr class="b">
<td><figure><img src="images/icon_success_sml.gif" alt="" /></figure>
<td><a href="#org.springframework.graphql:spring-graphql:1.4.0">org.springframework.graphql:spring-graphql:1.4.0</a></td>
<td>compile</td>
<td></td>
<td>jar</td>
<td>Apache License, Version 2.0</td></tr>
<tr class="a">
<td><figure><img src="images/icon_success_sml.gif" alt="" /></figure>
<td><a href="#org.springframework.security:spring-security-config:6.5.0">org.springframework.security:spring-security-config:6.5.0</a></td>
<td>compile</td>
<td></td>
<td>jar</td>
<td>Apache License, Version 2.0</td></tr>
<tr class="b">
<td><figure><img src="images/icon_success_sml.gif" alt="" /></figure>
<td><a href="#org.springframework.security:spring-security-core:6.5.0">org.springframework.security:spring-security-core:6.5.0</a></td>
<td>compile</td>
<td></td>
<td>jar</td>
<td>Apache License, Version 2.0</td></tr>
<tr class="a">
<td><figure><img src="images/icon_success_sml.gif" alt="" /></figure>
<td><a href="#org.springframework.security:spring-security-crypto:6.5.0">org.springframework.security:spring-security-crypto:6.5.0</a></td>
<td>compile</td>
<td></td>
<td>jar</td>
<td>Apache License, Version 2.0</td></tr>
<tr class="b">
<td><figure><img src="images/icon_success_sml.gif" alt="" /></figure>
<td><a href="#org.springframework.security:spring-security-oauth2-client:6.5.0">org.springframework.security:spring-security-oauth2-client:6.5.0</a></td>
<td>compile</td>
<td></td>
<td>jar</td>
<td>Apache License, Version 2.0</td></tr>
<tr class="a">
<td><figure><img src="images/icon_success_sml.gif" alt="" /></figure>
<td><a href="#org.springframework.security:spring-security-oauth2-core:6.5.0">org.springframework.security:spring-security-oauth2-core:6.5.0</a></td>
<td>compile</td>
<td></td>
<td>jar</td>
<td>Apache License, Version 2.0</td></tr>
<tr class="b">
<td><figure><img src="images/icon_success_sml.gif" alt="" /></figure>
<td><a href="#org.springframework.security:spring-security-oauth2-jose:6.5.0">org.springframework.security:spring-security-oauth2-jose:6.5.0</a></td>
<td>compile</td>
<td></td>
<td>jar</td>
<td>Apache License, Version 2.0</td></tr>
<tr class="a">
<td><figure><img src="images/icon_success_sml.gif" alt="" /></figure>
<td><a href="#org.springframework.security:spring-security-web:6.5.0">org.springframework.security:spring-security-web:6.5.0</a></td>
<td>compile</td>
<td></td>
<td>jar</td>
<td>Apache License, Version 2.0</td></tr>
<tr class="b">
<td><figure><img src="images/icon_success_sml.gif" alt="" /></figure>
<td><a href="#org.tallison:jmatio:1.5">org.tallison:jmatio:1.5</a></td>
<td>compile</td>
<td></td>
<td>jar</td>
<td>BSD</td></tr>
<tr class="a">
<td><figure><img src="images/icon_success_sml.gif" alt="" /></figure>
<td><a href="#org.tukaani:xz:1.9">org.tukaani:xz:1.9</a></td>
<td>compile</td>
<td></td>
<td>jar</td>
<td>Public Domain</td></tr>
<tr class="b">
<td><figure><img src="images/icon_success_sml.gif" alt="" /></figure>
<td><a href="#org.yaml:snakeyaml:2.4">org.yaml:snakeyaml:2.4</a></td>
<td>compile</td>
<td></td>
<td>bundle</td>
<td>Apache License, Version 2.0</td></tr>
<tr class="a">
<td><figure><img src="images/icon_success_sml.gif" alt="" /></figure>
<td><a href="#software.amazon.awssdk:annotations:2.20.162">software.amazon.awssdk:annotations:2.20.162</a></td>
<td>compile</td>
<td></td>
<td>jar</td>
<td>Apache License, Version 2.0</td></tr>
<tr class="b">
<td><figure><img src="images/icon_success_sml.gif" alt="" /></figure>
<td><a href="#software.amazon.awssdk:apache-client:2.20.162">software.amazon.awssdk:apache-client:2.20.162</a></td>
<td>runtime</td>
<td></td>
<td>jar</td>
<td>Apache License, Version 2.0</td></tr>
<tr class="a">
<td><figure><img src="images/icon_success_sml.gif" alt="" /></figure>
<td><a href="#software.amazon.awssdk:arns:2.20.162">software.amazon.awssdk:arns:2.20.162</a></td>
<td>compile</td>
<td></td>
<td>jar</td>
<td>Apache License, Version 2.0</td></tr>
<tr class="b">
<td><figure><img src="images/icon_success_sml.gif" alt="" /></figure>
<td><a href="#software.amazon.awssdk:auth:2.20.162">software.amazon.awssdk:auth:2.20.162</a></td>
<td>compile</td>
<td></td>
<td>jar</td>
<td>Apache License, Version 2.0</td></tr>
<tr class="a">
<td><figure><img src="images/icon_success_sml.gif" alt="" /></figure>
<td><a href="#software.amazon.awssdk:aws-core:2.20.162">software.amazon.awssdk:aws-core:2.20.162</a></td>
<td>compile</td>
<td></td>
<td>jar</td>
<td>Apache License, Version 2.0</td></tr>
<tr class="b">
<td><figure><img src="images/icon_success_sml.gif" alt="" /></figure>
<td><a href="#software.amazon.awssdk:aws-query-protocol:2.20.162">software.amazon.awssdk:aws-query-protocol:2.20.162</a></td>
<td>compile</td>
<td></td>
<td>jar</td>
<td>Apache License, Version 2.0</td></tr>
<tr class="a">
<td><figure><img src="images/icon_success_sml.gif" alt="" /></figure>
<td><a href="#software.amazon.awssdk:aws-xml-protocol:2.20.162">software.amazon.awssdk:aws-xml-protocol:2.20.162</a></td>
<td>compile</td>
<td></td>
<td>jar</td>
<td>Apache License, Version 2.0</td></tr>
<tr class="b">
<td><figure><img src="images/icon_success_sml.gif" alt="" /></figure>
<td><a href="#software.amazon.awssdk:crt-core:2.20.162">software.amazon.awssdk:crt-core:2.20.162</a></td>
<td>compile</td>
<td></td>
<td>jar</td>
<td>Apache License, Version 2.0</td></tr>
<tr class="a">
<td><figure><img src="images/icon_success_sml.gif" alt="" /></figure>
<td><a href="#software.amazon.awssdk:endpoints-spi:2.20.162">software.amazon.awssdk:endpoints-spi:2.20.162</a></td>
<td>compile</td>
<td></td>
<td>jar</td>
<td>Apache License, Version 2.0</td></tr>
<tr class="b">
<td><figure><img src="images/icon_success_sml.gif" alt="" /></figure>
<td><a href="#software.amazon.awssdk:http-client-spi:2.20.162">software.amazon.awssdk:http-client-spi:2.20.162</a></td>
<td>compile</td>
<td></td>
<td>jar</td>
<td>Apache License, Version 2.0</td></tr>
<tr class="a">
<td><figure><img src="images/icon_success_sml.gif" alt="" /></figure>
<td><a href="#software.amazon.awssdk:json-utils:2.20.162">software.amazon.awssdk:json-utils:2.20.162</a></td>
<td>compile</td>
<td></td>
<td>jar</td>
<td>Apache License, Version 2.0</td></tr>
<tr class="b">
<td><figure><img src="images/icon_success_sml.gif" alt="" /></figure>
<td><a href="#software.amazon.awssdk:metrics-spi:2.20.162">software.amazon.awssdk:metrics-spi:2.20.162</a></td>
<td>compile</td>
<td></td>
<td>jar</td>
<td>Apache License, Version 2.0</td></tr>
<tr class="a">
<td><figure><img src="images/icon_success_sml.gif" alt="" /></figure>
<td><a href="#software.amazon.awssdk:netty-nio-client:2.20.162">software.amazon.awssdk:netty-nio-client:2.20.162</a></td>
<td>runtime</td>
<td></td>
<td>jar</td>
<td>Apache License, Version 2.0</td></tr>
<tr class="b">
<td><figure><img src="images/icon_success_sml.gif" alt="" /></figure>
<td><a href="#software.amazon.awssdk:profiles:2.20.162">software.amazon.awssdk:profiles:2.20.162</a></td>
<td>compile</td>
<td></td>
<td>jar</td>
<td>Apache License, Version 2.0</td></tr>
<tr class="a">
<td><figure><img src="images/icon_success_sml.gif" alt="" /></figure>
<td><a href="#software.amazon.awssdk:protocol-core:2.20.162">software.amazon.awssdk:protocol-core:2.20.162</a></td>
<td>compile</td>
<td></td>
<td>jar</td>
<td>Apache License, Version 2.0</td></tr>
<tr class="b">
<td><figure><img src="images/icon_success_sml.gif" alt="" /></figure>
<td><a href="#software.amazon.awssdk:regions:2.20.162">software.amazon.awssdk:regions:2.20.162</a></td>
<td>compile</td>
<td></td>
<td>jar</td>
<td>Apache License, Version 2.0</td></tr>
<tr class="a">
<td><figure><img src="images/icon_success_sml.gif" alt="" /></figure>
<td><a href="#software.amazon.awssdk:s3:2.20.162">software.amazon.awssdk:s3:2.20.162</a></td>
<td>compile</td>
<td></td>
<td>jar</td>
<td>Apache License, Version 2.0</td></tr>
<tr class="b">
<td><figure><img src="images/icon_success_sml.gif" alt="" /></figure>
<td><a href="#software.amazon.awssdk:sdk-core:2.20.162">software.amazon.awssdk:sdk-core:2.20.162</a></td>
<td>compile</td>
<td></td>
<td>jar</td>
<td>Apache License, Version 2.0</td></tr>
<tr class="a">
<td><figure><img src="images/icon_success_sml.gif" alt="" /></figure>
<td><a href="#software.amazon.awssdk:third-party-jackson-core:2.20.162">software.amazon.awssdk:third-party-jackson-core:2.20.162</a></td>
<td>compile</td>
<td></td>
<td>jar</td>
<td>Apache License, Version 2.0</td></tr>
<tr class="b">
<td><figure><img src="images/icon_success_sml.gif" alt="" /></figure>
<td><a href="#software.amazon.awssdk:utils:2.20.162">software.amazon.awssdk:utils:2.20.162</a></td>
<td>compile</td>
<td></td>
<td>jar</td>
<td>Apache License, Version 2.0</td></tr>
<tr class="a">
<td><figure><img src="images/icon_success_sml.gif" alt="" /></figure>
<td><a href="#software.amazon.eventstream:eventstream:1.0.1">software.amazon.eventstream:eventstream:1.0.1</a></td>
<td>compile</td>
<td></td>
<td>jar</td>
<td>Apache License, Version 2.0</td></tr>
<tr class="b">
<td><figure><img src="images/icon_success_sml.gif" alt="" /></figure>
<td><a href="#xerces:xercesImpl:2.12.2">xerces:xercesImpl:2.12.2</a></td>
<td>compile</td>
<td></td>
<td>jar</td>
<td>The Apache Software License, Version 2.0</td></tr>
<tr class="a">
<td><figure><img src="images/icon_success_sml.gif" alt="" /></figure>
<td><a href="#xml-apis:xml-apis:1.4.01">xml-apis:xml-apis:1.4.01</a></td>
<td>compile</td>
<td></td>
<td>jar</td>
<td>The Apache Software License, Version 2.0<br />The SAX License<br />The W3C License</td></tr>
<tr class="b">
<th>Status</th>
<th>GroupId:ArtifactId:Version</th>
<th>Scope</th>
<th>Classifier</th>
<th>Type</th>
<th>License(s)</th></tr></table></section></section><section>
<h2><a name="Third-parties_detail_with_no_license"></a>Third-parties detail with no license</h2>
<p>Listing of Third-parties with no license.<br />No such dependency.</p></section><section>
<h2><a name="Third-parties_details_with_license_from_Third-party_file"></a>Third-parties details with license from Third-party file</h2>
<p>Listing of Third-parties with no license in pom and filled in the third-party missing file.<br />No such dependency.</p></section><section>
<h2><a name="Third-parties_with_license_in_pom"></a>Third-parties with license in pom</h2>
<p>Listing of Third-parties with license defined in their pom.</p><section>
<h3><a name="aopalliance:aopalliance:1.0"></a>aopalliance:aopalliance:1.0</h3>
<table border="0" class="bodyTable">
<tr class="a">
<th width="20%">Status</th>
<td width="80%" align="left"><figure><img src="images/icon_success_sml.gif" alt="" /></figure>&#160;License defined in the pom of the dependency</td></tr>
<tr class="b">
<th width="20%">GroupId:ArtifactId:Version</th>
<td width="80%" align="left">aopalliance:aopalliance:1.0</td></tr>
<tr class="a">
<th width="20%">Scope</th>
<td width="80%" align="left">compile</td></tr>
<tr class="b">
<th width="20%">Classifier</th>
<td width="80%" align="left"></td></tr>
<tr class="a">
<th width="20%">Type</th>
<td width="80%" align="left">jar</td></tr>
<tr class="b">
<th width="20%">License(s)</th>
<td width="80%" align="left">Public Domain</td></tr></table><a href="#Overview">Back to top</a><br /></section><section>
<h3><a name="ch.qos.logback:logback-classic:1.5.18"></a>ch.qos.logback:logback-classic:1.5.18</h3>
<table border="0" class="bodyTable">
<tr class="a">
<th width="20%">Status</th>
<td width="80%" align="left"><figure><img src="images/icon_success_sml.gif" alt="" /></figure>&#160;License defined in the pom of the dependency</td></tr>
<tr class="b">
<th width="20%">GroupId:ArtifactId:Version</th>
<td width="80%" align="left">ch.qos.logback:logback-classic:1.5.18</td></tr>
<tr class="a">
<th width="20%">Scope</th>
<td width="80%" align="left">compile</td></tr>
<tr class="b">
<th width="20%">Classifier</th>
<td width="80%" align="left"></td></tr>
<tr class="a">
<th width="20%">Type</th>
<td width="80%" align="left">jar</td></tr>
<tr class="b">
<th width="20%">License(s)</th>
<td width="80%" align="left">Eclipse Public License - v 1.0<br />GNU Lesser General Public License</td></tr></table><a href="#Overview">Back to top</a><br /></section><section>
<h3><a name="ch.qos.logback:logback-core:1.5.18"></a>ch.qos.logback:logback-core:1.5.18</h3>
<table border="0" class="bodyTable">
<tr class="a">
<th width="20%">Status</th>
<td width="80%" align="left"><figure><img src="images/icon_success_sml.gif" alt="" /></figure>&#160;License defined in the pom of the dependency</td></tr>
<tr class="b">
<th width="20%">GroupId:ArtifactId:Version</th>
<td width="80%" align="left">ch.qos.logback:logback-core:1.5.18</td></tr>
<tr class="a">
<th width="20%">Scope</th>
<td width="80%" align="left">compile</td></tr>
<tr class="b">
<th width="20%">Classifier</th>
<td width="80%" align="left"></td></tr>
<tr class="a">
<th width="20%">Type</th>
<td width="80%" align="left">jar</td></tr>
<tr class="b">
<th width="20%">License(s)</th>
<td width="80%" align="left">Eclipse Public License - v 1.0<br />GNU Lesser General Public License</td></tr></table><a href="#Overview">Back to top</a><br /></section><section>
<h3><a name="co.elastic.clients:elasticsearch-java:8.18.1"></a>co.elastic.clients:elasticsearch-java:8.18.1</h3>
<table border="0" class="bodyTable">
<tr class="a">
<th width="20%">Status</th>
<td width="80%" align="left"><figure><img src="images/icon_success_sml.gif" alt="" /></figure>&#160;License defined in the pom of the dependency</td></tr>
<tr class="b">
<th width="20%">GroupId:ArtifactId:Version</th>
<td width="80%" align="left">co.elastic.clients:elasticsearch-java:8.18.1</td></tr>
<tr class="a">
<th width="20%">Scope</th>
<td width="80%" align="left">compile</td></tr>
<tr class="b">
<th width="20%">Classifier</th>
<td width="80%" align="left"></td></tr>
<tr class="a">
<th width="20%">Type</th>
<td width="80%" align="left">jar</td></tr>
<tr class="b">
<th width="20%">License(s)</th>
<td width="80%" align="left">The Apache Software License, Version 2.0</td></tr></table><a href="#Overview">Back to top</a><br /></section><section>
<h3><a name="com.adobe.xmp:xmpcore:6.1.11"></a>com.adobe.xmp:xmpcore:6.1.11</h3>
<table border="0" class="bodyTable">
<tr class="a">
<th width="20%">Status</th>
<td width="80%" align="left"><figure><img src="images/icon_success_sml.gif" alt="" /></figure>&#160;License defined in the pom of the dependency</td></tr>
<tr class="b">
<th width="20%">GroupId:ArtifactId:Version</th>
<td width="80%" align="left">com.adobe.xmp:xmpcore:6.1.11</td></tr>
<tr class="a">
<th width="20%">Scope</th>
<td width="80%" align="left">compile</td></tr>
<tr class="b">
<th width="20%">Classifier</th>
<td width="80%" align="left"></td></tr>
<tr class="a">
<th width="20%">Type</th>
<td width="80%" align="left">bundle</td></tr>
<tr class="b">
<th width="20%">License(s)</th>
<td width="80%" align="left">The BSD 3-Clause License (BSD3)</td></tr></table><a href="#Overview">Back to top</a><br /></section><section>
<h3><a name="com.azure:azure-core:1.46.0"></a>com.azure:azure-core:1.46.0</h3>
<table border="0" class="bodyTable">
<tr class="a">
<th width="20%">Status</th>
<td width="80%" align="left"><figure><img src="images/icon_success_sml.gif" alt="" /></figure>&#160;License defined in the pom of the dependency</td></tr>
<tr class="b">
<th width="20%">GroupId:ArtifactId:Version</th>
<td width="80%" align="left">com.azure:azure-core:1.46.0</td></tr>
<tr class="a">
<th width="20%">Scope</th>
<td width="80%" align="left">compile</td></tr>
<tr class="b">
<th width="20%">Classifier</th>
<td width="80%" align="left"></td></tr>
<tr class="a">
<th width="20%">Type</th>
<td width="80%" align="left">jar</td></tr>
<tr class="b">
<th width="20%">License(s)</th>
<td width="80%" align="left">The MIT License (MIT)</td></tr></table><a href="#Overview">Back to top</a><br /></section><section>
<h3><a name="com.azure:azure-core-http-netty:1.14.1"></a>com.azure:azure-core-http-netty:1.14.1</h3>
<table border="0" class="bodyTable">
<tr class="a">
<th width="20%">Status</th>
<td width="80%" align="left"><figure><img src="images/icon_success_sml.gif" alt="" /></figure>&#160;License defined in the pom of the dependency</td></tr>
<tr class="b">
<th width="20%">GroupId:ArtifactId:Version</th>
<td width="80%" align="left">com.azure:azure-core-http-netty:1.14.1</td></tr>
<tr class="a">
<th width="20%">Scope</th>
<td width="80%" align="left">compile</td></tr>
<tr class="b">
<th width="20%">Classifier</th>
<td width="80%" align="left"></td></tr>
<tr class="a">
<th width="20%">Type</th>
<td width="80%" align="left">jar</td></tr>
<tr class="b">
<th width="20%">License(s)</th>
<td width="80%" align="left">The MIT License (MIT)</td></tr></table><a href="#Overview">Back to top</a><br /></section><section>
<h3><a name="com.azure:azure-identity:1.11.4"></a>com.azure:azure-identity:1.11.4</h3>
<table border="0" class="bodyTable">
<tr class="a">
<th width="20%">Status</th>
<td width="80%" align="left"><figure><img src="images/icon_success_sml.gif" alt="" /></figure>&#160;License defined in the pom of the dependency</td></tr>
<tr class="b">
<th width="20%">GroupId:ArtifactId:Version</th>
<td width="80%" align="left">com.azure:azure-identity:1.11.4</td></tr>
<tr class="a">
<th width="20%">Scope</th>
<td width="80%" align="left">compile</td></tr>
<tr class="b">
<th width="20%">Classifier</th>
<td width="80%" align="left"></td></tr>
<tr class="a">
<th width="20%">Type</th>
<td width="80%" align="left">jar</td></tr>
<tr class="b">
<th width="20%">License(s)</th>
<td width="80%" align="left">The MIT License (MIT)</td></tr></table><a href="#Overview">Back to top</a><br /></section><section>
<h3><a name="com.azure:azure-json:1.1.0"></a>com.azure:azure-json:1.1.0</h3>
<table border="0" class="bodyTable">
<tr class="a">
<th width="20%">Status</th>
<td width="80%" align="left"><figure><img src="images/icon_success_sml.gif" alt="" /></figure>&#160;License defined in the pom of the dependency</td></tr>
<tr class="b">
<th width="20%">GroupId:ArtifactId:Version</th>
<td width="80%" align="left">com.azure:azure-json:1.1.0</td></tr>
<tr class="a">
<th width="20%">Scope</th>
<td width="80%" align="left">compile</td></tr>
<tr class="b">
<th width="20%">Classifier</th>
<td width="80%" align="left"></td></tr>
<tr class="a">
<th width="20%">Type</th>
<td width="80%" align="left">jar</td></tr>
<tr class="b">
<th width="20%">License(s)</th>
<td width="80%" align="left">The MIT License (MIT)</td></tr></table><a href="#Overview">Back to top</a><br /></section><section>
<h3><a name="com.drewnoakes:metadata-extractor:2.18.0"></a>com.drewnoakes:metadata-extractor:2.18.0</h3>
<table border="0" class="bodyTable">
<tr class="a">
<th width="20%">Status</th>
<td width="80%" align="left"><figure><img src="images/icon_success_sml.gif" alt="" /></figure>&#160;License defined in the pom of the dependency</td></tr>
<tr class="b">
<th width="20%">GroupId:ArtifactId:Version</th>
<td width="80%" align="left">com.drewnoakes:metadata-extractor:2.18.0</td></tr>
<tr class="a">
<th width="20%">Scope</th>
<td width="80%" align="left">compile</td></tr>
<tr class="b">
<th width="20%">Classifier</th>
<td width="80%" align="left"></td></tr>
<tr class="a">
<th width="20%">Type</th>
<td width="80%" align="left">jar</td></tr>
<tr class="b">
<th width="20%">License(s)</th>
<td width="80%" align="left">The Apache Software License, Version 2.0</td></tr></table><a href="#Overview">Back to top</a><br /></section><section>
<h3><a name="com.epam:parso:2.0.14"></a>com.epam:parso:2.0.14</h3>
<table border="0" class="bodyTable">
<tr class="a">
<th width="20%">Status</th>
<td width="80%" align="left"><figure><img src="images/icon_success_sml.gif" alt="" /></figure>&#160;License defined in the pom of the dependency</td></tr>
<tr class="b">
<th width="20%">GroupId:ArtifactId:Version</th>
<td width="80%" align="left">com.epam:parso:2.0.14</td></tr>
<tr class="a">
<th width="20%">Scope</th>
<td width="80%" align="left">compile</td></tr>
<tr class="b">
<th width="20%">Classifier</th>
<td width="80%" align="left"></td></tr>
<tr class="a">
<th width="20%">Type</th>
<td width="80%" align="left">jar</td></tr>
<tr class="b">
<th width="20%">License(s)</th>
<td width="80%" align="left">Apache License v2</td></tr></table><a href="#Overview">Back to top</a><br /></section><section>
<h3><a name="com.fasterxml:classmate:1.7.0"></a>com.fasterxml:classmate:1.7.0</h3>
<table border="0" class="bodyTable">
<tr class="a">
<th width="20%">Status</th>
<td width="80%" align="left"><figure><img src="images/icon_success_sml.gif" alt="" /></figure>&#160;License defined in the pom of the dependency</td></tr>
<tr class="b">
<th width="20%">GroupId:ArtifactId:Version</th>
<td width="80%" align="left">com.fasterxml:classmate:1.7.0</td></tr>
<tr class="a">
<th width="20%">Scope</th>
<td width="80%" align="left">compile</td></tr>
<tr class="b">
<th width="20%">Classifier</th>
<td width="80%" align="left"></td></tr>
<tr class="a">
<th width="20%">Type</th>
<td width="80%" align="left">bundle</td></tr>
<tr class="b">
<th width="20%">License(s)</th>
<td width="80%" align="left">Apache License, Version 2.0</td></tr></table><a href="#Overview">Back to top</a><br /></section><section>
<h3><a name="com.fasterxml.jackson.core:jackson-annotations:2.19.0"></a>com.fasterxml.jackson.core:jackson-annotations:2.19.0</h3>
<table border="0" class="bodyTable">
<tr class="a">
<th width="20%">Status</th>
<td width="80%" align="left"><figure><img src="images/icon_success_sml.gif" alt="" /></figure>&#160;License defined in the pom of the dependency</td></tr>
<tr class="b">
<th width="20%">GroupId:ArtifactId:Version</th>
<td width="80%" align="left">com.fasterxml.jackson.core:jackson-annotations:2.19.0</td></tr>
<tr class="a">
<th width="20%">Scope</th>
<td width="80%" align="left">compile</td></tr>
<tr class="b">
<th width="20%">Classifier</th>
<td width="80%" align="left"></td></tr>
<tr class="a">
<th width="20%">Type</th>
<td width="80%" align="left">jar</td></tr>
<tr class="b">
<th width="20%">License(s)</th>
<td width="80%" align="left">The Apache Software License, Version 2.0</td></tr></table><a href="#Overview">Back to top</a><br /></section><section>
<h3><a name="com.fasterxml.jackson.core:jackson-core:2.19.0"></a>com.fasterxml.jackson.core:jackson-core:2.19.0</h3>
<table border="0" class="bodyTable">
<tr class="a">
<th width="20%">Status</th>
<td width="80%" align="left"><figure><img src="images/icon_success_sml.gif" alt="" /></figure>&#160;License defined in the pom of the dependency</td></tr>
<tr class="b">
<th width="20%">GroupId:ArtifactId:Version</th>
<td width="80%" align="left">com.fasterxml.jackson.core:jackson-core:2.19.0</td></tr>
<tr class="a">
<th width="20%">Scope</th>
<td width="80%" align="left">compile</td></tr>
<tr class="b">
<th width="20%">Classifier</th>
<td width="80%" align="left"></td></tr>
<tr class="a">
<th width="20%">Type</th>
<td width="80%" align="left">jar</td></tr>
<tr class="b">
<th width="20%">License(s)</th>
<td width="80%" align="left">The Apache Software License, Version 2.0</td></tr></table><a href="#Overview">Back to top</a><br /></section><section>
<h3><a name="com.fasterxml.jackson.core:jackson-databind:2.19.0"></a>com.fasterxml.jackson.core:jackson-databind:2.19.0</h3>
<table border="0" class="bodyTable">
<tr class="a">
<th width="20%">Status</th>
<td width="80%" align="left"><figure><img src="images/icon_success_sml.gif" alt="" /></figure>&#160;License defined in the pom of the dependency</td></tr>
<tr class="b">
<th width="20%">GroupId:ArtifactId:Version</th>
<td width="80%" align="left">com.fasterxml.jackson.core:jackson-databind:2.19.0</td></tr>
<tr class="a">
<th width="20%">Scope</th>
<td width="80%" align="left">compile</td></tr>
<tr class="b">
<th width="20%">Classifier</th>
<td width="80%" align="left"></td></tr>
<tr class="a">
<th width="20%">Type</th>
<td width="80%" align="left">jar</td></tr>
<tr class="b">
<th width="20%">License(s)</th>
<td width="80%" align="left">The Apache Software License, Version 2.0</td></tr></table><a href="#Overview">Back to top</a><br /></section><section>
<h3><a name="com.fasterxml.jackson.dataformat:jackson-dataformat-yaml:2.19.0"></a>com.fasterxml.jackson.dataformat:jackson-dataformat-yaml:2.19.0</h3>
<table border="0" class="bodyTable">
<tr class="a">
<th width="20%">Status</th>
<td width="80%" align="left"><figure><img src="images/icon_success_sml.gif" alt="" /></figure>&#160;License defined in the pom of the dependency</td></tr>
<tr class="b">
<th width="20%">GroupId:ArtifactId:Version</th>
<td width="80%" align="left">com.fasterxml.jackson.dataformat:jackson-dataformat-yaml:2.19.0</td></tr>
<tr class="a">
<th width="20%">Scope</th>
<td width="80%" align="left">runtime</td></tr>
<tr class="b">
<th width="20%">Classifier</th>
<td width="80%" align="left"></td></tr>
<tr class="a">
<th width="20%">Type</th>
<td width="80%" align="left">jar</td></tr>
<tr class="b">
<th width="20%">License(s)</th>
<td width="80%" align="left">The Apache Software License, Version 2.0</td></tr></table><a href="#Overview">Back to top</a><br /></section><section>
<h3><a name="com.fasterxml.jackson.datatype:jackson-datatype-jdk8:2.19.0"></a>com.fasterxml.jackson.datatype:jackson-datatype-jdk8:2.19.0</h3>
<table border="0" class="bodyTable">
<tr class="a">
<th width="20%">Status</th>
<td width="80%" align="left"><figure><img src="images/icon_success_sml.gif" alt="" /></figure>&#160;License defined in the pom of the dependency</td></tr>
<tr class="b">
<th width="20%">GroupId:ArtifactId:Version</th>
<td width="80%" align="left">com.fasterxml.jackson.datatype:jackson-datatype-jdk8:2.19.0</td></tr>
<tr class="a">
<th width="20%">Scope</th>
<td width="80%" align="left">compile</td></tr>
<tr class="b">
<th width="20%">Classifier</th>
<td width="80%" align="left"></td></tr>
<tr class="a">
<th width="20%">Type</th>
<td width="80%" align="left">bundle</td></tr>
<tr class="b">
<th width="20%">License(s)</th>
<td width="80%" align="left">The Apache Software License, Version 2.0</td></tr></table><a href="#Overview">Back to top</a><br /></section><section>
<h3><a name="com.fasterxml.jackson.datatype:jackson-datatype-jsr310:2.19.0"></a>com.fasterxml.jackson.datatype:jackson-datatype-jsr310:2.19.0</h3>
<table border="0" class="bodyTable">
<tr class="a">
<th width="20%">Status</th>
<td width="80%" align="left"><figure><img src="images/icon_success_sml.gif" alt="" /></figure>&#160;License defined in the pom of the dependency</td></tr>
<tr class="b">
<th width="20%">GroupId:ArtifactId:Version</th>
<td width="80%" align="left">com.fasterxml.jackson.datatype:jackson-datatype-jsr310:2.19.0</td></tr>
<tr class="a">
<th width="20%">Scope</th>
<td width="80%" align="left">compile</td></tr>
<tr class="b">
<th width="20%">Classifier</th>
<td width="80%" align="left"></td></tr>
<tr class="a">
<th width="20%">Type</th>
<td width="80%" align="left">bundle</td></tr>
<tr class="b">
<th width="20%">License(s)</th>
<td width="80%" align="left">The Apache Software License, Version 2.0</td></tr></table><a href="#Overview">Back to top</a><br /></section><section>
<h3><a name="com.fasterxml.jackson.module:jackson-module-parameter-names:2.19.0"></a>com.fasterxml.jackson.module:jackson-module-parameter-names:2.19.0</h3>
<table border="0" class="bodyTable">
<tr class="a">
<th width="20%">Status</th>
<td width="80%" align="left"><figure><img src="images/icon_success_sml.gif" alt="" /></figure>&#160;License defined in the pom of the dependency</td></tr>
<tr class="b">
<th width="20%">GroupId:ArtifactId:Version</th>
<td width="80%" align="left">com.fasterxml.jackson.module:jackson-module-parameter-names:2.19.0</td></tr>
<tr class="a">
<th width="20%">Scope</th>
<td width="80%" align="left">compile</td></tr>
<tr class="b">
<th width="20%">Classifier</th>
<td width="80%" align="left"></td></tr>
<tr class="a">
<th width="20%">Type</th>
<td width="80%" align="left">bundle</td></tr>
<tr class="b">
<th width="20%">License(s)</th>
<td width="80%" align="left">The Apache Software License, Version 2.0</td></tr></table><a href="#Overview">Back to top</a><br /></section><section>
<h3><a name="com.github.albfernandez:juniversalchardet:2.4.0"></a>com.github.albfernandez:juniversalchardet:2.4.0</h3>
<table border="0" class="bodyTable">
<tr class="a">
<th width="20%">Status</th>
<td width="80%" align="left"><figure><img src="images/icon_success_sml.gif" alt="" /></figure>&#160;License defined in the pom of the dependency</td></tr>
<tr class="b">
<th width="20%">GroupId:ArtifactId:Version</th>
<td width="80%" align="left">com.github.albfernandez:juniversalchardet:2.4.0</td></tr>
<tr class="a">
<th width="20%">Scope</th>
<td width="80%" align="left">compile</td></tr>
<tr class="b">
<th width="20%">Classifier</th>
<td width="80%" align="left"></td></tr>
<tr class="a">
<th width="20%">Type</th>
<td width="80%" align="left">jar</td></tr>
<tr class="b">
<th width="20%">License(s)</th>
<td width="80%" align="left">GENERAL PUBLIC LICENSE, version 3 (GPL-3.0)<br />GNU LESSER GENERAL PUBLIC LICENSE, version 3 (LGPL-3.0)<br />Mozilla Public License Version 1.1</td></tr></table><a href="#Overview">Back to top</a><br /></section><section>
<h3><a name="com.github.jai-imageio:jai-imageio-core:1.4.0"></a>com.github.jai-imageio:jai-imageio-core:1.4.0</h3>
<table border="0" class="bodyTable">
<tr class="a">
<th width="20%">Status</th>
<td width="80%" align="left"><figure><img src="images/icon_success_sml.gif" alt="" /></figure>&#160;License defined in the pom of the dependency</td></tr>
<tr class="b">
<th width="20%">GroupId:ArtifactId:Version</th>
<td width="80%" align="left">com.github.jai-imageio:jai-imageio-core:1.4.0</td></tr>
<tr class="a">
<th width="20%">Scope</th>
<td width="80%" align="left">compile</td></tr>
<tr class="b">
<th width="20%">Classifier</th>
<td width="80%" align="left"></td></tr>
<tr class="a">
<th width="20%">Type</th>
<td width="80%" align="left">bundle</td></tr>
<tr class="b">
<th width="20%">License(s)</th>
<td width="80%" align="left">BSD 3-clause License w/nuclear disclaimer</td></tr></table><a href="#Overview">Back to top</a><br /></section><section>
<h3><a name="com.github.junrar:junrar:7.5.5"></a>com.github.junrar:junrar:7.5.5</h3>
<table border="0" class="bodyTable">
<tr class="a">
<th width="20%">Status</th>
<td width="80%" align="left"><figure><img src="images/icon_success_sml.gif" alt="" /></figure>&#160;License defined in the pom of the dependency</td></tr>
<tr class="b">
<th width="20%">GroupId:ArtifactId:Version</th>
<td width="80%" align="left">com.github.junrar:junrar:7.5.5</td></tr>
<tr class="a">
<th width="20%">Scope</th>
<td width="80%" align="left">compile</td></tr>
<tr class="b">
<th width="20%">Classifier</th>
<td width="80%" align="left"></td></tr>
<tr class="a">
<th width="20%">Type</th>
<td width="80%" align="left">jar</td></tr>
<tr class="b">
<th width="20%">License(s)</th>
<td width="80%" align="left">UnRar License</td></tr></table><a href="#Overview">Back to top</a><br /></section><section>
<h3><a name="com.github.stephenc.jcip:jcip-annotations:1.0-1"></a>com.github.stephenc.jcip:jcip-annotations:1.0-1</h3>
<table border="0" class="bodyTable">
<tr class="a">
<th width="20%">Status</th>
<td width="80%" align="left"><figure><img src="images/icon_success_sml.gif" alt="" /></figure>&#160;License defined in the pom of the dependency</td></tr>
<tr class="b">
<th width="20%">GroupId:ArtifactId:Version</th>
<td width="80%" align="left">com.github.stephenc.jcip:jcip-annotations:1.0-1</td></tr>
<tr class="a">
<th width="20%">Scope</th>
<td width="80%" align="left">compile</td></tr>
<tr class="b">
<th width="20%">Classifier</th>
<td width="80%" align="left"></td></tr>
<tr class="a">
<th width="20%">Type</th>
<td width="80%" align="left">jar</td></tr>
<tr class="b">
<th width="20%">License(s)</th>
<td width="80%" align="left">Apache License, Version 2.0</td></tr></table><a href="#Overview">Back to top</a><br /></section><section>
<h3><a name="com.github.virtuald:curvesapi:1.08"></a>com.github.virtuald:curvesapi:1.08</h3>
<table border="0" class="bodyTable">
<tr class="a">
<th width="20%">Status</th>
<td width="80%" align="left"><figure><img src="images/icon_success_sml.gif" alt="" /></figure>&#160;License defined in the pom of the dependency</td></tr>
<tr class="b">
<th width="20%">GroupId:ArtifactId:Version</th>
<td width="80%" align="left">com.github.virtuald:curvesapi:1.08</td></tr>
<tr class="a">
<th width="20%">Scope</th>
<td width="80%" align="left">compile</td></tr>
<tr class="b">
<th width="20%">Classifier</th>
<td width="80%" align="left"></td></tr>
<tr class="a">
<th width="20%">Type</th>
<td width="80%" align="left">jar</td></tr>
<tr class="b">
<th width="20%">License(s)</th>
<td width="80%" align="left">BSD License</td></tr></table><a href="#Overview">Back to top</a><br /></section><section>
<h3><a name="com.google.cloud.opentelemetry:detector-resources-support:0.33.0"></a>com.google.cloud.opentelemetry:detector-resources-support:0.33.0</h3>
<table border="0" class="bodyTable">
<tr class="a">
<th width="20%">Status</th>
<td width="80%" align="left"><figure><img src="images/icon_success_sml.gif" alt="" /></figure>&#160;License defined in the pom of the dependency</td></tr>
<tr class="b">
<th width="20%">GroupId:ArtifactId:Version</th>
<td width="80%" align="left">com.google.cloud.opentelemetry:detector-resources-support:0.33.0</td></tr>
<tr class="a">
<th width="20%">Scope</th>
<td width="80%" align="left">runtime</td></tr>
<tr class="b">
<th width="20%">Classifier</th>
<td width="80%" align="left"></td></tr>
<tr class="a">
<th width="20%">Type</th>
<td width="80%" align="left">jar</td></tr>
<tr class="b">
<th width="20%">License(s)</th>
<td width="80%" align="left">The Apache License, Version 2.0</td></tr></table><a href="#Overview">Back to top</a><br /></section><section>
<h3><a name="com.google.code.findbugs:jsr305:3.0.2"></a>com.google.code.findbugs:jsr305:3.0.2</h3>
<table border="0" class="bodyTable">
<tr class="a">
<th width="20%">Status</th>
<td width="80%" align="left"><figure><img src="images/icon_success_sml.gif" alt="" /></figure>&#160;License defined in the pom of the dependency</td></tr>
<tr class="b">
<th width="20%">GroupId:ArtifactId:Version</th>
<td width="80%" align="left">com.google.code.findbugs:jsr305:3.0.2</td></tr>
<tr class="a">
<th width="20%">Scope</th>
<td width="80%" align="left">compile</td></tr>
<tr class="b">
<th width="20%">Classifier</th>
<td width="80%" align="left"></td></tr>
<tr class="a">
<th width="20%">Type</th>
<td width="80%" align="left">jar</td></tr>
<tr class="b">
<th width="20%">License(s)</th>
<td width="80%" align="left">The Apache Software License, Version 2.0</td></tr></table><a href="#Overview">Back to top</a><br /></section><section>
<h3><a name="com.google.code.gson:gson:2.13.1"></a>com.google.code.gson:gson:2.13.1</h3>
<table border="0" class="bodyTable">
<tr class="a">
<th width="20%">Status</th>
<td width="80%" align="left"><figure><img src="images/icon_success_sml.gif" alt="" /></figure>&#160;License defined in the pom of the dependency</td></tr>
<tr class="b">
<th width="20%">GroupId:ArtifactId:Version</th>
<td width="80%" align="left">com.google.code.gson:gson:2.13.1</td></tr>
<tr class="a">
<th width="20%">Scope</th>
<td width="80%" align="left">runtime</td></tr>
<tr class="b">
<th width="20%">Classifier</th>
<td width="80%" align="left"></td></tr>
<tr class="a">
<th width="20%">Type</th>
<td width="80%" align="left">jar</td></tr>
<tr class="b">
<th width="20%">License(s)</th>
<td width="80%" align="left">Apache-2.0</td></tr></table><a href="#Overview">Back to top</a><br /></section><section>
<h3><a name="com.google.errorprone:error_prone_annotations:2.11.0"></a>com.google.errorprone:error_prone_annotations:2.11.0</h3>
<table border="0" class="bodyTable">
<tr class="a">
<th width="20%">Status</th>
<td width="80%" align="left"><figure><img src="images/icon_success_sml.gif" alt="" /></figure>&#160;License defined in the pom of the dependency</td></tr>
<tr class="b">
<th width="20%">GroupId:ArtifactId:Version</th>
<td width="80%" align="left">com.google.errorprone:error_prone_annotations:2.11.0</td></tr>
<tr class="a">
<th width="20%">Scope</th>
<td width="80%" align="left">runtime</td></tr>
<tr class="b">
<th width="20%">Classifier</th>
<td width="80%" align="left"></td></tr>
<tr class="a">
<th width="20%">Type</th>
<td width="80%" align="left">jar</td></tr>
<tr class="b">
<th width="20%">License(s)</th>
<td width="80%" align="left">Apache 2.0</td></tr></table><a href="#Overview">Back to top</a><br /></section><section>
<h3><a name="com.googlecode.plist:dd-plist:1.27"></a>com.googlecode.plist:dd-plist:1.27</h3>
<table border="0" class="bodyTable">
<tr class="a">
<th width="20%">Status</th>
<td width="80%" align="left"><figure><img src="images/icon_success_sml.gif" alt="" /></figure>&#160;License defined in the pom of the dependency</td></tr>
<tr class="b">
<th width="20%">GroupId:ArtifactId:Version</th>
<td width="80%" align="left">com.googlecode.plist:dd-plist:1.27</td></tr>
<tr class="a">
<th width="20%">Scope</th>
<td width="80%" align="left">compile</td></tr>
<tr class="b">
<th width="20%">Classifier</th>
<td width="80%" align="left"></td></tr>
<tr class="a">
<th width="20%">Type</th>
<td width="80%" align="left">jar</td></tr>
<tr class="b">
<th width="20%">License(s)</th>
<td width="80%" align="left">MIT License</td></tr></table><a href="#Overview">Back to top</a><br /></section><section>
<h3><a name="com.graphql-java:graphql-java:24.0"></a>com.graphql-java:graphql-java:24.0</h3>
<table border="0" class="bodyTable">
<tr class="a">
<th width="20%">Status</th>
<td width="80%" align="left"><figure><img src="images/icon_success_sml.gif" alt="" /></figure>&#160;License defined in the pom of the dependency</td></tr>
<tr class="b">
<th width="20%">GroupId:ArtifactId:Version</th>
<td width="80%" align="left">com.graphql-java:graphql-java:24.0</td></tr>
<tr class="a">
<th width="20%">Scope</th>
<td width="80%" align="left">compile</td></tr>
<tr class="b">
<th width="20%">Classifier</th>
<td width="80%" align="left"></td></tr>
<tr class="a">
<th width="20%">Type</th>
<td width="80%" align="left">jar</td></tr>
<tr class="b">
<th width="20%">License(s)</th>
<td width="80%" align="left">MIT</td></tr></table><a href="#Overview">Back to top</a><br /></section><section>
<h3><a name="com.graphql-java:graphql-java-extended-scalars:22.0"></a>com.graphql-java:graphql-java-extended-scalars:22.0</h3>
<table border="0" class="bodyTable">
<tr class="a">
<th width="20%">Status</th>
<td width="80%" align="left"><figure><img src="images/icon_success_sml.gif" alt="" /></figure>&#160;License defined in the pom of the dependency</td></tr>
<tr class="b">
<th width="20%">GroupId:ArtifactId:Version</th>
<td width="80%" align="left">com.graphql-java:graphql-java-extended-scalars:22.0</td></tr>
<tr class="a">
<th width="20%">Scope</th>
<td width="80%" align="left">compile</td></tr>
<tr class="b">
<th width="20%">Classifier</th>
<td width="80%" align="left"></td></tr>
<tr class="a">
<th width="20%">Type</th>
<td width="80%" align="left">jar</td></tr>
<tr class="b">
<th width="20%">License(s)</th>
<td width="80%" align="left">MIT</td></tr></table><a href="#Overview">Back to top</a><br /></section><section>
<h3><a name="com.graphql-java:java-dataloader:5.0.0"></a>com.graphql-java:java-dataloader:5.0.0</h3>
<table border="0" class="bodyTable">
<tr class="a">
<th width="20%">Status</th>
<td width="80%" align="left"><figure><img src="images/icon_success_sml.gif" alt="" /></figure>&#160;License defined in the pom of the dependency</td></tr>
<tr class="b">
<th width="20%">GroupId:ArtifactId:Version</th>
<td width="80%" align="left">com.graphql-java:java-dataloader:5.0.0</td></tr>
<tr class="a">
<th width="20%">Scope</th>
<td width="80%" align="left">compile</td></tr>
<tr class="b">
<th width="20%">Classifier</th>
<td width="80%" align="left"></td></tr>
<tr class="a">
<th width="20%">Type</th>
<td width="80%" align="left">jar</td></tr>
<tr class="b">
<th width="20%">License(s)</th>
<td width="80%" align="left">The Apache Software License, Version 2.0</td></tr></table><a href="#Overview">Back to top</a><br /></section><section>
<h3><a name="com.healthmarketscience.jackcess:jackcess:4.0.5"></a>com.healthmarketscience.jackcess:jackcess:4.0.5</h3>
<table border="0" class="bodyTable">
<tr class="a">
<th width="20%">Status</th>
<td width="80%" align="left"><figure><img src="images/icon_success_sml.gif" alt="" /></figure>&#160;License defined in the pom of the dependency</td></tr>
<tr class="b">
<th width="20%">GroupId:ArtifactId:Version</th>
<td width="80%" align="left">com.healthmarketscience.jackcess:jackcess:4.0.5</td></tr>
<tr class="a">
<th width="20%">Scope</th>
<td width="80%" align="left">compile</td></tr>
<tr class="b">
<th width="20%">Classifier</th>
<td width="80%" align="left"></td></tr>
<tr class="a">
<th width="20%">Type</th>
<td width="80%" align="left">jar</td></tr>
<tr class="b">
<th width="20%">License(s)</th>
<td width="80%" align="left">Apache License, Version 2.0</td></tr></table><a href="#Overview">Back to top</a><br /></section><section>
<h3><a name="com.healthmarketscience.jackcess:jackcess-encrypt:4.0.2"></a>com.healthmarketscience.jackcess:jackcess-encrypt:4.0.2</h3>
<table border="0" class="bodyTable">
<tr class="a">
<th width="20%">Status</th>
<td width="80%" align="left"><figure><img src="images/icon_success_sml.gif" alt="" /></figure>&#160;License defined in the pom of the dependency</td></tr>
<tr class="b">
<th width="20%">GroupId:ArtifactId:Version</th>
<td width="80%" align="left">com.healthmarketscience.jackcess:jackcess-encrypt:4.0.2</td></tr>
<tr class="a">
<th width="20%">Scope</th>
<td width="80%" align="left">compile</td></tr>
<tr class="b">
<th width="20%">Classifier</th>
<td width="80%" align="left"></td></tr>
<tr class="a">
<th width="20%">Type</th>
<td width="80%" align="left">jar</td></tr>
<tr class="b">
<th width="20%">License(s)</th>
<td width="80%" align="left">Apache License, Version 2.0</td></tr></table><a href="#Overview">Back to top</a><br /></section><section>
<h3><a name="com.itextpdf:itextpdf:5.5.13.3"></a>com.itextpdf:itextpdf:5.5.13.3</h3>
<table border="0" class="bodyTable">
<tr class="a">
<th width="20%">Status</th>
<td width="80%" align="left"><figure><img src="images/icon_success_sml.gif" alt="" /></figure>&#160;License defined in the pom of the dependency</td></tr>
<tr class="b">
<th width="20%">GroupId:ArtifactId:Version</th>
<td width="80%" align="left">com.itextpdf:itextpdf:5.5.13.3</td></tr>
<tr class="a">
<th width="20%">Scope</th>
<td width="80%" align="left">compile</td></tr>
<tr class="b">
<th width="20%">Classifier</th>
<td width="80%" align="left"></td></tr>
<tr class="a">
<th width="20%">Type</th>
<td width="80%" align="left">jar</td></tr>
<tr class="b">
<th width="20%">License(s)</th>
<td width="80%" align="left">GNU Affero General Public License v3</td></tr></table><a href="#Overview">Back to top</a><br /></section><section>
<h3><a name="com.microsoft.azure:msal4j:1.14.3"></a>com.microsoft.azure:msal4j:1.14.3</h3>
<table border="0" class="bodyTable">
<tr class="a">
<th width="20%">Status</th>
<td width="80%" align="left"><figure><img src="images/icon_success_sml.gif" alt="" /></figure>&#160;License defined in the pom of the dependency</td></tr>
<tr class="b">
<th width="20%">GroupId:ArtifactId:Version</th>
<td width="80%" align="left">com.microsoft.azure:msal4j:1.14.3</td></tr>
<tr class="a">
<th width="20%">Scope</th>
<td width="80%" align="left">compile</td></tr>
<tr class="b">
<th width="20%">Classifier</th>
<td width="80%" align="left"></td></tr>
<tr class="a">
<th width="20%">Type</th>
<td width="80%" align="left">jar</td></tr>
<tr class="b">
<th width="20%">License(s)</th>
<td width="80%" align="left">MIT License</td></tr></table><a href="#Overview">Back to top</a><br /></section><section>
<h3><a name="com.microsoft.azure:msal4j-persistence-extension:1.2.0"></a>com.microsoft.azure:msal4j-persistence-extension:1.2.0</h3>
<table border="0" class="bodyTable">
<tr class="a">
<th width="20%">Status</th>
<td width="80%" align="left"><figure><img src="images/icon_success_sml.gif" alt="" /></figure>&#160;License defined in the pom of the dependency</td></tr>
<tr class="b">
<th width="20%">GroupId:ArtifactId:Version</th>
<td width="80%" align="left">com.microsoft.azure:msal4j-persistence-extension:1.2.0</td></tr>
<tr class="a">
<th width="20%">Scope</th>
<td width="80%" align="left">compile</td></tr>
<tr class="b">
<th width="20%">Classifier</th>
<td width="80%" align="left"></td></tr>
<tr class="a">
<th width="20%">Type</th>
<td width="80%" align="left">jar</td></tr>
<tr class="b">
<th width="20%">License(s)</th>
<td width="80%" align="left">MIT License</td></tr></table><a href="#Overview">Back to top</a><br /></section><section>
<h3><a name="com.microsoft.graph:microsoft-graph:6.15.0"></a>com.microsoft.graph:microsoft-graph:6.15.0</h3>
<table border="0" class="bodyTable">
<tr class="a">
<th width="20%">Status</th>
<td width="80%" align="left"><figure><img src="images/icon_success_sml.gif" alt="" /></figure>&#160;License defined in the pom of the dependency</td></tr>
<tr class="b">
<th width="20%">GroupId:ArtifactId:Version</th>
<td width="80%" align="left">com.microsoft.graph:microsoft-graph:6.15.0</td></tr>
<tr class="a">
<th width="20%">Scope</th>
<td width="80%" align="left">compile</td></tr>
<tr class="b">
<th width="20%">Classifier</th>
<td width="80%" align="left"></td></tr>
<tr class="a">
<th width="20%">Type</th>
<td width="80%" align="left">jar</td></tr>
<tr class="b">
<th width="20%">License(s)</th>
<td width="80%" align="left">MIT License</td></tr></table><a href="#Overview">Back to top</a><br /></section><section>
<h3><a name="com.microsoft.graph:microsoft-graph-core:3.2.0"></a>com.microsoft.graph:microsoft-graph-core:3.2.0</h3>
<table border="0" class="bodyTable">
<tr class="a">
<th width="20%">Status</th>
<td width="80%" align="left"><figure><img src="images/icon_success_sml.gif" alt="" /></figure>&#160;License defined in the pom of the dependency</td></tr>
<tr class="b">
<th width="20%">GroupId:ArtifactId:Version</th>
<td width="80%" align="left">com.microsoft.graph:microsoft-graph-core:3.2.0</td></tr>
<tr class="a">
<th width="20%">Scope</th>
<td width="80%" align="left">compile</td></tr>
<tr class="b">
<th width="20%">Classifier</th>
<td width="80%" align="left"></td></tr>
<tr class="a">
<th width="20%">Type</th>
<td width="80%" align="left">jar</td></tr>
<tr class="b">
<th width="20%">License(s)</th>
<td width="80%" align="left">MIT License</td></tr></table><a href="#Overview">Back to top</a><br /></section><section>
<h3><a name="com.microsoft.kiota:microsoft-kiota-abstractions:1.3.0"></a>com.microsoft.kiota:microsoft-kiota-abstractions:1.3.0</h3>
<table border="0" class="bodyTable">
<tr class="a">
<th width="20%">Status</th>
<td width="80%" align="left"><figure><img src="images/icon_success_sml.gif" alt="" /></figure>&#160;License defined in the pom of the dependency</td></tr>
<tr class="b">
<th width="20%">GroupId:ArtifactId:Version</th>
<td width="80%" align="left">com.microsoft.kiota:microsoft-kiota-abstractions:1.3.0</td></tr>
<tr class="a">
<th width="20%">Scope</th>
<td width="80%" align="left">compile</td></tr>
<tr class="b">
<th width="20%">Classifier</th>
<td width="80%" align="left"></td></tr>
<tr class="a">
<th width="20%">Type</th>
<td width="80%" align="left">jar</td></tr>
<tr class="b">
<th width="20%">License(s)</th>
<td width="80%" align="left">MIT License</td></tr></table><a href="#Overview">Back to top</a><br /></section><section>
<h3><a name="com.microsoft.kiota:microsoft-kiota-authentication-azure:1.3.0"></a>com.microsoft.kiota:microsoft-kiota-authentication-azure:1.3.0</h3>
<table border="0" class="bodyTable">
<tr class="a">
<th width="20%">Status</th>
<td width="80%" align="left"><figure><img src="images/icon_success_sml.gif" alt="" /></figure>&#160;License defined in the pom of the dependency</td></tr>
<tr class="b">
<th width="20%">GroupId:ArtifactId:Version</th>
<td width="80%" align="left">com.microsoft.kiota:microsoft-kiota-authentication-azure:1.3.0</td></tr>
<tr class="a">
<th width="20%">Scope</th>
<td width="80%" align="left">compile</td></tr>
<tr class="b">
<th width="20%">Classifier</th>
<td width="80%" align="left"></td></tr>
<tr class="a">
<th width="20%">Type</th>
<td width="80%" align="left">jar</td></tr>
<tr class="b">
<th width="20%">License(s)</th>
<td width="80%" align="left">MIT License</td></tr></table><a href="#Overview">Back to top</a><br /></section><section>
<h3><a name="com.microsoft.kiota:microsoft-kiota-http-okHttp:1.3.0"></a>com.microsoft.kiota:microsoft-kiota-http-okHttp:1.3.0</h3>
<table border="0" class="bodyTable">
<tr class="a">
<th width="20%">Status</th>
<td width="80%" align="left"><figure><img src="images/icon_success_sml.gif" alt="" /></figure>&#160;License defined in the pom of the dependency</td></tr>
<tr class="b">
<th width="20%">GroupId:ArtifactId:Version</th>
<td width="80%" align="left">com.microsoft.kiota:microsoft-kiota-http-okHttp:1.3.0</td></tr>
<tr class="a">
<th width="20%">Scope</th>
<td width="80%" align="left">runtime</td></tr>
<tr class="b">
<th width="20%">Classifier</th>
<td width="80%" align="left"></td></tr>
<tr class="a">
<th width="20%">Type</th>
<td width="80%" align="left">jar</td></tr>
<tr class="b">
<th width="20%">License(s)</th>
<td width="80%" align="left">MIT License</td></tr></table><a href="#Overview">Back to top</a><br /></section><section>
<h3><a name="com.microsoft.kiota:microsoft-kiota-serialization-form:1.3.0"></a>com.microsoft.kiota:microsoft-kiota-serialization-form:1.3.0</h3>
<table border="0" class="bodyTable">
<tr class="a">
<th width="20%">Status</th>
<td width="80%" align="left"><figure><img src="images/icon_success_sml.gif" alt="" /></figure>&#160;License defined in the pom of the dependency</td></tr>
<tr class="b">
<th width="20%">GroupId:ArtifactId:Version</th>
<td width="80%" align="left">com.microsoft.kiota:microsoft-kiota-serialization-form:1.3.0</td></tr>
<tr class="a">
<th width="20%">Scope</th>
<td width="80%" align="left">runtime</td></tr>
<tr class="b">
<th width="20%">Classifier</th>
<td width="80%" align="left"></td></tr>
<tr class="a">
<th width="20%">Type</th>
<td width="80%" align="left">jar</td></tr>
<tr class="b">
<th width="20%">License(s)</th>
<td width="80%" align="left">MIT License</td></tr></table><a href="#Overview">Back to top</a><br /></section><section>
<h3><a name="com.microsoft.kiota:microsoft-kiota-serialization-json:1.3.0"></a>com.microsoft.kiota:microsoft-kiota-serialization-json:1.3.0</h3>
<table border="0" class="bodyTable">
<tr class="a">
<th width="20%">Status</th>
<td width="80%" align="left"><figure><img src="images/icon_success_sml.gif" alt="" /></figure>&#160;License defined in the pom of the dependency</td></tr>
<tr class="b">
<th width="20%">GroupId:ArtifactId:Version</th>
<td width="80%" align="left">com.microsoft.kiota:microsoft-kiota-serialization-json:1.3.0</td></tr>
<tr class="a">
<th width="20%">Scope</th>
<td width="80%" align="left">runtime</td></tr>
<tr class="b">
<th width="20%">Classifier</th>
<td width="80%" align="left"></td></tr>
<tr class="a">
<th width="20%">Type</th>
<td width="80%" align="left">jar</td></tr>
<tr class="b">
<th width="20%">License(s)</th>
<td width="80%" align="left">MIT License</td></tr></table><a href="#Overview">Back to top</a><br /></section><section>
<h3><a name="com.microsoft.kiota:microsoft-kiota-serialization-multipart:1.3.0"></a>com.microsoft.kiota:microsoft-kiota-serialization-multipart:1.3.0</h3>
<table border="0" class="bodyTable">
<tr class="a">
<th width="20%">Status</th>
<td width="80%" align="left"><figure><img src="images/icon_success_sml.gif" alt="" /></figure>&#160;License defined in the pom of the dependency</td></tr>
<tr class="b">
<th width="20%">GroupId:ArtifactId:Version</th>
<td width="80%" align="left">com.microsoft.kiota:microsoft-kiota-serialization-multipart:1.3.0</td></tr>
<tr class="a">
<th width="20%">Scope</th>
<td width="80%" align="left">runtime</td></tr>
<tr class="b">
<th width="20%">Classifier</th>
<td width="80%" align="left"></td></tr>
<tr class="a">
<th width="20%">Type</th>
<td width="80%" align="left">jar</td></tr>
<tr class="b">
<th width="20%">License(s)</th>
<td width="80%" align="left">MIT License</td></tr></table><a href="#Overview">Back to top</a><br /></section><section>
<h3><a name="com.microsoft.kiota:microsoft-kiota-serialization-text:1.3.0"></a>com.microsoft.kiota:microsoft-kiota-serialization-text:1.3.0</h3>
<table border="0" class="bodyTable">
<tr class="a">
<th width="20%">Status</th>
<td width="80%" align="left"><figure><img src="images/icon_success_sml.gif" alt="" /></figure>&#160;License defined in the pom of the dependency</td></tr>
<tr class="b">
<th width="20%">GroupId:ArtifactId:Version</th>
<td width="80%" align="left">com.microsoft.kiota:microsoft-kiota-serialization-text:1.3.0</td></tr>
<tr class="a">
<th width="20%">Scope</th>
<td width="80%" align="left">runtime</td></tr>
<tr class="b">
<th width="20%">Classifier</th>
<td width="80%" align="left"></td></tr>
<tr class="a">
<th width="20%">Type</th>
<td width="80%" align="left">jar</td></tr>
<tr class="b">
<th width="20%">License(s)</th>
<td width="80%" align="left">MIT License</td></tr></table><a href="#Overview">Back to top</a><br /></section><section>
<h3><a name="com.mysql:mysql-connector-j:9.2.0"></a>com.mysql:mysql-connector-j:9.2.0</h3>
<table border="0" class="bodyTable">
<tr class="a">
<th width="20%">Status</th>
<td width="80%" align="left"><figure><img src="images/icon_success_sml.gif" alt="" /></figure>&#160;License defined in the pom of the dependency</td></tr>
<tr class="b">
<th width="20%">GroupId:ArtifactId:Version</th>
<td width="80%" align="left">com.mysql:mysql-connector-j:9.2.0</td></tr>
<tr class="a">
<th width="20%">Scope</th>
<td width="80%" align="left">runtime</td></tr>
<tr class="b">
<th width="20%">Classifier</th>
<td width="80%" align="left"></td></tr>
<tr class="a">
<th width="20%">Type</th>
<td width="80%" align="left">jar</td></tr>
<tr class="b">
<th width="20%">License(s)</th>
<td width="80%" align="left">The GNU General Public License, v2 with Universal FOSS Exception, v1.0</td></tr></table><a href="#Overview">Back to top</a><br /></section><section>
<h3><a name="com.nimbusds:content-type:2.3"></a>com.nimbusds:content-type:2.3</h3>
<table border="0" class="bodyTable">
<tr class="a">
<th width="20%">Status</th>
<td width="80%" align="left"><figure><img src="images/icon_success_sml.gif" alt="" /></figure>&#160;License defined in the pom of the dependency</td></tr>
<tr class="b">
<th width="20%">GroupId:ArtifactId:Version</th>
<td width="80%" align="left">com.nimbusds:content-type:2.3</td></tr>
<tr class="a">
<th width="20%">Scope</th>
<td width="80%" align="left">compile</td></tr>
<tr class="b">
<th width="20%">Classifier</th>
<td width="80%" align="left"></td></tr>
<tr class="a">
<th width="20%">Type</th>
<td width="80%" align="left">jar</td></tr>
<tr class="b">
<th width="20%">License(s)</th>
<td width="80%" align="left">The Apache Software License, Version 2.0</td></tr></table><a href="#Overview">Back to top</a><br /></section><section>
<h3><a name="com.nimbusds:lang-tag:1.7"></a>com.nimbusds:lang-tag:1.7</h3>
<table border="0" class="bodyTable">
<tr class="a">
<th width="20%">Status</th>
<td width="80%" align="left"><figure><img src="images/icon_success_sml.gif" alt="" /></figure>&#160;License defined in the pom of the dependency</td></tr>
<tr class="b">
<th width="20%">GroupId:ArtifactId:Version</th>
<td width="80%" align="left">com.nimbusds:lang-tag:1.7</td></tr>
<tr class="a">
<th width="20%">Scope</th>
<td width="80%" align="left">compile</td></tr>
<tr class="b">
<th width="20%">Classifier</th>
<td width="80%" align="left"></td></tr>
<tr class="a">
<th width="20%">Type</th>
<td width="80%" align="left">jar</td></tr>
<tr class="b">
<th width="20%">License(s)</th>
<td width="80%" align="left">The Apache Software License, Version 2.0</td></tr></table><a href="#Overview">Back to top</a><br /></section><section>
<h3><a name="com.nimbusds:nimbus-jose-jwt:9.37.3"></a>com.nimbusds:nimbus-jose-jwt:9.37.3</h3>
<table border="0" class="bodyTable">
<tr class="a">
<th width="20%">Status</th>
<td width="80%" align="left"><figure><img src="images/icon_success_sml.gif" alt="" /></figure>&#160;License defined in the pom of the dependency</td></tr>
<tr class="b">
<th width="20%">GroupId:ArtifactId:Version</th>
<td width="80%" align="left">com.nimbusds:nimbus-jose-jwt:9.37.3</td></tr>
<tr class="a">
<th width="20%">Scope</th>
<td width="80%" align="left">compile</td></tr>
<tr class="b">
<th width="20%">Classifier</th>
<td width="80%" align="left"></td></tr>
<tr class="a">
<th width="20%">Type</th>
<td width="80%" align="left">jar</td></tr>
<tr class="b">
<th width="20%">License(s)</th>
<td width="80%" align="left">The Apache Software License, Version 2.0</td></tr></table><a href="#Overview">Back to top</a><br /></section><section>
<h3><a name="com.nimbusds:oauth2-oidc-sdk:11.9.1"></a>com.nimbusds:oauth2-oidc-sdk:11.9.1</h3>
<table border="0" class="bodyTable">
<tr class="a">
<th width="20%">Status</th>
<td width="80%" align="left"><figure><img src="images/icon_success_sml.gif" alt="" /></figure>&#160;License defined in the pom of the dependency</td></tr>
<tr class="b">
<th width="20%">GroupId:ArtifactId:Version</th>
<td width="80%" align="left">com.nimbusds:oauth2-oidc-sdk:11.9.1</td></tr>
<tr class="a">
<th width="20%">Scope</th>
<td width="80%" align="left">compile</td></tr>
<tr class="b">
<th width="20%">Classifier</th>
<td width="80%" align="left"></td></tr>
<tr class="a">
<th width="20%">Type</th>
<td width="80%" align="left">jar</td></tr>
<tr class="b">
<th width="20%">License(s)</th>
<td width="80%" align="left">Apache License, version 2.0</td></tr></table><a href="#Overview">Back to top</a><br /></section><section>
<h3><a name="com.opencsv:opencsv:5.9"></a>com.opencsv:opencsv:5.9</h3>
<table border="0" class="bodyTable">
<tr class="a">
<th width="20%">Status</th>
<td width="80%" align="left"><figure><img src="images/icon_success_sml.gif" alt="" /></figure>&#160;License defined in the pom of the dependency</td></tr>
<tr class="b">
<th width="20%">GroupId:ArtifactId:Version</th>
<td width="80%" align="left">com.opencsv:opencsv:5.9</td></tr>
<tr class="a">
<th width="20%">Scope</th>
<td width="80%" align="left">compile</td></tr>
<tr class="b">
<th width="20%">Classifier</th>
<td width="80%" align="left"></td></tr>
<tr class="a">
<th width="20%">Type</th>
<td width="80%" align="left">jar</td></tr>
<tr class="b">
<th width="20%">License(s)</th>
<td width="80%" align="left">Apache 2</td></tr></table><a href="#Overview">Back to top</a><br /></section><section>
<h3><a name="com.pff:java-libpst:0.9.3"></a>com.pff:java-libpst:0.9.3</h3>
<table border="0" class="bodyTable">
<tr class="a">
<th width="20%">Status</th>
<td width="80%" align="left"><figure><img src="images/icon_success_sml.gif" alt="" /></figure>&#160;License defined in the pom of the dependency</td></tr>
<tr class="b">
<th width="20%">GroupId:ArtifactId:Version</th>
<td width="80%" align="left">com.pff:java-libpst:0.9.3</td></tr>
<tr class="a">
<th width="20%">Scope</th>
<td width="80%" align="left">compile</td></tr>
<tr class="b">
<th width="20%">Classifier</th>
<td width="80%" align="left"></td></tr>
<tr class="a">
<th width="20%">Type</th>
<td width="80%" align="left">jar</td></tr>
<tr class="b">
<th width="20%">License(s)</th>
<td width="80%" align="left">The Apache Software License, Version 2.0</td></tr></table><a href="#Overview">Back to top</a><br /></section><section>
<h3><a name="com.rometools:rome:2.1.0"></a>com.rometools:rome:2.1.0</h3>
<table border="0" class="bodyTable">
<tr class="a">
<th width="20%">Status</th>
<td width="80%" align="left"><figure><img src="images/icon_success_sml.gif" alt="" /></figure>&#160;License defined in the pom of the dependency</td></tr>
<tr class="b">
<th width="20%">GroupId:ArtifactId:Version</th>
<td width="80%" align="left">com.rometools:rome:2.1.0</td></tr>
<tr class="a">
<th width="20%">Scope</th>
<td width="80%" align="left">compile</td></tr>
<tr class="b">
<th width="20%">Classifier</th>
<td width="80%" align="left"></td></tr>
<tr class="a">
<th width="20%">Type</th>
<td width="80%" align="left">jar</td></tr>
<tr class="b">
<th width="20%">License(s)</th>
<td width="80%" align="left">The Apache Software License, Version 2.0</td></tr></table><a href="#Overview">Back to top</a><br /></section><section>
<h3><a name="com.rometools:rome-utils:2.1.0"></a>com.rometools:rome-utils:2.1.0</h3>
<table border="0" class="bodyTable">
<tr class="a">
<th width="20%">Status</th>
<td width="80%" align="left"><figure><img src="images/icon_success_sml.gif" alt="" /></figure>&#160;License defined in the pom of the dependency</td></tr>
<tr class="b">
<th width="20%">GroupId:ArtifactId:Version</th>
<td width="80%" align="left">com.rometools:rome-utils:2.1.0</td></tr>
<tr class="a">
<th width="20%">Scope</th>
<td width="80%" align="left">compile</td></tr>
<tr class="b">
<th width="20%">Classifier</th>
<td width="80%" align="left"></td></tr>
<tr class="a">
<th width="20%">Type</th>
<td width="80%" align="left">jar</td></tr>
<tr class="b">
<th width="20%">License(s)</th>
<td width="80%" align="left">The Apache Software License, Version 2.0</td></tr></table><a href="#Overview">Back to top</a><br /></section><section>
<h3><a name="com.squareup.okhttp3:okhttp:4.12.0"></a>com.squareup.okhttp3:okhttp:4.12.0</h3>
<table border="0" class="bodyTable">
<tr class="a">
<th width="20%">Status</th>
<td width="80%" align="left"><figure><img src="images/icon_success_sml.gif" alt="" /></figure>&#160;License defined in the pom of the dependency</td></tr>
<tr class="b">
<th width="20%">GroupId:ArtifactId:Version</th>
<td width="80%" align="left">com.squareup.okhttp3:okhttp:4.12.0</td></tr>
<tr class="a">
<th width="20%">Scope</th>
<td width="80%" align="left">compile</td></tr>
<tr class="b">
<th width="20%">Classifier</th>
<td width="80%" align="left"></td></tr>
<tr class="a">
<th width="20%">Type</th>
<td width="80%" align="left">jar</td></tr>
<tr class="b">
<th width="20%">License(s)</th>
<td width="80%" align="left">The Apache Software License, Version 2.0</td></tr></table><a href="#Overview">Back to top</a><br /></section><section>
<h3><a name="com.squareup.okio:okio:3.6.0"></a>com.squareup.okio:okio:3.6.0</h3>
<table border="0" class="bodyTable">
<tr class="a">
<th width="20%">Status</th>
<td width="80%" align="left"><figure><img src="images/icon_success_sml.gif" alt="" /></figure>&#160;License defined in the pom of the dependency</td></tr>
<tr class="b">
<th width="20%">GroupId:ArtifactId:Version</th>
<td width="80%" align="left">com.squareup.okio:okio:3.6.0</td></tr>
<tr class="a">
<th width="20%">Scope</th>
<td width="80%" align="left">compile</td></tr>
<tr class="b">
<th width="20%">Classifier</th>
<td width="80%" align="left"></td></tr>
<tr class="a">
<th width="20%">Type</th>
<td width="80%" align="left">jar</td></tr>
<tr class="b">
<th width="20%">License(s)</th>
<td width="80%" align="left">The Apache Software License, Version 2.0</td></tr></table><a href="#Overview">Back to top</a><br /></section><section>
<h3><a name="com.squareup.okio:okio-jvm:3.6.0"></a>com.squareup.okio:okio-jvm:3.6.0</h3>
<table border="0" class="bodyTable">
<tr class="a">
<th width="20%">Status</th>
<td width="80%" align="left"><figure><img src="images/icon_success_sml.gif" alt="" /></figure>&#160;License defined in the pom of the dependency</td></tr>
<tr class="b">
<th width="20%">GroupId:ArtifactId:Version</th>
<td width="80%" align="left">com.squareup.okio:okio-jvm:3.6.0</td></tr>
<tr class="a">
<th width="20%">Scope</th>
<td width="80%" align="left">compile</td></tr>
<tr class="b">
<th width="20%">Classifier</th>
<td width="80%" align="left"></td></tr>
<tr class="a">
<th width="20%">Type</th>
<td width="80%" align="left">jar</td></tr>
<tr class="b">
<th width="20%">License(s)</th>
<td width="80%" align="left">The Apache Software License, Version 2.0</td></tr></table><a href="#Overview">Back to top</a><br /></section><section>
<h3><a name="com.sun.istack:istack-commons-runtime:4.1.2"></a>com.sun.istack:istack-commons-runtime:4.1.2</h3>
<table border="0" class="bodyTable">
<tr class="a">
<th width="20%">Status</th>
<td width="80%" align="left"><figure><img src="images/icon_success_sml.gif" alt="" /></figure>&#160;License defined in the pom of the dependency</td></tr>
<tr class="b">
<th width="20%">GroupId:ArtifactId:Version</th>
<td width="80%" align="left">com.sun.istack:istack-commons-runtime:4.1.2</td></tr>
<tr class="a">
<th width="20%">Scope</th>
<td width="80%" align="left">compile</td></tr>
<tr class="b">
<th width="20%">Classifier</th>
<td width="80%" align="left"></td></tr>
<tr class="a">
<th width="20%">Type</th>
<td width="80%" align="left">jar</td></tr>
<tr class="b">
<th width="20%">License(s)</th>
<td width="80%" align="left">Eclipse Distribution License - v 1.0</td></tr></table><a href="#Overview">Back to top</a><br /></section><section>
<h3><a name="com.zaxxer:HikariCP:6.3.0"></a>com.zaxxer:HikariCP:6.3.0</h3>
<table border="0" class="bodyTable">
<tr class="a">
<th width="20%">Status</th>
<td width="80%" align="left"><figure><img src="images/icon_success_sml.gif" alt="" /></figure>&#160;License defined in the pom of the dependency</td></tr>
<tr class="b">
<th width="20%">GroupId:ArtifactId:Version</th>
<td width="80%" align="left">com.zaxxer:HikariCP:6.3.0</td></tr>
<tr class="a">
<th width="20%">Scope</th>
<td width="80%" align="left">compile</td></tr>
<tr class="b">
<th width="20%">Classifier</th>
<td width="80%" align="left"></td></tr>
<tr class="a">
<th width="20%">Type</th>
<td width="80%" align="left">bundle</td></tr>
<tr class="b">
<th width="20%">License(s)</th>
<td width="80%" align="left">The Apache Software License, Version 2.0</td></tr></table><a href="#Overview">Back to top</a><br /></section><section>
<h3><a name="com.zaxxer:SparseBitSet:1.3"></a>com.zaxxer:SparseBitSet:1.3</h3>
<table border="0" class="bodyTable">
<tr class="a">
<th width="20%">Status</th>
<td width="80%" align="left"><figure><img src="images/icon_success_sml.gif" alt="" /></figure>&#160;License defined in the pom of the dependency</td></tr>
<tr class="b">
<th width="20%">GroupId:ArtifactId:Version</th>
<td width="80%" align="left">com.zaxxer:SparseBitSet:1.3</td></tr>
<tr class="a">
<th width="20%">Scope</th>
<td width="80%" align="left">compile</td></tr>
<tr class="b">
<th width="20%">Classifier</th>
<td width="80%" align="left"></td></tr>
<tr class="a">
<th width="20%">Type</th>
<td width="80%" align="left">jar</td></tr>
<tr class="b">
<th width="20%">License(s)</th>
<td width="80%" align="left">The Apache Software License, Version 2.0</td></tr></table><a href="#Overview">Back to top</a><br /></section><section>
<h3><a name="commons-codec:commons-codec:1.18.0"></a>commons-codec:commons-codec:1.18.0</h3>
<table border="0" class="bodyTable">
<tr class="a">
<th width="20%">Status</th>
<td width="80%" align="left"><figure><img src="images/icon_success_sml.gif" alt="" /></figure>&#160;License defined in the pom of the dependency</td></tr>
<tr class="b">
<th width="20%">GroupId:ArtifactId:Version</th>
<td width="80%" align="left">commons-codec:commons-codec:1.18.0</td></tr>
<tr class="a">
<th width="20%">Scope</th>
<td width="80%" align="left">compile</td></tr>
<tr class="b">
<th width="20%">Classifier</th>
<td width="80%" align="left"></td></tr>
<tr class="a">
<th width="20%">Type</th>
<td width="80%" align="left">jar</td></tr>
<tr class="b">
<th width="20%">License(s)</th>
<td width="80%" align="left">Apache-2.0</td></tr></table><a href="#Overview">Back to top</a><br /></section><section>
<h3><a name="commons-io:commons-io:2.15.1"></a>commons-io:commons-io:2.15.1</h3>
<table border="0" class="bodyTable">
<tr class="a">
<th width="20%">Status</th>
<td width="80%" align="left"><figure><img src="images/icon_success_sml.gif" alt="" /></figure>&#160;License defined in the pom of the dependency</td></tr>
<tr class="b">
<th width="20%">GroupId:ArtifactId:Version</th>
<td width="80%" align="left">commons-io:commons-io:2.15.1</td></tr>
<tr class="a">
<th width="20%">Scope</th>
<td width="80%" align="left">compile</td></tr>
<tr class="b">
<th width="20%">Classifier</th>
<td width="80%" align="left"></td></tr>
<tr class="a">
<th width="20%">Type</th>
<td width="80%" align="left">jar</td></tr>
<tr class="b">
<th width="20%">License(s)</th>
<td width="80%" align="left">Apache-2.0</td></tr></table><a href="#Overview">Back to top</a><br /></section><section>
<h3><a name="commons-logging:commons-logging:1.2"></a>commons-logging:commons-logging:1.2</h3>
<table border="0" class="bodyTable">
<tr class="a">
<th width="20%">Status</th>
<td width="80%" align="left"><figure><img src="images/icon_success_sml.gif" alt="" /></figure>&#160;License defined in the pom of the dependency</td></tr>
<tr class="b">
<th width="20%">GroupId:ArtifactId:Version</th>
<td width="80%" align="left">commons-logging:commons-logging:1.2</td></tr>
<tr class="a">
<th width="20%">Scope</th>
<td width="80%" align="left">compile</td></tr>
<tr class="b">
<th width="20%">Classifier</th>
<td width="80%" align="left"></td></tr>
<tr class="a">
<th width="20%">Type</th>
<td width="80%" align="left">jar</td></tr>
<tr class="b">
<th width="20%">License(s)</th>
<td width="80%" align="left">The Apache Software License, Version 2.0</td></tr></table><a href="#Overview">Back to top</a><br /></section><section>
<h3><a name="io.github.std-uritemplate:std-uritemplate:1.0.5"></a>io.github.std-uritemplate:std-uritemplate:1.0.5</h3>
<table border="0" class="bodyTable">
<tr class="a">
<th width="20%">Status</th>
<td width="80%" align="left"><figure><img src="images/icon_success_sml.gif" alt="" /></figure>&#160;License defined in the pom of the dependency</td></tr>
<tr class="b">
<th width="20%">GroupId:ArtifactId:Version</th>
<td width="80%" align="left">io.github.std-uritemplate:std-uritemplate:1.0.5</td></tr>
<tr class="a">
<th width="20%">Scope</th>
<td width="80%" align="left">runtime</td></tr>
<tr class="b">
<th width="20%">Classifier</th>
<td width="80%" align="left"></td></tr>
<tr class="a">
<th width="20%">Type</th>
<td width="80%" align="left">jar</td></tr>
<tr class="b">
<th width="20%">License(s)</th>
<td width="80%" align="left">Apache License, Version 2.0</td></tr></table><a href="#Overview">Back to top</a><br /></section><section>
<h3><a name="io.jsonwebtoken:jjwt-api:0.12.3"></a>io.jsonwebtoken:jjwt-api:0.12.3</h3>
<table border="0" class="bodyTable">
<tr class="a">
<th width="20%">Status</th>
<td width="80%" align="left"><figure><img src="images/icon_success_sml.gif" alt="" /></figure>&#160;License defined in the pom of the dependency</td></tr>
<tr class="b">
<th width="20%">GroupId:ArtifactId:Version</th>
<td width="80%" align="left">io.jsonwebtoken:jjwt-api:0.12.3</td></tr>
<tr class="a">
<th width="20%">Scope</th>
<td width="80%" align="left">compile</td></tr>
<tr class="b">
<th width="20%">Classifier</th>
<td width="80%" align="left"></td></tr>
<tr class="a">
<th width="20%">Type</th>
<td width="80%" align="left">jar</td></tr>
<tr class="b">
<th width="20%">License(s)</th>
<td width="80%" align="left">Apache License, Version 2.0</td></tr></table><a href="#Overview">Back to top</a><br /></section><section>
<h3><a name="io.jsonwebtoken:jjwt-impl:0.12.3"></a>io.jsonwebtoken:jjwt-impl:0.12.3</h3>
<table border="0" class="bodyTable">
<tr class="a">
<th width="20%">Status</th>
<td width="80%" align="left"><figure><img src="images/icon_success_sml.gif" alt="" /></figure>&#160;License defined in the pom of the dependency</td></tr>
<tr class="b">
<th width="20%">GroupId:ArtifactId:Version</th>
<td width="80%" align="left">io.jsonwebtoken:jjwt-impl:0.12.3</td></tr>
<tr class="a">
<th width="20%">Scope</th>
<td width="80%" align="left">runtime</td></tr>
<tr class="b">
<th width="20%">Classifier</th>
<td width="80%" align="left"></td></tr>
<tr class="a">
<th width="20%">Type</th>
<td width="80%" align="left">jar</td></tr>
<tr class="b">
<th width="20%">License(s)</th>
<td width="80%" align="left">Apache License, Version 2.0</td></tr></table><a href="#Overview">Back to top</a><br /></section><section>
<h3><a name="io.jsonwebtoken:jjwt-jackson:0.12.3"></a>io.jsonwebtoken:jjwt-jackson:0.12.3</h3>
<table border="0" class="bodyTable">
<tr class="a">
<th width="20%">Status</th>
<td width="80%" align="left"><figure><img src="images/icon_success_sml.gif" alt="" /></figure>&#160;License defined in the pom of the dependency</td></tr>
<tr class="b">
<th width="20%">GroupId:ArtifactId:Version</th>
<td width="80%" align="left">io.jsonwebtoken:jjwt-jackson:0.12.3</td></tr>
<tr class="a">
<th width="20%">Scope</th>
<td width="80%" align="left">runtime</td></tr>
<tr class="b">
<th width="20%">Classifier</th>
<td width="80%" align="left"></td></tr>
<tr class="a">
<th width="20%">Type</th>
<td width="80%" align="left">jar</td></tr>
<tr class="b">
<th width="20%">License(s)</th>
<td width="80%" align="left">Apache License, Version 2.0</td></tr></table><a href="#Overview">Back to top</a><br /></section><section>
<h3><a name="io.lettuce:lettuce-core:6.5.5.RELEASE"></a>io.lettuce:lettuce-core:6.5.5.RELEASE</h3>
<table border="0" class="bodyTable">
<tr class="a">
<th width="20%">Status</th>
<td width="80%" align="left"><figure><img src="images/icon_success_sml.gif" alt="" /></figure>&#160;License defined in the pom of the dependency</td></tr>
<tr class="b">
<th width="20%">GroupId:ArtifactId:Version</th>
<td width="80%" align="left">io.lettuce:lettuce-core:6.5.5.RELEASE</td></tr>
<tr class="a">
<th width="20%">Scope</th>
<td width="80%" align="left">compile</td></tr>
<tr class="b">
<th width="20%">Classifier</th>
<td width="80%" align="left"></td></tr>
<tr class="a">
<th width="20%">Type</th>
<td width="80%" align="left">jar</td></tr>
<tr class="b">
<th width="20%">License(s)</th>
<td width="80%" align="left">MIT</td></tr></table><a href="#Overview">Back to top</a><br /></section><section>
<h3><a name="io.micrometer:context-propagation:1.1.3"></a>io.micrometer:context-propagation:1.1.3</h3>
<table border="0" class="bodyTable">
<tr class="a">
<th width="20%">Status</th>
<td width="80%" align="left"><figure><img src="images/icon_success_sml.gif" alt="" /></figure>&#160;License defined in the pom of the dependency</td></tr>
<tr class="b">
<th width="20%">GroupId:ArtifactId:Version</th>
<td width="80%" align="left">io.micrometer:context-propagation:1.1.3</td></tr>
<tr class="a">
<th width="20%">Scope</th>
<td width="80%" align="left">compile</td></tr>
<tr class="b">
<th width="20%">Classifier</th>
<td width="80%" align="left"></td></tr>
<tr class="a">
<th width="20%">Type</th>
<td width="80%" align="left">jar</td></tr>
<tr class="b">
<th width="20%">License(s)</th>
<td width="80%" align="left">The Apache Software License, Version 2.0</td></tr></table><a href="#Overview">Back to top</a><br /></section><section>
<h3><a name="io.micrometer:micrometer-commons:1.15.0"></a>io.micrometer:micrometer-commons:1.15.0</h3>
<table border="0" class="bodyTable">
<tr class="a">
<th width="20%">Status</th>
<td width="80%" align="left"><figure><img src="images/icon_success_sml.gif" alt="" /></figure>&#160;License defined in the pom of the dependency</td></tr>
<tr class="b">
<th width="20%">GroupId:ArtifactId:Version</th>
<td width="80%" align="left">io.micrometer:micrometer-commons:1.15.0</td></tr>
<tr class="a">
<th width="20%">Scope</th>
<td width="80%" align="left">compile</td></tr>
<tr class="b">
<th width="20%">Classifier</th>
<td width="80%" align="left"></td></tr>
<tr class="a">
<th width="20%">Type</th>
<td width="80%" align="left">jar</td></tr>
<tr class="b">
<th width="20%">License(s)</th>
<td width="80%" align="left">The Apache Software License, Version 2.0</td></tr></table><a href="#Overview">Back to top</a><br /></section><section>
<h3><a name="io.micrometer:micrometer-core:1.15.0"></a>io.micrometer:micrometer-core:1.15.0</h3>
<table border="0" class="bodyTable">
<tr class="a">
<th width="20%">Status</th>
<td width="80%" align="left"><figure><img src="images/icon_success_sml.gif" alt="" /></figure>&#160;License defined in the pom of the dependency</td></tr>
<tr class="b">
<th width="20%">GroupId:ArtifactId:Version</th>
<td width="80%" align="left">io.micrometer:micrometer-core:1.15.0</td></tr>
<tr class="a">
<th width="20%">Scope</th>
<td width="80%" align="left">compile</td></tr>
<tr class="b">
<th width="20%">Classifier</th>
<td width="80%" align="left"></td></tr>
<tr class="a">
<th width="20%">Type</th>
<td width="80%" align="left">jar</td></tr>
<tr class="b">
<th width="20%">License(s)</th>
<td width="80%" align="left">The Apache Software License, Version 2.0</td></tr></table><a href="#Overview">Back to top</a><br /></section><section>
<h3><a name="io.micrometer:micrometer-jakarta9:1.15.0"></a>io.micrometer:micrometer-jakarta9:1.15.0</h3>
<table border="0" class="bodyTable">
<tr class="a">
<th width="20%">Status</th>
<td width="80%" align="left"><figure><img src="images/icon_success_sml.gif" alt="" /></figure>&#160;License defined in the pom of the dependency</td></tr>
<tr class="b">
<th width="20%">GroupId:ArtifactId:Version</th>
<td width="80%" align="left">io.micrometer:micrometer-jakarta9:1.15.0</td></tr>
<tr class="a">
<th width="20%">Scope</th>
<td width="80%" align="left">compile</td></tr>
<tr class="b">
<th width="20%">Classifier</th>
<td width="80%" align="left"></td></tr>
<tr class="a">
<th width="20%">Type</th>
<td width="80%" align="left">jar</td></tr>
<tr class="b">
<th width="20%">License(s)</th>
<td width="80%" align="left">The Apache Software License, Version 2.0</td></tr></table><a href="#Overview">Back to top</a><br /></section><section>
<h3><a name="io.micrometer:micrometer-observation:1.15.0"></a>io.micrometer:micrometer-observation:1.15.0</h3>
<table border="0" class="bodyTable">
<tr class="a">
<th width="20%">Status</th>
<td width="80%" align="left"><figure><img src="images/icon_success_sml.gif" alt="" /></figure>&#160;License defined in the pom of the dependency</td></tr>
<tr class="b">
<th width="20%">GroupId:ArtifactId:Version</th>
<td width="80%" align="left">io.micrometer:micrometer-observation:1.15.0</td></tr>
<tr class="a">
<th width="20%">Scope</th>
<td width="80%" align="left">compile</td></tr>
<tr class="b">
<th width="20%">Classifier</th>
<td width="80%" align="left"></td></tr>
<tr class="a">
<th width="20%">Type</th>
<td width="80%" align="left">jar</td></tr>
<tr class="b">
<th width="20%">License(s)</th>
<td width="80%" align="left">The Apache Software License, Version 2.0</td></tr></table><a href="#Overview">Back to top</a><br /></section><section>
<h3><a name="io.micrometer:micrometer-registry-prometheus:1.15.0"></a>io.micrometer:micrometer-registry-prometheus:1.15.0</h3>
<table border="0" class="bodyTable">
<tr class="a">
<th width="20%">Status</th>
<td width="80%" align="left"><figure><img src="images/icon_success_sml.gif" alt="" /></figure>&#160;License defined in the pom of the dependency</td></tr>
<tr class="b">
<th width="20%">GroupId:ArtifactId:Version</th>
<td width="80%" align="left">io.micrometer:micrometer-registry-prometheus:1.15.0</td></tr>
<tr class="a">
<th width="20%">Scope</th>
<td width="80%" align="left">compile</td></tr>
<tr class="b">
<th width="20%">Classifier</th>
<td width="80%" align="left"></td></tr>
<tr class="a">
<th width="20%">Type</th>
<td width="80%" align="left">jar</td></tr>
<tr class="b">
<th width="20%">License(s)</th>
<td width="80%" align="left">The Apache Software License, Version 2.0</td></tr></table><a href="#Overview">Back to top</a><br /></section><section>
<h3><a name="io.micrometer:micrometer-tracing:1.5.0"></a>io.micrometer:micrometer-tracing:1.5.0</h3>
<table border="0" class="bodyTable">
<tr class="a">
<th width="20%">Status</th>
<td width="80%" align="left"><figure><img src="images/icon_success_sml.gif" alt="" /></figure>&#160;License defined in the pom of the dependency</td></tr>
<tr class="b">
<th width="20%">GroupId:ArtifactId:Version</th>
<td width="80%" align="left">io.micrometer:micrometer-tracing:1.5.0</td></tr>
<tr class="a">
<th width="20%">Scope</th>
<td width="80%" align="left">compile</td></tr>
<tr class="b">
<th width="20%">Classifier</th>
<td width="80%" align="left"></td></tr>
<tr class="a">
<th width="20%">Type</th>
<td width="80%" align="left">jar</td></tr>
<tr class="b">
<th width="20%">License(s)</th>
<td width="80%" align="left">The Apache Software License, Version 2.0</td></tr></table><a href="#Overview">Back to top</a><br /></section><section>
<h3><a name="io.micrometer:micrometer-tracing-bridge-brave:1.5.0"></a>io.micrometer:micrometer-tracing-bridge-brave:1.5.0</h3>
<table border="0" class="bodyTable">
<tr class="a">
<th width="20%">Status</th>
<td width="80%" align="left"><figure><img src="images/icon_success_sml.gif" alt="" /></figure>&#160;License defined in the pom of the dependency</td></tr>
<tr class="b">
<th width="20%">GroupId:ArtifactId:Version</th>
<td width="80%" align="left">io.micrometer:micrometer-tracing-bridge-brave:1.5.0</td></tr>
<tr class="a">
<th width="20%">Scope</th>
<td width="80%" align="left">compile</td></tr>
<tr class="b">
<th width="20%">Classifier</th>
<td width="80%" align="left"></td></tr>
<tr class="a">
<th width="20%">Type</th>
<td width="80%" align="left">jar</td></tr>
<tr class="b">
<th width="20%">License(s)</th>
<td width="80%" align="left">The Apache Software License, Version 2.0</td></tr></table><a href="#Overview">Back to top</a><br /></section><section>
<h3><a name="io.netty:netty-buffer:4.1.121.Final"></a>io.netty:netty-buffer:4.1.121.Final</h3>
<table border="0" class="bodyTable">
<tr class="a">
<th width="20%">Status</th>
<td width="80%" align="left"><figure><img src="images/icon_success_sml.gif" alt="" /></figure>&#160;License defined in the pom of the dependency</td></tr>
<tr class="b">
<th width="20%">GroupId:ArtifactId:Version</th>
<td width="80%" align="left">io.netty:netty-buffer:4.1.121.Final</td></tr>
<tr class="a">
<th width="20%">Scope</th>
<td width="80%" align="left">compile</td></tr>
<tr class="b">
<th width="20%">Classifier</th>
<td width="80%" align="left"></td></tr>
<tr class="a">
<th width="20%">Type</th>
<td width="80%" align="left">jar</td></tr>
<tr class="b">
<th width="20%">License(s)</th>
<td width="80%" align="left">Apache License, Version 2.0</td></tr></table><a href="#Overview">Back to top</a><br /></section><section>
<h3><a name="io.netty:netty-codec:4.1.121.Final"></a>io.netty:netty-codec:4.1.121.Final</h3>
<table border="0" class="bodyTable">
<tr class="a">
<th width="20%">Status</th>
<td width="80%" align="left"><figure><img src="images/icon_success_sml.gif" alt="" /></figure>&#160;License defined in the pom of the dependency</td></tr>
<tr class="b">
<th width="20%">GroupId:ArtifactId:Version</th>
<td width="80%" align="left">io.netty:netty-codec:4.1.121.Final</td></tr>
<tr class="a">
<th width="20%">Scope</th>
<td width="80%" align="left">compile</td></tr>
<tr class="b">
<th width="20%">Classifier</th>
<td width="80%" align="left"></td></tr>
<tr class="a">
<th width="20%">Type</th>
<td width="80%" align="left">jar</td></tr>
<tr class="b">
<th width="20%">License(s)</th>
<td width="80%" align="left">Apache License, Version 2.0</td></tr></table><a href="#Overview">Back to top</a><br /></section><section>
<h3><a name="io.netty:netty-codec-dns:4.1.121.Final"></a>io.netty:netty-codec-dns:4.1.121.Final</h3>
<table border="0" class="bodyTable">
<tr class="a">
<th width="20%">Status</th>
<td width="80%" align="left"><figure><img src="images/icon_success_sml.gif" alt="" /></figure>&#160;License defined in the pom of the dependency</td></tr>
<tr class="b">
<th width="20%">GroupId:ArtifactId:Version</th>
<td width="80%" align="left">io.netty:netty-codec-dns:4.1.121.Final</td></tr>
<tr class="a">
<th width="20%">Scope</th>
<td width="80%" align="left">compile</td></tr>
<tr class="b">
<th width="20%">Classifier</th>
<td width="80%" align="left"></td></tr>
<tr class="a">
<th width="20%">Type</th>
<td width="80%" align="left">jar</td></tr>
<tr class="b">
<th width="20%">License(s)</th>
<td width="80%" align="left">Apache License, Version 2.0</td></tr></table><a href="#Overview">Back to top</a><br /></section><section>
<h3><a name="io.netty:netty-codec-http:4.1.121.Final"></a>io.netty:netty-codec-http:4.1.121.Final</h3>
<table border="0" class="bodyTable">
<tr class="a">
<th width="20%">Status</th>
<td width="80%" align="left"><figure><img src="images/icon_success_sml.gif" alt="" /></figure>&#160;License defined in the pom of the dependency</td></tr>
<tr class="b">
<th width="20%">GroupId:ArtifactId:Version</th>
<td width="80%" align="left">io.netty:netty-codec-http:4.1.121.Final</td></tr>
<tr class="a">
<th width="20%">Scope</th>
<td width="80%" align="left">compile</td></tr>
<tr class="b">
<th width="20%">Classifier</th>
<td width="80%" align="left"></td></tr>
<tr class="a">
<th width="20%">Type</th>
<td width="80%" align="left">jar</td></tr>
<tr class="b">
<th width="20%">License(s)</th>
<td width="80%" align="left">Apache License, Version 2.0</td></tr></table><a href="#Overview">Back to top</a><br /></section><section>
<h3><a name="io.netty:netty-codec-http2:4.1.121.Final"></a>io.netty:netty-codec-http2:4.1.121.Final</h3>
<table border="0" class="bodyTable">
<tr class="a">
<th width="20%">Status</th>
<td width="80%" align="left"><figure><img src="images/icon_success_sml.gif" alt="" /></figure>&#160;License defined in the pom of the dependency</td></tr>
<tr class="b">
<th width="20%">GroupId:ArtifactId:Version</th>
<td width="80%" align="left">io.netty:netty-codec-http2:4.1.121.Final</td></tr>
<tr class="a">
<th width="20%">Scope</th>
<td width="80%" align="left">compile</td></tr>
<tr class="b">
<th width="20%">Classifier</th>
<td width="80%" align="left"></td></tr>
<tr class="a">
<th width="20%">Type</th>
<td width="80%" align="left">jar</td></tr>
<tr class="b">
<th width="20%">License(s)</th>
<td width="80%" align="left">Apache License, Version 2.0</td></tr></table><a href="#Overview">Back to top</a><br /></section><section>
<h3><a name="io.netty:netty-codec-socks:4.1.121.Final"></a>io.netty:netty-codec-socks:4.1.121.Final</h3>
<table border="0" class="bodyTable">
<tr class="a">
<th width="20%">Status</th>
<td width="80%" align="left"><figure><img src="images/icon_success_sml.gif" alt="" /></figure>&#160;License defined in the pom of the dependency</td></tr>
<tr class="b">
<th width="20%">GroupId:ArtifactId:Version</th>
<td width="80%" align="left">io.netty:netty-codec-socks:4.1.121.Final</td></tr>
<tr class="a">
<th width="20%">Scope</th>
<td width="80%" align="left">compile</td></tr>
<tr class="b">
<th width="20%">Classifier</th>
<td width="80%" align="left"></td></tr>
<tr class="a">
<th width="20%">Type</th>
<td width="80%" align="left">jar</td></tr>
<tr class="b">
<th width="20%">License(s)</th>
<td width="80%" align="left">Apache License, Version 2.0</td></tr></table><a href="#Overview">Back to top</a><br /></section><section>
<h3><a name="io.netty:netty-common:4.1.121.Final"></a>io.netty:netty-common:4.1.121.Final</h3>
<table border="0" class="bodyTable">
<tr class="a">
<th width="20%">Status</th>
<td width="80%" align="left"><figure><img src="images/icon_success_sml.gif" alt="" /></figure>&#160;License defined in the pom of the dependency</td></tr>
<tr class="b">
<th width="20%">GroupId:ArtifactId:Version</th>
<td width="80%" align="left">io.netty:netty-common:4.1.121.Final</td></tr>
<tr class="a">
<th width="20%">Scope</th>
<td width="80%" align="left">compile</td></tr>
<tr class="b">
<th width="20%">Classifier</th>
<td width="80%" align="left"></td></tr>
<tr class="a">
<th width="20%">Type</th>
<td width="80%" align="left">jar</td></tr>
<tr class="b">
<th width="20%">License(s)</th>
<td width="80%" align="left">Apache License, Version 2.0</td></tr></table><a href="#Overview">Back to top</a><br /></section><section>
<h3><a name="io.netty:netty-handler:4.1.121.Final"></a>io.netty:netty-handler:4.1.121.Final</h3>
<table border="0" class="bodyTable">
<tr class="a">
<th width="20%">Status</th>
<td width="80%" align="left"><figure><img src="images/icon_success_sml.gif" alt="" /></figure>&#160;License defined in the pom of the dependency</td></tr>
<tr class="b">
<th width="20%">GroupId:ArtifactId:Version</th>
<td width="80%" align="left">io.netty:netty-handler:4.1.121.Final</td></tr>
<tr class="a">
<th width="20%">Scope</th>
<td width="80%" align="left">compile</td></tr>
<tr class="b">
<th width="20%">Classifier</th>
<td width="80%" align="left"></td></tr>
<tr class="a">
<th width="20%">Type</th>
<td width="80%" align="left">jar</td></tr>
<tr class="b">
<th width="20%">License(s)</th>
<td width="80%" align="left">Apache License, Version 2.0</td></tr></table><a href="#Overview">Back to top</a><br /></section><section>
<h3><a name="io.netty:netty-handler-proxy:4.1.121.Final"></a>io.netty:netty-handler-proxy:4.1.121.Final</h3>
<table border="0" class="bodyTable">
<tr class="a">
<th width="20%">Status</th>
<td width="80%" align="left"><figure><img src="images/icon_success_sml.gif" alt="" /></figure>&#160;License defined in the pom of the dependency</td></tr>
<tr class="b">
<th width="20%">GroupId:ArtifactId:Version</th>
<td width="80%" align="left">io.netty:netty-handler-proxy:4.1.121.Final</td></tr>
<tr class="a">
<th width="20%">Scope</th>
<td width="80%" align="left">compile</td></tr>
<tr class="b">
<th width="20%">Classifier</th>
<td width="80%" align="left"></td></tr>
<tr class="a">
<th width="20%">Type</th>
<td width="80%" align="left">jar</td></tr>
<tr class="b">
<th width="20%">License(s)</th>
<td width="80%" align="left">Apache License, Version 2.0</td></tr></table><a href="#Overview">Back to top</a><br /></section><section>
<h3><a name="io.netty:netty-resolver:4.1.121.Final"></a>io.netty:netty-resolver:4.1.121.Final</h3>
<table border="0" class="bodyTable">
<tr class="a">
<th width="20%">Status</th>
<td width="80%" align="left"><figure><img src="images/icon_success_sml.gif" alt="" /></figure>&#160;License defined in the pom of the dependency</td></tr>
<tr class="b">
<th width="20%">GroupId:ArtifactId:Version</th>
<td width="80%" align="left">io.netty:netty-resolver:4.1.121.Final</td></tr>
<tr class="a">
<th width="20%">Scope</th>
<td width="80%" align="left">compile</td></tr>
<tr class="b">
<th width="20%">Classifier</th>
<td width="80%" align="left"></td></tr>
<tr class="a">
<th width="20%">Type</th>
<td width="80%" align="left">jar</td></tr>
<tr class="b">
<th width="20%">License(s)</th>
<td width="80%" align="left">Apache License, Version 2.0</td></tr></table><a href="#Overview">Back to top</a><br /></section><section>
<h3><a name="io.netty:netty-resolver-dns:4.1.121.Final"></a>io.netty:netty-resolver-dns:4.1.121.Final</h3>
<table border="0" class="bodyTable">
<tr class="a">
<th width="20%">Status</th>
<td width="80%" align="left"><figure><img src="images/icon_success_sml.gif" alt="" /></figure>&#160;License defined in the pom of the dependency</td></tr>
<tr class="b">
<th width="20%">GroupId:ArtifactId:Version</th>
<td width="80%" align="left">io.netty:netty-resolver-dns:4.1.121.Final</td></tr>
<tr class="a">
<th width="20%">Scope</th>
<td width="80%" align="left">compile</td></tr>
<tr class="b">
<th width="20%">Classifier</th>
<td width="80%" align="left"></td></tr>
<tr class="a">
<th width="20%">Type</th>
<td width="80%" align="left">jar</td></tr>
<tr class="b">
<th width="20%">License(s)</th>
<td width="80%" align="left">Apache License, Version 2.0</td></tr></table><a href="#Overview">Back to top</a><br /></section><section>
<h3><a name="io.netty:netty-resolver-dns-classes-macos:4.1.121.Final"></a>io.netty:netty-resolver-dns-classes-macos:4.1.121.Final</h3>
<table border="0" class="bodyTable">
<tr class="a">
<th width="20%">Status</th>
<td width="80%" align="left"><figure><img src="images/icon_success_sml.gif" alt="" /></figure>&#160;License defined in the pom of the dependency</td></tr>
<tr class="b">
<th width="20%">GroupId:ArtifactId:Version</th>
<td width="80%" align="left">io.netty:netty-resolver-dns-classes-macos:4.1.121.Final</td></tr>
<tr class="a">
<th width="20%">Scope</th>
<td width="80%" align="left">compile</td></tr>
<tr class="b">
<th width="20%">Classifier</th>
<td width="80%" align="left"></td></tr>
<tr class="a">
<th width="20%">Type</th>
<td width="80%" align="left">jar</td></tr>
<tr class="b">
<th width="20%">License(s)</th>
<td width="80%" align="left">Apache License, Version 2.0</td></tr></table><a href="#Overview">Back to top</a><br /></section><section>
<h3><a name="io.netty:netty-resolver-dns-native-macos:4.1.121.Final"></a>io.netty:netty-resolver-dns-native-macos:4.1.121.Final</h3>
<table border="0" class="bodyTable">
<tr class="a">
<th width="20%">Status</th>
<td width="80%" align="left"><figure><img src="images/icon_success_sml.gif" alt="" /></figure>&#160;License defined in the pom of the dependency</td></tr>
<tr class="b">
<th width="20%">GroupId:ArtifactId:Version</th>
<td width="80%" align="left">io.netty:netty-resolver-dns-native-macos:4.1.121.Final</td></tr>
<tr class="a">
<th width="20%">Scope</th>
<td width="80%" align="left">compile</td></tr>
<tr class="b">
<th width="20%">Classifier</th>
<td width="80%" align="left"></td></tr>
<tr class="a">
<th width="20%">Type</th>
<td width="80%" align="left">jar</td></tr>
<tr class="b">
<th width="20%">License(s)</th>
<td width="80%" align="left">Apache License, Version 2.0</td></tr></table><a href="#Overview">Back to top</a><br /></section><section>
<h3><a name="io.netty:netty-tcnative-boringssl-static:2.0.70.Final"></a>io.netty:netty-tcnative-boringssl-static:2.0.70.Final</h3>
<table border="0" class="bodyTable">
<tr class="a">
<th width="20%">Status</th>
<td width="80%" align="left"><figure><img src="images/icon_success_sml.gif" alt="" /></figure>&#160;License defined in the pom of the dependency</td></tr>
<tr class="b">
<th width="20%">GroupId:ArtifactId:Version</th>
<td width="80%" align="left">io.netty:netty-tcnative-boringssl-static:2.0.70.Final</td></tr>
<tr class="a">
<th width="20%">Scope</th>
<td width="80%" align="left">compile</td></tr>
<tr class="b">
<th width="20%">Classifier</th>
<td width="80%" align="left"></td></tr>
<tr class="a">
<th width="20%">Type</th>
<td width="80%" align="left">jar</td></tr>
<tr class="b">
<th width="20%">License(s)</th>
<td width="80%" align="left">The Apache Software License, Version 2.0</td></tr></table><a href="#Overview">Back to top</a><br /></section><section>
<h3><a name="io.netty:netty-tcnative-classes:2.0.70.Final"></a>io.netty:netty-tcnative-classes:2.0.70.Final</h3>
<table border="0" class="bodyTable">
<tr class="a">
<th width="20%">Status</th>
<td width="80%" align="left"><figure><img src="images/icon_success_sml.gif" alt="" /></figure>&#160;License defined in the pom of the dependency</td></tr>
<tr class="b">
<th width="20%">GroupId:ArtifactId:Version</th>
<td width="80%" align="left">io.netty:netty-tcnative-classes:2.0.70.Final</td></tr>
<tr class="a">
<th width="20%">Scope</th>
<td width="80%" align="left">compile</td></tr>
<tr class="b">
<th width="20%">Classifier</th>
<td width="80%" align="left"></td></tr>
<tr class="a">
<th width="20%">Type</th>
<td width="80%" align="left">jar</td></tr>
<tr class="b">
<th width="20%">License(s)</th>
<td width="80%" align="left">The Apache Software License, Version 2.0</td></tr></table><a href="#Overview">Back to top</a><br /></section><section>
<h3><a name="io.netty:netty-transport:4.1.121.Final"></a>io.netty:netty-transport:4.1.121.Final</h3>
<table border="0" class="bodyTable">
<tr class="a">
<th width="20%">Status</th>
<td width="80%" align="left"><figure><img src="images/icon_success_sml.gif" alt="" /></figure>&#160;License defined in the pom of the dependency</td></tr>
<tr class="b">
<th width="20%">GroupId:ArtifactId:Version</th>
<td width="80%" align="left">io.netty:netty-transport:4.1.121.Final</td></tr>
<tr class="a">
<th width="20%">Scope</th>
<td width="80%" align="left">compile</td></tr>
<tr class="b">
<th width="20%">Classifier</th>
<td width="80%" align="left"></td></tr>
<tr class="a">
<th width="20%">Type</th>
<td width="80%" align="left">jar</td></tr>
<tr class="b">
<th width="20%">License(s)</th>
<td width="80%" align="left">Apache License, Version 2.0</td></tr></table><a href="#Overview">Back to top</a><br /></section><section>
<h3><a name="io.netty:netty-transport-classes-epoll:4.1.121.Final"></a>io.netty:netty-transport-classes-epoll:4.1.121.Final</h3>
<table border="0" class="bodyTable">
<tr class="a">
<th width="20%">Status</th>
<td width="80%" align="left"><figure><img src="images/icon_success_sml.gif" alt="" /></figure>&#160;License defined in the pom of the dependency</td></tr>
<tr class="b">
<th width="20%">GroupId:ArtifactId:Version</th>
<td width="80%" align="left">io.netty:netty-transport-classes-epoll:4.1.121.Final</td></tr>
<tr class="a">
<th width="20%">Scope</th>
<td width="80%" align="left">compile</td></tr>
<tr class="b">
<th width="20%">Classifier</th>
<td width="80%" align="left"></td></tr>
<tr class="a">
<th width="20%">Type</th>
<td width="80%" align="left">jar</td></tr>
<tr class="b">
<th width="20%">License(s)</th>
<td width="80%" align="left">Apache License, Version 2.0</td></tr></table><a href="#Overview">Back to top</a><br /></section><section>
<h3><a name="io.netty:netty-transport-classes-kqueue:4.1.121.Final"></a>io.netty:netty-transport-classes-kqueue:4.1.121.Final</h3>
<table border="0" class="bodyTable">
<tr class="a">
<th width="20%">Status</th>
<td width="80%" align="left"><figure><img src="images/icon_success_sml.gif" alt="" /></figure>&#160;License defined in the pom of the dependency</td></tr>
<tr class="b">
<th width="20%">GroupId:ArtifactId:Version</th>
<td width="80%" align="left">io.netty:netty-transport-classes-kqueue:4.1.121.Final</td></tr>
<tr class="a">
<th width="20%">Scope</th>
<td width="80%" align="left">compile</td></tr>
<tr class="b">
<th width="20%">Classifier</th>
<td width="80%" align="left"></td></tr>
<tr class="a">
<th width="20%">Type</th>
<td width="80%" align="left">jar</td></tr>
<tr class="b">
<th width="20%">License(s)</th>
<td width="80%" align="left">Apache License, Version 2.0</td></tr></table><a href="#Overview">Back to top</a><br /></section><section>
<h3><a name="io.netty:netty-transport-native-epoll:4.1.121.Final"></a>io.netty:netty-transport-native-epoll:4.1.121.Final</h3>
<table border="0" class="bodyTable">
<tr class="a">
<th width="20%">Status</th>
<td width="80%" align="left"><figure><img src="images/icon_success_sml.gif" alt="" /></figure>&#160;License defined in the pom of the dependency</td></tr>
<tr class="b">
<th width="20%">GroupId:ArtifactId:Version</th>
<td width="80%" align="left">io.netty:netty-transport-native-epoll:4.1.121.Final</td></tr>
<tr class="a">
<th width="20%">Scope</th>
<td width="80%" align="left">compile</td></tr>
<tr class="b">
<th width="20%">Classifier</th>
<td width="80%" align="left"></td></tr>
<tr class="a">
<th width="20%">Type</th>
<td width="80%" align="left">jar</td></tr>
<tr class="b">
<th width="20%">License(s)</th>
<td width="80%" align="left">Apache License, Version 2.0</td></tr></table><a href="#Overview">Back to top</a><br /></section><section>
<h3><a name="io.netty:netty-transport-native-kqueue:4.1.121.Final"></a>io.netty:netty-transport-native-kqueue:4.1.121.Final</h3>
<table border="0" class="bodyTable">
<tr class="a">
<th width="20%">Status</th>
<td width="80%" align="left"><figure><img src="images/icon_success_sml.gif" alt="" /></figure>&#160;License defined in the pom of the dependency</td></tr>
<tr class="b">
<th width="20%">GroupId:ArtifactId:Version</th>
<td width="80%" align="left">io.netty:netty-transport-native-kqueue:4.1.121.Final</td></tr>
<tr class="a">
<th width="20%">Scope</th>
<td width="80%" align="left">compile</td></tr>
<tr class="b">
<th width="20%">Classifier</th>
<td width="80%" align="left"></td></tr>
<tr class="a">
<th width="20%">Type</th>
<td width="80%" align="left">jar</td></tr>
<tr class="b">
<th width="20%">License(s)</th>
<td width="80%" align="left">Apache License, Version 2.0</td></tr></table><a href="#Overview">Back to top</a><br /></section><section>
<h3><a name="io.netty:netty-transport-native-unix-common:4.1.121.Final"></a>io.netty:netty-transport-native-unix-common:4.1.121.Final</h3>
<table border="0" class="bodyTable">
<tr class="a">
<th width="20%">Status</th>
<td width="80%" align="left"><figure><img src="images/icon_success_sml.gif" alt="" /></figure>&#160;License defined in the pom of the dependency</td></tr>
<tr class="b">
<th width="20%">GroupId:ArtifactId:Version</th>
<td width="80%" align="left">io.netty:netty-transport-native-unix-common:4.1.121.Final</td></tr>
<tr class="a">
<th width="20%">Scope</th>
<td width="80%" align="left">compile</td></tr>
<tr class="b">
<th width="20%">Classifier</th>
<td width="80%" align="left"></td></tr>
<tr class="a">
<th width="20%">Type</th>
<td width="80%" align="left">jar</td></tr>
<tr class="b">
<th width="20%">License(s)</th>
<td width="80%" align="left">Apache License, Version 2.0</td></tr></table><a href="#Overview">Back to top</a><br /></section><section>
<h3><a name="io.opentelemetry:opentelemetry-api:1.49.0"></a>io.opentelemetry:opentelemetry-api:1.49.0</h3>
<table border="0" class="bodyTable">
<tr class="a">
<th width="20%">Status</th>
<td width="80%" align="left"><figure><img src="images/icon_success_sml.gif" alt="" /></figure>&#160;License defined in the pom of the dependency</td></tr>
<tr class="b">
<th width="20%">GroupId:ArtifactId:Version</th>
<td width="80%" align="left">io.opentelemetry:opentelemetry-api:1.49.0</td></tr>
<tr class="a">
<th width="20%">Scope</th>
<td width="80%" align="left">compile</td></tr>
<tr class="b">
<th width="20%">Classifier</th>
<td width="80%" align="left"></td></tr>
<tr class="a">
<th width="20%">Type</th>
<td width="80%" align="left">jar</td></tr>
<tr class="b">
<th width="20%">License(s)</th>
<td width="80%" align="left">The Apache License, Version 2.0</td></tr></table><a href="#Overview">Back to top</a><br /></section><section>
<h3><a name="io.opentelemetry:opentelemetry-api-incubator:1.44.1-alpha"></a>io.opentelemetry:opentelemetry-api-incubator:1.44.1-alpha</h3>
<table border="0" class="bodyTable">
<tr class="a">
<th width="20%">Status</th>
<td width="80%" align="left"><figure><img src="images/icon_success_sml.gif" alt="" /></figure>&#160;License defined in the pom of the dependency</td></tr>
<tr class="b">
<th width="20%">GroupId:ArtifactId:Version</th>
<td width="80%" align="left">io.opentelemetry:opentelemetry-api-incubator:1.44.1-alpha</td></tr>
<tr class="a">
<th width="20%">Scope</th>
<td width="80%" align="left">runtime</td></tr>
<tr class="b">
<th width="20%">Classifier</th>
<td width="80%" align="left"></td></tr>
<tr class="a">
<th width="20%">Type</th>
<td width="80%" align="left">jar</td></tr>
<tr class="b">
<th width="20%">License(s)</th>
<td width="80%" align="left">The Apache License, Version 2.0</td></tr></table><a href="#Overview">Back to top</a><br /></section><section>
<h3><a name="io.opentelemetry:opentelemetry-context:1.49.0"></a>io.opentelemetry:opentelemetry-context:1.49.0</h3>
<table border="0" class="bodyTable">
<tr class="a">
<th width="20%">Status</th>
<td width="80%" align="left"><figure><img src="images/icon_success_sml.gif" alt="" /></figure>&#160;License defined in the pom of the dependency</td></tr>
<tr class="b">
<th width="20%">GroupId:ArtifactId:Version</th>
<td width="80%" align="left">io.opentelemetry:opentelemetry-context:1.49.0</td></tr>
<tr class="a">
<th width="20%">Scope</th>
<td width="80%" align="left">compile</td></tr>
<tr class="b">
<th width="20%">Classifier</th>
<td width="80%" align="left"></td></tr>
<tr class="a">
<th width="20%">Type</th>
<td width="80%" align="left">jar</td></tr>
<tr class="b">
<th width="20%">License(s)</th>
<td width="80%" align="left">The Apache License, Version 2.0</td></tr></table><a href="#Overview">Back to top</a><br /></section><section>
<h3><a name="io.opentelemetry:opentelemetry-exporter-common:1.49.0"></a>io.opentelemetry:opentelemetry-exporter-common:1.49.0</h3>
<table border="0" class="bodyTable">
<tr class="a">
<th width="20%">Status</th>
<td width="80%" align="left"><figure><img src="images/icon_success_sml.gif" alt="" /></figure>&#160;License defined in the pom of the dependency</td></tr>
<tr class="b">
<th width="20%">GroupId:ArtifactId:Version</th>
<td width="80%" align="left">io.opentelemetry:opentelemetry-exporter-common:1.49.0</td></tr>
<tr class="a">
<th width="20%">Scope</th>
<td width="80%" align="left">runtime</td></tr>
<tr class="b">
<th width="20%">Classifier</th>
<td width="80%" align="left"></td></tr>
<tr class="a">
<th width="20%">Type</th>
<td width="80%" align="left">jar</td></tr>
<tr class="b">
<th width="20%">License(s)</th>
<td width="80%" align="left">The Apache License, Version 2.0</td></tr></table><a href="#Overview">Back to top</a><br /></section><section>
<h3><a name="io.opentelemetry:opentelemetry-exporter-logging:1.49.0"></a>io.opentelemetry:opentelemetry-exporter-logging:1.49.0</h3>
<table border="0" class="bodyTable">
<tr class="a">
<th width="20%">Status</th>
<td width="80%" align="left"><figure><img src="images/icon_success_sml.gif" alt="" /></figure>&#160;License defined in the pom of the dependency</td></tr>
<tr class="b">
<th width="20%">GroupId:ArtifactId:Version</th>
<td width="80%" align="left">io.opentelemetry:opentelemetry-exporter-logging:1.49.0</td></tr>
<tr class="a">
<th width="20%">Scope</th>
<td width="80%" align="left">compile</td></tr>
<tr class="b">
<th width="20%">Classifier</th>
<td width="80%" align="left"></td></tr>
<tr class="a">
<th width="20%">Type</th>
<td width="80%" align="left">jar</td></tr>
<tr class="b">
<th width="20%">License(s)</th>
<td width="80%" align="left">The Apache License, Version 2.0</td></tr></table><a href="#Overview">Back to top</a><br /></section><section>
<h3><a name="io.opentelemetry:opentelemetry-exporter-otlp:1.49.0"></a>io.opentelemetry:opentelemetry-exporter-otlp:1.49.0</h3>
<table border="0" class="bodyTable">
<tr class="a">
<th width="20%">Status</th>
<td width="80%" align="left"><figure><img src="images/icon_success_sml.gif" alt="" /></figure>&#160;License defined in the pom of the dependency</td></tr>
<tr class="b">
<th width="20%">GroupId:ArtifactId:Version</th>
<td width="80%" align="left">io.opentelemetry:opentelemetry-exporter-otlp:1.49.0</td></tr>
<tr class="a">
<th width="20%">Scope</th>
<td width="80%" align="left">compile</td></tr>
<tr class="b">
<th width="20%">Classifier</th>
<td width="80%" align="left"></td></tr>
<tr class="a">
<th width="20%">Type</th>
<td width="80%" align="left">jar</td></tr>
<tr class="b">
<th width="20%">License(s)</th>
<td width="80%" align="left">The Apache License, Version 2.0</td></tr></table><a href="#Overview">Back to top</a><br /></section><section>
<h3><a name="io.opentelemetry:opentelemetry-exporter-otlp-common:1.49.0"></a>io.opentelemetry:opentelemetry-exporter-otlp-common:1.49.0</h3>
<table border="0" class="bodyTable">
<tr class="a">
<th width="20%">Status</th>
<td width="80%" align="left"><figure><img src="images/icon_success_sml.gif" alt="" /></figure>&#160;License defined in the pom of the dependency</td></tr>
<tr class="b">
<th width="20%">GroupId:ArtifactId:Version</th>
<td width="80%" align="left">io.opentelemetry:opentelemetry-exporter-otlp-common:1.49.0</td></tr>
<tr class="a">
<th width="20%">Scope</th>
<td width="80%" align="left">runtime</td></tr>
<tr class="b">
<th width="20%">Classifier</th>
<td width="80%" align="left"></td></tr>
<tr class="a">
<th width="20%">Type</th>
<td width="80%" align="left">jar</td></tr>
<tr class="b">
<th width="20%">License(s)</th>
<td width="80%" align="left">The Apache License, Version 2.0</td></tr></table><a href="#Overview">Back to top</a><br /></section><section>
<h3><a name="io.opentelemetry:opentelemetry-exporter-sender-okhttp:1.49.0"></a>io.opentelemetry:opentelemetry-exporter-sender-okhttp:1.49.0</h3>
<table border="0" class="bodyTable">
<tr class="a">
<th width="20%">Status</th>
<td width="80%" align="left"><figure><img src="images/icon_success_sml.gif" alt="" /></figure>&#160;License defined in the pom of the dependency</td></tr>
<tr class="b">
<th width="20%">GroupId:ArtifactId:Version</th>
<td width="80%" align="left">io.opentelemetry:opentelemetry-exporter-sender-okhttp:1.49.0</td></tr>
<tr class="a">
<th width="20%">Scope</th>
<td width="80%" align="left">runtime</td></tr>
<tr class="b">
<th width="20%">Classifier</th>
<td width="80%" align="left"></td></tr>
<tr class="a">
<th width="20%">Type</th>
<td width="80%" align="left">jar</td></tr>
<tr class="b">
<th width="20%">License(s)</th>
<td width="80%" align="left">The Apache License, Version 2.0</td></tr></table><a href="#Overview">Back to top</a><br /></section><section>
<h3><a name="io.opentelemetry:opentelemetry-exporter-zipkin:1.44.1"></a>io.opentelemetry:opentelemetry-exporter-zipkin:1.44.1</h3>
<table border="0" class="bodyTable">
<tr class="a">
<th width="20%">Status</th>
<td width="80%" align="left"><figure><img src="images/icon_success_sml.gif" alt="" /></figure>&#160;License defined in the pom of the dependency</td></tr>
<tr class="b">
<th width="20%">GroupId:ArtifactId:Version</th>
<td width="80%" align="left">io.opentelemetry:opentelemetry-exporter-zipkin:1.44.1</td></tr>
<tr class="a">
<th width="20%">Scope</th>
<td width="80%" align="left">compile</td></tr>
<tr class="b">
<th width="20%">Classifier</th>
<td width="80%" align="left"></td></tr>
<tr class="a">
<th width="20%">Type</th>
<td width="80%" align="left">jar</td></tr>
<tr class="b">
<th width="20%">License(s)</th>
<td width="80%" align="left">The Apache License, Version 2.0</td></tr></table><a href="#Overview">Back to top</a><br /></section><section>
<h3><a name="io.opentelemetry:opentelemetry-sdk:1.49.0"></a>io.opentelemetry:opentelemetry-sdk:1.49.0</h3>
<table border="0" class="bodyTable">
<tr class="a">
<th width="20%">Status</th>
<td width="80%" align="left"><figure><img src="images/icon_success_sml.gif" alt="" /></figure>&#160;License defined in the pom of the dependency</td></tr>
<tr class="b">
<th width="20%">GroupId:ArtifactId:Version</th>
<td width="80%" align="left">io.opentelemetry:opentelemetry-sdk:1.49.0</td></tr>
<tr class="a">
<th width="20%">Scope</th>
<td width="80%" align="left">compile</td></tr>
<tr class="b">
<th width="20%">Classifier</th>
<td width="80%" align="left"></td></tr>
<tr class="a">
<th width="20%">Type</th>
<td width="80%" align="left">jar</td></tr>
<tr class="b">
<th width="20%">License(s)</th>
<td width="80%" align="left">The Apache License, Version 2.0</td></tr></table><a href="#Overview">Back to top</a><br /></section><section>
<h3><a name="io.opentelemetry:opentelemetry-sdk-common:1.49.0"></a>io.opentelemetry:opentelemetry-sdk-common:1.49.0</h3>
<table border="0" class="bodyTable">
<tr class="a">
<th width="20%">Status</th>
<td width="80%" align="left"><figure><img src="images/icon_success_sml.gif" alt="" /></figure>&#160;License defined in the pom of the dependency</td></tr>
<tr class="b">
<th width="20%">GroupId:ArtifactId:Version</th>
<td width="80%" align="left">io.opentelemetry:opentelemetry-sdk-common:1.49.0</td></tr>
<tr class="a">
<th width="20%">Scope</th>
<td width="80%" align="left">compile</td></tr>
<tr class="b">
<th width="20%">Classifier</th>
<td width="80%" align="left"></td></tr>
<tr class="a">
<th width="20%">Type</th>
<td width="80%" align="left">jar</td></tr>
<tr class="b">
<th width="20%">License(s)</th>
<td width="80%" align="left">The Apache License, Version 2.0</td></tr></table><a href="#Overview">Back to top</a><br /></section><section>
<h3><a name="io.opentelemetry:opentelemetry-sdk-extension-autoconfigure:1.49.0"></a>io.opentelemetry:opentelemetry-sdk-extension-autoconfigure:1.49.0</h3>
<table border="0" class="bodyTable">
<tr class="a">
<th width="20%">Status</th>
<td width="80%" align="left"><figure><img src="images/icon_success_sml.gif" alt="" /></figure>&#160;License defined in the pom of the dependency</td></tr>
<tr class="b">
<th width="20%">GroupId:ArtifactId:Version</th>
<td width="80%" align="left">io.opentelemetry:opentelemetry-sdk-extension-autoconfigure:1.49.0</td></tr>
<tr class="a">
<th width="20%">Scope</th>
<td width="80%" align="left">runtime</td></tr>
<tr class="b">
<th width="20%">Classifier</th>
<td width="80%" align="left"></td></tr>
<tr class="a">
<th width="20%">Type</th>
<td width="80%" align="left">jar</td></tr>
<tr class="b">
<th width="20%">License(s)</th>
<td width="80%" align="left">The Apache License, Version 2.0</td></tr></table><a href="#Overview">Back to top</a><br /></section><section>
<h3><a name="io.opentelemetry:opentelemetry-sdk-extension-autoconfigure-spi:1.49.0"></a>io.opentelemetry:opentelemetry-sdk-extension-autoconfigure-spi:1.49.0</h3>
<table border="0" class="bodyTable">
<tr class="a">
<th width="20%">Status</th>
<td width="80%" align="left"><figure><img src="images/icon_success_sml.gif" alt="" /></figure>&#160;License defined in the pom of the dependency</td></tr>
<tr class="b">
<th width="20%">GroupId:ArtifactId:Version</th>
<td width="80%" align="left">io.opentelemetry:opentelemetry-sdk-extension-autoconfigure-spi:1.49.0</td></tr>
<tr class="a">
<th width="20%">Scope</th>
<td width="80%" align="left">compile</td></tr>
<tr class="b">
<th width="20%">Classifier</th>
<td width="80%" align="left"></td></tr>
<tr class="a">
<th width="20%">Type</th>
<td width="80%" align="left">jar</td></tr>
<tr class="b">
<th width="20%">License(s)</th>
<td width="80%" align="left">The Apache License, Version 2.0</td></tr></table><a href="#Overview">Back to top</a><br /></section><section>
<h3><a name="io.opentelemetry:opentelemetry-sdk-extension-incubator:1.44.1-alpha"></a>io.opentelemetry:opentelemetry-sdk-extension-incubator:1.44.1-alpha</h3>
<table border="0" class="bodyTable">
<tr class="a">
<th width="20%">Status</th>
<td width="80%" align="left"><figure><img src="images/icon_success_sml.gif" alt="" /></figure>&#160;License defined in the pom of the dependency</td></tr>
<tr class="b">
<th width="20%">GroupId:ArtifactId:Version</th>
<td width="80%" align="left">io.opentelemetry:opentelemetry-sdk-extension-incubator:1.44.1-alpha</td></tr>
<tr class="a">
<th width="20%">Scope</th>
<td width="80%" align="left">runtime</td></tr>
<tr class="b">
<th width="20%">Classifier</th>
<td width="80%" align="left"></td></tr>
<tr class="a">
<th width="20%">Type</th>
<td width="80%" align="left">jar</td></tr>
<tr class="b">
<th width="20%">License(s)</th>
<td width="80%" align="left">The Apache License, Version 2.0</td></tr></table><a href="#Overview">Back to top</a><br /></section><section>
<h3><a name="io.opentelemetry:opentelemetry-sdk-logs:1.49.0"></a>io.opentelemetry:opentelemetry-sdk-logs:1.49.0</h3>
<table border="0" class="bodyTable">
<tr class="a">
<th width="20%">Status</th>
<td width="80%" align="left"><figure><img src="images/icon_success_sml.gif" alt="" /></figure>&#160;License defined in the pom of the dependency</td></tr>
<tr class="b">
<th width="20%">GroupId:ArtifactId:Version</th>
<td width="80%" align="left">io.opentelemetry:opentelemetry-sdk-logs:1.49.0</td></tr>
<tr class="a">
<th width="20%">Scope</th>
<td width="80%" align="left">compile</td></tr>
<tr class="b">
<th width="20%">Classifier</th>
<td width="80%" align="left"></td></tr>
<tr class="a">
<th width="20%">Type</th>
<td width="80%" align="left">jar</td></tr>
<tr class="b">
<th width="20%">License(s)</th>
<td width="80%" align="left">The Apache License, Version 2.0</td></tr></table><a href="#Overview">Back to top</a><br /></section><section>
<h3><a name="io.opentelemetry:opentelemetry-sdk-metrics:1.49.0"></a>io.opentelemetry:opentelemetry-sdk-metrics:1.49.0</h3>
<table border="0" class="bodyTable">
<tr class="a">
<th width="20%">Status</th>
<td width="80%" align="left"><figure><img src="images/icon_success_sml.gif" alt="" /></figure>&#160;License defined in the pom of the dependency</td></tr>
<tr class="b">
<th width="20%">GroupId:ArtifactId:Version</th>
<td width="80%" align="left">io.opentelemetry:opentelemetry-sdk-metrics:1.49.0</td></tr>
<tr class="a">
<th width="20%">Scope</th>
<td width="80%" align="left">compile</td></tr>
<tr class="b">
<th width="20%">Classifier</th>
<td width="80%" align="left"></td></tr>
<tr class="a">
<th width="20%">Type</th>
<td width="80%" align="left">jar</td></tr>
<tr class="b">
<th width="20%">License(s)</th>
<td width="80%" align="left">The Apache License, Version 2.0</td></tr></table><a href="#Overview">Back to top</a><br /></section><section>
<h3><a name="io.opentelemetry:opentelemetry-sdk-trace:1.49.0"></a>io.opentelemetry:opentelemetry-sdk-trace:1.49.0</h3>
<table border="0" class="bodyTable">
<tr class="a">
<th width="20%">Status</th>
<td width="80%" align="left"><figure><img src="images/icon_success_sml.gif" alt="" /></figure>&#160;License defined in the pom of the dependency</td></tr>
<tr class="b">
<th width="20%">GroupId:ArtifactId:Version</th>
<td width="80%" align="left">io.opentelemetry:opentelemetry-sdk-trace:1.49.0</td></tr>
<tr class="a">
<th width="20%">Scope</th>
<td width="80%" align="left">compile</td></tr>
<tr class="b">
<th width="20%">Classifier</th>
<td width="80%" align="left"></td></tr>
<tr class="a">
<th width="20%">Type</th>
<td width="80%" align="left">jar</td></tr>
<tr class="b">
<th width="20%">License(s)</th>
<td width="80%" align="left">The Apache License, Version 2.0</td></tr></table><a href="#Overview">Back to top</a><br /></section><section>
<h3><a name="io.opentelemetry.contrib:opentelemetry-aws-resources:1.40.0-alpha"></a>io.opentelemetry.contrib:opentelemetry-aws-resources:1.40.0-alpha</h3>
<table border="0" class="bodyTable">
<tr class="a">
<th width="20%">Status</th>
<td width="80%" align="left"><figure><img src="images/icon_success_sml.gif" alt="" /></figure>&#160;License defined in the pom of the dependency</td></tr>
<tr class="b">
<th width="20%">GroupId:ArtifactId:Version</th>
<td width="80%" align="left">io.opentelemetry.contrib:opentelemetry-aws-resources:1.40.0-alpha</td></tr>
<tr class="a">
<th width="20%">Scope</th>
<td width="80%" align="left">runtime</td></tr>
<tr class="b">
<th width="20%">Classifier</th>
<td width="80%" align="left"></td></tr>
<tr class="a">
<th width="20%">Type</th>
<td width="80%" align="left">jar</td></tr>
<tr class="b">
<th width="20%">License(s)</th>
<td width="80%" align="left">The Apache License, Version 2.0</td></tr></table><a href="#Overview">Back to top</a><br /></section><section>
<h3><a name="io.opentelemetry.contrib:opentelemetry-baggage-processor:1.40.0-alpha"></a>io.opentelemetry.contrib:opentelemetry-baggage-processor:1.40.0-alpha</h3>
<table border="0" class="bodyTable">
<tr class="a">
<th width="20%">Status</th>
<td width="80%" align="left"><figure><img src="images/icon_success_sml.gif" alt="" /></figure>&#160;License defined in the pom of the dependency</td></tr>
<tr class="b">
<th width="20%">GroupId:ArtifactId:Version</th>
<td width="80%" align="left">io.opentelemetry.contrib:opentelemetry-baggage-processor:1.40.0-alpha</td></tr>
<tr class="a">
<th width="20%">Scope</th>
<td width="80%" align="left">runtime</td></tr>
<tr class="b">
<th width="20%">Classifier</th>
<td width="80%" align="left"></td></tr>
<tr class="a">
<th width="20%">Type</th>
<td width="80%" align="left">jar</td></tr>
<tr class="b">
<th width="20%">License(s)</th>
<td width="80%" align="left">The Apache License, Version 2.0</td></tr></table><a href="#Overview">Back to top</a><br /></section><section>
<h3><a name="io.opentelemetry.contrib:opentelemetry-gcp-resources:1.40.0-alpha"></a>io.opentelemetry.contrib:opentelemetry-gcp-resources:1.40.0-alpha</h3>
<table border="0" class="bodyTable">
<tr class="a">
<th width="20%">Status</th>
<td width="80%" align="left"><figure><img src="images/icon_success_sml.gif" alt="" /></figure>&#160;License defined in the pom of the dependency</td></tr>
<tr class="b">
<th width="20%">GroupId:ArtifactId:Version</th>
<td width="80%" align="left">io.opentelemetry.contrib:opentelemetry-gcp-resources:1.40.0-alpha</td></tr>
<tr class="a">
<th width="20%">Scope</th>
<td width="80%" align="left">runtime</td></tr>
<tr class="b">
<th width="20%">Classifier</th>
<td width="80%" align="left"></td></tr>
<tr class="a">
<th width="20%">Type</th>
<td width="80%" align="left">jar</td></tr>
<tr class="b">
<th width="20%">License(s)</th>
<td width="80%" align="left">The Apache License, Version 2.0</td></tr></table><a href="#Overview">Back to top</a><br /></section><section>
<h3><a name="io.opentelemetry.instrumentation:opentelemetry-instrumentation-annotations:2.10.0"></a>io.opentelemetry.instrumentation:opentelemetry-instrumentation-annotations:2.10.0</h3>
<table border="0" class="bodyTable">
<tr class="a">
<th width="20%">Status</th>
<td width="80%" align="left"><figure><img src="images/icon_success_sml.gif" alt="" /></figure>&#160;License defined in the pom of the dependency</td></tr>
<tr class="b">
<th width="20%">GroupId:ArtifactId:Version</th>
<td width="80%" align="left">io.opentelemetry.instrumentation:opentelemetry-instrumentation-annotations:2.10.0</td></tr>
<tr class="a">
<th width="20%">Scope</th>
<td width="80%" align="left">compile</td></tr>
<tr class="b">
<th width="20%">Classifier</th>
<td width="80%" align="left"></td></tr>
<tr class="a">
<th width="20%">Type</th>
<td width="80%" align="left">jar</td></tr>
<tr class="b">
<th width="20%">License(s)</th>
<td width="80%" align="left">The Apache License, Version 2.0</td></tr></table><a href="#Overview">Back to top</a><br /></section><section>
<h3><a name="io.opentelemetry.instrumentation:opentelemetry-instrumentation-annotations-support:2.10.0-alpha"></a>io.opentelemetry.instrumentation:opentelemetry-instrumentation-annotations-support:2.10.0-alpha</h3>
<table border="0" class="bodyTable">
<tr class="a">
<th width="20%">Status</th>
<td width="80%" align="left"><figure><img src="images/icon_success_sml.gif" alt="" /></figure>&#160;License defined in the pom of the dependency</td></tr>
<tr class="b">
<th width="20%">GroupId:ArtifactId:Version</th>
<td width="80%" align="left">io.opentelemetry.instrumentation:opentelemetry-instrumentation-annotations-support:2.10.0-alpha</td></tr>
<tr class="a">
<th width="20%">Scope</th>
<td width="80%" align="left">runtime</td></tr>
<tr class="b">
<th width="20%">Classifier</th>
<td width="80%" align="left"></td></tr>
<tr class="a">
<th width="20%">Type</th>
<td width="80%" align="left">jar</td></tr>
<tr class="b">
<th width="20%">License(s)</th>
<td width="80%" align="left">The Apache License, Version 2.0</td></tr></table><a href="#Overview">Back to top</a><br /></section><section>
<h3><a name="io.opentelemetry.instrumentation:opentelemetry-instrumentation-api:2.10.0"></a>io.opentelemetry.instrumentation:opentelemetry-instrumentation-api:2.10.0</h3>
<table border="0" class="bodyTable">
<tr class="a">
<th width="20%">Status</th>
<td width="80%" align="left"><figure><img src="images/icon_success_sml.gif" alt="" /></figure>&#160;License defined in the pom of the dependency</td></tr>
<tr class="b">
<th width="20%">GroupId:ArtifactId:Version</th>
<td width="80%" align="left">io.opentelemetry.instrumentation:opentelemetry-instrumentation-api:2.10.0</td></tr>
<tr class="a">
<th width="20%">Scope</th>
<td width="80%" align="left">compile</td></tr>
<tr class="b">
<th width="20%">Classifier</th>
<td width="80%" align="left"></td></tr>
<tr class="a">
<th width="20%">Type</th>
<td width="80%" align="left">jar</td></tr>
<tr class="b">
<th width="20%">License(s)</th>
<td width="80%" align="left">The Apache License, Version 2.0</td></tr></table><a href="#Overview">Back to top</a><br /></section><section>
<h3><a name="io.opentelemetry.instrumentation:opentelemetry-instrumentation-api-incubator:2.10.0-alpha"></a>io.opentelemetry.instrumentation:opentelemetry-instrumentation-api-incubator:2.10.0-alpha</h3>
<table border="0" class="bodyTable">
<tr class="a">
<th width="20%">Status</th>
<td width="80%" align="left"><figure><img src="images/icon_success_sml.gif" alt="" /></figure>&#160;License defined in the pom of the dependency</td></tr>
<tr class="b">
<th width="20%">GroupId:ArtifactId:Version</th>
<td width="80%" align="left">io.opentelemetry.instrumentation:opentelemetry-instrumentation-api-incubator:2.10.0-alpha</td></tr>
<tr class="a">
<th width="20%">Scope</th>
<td width="80%" align="left">compile</td></tr>
<tr class="b">
<th width="20%">Classifier</th>
<td width="80%" align="left"></td></tr>
<tr class="a">
<th width="20%">Type</th>
<td width="80%" align="left">jar</td></tr>
<tr class="b">
<th width="20%">License(s)</th>
<td width="80%" align="left">The Apache License, Version 2.0</td></tr></table><a href="#Overview">Back to top</a><br /></section><section>
<h3><a name="io.opentelemetry.instrumentation:opentelemetry-jdbc:2.10.0-alpha"></a>io.opentelemetry.instrumentation:opentelemetry-jdbc:2.10.0-alpha</h3>
<table border="0" class="bodyTable">
<tr class="a">
<th width="20%">Status</th>
<td width="80%" align="left"><figure><img src="images/icon_success_sml.gif" alt="" /></figure>&#160;License defined in the pom of the dependency</td></tr>
<tr class="b">
<th width="20%">GroupId:ArtifactId:Version</th>
<td width="80%" align="left">io.opentelemetry.instrumentation:opentelemetry-jdbc:2.10.0-alpha</td></tr>
<tr class="a">
<th width="20%">Scope</th>
<td width="80%" align="left">runtime</td></tr>
<tr class="b">
<th width="20%">Classifier</th>
<td width="80%" align="left"></td></tr>
<tr class="a">
<th width="20%">Type</th>
<td width="80%" align="left">jar</td></tr>
<tr class="b">
<th width="20%">License(s)</th>
<td width="80%" align="left">The Apache License, Version 2.0</td></tr></table><a href="#Overview">Back to top</a><br /></section><section>
<h3><a name="io.opentelemetry.instrumentation:opentelemetry-kafka-clients-2.6:2.10.0-alpha"></a>io.opentelemetry.instrumentation:opentelemetry-kafka-clients-2.6:2.10.0-alpha</h3>
<table border="0" class="bodyTable">
<tr class="a">
<th width="20%">Status</th>
<td width="80%" align="left"><figure><img src="images/icon_success_sml.gif" alt="" /></figure>&#160;License defined in the pom of the dependency</td></tr>
<tr class="b">
<th width="20%">GroupId:ArtifactId:Version</th>
<td width="80%" align="left">io.opentelemetry.instrumentation:opentelemetry-kafka-clients-2.6:2.10.0-alpha</td></tr>
<tr class="a">
<th width="20%">Scope</th>
<td width="80%" align="left">runtime</td></tr>
<tr class="b">
<th width="20%">Classifier</th>
<td width="80%" align="left"></td></tr>
<tr class="a">
<th width="20%">Type</th>
<td width="80%" align="left">jar</td></tr>
<tr class="b">
<th width="20%">License(s)</th>
<td width="80%" align="left">The Apache License, Version 2.0</td></tr></table><a href="#Overview">Back to top</a><br /></section><section>
<h3><a name="io.opentelemetry.instrumentation:opentelemetry-kafka-clients-common:2.10.0-alpha"></a>io.opentelemetry.instrumentation:opentelemetry-kafka-clients-common:2.10.0-alpha</h3>
<table border="0" class="bodyTable">
<tr class="a">
<th width="20%">Status</th>
<td width="80%" align="left"><figure><img src="images/icon_success_sml.gif" alt="" /></figure>&#160;License defined in the pom of the dependency</td></tr>
<tr class="b">
<th width="20%">GroupId:ArtifactId:Version</th>
<td width="80%" align="left">io.opentelemetry.instrumentation:opentelemetry-kafka-clients-common:2.10.0-alpha</td></tr>
<tr class="a">
<th width="20%">Scope</th>
<td width="80%" align="left">runtime</td></tr>
<tr class="b">
<th width="20%">Classifier</th>
<td width="80%" align="left"></td></tr>
<tr class="a">
<th width="20%">Type</th>
<td width="80%" align="left">jar</td></tr>
<tr class="b">
<th width="20%">License(s)</th>
<td width="80%" align="left">The Apache License, Version 2.0</td></tr></table><a href="#Overview">Back to top</a><br /></section><section>
<h3><a name="io.opentelemetry.instrumentation:opentelemetry-log4j-appender-2.17:2.10.0-alpha"></a>io.opentelemetry.instrumentation:opentelemetry-log4j-appender-2.17:2.10.0-alpha</h3>
<table border="0" class="bodyTable">
<tr class="a">
<th width="20%">Status</th>
<td width="80%" align="left"><figure><img src="images/icon_success_sml.gif" alt="" /></figure>&#160;License defined in the pom of the dependency</td></tr>
<tr class="b">
<th width="20%">GroupId:ArtifactId:Version</th>
<td width="80%" align="left">io.opentelemetry.instrumentation:opentelemetry-log4j-appender-2.17:2.10.0-alpha</td></tr>
<tr class="a">
<th width="20%">Scope</th>
<td width="80%" align="left">runtime</td></tr>
<tr class="b">
<th width="20%">Classifier</th>
<td width="80%" align="left"></td></tr>
<tr class="a">
<th width="20%">Type</th>
<td width="80%" align="left">jar</td></tr>
<tr class="b">
<th width="20%">License(s)</th>
<td width="80%" align="left">The Apache License, Version 2.0</td></tr></table><a href="#Overview">Back to top</a><br /></section><section>
<h3><a name="io.opentelemetry.instrumentation:opentelemetry-log4j-context-data-2.17-autoconfigure:2.10.0-alpha"></a>io.opentelemetry.instrumentation:opentelemetry-log4j-context-data-2.17-autoconfigure:2.10.0-alpha</h3>
<table border="0" class="bodyTable">
<tr class="a">
<th width="20%">Status</th>
<td width="80%" align="left"><figure><img src="images/icon_success_sml.gif" alt="" /></figure>&#160;License defined in the pom of the dependency</td></tr>
<tr class="b">
<th width="20%">GroupId:ArtifactId:Version</th>
<td width="80%" align="left">io.opentelemetry.instrumentation:opentelemetry-log4j-context-data-2.17-autoconfigure:2.10.0-alpha</td></tr>
<tr class="a">
<th width="20%">Scope</th>
<td width="80%" align="left">runtime</td></tr>
<tr class="b">
<th width="20%">Classifier</th>
<td width="80%" align="left"></td></tr>
<tr class="a">
<th width="20%">Type</th>
<td width="80%" align="left">jar</td></tr>
<tr class="b">
<th width="20%">License(s)</th>
<td width="80%" align="left">The Apache License, Version 2.0</td></tr></table><a href="#Overview">Back to top</a><br /></section><section>
<h3><a name="io.opentelemetry.instrumentation:opentelemetry-logback-appender-1.0:2.10.0-alpha"></a>io.opentelemetry.instrumentation:opentelemetry-logback-appender-1.0:2.10.0-alpha</h3>
<table border="0" class="bodyTable">
<tr class="a">
<th width="20%">Status</th>
<td width="80%" align="left"><figure><img src="images/icon_success_sml.gif" alt="" /></figure>&#160;License defined in the pom of the dependency</td></tr>
<tr class="b">
<th width="20%">GroupId:ArtifactId:Version</th>
<td width="80%" align="left">io.opentelemetry.instrumentation:opentelemetry-logback-appender-1.0:2.10.0-alpha</td></tr>
<tr class="a">
<th width="20%">Scope</th>
<td width="80%" align="left">runtime</td></tr>
<tr class="b">
<th width="20%">Classifier</th>
<td width="80%" align="left"></td></tr>
<tr class="a">
<th width="20%">Type</th>
<td width="80%" align="left">jar</td></tr>
<tr class="b">
<th width="20%">License(s)</th>
<td width="80%" align="left">The Apache License, Version 2.0</td></tr></table><a href="#Overview">Back to top</a><br /></section><section>
<h3><a name="io.opentelemetry.instrumentation:opentelemetry-logback-mdc-1.0:2.10.0-alpha"></a>io.opentelemetry.instrumentation:opentelemetry-logback-mdc-1.0:2.10.0-alpha</h3>
<table border="0" class="bodyTable">
<tr class="a">
<th width="20%">Status</th>
<td width="80%" align="left"><figure><img src="images/icon_success_sml.gif" alt="" /></figure>&#160;License defined in the pom of the dependency</td></tr>
<tr class="b">
<th width="20%">GroupId:ArtifactId:Version</th>
<td width="80%" align="left">io.opentelemetry.instrumentation:opentelemetry-logback-mdc-1.0:2.10.0-alpha</td></tr>
<tr class="a">
<th width="20%">Scope</th>
<td width="80%" align="left">runtime</td></tr>
<tr class="b">
<th width="20%">Classifier</th>
<td width="80%" align="left"></td></tr>
<tr class="a">
<th width="20%">Type</th>
<td width="80%" align="left">jar</td></tr>
<tr class="b">
<th width="20%">License(s)</th>
<td width="80%" align="left">The Apache License, Version 2.0</td></tr></table><a href="#Overview">Back to top</a><br /></section><section>
<h3><a name="io.opentelemetry.instrumentation:opentelemetry-micrometer-1.5:2.10.0-alpha"></a>io.opentelemetry.instrumentation:opentelemetry-micrometer-1.5:2.10.0-alpha</h3>
<table border="0" class="bodyTable">
<tr class="a">
<th width="20%">Status</th>
<td width="80%" align="left"><figure><img src="images/icon_success_sml.gif" alt="" /></figure>&#160;License defined in the pom of the dependency</td></tr>
<tr class="b">
<th width="20%">GroupId:ArtifactId:Version</th>
<td width="80%" align="left">io.opentelemetry.instrumentation:opentelemetry-micrometer-1.5:2.10.0-alpha</td></tr>
<tr class="a">
<th width="20%">Scope</th>
<td width="80%" align="left">runtime</td></tr>
<tr class="b">
<th width="20%">Classifier</th>
<td width="80%" align="left"></td></tr>
<tr class="a">
<th width="20%">Type</th>
<td width="80%" align="left">jar</td></tr>
<tr class="b">
<th width="20%">License(s)</th>
<td width="80%" align="left">The Apache License, Version 2.0</td></tr></table><a href="#Overview">Back to top</a><br /></section><section>
<h3><a name="io.opentelemetry.instrumentation:opentelemetry-mongo-3.1:2.10.0-alpha"></a>io.opentelemetry.instrumentation:opentelemetry-mongo-3.1:2.10.0-alpha</h3>
<table border="0" class="bodyTable">
<tr class="a">
<th width="20%">Status</th>
<td width="80%" align="left"><figure><img src="images/icon_success_sml.gif" alt="" /></figure>&#160;License defined in the pom of the dependency</td></tr>
<tr class="b">
<th width="20%">GroupId:ArtifactId:Version</th>
<td width="80%" align="left">io.opentelemetry.instrumentation:opentelemetry-mongo-3.1:2.10.0-alpha</td></tr>
<tr class="a">
<th width="20%">Scope</th>
<td width="80%" align="left">runtime</td></tr>
<tr class="b">
<th width="20%">Classifier</th>
<td width="80%" align="left"></td></tr>
<tr class="a">
<th width="20%">Type</th>
<td width="80%" align="left">jar</td></tr>
<tr class="b">
<th width="20%">License(s)</th>
<td width="80%" align="left">The Apache License, Version 2.0</td></tr></table><a href="#Overview">Back to top</a><br /></section><section>
<h3><a name="io.opentelemetry.instrumentation:opentelemetry-reactor-3.1:2.10.0-alpha"></a>io.opentelemetry.instrumentation:opentelemetry-reactor-3.1:2.10.0-alpha</h3>
<table border="0" class="bodyTable">
<tr class="a">
<th width="20%">Status</th>
<td width="80%" align="left"><figure><img src="images/icon_success_sml.gif" alt="" /></figure>&#160;License defined in the pom of the dependency</td></tr>
<tr class="b">
<th width="20%">GroupId:ArtifactId:Version</th>
<td width="80%" align="left">io.opentelemetry.instrumentation:opentelemetry-reactor-3.1:2.10.0-alpha</td></tr>
<tr class="a">
<th width="20%">Scope</th>
<td width="80%" align="left">runtime</td></tr>
<tr class="b">
<th width="20%">Classifier</th>
<td width="80%" align="left"></td></tr>
<tr class="a">
<th width="20%">Type</th>
<td width="80%" align="left">jar</td></tr>
<tr class="b">
<th width="20%">License(s)</th>
<td width="80%" align="left">The Apache License, Version 2.0</td></tr></table><a href="#Overview">Back to top</a><br /></section><section>
<h3><a name="io.opentelemetry.instrumentation:opentelemetry-resources:2.10.0-alpha"></a>io.opentelemetry.instrumentation:opentelemetry-resources:2.10.0-alpha</h3>
<table border="0" class="bodyTable">
<tr class="a">
<th width="20%">Status</th>
<td width="80%" align="left"><figure><img src="images/icon_success_sml.gif" alt="" /></figure>&#160;License defined in the pom of the dependency</td></tr>
<tr class="b">
<th width="20%">GroupId:ArtifactId:Version</th>
<td width="80%" align="left">io.opentelemetry.instrumentation:opentelemetry-resources:2.10.0-alpha</td></tr>
<tr class="a">
<th width="20%">Scope</th>
<td width="80%" align="left">runtime</td></tr>
<tr class="b">
<th width="20%">Classifier</th>
<td width="80%" align="left"></td></tr>
<tr class="a">
<th width="20%">Type</th>
<td width="80%" align="left">jar</td></tr>
<tr class="b">
<th width="20%">License(s)</th>
<td width="80%" align="left">The Apache License, Version 2.0</td></tr></table><a href="#Overview">Back to top</a><br /></section><section>
<h3><a name="io.opentelemetry.instrumentation:opentelemetry-sdk-autoconfigure-support:2.10.0-alpha"></a>io.opentelemetry.instrumentation:opentelemetry-sdk-autoconfigure-support:2.10.0-alpha</h3>
<table border="0" class="bodyTable">
<tr class="a">
<th width="20%">Status</th>
<td width="80%" align="left"><figure><img src="images/icon_success_sml.gif" alt="" /></figure>&#160;License defined in the pom of the dependency</td></tr>
<tr class="b">
<th width="20%">GroupId:ArtifactId:Version</th>
<td width="80%" align="left">io.opentelemetry.instrumentation:opentelemetry-sdk-autoconfigure-support:2.10.0-alpha</td></tr>
<tr class="a">
<th width="20%">Scope</th>
<td width="80%" align="left">runtime</td></tr>
<tr class="b">
<th width="20%">Classifier</th>
<td width="80%" align="left"></td></tr>
<tr class="a">
<th width="20%">Type</th>
<td width="80%" align="left">jar</td></tr>
<tr class="b">
<th width="20%">License(s)</th>
<td width="80%" align="left">The Apache License, Version 2.0</td></tr></table><a href="#Overview">Back to top</a><br /></section><section>
<h3><a name="io.opentelemetry.instrumentation:opentelemetry-spring-boot-autoconfigure:2.10.0"></a>io.opentelemetry.instrumentation:opentelemetry-spring-boot-autoconfigure:2.10.0</h3>
<table border="0" class="bodyTable">
<tr class="a">
<th width="20%">Status</th>
<td width="80%" align="left"><figure><img src="images/icon_success_sml.gif" alt="" /></figure>&#160;License defined in the pom of the dependency</td></tr>
<tr class="b">
<th width="20%">GroupId:ArtifactId:Version</th>
<td width="80%" align="left">io.opentelemetry.instrumentation:opentelemetry-spring-boot-autoconfigure:2.10.0</td></tr>
<tr class="a">
<th width="20%">Scope</th>
<td width="80%" align="left">compile</td></tr>
<tr class="b">
<th width="20%">Classifier</th>
<td width="80%" align="left"></td></tr>
<tr class="a">
<th width="20%">Type</th>
<td width="80%" align="left">jar</td></tr>
<tr class="b">
<th width="20%">License(s)</th>
<td width="80%" align="left">The Apache License, Version 2.0</td></tr></table><a href="#Overview">Back to top</a><br /></section><section>
<h3><a name="io.opentelemetry.instrumentation:opentelemetry-spring-boot-starter:2.10.0"></a>io.opentelemetry.instrumentation:opentelemetry-spring-boot-starter:2.10.0</h3>
<table border="0" class="bodyTable">
<tr class="a">
<th width="20%">Status</th>
<td width="80%" align="left"><figure><img src="images/icon_success_sml.gif" alt="" /></figure>&#160;License defined in the pom of the dependency</td></tr>
<tr class="b">
<th width="20%">GroupId:ArtifactId:Version</th>
<td width="80%" align="left">io.opentelemetry.instrumentation:opentelemetry-spring-boot-starter:2.10.0</td></tr>
<tr class="a">
<th width="20%">Scope</th>
<td width="80%" align="left">compile</td></tr>
<tr class="b">
<th width="20%">Classifier</th>
<td width="80%" align="left"></td></tr>
<tr class="a">
<th width="20%">Type</th>
<td width="80%" align="left">jar</td></tr>
<tr class="b">
<th width="20%">License(s)</th>
<td width="80%" align="left">The Apache License, Version 2.0</td></tr></table><a href="#Overview">Back to top</a><br /></section><section>
<h3><a name="io.opentelemetry.instrumentation:opentelemetry-spring-kafka-2.7:2.10.0-alpha"></a>io.opentelemetry.instrumentation:opentelemetry-spring-kafka-2.7:2.10.0-alpha</h3>
<table border="0" class="bodyTable">
<tr class="a">
<th width="20%">Status</th>
<td width="80%" align="left"><figure><img src="images/icon_success_sml.gif" alt="" /></figure>&#160;License defined in the pom of the dependency</td></tr>
<tr class="b">
<th width="20%">GroupId:ArtifactId:Version</th>
<td width="80%" align="left">io.opentelemetry.instrumentation:opentelemetry-spring-kafka-2.7:2.10.0-alpha</td></tr>
<tr class="a">
<th width="20%">Scope</th>
<td width="80%" align="left">runtime</td></tr>
<tr class="b">
<th width="20%">Classifier</th>
<td width="80%" align="left"></td></tr>
<tr class="a">
<th width="20%">Type</th>
<td width="80%" align="left">jar</td></tr>
<tr class="b">
<th width="20%">License(s)</th>
<td width="80%" align="left">The Apache License, Version 2.0</td></tr></table><a href="#Overview">Back to top</a><br /></section><section>
<h3><a name="io.opentelemetry.instrumentation:opentelemetry-spring-web-3.1:2.10.0-alpha"></a>io.opentelemetry.instrumentation:opentelemetry-spring-web-3.1:2.10.0-alpha</h3>
<table border="0" class="bodyTable">
<tr class="a">
<th width="20%">Status</th>
<td width="80%" align="left"><figure><img src="images/icon_success_sml.gif" alt="" /></figure>&#160;License defined in the pom of the dependency</td></tr>
<tr class="b">
<th width="20%">GroupId:ArtifactId:Version</th>
<td width="80%" align="left">io.opentelemetry.instrumentation:opentelemetry-spring-web-3.1:2.10.0-alpha</td></tr>
<tr class="a">
<th width="20%">Scope</th>
<td width="80%" align="left">runtime</td></tr>
<tr class="b">
<th width="20%">Classifier</th>
<td width="80%" align="left"></td></tr>
<tr class="a">
<th width="20%">Type</th>
<td width="80%" align="left">jar</td></tr>
<tr class="b">
<th width="20%">License(s)</th>
<td width="80%" align="left">The Apache License, Version 2.0</td></tr></table><a href="#Overview">Back to top</a><br /></section><section>
<h3><a name="io.opentelemetry.instrumentation:opentelemetry-spring-webflux-5.3:2.10.0-alpha"></a>io.opentelemetry.instrumentation:opentelemetry-spring-webflux-5.3:2.10.0-alpha</h3>
<table border="0" class="bodyTable">
<tr class="a">
<th width="20%">Status</th>
<td width="80%" align="left"><figure><img src="images/icon_success_sml.gif" alt="" /></figure>&#160;License defined in the pom of the dependency</td></tr>
<tr class="b">
<th width="20%">GroupId:ArtifactId:Version</th>
<td width="80%" align="left">io.opentelemetry.instrumentation:opentelemetry-spring-webflux-5.3:2.10.0-alpha</td></tr>
<tr class="a">
<th width="20%">Scope</th>
<td width="80%" align="left">runtime</td></tr>
<tr class="b">
<th width="20%">Classifier</th>
<td width="80%" align="left"></td></tr>
<tr class="a">
<th width="20%">Type</th>
<td width="80%" align="left">jar</td></tr>
<tr class="b">
<th width="20%">License(s)</th>
<td width="80%" align="left">The Apache License, Version 2.0</td></tr></table><a href="#Overview">Back to top</a><br /></section><section>
<h3><a name="io.opentelemetry.instrumentation:opentelemetry-spring-webmvc-5.3:2.10.0-alpha"></a>io.opentelemetry.instrumentation:opentelemetry-spring-webmvc-5.3:2.10.0-alpha</h3>
<table border="0" class="bodyTable">
<tr class="a">
<th width="20%">Status</th>
<td width="80%" align="left"><figure><img src="images/icon_success_sml.gif" alt="" /></figure>&#160;License defined in the pom of the dependency</td></tr>
<tr class="b">
<th width="20%">GroupId:ArtifactId:Version</th>
<td width="80%" align="left">io.opentelemetry.instrumentation:opentelemetry-spring-webmvc-5.3:2.10.0-alpha</td></tr>
<tr class="a">
<th width="20%">Scope</th>
<td width="80%" align="left">runtime</td></tr>
<tr class="b">
<th width="20%">Classifier</th>
<td width="80%" align="left"></td></tr>
<tr class="a">
<th width="20%">Type</th>
<td width="80%" align="left">jar</td></tr>
<tr class="b">
<th width="20%">License(s)</th>
<td width="80%" align="left">The Apache License, Version 2.0</td></tr></table><a href="#Overview">Back to top</a><br /></section><section>
<h3><a name="io.opentelemetry.instrumentation:opentelemetry-spring-webmvc-6.0:2.10.0-alpha"></a>io.opentelemetry.instrumentation:opentelemetry-spring-webmvc-6.0:2.10.0-alpha</h3>
<table border="0" class="bodyTable">
<tr class="a">
<th width="20%">Status</th>
<td width="80%" align="left"><figure><img src="images/icon_success_sml.gif" alt="" /></figure>&#160;License defined in the pom of the dependency</td></tr>
<tr class="b">
<th width="20%">GroupId:ArtifactId:Version</th>
<td width="80%" align="left">io.opentelemetry.instrumentation:opentelemetry-spring-webmvc-6.0:2.10.0-alpha</td></tr>
<tr class="a">
<th width="20%">Scope</th>
<td width="80%" align="left">runtime</td></tr>
<tr class="b">
<th width="20%">Classifier</th>
<td width="80%" align="left"></td></tr>
<tr class="a">
<th width="20%">Type</th>
<td width="80%" align="left">jar</td></tr>
<tr class="b">
<th width="20%">License(s)</th>
<td width="80%" align="left">The Apache License, Version 2.0</td></tr></table><a href="#Overview">Back to top</a><br /></section><section>
<h3><a name="io.opentelemetry.semconv:opentelemetry-semconv:1.28.0-alpha"></a>io.opentelemetry.semconv:opentelemetry-semconv:1.28.0-alpha</h3>
<table border="0" class="bodyTable">
<tr class="a">
<th width="20%">Status</th>
<td width="80%" align="left"><figure><img src="images/icon_success_sml.gif" alt="" /></figure>&#160;License defined in the pom of the dependency</td></tr>
<tr class="b">
<th width="20%">GroupId:ArtifactId:Version</th>
<td width="80%" align="left">io.opentelemetry.semconv:opentelemetry-semconv:1.28.0-alpha</td></tr>
<tr class="a">
<th width="20%">Scope</th>
<td width="80%" align="left">compile</td></tr>
<tr class="b">
<th width="20%">Classifier</th>
<td width="80%" align="left"></td></tr>
<tr class="a">
<th width="20%">Type</th>
<td width="80%" align="left">jar</td></tr>
<tr class="b">
<th width="20%">License(s)</th>
<td width="80%" align="left">The Apache License, Version 2.0</td></tr></table><a href="#Overview">Back to top</a><br /></section><section>
<h3><a name="io.opentelemetry.semconv:opentelemetry-semconv-incubating:1.28.0-alpha"></a>io.opentelemetry.semconv:opentelemetry-semconv-incubating:1.28.0-alpha</h3>
<table border="0" class="bodyTable">
<tr class="a">
<th width="20%">Status</th>
<td width="80%" align="left"><figure><img src="images/icon_success_sml.gif" alt="" /></figure>&#160;License defined in the pom of the dependency</td></tr>
<tr class="b">
<th width="20%">GroupId:ArtifactId:Version</th>
<td width="80%" align="left">io.opentelemetry.semconv:opentelemetry-semconv-incubating:1.28.0-alpha</td></tr>
<tr class="a">
<th width="20%">Scope</th>
<td width="80%" align="left">runtime</td></tr>
<tr class="b">
<th width="20%">Classifier</th>
<td width="80%" align="left"></td></tr>
<tr class="a">
<th width="20%">Type</th>
<td width="80%" align="left">jar</td></tr>
<tr class="b">
<th width="20%">License(s)</th>
<td width="80%" align="left">The Apache License, Version 2.0</td></tr></table><a href="#Overview">Back to top</a><br /></section><section>
<h3><a name="io.projectreactor:reactor-core:3.7.6"></a>io.projectreactor:reactor-core:3.7.6</h3>
<table border="0" class="bodyTable">
<tr class="a">
<th width="20%">Status</th>
<td width="80%" align="left"><figure><img src="images/icon_success_sml.gif" alt="" /></figure>&#160;License defined in the pom of the dependency</td></tr>
<tr class="b">
<th width="20%">GroupId:ArtifactId:Version</th>
<td width="80%" align="left">io.projectreactor:reactor-core:3.7.6</td></tr>
<tr class="a">
<th width="20%">Scope</th>
<td width="80%" align="left">compile</td></tr>
<tr class="b">
<th width="20%">Classifier</th>
<td width="80%" align="left"></td></tr>
<tr class="a">
<th width="20%">Type</th>
<td width="80%" align="left">jar</td></tr>
<tr class="b">
<th width="20%">License(s)</th>
<td width="80%" align="left">Apache License, Version 2.0</td></tr></table><a href="#Overview">Back to top</a><br /></section><section>
<h3><a name="io.projectreactor.netty:reactor-netty-core:1.2.6"></a>io.projectreactor.netty:reactor-netty-core:1.2.6</h3>
<table border="0" class="bodyTable">
<tr class="a">
<th width="20%">Status</th>
<td width="80%" align="left"><figure><img src="images/icon_success_sml.gif" alt="" /></figure>&#160;License defined in the pom of the dependency</td></tr>
<tr class="b">
<th width="20%">GroupId:ArtifactId:Version</th>
<td width="80%" align="left">io.projectreactor.netty:reactor-netty-core:1.2.6</td></tr>
<tr class="a">
<th width="20%">Scope</th>
<td width="80%" align="left">compile</td></tr>
<tr class="b">
<th width="20%">Classifier</th>
<td width="80%" align="left"></td></tr>
<tr class="a">
<th width="20%">Type</th>
<td width="80%" align="left">jar</td></tr>
<tr class="b">
<th width="20%">License(s)</th>
<td width="80%" align="left">The Apache Software License, Version 2.0</td></tr></table><a href="#Overview">Back to top</a><br /></section><section>
<h3><a name="io.projectreactor.netty:reactor-netty-http:1.2.6"></a>io.projectreactor.netty:reactor-netty-http:1.2.6</h3>
<table border="0" class="bodyTable">
<tr class="a">
<th width="20%">Status</th>
<td width="80%" align="left"><figure><img src="images/icon_success_sml.gif" alt="" /></figure>&#160;License defined in the pom of the dependency</td></tr>
<tr class="b">
<th width="20%">GroupId:ArtifactId:Version</th>
<td width="80%" align="left">io.projectreactor.netty:reactor-netty-http:1.2.6</td></tr>
<tr class="a">
<th width="20%">Scope</th>
<td width="80%" align="left">compile</td></tr>
<tr class="b">
<th width="20%">Classifier</th>
<td width="80%" align="left"></td></tr>
<tr class="a">
<th width="20%">Type</th>
<td width="80%" align="left">jar</td></tr>
<tr class="b">
<th width="20%">License(s)</th>
<td width="80%" align="left">The Apache Software License, Version 2.0</td></tr></table><a href="#Overview">Back to top</a><br /></section><section>
<h3><a name="io.prometheus:prometheus-metrics-config:1.3.6"></a>io.prometheus:prometheus-metrics-config:1.3.6</h3>
<table border="0" class="bodyTable">
<tr class="a">
<th width="20%">Status</th>
<td width="80%" align="left"><figure><img src="images/icon_success_sml.gif" alt="" /></figure>&#160;License defined in the pom of the dependency</td></tr>
<tr class="b">
<th width="20%">GroupId:ArtifactId:Version</th>
<td width="80%" align="left">io.prometheus:prometheus-metrics-config:1.3.6</td></tr>
<tr class="a">
<th width="20%">Scope</th>
<td width="80%" align="left">compile</td></tr>
<tr class="b">
<th width="20%">Classifier</th>
<td width="80%" align="left"></td></tr>
<tr class="a">
<th width="20%">Type</th>
<td width="80%" align="left">bundle</td></tr>
<tr class="b">
<th width="20%">License(s)</th>
<td width="80%" align="left">The Apache Software License, Version 2.0</td></tr></table><a href="#Overview">Back to top</a><br /></section><section>
<h3><a name="io.prometheus:prometheus-metrics-core:1.3.6"></a>io.prometheus:prometheus-metrics-core:1.3.6</h3>
<table border="0" class="bodyTable">
<tr class="a">
<th width="20%">Status</th>
<td width="80%" align="left"><figure><img src="images/icon_success_sml.gif" alt="" /></figure>&#160;License defined in the pom of the dependency</td></tr>
<tr class="b">
<th width="20%">GroupId:ArtifactId:Version</th>
<td width="80%" align="left">io.prometheus:prometheus-metrics-core:1.3.6</td></tr>
<tr class="a">
<th width="20%">Scope</th>
<td width="80%" align="left">compile</td></tr>
<tr class="b">
<th width="20%">Classifier</th>
<td width="80%" align="left"></td></tr>
<tr class="a">
<th width="20%">Type</th>
<td width="80%" align="left">bundle</td></tr>
<tr class="b">
<th width="20%">License(s)</th>
<td width="80%" align="left">The Apache Software License, Version 2.0</td></tr></table><a href="#Overview">Back to top</a><br /></section><section>
<h3><a name="io.prometheus:prometheus-metrics-exposition-formats:1.3.6"></a>io.prometheus:prometheus-metrics-exposition-formats:1.3.6</h3>
<table border="0" class="bodyTable">
<tr class="a">
<th width="20%">Status</th>
<td width="80%" align="left"><figure><img src="images/icon_success_sml.gif" alt="" /></figure>&#160;License defined in the pom of the dependency</td></tr>
<tr class="b">
<th width="20%">GroupId:ArtifactId:Version</th>
<td width="80%" align="left">io.prometheus:prometheus-metrics-exposition-formats:1.3.6</td></tr>
<tr class="a">
<th width="20%">Scope</th>
<td width="80%" align="left">runtime</td></tr>
<tr class="b">
<th width="20%">Classifier</th>
<td width="80%" align="left"></td></tr>
<tr class="a">
<th width="20%">Type</th>
<td width="80%" align="left">bundle</td></tr>
<tr class="b">
<th width="20%">License(s)</th>
<td width="80%" align="left">The Apache Software License, Version 2.0</td></tr></table><a href="#Overview">Back to top</a><br /></section><section>
<h3><a name="io.prometheus:prometheus-metrics-exposition-textformats:1.3.6"></a>io.prometheus:prometheus-metrics-exposition-textformats:1.3.6</h3>
<table border="0" class="bodyTable">
<tr class="a">
<th width="20%">Status</th>
<td width="80%" align="left"><figure><img src="images/icon_success_sml.gif" alt="" /></figure>&#160;License defined in the pom of the dependency</td></tr>
<tr class="b">
<th width="20%">GroupId:ArtifactId:Version</th>
<td width="80%" align="left">io.prometheus:prometheus-metrics-exposition-textformats:1.3.6</td></tr>
<tr class="a">
<th width="20%">Scope</th>
<td width="80%" align="left">runtime</td></tr>
<tr class="b">
<th width="20%">Classifier</th>
<td width="80%" align="left"></td></tr>
<tr class="a">
<th width="20%">Type</th>
<td width="80%" align="left">bundle</td></tr>
<tr class="b">
<th width="20%">License(s)</th>
<td width="80%" align="left">The Apache Software License, Version 2.0</td></tr></table><a href="#Overview">Back to top</a><br /></section><section>
<h3><a name="io.prometheus:prometheus-metrics-model:1.3.6"></a>io.prometheus:prometheus-metrics-model:1.3.6</h3>
<table border="0" class="bodyTable">
<tr class="a">
<th width="20%">Status</th>
<td width="80%" align="left"><figure><img src="images/icon_success_sml.gif" alt="" /></figure>&#160;License defined in the pom of the dependency</td></tr>
<tr class="b">
<th width="20%">GroupId:ArtifactId:Version</th>
<td width="80%" align="left">io.prometheus:prometheus-metrics-model:1.3.6</td></tr>
<tr class="a">
<th width="20%">Scope</th>
<td width="80%" align="left">compile</td></tr>
<tr class="b">
<th width="20%">Classifier</th>
<td width="80%" align="left"></td></tr>
<tr class="a">
<th width="20%">Type</th>
<td width="80%" align="left">bundle</td></tr>
<tr class="b">
<th width="20%">License(s)</th>
<td width="80%" align="left">The Apache Software License, Version 2.0</td></tr></table><a href="#Overview">Back to top</a><br /></section><section>
<h3><a name="io.prometheus:prometheus-metrics-tracer-common:1.3.6"></a>io.prometheus:prometheus-metrics-tracer-common:1.3.6</h3>
<table border="0" class="bodyTable">
<tr class="a">
<th width="20%">Status</th>
<td width="80%" align="left"><figure><img src="images/icon_success_sml.gif" alt="" /></figure>&#160;License defined in the pom of the dependency</td></tr>
<tr class="b">
<th width="20%">GroupId:ArtifactId:Version</th>
<td width="80%" align="left">io.prometheus:prometheus-metrics-tracer-common:1.3.6</td></tr>
<tr class="a">
<th width="20%">Scope</th>
<td width="80%" align="left">compile</td></tr>
<tr class="b">
<th width="20%">Classifier</th>
<td width="80%" align="left"></td></tr>
<tr class="a">
<th width="20%">Type</th>
<td width="80%" align="left">bundle</td></tr>
<tr class="b">
<th width="20%">License(s)</th>
<td width="80%" align="left">The Apache Software License, Version 2.0</td></tr></table><a href="#Overview">Back to top</a><br /></section><section>
<h3><a name="io.smallrye:jandex:3.2.0"></a>io.smallrye:jandex:3.2.0</h3>
<table border="0" class="bodyTable">
<tr class="a">
<th width="20%">Status</th>
<td width="80%" align="left"><figure><img src="images/icon_success_sml.gif" alt="" /></figure>&#160;License defined in the pom of the dependency</td></tr>
<tr class="b">
<th width="20%">GroupId:ArtifactId:Version</th>
<td width="80%" align="left">io.smallrye:jandex:3.2.0</td></tr>
<tr class="a">
<th width="20%">Scope</th>
<td width="80%" align="left">runtime</td></tr>
<tr class="b">
<th width="20%">Classifier</th>
<td width="80%" align="left"></td></tr>
<tr class="a">
<th width="20%">Type</th>
<td width="80%" align="left">bundle</td></tr>
<tr class="b">
<th width="20%">License(s)</th>
<td width="80%" align="left">Apache License, Version 2.0</td></tr></table><a href="#Overview">Back to top</a><br /></section><section>
<h3><a name="io.zipkin.aws:brave-propagation-aws:1.3.0"></a>io.zipkin.aws:brave-propagation-aws:1.3.0</h3>
<table border="0" class="bodyTable">
<tr class="a">
<th width="20%">Status</th>
<td width="80%" align="left"><figure><img src="images/icon_success_sml.gif" alt="" /></figure>&#160;License defined in the pom of the dependency</td></tr>
<tr class="b">
<th width="20%">GroupId:ArtifactId:Version</th>
<td width="80%" align="left">io.zipkin.aws:brave-propagation-aws:1.3.0</td></tr>
<tr class="a">
<th width="20%">Scope</th>
<td width="80%" align="left">compile</td></tr>
<tr class="b">
<th width="20%">Classifier</th>
<td width="80%" align="left"></td></tr>
<tr class="a">
<th width="20%">Type</th>
<td width="80%" align="left">jar</td></tr>
<tr class="b">
<th width="20%">License(s)</th>
<td width="80%" align="left">The Apache Software License, Version 2.0</td></tr></table><a href="#Overview">Back to top</a><br /></section><section>
<h3><a name="io.zipkin.brave:brave:6.1.0"></a>io.zipkin.brave:brave:6.1.0</h3>
<table border="0" class="bodyTable">
<tr class="a">
<th width="20%">Status</th>
<td width="80%" align="left"><figure><img src="images/icon_success_sml.gif" alt="" /></figure>&#160;License defined in the pom of the dependency</td></tr>
<tr class="b">
<th width="20%">GroupId:ArtifactId:Version</th>
<td width="80%" align="left">io.zipkin.brave:brave:6.1.0</td></tr>
<tr class="a">
<th width="20%">Scope</th>
<td width="80%" align="left">compile</td></tr>
<tr class="b">
<th width="20%">Classifier</th>
<td width="80%" align="left"></td></tr>
<tr class="a">
<th width="20%">Type</th>
<td width="80%" align="left">jar</td></tr>
<tr class="b">
<th width="20%">License(s)</th>
<td width="80%" align="left">The Apache Software License, Version 2.0</td></tr></table><a href="#Overview">Back to top</a><br /></section><section>
<h3><a name="io.zipkin.brave:brave-context-slf4j:6.1.0"></a>io.zipkin.brave:brave-context-slf4j:6.1.0</h3>
<table border="0" class="bodyTable">
<tr class="a">
<th width="20%">Status</th>
<td width="80%" align="left"><figure><img src="images/icon_success_sml.gif" alt="" /></figure>&#160;License defined in the pom of the dependency</td></tr>
<tr class="b">
<th width="20%">GroupId:ArtifactId:Version</th>
<td width="80%" align="left">io.zipkin.brave:brave-context-slf4j:6.1.0</td></tr>
<tr class="a">
<th width="20%">Scope</th>
<td width="80%" align="left">compile</td></tr>
<tr class="b">
<th width="20%">Classifier</th>
<td width="80%" align="left"></td></tr>
<tr class="a">
<th width="20%">Type</th>
<td width="80%" align="left">jar</td></tr>
<tr class="b">
<th width="20%">License(s)</th>
<td width="80%" align="left">The Apache Software License, Version 2.0</td></tr></table><a href="#Overview">Back to top</a><br /></section><section>
<h3><a name="io.zipkin.brave:brave-instrumentation-http:6.1.0"></a>io.zipkin.brave:brave-instrumentation-http:6.1.0</h3>
<table border="0" class="bodyTable">
<tr class="a">
<th width="20%">Status</th>
<td width="80%" align="left"><figure><img src="images/icon_success_sml.gif" alt="" /></figure>&#160;License defined in the pom of the dependency</td></tr>
<tr class="b">
<th width="20%">GroupId:ArtifactId:Version</th>
<td width="80%" align="left">io.zipkin.brave:brave-instrumentation-http:6.1.0</td></tr>
<tr class="a">
<th width="20%">Scope</th>
<td width="80%" align="left">compile</td></tr>
<tr class="b">
<th width="20%">Classifier</th>
<td width="80%" align="left"></td></tr>
<tr class="a">
<th width="20%">Type</th>
<td width="80%" align="left">jar</td></tr>
<tr class="b">
<th width="20%">License(s)</th>
<td width="80%" align="left">The Apache Software License, Version 2.0</td></tr></table><a href="#Overview">Back to top</a><br /></section><section>
<h3><a name="io.zipkin.contrib.brave-propagation-w3c:brave-propagation-tracecontext:0.2.0"></a>io.zipkin.contrib.brave-propagation-w3c:brave-propagation-tracecontext:0.2.0</h3>
<table border="0" class="bodyTable">
<tr class="a">
<th width="20%">Status</th>
<td width="80%" align="left"><figure><img src="images/icon_success_sml.gif" alt="" /></figure>&#160;License defined in the pom of the dependency</td></tr>
<tr class="b">
<th width="20%">GroupId:ArtifactId:Version</th>
<td width="80%" align="left">io.zipkin.contrib.brave-propagation-w3c:brave-propagation-tracecontext:0.2.0</td></tr>
<tr class="a">
<th width="20%">Scope</th>
<td width="80%" align="left">compile</td></tr>
<tr class="b">
<th width="20%">Classifier</th>
<td width="80%" align="left"></td></tr>
<tr class="a">
<th width="20%">Type</th>
<td width="80%" align="left">jar</td></tr>
<tr class="b">
<th width="20%">License(s)</th>
<td width="80%" align="left">The Apache Software License, Version 2.0</td></tr></table><a href="#Overview">Back to top</a><br /></section><section>
<h3><a name="io.zipkin.reporter2:zipkin-reporter:3.5.0"></a>io.zipkin.reporter2:zipkin-reporter:3.5.0</h3>
<table border="0" class="bodyTable">
<tr class="a">
<th width="20%">Status</th>
<td width="80%" align="left"><figure><img src="images/icon_success_sml.gif" alt="" /></figure>&#160;License defined in the pom of the dependency</td></tr>
<tr class="b">
<th width="20%">GroupId:ArtifactId:Version</th>
<td width="80%" align="left">io.zipkin.reporter2:zipkin-reporter:3.5.0</td></tr>
<tr class="a">
<th width="20%">Scope</th>
<td width="80%" align="left">compile</td></tr>
<tr class="b">
<th width="20%">Classifier</th>
<td width="80%" align="left"></td></tr>
<tr class="a">
<th width="20%">Type</th>
<td width="80%" align="left">jar</td></tr>
<tr class="b">
<th width="20%">License(s)</th>
<td width="80%" align="left">The Apache Software License, Version 2.0</td></tr></table><a href="#Overview">Back to top</a><br /></section><section>
<h3><a name="io.zipkin.reporter2:zipkin-reporter-brave:3.5.0"></a>io.zipkin.reporter2:zipkin-reporter-brave:3.5.0</h3>
<table border="0" class="bodyTable">
<tr class="a">
<th width="20%">Status</th>
<td width="80%" align="left"><figure><img src="images/icon_success_sml.gif" alt="" /></figure>&#160;License defined in the pom of the dependency</td></tr>
<tr class="b">
<th width="20%">GroupId:ArtifactId:Version</th>
<td width="80%" align="left">io.zipkin.reporter2:zipkin-reporter-brave:3.5.0</td></tr>
<tr class="a">
<th width="20%">Scope</th>
<td width="80%" align="left">compile</td></tr>
<tr class="b">
<th width="20%">Classifier</th>
<td width="80%" align="left"></td></tr>
<tr class="a">
<th width="20%">Type</th>
<td width="80%" align="left">jar</td></tr>
<tr class="b">
<th width="20%">License(s)</th>
<td width="80%" align="left">The Apache Software License, Version 2.0</td></tr></table><a href="#Overview">Back to top</a><br /></section><section>
<h3><a name="io.zipkin.reporter2:zipkin-sender-okhttp3:3.5.0"></a>io.zipkin.reporter2:zipkin-sender-okhttp3:3.5.0</h3>
<table border="0" class="bodyTable">
<tr class="a">
<th width="20%">Status</th>
<td width="80%" align="left"><figure><img src="images/icon_success_sml.gif" alt="" /></figure>&#160;License defined in the pom of the dependency</td></tr>
<tr class="b">
<th width="20%">GroupId:ArtifactId:Version</th>
<td width="80%" align="left">io.zipkin.reporter2:zipkin-sender-okhttp3:3.5.0</td></tr>
<tr class="a">
<th width="20%">Scope</th>
<td width="80%" align="left">runtime</td></tr>
<tr class="b">
<th width="20%">Classifier</th>
<td width="80%" align="left"></td></tr>
<tr class="a">
<th width="20%">Type</th>
<td width="80%" align="left">jar</td></tr>
<tr class="b">
<th width="20%">License(s)</th>
<td width="80%" align="left">The Apache Software License, Version 2.0</td></tr></table><a href="#Overview">Back to top</a><br /></section><section>
<h3><a name="io.zipkin.zipkin2:zipkin:2.27.1"></a>io.zipkin.zipkin2:zipkin:2.27.1</h3>
<table border="0" class="bodyTable">
<tr class="a">
<th width="20%">Status</th>
<td width="80%" align="left"><figure><img src="images/icon_success_sml.gif" alt="" /></figure>&#160;License defined in the pom of the dependency</td></tr>
<tr class="b">
<th width="20%">GroupId:ArtifactId:Version</th>
<td width="80%" align="left">io.zipkin.zipkin2:zipkin:2.27.1</td></tr>
<tr class="a">
<th width="20%">Scope</th>
<td width="80%" align="left">compile</td></tr>
<tr class="b">
<th width="20%">Classifier</th>
<td width="80%" align="left"></td></tr>
<tr class="a">
<th width="20%">Type</th>
<td width="80%" align="left">jar</td></tr>
<tr class="b">
<th width="20%">License(s)</th>
<td width="80%" align="left">The Apache Software License, Version 2.0</td></tr></table><a href="#Overview">Back to top</a><br /></section><section>
<h3><a name="jakarta.activation:jakarta.activation-api:2.1.3"></a>jakarta.activation:jakarta.activation-api:2.1.3</h3>
<table border="0" class="bodyTable">
<tr class="a">
<th width="20%">Status</th>
<td width="80%" align="left"><figure><img src="images/icon_success_sml.gif" alt="" /></figure>&#160;License defined in the pom of the dependency</td></tr>
<tr class="b">
<th width="20%">GroupId:ArtifactId:Version</th>
<td width="80%" align="left">jakarta.activation:jakarta.activation-api:2.1.3</td></tr>
<tr class="a">
<th width="20%">Scope</th>
<td width="80%" align="left">compile</td></tr>
<tr class="b">
<th width="20%">Classifier</th>
<td width="80%" align="left"></td></tr>
<tr class="a">
<th width="20%">Type</th>
<td width="80%" align="left">jar</td></tr>
<tr class="b">
<th width="20%">License(s)</th>
<td width="80%" align="left">EDL 1.0</td></tr></table><a href="#Overview">Back to top</a><br /></section><section>
<h3><a name="jakarta.annotation:jakarta.annotation-api:2.1.1"></a>jakarta.annotation:jakarta.annotation-api:2.1.1</h3>
<table border="0" class="bodyTable">
<tr class="a">
<th width="20%">Status</th>
<td width="80%" align="left"><figure><img src="images/icon_success_sml.gif" alt="" /></figure>&#160;License defined in the pom of the dependency</td></tr>
<tr class="b">
<th width="20%">GroupId:ArtifactId:Version</th>
<td width="80%" align="left">jakarta.annotation:jakarta.annotation-api:2.1.1</td></tr>
<tr class="a">
<th width="20%">Scope</th>
<td width="80%" align="left">compile</td></tr>
<tr class="b">
<th width="20%">Classifier</th>
<td width="80%" align="left"></td></tr>
<tr class="a">
<th width="20%">Type</th>
<td width="80%" align="left">jar</td></tr>
<tr class="b">
<th width="20%">License(s)</th>
<td width="80%" align="left">EPL 2.0<br />GPL2 w/ CPE</td></tr></table><a href="#Overview">Back to top</a><br /></section><section>
<h3><a name="jakarta.inject:jakarta.inject-api:2.0.1"></a>jakarta.inject:jakarta.inject-api:2.0.1</h3>
<table border="0" class="bodyTable">
<tr class="a">
<th width="20%">Status</th>
<td width="80%" align="left"><figure><img src="images/icon_success_sml.gif" alt="" /></figure>&#160;License defined in the pom of the dependency</td></tr>
<tr class="b">
<th width="20%">GroupId:ArtifactId:Version</th>
<td width="80%" align="left">jakarta.inject:jakarta.inject-api:2.0.1</td></tr>
<tr class="a">
<th width="20%">Scope</th>
<td width="80%" align="left">runtime</td></tr>
<tr class="b">
<th width="20%">Classifier</th>
<td width="80%" align="left"></td></tr>
<tr class="a">
<th width="20%">Type</th>
<td width="80%" align="left">jar</td></tr>
<tr class="b">
<th width="20%">License(s)</th>
<td width="80%" align="left">The Apache Software License, Version 2.0</td></tr></table><a href="#Overview">Back to top</a><br /></section><section>
<h3><a name="jakarta.json:jakarta.json-api:2.1.3"></a>jakarta.json:jakarta.json-api:2.1.3</h3>
<table border="0" class="bodyTable">
<tr class="a">
<th width="20%">Status</th>
<td width="80%" align="left"><figure><img src="images/icon_success_sml.gif" alt="" /></figure>&#160;License defined in the pom of the dependency</td></tr>
<tr class="b">
<th width="20%">GroupId:ArtifactId:Version</th>
<td width="80%" align="left">jakarta.json:jakarta.json-api:2.1.3</td></tr>
<tr class="a">
<th width="20%">Scope</th>
<td width="80%" align="left">compile</td></tr>
<tr class="b">
<th width="20%">Classifier</th>
<td width="80%" align="left"></td></tr>
<tr class="a">
<th width="20%">Type</th>
<td width="80%" align="left">jar</td></tr>
<tr class="b">
<th width="20%">License(s)</th>
<td width="80%" align="left">Eclipse Public License 2.0<br />GNU General Public License, version 2 with the GNU Classpath Exception</td></tr></table><a href="#Overview">Back to top</a><br /></section><section>
<h3><a name="jakarta.persistence:jakarta.persistence-api:3.1.0"></a>jakarta.persistence:jakarta.persistence-api:3.1.0</h3>
<table border="0" class="bodyTable">
<tr class="a">
<th width="20%">Status</th>
<td width="80%" align="left"><figure><img src="images/icon_success_sml.gif" alt="" /></figure>&#160;License defined in the pom of the dependency</td></tr>
<tr class="b">
<th width="20%">GroupId:ArtifactId:Version</th>
<td width="80%" align="left">jakarta.persistence:jakarta.persistence-api:3.1.0</td></tr>
<tr class="a">
<th width="20%">Scope</th>
<td width="80%" align="left">compile</td></tr>
<tr class="b">
<th width="20%">Classifier</th>
<td width="80%" align="left"></td></tr>
<tr class="a">
<th width="20%">Type</th>
<td width="80%" align="left">jar</td></tr>
<tr class="b">
<th width="20%">License(s)</th>
<td width="80%" align="left">Eclipse Distribution License v. 1.0<br />Eclipse Public License v. 2.0</td></tr></table><a href="#Overview">Back to top</a><br /></section><section>
<h3><a name="jakarta.transaction:jakarta.transaction-api:2.0.1"></a>jakarta.transaction:jakarta.transaction-api:2.0.1</h3>
<table border="0" class="bodyTable">
<tr class="a">
<th width="20%">Status</th>
<td width="80%" align="left"><figure><img src="images/icon_success_sml.gif" alt="" /></figure>&#160;License defined in the pom of the dependency</td></tr>
<tr class="b">
<th width="20%">GroupId:ArtifactId:Version</th>
<td width="80%" align="left">jakarta.transaction:jakarta.transaction-api:2.0.1</td></tr>
<tr class="a">
<th width="20%">Scope</th>
<td width="80%" align="left">compile</td></tr>
<tr class="b">
<th width="20%">Classifier</th>
<td width="80%" align="left"></td></tr>
<tr class="a">
<th width="20%">Type</th>
<td width="80%" align="left">jar</td></tr>
<tr class="b">
<th width="20%">License(s)</th>
<td width="80%" align="left">EPL 2.0<br />GPL2 w/ CPE</td></tr></table><a href="#Overview">Back to top</a><br /></section><section>
<h3><a name="jakarta.validation:jakarta.validation-api:3.0.2"></a>jakarta.validation:jakarta.validation-api:3.0.2</h3>
<table border="0" class="bodyTable">
<tr class="a">
<th width="20%">Status</th>
<td width="80%" align="left"><figure><img src="images/icon_success_sml.gif" alt="" /></figure>&#160;License defined in the pom of the dependency</td></tr>
<tr class="b">
<th width="20%">GroupId:ArtifactId:Version</th>
<td width="80%" align="left">jakarta.validation:jakarta.validation-api:3.0.2</td></tr>
<tr class="a">
<th width="20%">Scope</th>
<td width="80%" align="left">compile</td></tr>
<tr class="b">
<th width="20%">Classifier</th>
<td width="80%" align="left"></td></tr>
<tr class="a">
<th width="20%">Type</th>
<td width="80%" align="left">jar</td></tr>
<tr class="b">
<th width="20%">License(s)</th>
<td width="80%" align="left">Apache License 2.0</td></tr></table><a href="#Overview">Back to top</a><br /></section><section>
<h3><a name="jakarta.xml.bind:jakarta.xml.bind-api:4.0.2"></a>jakarta.xml.bind:jakarta.xml.bind-api:4.0.2</h3>
<table border="0" class="bodyTable">
<tr class="a">
<th width="20%">Status</th>
<td width="80%" align="left"><figure><img src="images/icon_success_sml.gif" alt="" /></figure>&#160;License defined in the pom of the dependency</td></tr>
<tr class="b">
<th width="20%">GroupId:ArtifactId:Version</th>
<td width="80%" align="left">jakarta.xml.bind:jakarta.xml.bind-api:4.0.2</td></tr>
<tr class="a">
<th width="20%">Scope</th>
<td width="80%" align="left">compile</td></tr>
<tr class="b">
<th width="20%">Classifier</th>
<td width="80%" align="left"></td></tr>
<tr class="a">
<th width="20%">Type</th>
<td width="80%" align="left">jar</td></tr>
<tr class="b">
<th width="20%">License(s)</th>
<td width="80%" align="left">Eclipse Distribution License - v 1.0</td></tr></table><a href="#Overview">Back to top</a><br /></section><section>
<h3><a name="javax.validation:validation-api:2.0.1.Final"></a>javax.validation:validation-api:2.0.1.Final</h3>
<table border="0" class="bodyTable">
<tr class="a">
<th width="20%">Status</th>
<td width="80%" align="left"><figure><img src="images/icon_success_sml.gif" alt="" /></figure>&#160;License defined in the pom of the dependency</td></tr>
<tr class="b">
<th width="20%">GroupId:ArtifactId:Version</th>
<td width="80%" align="left">javax.validation:validation-api:2.0.1.Final</td></tr>
<tr class="a">
<th width="20%">Scope</th>
<td width="80%" align="left">runtime</td></tr>
<tr class="b">
<th width="20%">Classifier</th>
<td width="80%" align="left"></td></tr>
<tr class="a">
<th width="20%">Type</th>
<td width="80%" align="left">jar</td></tr>
<tr class="b">
<th width="20%">License(s)</th>
<td width="80%" align="left">Apache License 2.0</td></tr></table><a href="#Overview">Back to top</a><br /></section><section>
<h3><a name="javax.xml.bind:jaxb-api:2.3.1"></a>javax.xml.bind:jaxb-api:2.3.1</h3>
<table border="0" class="bodyTable">
<tr class="a">
<th width="20%">Status</th>
<td width="80%" align="left"><figure><img src="images/icon_success_sml.gif" alt="" /></figure>&#160;License defined in the pom of the dependency</td></tr>
<tr class="b">
<th width="20%">GroupId:ArtifactId:Version</th>
<td width="80%" align="left">javax.xml.bind:jaxb-api:2.3.1</td></tr>
<tr class="a">
<th width="20%">Scope</th>
<td width="80%" align="left">compile</td></tr>
<tr class="b">
<th width="20%">Classifier</th>
<td width="80%" align="left"></td></tr>
<tr class="a">
<th width="20%">Type</th>
<td width="80%" align="left">jar</td></tr>
<tr class="b">
<th width="20%">License(s)</th>
<td width="80%" align="left">CDDL 1.1<br />GPL2 w/ CPE</td></tr></table><a href="#Overview">Back to top</a><br /></section><section>
<h3><a name="net.bytebuddy:byte-buddy:1.17.5"></a>net.bytebuddy:byte-buddy:1.17.5</h3>
<table border="0" class="bodyTable">
<tr class="a">
<th width="20%">Status</th>
<td width="80%" align="left"><figure><img src="images/icon_success_sml.gif" alt="" /></figure>&#160;License defined in the pom of the dependency</td></tr>
<tr class="b">
<th width="20%">GroupId:ArtifactId:Version</th>
<td width="80%" align="left">net.bytebuddy:byte-buddy:1.17.5</td></tr>
<tr class="a">
<th width="20%">Scope</th>
<td width="80%" align="left">runtime</td></tr>
<tr class="b">
<th width="20%">Classifier</th>
<td width="80%" align="left"></td></tr>
<tr class="a">
<th width="20%">Type</th>
<td width="80%" align="left">jar</td></tr>
<tr class="b">
<th width="20%">License(s)</th>
<td width="80%" align="left">Apache License, Version 2.0</td></tr></table><a href="#Overview">Back to top</a><br /></section><section>
<h3><a name="net.java.dev.jna:jna:5.13.0"></a>net.java.dev.jna:jna:5.13.0</h3>
<table border="0" class="bodyTable">
<tr class="a">
<th width="20%">Status</th>
<td width="80%" align="left"><figure><img src="images/icon_success_sml.gif" alt="" /></figure>&#160;License defined in the pom of the dependency</td></tr>
<tr class="b">
<th width="20%">GroupId:ArtifactId:Version</th>
<td width="80%" align="left">net.java.dev.jna:jna:5.13.0</td></tr>
<tr class="a">
<th width="20%">Scope</th>
<td width="80%" align="left">compile</td></tr>
<tr class="b">
<th width="20%">Classifier</th>
<td width="80%" align="left"></td></tr>
<tr class="a">
<th width="20%">Type</th>
<td width="80%" align="left">jar</td></tr>
<tr class="b">
<th width="20%">License(s)</th>
<td width="80%" align="left">Apache-2.0<br />LGPL-2.1-or-later</td></tr></table><a href="#Overview">Back to top</a><br /></section><section>
<h3><a name="net.java.dev.jna:jna-platform:5.6.0"></a>net.java.dev.jna:jna-platform:5.6.0</h3>
<table border="0" class="bodyTable">
<tr class="a">
<th width="20%">Status</th>
<td width="80%" align="left"><figure><img src="images/icon_success_sml.gif" alt="" /></figure>&#160;License defined in the pom of the dependency</td></tr>
<tr class="b">
<th width="20%">GroupId:ArtifactId:Version</th>
<td width="80%" align="left">net.java.dev.jna:jna-platform:5.6.0</td></tr>
<tr class="a">
<th width="20%">Scope</th>
<td width="80%" align="left">compile</td></tr>
<tr class="b">
<th width="20%">Classifier</th>
<td width="80%" align="left"></td></tr>
<tr class="a">
<th width="20%">Type</th>
<td width="80%" align="left">jar</td></tr>
<tr class="b">
<th width="20%">License(s)</th>
<td width="80%" align="left">Apache License v2.0<br />LGPL, version 2.1</td></tr></table><a href="#Overview">Back to top</a><br /></section><section>
<h3><a name="net.logstash.logback:logstash-logback-encoder:7.4"></a>net.logstash.logback:logstash-logback-encoder:7.4</h3>
<table border="0" class="bodyTable">
<tr class="a">
<th width="20%">Status</th>
<td width="80%" align="left"><figure><img src="images/icon_success_sml.gif" alt="" /></figure>&#160;License defined in the pom of the dependency</td></tr>
<tr class="b">
<th width="20%">GroupId:ArtifactId:Version</th>
<td width="80%" align="left">net.logstash.logback:logstash-logback-encoder:7.4</td></tr>
<tr class="a">
<th width="20%">Scope</th>
<td width="80%" align="left">compile</td></tr>
<tr class="b">
<th width="20%">Classifier</th>
<td width="80%" align="left"></td></tr>
<tr class="a">
<th width="20%">Type</th>
<td width="80%" align="left">jar</td></tr>
<tr class="b">
<th width="20%">License(s)</th>
<td width="80%" align="left">Apache License, Version 2.0<br />MIT License</td></tr></table><a href="#Overview">Back to top</a><br /></section><section>
<h3><a name="net.minidev:accessors-smart:2.5.2"></a>net.minidev:accessors-smart:2.5.2</h3>
<table border="0" class="bodyTable">
<tr class="a">
<th width="20%">Status</th>
<td width="80%" align="left"><figure><img src="images/icon_success_sml.gif" alt="" /></figure>&#160;License defined in the pom of the dependency</td></tr>
<tr class="b">
<th width="20%">GroupId:ArtifactId:Version</th>
<td width="80%" align="left">net.minidev:accessors-smart:2.5.2</td></tr>
<tr class="a">
<th width="20%">Scope</th>
<td width="80%" align="left">compile</td></tr>
<tr class="b">
<th width="20%">Classifier</th>
<td width="80%" align="left"></td></tr>
<tr class="a">
<th width="20%">Type</th>
<td width="80%" align="left">bundle</td></tr>
<tr class="b">
<th width="20%">License(s)</th>
<td width="80%" align="left">The Apache Software License, Version 2.0</td></tr></table><a href="#Overview">Back to top</a><br /></section><section>
<h3><a name="net.minidev:json-smart:2.5.2"></a>net.minidev:json-smart:2.5.2</h3>
<table border="0" class="bodyTable">
<tr class="a">
<th width="20%">Status</th>
<td width="80%" align="left"><figure><img src="images/icon_success_sml.gif" alt="" /></figure>&#160;License defined in the pom of the dependency</td></tr>
<tr class="b">
<th width="20%">GroupId:ArtifactId:Version</th>
<td width="80%" align="left">net.minidev:json-smart:2.5.2</td></tr>
<tr class="a">
<th width="20%">Scope</th>
<td width="80%" align="left">compile</td></tr>
<tr class="b">
<th width="20%">Classifier</th>
<td width="80%" align="left"></td></tr>
<tr class="a">
<th width="20%">Type</th>
<td width="80%" align="left">bundle</td></tr>
<tr class="b">
<th width="20%">License(s)</th>
<td width="80%" align="left">The Apache Software License, Version 2.0</td></tr></table><a href="#Overview">Back to top</a><br /></section><section>
<h3><a name="org.antlr:antlr4-runtime:4.13.0"></a>org.antlr:antlr4-runtime:4.13.0</h3>
<table border="0" class="bodyTable">
<tr class="a">
<th width="20%">Status</th>
<td width="80%" align="left"><figure><img src="images/icon_success_sml.gif" alt="" /></figure>&#160;License defined in the pom of the dependency</td></tr>
<tr class="b">
<th width="20%">GroupId:ArtifactId:Version</th>
<td width="80%" align="left">org.antlr:antlr4-runtime:4.13.0</td></tr>
<tr class="a">
<th width="20%">Scope</th>
<td width="80%" align="left">compile</td></tr>
<tr class="b">
<th width="20%">Classifier</th>
<td width="80%" align="left"></td></tr>
<tr class="a">
<th width="20%">Type</th>
<td width="80%" align="left">jar</td></tr>
<tr class="b">
<th width="20%">License(s)</th>
<td width="80%" align="left">BSD-3-Clause</td></tr></table><a href="#Overview">Back to top</a><br /></section><section>
<h3><a name="org.apache.commons:commons-collections4:4.4"></a>org.apache.commons:commons-collections4:4.4</h3>
<table border="0" class="bodyTable">
<tr class="a">
<th width="20%">Status</th>
<td width="80%" align="left"><figure><img src="images/icon_success_sml.gif" alt="" /></figure>&#160;License defined in the pom of the dependency</td></tr>
<tr class="b">
<th width="20%">GroupId:ArtifactId:Version</th>
<td width="80%" align="left">org.apache.commons:commons-collections4:4.4</td></tr>
<tr class="a">
<th width="20%">Scope</th>
<td width="80%" align="left">compile</td></tr>
<tr class="b">
<th width="20%">Classifier</th>
<td width="80%" align="left"></td></tr>
<tr class="a">
<th width="20%">Type</th>
<td width="80%" align="left">jar</td></tr>
<tr class="b">
<th width="20%">License(s)</th>
<td width="80%" align="left">Apache License, Version 2.0</td></tr></table><a href="#Overview">Back to top</a><br /></section><section>
<h3><a name="org.apache.commons:commons-compress:1.25.0"></a>org.apache.commons:commons-compress:1.25.0</h3>
<table border="0" class="bodyTable">
<tr class="a">
<th width="20%">Status</th>
<td width="80%" align="left"><figure><img src="images/icon_success_sml.gif" alt="" /></figure>&#160;License defined in the pom of the dependency</td></tr>
<tr class="b">
<th width="20%">GroupId:ArtifactId:Version</th>
<td width="80%" align="left">org.apache.commons:commons-compress:1.25.0</td></tr>
<tr class="a">
<th width="20%">Scope</th>
<td width="80%" align="left">compile</td></tr>
<tr class="b">
<th width="20%">Classifier</th>
<td width="80%" align="left"></td></tr>
<tr class="a">
<th width="20%">Type</th>
<td width="80%" align="left">jar</td></tr>
<tr class="b">
<th width="20%">License(s)</th>
<td width="80%" align="left">Apache-2.0</td></tr></table><a href="#Overview">Back to top</a><br /></section><section>
<h3><a name="org.apache.commons:commons-csv:1.10.0"></a>org.apache.commons:commons-csv:1.10.0</h3>
<table border="0" class="bodyTable">
<tr class="a">
<th width="20%">Status</th>
<td width="80%" align="left"><figure><img src="images/icon_success_sml.gif" alt="" /></figure>&#160;License defined in the pom of the dependency</td></tr>
<tr class="b">
<th width="20%">GroupId:ArtifactId:Version</th>
<td width="80%" align="left">org.apache.commons:commons-csv:1.10.0</td></tr>
<tr class="a">
<th width="20%">Scope</th>
<td width="80%" align="left">compile</td></tr>
<tr class="b">
<th width="20%">Classifier</th>
<td width="80%" align="left"></td></tr>
<tr class="a">
<th width="20%">Type</th>
<td width="80%" align="left">jar</td></tr>
<tr class="b">
<th width="20%">License(s)</th>
<td width="80%" align="left">Apache-2.0</td></tr></table><a href="#Overview">Back to top</a><br /></section><section>
<h3><a name="org.apache.commons:commons-exec:1.3"></a>org.apache.commons:commons-exec:1.3</h3>
<table border="0" class="bodyTable">
<tr class="a">
<th width="20%">Status</th>
<td width="80%" align="left"><figure><img src="images/icon_success_sml.gif" alt="" /></figure>&#160;License defined in the pom of the dependency</td></tr>
<tr class="b">
<th width="20%">GroupId:ArtifactId:Version</th>
<td width="80%" align="left">org.apache.commons:commons-exec:1.3</td></tr>
<tr class="a">
<th width="20%">Scope</th>
<td width="80%" align="left">compile</td></tr>
<tr class="b">
<th width="20%">Classifier</th>
<td width="80%" align="left"></td></tr>
<tr class="a">
<th width="20%">Type</th>
<td width="80%" align="left">jar</td></tr>
<tr class="b">
<th width="20%">License(s)</th>
<td width="80%" align="left">Apache License, Version 2.0</td></tr></table><a href="#Overview">Back to top</a><br /></section><section>
<h3><a name="org.apache.commons:commons-lang3:3.17.0"></a>org.apache.commons:commons-lang3:3.17.0</h3>
<table border="0" class="bodyTable">
<tr class="a">
<th width="20%">Status</th>
<td width="80%" align="left"><figure><img src="images/icon_success_sml.gif" alt="" /></figure>&#160;License defined in the pom of the dependency</td></tr>
<tr class="b">
<th width="20%">GroupId:ArtifactId:Version</th>
<td width="80%" align="left">org.apache.commons:commons-lang3:3.17.0</td></tr>
<tr class="a">
<th width="20%">Scope</th>
<td width="80%" align="left">compile</td></tr>
<tr class="b">
<th width="20%">Classifier</th>
<td width="80%" align="left"></td></tr>
<tr class="a">
<th width="20%">Type</th>
<td width="80%" align="left">jar</td></tr>
<tr class="b">
<th width="20%">License(s)</th>
<td width="80%" align="left">Apache-2.0</td></tr></table><a href="#Overview">Back to top</a><br /></section><section>
<h3><a name="org.apache.commons:commons-math3:3.6.1"></a>org.apache.commons:commons-math3:3.6.1</h3>
<table border="0" class="bodyTable">
<tr class="a">
<th width="20%">Status</th>
<td width="80%" align="left"><figure><img src="images/icon_success_sml.gif" alt="" /></figure>&#160;License defined in the pom of the dependency</td></tr>
<tr class="b">
<th width="20%">GroupId:ArtifactId:Version</th>
<td width="80%" align="left">org.apache.commons:commons-math3:3.6.1</td></tr>
<tr class="a">
<th width="20%">Scope</th>
<td width="80%" align="left">compile</td></tr>
<tr class="b">
<th width="20%">Classifier</th>
<td width="80%" align="left"></td></tr>
<tr class="a">
<th width="20%">Type</th>
<td width="80%" align="left">jar</td></tr>
<tr class="b">
<th width="20%">License(s)</th>
<td width="80%" align="left">Apache License, Version 2.0</td></tr></table><a href="#Overview">Back to top</a><br /></section><section>
<h3><a name="org.apache.commons:commons-text:1.13.0"></a>org.apache.commons:commons-text:1.13.0</h3>
<table border="0" class="bodyTable">
<tr class="a">
<th width="20%">Status</th>
<td width="80%" align="left"><figure><img src="images/icon_success_sml.gif" alt="" /></figure>&#160;License defined in the pom of the dependency</td></tr>
<tr class="b">
<th width="20%">GroupId:ArtifactId:Version</th>
<td width="80%" align="left">org.apache.commons:commons-text:1.13.0</td></tr>
<tr class="a">
<th width="20%">Scope</th>
<td width="80%" align="left">compile</td></tr>
<tr class="b">
<th width="20%">Classifier</th>
<td width="80%" align="left"></td></tr>
<tr class="a">
<th width="20%">Type</th>
<td width="80%" align="left">jar</td></tr>
<tr class="b">
<th width="20%">License(s)</th>
<td width="80%" align="left">Apache-2.0</td></tr></table><a href="#Overview">Back to top</a><br /></section><section>
<h3><a name="org.apache.httpcomponents:httpasyncclient:4.1.5"></a>org.apache.httpcomponents:httpasyncclient:4.1.5</h3>
<table border="0" class="bodyTable">
<tr class="a">
<th width="20%">Status</th>
<td width="80%" align="left"><figure><img src="images/icon_success_sml.gif" alt="" /></figure>&#160;License defined in the pom of the dependency</td></tr>
<tr class="b">
<th width="20%">GroupId:ArtifactId:Version</th>
<td width="80%" align="left">org.apache.httpcomponents:httpasyncclient:4.1.5</td></tr>
<tr class="a">
<th width="20%">Scope</th>
<td width="80%" align="left">compile</td></tr>
<tr class="b">
<th width="20%">Classifier</th>
<td width="80%" align="left"></td></tr>
<tr class="a">
<th width="20%">Type</th>
<td width="80%" align="left">jar</td></tr>
<tr class="b">
<th width="20%">License(s)</th>
<td width="80%" align="left">Apache License, Version 2.0</td></tr></table><a href="#Overview">Back to top</a><br /></section><section>
<h3><a name="org.apache.httpcomponents:httpclient:4.5.13"></a>org.apache.httpcomponents:httpclient:4.5.13</h3>
<table border="0" class="bodyTable">
<tr class="a">
<th width="20%">Status</th>
<td width="80%" align="left"><figure><img src="images/icon_success_sml.gif" alt="" /></figure>&#160;License defined in the pom of the dependency</td></tr>
<tr class="b">
<th width="20%">GroupId:ArtifactId:Version</th>
<td width="80%" align="left">org.apache.httpcomponents:httpclient:4.5.13</td></tr>
<tr class="a">
<th width="20%">Scope</th>
<td width="80%" align="left">compile</td></tr>
<tr class="b">
<th width="20%">Classifier</th>
<td width="80%" align="left"></td></tr>
<tr class="a">
<th width="20%">Type</th>
<td width="80%" align="left">jar</td></tr>
<tr class="b">
<th width="20%">License(s)</th>
<td width="80%" align="left">Apache License, Version 2.0</td></tr></table><a href="#Overview">Back to top</a><br /></section><section>
<h3><a name="org.apache.httpcomponents:httpcore:4.4.16"></a>org.apache.httpcomponents:httpcore:4.4.16</h3>
<table border="0" class="bodyTable">
<tr class="a">
<th width="20%">Status</th>
<td width="80%" align="left"><figure><img src="images/icon_success_sml.gif" alt="" /></figure>&#160;License defined in the pom of the dependency</td></tr>
<tr class="b">
<th width="20%">GroupId:ArtifactId:Version</th>
<td width="80%" align="left">org.apache.httpcomponents:httpcore:4.4.16</td></tr>
<tr class="a">
<th width="20%">Scope</th>
<td width="80%" align="left">compile</td></tr>
<tr class="b">
<th width="20%">Classifier</th>
<td width="80%" align="left"></td></tr>
<tr class="a">
<th width="20%">Type</th>
<td width="80%" align="left">jar</td></tr>
<tr class="b">
<th width="20%">License(s)</th>
<td width="80%" align="left">Apache License, Version 2.0</td></tr></table><a href="#Overview">Back to top</a><br /></section><section>
<h3><a name="org.apache.httpcomponents:httpcore-nio:4.4.16"></a>org.apache.httpcomponents:httpcore-nio:4.4.16</h3>
<table border="0" class="bodyTable">
<tr class="a">
<th width="20%">Status</th>
<td width="80%" align="left"><figure><img src="images/icon_success_sml.gif" alt="" /></figure>&#160;License defined in the pom of the dependency</td></tr>
<tr class="b">
<th width="20%">GroupId:ArtifactId:Version</th>
<td width="80%" align="left">org.apache.httpcomponents:httpcore-nio:4.4.16</td></tr>
<tr class="a">
<th width="20%">Scope</th>
<td width="80%" align="left">compile</td></tr>
<tr class="b">
<th width="20%">Classifier</th>
<td width="80%" align="left"></td></tr>
<tr class="a">
<th width="20%">Type</th>
<td width="80%" align="left">jar</td></tr>
<tr class="b">
<th width="20%">License(s)</th>
<td width="80%" align="left">Apache License, Version 2.0</td></tr></table><a href="#Overview">Back to top</a><br /></section><section>
<h3><a name="org.apache.james:apache-mime4j-core:0.8.9"></a>org.apache.james:apache-mime4j-core:0.8.9</h3>
<table border="0" class="bodyTable">
<tr class="a">
<th width="20%">Status</th>
<td width="80%" align="left"><figure><img src="images/icon_success_sml.gif" alt="" /></figure>&#160;License defined in the pom of the dependency</td></tr>
<tr class="b">
<th width="20%">GroupId:ArtifactId:Version</th>
<td width="80%" align="left">org.apache.james:apache-mime4j-core:0.8.9</td></tr>
<tr class="a">
<th width="20%">Scope</th>
<td width="80%" align="left">compile</td></tr>
<tr class="b">
<th width="20%">Classifier</th>
<td width="80%" align="left"></td></tr>
<tr class="a">
<th width="20%">Type</th>
<td width="80%" align="left">bundle</td></tr>
<tr class="b">
<th width="20%">License(s)</th>
<td width="80%" align="left">Apache License, Version 2.0</td></tr></table><a href="#Overview">Back to top</a><br /></section><section>
<h3><a name="org.apache.james:apache-mime4j-dom:0.8.9"></a>org.apache.james:apache-mime4j-dom:0.8.9</h3>
<table border="0" class="bodyTable">
<tr class="a">
<th width="20%">Status</th>
<td width="80%" align="left"><figure><img src="images/icon_success_sml.gif" alt="" /></figure>&#160;License defined in the pom of the dependency</td></tr>
<tr class="b">
<th width="20%">GroupId:ArtifactId:Version</th>
<td width="80%" align="left">org.apache.james:apache-mime4j-dom:0.8.9</td></tr>
<tr class="a">
<th width="20%">Scope</th>
<td width="80%" align="left">compile</td></tr>
<tr class="b">
<th width="20%">Classifier</th>
<td width="80%" align="left"></td></tr>
<tr class="a">
<th width="20%">Type</th>
<td width="80%" align="left">bundle</td></tr>
<tr class="b">
<th width="20%">License(s)</th>
<td width="80%" align="left">Apache License, Version 2.0</td></tr></table><a href="#Overview">Back to top</a><br /></section><section>
<h3><a name="org.apache.logging.log4j:log4j-api:2.24.3"></a>org.apache.logging.log4j:log4j-api:2.24.3</h3>
<table border="0" class="bodyTable">
<tr class="a">
<th width="20%">Status</th>
<td width="80%" align="left"><figure><img src="images/icon_success_sml.gif" alt="" /></figure>&#160;License defined in the pom of the dependency</td></tr>
<tr class="b">
<th width="20%">GroupId:ArtifactId:Version</th>
<td width="80%" align="left">org.apache.logging.log4j:log4j-api:2.24.3</td></tr>
<tr class="a">
<th width="20%">Scope</th>
<td width="80%" align="left">compile</td></tr>
<tr class="b">
<th width="20%">Classifier</th>
<td width="80%" align="left"></td></tr>
<tr class="a">
<th width="20%">Type</th>
<td width="80%" align="left">jar</td></tr>
<tr class="b">
<th width="20%">License(s)</th>
<td width="80%" align="left">Apache-2.0</td></tr></table><a href="#Overview">Back to top</a><br /></section><section>
<h3><a name="org.apache.logging.log4j:log4j-to-slf4j:2.24.3"></a>org.apache.logging.log4j:log4j-to-slf4j:2.24.3</h3>
<table border="0" class="bodyTable">
<tr class="a">
<th width="20%">Status</th>
<td width="80%" align="left"><figure><img src="images/icon_success_sml.gif" alt="" /></figure>&#160;License defined in the pom of the dependency</td></tr>
<tr class="b">
<th width="20%">GroupId:ArtifactId:Version</th>
<td width="80%" align="left">org.apache.logging.log4j:log4j-to-slf4j:2.24.3</td></tr>
<tr class="a">
<th width="20%">Scope</th>
<td width="80%" align="left">compile</td></tr>
<tr class="b">
<th width="20%">Classifier</th>
<td width="80%" align="left"></td></tr>
<tr class="a">
<th width="20%">Type</th>
<td width="80%" align="left">jar</td></tr>
<tr class="b">
<th width="20%">License(s)</th>
<td width="80%" align="left">Apache-2.0</td></tr></table><a href="#Overview">Back to top</a><br /></section><section>
<h3><a name="org.apache.pdfbox:fontbox:3.0.1"></a>org.apache.pdfbox:fontbox:3.0.1</h3>
<table border="0" class="bodyTable">
<tr class="a">
<th width="20%">Status</th>
<td width="80%" align="left"><figure><img src="images/icon_success_sml.gif" alt="" /></figure>&#160;License defined in the pom of the dependency</td></tr>
<tr class="b">
<th width="20%">GroupId:ArtifactId:Version</th>
<td width="80%" align="left">org.apache.pdfbox:fontbox:3.0.1</td></tr>
<tr class="a">
<th width="20%">Scope</th>
<td width="80%" align="left">compile</td></tr>
<tr class="b">
<th width="20%">Classifier</th>
<td width="80%" align="left"></td></tr>
<tr class="a">
<th width="20%">Type</th>
<td width="80%" align="left">bundle</td></tr>
<tr class="b">
<th width="20%">License(s)</th>
<td width="80%" align="left">Apache-2.0</td></tr></table><a href="#Overview">Back to top</a><br /></section><section>
<h3><a name="org.apache.pdfbox:jbig2-imageio:3.0.4"></a>org.apache.pdfbox:jbig2-imageio:3.0.4</h3>
<table border="0" class="bodyTable">
<tr class="a">
<th width="20%">Status</th>
<td width="80%" align="left"><figure><img src="images/icon_success_sml.gif" alt="" /></figure>&#160;License defined in the pom of the dependency</td></tr>
<tr class="b">
<th width="20%">GroupId:ArtifactId:Version</th>
<td width="80%" align="left">org.apache.pdfbox:jbig2-imageio:3.0.4</td></tr>
<tr class="a">
<th width="20%">Scope</th>
<td width="80%" align="left">compile</td></tr>
<tr class="b">
<th width="20%">Classifier</th>
<td width="80%" align="left"></td></tr>
<tr class="a">
<th width="20%">Type</th>
<td width="80%" align="left">jar</td></tr>
<tr class="b">
<th width="20%">License(s)</th>
<td width="80%" align="left">Apache License, Version 2.0</td></tr></table><a href="#Overview">Back to top</a><br /></section><section>
<h3><a name="org.apache.pdfbox:jempbox:1.8.17"></a>org.apache.pdfbox:jempbox:1.8.17</h3>
<table border="0" class="bodyTable">
<tr class="a">
<th width="20%">Status</th>
<td width="80%" align="left"><figure><img src="images/icon_success_sml.gif" alt="" /></figure>&#160;License defined in the pom of the dependency</td></tr>
<tr class="b">
<th width="20%">GroupId:ArtifactId:Version</th>
<td width="80%" align="left">org.apache.pdfbox:jempbox:1.8.17</td></tr>
<tr class="a">
<th width="20%">Scope</th>
<td width="80%" align="left">compile</td></tr>
<tr class="b">
<th width="20%">Classifier</th>
<td width="80%" align="left"></td></tr>
<tr class="a">
<th width="20%">Type</th>
<td width="80%" align="left">bundle</td></tr>
<tr class="b">
<th width="20%">License(s)</th>
<td width="80%" align="left">Apache License, Version 2.0</td></tr></table><a href="#Overview">Back to top</a><br /></section><section>
<h3><a name="org.apache.pdfbox:pdfbox:3.0.1"></a>org.apache.pdfbox:pdfbox:3.0.1</h3>
<table border="0" class="bodyTable">
<tr class="a">
<th width="20%">Status</th>
<td width="80%" align="left"><figure><img src="images/icon_success_sml.gif" alt="" /></figure>&#160;License defined in the pom of the dependency</td></tr>
<tr class="b">
<th width="20%">GroupId:ArtifactId:Version</th>
<td width="80%" align="left">org.apache.pdfbox:pdfbox:3.0.1</td></tr>
<tr class="a">
<th width="20%">Scope</th>
<td width="80%" align="left">compile</td></tr>
<tr class="b">
<th width="20%">Classifier</th>
<td width="80%" align="left"></td></tr>
<tr class="a">
<th width="20%">Type</th>
<td width="80%" align="left">bundle</td></tr>
<tr class="b">
<th width="20%">License(s)</th>
<td width="80%" align="left">Apache-2.0</td></tr></table><a href="#Overview">Back to top</a><br /></section><section>
<h3><a name="org.apache.pdfbox:pdfbox-io:3.0.1"></a>org.apache.pdfbox:pdfbox-io:3.0.1</h3>
<table border="0" class="bodyTable">
<tr class="a">
<th width="20%">Status</th>
<td width="80%" align="left"><figure><img src="images/icon_success_sml.gif" alt="" /></figure>&#160;License defined in the pom of the dependency</td></tr>
<tr class="b">
<th width="20%">GroupId:ArtifactId:Version</th>
<td width="80%" align="left">org.apache.pdfbox:pdfbox-io:3.0.1</td></tr>
<tr class="a">
<th width="20%">Scope</th>
<td width="80%" align="left">compile</td></tr>
<tr class="b">
<th width="20%">Classifier</th>
<td width="80%" align="left"></td></tr>
<tr class="a">
<th width="20%">Type</th>
<td width="80%" align="left">bundle</td></tr>
<tr class="b">
<th width="20%">License(s)</th>
<td width="80%" align="left">Apache-2.0</td></tr></table><a href="#Overview">Back to top</a><br /></section><section>
<h3><a name="org.apache.pdfbox:pdfbox-tools:2.0.29"></a>org.apache.pdfbox:pdfbox-tools:2.0.29</h3>
<table border="0" class="bodyTable">
<tr class="a">
<th width="20%">Status</th>
<td width="80%" align="left"><figure><img src="images/icon_success_sml.gif" alt="" /></figure>&#160;License defined in the pom of the dependency</td></tr>
<tr class="b">
<th width="20%">GroupId:ArtifactId:Version</th>
<td width="80%" align="left">org.apache.pdfbox:pdfbox-tools:2.0.29</td></tr>
<tr class="a">
<th width="20%">Scope</th>
<td width="80%" align="left">compile</td></tr>
<tr class="b">
<th width="20%">Classifier</th>
<td width="80%" align="left"></td></tr>
<tr class="a">
<th width="20%">Type</th>
<td width="80%" align="left">jar</td></tr>
<tr class="b">
<th width="20%">License(s)</th>
<td width="80%" align="left">Apache License, Version 2.0</td></tr></table><a href="#Overview">Back to top</a><br /></section><section>
<h3><a name="org.apache.pdfbox:xmpbox:2.0.29"></a>org.apache.pdfbox:xmpbox:2.0.29</h3>
<table border="0" class="bodyTable">
<tr class="a">
<th width="20%">Status</th>
<td width="80%" align="left"><figure><img src="images/icon_success_sml.gif" alt="" /></figure>&#160;License defined in the pom of the dependency</td></tr>
<tr class="b">
<th width="20%">GroupId:ArtifactId:Version</th>
<td width="80%" align="left">org.apache.pdfbox:xmpbox:2.0.29</td></tr>
<tr class="a">
<th width="20%">Scope</th>
<td width="80%" align="left">compile</td></tr>
<tr class="b">
<th width="20%">Classifier</th>
<td width="80%" align="left"></td></tr>
<tr class="a">
<th width="20%">Type</th>
<td width="80%" align="left">bundle</td></tr>
<tr class="b">
<th width="20%">License(s)</th>
<td width="80%" align="left">Apache License, Version 2.0</td></tr></table><a href="#Overview">Back to top</a><br /></section><section>
<h3><a name="org.apache.poi:poi:5.2.5"></a>org.apache.poi:poi:5.2.5</h3>
<table border="0" class="bodyTable">
<tr class="a">
<th width="20%">Status</th>
<td width="80%" align="left"><figure><img src="images/icon_success_sml.gif" alt="" /></figure>&#160;License defined in the pom of the dependency</td></tr>
<tr class="b">
<th width="20%">GroupId:ArtifactId:Version</th>
<td width="80%" align="left">org.apache.poi:poi:5.2.5</td></tr>
<tr class="a">
<th width="20%">Scope</th>
<td width="80%" align="left">compile</td></tr>
<tr class="b">
<th width="20%">Classifier</th>
<td width="80%" align="left"></td></tr>
<tr class="a">
<th width="20%">Type</th>
<td width="80%" align="left">jar</td></tr>
<tr class="b">
<th width="20%">License(s)</th>
<td width="80%" align="left">Apache License, Version 2.0</td></tr></table><a href="#Overview">Back to top</a><br /></section><section>
<h3><a name="org.apache.poi:poi-ooxml:5.2.5"></a>org.apache.poi:poi-ooxml:5.2.5</h3>
<table border="0" class="bodyTable">
<tr class="a">
<th width="20%">Status</th>
<td width="80%" align="left"><figure><img src="images/icon_success_sml.gif" alt="" /></figure>&#160;License defined in the pom of the dependency</td></tr>
<tr class="b">
<th width="20%">GroupId:ArtifactId:Version</th>
<td width="80%" align="left">org.apache.poi:poi-ooxml:5.2.5</td></tr>
<tr class="a">
<th width="20%">Scope</th>
<td width="80%" align="left">compile</td></tr>
<tr class="b">
<th width="20%">Classifier</th>
<td width="80%" align="left"></td></tr>
<tr class="a">
<th width="20%">Type</th>
<td width="80%" align="left">jar</td></tr>
<tr class="b">
<th width="20%">License(s)</th>
<td width="80%" align="left">Apache License, Version 2.0</td></tr></table><a href="#Overview">Back to top</a><br /></section><section>
<h3><a name="org.apache.poi:poi-ooxml-lite:5.2.5"></a>org.apache.poi:poi-ooxml-lite:5.2.5</h3>
<table border="0" class="bodyTable">
<tr class="a">
<th width="20%">Status</th>
<td width="80%" align="left"><figure><img src="images/icon_success_sml.gif" alt="" /></figure>&#160;License defined in the pom of the dependency</td></tr>
<tr class="b">
<th width="20%">GroupId:ArtifactId:Version</th>
<td width="80%" align="left">org.apache.poi:poi-ooxml-lite:5.2.5</td></tr>
<tr class="a">
<th width="20%">Scope</th>
<td width="80%" align="left">compile</td></tr>
<tr class="b">
<th width="20%">Classifier</th>
<td width="80%" align="left"></td></tr>
<tr class="a">
<th width="20%">Type</th>
<td width="80%" align="left">jar</td></tr>
<tr class="b">
<th width="20%">License(s)</th>
<td width="80%" align="left">Apache License, Version 2.0</td></tr></table><a href="#Overview">Back to top</a><br /></section><section>
<h3><a name="org.apache.poi:poi-scratchpad:5.2.5"></a>org.apache.poi:poi-scratchpad:5.2.5</h3>
<table border="0" class="bodyTable">
<tr class="a">
<th width="20%">Status</th>
<td width="80%" align="left"><figure><img src="images/icon_success_sml.gif" alt="" /></figure>&#160;License defined in the pom of the dependency</td></tr>
<tr class="b">
<th width="20%">GroupId:ArtifactId:Version</th>
<td width="80%" align="left">org.apache.poi:poi-scratchpad:5.2.5</td></tr>
<tr class="a">
<th width="20%">Scope</th>
<td width="80%" align="left">compile</td></tr>
<tr class="b">
<th width="20%">Classifier</th>
<td width="80%" align="left"></td></tr>
<tr class="a">
<th width="20%">Type</th>
<td width="80%" align="left">jar</td></tr>
<tr class="b">
<th width="20%">License(s)</th>
<td width="80%" align="left">Apache License, Version 2.0</td></tr></table><a href="#Overview">Back to top</a><br /></section><section>
<h3><a name="org.apache.tika:tika-core:2.9.1"></a>org.apache.tika:tika-core:2.9.1</h3>
<table border="0" class="bodyTable">
<tr class="a">
<th width="20%">Status</th>
<td width="80%" align="left"><figure><img src="images/icon_success_sml.gif" alt="" /></figure>&#160;License defined in the pom of the dependency</td></tr>
<tr class="b">
<th width="20%">GroupId:ArtifactId:Version</th>
<td width="80%" align="left">org.apache.tika:tika-core:2.9.1</td></tr>
<tr class="a">
<th width="20%">Scope</th>
<td width="80%" align="left">compile</td></tr>
<tr class="b">
<th width="20%">Classifier</th>
<td width="80%" align="left"></td></tr>
<tr class="a">
<th width="20%">Type</th>
<td width="80%" align="left">bundle</td></tr>
<tr class="b">
<th width="20%">License(s)</th>
<td width="80%" align="left">Apache-2.0</td></tr></table><a href="#Overview">Back to top</a><br /></section><section>
<h3><a name="org.apache.tika:tika-parser-apple-module:2.9.1"></a>org.apache.tika:tika-parser-apple-module:2.9.1</h3>
<table border="0" class="bodyTable">
<tr class="a">
<th width="20%">Status</th>
<td width="80%" align="left"><figure><img src="images/icon_success_sml.gif" alt="" /></figure>&#160;License defined in the pom of the dependency</td></tr>
<tr class="b">
<th width="20%">GroupId:ArtifactId:Version</th>
<td width="80%" align="left">org.apache.tika:tika-parser-apple-module:2.9.1</td></tr>
<tr class="a">
<th width="20%">Scope</th>
<td width="80%" align="left">compile</td></tr>
<tr class="b">
<th width="20%">Classifier</th>
<td width="80%" align="left"></td></tr>
<tr class="a">
<th width="20%">Type</th>
<td width="80%" align="left">jar</td></tr>
<tr class="b">
<th width="20%">License(s)</th>
<td width="80%" align="left">Apache-2.0</td></tr></table><a href="#Overview">Back to top</a><br /></section><section>
<h3><a name="org.apache.tika:tika-parser-audiovideo-module:2.9.1"></a>org.apache.tika:tika-parser-audiovideo-module:2.9.1</h3>
<table border="0" class="bodyTable">
<tr class="a">
<th width="20%">Status</th>
<td width="80%" align="left"><figure><img src="images/icon_success_sml.gif" alt="" /></figure>&#160;License defined in the pom of the dependency</td></tr>
<tr class="b">
<th width="20%">GroupId:ArtifactId:Version</th>
<td width="80%" align="left">org.apache.tika:tika-parser-audiovideo-module:2.9.1</td></tr>
<tr class="a">
<th width="20%">Scope</th>
<td width="80%" align="left">compile</td></tr>
<tr class="b">
<th width="20%">Classifier</th>
<td width="80%" align="left"></td></tr>
<tr class="a">
<th width="20%">Type</th>
<td width="80%" align="left">jar</td></tr>
<tr class="b">
<th width="20%">License(s)</th>
<td width="80%" align="left">Apache-2.0</td></tr></table><a href="#Overview">Back to top</a><br /></section><section>
<h3><a name="org.apache.tika:tika-parser-cad-module:2.9.1"></a>org.apache.tika:tika-parser-cad-module:2.9.1</h3>
<table border="0" class="bodyTable">
<tr class="a">
<th width="20%">Status</th>
<td width="80%" align="left"><figure><img src="images/icon_success_sml.gif" alt="" /></figure>&#160;License defined in the pom of the dependency</td></tr>
<tr class="b">
<th width="20%">GroupId:ArtifactId:Version</th>
<td width="80%" align="left">org.apache.tika:tika-parser-cad-module:2.9.1</td></tr>
<tr class="a">
<th width="20%">Scope</th>
<td width="80%" align="left">compile</td></tr>
<tr class="b">
<th width="20%">Classifier</th>
<td width="80%" align="left"></td></tr>
<tr class="a">
<th width="20%">Type</th>
<td width="80%" align="left">jar</td></tr>
<tr class="b">
<th width="20%">License(s)</th>
<td width="80%" align="left">Apache-2.0</td></tr></table><a href="#Overview">Back to top</a><br /></section><section>
<h3><a name="org.apache.tika:tika-parser-code-module:2.9.1"></a>org.apache.tika:tika-parser-code-module:2.9.1</h3>
<table border="0" class="bodyTable">
<tr class="a">
<th width="20%">Status</th>
<td width="80%" align="left"><figure><img src="images/icon_success_sml.gif" alt="" /></figure>&#160;License defined in the pom of the dependency</td></tr>
<tr class="b">
<th width="20%">GroupId:ArtifactId:Version</th>
<td width="80%" align="left">org.apache.tika:tika-parser-code-module:2.9.1</td></tr>
<tr class="a">
<th width="20%">Scope</th>
<td width="80%" align="left">compile</td></tr>
<tr class="b">
<th width="20%">Classifier</th>
<td width="80%" align="left"></td></tr>
<tr class="a">
<th width="20%">Type</th>
<td width="80%" align="left">jar</td></tr>
<tr class="b">
<th width="20%">License(s)</th>
<td width="80%" align="left">Apache-2.0</td></tr></table><a href="#Overview">Back to top</a><br /></section><section>
<h3><a name="org.apache.tika:tika-parser-crypto-module:2.9.1"></a>org.apache.tika:tika-parser-crypto-module:2.9.1</h3>
<table border="0" class="bodyTable">
<tr class="a">
<th width="20%">Status</th>
<td width="80%" align="left"><figure><img src="images/icon_success_sml.gif" alt="" /></figure>&#160;License defined in the pom of the dependency</td></tr>
<tr class="b">
<th width="20%">GroupId:ArtifactId:Version</th>
<td width="80%" align="left">org.apache.tika:tika-parser-crypto-module:2.9.1</td></tr>
<tr class="a">
<th width="20%">Scope</th>
<td width="80%" align="left">compile</td></tr>
<tr class="b">
<th width="20%">Classifier</th>
<td width="80%" align="left"></td></tr>
<tr class="a">
<th width="20%">Type</th>
<td width="80%" align="left">jar</td></tr>
<tr class="b">
<th width="20%">License(s)</th>
<td width="80%" align="left">Apache-2.0</td></tr></table><a href="#Overview">Back to top</a><br /></section><section>
<h3><a name="org.apache.tika:tika-parser-digest-commons:2.9.1"></a>org.apache.tika:tika-parser-digest-commons:2.9.1</h3>
<table border="0" class="bodyTable">
<tr class="a">
<th width="20%">Status</th>
<td width="80%" align="left"><figure><img src="images/icon_success_sml.gif" alt="" /></figure>&#160;License defined in the pom of the dependency</td></tr>
<tr class="b">
<th width="20%">GroupId:ArtifactId:Version</th>
<td width="80%" align="left">org.apache.tika:tika-parser-digest-commons:2.9.1</td></tr>
<tr class="a">
<th width="20%">Scope</th>
<td width="80%" align="left">compile</td></tr>
<tr class="b">
<th width="20%">Classifier</th>
<td width="80%" align="left"></td></tr>
<tr class="a">
<th width="20%">Type</th>
<td width="80%" align="left">jar</td></tr>
<tr class="b">
<th width="20%">License(s)</th>
<td width="80%" align="left">Apache-2.0</td></tr></table><a href="#Overview">Back to top</a><br /></section><section>
<h3><a name="org.apache.tika:tika-parser-font-module:2.9.1"></a>org.apache.tika:tika-parser-font-module:2.9.1</h3>
<table border="0" class="bodyTable">
<tr class="a">
<th width="20%">Status</th>
<td width="80%" align="left"><figure><img src="images/icon_success_sml.gif" alt="" /></figure>&#160;License defined in the pom of the dependency</td></tr>
<tr class="b">
<th width="20%">GroupId:ArtifactId:Version</th>
<td width="80%" align="left">org.apache.tika:tika-parser-font-module:2.9.1</td></tr>
<tr class="a">
<th width="20%">Scope</th>
<td width="80%" align="left">compile</td></tr>
<tr class="b">
<th width="20%">Classifier</th>
<td width="80%" align="left"></td></tr>
<tr class="a">
<th width="20%">Type</th>
<td width="80%" align="left">jar</td></tr>
<tr class="b">
<th width="20%">License(s)</th>
<td width="80%" align="left">Apache-2.0</td></tr></table><a href="#Overview">Back to top</a><br /></section><section>
<h3><a name="org.apache.tika:tika-parser-html-module:2.9.1"></a>org.apache.tika:tika-parser-html-module:2.9.1</h3>
<table border="0" class="bodyTable">
<tr class="a">
<th width="20%">Status</th>
<td width="80%" align="left"><figure><img src="images/icon_success_sml.gif" alt="" /></figure>&#160;License defined in the pom of the dependency</td></tr>
<tr class="b">
<th width="20%">GroupId:ArtifactId:Version</th>
<td width="80%" align="left">org.apache.tika:tika-parser-html-module:2.9.1</td></tr>
<tr class="a">
<th width="20%">Scope</th>
<td width="80%" align="left">compile</td></tr>
<tr class="b">
<th width="20%">Classifier</th>
<td width="80%" align="left"></td></tr>
<tr class="a">
<th width="20%">Type</th>
<td width="80%" align="left">jar</td></tr>
<tr class="b">
<th width="20%">License(s)</th>
<td width="80%" align="left">Apache-2.0</td></tr></table><a href="#Overview">Back to top</a><br /></section><section>
<h3><a name="org.apache.tika:tika-parser-image-module:2.9.1"></a>org.apache.tika:tika-parser-image-module:2.9.1</h3>
<table border="0" class="bodyTable">
<tr class="a">
<th width="20%">Status</th>
<td width="80%" align="left"><figure><img src="images/icon_success_sml.gif" alt="" /></figure>&#160;License defined in the pom of the dependency</td></tr>
<tr class="b">
<th width="20%">GroupId:ArtifactId:Version</th>
<td width="80%" align="left">org.apache.tika:tika-parser-image-module:2.9.1</td></tr>
<tr class="a">
<th width="20%">Scope</th>
<td width="80%" align="left">compile</td></tr>
<tr class="b">
<th width="20%">Classifier</th>
<td width="80%" align="left"></td></tr>
<tr class="a">
<th width="20%">Type</th>
<td width="80%" align="left">jar</td></tr>
<tr class="b">
<th width="20%">License(s)</th>
<td width="80%" align="left">Apache-2.0</td></tr></table><a href="#Overview">Back to top</a><br /></section><section>
<h3><a name="org.apache.tika:tika-parser-mail-commons:2.9.1"></a>org.apache.tika:tika-parser-mail-commons:2.9.1</h3>
<table border="0" class="bodyTable">
<tr class="a">
<th width="20%">Status</th>
<td width="80%" align="left"><figure><img src="images/icon_success_sml.gif" alt="" /></figure>&#160;License defined in the pom of the dependency</td></tr>
<tr class="b">
<th width="20%">GroupId:ArtifactId:Version</th>
<td width="80%" align="left">org.apache.tika:tika-parser-mail-commons:2.9.1</td></tr>
<tr class="a">
<th width="20%">Scope</th>
<td width="80%" align="left">compile</td></tr>
<tr class="b">
<th width="20%">Classifier</th>
<td width="80%" align="left"></td></tr>
<tr class="a">
<th width="20%">Type</th>
<td width="80%" align="left">jar</td></tr>
<tr class="b">
<th width="20%">License(s)</th>
<td width="80%" align="left">Apache-2.0</td></tr></table><a href="#Overview">Back to top</a><br /></section><section>
<h3><a name="org.apache.tika:tika-parser-mail-module:2.9.1"></a>org.apache.tika:tika-parser-mail-module:2.9.1</h3>
<table border="0" class="bodyTable">
<tr class="a">
<th width="20%">Status</th>
<td width="80%" align="left"><figure><img src="images/icon_success_sml.gif" alt="" /></figure>&#160;License defined in the pom of the dependency</td></tr>
<tr class="b">
<th width="20%">GroupId:ArtifactId:Version</th>
<td width="80%" align="left">org.apache.tika:tika-parser-mail-module:2.9.1</td></tr>
<tr class="a">
<th width="20%">Scope</th>
<td width="80%" align="left">compile</td></tr>
<tr class="b">
<th width="20%">Classifier</th>
<td width="80%" align="left"></td></tr>
<tr class="a">
<th width="20%">Type</th>
<td width="80%" align="left">jar</td></tr>
<tr class="b">
<th width="20%">License(s)</th>
<td width="80%" align="left">Apache-2.0</td></tr></table><a href="#Overview">Back to top</a><br /></section><section>
<h3><a name="org.apache.tika:tika-parser-microsoft-module:2.9.1"></a>org.apache.tika:tika-parser-microsoft-module:2.9.1</h3>
<table border="0" class="bodyTable">
<tr class="a">
<th width="20%">Status</th>
<td width="80%" align="left"><figure><img src="images/icon_success_sml.gif" alt="" /></figure>&#160;License defined in the pom of the dependency</td></tr>
<tr class="b">
<th width="20%">GroupId:ArtifactId:Version</th>
<td width="80%" align="left">org.apache.tika:tika-parser-microsoft-module:2.9.1</td></tr>
<tr class="a">
<th width="20%">Scope</th>
<td width="80%" align="left">compile</td></tr>
<tr class="b">
<th width="20%">Classifier</th>
<td width="80%" align="left"></td></tr>
<tr class="a">
<th width="20%">Type</th>
<td width="80%" align="left">jar</td></tr>
<tr class="b">
<th width="20%">License(s)</th>
<td width="80%" align="left">Apache-2.0</td></tr></table><a href="#Overview">Back to top</a><br /></section><section>
<h3><a name="org.apache.tika:tika-parser-miscoffice-module:2.9.1"></a>org.apache.tika:tika-parser-miscoffice-module:2.9.1</h3>
<table border="0" class="bodyTable">
<tr class="a">
<th width="20%">Status</th>
<td width="80%" align="left"><figure><img src="images/icon_success_sml.gif" alt="" /></figure>&#160;License defined in the pom of the dependency</td></tr>
<tr class="b">
<th width="20%">GroupId:ArtifactId:Version</th>
<td width="80%" align="left">org.apache.tika:tika-parser-miscoffice-module:2.9.1</td></tr>
<tr class="a">
<th width="20%">Scope</th>
<td width="80%" align="left">compile</td></tr>
<tr class="b">
<th width="20%">Classifier</th>
<td width="80%" align="left"></td></tr>
<tr class="a">
<th width="20%">Type</th>
<td width="80%" align="left">jar</td></tr>
<tr class="b">
<th width="20%">License(s)</th>
<td width="80%" align="left">Apache-2.0</td></tr></table><a href="#Overview">Back to top</a><br /></section><section>
<h3><a name="org.apache.tika:tika-parser-news-module:2.9.1"></a>org.apache.tika:tika-parser-news-module:2.9.1</h3>
<table border="0" class="bodyTable">
<tr class="a">
<th width="20%">Status</th>
<td width="80%" align="left"><figure><img src="images/icon_success_sml.gif" alt="" /></figure>&#160;License defined in the pom of the dependency</td></tr>
<tr class="b">
<th width="20%">GroupId:ArtifactId:Version</th>
<td width="80%" align="left">org.apache.tika:tika-parser-news-module:2.9.1</td></tr>
<tr class="a">
<th width="20%">Scope</th>
<td width="80%" align="left">compile</td></tr>
<tr class="b">
<th width="20%">Classifier</th>
<td width="80%" align="left"></td></tr>
<tr class="a">
<th width="20%">Type</th>
<td width="80%" align="left">jar</td></tr>
<tr class="b">
<th width="20%">License(s)</th>
<td width="80%" align="left">Apache-2.0</td></tr></table><a href="#Overview">Back to top</a><br /></section><section>
<h3><a name="org.apache.tika:tika-parser-ocr-module:2.9.1"></a>org.apache.tika:tika-parser-ocr-module:2.9.1</h3>
<table border="0" class="bodyTable">
<tr class="a">
<th width="20%">Status</th>
<td width="80%" align="left"><figure><img src="images/icon_success_sml.gif" alt="" /></figure>&#160;License defined in the pom of the dependency</td></tr>
<tr class="b">
<th width="20%">GroupId:ArtifactId:Version</th>
<td width="80%" align="left">org.apache.tika:tika-parser-ocr-module:2.9.1</td></tr>
<tr class="a">
<th width="20%">Scope</th>
<td width="80%" align="left">compile</td></tr>
<tr class="b">
<th width="20%">Classifier</th>
<td width="80%" align="left"></td></tr>
<tr class="a">
<th width="20%">Type</th>
<td width="80%" align="left">jar</td></tr>
<tr class="b">
<th width="20%">License(s)</th>
<td width="80%" align="left">Apache-2.0</td></tr></table><a href="#Overview">Back to top</a><br /></section><section>
<h3><a name="org.apache.tika:tika-parser-pdf-module:2.9.1"></a>org.apache.tika:tika-parser-pdf-module:2.9.1</h3>
<table border="0" class="bodyTable">
<tr class="a">
<th width="20%">Status</th>
<td width="80%" align="left"><figure><img src="images/icon_success_sml.gif" alt="" /></figure>&#160;License defined in the pom of the dependency</td></tr>
<tr class="b">
<th width="20%">GroupId:ArtifactId:Version</th>
<td width="80%" align="left">org.apache.tika:tika-parser-pdf-module:2.9.1</td></tr>
<tr class="a">
<th width="20%">Scope</th>
<td width="80%" align="left">compile</td></tr>
<tr class="b">
<th width="20%">Classifier</th>
<td width="80%" align="left"></td></tr>
<tr class="a">
<th width="20%">Type</th>
<td width="80%" align="left">jar</td></tr>
<tr class="b">
<th width="20%">License(s)</th>
<td width="80%" align="left">Apache-2.0</td></tr></table><a href="#Overview">Back to top</a><br /></section><section>
<h3><a name="org.apache.tika:tika-parser-pkg-module:2.9.1"></a>org.apache.tika:tika-parser-pkg-module:2.9.1</h3>
<table border="0" class="bodyTable">
<tr class="a">
<th width="20%">Status</th>
<td width="80%" align="left"><figure><img src="images/icon_success_sml.gif" alt="" /></figure>&#160;License defined in the pom of the dependency</td></tr>
<tr class="b">
<th width="20%">GroupId:ArtifactId:Version</th>
<td width="80%" align="left">org.apache.tika:tika-parser-pkg-module:2.9.1</td></tr>
<tr class="a">
<th width="20%">Scope</th>
<td width="80%" align="left">compile</td></tr>
<tr class="b">
<th width="20%">Classifier</th>
<td width="80%" align="left"></td></tr>
<tr class="a">
<th width="20%">Type</th>
<td width="80%" align="left">jar</td></tr>
<tr class="b">
<th width="20%">License(s)</th>
<td width="80%" align="left">Apache-2.0</td></tr></table><a href="#Overview">Back to top</a><br /></section><section>
<h3><a name="org.apache.tika:tika-parser-text-module:2.9.1"></a>org.apache.tika:tika-parser-text-module:2.9.1</h3>
<table border="0" class="bodyTable">
<tr class="a">
<th width="20%">Status</th>
<td width="80%" align="left"><figure><img src="images/icon_success_sml.gif" alt="" /></figure>&#160;License defined in the pom of the dependency</td></tr>
<tr class="b">
<th width="20%">GroupId:ArtifactId:Version</th>
<td width="80%" align="left">org.apache.tika:tika-parser-text-module:2.9.1</td></tr>
<tr class="a">
<th width="20%">Scope</th>
<td width="80%" align="left">compile</td></tr>
<tr class="b">
<th width="20%">Classifier</th>
<td width="80%" align="left"></td></tr>
<tr class="a">
<th width="20%">Type</th>
<td width="80%" align="left">jar</td></tr>
<tr class="b">
<th width="20%">License(s)</th>
<td width="80%" align="left">Apache-2.0</td></tr></table><a href="#Overview">Back to top</a><br /></section><section>
<h3><a name="org.apache.tika:tika-parser-webarchive-module:2.9.1"></a>org.apache.tika:tika-parser-webarchive-module:2.9.1</h3>
<table border="0" class="bodyTable">
<tr class="a">
<th width="20%">Status</th>
<td width="80%" align="left"><figure><img src="images/icon_success_sml.gif" alt="" /></figure>&#160;License defined in the pom of the dependency</td></tr>
<tr class="b">
<th width="20%">GroupId:ArtifactId:Version</th>
<td width="80%" align="left">org.apache.tika:tika-parser-webarchive-module:2.9.1</td></tr>
<tr class="a">
<th width="20%">Scope</th>
<td width="80%" align="left">compile</td></tr>
<tr class="b">
<th width="20%">Classifier</th>
<td width="80%" align="left"></td></tr>
<tr class="a">
<th width="20%">Type</th>
<td width="80%" align="left">jar</td></tr>
<tr class="b">
<th width="20%">License(s)</th>
<td width="80%" align="left">Apache-2.0</td></tr></table><a href="#Overview">Back to top</a><br /></section><section>
<h3><a name="org.apache.tika:tika-parser-xml-module:2.9.1"></a>org.apache.tika:tika-parser-xml-module:2.9.1</h3>
<table border="0" class="bodyTable">
<tr class="a">
<th width="20%">Status</th>
<td width="80%" align="left"><figure><img src="images/icon_success_sml.gif" alt="" /></figure>&#160;License defined in the pom of the dependency</td></tr>
<tr class="b">
<th width="20%">GroupId:ArtifactId:Version</th>
<td width="80%" align="left">org.apache.tika:tika-parser-xml-module:2.9.1</td></tr>
<tr class="a">
<th width="20%">Scope</th>
<td width="80%" align="left">compile</td></tr>
<tr class="b">
<th width="20%">Classifier</th>
<td width="80%" align="left"></td></tr>
<tr class="a">
<th width="20%">Type</th>
<td width="80%" align="left">jar</td></tr>
<tr class="b">
<th width="20%">License(s)</th>
<td width="80%" align="left">Apache-2.0</td></tr></table><a href="#Overview">Back to top</a><br /></section><section>
<h3><a name="org.apache.tika:tika-parser-xmp-commons:2.9.1"></a>org.apache.tika:tika-parser-xmp-commons:2.9.1</h3>
<table border="0" class="bodyTable">
<tr class="a">
<th width="20%">Status</th>
<td width="80%" align="left"><figure><img src="images/icon_success_sml.gif" alt="" /></figure>&#160;License defined in the pom of the dependency</td></tr>
<tr class="b">
<th width="20%">GroupId:ArtifactId:Version</th>
<td width="80%" align="left">org.apache.tika:tika-parser-xmp-commons:2.9.1</td></tr>
<tr class="a">
<th width="20%">Scope</th>
<td width="80%" align="left">compile</td></tr>
<tr class="b">
<th width="20%">Classifier</th>
<td width="80%" align="left"></td></tr>
<tr class="a">
<th width="20%">Type</th>
<td width="80%" align="left">jar</td></tr>
<tr class="b">
<th width="20%">License(s)</th>
<td width="80%" align="left">Apache-2.0</td></tr></table><a href="#Overview">Back to top</a><br /></section><section>
<h3><a name="org.apache.tika:tika-parser-zip-commons:2.9.1"></a>org.apache.tika:tika-parser-zip-commons:2.9.1</h3>
<table border="0" class="bodyTable">
<tr class="a">
<th width="20%">Status</th>
<td width="80%" align="left"><figure><img src="images/icon_success_sml.gif" alt="" /></figure>&#160;License defined in the pom of the dependency</td></tr>
<tr class="b">
<th width="20%">GroupId:ArtifactId:Version</th>
<td width="80%" align="left">org.apache.tika:tika-parser-zip-commons:2.9.1</td></tr>
<tr class="a">
<th width="20%">Scope</th>
<td width="80%" align="left">compile</td></tr>
<tr class="b">
<th width="20%">Classifier</th>
<td width="80%" align="left"></td></tr>
<tr class="a">
<th width="20%">Type</th>
<td width="80%" align="left">jar</td></tr>
<tr class="b">
<th width="20%">License(s)</th>
<td width="80%" align="left">Apache-2.0</td></tr></table><a href="#Overview">Back to top</a><br /></section><section>
<h3><a name="org.apache.tika:tika-parsers-standard-package:2.9.1"></a>org.apache.tika:tika-parsers-standard-package:2.9.1</h3>
<table border="0" class="bodyTable">
<tr class="a">
<th width="20%">Status</th>
<td width="80%" align="left"><figure><img src="images/icon_success_sml.gif" alt="" /></figure>&#160;License defined in the pom of the dependency</td></tr>
<tr class="b">
<th width="20%">GroupId:ArtifactId:Version</th>
<td width="80%" align="left">org.apache.tika:tika-parsers-standard-package:2.9.1</td></tr>
<tr class="a">
<th width="20%">Scope</th>
<td width="80%" align="left">compile</td></tr>
<tr class="b">
<th width="20%">Classifier</th>
<td width="80%" align="left"></td></tr>
<tr class="a">
<th width="20%">Type</th>
<td width="80%" align="left">jar</td></tr>
<tr class="b">
<th width="20%">License(s)</th>
<td width="80%" align="left">Apache-2.0</td></tr></table><a href="#Overview">Back to top</a><br /></section><section>
<h3><a name="org.apache.tomcat.embed:tomcat-embed-core:10.1.41"></a>org.apache.tomcat.embed:tomcat-embed-core:10.1.41</h3>
<table border="0" class="bodyTable">
<tr class="a">
<th width="20%">Status</th>
<td width="80%" align="left"><figure><img src="images/icon_success_sml.gif" alt="" /></figure>&#160;License defined in the pom of the dependency</td></tr>
<tr class="b">
<th width="20%">GroupId:ArtifactId:Version</th>
<td width="80%" align="left">org.apache.tomcat.embed:tomcat-embed-core:10.1.41</td></tr>
<tr class="a">
<th width="20%">Scope</th>
<td width="80%" align="left">compile</td></tr>
<tr class="b">
<th width="20%">Classifier</th>
<td width="80%" align="left"></td></tr>
<tr class="a">
<th width="20%">Type</th>
<td width="80%" align="left">jar</td></tr>
<tr class="b">
<th width="20%">License(s)</th>
<td width="80%" align="left">Apache License, Version 2.0</td></tr></table><a href="#Overview">Back to top</a><br /></section><section>
<h3><a name="org.apache.tomcat.embed:tomcat-embed-el:10.1.41"></a>org.apache.tomcat.embed:tomcat-embed-el:10.1.41</h3>
<table border="0" class="bodyTable">
<tr class="a">
<th width="20%">Status</th>
<td width="80%" align="left"><figure><img src="images/icon_success_sml.gif" alt="" /></figure>&#160;License defined in the pom of the dependency</td></tr>
<tr class="b">
<th width="20%">GroupId:ArtifactId:Version</th>
<td width="80%" align="left">org.apache.tomcat.embed:tomcat-embed-el:10.1.41</td></tr>
<tr class="a">
<th width="20%">Scope</th>
<td width="80%" align="left">compile</td></tr>
<tr class="b">
<th width="20%">Classifier</th>
<td width="80%" align="left"></td></tr>
<tr class="a">
<th width="20%">Type</th>
<td width="80%" align="left">jar</td></tr>
<tr class="b">
<th width="20%">License(s)</th>
<td width="80%" align="left">Apache License, Version 2.0</td></tr></table><a href="#Overview">Back to top</a><br /></section><section>
<h3><a name="org.apache.tomcat.embed:tomcat-embed-websocket:10.1.41"></a>org.apache.tomcat.embed:tomcat-embed-websocket:10.1.41</h3>
<table border="0" class="bodyTable">
<tr class="a">
<th width="20%">Status</th>
<td width="80%" align="left"><figure><img src="images/icon_success_sml.gif" alt="" /></figure>&#160;License defined in the pom of the dependency</td></tr>
<tr class="b">
<th width="20%">GroupId:ArtifactId:Version</th>
<td width="80%" align="left">org.apache.tomcat.embed:tomcat-embed-websocket:10.1.41</td></tr>
<tr class="a">
<th width="20%">Scope</th>
<td width="80%" align="left">compile</td></tr>
<tr class="b">
<th width="20%">Classifier</th>
<td width="80%" align="left"></td></tr>
<tr class="a">
<th width="20%">Type</th>
<td width="80%" align="left">jar</td></tr>
<tr class="b">
<th width="20%">License(s)</th>
<td width="80%" align="left">Apache License, Version 2.0</td></tr></table><a href="#Overview">Back to top</a><br /></section><section>
<h3><a name="org.apache.xmlbeans:xmlbeans:5.2.0"></a>org.apache.xmlbeans:xmlbeans:5.2.0</h3>
<table border="0" class="bodyTable">
<tr class="a">
<th width="20%">Status</th>
<td width="80%" align="left"><figure><img src="images/icon_success_sml.gif" alt="" /></figure>&#160;License defined in the pom of the dependency</td></tr>
<tr class="b">
<th width="20%">GroupId:ArtifactId:Version</th>
<td width="80%" align="left">org.apache.xmlbeans:xmlbeans:5.2.0</td></tr>
<tr class="a">
<th width="20%">Scope</th>
<td width="80%" align="left">compile</td></tr>
<tr class="b">
<th width="20%">Classifier</th>
<td width="80%" align="left"></td></tr>
<tr class="a">
<th width="20%">Type</th>
<td width="80%" align="left">jar</td></tr>
<tr class="b">
<th width="20%">License(s)</th>
<td width="80%" align="left">The Apache Software License, Version 2.0</td></tr></table><a href="#Overview">Back to top</a><br /></section><section>
<h3><a name="org.apiguardian:apiguardian-api:1.1.2"></a>org.apiguardian:apiguardian-api:1.1.2</h3>
<table border="0" class="bodyTable">
<tr class="a">
<th width="20%">Status</th>
<td width="80%" align="left"><figure><img src="images/icon_success_sml.gif" alt="" /></figure>&#160;License defined in the pom of the dependency</td></tr>
<tr class="b">
<th width="20%">GroupId:ArtifactId:Version</th>
<td width="80%" align="left">org.apiguardian:apiguardian-api:1.1.2</td></tr>
<tr class="a">
<th width="20%">Scope</th>
<td width="80%" align="left">compile</td></tr>
<tr class="b">
<th width="20%">Classifier</th>
<td width="80%" align="left"></td></tr>
<tr class="a">
<th width="20%">Type</th>
<td width="80%" align="left">jar</td></tr>
<tr class="b">
<th width="20%">License(s)</th>
<td width="80%" align="left">The Apache License, Version 2.0</td></tr></table><a href="#Overview">Back to top</a><br /></section><section>
<h3><a name="org.aspectj:aspectjweaver:1.9.24"></a>org.aspectj:aspectjweaver:1.9.24</h3>
<table border="0" class="bodyTable">
<tr class="a">
<th width="20%">Status</th>
<td width="80%" align="left"><figure><img src="images/icon_success_sml.gif" alt="" /></figure>&#160;License defined in the pom of the dependency</td></tr>
<tr class="b">
<th width="20%">GroupId:ArtifactId:Version</th>
<td width="80%" align="left">org.aspectj:aspectjweaver:1.9.24</td></tr>
<tr class="a">
<th width="20%">Scope</th>
<td width="80%" align="left">compile</td></tr>
<tr class="b">
<th width="20%">Classifier</th>
<td width="80%" align="left"></td></tr>
<tr class="a">
<th width="20%">Type</th>
<td width="80%" align="left">jar</td></tr>
<tr class="b">
<th width="20%">License(s)</th>
<td width="80%" align="left">Eclipse Public License - v 2.0</td></tr></table><a href="#Overview">Back to top</a><br /></section><section>
<h3><a name="org.bouncycastle:bcmail-jdk18on:1.76"></a>org.bouncycastle:bcmail-jdk18on:1.76</h3>
<table border="0" class="bodyTable">
<tr class="a">
<th width="20%">Status</th>
<td width="80%" align="left"><figure><img src="images/icon_success_sml.gif" alt="" /></figure>&#160;License defined in the pom of the dependency</td></tr>
<tr class="b">
<th width="20%">GroupId:ArtifactId:Version</th>
<td width="80%" align="left">org.bouncycastle:bcmail-jdk18on:1.76</td></tr>
<tr class="a">
<th width="20%">Scope</th>
<td width="80%" align="left">compile</td></tr>
<tr class="b">
<th width="20%">Classifier</th>
<td width="80%" align="left"></td></tr>
<tr class="a">
<th width="20%">Type</th>
<td width="80%" align="left">jar</td></tr>
<tr class="b">
<th width="20%">License(s)</th>
<td width="80%" align="left">Bouncy Castle Licence</td></tr></table><a href="#Overview">Back to top</a><br /></section><section>
<h3><a name="org.bouncycastle:bcpkix-jdk18on:1.76"></a>org.bouncycastle:bcpkix-jdk18on:1.76</h3>
<table border="0" class="bodyTable">
<tr class="a">
<th width="20%">Status</th>
<td width="80%" align="left"><figure><img src="images/icon_success_sml.gif" alt="" /></figure>&#160;License defined in the pom of the dependency</td></tr>
<tr class="b">
<th width="20%">GroupId:ArtifactId:Version</th>
<td width="80%" align="left">org.bouncycastle:bcpkix-jdk18on:1.76</td></tr>
<tr class="a">
<th width="20%">Scope</th>
<td width="80%" align="left">compile</td></tr>
<tr class="b">
<th width="20%">Classifier</th>
<td width="80%" align="left"></td></tr>
<tr class="a">
<th width="20%">Type</th>
<td width="80%" align="left">jar</td></tr>
<tr class="b">
<th width="20%">License(s)</th>
<td width="80%" align="left">Bouncy Castle Licence</td></tr></table><a href="#Overview">Back to top</a><br /></section><section>
<h3><a name="org.bouncycastle:bcprov-jdk18on:1.76"></a>org.bouncycastle:bcprov-jdk18on:1.76</h3>
<table border="0" class="bodyTable">
<tr class="a">
<th width="20%">Status</th>
<td width="80%" align="left"><figure><img src="images/icon_success_sml.gif" alt="" /></figure>&#160;License defined in the pom of the dependency</td></tr>
<tr class="b">
<th width="20%">GroupId:ArtifactId:Version</th>
<td width="80%" align="left">org.bouncycastle:bcprov-jdk18on:1.76</td></tr>
<tr class="a">
<th width="20%">Scope</th>
<td width="80%" align="left">compile</td></tr>
<tr class="b">
<th width="20%">Classifier</th>
<td width="80%" align="left"></td></tr>
<tr class="a">
<th width="20%">Type</th>
<td width="80%" align="left">jar</td></tr>
<tr class="b">
<th width="20%">License(s)</th>
<td width="80%" align="left">Bouncy Castle Licence</td></tr></table><a href="#Overview">Back to top</a><br /></section><section>
<h3><a name="org.bouncycastle:bcutil-jdk18on:1.76"></a>org.bouncycastle:bcutil-jdk18on:1.76</h3>
<table border="0" class="bodyTable">
<tr class="a">
<th width="20%">Status</th>
<td width="80%" align="left"><figure><img src="images/icon_success_sml.gif" alt="" /></figure>&#160;License defined in the pom of the dependency</td></tr>
<tr class="b">
<th width="20%">GroupId:ArtifactId:Version</th>
<td width="80%" align="left">org.bouncycastle:bcutil-jdk18on:1.76</td></tr>
<tr class="a">
<th width="20%">Scope</th>
<td width="80%" align="left">compile</td></tr>
<tr class="b">
<th width="20%">Classifier</th>
<td width="80%" align="left"></td></tr>
<tr class="a">
<th width="20%">Type</th>
<td width="80%" align="left">jar</td></tr>
<tr class="b">
<th width="20%">License(s)</th>
<td width="80%" align="left">Bouncy Castle Licence</td></tr></table><a href="#Overview">Back to top</a><br /></section><section>
<h3><a name="org.brotli:dec:0.1.2"></a>org.brotli:dec:0.1.2</h3>
<table border="0" class="bodyTable">
<tr class="a">
<th width="20%">Status</th>
<td width="80%" align="left"><figure><img src="images/icon_success_sml.gif" alt="" /></figure>&#160;License defined in the pom of the dependency</td></tr>
<tr class="b">
<th width="20%">GroupId:ArtifactId:Version</th>
<td width="80%" align="left">org.brotli:dec:0.1.2</td></tr>
<tr class="a">
<th width="20%">Scope</th>
<td width="80%" align="left">compile</td></tr>
<tr class="b">
<th width="20%">Classifier</th>
<td width="80%" align="left"></td></tr>
<tr class="a">
<th width="20%">Type</th>
<td width="80%" align="left">jar</td></tr>
<tr class="b">
<th width="20%">License(s)</th>
<td width="80%" align="left">MIT License</td></tr></table><a href="#Overview">Back to top</a><br /></section><section>
<h3><a name="org.ccil.cowan.tagsoup:tagsoup:1.2.1"></a>org.ccil.cowan.tagsoup:tagsoup:1.2.1</h3>
<table border="0" class="bodyTable">
<tr class="a">
<th width="20%">Status</th>
<td width="80%" align="left"><figure><img src="images/icon_success_sml.gif" alt="" /></figure>&#160;License defined in the pom of the dependency</td></tr>
<tr class="b">
<th width="20%">GroupId:ArtifactId:Version</th>
<td width="80%" align="left">org.ccil.cowan.tagsoup:tagsoup:1.2.1</td></tr>
<tr class="a">
<th width="20%">Scope</th>
<td width="80%" align="left">compile</td></tr>
<tr class="b">
<th width="20%">Classifier</th>
<td width="80%" align="left"></td></tr>
<tr class="a">
<th width="20%">Type</th>
<td width="80%" align="left">jar</td></tr>
<tr class="b">
<th width="20%">License(s)</th>
<td width="80%" align="left">Apache License 2.0</td></tr></table><a href="#Overview">Back to top</a><br /></section><section>
<h3><a name="org.codelibs:jhighlight:1.1.0"></a>org.codelibs:jhighlight:1.1.0</h3>
<table border="0" class="bodyTable">
<tr class="a">
<th width="20%">Status</th>
<td width="80%" align="left"><figure><img src="images/icon_success_sml.gif" alt="" /></figure>&#160;License defined in the pom of the dependency</td></tr>
<tr class="b">
<th width="20%">GroupId:ArtifactId:Version</th>
<td width="80%" align="left">org.codelibs:jhighlight:1.1.0</td></tr>
<tr class="a">
<th width="20%">Scope</th>
<td width="80%" align="left">compile</td></tr>
<tr class="b">
<th width="20%">Classifier</th>
<td width="80%" align="left"></td></tr>
<tr class="a">
<th width="20%">Type</th>
<td width="80%" align="left">jar</td></tr>
<tr class="b">
<th width="20%">License(s)</th>
<td width="80%" align="left">CDDL, v1.0<br />LGPL, v2.1 or later</td></tr></table><a href="#Overview">Back to top</a><br /></section><section>
<h3><a name="org.eclipse.angus:angus-activation:2.0.2"></a>org.eclipse.angus:angus-activation:2.0.2</h3>
<table border="0" class="bodyTable">
<tr class="a">
<th width="20%">Status</th>
<td width="80%" align="left"><figure><img src="images/icon_success_sml.gif" alt="" /></figure>&#160;License defined in the pom of the dependency</td></tr>
<tr class="b">
<th width="20%">GroupId:ArtifactId:Version</th>
<td width="80%" align="left">org.eclipse.angus:angus-activation:2.0.2</td></tr>
<tr class="a">
<th width="20%">Scope</th>
<td width="80%" align="left">runtime</td></tr>
<tr class="b">
<th width="20%">Classifier</th>
<td width="80%" align="left"></td></tr>
<tr class="a">
<th width="20%">Type</th>
<td width="80%" align="left">jar</td></tr>
<tr class="b">
<th width="20%">License(s)</th>
<td width="80%" align="left">EDL 1.0</td></tr></table><a href="#Overview">Back to top</a><br /></section><section>
<h3><a name="org.eclipse.parsson:parsson:1.0.5"></a>org.eclipse.parsson:parsson:1.0.5</h3>
<table border="0" class="bodyTable">
<tr class="a">
<th width="20%">Status</th>
<td width="80%" align="left"><figure><img src="images/icon_success_sml.gif" alt="" /></figure>&#160;License defined in the pom of the dependency</td></tr>
<tr class="b">
<th width="20%">GroupId:ArtifactId:Version</th>
<td width="80%" align="left">org.eclipse.parsson:parsson:1.0.5</td></tr>
<tr class="a">
<th width="20%">Scope</th>
<td width="80%" align="left">compile</td></tr>
<tr class="b">
<th width="20%">Classifier</th>
<td width="80%" align="left"></td></tr>
<tr class="a">
<th width="20%">Type</th>
<td width="80%" align="left">jar</td></tr>
<tr class="b">
<th width="20%">License(s)</th>
<td width="80%" align="left">Eclipse Public License 2.0<br />GNU General Public License, version 2 with the GNU Classpath Exception</td></tr></table><a href="#Overview">Back to top</a><br /></section><section>
<h3><a name="org.elasticsearch.client:elasticsearch-rest-client:8.18.1"></a>org.elasticsearch.client:elasticsearch-rest-client:8.18.1</h3>
<table border="0" class="bodyTable">
<tr class="a">
<th width="20%">Status</th>
<td width="80%" align="left"><figure><img src="images/icon_success_sml.gif" alt="" /></figure>&#160;License defined in the pom of the dependency</td></tr>
<tr class="b">
<th width="20%">GroupId:ArtifactId:Version</th>
<td width="80%" align="left">org.elasticsearch.client:elasticsearch-rest-client:8.18.1</td></tr>
<tr class="a">
<th width="20%">Scope</th>
<td width="80%" align="left">compile</td></tr>
<tr class="b">
<th width="20%">Classifier</th>
<td width="80%" align="left"></td></tr>
<tr class="a">
<th width="20%">Type</th>
<td width="80%" align="left">jar</td></tr>
<tr class="b">
<th width="20%">License(s)</th>
<td width="80%" align="left">The Apache Software License, Version 2.0</td></tr></table><a href="#Overview">Back to top</a><br /></section><section>
<h3><a name="org.gagravarr:vorbis-java-core:0.8"></a>org.gagravarr:vorbis-java-core:0.8</h3>
<table border="0" class="bodyTable">
<tr class="a">
<th width="20%">Status</th>
<td width="80%" align="left"><figure><img src="images/icon_success_sml.gif" alt="" /></figure>&#160;License defined in the pom of the dependency</td></tr>
<tr class="b">
<th width="20%">GroupId:ArtifactId:Version</th>
<td width="80%" align="left">org.gagravarr:vorbis-java-core:0.8</td></tr>
<tr class="a">
<th width="20%">Scope</th>
<td width="80%" align="left">compile</td></tr>
<tr class="b">
<th width="20%">Classifier</th>
<td width="80%" align="left"></td></tr>
<tr class="a">
<th width="20%">Type</th>
<td width="80%" align="left">jar</td></tr>
<tr class="b">
<th width="20%">License(s)</th>
<td width="80%" align="left">The Apache Software License, Version 2.0</td></tr></table><a href="#Overview">Back to top</a><br /></section><section>
<h3><a name="org.gagravarr:vorbis-java-tika:0.8"></a>org.gagravarr:vorbis-java-tika:0.8</h3>
<table border="0" class="bodyTable">
<tr class="a">
<th width="20%">Status</th>
<td width="80%" align="left"><figure><img src="images/icon_success_sml.gif" alt="" /></figure>&#160;License defined in the pom of the dependency</td></tr>
<tr class="b">
<th width="20%">GroupId:ArtifactId:Version</th>
<td width="80%" align="left">org.gagravarr:vorbis-java-tika:0.8</td></tr>
<tr class="a">
<th width="20%">Scope</th>
<td width="80%" align="left">compile</td></tr>
<tr class="b">
<th width="20%">Classifier</th>
<td width="80%" align="left"></td></tr>
<tr class="a">
<th width="20%">Type</th>
<td width="80%" align="left">jar</td></tr>
<tr class="b">
<th width="20%">License(s)</th>
<td width="80%" align="left">The Apache Software License, Version 2.0</td></tr></table><a href="#Overview">Back to top</a><br /></section><section>
<h3><a name="org.glassfish.jaxb:jaxb-core:4.0.5"></a>org.glassfish.jaxb:jaxb-core:4.0.5</h3>
<table border="0" class="bodyTable">
<tr class="a">
<th width="20%">Status</th>
<td width="80%" align="left"><figure><img src="images/icon_success_sml.gif" alt="" /></figure>&#160;License defined in the pom of the dependency</td></tr>
<tr class="b">
<th width="20%">GroupId:ArtifactId:Version</th>
<td width="80%" align="left">org.glassfish.jaxb:jaxb-core:4.0.5</td></tr>
<tr class="a">
<th width="20%">Scope</th>
<td width="80%" align="left">compile</td></tr>
<tr class="b">
<th width="20%">Classifier</th>
<td width="80%" align="left"></td></tr>
<tr class="a">
<th width="20%">Type</th>
<td width="80%" align="left">jar</td></tr>
<tr class="b">
<th width="20%">License(s)</th>
<td width="80%" align="left">Eclipse Distribution License - v 1.0</td></tr></table><a href="#Overview">Back to top</a><br /></section><section>
<h3><a name="org.glassfish.jaxb:jaxb-runtime:4.0.5"></a>org.glassfish.jaxb:jaxb-runtime:4.0.5</h3>
<table border="0" class="bodyTable">
<tr class="a">
<th width="20%">Status</th>
<td width="80%" align="left"><figure><img src="images/icon_success_sml.gif" alt="" /></figure>&#160;License defined in the pom of the dependency</td></tr>
<tr class="b">
<th width="20%">GroupId:ArtifactId:Version</th>
<td width="80%" align="left">org.glassfish.jaxb:jaxb-runtime:4.0.5</td></tr>
<tr class="a">
<th width="20%">Scope</th>
<td width="80%" align="left">compile</td></tr>
<tr class="b">
<th width="20%">Classifier</th>
<td width="80%" align="left"></td></tr>
<tr class="a">
<th width="20%">Type</th>
<td width="80%" align="left">jar</td></tr>
<tr class="b">
<th width="20%">License(s)</th>
<td width="80%" align="left">Eclipse Distribution License - v 1.0</td></tr></table><a href="#Overview">Back to top</a><br /></section><section>
<h3><a name="org.glassfish.jaxb:txw2:4.0.5"></a>org.glassfish.jaxb:txw2:4.0.5</h3>
<table border="0" class="bodyTable">
<tr class="a">
<th width="20%">Status</th>
<td width="80%" align="left"><figure><img src="images/icon_success_sml.gif" alt="" /></figure>&#160;License defined in the pom of the dependency</td></tr>
<tr class="b">
<th width="20%">GroupId:ArtifactId:Version</th>
<td width="80%" align="left">org.glassfish.jaxb:txw2:4.0.5</td></tr>
<tr class="a">
<th width="20%">Scope</th>
<td width="80%" align="left">compile</td></tr>
<tr class="b">
<th width="20%">Classifier</th>
<td width="80%" align="left"></td></tr>
<tr class="a">
<th width="20%">Type</th>
<td width="80%" align="left">jar</td></tr>
<tr class="b">
<th width="20%">License(s)</th>
<td width="80%" align="left">Eclipse Distribution License - v 1.0</td></tr></table><a href="#Overview">Back to top</a><br /></section><section>
<h3><a name="org.hdrhistogram:HdrHistogram:2.2.2"></a>org.hdrhistogram:HdrHistogram:2.2.2</h3>
<table border="0" class="bodyTable">
<tr class="a">
<th width="20%">Status</th>
<td width="80%" align="left"><figure><img src="images/icon_success_sml.gif" alt="" /></figure>&#160;License defined in the pom of the dependency</td></tr>
<tr class="b">
<th width="20%">GroupId:ArtifactId:Version</th>
<td width="80%" align="left">org.hdrhistogram:HdrHistogram:2.2.2</td></tr>
<tr class="a">
<th width="20%">Scope</th>
<td width="80%" align="left">runtime</td></tr>
<tr class="b">
<th width="20%">Classifier</th>
<td width="80%" align="left"></td></tr>
<tr class="a">
<th width="20%">Type</th>
<td width="80%" align="left">bundle</td></tr>
<tr class="b">
<th width="20%">License(s)</th>
<td width="80%" align="left">BSD-2-Clause<br />Public Domain, per Creative Commons CC0</td></tr></table><a href="#Overview">Back to top</a><br /></section><section>
<h3><a name="org.hibernate.common:hibernate-commons-annotations:7.0.3.Final"></a>org.hibernate.common:hibernate-commons-annotations:7.0.3.Final</h3>
<table border="0" class="bodyTable">
<tr class="a">
<th width="20%">Status</th>
<td width="80%" align="left"><figure><img src="images/icon_success_sml.gif" alt="" /></figure>&#160;License defined in the pom of the dependency</td></tr>
<tr class="b">
<th width="20%">GroupId:ArtifactId:Version</th>
<td width="80%" align="left">org.hibernate.common:hibernate-commons-annotations:7.0.3.Final</td></tr>
<tr class="a">
<th width="20%">Scope</th>
<td width="80%" align="left">runtime</td></tr>
<tr class="b">
<th width="20%">Classifier</th>
<td width="80%" align="left"></td></tr>
<tr class="a">
<th width="20%">Type</th>
<td width="80%" align="left">jar</td></tr>
<tr class="b">
<th width="20%">License(s)</th>
<td width="80%" align="left">Apache License Version 2.0</td></tr></table><a href="#Overview">Back to top</a><br /></section><section>
<h3><a name="org.hibernate.orm:hibernate-core:6.6.15.Final"></a>org.hibernate.orm:hibernate-core:6.6.15.Final</h3>
<table border="0" class="bodyTable">
<tr class="a">
<th width="20%">Status</th>
<td width="80%" align="left"><figure><img src="images/icon_success_sml.gif" alt="" /></figure>&#160;License defined in the pom of the dependency</td></tr>
<tr class="b">
<th width="20%">GroupId:ArtifactId:Version</th>
<td width="80%" align="left">org.hibernate.orm:hibernate-core:6.6.15.Final</td></tr>
<tr class="a">
<th width="20%">Scope</th>
<td width="80%" align="left">compile</td></tr>
<tr class="b">
<th width="20%">Classifier</th>
<td width="80%" align="left"></td></tr>
<tr class="a">
<th width="20%">Type</th>
<td width="80%" align="left">jar</td></tr>
<tr class="b">
<th width="20%">License(s)</th>
<td width="80%" align="left">GNU Library General Public License v2.1 or later</td></tr></table><a href="#Overview">Back to top</a><br /></section><section>
<h3><a name="org.hibernate.validator:hibernate-validator:8.0.2.Final"></a>org.hibernate.validator:hibernate-validator:8.0.2.Final</h3>
<table border="0" class="bodyTable">
<tr class="a">
<th width="20%">Status</th>
<td width="80%" align="left"><figure><img src="images/icon_success_sml.gif" alt="" /></figure>&#160;License defined in the pom of the dependency</td></tr>
<tr class="b">
<th width="20%">GroupId:ArtifactId:Version</th>
<td width="80%" align="left">org.hibernate.validator:hibernate-validator:8.0.2.Final</td></tr>
<tr class="a">
<th width="20%">Scope</th>
<td width="80%" align="left">compile</td></tr>
<tr class="b">
<th width="20%">Classifier</th>
<td width="80%" align="left"></td></tr>
<tr class="a">
<th width="20%">Type</th>
<td width="80%" align="left">jar</td></tr>
<tr class="b">
<th width="20%">License(s)</th>
<td width="80%" align="left">Apache License 2.0</td></tr></table><a href="#Overview">Back to top</a><br /></section><section>
<h3><a name="org.jboss.logging:jboss-logging:3.6.1.Final"></a>org.jboss.logging:jboss-logging:3.6.1.Final</h3>
<table border="0" class="bodyTable">
<tr class="a">
<th width="20%">Status</th>
<td width="80%" align="left"><figure><img src="images/icon_success_sml.gif" alt="" /></figure>&#160;License defined in the pom of the dependency</td></tr>
<tr class="b">
<th width="20%">GroupId:ArtifactId:Version</th>
<td width="80%" align="left">org.jboss.logging:jboss-logging:3.6.1.Final</td></tr>
<tr class="a">
<th width="20%">Scope</th>
<td width="80%" align="left">compile</td></tr>
<tr class="b">
<th width="20%">Classifier</th>
<td width="80%" align="left"></td></tr>
<tr class="a">
<th width="20%">Type</th>
<td width="80%" align="left">jar</td></tr>
<tr class="b">
<th width="20%">License(s)</th>
<td width="80%" align="left">Apache License 2.0</td></tr></table><a href="#Overview">Back to top</a><br /></section><section>
<h3><a name="org.jdom:jdom2:2.0.6.1"></a>org.jdom:jdom2:2.0.6.1</h3>
<table border="0" class="bodyTable">
<tr class="a">
<th width="20%">Status</th>
<td width="80%" align="left"><figure><img src="images/icon_success_sml.gif" alt="" /></figure>&#160;License defined in the pom of the dependency</td></tr>
<tr class="b">
<th width="20%">GroupId:ArtifactId:Version</th>
<td width="80%" align="left">org.jdom:jdom2:2.0.6.1</td></tr>
<tr class="a">
<th width="20%">Scope</th>
<td width="80%" align="left">compile</td></tr>
<tr class="b">
<th width="20%">Classifier</th>
<td width="80%" align="left"></td></tr>
<tr class="a">
<th width="20%">Type</th>
<td width="80%" align="left">jar</td></tr>
<tr class="b">
<th width="20%">License(s)</th>
<td width="80%" align="left">Similar to Apache License but with the acknowledgment clause removed</td></tr></table><a href="#Overview">Back to top</a><br /></section><section>
<h3><a name="org.jetbrains:annotations:17.0.0"></a>org.jetbrains:annotations:17.0.0</h3>
<table border="0" class="bodyTable">
<tr class="a">
<th width="20%">Status</th>
<td width="80%" align="left"><figure><img src="images/icon_success_sml.gif" alt="" /></figure>&#160;License defined in the pom of the dependency</td></tr>
<tr class="b">
<th width="20%">GroupId:ArtifactId:Version</th>
<td width="80%" align="left">org.jetbrains:annotations:17.0.0</td></tr>
<tr class="a">
<th width="20%">Scope</th>
<td width="80%" align="left">compile</td></tr>
<tr class="b">
<th width="20%">Classifier</th>
<td width="80%" align="left"></td></tr>
<tr class="a">
<th width="20%">Type</th>
<td width="80%" align="left">jar</td></tr>
<tr class="b">
<th width="20%">License(s)</th>
<td width="80%" align="left">The Apache Software License, Version 2.0</td></tr></table><a href="#Overview">Back to top</a><br /></section><section>
<h3><a name="org.jetbrains.kotlin:kotlin-stdlib:1.9.25"></a>org.jetbrains.kotlin:kotlin-stdlib:1.9.25</h3>
<table border="0" class="bodyTable">
<tr class="a">
<th width="20%">Status</th>
<td width="80%" align="left"><figure><img src="images/icon_success_sml.gif" alt="" /></figure>&#160;License defined in the pom of the dependency</td></tr>
<tr class="b">
<th width="20%">GroupId:ArtifactId:Version</th>
<td width="80%" align="left">org.jetbrains.kotlin:kotlin-stdlib:1.9.25</td></tr>
<tr class="a">
<th width="20%">Scope</th>
<td width="80%" align="left">compile</td></tr>
<tr class="b">
<th width="20%">Classifier</th>
<td width="80%" align="left"></td></tr>
<tr class="a">
<th width="20%">Type</th>
<td width="80%" align="left">jar</td></tr>
<tr class="b">
<th width="20%">License(s)</th>
<td width="80%" align="left">The Apache License, Version 2.0</td></tr></table><a href="#Overview">Back to top</a><br /></section><section>
<h3><a name="org.jetbrains.kotlin:kotlin-stdlib-common:1.9.25"></a>org.jetbrains.kotlin:kotlin-stdlib-common:1.9.25</h3>
<table border="0" class="bodyTable">
<tr class="a">
<th width="20%">Status</th>
<td width="80%" align="left"><figure><img src="images/icon_success_sml.gif" alt="" /></figure>&#160;License defined in the pom of the dependency</td></tr>
<tr class="b">
<th width="20%">GroupId:ArtifactId:Version</th>
<td width="80%" align="left">org.jetbrains.kotlin:kotlin-stdlib-common:1.9.25</td></tr>
<tr class="a">
<th width="20%">Scope</th>
<td width="80%" align="left">compile</td></tr>
<tr class="b">
<th width="20%">Classifier</th>
<td width="80%" align="left"></td></tr>
<tr class="a">
<th width="20%">Type</th>
<td width="80%" align="left">jar</td></tr>
<tr class="b">
<th width="20%">License(s)</th>
<td width="80%" align="left">The Apache License, Version 2.0</td></tr></table><a href="#Overview">Back to top</a><br /></section><section>
<h3><a name="org.jetbrains.kotlin:kotlin-stdlib-jdk7:1.9.25"></a>org.jetbrains.kotlin:kotlin-stdlib-jdk7:1.9.25</h3>
<table border="0" class="bodyTable">
<tr class="a">
<th width="20%">Status</th>
<td width="80%" align="left"><figure><img src="images/icon_success_sml.gif" alt="" /></figure>&#160;License defined in the pom of the dependency</td></tr>
<tr class="b">
<th width="20%">GroupId:ArtifactId:Version</th>
<td width="80%" align="left">org.jetbrains.kotlin:kotlin-stdlib-jdk7:1.9.25</td></tr>
<tr class="a">
<th width="20%">Scope</th>
<td width="80%" align="left">compile</td></tr>
<tr class="b">
<th width="20%">Classifier</th>
<td width="80%" align="left"></td></tr>
<tr class="a">
<th width="20%">Type</th>
<td width="80%" align="left">jar</td></tr>
<tr class="b">
<th width="20%">License(s)</th>
<td width="80%" align="left">The Apache License, Version 2.0</td></tr></table><a href="#Overview">Back to top</a><br /></section><section>
<h3><a name="org.jetbrains.kotlin:kotlin-stdlib-jdk8:1.9.25"></a>org.jetbrains.kotlin:kotlin-stdlib-jdk8:1.9.25</h3>
<table border="0" class="bodyTable">
<tr class="a">
<th width="20%">Status</th>
<td width="80%" align="left"><figure><img src="images/icon_success_sml.gif" alt="" /></figure>&#160;License defined in the pom of the dependency</td></tr>
<tr class="b">
<th width="20%">GroupId:ArtifactId:Version</th>
<td width="80%" align="left">org.jetbrains.kotlin:kotlin-stdlib-jdk8:1.9.25</td></tr>
<tr class="a">
<th width="20%">Scope</th>
<td width="80%" align="left">compile</td></tr>
<tr class="b">
<th width="20%">Classifier</th>
<td width="80%" align="left"></td></tr>
<tr class="a">
<th width="20%">Type</th>
<td width="80%" align="left">jar</td></tr>
<tr class="b">
<th width="20%">License(s)</th>
<td width="80%" align="left">The Apache License, Version 2.0</td></tr></table><a href="#Overview">Back to top</a><br /></section><section>
<h3><a name="org.jspecify:jspecify:1.0.0"></a>org.jspecify:jspecify:1.0.0</h3>
<table border="0" class="bodyTable">
<tr class="a">
<th width="20%">Status</th>
<td width="80%" align="left"><figure><img src="images/icon_success_sml.gif" alt="" /></figure>&#160;License defined in the pom of the dependency</td></tr>
<tr class="b">
<th width="20%">GroupId:ArtifactId:Version</th>
<td width="80%" align="left">org.jspecify:jspecify:1.0.0</td></tr>
<tr class="a">
<th width="20%">Scope</th>
<td width="80%" align="left">compile</td></tr>
<tr class="b">
<th width="20%">Classifier</th>
<td width="80%" align="left"></td></tr>
<tr class="a">
<th width="20%">Type</th>
<td width="80%" align="left">jar</td></tr>
<tr class="b">
<th width="20%">License(s)</th>
<td width="80%" align="left">The Apache License, Version 2.0</td></tr></table><a href="#Overview">Back to top</a><br /></section><section>
<h3><a name="org.junit.jupiter:junit-jupiter:5.12.2"></a>org.junit.jupiter:junit-jupiter:5.12.2</h3>
<table border="0" class="bodyTable">
<tr class="a">
<th width="20%">Status</th>
<td width="80%" align="left"><figure><img src="images/icon_success_sml.gif" alt="" /></figure>&#160;License defined in the pom of the dependency</td></tr>
<tr class="b">
<th width="20%">GroupId:ArtifactId:Version</th>
<td width="80%" align="left">org.junit.jupiter:junit-jupiter:5.12.2</td></tr>
<tr class="a">
<th width="20%">Scope</th>
<td width="80%" align="left">compile</td></tr>
<tr class="b">
<th width="20%">Classifier</th>
<td width="80%" align="left"></td></tr>
<tr class="a">
<th width="20%">Type</th>
<td width="80%" align="left">jar</td></tr>
<tr class="b">
<th width="20%">License(s)</th>
<td width="80%" align="left">Eclipse Public License v2.0</td></tr></table><a href="#Overview">Back to top</a><br /></section><section>
<h3><a name="org.junit.jupiter:junit-jupiter-api:5.12.2"></a>org.junit.jupiter:junit-jupiter-api:5.12.2</h3>
<table border="0" class="bodyTable">
<tr class="a">
<th width="20%">Status</th>
<td width="80%" align="left"><figure><img src="images/icon_success_sml.gif" alt="" /></figure>&#160;License defined in the pom of the dependency</td></tr>
<tr class="b">
<th width="20%">GroupId:ArtifactId:Version</th>
<td width="80%" align="left">org.junit.jupiter:junit-jupiter-api:5.12.2</td></tr>
<tr class="a">
<th width="20%">Scope</th>
<td width="80%" align="left">compile</td></tr>
<tr class="b">
<th width="20%">Classifier</th>
<td width="80%" align="left"></td></tr>
<tr class="a">
<th width="20%">Type</th>
<td width="80%" align="left">jar</td></tr>
<tr class="b">
<th width="20%">License(s)</th>
<td width="80%" align="left">Eclipse Public License v2.0</td></tr></table><a href="#Overview">Back to top</a><br /></section><section>
<h3><a name="org.junit.jupiter:junit-jupiter-engine:5.12.2"></a>org.junit.jupiter:junit-jupiter-engine:5.12.2</h3>
<table border="0" class="bodyTable">
<tr class="a">
<th width="20%">Status</th>
<td width="80%" align="left"><figure><img src="images/icon_success_sml.gif" alt="" /></figure>&#160;License defined in the pom of the dependency</td></tr>
<tr class="b">
<th width="20%">GroupId:ArtifactId:Version</th>
<td width="80%" align="left">org.junit.jupiter:junit-jupiter-engine:5.12.2</td></tr>
<tr class="a">
<th width="20%">Scope</th>
<td width="80%" align="left">runtime</td></tr>
<tr class="b">
<th width="20%">Classifier</th>
<td width="80%" align="left"></td></tr>
<tr class="a">
<th width="20%">Type</th>
<td width="80%" align="left">jar</td></tr>
<tr class="b">
<th width="20%">License(s)</th>
<td width="80%" align="left">Eclipse Public License v2.0</td></tr></table><a href="#Overview">Back to top</a><br /></section><section>
<h3><a name="org.junit.jupiter:junit-jupiter-params:5.12.2"></a>org.junit.jupiter:junit-jupiter-params:5.12.2</h3>
<table border="0" class="bodyTable">
<tr class="a">
<th width="20%">Status</th>
<td width="80%" align="left"><figure><img src="images/icon_success_sml.gif" alt="" /></figure>&#160;License defined in the pom of the dependency</td></tr>
<tr class="b">
<th width="20%">GroupId:ArtifactId:Version</th>
<td width="80%" align="left">org.junit.jupiter:junit-jupiter-params:5.12.2</td></tr>
<tr class="a">
<th width="20%">Scope</th>
<td width="80%" align="left">compile</td></tr>
<tr class="b">
<th width="20%">Classifier</th>
<td width="80%" align="left"></td></tr>
<tr class="a">
<th width="20%">Type</th>
<td width="80%" align="left">jar</td></tr>
<tr class="b">
<th width="20%">License(s)</th>
<td width="80%" align="left">Eclipse Public License v2.0</td></tr></table><a href="#Overview">Back to top</a><br /></section><section>
<h3><a name="org.junit.platform:junit-platform-commons:1.12.2"></a>org.junit.platform:junit-platform-commons:1.12.2</h3>
<table border="0" class="bodyTable">
<tr class="a">
<th width="20%">Status</th>
<td width="80%" align="left"><figure><img src="images/icon_success_sml.gif" alt="" /></figure>&#160;License defined in the pom of the dependency</td></tr>
<tr class="b">
<th width="20%">GroupId:ArtifactId:Version</th>
<td width="80%" align="left">org.junit.platform:junit-platform-commons:1.12.2</td></tr>
<tr class="a">
<th width="20%">Scope</th>
<td width="80%" align="left">compile</td></tr>
<tr class="b">
<th width="20%">Classifier</th>
<td width="80%" align="left"></td></tr>
<tr class="a">
<th width="20%">Type</th>
<td width="80%" align="left">jar</td></tr>
<tr class="b">
<th width="20%">License(s)</th>
<td width="80%" align="left">Eclipse Public License v2.0</td></tr></table><a href="#Overview">Back to top</a><br /></section><section>
<h3><a name="org.junit.platform:junit-platform-engine:1.12.2"></a>org.junit.platform:junit-platform-engine:1.12.2</h3>
<table border="0" class="bodyTable">
<tr class="a">
<th width="20%">Status</th>
<td width="80%" align="left"><figure><img src="images/icon_success_sml.gif" alt="" /></figure>&#160;License defined in the pom of the dependency</td></tr>
<tr class="b">
<th width="20%">GroupId:ArtifactId:Version</th>
<td width="80%" align="left">org.junit.platform:junit-platform-engine:1.12.2</td></tr>
<tr class="a">
<th width="20%">Scope</th>
<td width="80%" align="left">runtime</td></tr>
<tr class="b">
<th width="20%">Classifier</th>
<td width="80%" align="left"></td></tr>
<tr class="a">
<th width="20%">Type</th>
<td width="80%" align="left">jar</td></tr>
<tr class="b">
<th width="20%">License(s)</th>
<td width="80%" align="left">Eclipse Public License v2.0</td></tr></table><a href="#Overview">Back to top</a><br /></section><section>
<h3><a name="org.latencyutils:LatencyUtils:2.0.3"></a>org.latencyutils:LatencyUtils:2.0.3</h3>
<table border="0" class="bodyTable">
<tr class="a">
<th width="20%">Status</th>
<td width="80%" align="left"><figure><img src="images/icon_success_sml.gif" alt="" /></figure>&#160;License defined in the pom of the dependency</td></tr>
<tr class="b">
<th width="20%">GroupId:ArtifactId:Version</th>
<td width="80%" align="left">org.latencyutils:LatencyUtils:2.0.3</td></tr>
<tr class="a">
<th width="20%">Scope</th>
<td width="80%" align="left">runtime</td></tr>
<tr class="b">
<th width="20%">Classifier</th>
<td width="80%" align="left"></td></tr>
<tr class="a">
<th width="20%">Type</th>
<td width="80%" align="left">jar</td></tr>
<tr class="b">
<th width="20%">License(s)</th>
<td width="80%" align="left">Public Domain, per Creative Commons CC0</td></tr></table><a href="#Overview">Back to top</a><br /></section><section>
<h3><a name="org.liquibase:liquibase-core:4.31.1"></a>org.liquibase:liquibase-core:4.31.1</h3>
<table border="0" class="bodyTable">
<tr class="a">
<th width="20%">Status</th>
<td width="80%" align="left"><figure><img src="images/icon_success_sml.gif" alt="" /></figure>&#160;License defined in the pom of the dependency</td></tr>
<tr class="b">
<th width="20%">GroupId:ArtifactId:Version</th>
<td width="80%" align="left">org.liquibase:liquibase-core:4.31.1</td></tr>
<tr class="a">
<th width="20%">Scope</th>
<td width="80%" align="left">compile</td></tr>
<tr class="b">
<th width="20%">Classifier</th>
<td width="80%" align="left"></td></tr>
<tr class="a">
<th width="20%">Type</th>
<td width="80%" align="left">jar</td></tr>
<tr class="b">
<th width="20%">License(s)</th>
<td width="80%" align="left">Apache License, Version 2.0</td></tr></table><a href="#Overview">Back to top</a><br /></section><section>
<h3><a name="org.netpreserve:jwarc:0.28.3"></a>org.netpreserve:jwarc:0.28.3</h3>
<table border="0" class="bodyTable">
<tr class="a">
<th width="20%">Status</th>
<td width="80%" align="left"><figure><img src="images/icon_success_sml.gif" alt="" /></figure>&#160;License defined in the pom of the dependency</td></tr>
<tr class="b">
<th width="20%">GroupId:ArtifactId:Version</th>
<td width="80%" align="left">org.netpreserve:jwarc:0.28.3</td></tr>
<tr class="a">
<th width="20%">Scope</th>
<td width="80%" align="left">compile</td></tr>
<tr class="b">
<th width="20%">Classifier</th>
<td width="80%" align="left"></td></tr>
<tr class="a">
<th width="20%">Type</th>
<td width="80%" align="left">jar</td></tr>
<tr class="b">
<th width="20%">License(s)</th>
<td width="80%" align="left">Apache License, Version 2.0</td></tr></table><a href="#Overview">Back to top</a><br /></section><section>
<h3><a name="org.opentest4j:opentest4j:1.3.0"></a>org.opentest4j:opentest4j:1.3.0</h3>
<table border="0" class="bodyTable">
<tr class="a">
<th width="20%">Status</th>
<td width="80%" align="left"><figure><img src="images/icon_success_sml.gif" alt="" /></figure>&#160;License defined in the pom of the dependency</td></tr>
<tr class="b">
<th width="20%">GroupId:ArtifactId:Version</th>
<td width="80%" align="left">org.opentest4j:opentest4j:1.3.0</td></tr>
<tr class="a">
<th width="20%">Scope</th>
<td width="80%" align="left">compile</td></tr>
<tr class="b">
<th width="20%">Classifier</th>
<td width="80%" align="left"></td></tr>
<tr class="a">
<th width="20%">Type</th>
<td width="80%" align="left">jar</td></tr>
<tr class="b">
<th width="20%">License(s)</th>
<td width="80%" align="left">The Apache License, Version 2.0</td></tr></table><a href="#Overview">Back to top</a><br /></section><section>
<h3><a name="org.ow2.asm:asm:9.4"></a>org.ow2.asm:asm:9.4</h3>
<table border="0" class="bodyTable">
<tr class="a">
<th width="20%">Status</th>
<td width="80%" align="left"><figure><img src="images/icon_success_sml.gif" alt="" /></figure>&#160;License defined in the pom of the dependency</td></tr>
<tr class="b">
<th width="20%">GroupId:ArtifactId:Version</th>
<td width="80%" align="left">org.ow2.asm:asm:9.4</td></tr>
<tr class="a">
<th width="20%">Scope</th>
<td width="80%" align="left">compile</td></tr>
<tr class="b">
<th width="20%">Classifier</th>
<td width="80%" align="left"></td></tr>
<tr class="a">
<th width="20%">Type</th>
<td width="80%" align="left">jar</td></tr>
<tr class="b">
<th width="20%">License(s)</th>
<td width="80%" align="left">BSD-3-Clause</td></tr></table><a href="#Overview">Back to top</a><br /></section><section>
<h3><a name="org.projectlombok:lombok:1.18.38"></a>org.projectlombok:lombok:1.18.38</h3>
<table border="0" class="bodyTable">
<tr class="a">
<th width="20%">Status</th>
<td width="80%" align="left"><figure><img src="images/icon_success_sml.gif" alt="" /></figure>&#160;License defined in the pom of the dependency</td></tr>
<tr class="b">
<th width="20%">GroupId:ArtifactId:Version</th>
<td width="80%" align="left">org.projectlombok:lombok:1.18.38</td></tr>
<tr class="a">
<th width="20%">Scope</th>
<td width="80%" align="left">compile</td></tr>
<tr class="b">
<th width="20%">Classifier</th>
<td width="80%" align="left"></td></tr>
<tr class="a">
<th width="20%">Type</th>
<td width="80%" align="left">jar</td></tr>
<tr class="b">
<th width="20%">License(s)</th>
<td width="80%" align="left">The MIT License</td></tr></table><a href="#Overview">Back to top</a><br /></section><section>
<h3><a name="org.reactivestreams:reactive-streams:1.0.4"></a>org.reactivestreams:reactive-streams:1.0.4</h3>
<table border="0" class="bodyTable">
<tr class="a">
<th width="20%">Status</th>
<td width="80%" align="left"><figure><img src="images/icon_success_sml.gif" alt="" /></figure>&#160;License defined in the pom of the dependency</td></tr>
<tr class="b">
<th width="20%">GroupId:ArtifactId:Version</th>
<td width="80%" align="left">org.reactivestreams:reactive-streams:1.0.4</td></tr>
<tr class="a">
<th width="20%">Scope</th>
<td width="80%" align="left">compile</td></tr>
<tr class="b">
<th width="20%">Classifier</th>
<td width="80%" align="left"></td></tr>
<tr class="a">
<th width="20%">Type</th>
<td width="80%" align="left">jar</td></tr>
<tr class="b">
<th width="20%">License(s)</th>
<td width="80%" align="left">MIT-0</td></tr></table><a href="#Overview">Back to top</a><br /></section><section>
<h3><a name="org.slf4j:jcl-over-slf4j:2.0.17"></a>org.slf4j:jcl-over-slf4j:2.0.17</h3>
<table border="0" class="bodyTable">
<tr class="a">
<th width="20%">Status</th>
<td width="80%" align="left"><figure><img src="images/icon_success_sml.gif" alt="" /></figure>&#160;License defined in the pom of the dependency</td></tr>
<tr class="b">
<th width="20%">GroupId:ArtifactId:Version</th>
<td width="80%" align="left">org.slf4j:jcl-over-slf4j:2.0.17</td></tr>
<tr class="a">
<th width="20%">Scope</th>
<td width="80%" align="left">compile</td></tr>
<tr class="b">
<th width="20%">Classifier</th>
<td width="80%" align="left"></td></tr>
<tr class="a">
<th width="20%">Type</th>
<td width="80%" align="left">jar</td></tr>
<tr class="b">
<th width="20%">License(s)</th>
<td width="80%" align="left">Apache-2.0</td></tr></table><a href="#Overview">Back to top</a><br /></section><section>
<h3><a name="org.slf4j:jul-to-slf4j:2.0.17"></a>org.slf4j:jul-to-slf4j:2.0.17</h3>
<table border="0" class="bodyTable">
<tr class="a">
<th width="20%">Status</th>
<td width="80%" align="left"><figure><img src="images/icon_success_sml.gif" alt="" /></figure>&#160;License defined in the pom of the dependency</td></tr>
<tr class="b">
<th width="20%">GroupId:ArtifactId:Version</th>
<td width="80%" align="left">org.slf4j:jul-to-slf4j:2.0.17</td></tr>
<tr class="a">
<th width="20%">Scope</th>
<td width="80%" align="left">compile</td></tr>
<tr class="b">
<th width="20%">Classifier</th>
<td width="80%" align="left"></td></tr>
<tr class="a">
<th width="20%">Type</th>
<td width="80%" align="left">jar</td></tr>
<tr class="b">
<th width="20%">License(s)</th>
<td width="80%" align="left">MIT</td></tr></table><a href="#Overview">Back to top</a><br /></section><section>
<h3><a name="org.slf4j:slf4j-api:2.0.17"></a>org.slf4j:slf4j-api:2.0.17</h3>
<table border="0" class="bodyTable">
<tr class="a">
<th width="20%">Status</th>
<td width="80%" align="left"><figure><img src="images/icon_success_sml.gif" alt="" /></figure>&#160;License defined in the pom of the dependency</td></tr>
<tr class="b">
<th width="20%">GroupId:ArtifactId:Version</th>
<td width="80%" align="left">org.slf4j:slf4j-api:2.0.17</td></tr>
<tr class="a">
<th width="20%">Scope</th>
<td width="80%" align="left">compile</td></tr>
<tr class="b">
<th width="20%">Classifier</th>
<td width="80%" align="left"></td></tr>
<tr class="a">
<th width="20%">Type</th>
<td width="80%" align="left">jar</td></tr>
<tr class="b">
<th width="20%">License(s)</th>
<td width="80%" align="left">MIT</td></tr></table><a href="#Overview">Back to top</a><br /></section><section>
<h3><a name="org.snakeyaml:snakeyaml-engine:2.8"></a>org.snakeyaml:snakeyaml-engine:2.8</h3>
<table border="0" class="bodyTable">
<tr class="a">
<th width="20%">Status</th>
<td width="80%" align="left"><figure><img src="images/icon_success_sml.gif" alt="" /></figure>&#160;License defined in the pom of the dependency</td></tr>
<tr class="b">
<th width="20%">GroupId:ArtifactId:Version</th>
<td width="80%" align="left">org.snakeyaml:snakeyaml-engine:2.8</td></tr>
<tr class="a">
<th width="20%">Scope</th>
<td width="80%" align="left">runtime</td></tr>
<tr class="b">
<th width="20%">Classifier</th>
<td width="80%" align="left"></td></tr>
<tr class="a">
<th width="20%">Type</th>
<td width="80%" align="left">bundle</td></tr>
<tr class="b">
<th width="20%">License(s)</th>
<td width="80%" align="left">Apache License, Version 2.0</td></tr></table><a href="#Overview">Back to top</a><br /></section><section>
<h3><a name="org.springframework:spring-aop:6.2.7"></a>org.springframework:spring-aop:6.2.7</h3>
<table border="0" class="bodyTable">
<tr class="a">
<th width="20%">Status</th>
<td width="80%" align="left"><figure><img src="images/icon_success_sml.gif" alt="" /></figure>&#160;License defined in the pom of the dependency</td></tr>
<tr class="b">
<th width="20%">GroupId:ArtifactId:Version</th>
<td width="80%" align="left">org.springframework:spring-aop:6.2.7</td></tr>
<tr class="a">
<th width="20%">Scope</th>
<td width="80%" align="left">compile</td></tr>
<tr class="b">
<th width="20%">Classifier</th>
<td width="80%" align="left"></td></tr>
<tr class="a">
<th width="20%">Type</th>
<td width="80%" align="left">jar</td></tr>
<tr class="b">
<th width="20%">License(s)</th>
<td width="80%" align="left">Apache License, Version 2.0</td></tr></table><a href="#Overview">Back to top</a><br /></section><section>
<h3><a name="org.springframework:spring-aspects:6.2.7"></a>org.springframework:spring-aspects:6.2.7</h3>
<table border="0" class="bodyTable">
<tr class="a">
<th width="20%">Status</th>
<td width="80%" align="left"><figure><img src="images/icon_success_sml.gif" alt="" /></figure>&#160;License defined in the pom of the dependency</td></tr>
<tr class="b">
<th width="20%">GroupId:ArtifactId:Version</th>
<td width="80%" align="left">org.springframework:spring-aspects:6.2.7</td></tr>
<tr class="a">
<th width="20%">Scope</th>
<td width="80%" align="left">compile</td></tr>
<tr class="b">
<th width="20%">Classifier</th>
<td width="80%" align="left"></td></tr>
<tr class="a">
<th width="20%">Type</th>
<td width="80%" align="left">jar</td></tr>
<tr class="b">
<th width="20%">License(s)</th>
<td width="80%" align="left">Apache License, Version 2.0</td></tr></table><a href="#Overview">Back to top</a><br /></section><section>
<h3><a name="org.springframework:spring-beans:6.2.7"></a>org.springframework:spring-beans:6.2.7</h3>
<table border="0" class="bodyTable">
<tr class="a">
<th width="20%">Status</th>
<td width="80%" align="left"><figure><img src="images/icon_success_sml.gif" alt="" /></figure>&#160;License defined in the pom of the dependency</td></tr>
<tr class="b">
<th width="20%">GroupId:ArtifactId:Version</th>
<td width="80%" align="left">org.springframework:spring-beans:6.2.7</td></tr>
<tr class="a">
<th width="20%">Scope</th>
<td width="80%" align="left">compile</td></tr>
<tr class="b">
<th width="20%">Classifier</th>
<td width="80%" align="left"></td></tr>
<tr class="a">
<th width="20%">Type</th>
<td width="80%" align="left">jar</td></tr>
<tr class="b">
<th width="20%">License(s)</th>
<td width="80%" align="left">Apache License, Version 2.0</td></tr></table><a href="#Overview">Back to top</a><br /></section><section>
<h3><a name="org.springframework:spring-context:6.2.7"></a>org.springframework:spring-context:6.2.7</h3>
<table border="0" class="bodyTable">
<tr class="a">
<th width="20%">Status</th>
<td width="80%" align="left"><figure><img src="images/icon_success_sml.gif" alt="" /></figure>&#160;License defined in the pom of the dependency</td></tr>
<tr class="b">
<th width="20%">GroupId:ArtifactId:Version</th>
<td width="80%" align="left">org.springframework:spring-context:6.2.7</td></tr>
<tr class="a">
<th width="20%">Scope</th>
<td width="80%" align="left">compile</td></tr>
<tr class="b">
<th width="20%">Classifier</th>
<td width="80%" align="left"></td></tr>
<tr class="a">
<th width="20%">Type</th>
<td width="80%" align="left">jar</td></tr>
<tr class="b">
<th width="20%">License(s)</th>
<td width="80%" align="left">Apache License, Version 2.0</td></tr></table><a href="#Overview">Back to top</a><br /></section><section>
<h3><a name="org.springframework:spring-context-support:6.2.7"></a>org.springframework:spring-context-support:6.2.7</h3>
<table border="0" class="bodyTable">
<tr class="a">
<th width="20%">Status</th>
<td width="80%" align="left"><figure><img src="images/icon_success_sml.gif" alt="" /></figure>&#160;License defined in the pom of the dependency</td></tr>
<tr class="b">
<th width="20%">GroupId:ArtifactId:Version</th>
<td width="80%" align="left">org.springframework:spring-context-support:6.2.7</td></tr>
<tr class="a">
<th width="20%">Scope</th>
<td width="80%" align="left">compile</td></tr>
<tr class="b">
<th width="20%">Classifier</th>
<td width="80%" align="left"></td></tr>
<tr class="a">
<th width="20%">Type</th>
<td width="80%" align="left">jar</td></tr>
<tr class="b">
<th width="20%">License(s)</th>
<td width="80%" align="left">Apache License, Version 2.0</td></tr></table><a href="#Overview">Back to top</a><br /></section><section>
<h3><a name="org.springframework:spring-core:6.2.7"></a>org.springframework:spring-core:6.2.7</h3>
<table border="0" class="bodyTable">
<tr class="a">
<th width="20%">Status</th>
<td width="80%" align="left"><figure><img src="images/icon_success_sml.gif" alt="" /></figure>&#160;License defined in the pom of the dependency</td></tr>
<tr class="b">
<th width="20%">GroupId:ArtifactId:Version</th>
<td width="80%" align="left">org.springframework:spring-core:6.2.7</td></tr>
<tr class="a">
<th width="20%">Scope</th>
<td width="80%" align="left">compile</td></tr>
<tr class="b">
<th width="20%">Classifier</th>
<td width="80%" align="left"></td></tr>
<tr class="a">
<th width="20%">Type</th>
<td width="80%" align="left">jar</td></tr>
<tr class="b">
<th width="20%">License(s)</th>
<td width="80%" align="left">Apache License, Version 2.0</td></tr></table><a href="#Overview">Back to top</a><br /></section><section>
<h3><a name="org.springframework:spring-expression:6.2.7"></a>org.springframework:spring-expression:6.2.7</h3>
<table border="0" class="bodyTable">
<tr class="a">
<th width="20%">Status</th>
<td width="80%" align="left"><figure><img src="images/icon_success_sml.gif" alt="" /></figure>&#160;License defined in the pom of the dependency</td></tr>
<tr class="b">
<th width="20%">GroupId:ArtifactId:Version</th>
<td width="80%" align="left">org.springframework:spring-expression:6.2.7</td></tr>
<tr class="a">
<th width="20%">Scope</th>
<td width="80%" align="left">compile</td></tr>
<tr class="b">
<th width="20%">Classifier</th>
<td width="80%" align="left"></td></tr>
<tr class="a">
<th width="20%">Type</th>
<td width="80%" align="left">jar</td></tr>
<tr class="b">
<th width="20%">License(s)</th>
<td width="80%" align="left">Apache License, Version 2.0</td></tr></table><a href="#Overview">Back to top</a><br /></section><section>
<h3><a name="org.springframework:spring-jcl:6.2.7"></a>org.springframework:spring-jcl:6.2.7</h3>
<table border="0" class="bodyTable">
<tr class="a">
<th width="20%">Status</th>
<td width="80%" align="left"><figure><img src="images/icon_success_sml.gif" alt="" /></figure>&#160;License defined in the pom of the dependency</td></tr>
<tr class="b">
<th width="20%">GroupId:ArtifactId:Version</th>
<td width="80%" align="left">org.springframework:spring-jcl:6.2.7</td></tr>
<tr class="a">
<th width="20%">Scope</th>
<td width="80%" align="left">compile</td></tr>
<tr class="b">
<th width="20%">Classifier</th>
<td width="80%" align="left"></td></tr>
<tr class="a">
<th width="20%">Type</th>
<td width="80%" align="left">jar</td></tr>
<tr class="b">
<th width="20%">License(s)</th>
<td width="80%" align="left">Apache License, Version 2.0</td></tr></table><a href="#Overview">Back to top</a><br /></section><section>
<h3><a name="org.springframework:spring-jdbc:6.2.7"></a>org.springframework:spring-jdbc:6.2.7</h3>
<table border="0" class="bodyTable">
<tr class="a">
<th width="20%">Status</th>
<td width="80%" align="left"><figure><img src="images/icon_success_sml.gif" alt="" /></figure>&#160;License defined in the pom of the dependency</td></tr>
<tr class="b">
<th width="20%">GroupId:ArtifactId:Version</th>
<td width="80%" align="left">org.springframework:spring-jdbc:6.2.7</td></tr>
<tr class="a">
<th width="20%">Scope</th>
<td width="80%" align="left">compile</td></tr>
<tr class="b">
<th width="20%">Classifier</th>
<td width="80%" align="left"></td></tr>
<tr class="a">
<th width="20%">Type</th>
<td width="80%" align="left">jar</td></tr>
<tr class="b">
<th width="20%">License(s)</th>
<td width="80%" align="left">Apache License, Version 2.0</td></tr></table><a href="#Overview">Back to top</a><br /></section><section>
<h3><a name="org.springframework:spring-orm:6.2.7"></a>org.springframework:spring-orm:6.2.7</h3>
<table border="0" class="bodyTable">
<tr class="a">
<th width="20%">Status</th>
<td width="80%" align="left"><figure><img src="images/icon_success_sml.gif" alt="" /></figure>&#160;License defined in the pom of the dependency</td></tr>
<tr class="b">
<th width="20%">GroupId:ArtifactId:Version</th>
<td width="80%" align="left">org.springframework:spring-orm:6.2.7</td></tr>
<tr class="a">
<th width="20%">Scope</th>
<td width="80%" align="left">compile</td></tr>
<tr class="b">
<th width="20%">Classifier</th>
<td width="80%" align="left"></td></tr>
<tr class="a">
<th width="20%">Type</th>
<td width="80%" align="left">jar</td></tr>
<tr class="b">
<th width="20%">License(s)</th>
<td width="80%" align="left">Apache License, Version 2.0</td></tr></table><a href="#Overview">Back to top</a><br /></section><section>
<h3><a name="org.springframework:spring-oxm:6.2.7"></a>org.springframework:spring-oxm:6.2.7</h3>
<table border="0" class="bodyTable">
<tr class="a">
<th width="20%">Status</th>
<td width="80%" align="left"><figure><img src="images/icon_success_sml.gif" alt="" /></figure>&#160;License defined in the pom of the dependency</td></tr>
<tr class="b">
<th width="20%">GroupId:ArtifactId:Version</th>
<td width="80%" align="left">org.springframework:spring-oxm:6.2.7</td></tr>
<tr class="a">
<th width="20%">Scope</th>
<td width="80%" align="left">compile</td></tr>
<tr class="b">
<th width="20%">Classifier</th>
<td width="80%" align="left"></td></tr>
<tr class="a">
<th width="20%">Type</th>
<td width="80%" align="left">jar</td></tr>
<tr class="b">
<th width="20%">License(s)</th>
<td width="80%" align="left">Apache License, Version 2.0</td></tr></table><a href="#Overview">Back to top</a><br /></section><section>
<h3><a name="org.springframework:spring-tx:6.2.7"></a>org.springframework:spring-tx:6.2.7</h3>
<table border="0" class="bodyTable">
<tr class="a">
<th width="20%">Status</th>
<td width="80%" align="left"><figure><img src="images/icon_success_sml.gif" alt="" /></figure>&#160;License defined in the pom of the dependency</td></tr>
<tr class="b">
<th width="20%">GroupId:ArtifactId:Version</th>
<td width="80%" align="left">org.springframework:spring-tx:6.2.7</td></tr>
<tr class="a">
<th width="20%">Scope</th>
<td width="80%" align="left">compile</td></tr>
<tr class="b">
<th width="20%">Classifier</th>
<td width="80%" align="left"></td></tr>
<tr class="a">
<th width="20%">Type</th>
<td width="80%" align="left">jar</td></tr>
<tr class="b">
<th width="20%">License(s)</th>
<td width="80%" align="left">Apache License, Version 2.0</td></tr></table><a href="#Overview">Back to top</a><br /></section><section>
<h3><a name="org.springframework:spring-web:6.2.7"></a>org.springframework:spring-web:6.2.7</h3>
<table border="0" class="bodyTable">
<tr class="a">
<th width="20%">Status</th>
<td width="80%" align="left"><figure><img src="images/icon_success_sml.gif" alt="" /></figure>&#160;License defined in the pom of the dependency</td></tr>
<tr class="b">
<th width="20%">GroupId:ArtifactId:Version</th>
<td width="80%" align="left">org.springframework:spring-web:6.2.7</td></tr>
<tr class="a">
<th width="20%">Scope</th>
<td width="80%" align="left">compile</td></tr>
<tr class="b">
<th width="20%">Classifier</th>
<td width="80%" align="left"></td></tr>
<tr class="a">
<th width="20%">Type</th>
<td width="80%" align="left">jar</td></tr>
<tr class="b">
<th width="20%">License(s)</th>
<td width="80%" align="left">Apache License, Version 2.0</td></tr></table><a href="#Overview">Back to top</a><br /></section><section>
<h3><a name="org.springframework:spring-webmvc:6.2.7"></a>org.springframework:spring-webmvc:6.2.7</h3>
<table border="0" class="bodyTable">
<tr class="a">
<th width="20%">Status</th>
<td width="80%" align="left"><figure><img src="images/icon_success_sml.gif" alt="" /></figure>&#160;License defined in the pom of the dependency</td></tr>
<tr class="b">
<th width="20%">GroupId:ArtifactId:Version</th>
<td width="80%" align="left">org.springframework:spring-webmvc:6.2.7</td></tr>
<tr class="a">
<th width="20%">Scope</th>
<td width="80%" align="left">compile</td></tr>
<tr class="b">
<th width="20%">Classifier</th>
<td width="80%" align="left"></td></tr>
<tr class="a">
<th width="20%">Type</th>
<td width="80%" align="left">jar</td></tr>
<tr class="b">
<th width="20%">License(s)</th>
<td width="80%" align="left">Apache License, Version 2.0</td></tr></table><a href="#Overview">Back to top</a><br /></section><section>
<h3><a name="org.springframework.data:spring-data-commons:3.5.0"></a>org.springframework.data:spring-data-commons:3.5.0</h3>
<table border="0" class="bodyTable">
<tr class="a">
<th width="20%">Status</th>
<td width="80%" align="left"><figure><img src="images/icon_success_sml.gif" alt="" /></figure>&#160;License defined in the pom of the dependency</td></tr>
<tr class="b">
<th width="20%">GroupId:ArtifactId:Version</th>
<td width="80%" align="left">org.springframework.data:spring-data-commons:3.5.0</td></tr>
<tr class="a">
<th width="20%">Scope</th>
<td width="80%" align="left">compile</td></tr>
<tr class="b">
<th width="20%">Classifier</th>
<td width="80%" align="left"></td></tr>
<tr class="a">
<th width="20%">Type</th>
<td width="80%" align="left">jar</td></tr>
<tr class="b">
<th width="20%">License(s)</th>
<td width="80%" align="left">Apache License, Version 2.0</td></tr></table><a href="#Overview">Back to top</a><br /></section><section>
<h3><a name="org.springframework.data:spring-data-elasticsearch:5.5.0"></a>org.springframework.data:spring-data-elasticsearch:5.5.0</h3>
<table border="0" class="bodyTable">
<tr class="a">
<th width="20%">Status</th>
<td width="80%" align="left"><figure><img src="images/icon_success_sml.gif" alt="" /></figure>&#160;License defined in the pom of the dependency</td></tr>
<tr class="b">
<th width="20%">GroupId:ArtifactId:Version</th>
<td width="80%" align="left">org.springframework.data:spring-data-elasticsearch:5.5.0</td></tr>
<tr class="a">
<th width="20%">Scope</th>
<td width="80%" align="left">compile</td></tr>
<tr class="b">
<th width="20%">Classifier</th>
<td width="80%" align="left"></td></tr>
<tr class="a">
<th width="20%">Type</th>
<td width="80%" align="left">jar</td></tr>
<tr class="b">
<th width="20%">License(s)</th>
<td width="80%" align="left">Apache License, Version 2.0</td></tr></table><a href="#Overview">Back to top</a><br /></section><section>
<h3><a name="org.springframework.data:spring-data-jpa:3.5.0"></a>org.springframework.data:spring-data-jpa:3.5.0</h3>
<table border="0" class="bodyTable">
<tr class="a">
<th width="20%">Status</th>
<td width="80%" align="left"><figure><img src="images/icon_success_sml.gif" alt="" /></figure>&#160;License defined in the pom of the dependency</td></tr>
<tr class="b">
<th width="20%">GroupId:ArtifactId:Version</th>
<td width="80%" align="left">org.springframework.data:spring-data-jpa:3.5.0</td></tr>
<tr class="a">
<th width="20%">Scope</th>
<td width="80%" align="left">compile</td></tr>
<tr class="b">
<th width="20%">Classifier</th>
<td width="80%" align="left"></td></tr>
<tr class="a">
<th width="20%">Type</th>
<td width="80%" align="left">jar</td></tr>
<tr class="b">
<th width="20%">License(s)</th>
<td width="80%" align="left">Apache License, Version 2.0</td></tr></table><a href="#Overview">Back to top</a><br /></section><section>
<h3><a name="org.springframework.data:spring-data-keyvalue:3.5.0"></a>org.springframework.data:spring-data-keyvalue:3.5.0</h3>
<table border="0" class="bodyTable">
<tr class="a">
<th width="20%">Status</th>
<td width="80%" align="left"><figure><img src="images/icon_success_sml.gif" alt="" /></figure>&#160;License defined in the pom of the dependency</td></tr>
<tr class="b">
<th width="20%">GroupId:ArtifactId:Version</th>
<td width="80%" align="left">org.springframework.data:spring-data-keyvalue:3.5.0</td></tr>
<tr class="a">
<th width="20%">Scope</th>
<td width="80%" align="left">compile</td></tr>
<tr class="b">
<th width="20%">Classifier</th>
<td width="80%" align="left"></td></tr>
<tr class="a">
<th width="20%">Type</th>
<td width="80%" align="left">jar</td></tr>
<tr class="b">
<th width="20%">License(s)</th>
<td width="80%" align="left">Apache License, Version 2.0</td></tr></table><a href="#Overview">Back to top</a><br /></section><section>
<h3><a name="org.springframework.data:spring-data-redis:3.5.0"></a>org.springframework.data:spring-data-redis:3.5.0</h3>
<table border="0" class="bodyTable">
<tr class="a">
<th width="20%">Status</th>
<td width="80%" align="left"><figure><img src="images/icon_success_sml.gif" alt="" /></figure>&#160;License defined in the pom of the dependency</td></tr>
<tr class="b">
<th width="20%">GroupId:ArtifactId:Version</th>
<td width="80%" align="left">org.springframework.data:spring-data-redis:3.5.0</td></tr>
<tr class="a">
<th width="20%">Scope</th>
<td width="80%" align="left">compile</td></tr>
<tr class="b">
<th width="20%">Classifier</th>
<td width="80%" align="left"></td></tr>
<tr class="a">
<th width="20%">Type</th>
<td width="80%" align="left">jar</td></tr>
<tr class="b">
<th width="20%">License(s)</th>
<td width="80%" align="left">Apache License, Version 2.0</td></tr></table><a href="#Overview">Back to top</a><br /></section><section>
<h3><a name="org.springframework.graphql:spring-graphql:1.4.0"></a>org.springframework.graphql:spring-graphql:1.4.0</h3>
<table border="0" class="bodyTable">
<tr class="a">
<th width="20%">Status</th>
<td width="80%" align="left"><figure><img src="images/icon_success_sml.gif" alt="" /></figure>&#160;License defined in the pom of the dependency</td></tr>
<tr class="b">
<th width="20%">GroupId:ArtifactId:Version</th>
<td width="80%" align="left">org.springframework.graphql:spring-graphql:1.4.0</td></tr>
<tr class="a">
<th width="20%">Scope</th>
<td width="80%" align="left">compile</td></tr>
<tr class="b">
<th width="20%">Classifier</th>
<td width="80%" align="left"></td></tr>
<tr class="a">
<th width="20%">Type</th>
<td width="80%" align="left">jar</td></tr>
<tr class="b">
<th width="20%">License(s)</th>
<td width="80%" align="left">Apache License, Version 2.0</td></tr></table><a href="#Overview">Back to top</a><br /></section><section>
<h3><a name="org.springframework.security:spring-security-config:6.5.0"></a>org.springframework.security:spring-security-config:6.5.0</h3>
<table border="0" class="bodyTable">
<tr class="a">
<th width="20%">Status</th>
<td width="80%" align="left"><figure><img src="images/icon_success_sml.gif" alt="" /></figure>&#160;License defined in the pom of the dependency</td></tr>
<tr class="b">
<th width="20%">GroupId:ArtifactId:Version</th>
<td width="80%" align="left">org.springframework.security:spring-security-config:6.5.0</td></tr>
<tr class="a">
<th width="20%">Scope</th>
<td width="80%" align="left">compile</td></tr>
<tr class="b">
<th width="20%">Classifier</th>
<td width="80%" align="left"></td></tr>
<tr class="a">
<th width="20%">Type</th>
<td width="80%" align="left">jar</td></tr>
<tr class="b">
<th width="20%">License(s)</th>
<td width="80%" align="left">Apache License, Version 2.0</td></tr></table><a href="#Overview">Back to top</a><br /></section><section>
<h3><a name="org.springframework.security:spring-security-core:6.5.0"></a>org.springframework.security:spring-security-core:6.5.0</h3>
<table border="0" class="bodyTable">
<tr class="a">
<th width="20%">Status</th>
<td width="80%" align="left"><figure><img src="images/icon_success_sml.gif" alt="" /></figure>&#160;License defined in the pom of the dependency</td></tr>
<tr class="b">
<th width="20%">GroupId:ArtifactId:Version</th>
<td width="80%" align="left">org.springframework.security:spring-security-core:6.5.0</td></tr>
<tr class="a">
<th width="20%">Scope</th>
<td width="80%" align="left">compile</td></tr>
<tr class="b">
<th width="20%">Classifier</th>
<td width="80%" align="left"></td></tr>
<tr class="a">
<th width="20%">Type</th>
<td width="80%" align="left">jar</td></tr>
<tr class="b">
<th width="20%">License(s)</th>
<td width="80%" align="left">Apache License, Version 2.0</td></tr></table><a href="#Overview">Back to top</a><br /></section><section>
<h3><a name="org.springframework.security:spring-security-crypto:6.5.0"></a>org.springframework.security:spring-security-crypto:6.5.0</h3>
<table border="0" class="bodyTable">
<tr class="a">
<th width="20%">Status</th>
<td width="80%" align="left"><figure><img src="images/icon_success_sml.gif" alt="" /></figure>&#160;License defined in the pom of the dependency</td></tr>
<tr class="b">
<th width="20%">GroupId:ArtifactId:Version</th>
<td width="80%" align="left">org.springframework.security:spring-security-crypto:6.5.0</td></tr>
<tr class="a">
<th width="20%">Scope</th>
<td width="80%" align="left">compile</td></tr>
<tr class="b">
<th width="20%">Classifier</th>
<td width="80%" align="left"></td></tr>
<tr class="a">
<th width="20%">Type</th>
<td width="80%" align="left">jar</td></tr>
<tr class="b">
<th width="20%">License(s)</th>
<td width="80%" align="left">Apache License, Version 2.0</td></tr></table><a href="#Overview">Back to top</a><br /></section><section>
<h3><a name="org.springframework.security:spring-security-oauth2-client:6.5.0"></a>org.springframework.security:spring-security-oauth2-client:6.5.0</h3>
<table border="0" class="bodyTable">
<tr class="a">
<th width="20%">Status</th>
<td width="80%" align="left"><figure><img src="images/icon_success_sml.gif" alt="" /></figure>&#160;License defined in the pom of the dependency</td></tr>
<tr class="b">
<th width="20%">GroupId:ArtifactId:Version</th>
<td width="80%" align="left">org.springframework.security:spring-security-oauth2-client:6.5.0</td></tr>
<tr class="a">
<th width="20%">Scope</th>
<td width="80%" align="left">compile</td></tr>
<tr class="b">
<th width="20%">Classifier</th>
<td width="80%" align="left"></td></tr>
<tr class="a">
<th width="20%">Type</th>
<td width="80%" align="left">jar</td></tr>
<tr class="b">
<th width="20%">License(s)</th>
<td width="80%" align="left">Apache License, Version 2.0</td></tr></table><a href="#Overview">Back to top</a><br /></section><section>
<h3><a name="org.springframework.security:spring-security-oauth2-core:6.5.0"></a>org.springframework.security:spring-security-oauth2-core:6.5.0</h3>
<table border="0" class="bodyTable">
<tr class="a">
<th width="20%">Status</th>
<td width="80%" align="left"><figure><img src="images/icon_success_sml.gif" alt="" /></figure>&#160;License defined in the pom of the dependency</td></tr>
<tr class="b">
<th width="20%">GroupId:ArtifactId:Version</th>
<td width="80%" align="left">org.springframework.security:spring-security-oauth2-core:6.5.0</td></tr>
<tr class="a">
<th width="20%">Scope</th>
<td width="80%" align="left">compile</td></tr>
<tr class="b">
<th width="20%">Classifier</th>
<td width="80%" align="left"></td></tr>
<tr class="a">
<th width="20%">Type</th>
<td width="80%" align="left">jar</td></tr>
<tr class="b">
<th width="20%">License(s)</th>
<td width="80%" align="left">Apache License, Version 2.0</td></tr></table><a href="#Overview">Back to top</a><br /></section><section>
<h3><a name="org.springframework.security:spring-security-oauth2-jose:6.5.0"></a>org.springframework.security:spring-security-oauth2-jose:6.5.0</h3>
<table border="0" class="bodyTable">
<tr class="a">
<th width="20%">Status</th>
<td width="80%" align="left"><figure><img src="images/icon_success_sml.gif" alt="" /></figure>&#160;License defined in the pom of the dependency</td></tr>
<tr class="b">
<th width="20%">GroupId:ArtifactId:Version</th>
<td width="80%" align="left">org.springframework.security:spring-security-oauth2-jose:6.5.0</td></tr>
<tr class="a">
<th width="20%">Scope</th>
<td width="80%" align="left">compile</td></tr>
<tr class="b">
<th width="20%">Classifier</th>
<td width="80%" align="left"></td></tr>
<tr class="a">
<th width="20%">Type</th>
<td width="80%" align="left">jar</td></tr>
<tr class="b">
<th width="20%">License(s)</th>
<td width="80%" align="left">Apache License, Version 2.0</td></tr></table><a href="#Overview">Back to top</a><br /></section><section>
<h3><a name="org.springframework.security:spring-security-web:6.5.0"></a>org.springframework.security:spring-security-web:6.5.0</h3>
<table border="0" class="bodyTable">
<tr class="a">
<th width="20%">Status</th>
<td width="80%" align="left"><figure><img src="images/icon_success_sml.gif" alt="" /></figure>&#160;License defined in the pom of the dependency</td></tr>
<tr class="b">
<th width="20%">GroupId:ArtifactId:Version</th>
<td width="80%" align="left">org.springframework.security:spring-security-web:6.5.0</td></tr>
<tr class="a">
<th width="20%">Scope</th>
<td width="80%" align="left">compile</td></tr>
<tr class="b">
<th width="20%">Classifier</th>
<td width="80%" align="left"></td></tr>
<tr class="a">
<th width="20%">Type</th>
<td width="80%" align="left">jar</td></tr>
<tr class="b">
<th width="20%">License(s)</th>
<td width="80%" align="left">Apache License, Version 2.0</td></tr></table><a href="#Overview">Back to top</a><br /></section><section>
<h3><a name="org.tallison:jmatio:1.5"></a>org.tallison:jmatio:1.5</h3>
<table border="0" class="bodyTable">
<tr class="a">
<th width="20%">Status</th>
<td width="80%" align="left"><figure><img src="images/icon_success_sml.gif" alt="" /></figure>&#160;License defined in the pom of the dependency</td></tr>
<tr class="b">
<th width="20%">GroupId:ArtifactId:Version</th>
<td width="80%" align="left">org.tallison:jmatio:1.5</td></tr>
<tr class="a">
<th width="20%">Scope</th>
<td width="80%" align="left">compile</td></tr>
<tr class="b">
<th width="20%">Classifier</th>
<td width="80%" align="left"></td></tr>
<tr class="a">
<th width="20%">Type</th>
<td width="80%" align="left">jar</td></tr>
<tr class="b">
<th width="20%">License(s)</th>
<td width="80%" align="left">BSD</td></tr></table><a href="#Overview">Back to top</a><br /></section><section>
<h3><a name="org.tukaani:xz:1.9"></a>org.tukaani:xz:1.9</h3>
<table border="0" class="bodyTable">
<tr class="a">
<th width="20%">Status</th>
<td width="80%" align="left"><figure><img src="images/icon_success_sml.gif" alt="" /></figure>&#160;License defined in the pom of the dependency</td></tr>
<tr class="b">
<th width="20%">GroupId:ArtifactId:Version</th>
<td width="80%" align="left">org.tukaani:xz:1.9</td></tr>
<tr class="a">
<th width="20%">Scope</th>
<td width="80%" align="left">compile</td></tr>
<tr class="b">
<th width="20%">Classifier</th>
<td width="80%" align="left"></td></tr>
<tr class="a">
<th width="20%">Type</th>
<td width="80%" align="left">jar</td></tr>
<tr class="b">
<th width="20%">License(s)</th>
<td width="80%" align="left">Public Domain</td></tr></table><a href="#Overview">Back to top</a><br /></section><section>
<h3><a name="org.yaml:snakeyaml:2.4"></a>org.yaml:snakeyaml:2.4</h3>
<table border="0" class="bodyTable">
<tr class="a">
<th width="20%">Status</th>
<td width="80%" align="left"><figure><img src="images/icon_success_sml.gif" alt="" /></figure>&#160;License defined in the pom of the dependency</td></tr>
<tr class="b">
<th width="20%">GroupId:ArtifactId:Version</th>
<td width="80%" align="left">org.yaml:snakeyaml:2.4</td></tr>
<tr class="a">
<th width="20%">Scope</th>
<td width="80%" align="left">compile</td></tr>
<tr class="b">
<th width="20%">Classifier</th>
<td width="80%" align="left"></td></tr>
<tr class="a">
<th width="20%">Type</th>
<td width="80%" align="left">bundle</td></tr>
<tr class="b">
<th width="20%">License(s)</th>
<td width="80%" align="left">Apache License, Version 2.0</td></tr></table><a href="#Overview">Back to top</a><br /></section><section>
<h3><a name="software.amazon.awssdk:annotations:2.20.162"></a>software.amazon.awssdk:annotations:2.20.162</h3>
<table border="0" class="bodyTable">
<tr class="a">
<th width="20%">Status</th>
<td width="80%" align="left"><figure><img src="images/icon_success_sml.gif" alt="" /></figure>&#160;License defined in the pom of the dependency</td></tr>
<tr class="b">
<th width="20%">GroupId:ArtifactId:Version</th>
<td width="80%" align="left">software.amazon.awssdk:annotations:2.20.162</td></tr>
<tr class="a">
<th width="20%">Scope</th>
<td width="80%" align="left">compile</td></tr>
<tr class="b">
<th width="20%">Classifier</th>
<td width="80%" align="left"></td></tr>
<tr class="a">
<th width="20%">Type</th>
<td width="80%" align="left">jar</td></tr>
<tr class="b">
<th width="20%">License(s)</th>
<td width="80%" align="left">Apache License, Version 2.0</td></tr></table><a href="#Overview">Back to top</a><br /></section><section>
<h3><a name="software.amazon.awssdk:apache-client:2.20.162"></a>software.amazon.awssdk:apache-client:2.20.162</h3>
<table border="0" class="bodyTable">
<tr class="a">
<th width="20%">Status</th>
<td width="80%" align="left"><figure><img src="images/icon_success_sml.gif" alt="" /></figure>&#160;License defined in the pom of the dependency</td></tr>
<tr class="b">
<th width="20%">GroupId:ArtifactId:Version</th>
<td width="80%" align="left">software.amazon.awssdk:apache-client:2.20.162</td></tr>
<tr class="a">
<th width="20%">Scope</th>
<td width="80%" align="left">runtime</td></tr>
<tr class="b">
<th width="20%">Classifier</th>
<td width="80%" align="left"></td></tr>
<tr class="a">
<th width="20%">Type</th>
<td width="80%" align="left">jar</td></tr>
<tr class="b">
<th width="20%">License(s)</th>
<td width="80%" align="left">Apache License, Version 2.0</td></tr></table><a href="#Overview">Back to top</a><br /></section><section>
<h3><a name="software.amazon.awssdk:arns:2.20.162"></a>software.amazon.awssdk:arns:2.20.162</h3>
<table border="0" class="bodyTable">
<tr class="a">
<th width="20%">Status</th>
<td width="80%" align="left"><figure><img src="images/icon_success_sml.gif" alt="" /></figure>&#160;License defined in the pom of the dependency</td></tr>
<tr class="b">
<th width="20%">GroupId:ArtifactId:Version</th>
<td width="80%" align="left">software.amazon.awssdk:arns:2.20.162</td></tr>
<tr class="a">
<th width="20%">Scope</th>
<td width="80%" align="left">compile</td></tr>
<tr class="b">
<th width="20%">Classifier</th>
<td width="80%" align="left"></td></tr>
<tr class="a">
<th width="20%">Type</th>
<td width="80%" align="left">jar</td></tr>
<tr class="b">
<th width="20%">License(s)</th>
<td width="80%" align="left">Apache License, Version 2.0</td></tr></table><a href="#Overview">Back to top</a><br /></section><section>
<h3><a name="software.amazon.awssdk:auth:2.20.162"></a>software.amazon.awssdk:auth:2.20.162</h3>
<table border="0" class="bodyTable">
<tr class="a">
<th width="20%">Status</th>
<td width="80%" align="left"><figure><img src="images/icon_success_sml.gif" alt="" /></figure>&#160;License defined in the pom of the dependency</td></tr>
<tr class="b">
<th width="20%">GroupId:ArtifactId:Version</th>
<td width="80%" align="left">software.amazon.awssdk:auth:2.20.162</td></tr>
<tr class="a">
<th width="20%">Scope</th>
<td width="80%" align="left">compile</td></tr>
<tr class="b">
<th width="20%">Classifier</th>
<td width="80%" align="left"></td></tr>
<tr class="a">
<th width="20%">Type</th>
<td width="80%" align="left">jar</td></tr>
<tr class="b">
<th width="20%">License(s)</th>
<td width="80%" align="left">Apache License, Version 2.0</td></tr></table><a href="#Overview">Back to top</a><br /></section><section>
<h3><a name="software.amazon.awssdk:aws-core:2.20.162"></a>software.amazon.awssdk:aws-core:2.20.162</h3>
<table border="0" class="bodyTable">
<tr class="a">
<th width="20%">Status</th>
<td width="80%" align="left"><figure><img src="images/icon_success_sml.gif" alt="" /></figure>&#160;License defined in the pom of the dependency</td></tr>
<tr class="b">
<th width="20%">GroupId:ArtifactId:Version</th>
<td width="80%" align="left">software.amazon.awssdk:aws-core:2.20.162</td></tr>
<tr class="a">
<th width="20%">Scope</th>
<td width="80%" align="left">compile</td></tr>
<tr class="b">
<th width="20%">Classifier</th>
<td width="80%" align="left"></td></tr>
<tr class="a">
<th width="20%">Type</th>
<td width="80%" align="left">jar</td></tr>
<tr class="b">
<th width="20%">License(s)</th>
<td width="80%" align="left">Apache License, Version 2.0</td></tr></table><a href="#Overview">Back to top</a><br /></section><section>
<h3><a name="software.amazon.awssdk:aws-query-protocol:2.20.162"></a>software.amazon.awssdk:aws-query-protocol:2.20.162</h3>
<table border="0" class="bodyTable">
<tr class="a">
<th width="20%">Status</th>
<td width="80%" align="left"><figure><img src="images/icon_success_sml.gif" alt="" /></figure>&#160;License defined in the pom of the dependency</td></tr>
<tr class="b">
<th width="20%">GroupId:ArtifactId:Version</th>
<td width="80%" align="left">software.amazon.awssdk:aws-query-protocol:2.20.162</td></tr>
<tr class="a">
<th width="20%">Scope</th>
<td width="80%" align="left">compile</td></tr>
<tr class="b">
<th width="20%">Classifier</th>
<td width="80%" align="left"></td></tr>
<tr class="a">
<th width="20%">Type</th>
<td width="80%" align="left">jar</td></tr>
<tr class="b">
<th width="20%">License(s)</th>
<td width="80%" align="left">Apache License, Version 2.0</td></tr></table><a href="#Overview">Back to top</a><br /></section><section>
<h3><a name="software.amazon.awssdk:aws-xml-protocol:2.20.162"></a>software.amazon.awssdk:aws-xml-protocol:2.20.162</h3>
<table border="0" class="bodyTable">
<tr class="a">
<th width="20%">Status</th>
<td width="80%" align="left"><figure><img src="images/icon_success_sml.gif" alt="" /></figure>&#160;License defined in the pom of the dependency</td></tr>
<tr class="b">
<th width="20%">GroupId:ArtifactId:Version</th>
<td width="80%" align="left">software.amazon.awssdk:aws-xml-protocol:2.20.162</td></tr>
<tr class="a">
<th width="20%">Scope</th>
<td width="80%" align="left">compile</td></tr>
<tr class="b">
<th width="20%">Classifier</th>
<td width="80%" align="left"></td></tr>
<tr class="a">
<th width="20%">Type</th>
<td width="80%" align="left">jar</td></tr>
<tr class="b">
<th width="20%">License(s)</th>
<td width="80%" align="left">Apache License, Version 2.0</td></tr></table><a href="#Overview">Back to top</a><br /></section><section>
<h3><a name="software.amazon.awssdk:crt-core:2.20.162"></a>software.amazon.awssdk:crt-core:2.20.162</h3>
<table border="0" class="bodyTable">
<tr class="a">
<th width="20%">Status</th>
<td width="80%" align="left"><figure><img src="images/icon_success_sml.gif" alt="" /></figure>&#160;License defined in the pom of the dependency</td></tr>
<tr class="b">
<th width="20%">GroupId:ArtifactId:Version</th>
<td width="80%" align="left">software.amazon.awssdk:crt-core:2.20.162</td></tr>
<tr class="a">
<th width="20%">Scope</th>
<td width="80%" align="left">compile</td></tr>
<tr class="b">
<th width="20%">Classifier</th>
<td width="80%" align="left"></td></tr>
<tr class="a">
<th width="20%">Type</th>
<td width="80%" align="left">jar</td></tr>
<tr class="b">
<th width="20%">License(s)</th>
<td width="80%" align="left">Apache License, Version 2.0</td></tr></table><a href="#Overview">Back to top</a><br /></section><section>
<h3><a name="software.amazon.awssdk:endpoints-spi:2.20.162"></a>software.amazon.awssdk:endpoints-spi:2.20.162</h3>
<table border="0" class="bodyTable">
<tr class="a">
<th width="20%">Status</th>
<td width="80%" align="left"><figure><img src="images/icon_success_sml.gif" alt="" /></figure>&#160;License defined in the pom of the dependency</td></tr>
<tr class="b">
<th width="20%">GroupId:ArtifactId:Version</th>
<td width="80%" align="left">software.amazon.awssdk:endpoints-spi:2.20.162</td></tr>
<tr class="a">
<th width="20%">Scope</th>
<td width="80%" align="left">compile</td></tr>
<tr class="b">
<th width="20%">Classifier</th>
<td width="80%" align="left"></td></tr>
<tr class="a">
<th width="20%">Type</th>
<td width="80%" align="left">jar</td></tr>
<tr class="b">
<th width="20%">License(s)</th>
<td width="80%" align="left">Apache License, Version 2.0</td></tr></table><a href="#Overview">Back to top</a><br /></section><section>
<h3><a name="software.amazon.awssdk:http-client-spi:2.20.162"></a>software.amazon.awssdk:http-client-spi:2.20.162</h3>
<table border="0" class="bodyTable">
<tr class="a">
<th width="20%">Status</th>
<td width="80%" align="left"><figure><img src="images/icon_success_sml.gif" alt="" /></figure>&#160;License defined in the pom of the dependency</td></tr>
<tr class="b">
<th width="20%">GroupId:ArtifactId:Version</th>
<td width="80%" align="left">software.amazon.awssdk:http-client-spi:2.20.162</td></tr>
<tr class="a">
<th width="20%">Scope</th>
<td width="80%" align="left">compile</td></tr>
<tr class="b">
<th width="20%">Classifier</th>
<td width="80%" align="left"></td></tr>
<tr class="a">
<th width="20%">Type</th>
<td width="80%" align="left">jar</td></tr>
<tr class="b">
<th width="20%">License(s)</th>
<td width="80%" align="left">Apache License, Version 2.0</td></tr></table><a href="#Overview">Back to top</a><br /></section><section>
<h3><a name="software.amazon.awssdk:json-utils:2.20.162"></a>software.amazon.awssdk:json-utils:2.20.162</h3>
<table border="0" class="bodyTable">
<tr class="a">
<th width="20%">Status</th>
<td width="80%" align="left"><figure><img src="images/icon_success_sml.gif" alt="" /></figure>&#160;License defined in the pom of the dependency</td></tr>
<tr class="b">
<th width="20%">GroupId:ArtifactId:Version</th>
<td width="80%" align="left">software.amazon.awssdk:json-utils:2.20.162</td></tr>
<tr class="a">
<th width="20%">Scope</th>
<td width="80%" align="left">compile</td></tr>
<tr class="b">
<th width="20%">Classifier</th>
<td width="80%" align="left"></td></tr>
<tr class="a">
<th width="20%">Type</th>
<td width="80%" align="left">jar</td></tr>
<tr class="b">
<th width="20%">License(s)</th>
<td width="80%" align="left">Apache License, Version 2.0</td></tr></table><a href="#Overview">Back to top</a><br /></section><section>
<h3><a name="software.amazon.awssdk:metrics-spi:2.20.162"></a>software.amazon.awssdk:metrics-spi:2.20.162</h3>
<table border="0" class="bodyTable">
<tr class="a">
<th width="20%">Status</th>
<td width="80%" align="left"><figure><img src="images/icon_success_sml.gif" alt="" /></figure>&#160;License defined in the pom of the dependency</td></tr>
<tr class="b">
<th width="20%">GroupId:ArtifactId:Version</th>
<td width="80%" align="left">software.amazon.awssdk:metrics-spi:2.20.162</td></tr>
<tr class="a">
<th width="20%">Scope</th>
<td width="80%" align="left">compile</td></tr>
<tr class="b">
<th width="20%">Classifier</th>
<td width="80%" align="left"></td></tr>
<tr class="a">
<th width="20%">Type</th>
<td width="80%" align="left">jar</td></tr>
<tr class="b">
<th width="20%">License(s)</th>
<td width="80%" align="left">Apache License, Version 2.0</td></tr></table><a href="#Overview">Back to top</a><br /></section><section>
<h3><a name="software.amazon.awssdk:netty-nio-client:2.20.162"></a>software.amazon.awssdk:netty-nio-client:2.20.162</h3>
<table border="0" class="bodyTable">
<tr class="a">
<th width="20%">Status</th>
<td width="80%" align="left"><figure><img src="images/icon_success_sml.gif" alt="" /></figure>&#160;License defined in the pom of the dependency</td></tr>
<tr class="b">
<th width="20%">GroupId:ArtifactId:Version</th>
<td width="80%" align="left">software.amazon.awssdk:netty-nio-client:2.20.162</td></tr>
<tr class="a">
<th width="20%">Scope</th>
<td width="80%" align="left">runtime</td></tr>
<tr class="b">
<th width="20%">Classifier</th>
<td width="80%" align="left"></td></tr>
<tr class="a">
<th width="20%">Type</th>
<td width="80%" align="left">jar</td></tr>
<tr class="b">
<th width="20%">License(s)</th>
<td width="80%" align="left">Apache License, Version 2.0</td></tr></table><a href="#Overview">Back to top</a><br /></section><section>
<h3><a name="software.amazon.awssdk:profiles:2.20.162"></a>software.amazon.awssdk:profiles:2.20.162</h3>
<table border="0" class="bodyTable">
<tr class="a">
<th width="20%">Status</th>
<td width="80%" align="left"><figure><img src="images/icon_success_sml.gif" alt="" /></figure>&#160;License defined in the pom of the dependency</td></tr>
<tr class="b">
<th width="20%">GroupId:ArtifactId:Version</th>
<td width="80%" align="left">software.amazon.awssdk:profiles:2.20.162</td></tr>
<tr class="a">
<th width="20%">Scope</th>
<td width="80%" align="left">compile</td></tr>
<tr class="b">
<th width="20%">Classifier</th>
<td width="80%" align="left"></td></tr>
<tr class="a">
<th width="20%">Type</th>
<td width="80%" align="left">jar</td></tr>
<tr class="b">
<th width="20%">License(s)</th>
<td width="80%" align="left">Apache License, Version 2.0</td></tr></table><a href="#Overview">Back to top</a><br /></section><section>
<h3><a name="software.amazon.awssdk:protocol-core:2.20.162"></a>software.amazon.awssdk:protocol-core:2.20.162</h3>
<table border="0" class="bodyTable">
<tr class="a">
<th width="20%">Status</th>
<td width="80%" align="left"><figure><img src="images/icon_success_sml.gif" alt="" /></figure>&#160;License defined in the pom of the dependency</td></tr>
<tr class="b">
<th width="20%">GroupId:ArtifactId:Version</th>
<td width="80%" align="left">software.amazon.awssdk:protocol-core:2.20.162</td></tr>
<tr class="a">
<th width="20%">Scope</th>
<td width="80%" align="left">compile</td></tr>
<tr class="b">
<th width="20%">Classifier</th>
<td width="80%" align="left"></td></tr>
<tr class="a">
<th width="20%">Type</th>
<td width="80%" align="left">jar</td></tr>
<tr class="b">
<th width="20%">License(s)</th>
<td width="80%" align="left">Apache License, Version 2.0</td></tr></table><a href="#Overview">Back to top</a><br /></section><section>
<h3><a name="software.amazon.awssdk:regions:2.20.162"></a>software.amazon.awssdk:regions:2.20.162</h3>
<table border="0" class="bodyTable">
<tr class="a">
<th width="20%">Status</th>
<td width="80%" align="left"><figure><img src="images/icon_success_sml.gif" alt="" /></figure>&#160;License defined in the pom of the dependency</td></tr>
<tr class="b">
<th width="20%">GroupId:ArtifactId:Version</th>
<td width="80%" align="left">software.amazon.awssdk:regions:2.20.162</td></tr>
<tr class="a">
<th width="20%">Scope</th>
<td width="80%" align="left">compile</td></tr>
<tr class="b">
<th width="20%">Classifier</th>
<td width="80%" align="left"></td></tr>
<tr class="a">
<th width="20%">Type</th>
<td width="80%" align="left">jar</td></tr>
<tr class="b">
<th width="20%">License(s)</th>
<td width="80%" align="left">Apache License, Version 2.0</td></tr></table><a href="#Overview">Back to top</a><br /></section><section>
<h3><a name="software.amazon.awssdk:s3:2.20.162"></a>software.amazon.awssdk:s3:2.20.162</h3>
<table border="0" class="bodyTable">
<tr class="a">
<th width="20%">Status</th>
<td width="80%" align="left"><figure><img src="images/icon_success_sml.gif" alt="" /></figure>&#160;License defined in the pom of the dependency</td></tr>
<tr class="b">
<th width="20%">GroupId:ArtifactId:Version</th>
<td width="80%" align="left">software.amazon.awssdk:s3:2.20.162</td></tr>
<tr class="a">
<th width="20%">Scope</th>
<td width="80%" align="left">compile</td></tr>
<tr class="b">
<th width="20%">Classifier</th>
<td width="80%" align="left"></td></tr>
<tr class="a">
<th width="20%">Type</th>
<td width="80%" align="left">jar</td></tr>
<tr class="b">
<th width="20%">License(s)</th>
<td width="80%" align="left">Apache License, Version 2.0</td></tr></table><a href="#Overview">Back to top</a><br /></section><section>
<h3><a name="software.amazon.awssdk:sdk-core:2.20.162"></a>software.amazon.awssdk:sdk-core:2.20.162</h3>
<table border="0" class="bodyTable">
<tr class="a">
<th width="20%">Status</th>
<td width="80%" align="left"><figure><img src="images/icon_success_sml.gif" alt="" /></figure>&#160;License defined in the pom of the dependency</td></tr>
<tr class="b">
<th width="20%">GroupId:ArtifactId:Version</th>
<td width="80%" align="left">software.amazon.awssdk:sdk-core:2.20.162</td></tr>
<tr class="a">
<th width="20%">Scope</th>
<td width="80%" align="left">compile</td></tr>
<tr class="b">
<th width="20%">Classifier</th>
<td width="80%" align="left"></td></tr>
<tr class="a">
<th width="20%">Type</th>
<td width="80%" align="left">jar</td></tr>
<tr class="b">
<th width="20%">License(s)</th>
<td width="80%" align="left">Apache License, Version 2.0</td></tr></table><a href="#Overview">Back to top</a><br /></section><section>
<h3><a name="software.amazon.awssdk:third-party-jackson-core:2.20.162"></a>software.amazon.awssdk:third-party-jackson-core:2.20.162</h3>
<table border="0" class="bodyTable">
<tr class="a">
<th width="20%">Status</th>
<td width="80%" align="left"><figure><img src="images/icon_success_sml.gif" alt="" /></figure>&#160;License defined in the pom of the dependency</td></tr>
<tr class="b">
<th width="20%">GroupId:ArtifactId:Version</th>
<td width="80%" align="left">software.amazon.awssdk:third-party-jackson-core:2.20.162</td></tr>
<tr class="a">
<th width="20%">Scope</th>
<td width="80%" align="left">compile</td></tr>
<tr class="b">
<th width="20%">Classifier</th>
<td width="80%" align="left"></td></tr>
<tr class="a">
<th width="20%">Type</th>
<td width="80%" align="left">jar</td></tr>
<tr class="b">
<th width="20%">License(s)</th>
<td width="80%" align="left">Apache License, Version 2.0</td></tr></table><a href="#Overview">Back to top</a><br /></section><section>
<h3><a name="software.amazon.awssdk:utils:2.20.162"></a>software.amazon.awssdk:utils:2.20.162</h3>
<table border="0" class="bodyTable">
<tr class="a">
<th width="20%">Status</th>
<td width="80%" align="left"><figure><img src="images/icon_success_sml.gif" alt="" /></figure>&#160;License defined in the pom of the dependency</td></tr>
<tr class="b">
<th width="20%">GroupId:ArtifactId:Version</th>
<td width="80%" align="left">software.amazon.awssdk:utils:2.20.162</td></tr>
<tr class="a">
<th width="20%">Scope</th>
<td width="80%" align="left">compile</td></tr>
<tr class="b">
<th width="20%">Classifier</th>
<td width="80%" align="left"></td></tr>
<tr class="a">
<th width="20%">Type</th>
<td width="80%" align="left">jar</td></tr>
<tr class="b">
<th width="20%">License(s)</th>
<td width="80%" align="left">Apache License, Version 2.0</td></tr></table><a href="#Overview">Back to top</a><br /></section><section>
<h3><a name="software.amazon.eventstream:eventstream:1.0.1"></a>software.amazon.eventstream:eventstream:1.0.1</h3>
<table border="0" class="bodyTable">
<tr class="a">
<th width="20%">Status</th>
<td width="80%" align="left"><figure><img src="images/icon_success_sml.gif" alt="" /></figure>&#160;License defined in the pom of the dependency</td></tr>
<tr class="b">
<th width="20%">GroupId:ArtifactId:Version</th>
<td width="80%" align="left">software.amazon.eventstream:eventstream:1.0.1</td></tr>
<tr class="a">
<th width="20%">Scope</th>
<td width="80%" align="left">compile</td></tr>
<tr class="b">
<th width="20%">Classifier</th>
<td width="80%" align="left"></td></tr>
<tr class="a">
<th width="20%">Type</th>
<td width="80%" align="left">jar</td></tr>
<tr class="b">
<th width="20%">License(s)</th>
<td width="80%" align="left">Apache License, Version 2.0</td></tr></table><a href="#Overview">Back to top</a><br /></section><section>
<h3><a name="xerces:xercesImpl:2.12.2"></a>xerces:xercesImpl:2.12.2</h3>
<table border="0" class="bodyTable">
<tr class="a">
<th width="20%">Status</th>
<td width="80%" align="left"><figure><img src="images/icon_success_sml.gif" alt="" /></figure>&#160;License defined in the pom of the dependency</td></tr>
<tr class="b">
<th width="20%">GroupId:ArtifactId:Version</th>
<td width="80%" align="left">xerces:xercesImpl:2.12.2</td></tr>
<tr class="a">
<th width="20%">Scope</th>
<td width="80%" align="left">compile</td></tr>
<tr class="b">
<th width="20%">Classifier</th>
<td width="80%" align="left"></td></tr>
<tr class="a">
<th width="20%">Type</th>
<td width="80%" align="left">jar</td></tr>
<tr class="b">
<th width="20%">License(s)</th>
<td width="80%" align="left">The Apache Software License, Version 2.0</td></tr></table><a href="#Overview">Back to top</a><br /></section><section>
<h3><a name="xml-apis:xml-apis:1.4.01"></a>xml-apis:xml-apis:1.4.01</h3>
<table border="0" class="bodyTable">
<tr class="a">
<th width="20%">Status</th>
<td width="80%" align="left"><figure><img src="images/icon_success_sml.gif" alt="" /></figure>&#160;License defined in the pom of the dependency</td></tr>
<tr class="b">
<th width="20%">GroupId:ArtifactId:Version</th>
<td width="80%" align="left">xml-apis:xml-apis:1.4.01</td></tr>
<tr class="a">
<th width="20%">Scope</th>
<td width="80%" align="left">compile</td></tr>
<tr class="b">
<th width="20%">Classifier</th>
<td width="80%" align="left"></td></tr>
<tr class="a">
<th width="20%">Type</th>
<td width="80%" align="left">jar</td></tr>
<tr class="b">
<th width="20%">License(s)</th>
<td width="80%" align="left">The Apache Software License, Version 2.0<br />The SAX License<br />The W3C License</td></tr></table><a href="#Overview">Back to top</a><br /></section></section>
      </div>
    </div>
    <div class="clear">
      <hr/>
    </div>
    <div id="footer">
      <div class="xright">
        Copyright &#169;      2025..      </div>
      <div class="clear">
        <hr/>
      </div>
    </div>
  </body>
</html>
