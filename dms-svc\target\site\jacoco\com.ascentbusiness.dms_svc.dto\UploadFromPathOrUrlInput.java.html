<?xml version="1.0" encoding="UTF-8"?><!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Strict//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-strict.dtd"><html xmlns="http://www.w3.org/1999/xhtml" lang="en"><head><meta http-equiv="Content-Type" content="text/html;charset=UTF-8"/><link rel="stylesheet" href="../jacoco-resources/report.css" type="text/css"/><link rel="shortcut icon" href="../jacoco-resources/report.gif" type="image/gif"/><title>UploadFromPathOrUrlInput.java</title><link rel="stylesheet" href="../jacoco-resources/prettify.css" type="text/css"/><script type="text/javascript" src="../jacoco-resources/prettify.js"></script></head><body onload="window['PR_TAB_WIDTH']=4;prettyPrint()"><div class="breadcrumb" id="breadcrumb"><span class="info"><a href="../jacoco-sessions.html" class="el_session">Sessions</a></span><a href="../index.html" class="el_report">dms-svc</a> &gt; <a href="index.source.html" class="el_package">com.ascentbusiness.dms_svc.dto</a> &gt; <span class="el_source">UploadFromPathOrUrlInput.java</span></div><h1>UploadFromPathOrUrlInput.java</h1><pre class="source lang-java linenums">/*
 * Copyright (c) 2024 Ascent Business Technology
 * All rights reserved.
 */

package com.ascentbusiness.dms_svc.dto;

import com.ascentbusiness.dms_svc.enums.StorageProvider;
import lombok.Data;

import java.nio.file.Files;
import java.nio.file.Paths;
import java.util.List;

/**
 * Input DTO for document upload from various sources (file paths, URLs, network paths).
 * 
 * &lt;p&gt;This DTO supports multiple source types:
 * &lt;ul&gt;
 *   &lt;li&gt;Local file paths (for development environments)&lt;/li&gt;
 *   &lt;li&gt;HTTP/HTTPS URLs (for remote file downloads)&lt;/li&gt;
 *   &lt;li&gt;Network paths (for shared storage scenarios)&lt;/li&gt;
 *   &lt;li&gt;File URIs (file:// protocol)&lt;/li&gt;
 * &lt;/ul&gt;
 * 
 * &lt;p&gt;The implementation automatically detects the source type and handles
 * the upload accordingly, providing a unified interface for different
 * deployment scenarios.
 * 
 * <AUTHOR> Development Team
 * @version 1.0.0
 * @since 1.0.0
 */
@Data
public class UploadFromPathOrUrlInput {
    
    /**
     * Source path or URL for the file to upload.
     * Can be:
     * - Local file path: C:\Users\<USER>\file.pdf
     * - HTTP URL: https://example.com/files/document.pdf
     * - Network path: \\\\shared-server\\uploads\\file.pdf
     * - File URI: file:///path/to/file.pdf
     */
    private String sourcePath;
    
    /**
     * Display name for the document (optional).
     * If not provided, will be extracted from the source path/URL.
     */
    private String name;
    
    /**
     * Description of the document (optional).
     */
    private String description;
    
    /**
     * Storage provider to use for storing the document (optional).
     * Defaults to application configuration if not specified.
     */
    private StorageProvider storageProvider;
    
    /**
     * Keywords/tags for the document (optional).
     */
    private List&lt;String&gt; keywords;
    
    /**
     * Access roles for the document (optional).
     */
    private List&lt;DocumentAccessRoleInput&gt; accessRoles;
    
    /**
     * Whether to allow duplicate documents (optional).
     * Defaults to false.
     */
    private Boolean allowDuplicates = false;
    
    /**
     * Timeout for URL downloads in milliseconds (optional).
     * Uses system default if not specified.
     */
    private Integer downloadTimeoutMs;
    
    /**
     * Maximum file size to download from URLs in bytes (optional).
     * Uses system default if not specified.
     */
    private Long maxDownloadSizeBytes;
    
    // Helper methods for source type detection
    
    /**
     * Check if the source is an HTTP/HTTPS URL.
     * 
     * @return true if source is a web URL
     */
    public boolean isUrl() {
<span class="pc bpc" id="L100" title="1 of 2 branches missed.">        return sourcePath != null &amp;&amp; </span>
<span class="pc bpc" id="L101" title="1 of 4 branches missed.">               (sourcePath.startsWith(&quot;http://&quot;) || sourcePath.startsWith(&quot;https://&quot;));</span>
    }
    
    /**
     * Check if the source is a network path (UNC path).
     * 
     * @return true if source is a network path
     */
    public boolean isNetworkPath() {
<span class="pc bpc" id="L110" title="1 of 2 branches missed.">        return sourcePath != null &amp;&amp; </span>
<span class="fc bfc" id="L111" title="All 4 branches covered.">               (sourcePath.startsWith(&quot;\\\\&quot;) || sourcePath.startsWith(&quot;//&quot;));</span>
    }
    
    /**
     * Check if the source is a file URI.
     * 
     * @return true if source is a file URI
     */
    public boolean isFileUri() {
<span class="pc bpc" id="L120" title="1 of 4 branches missed.">        return sourcePath != null &amp;&amp; sourcePath.startsWith(&quot;file://&quot;);</span>
    }
    
    /**
     * Check if the source is a local file path that exists.
     * 
     * @return true if source is an existing local file path
     */
    public boolean isLocalPath() {
<span class="pc bpc" id="L129" title="1 of 2 branches missed.">        if (sourcePath == null) {</span>
<span class="nc" id="L130">            return false;</span>
        }
        try {
<span class="fc" id="L133">            return Files.exists(Paths.get(sourcePath));</span>
<span class="fc" id="L134">        } catch (Exception e) {</span>
<span class="fc" id="L135">            return false;</span>
        }
    }
    
    /**
     * Extract filename from the source path or URL.
     * 
     * @return extracted filename or null if cannot be determined
     */
    public String extractFileName() {
<span class="pc bpc" id="L145" title="1 of 2 branches missed.">        if (sourcePath == null) {</span>
<span class="nc" id="L146">            return null;</span>
        }
        
        try {
<span class="fc bfc" id="L150" title="All 2 branches covered.">            if (isUrl()) {</span>
                // Extract filename from URL
<span class="fc" id="L152">                String path = new java.net.URL(sourcePath).getPath();</span>
<span class="fc" id="L153">                return path.substring(path.lastIndexOf('/') + 1);</span>
<span class="fc bfc" id="L154" title="All 2 branches covered.">            } else if (isFileUri()) {</span>
                // Extract filename from file URI
<span class="fc" id="L156">                String path = sourcePath.substring(&quot;file://&quot;.length());</span>
<span class="fc" id="L157">                return Paths.get(path).getFileName().toString();</span>
            } else {
                // Extract filename from regular path
<span class="fc" id="L160">                return Paths.get(sourcePath).getFileName().toString();</span>
            }
<span class="nc" id="L162">        } catch (Exception e) {</span>
            // Fallback: extract from the end of the path
<span class="nc" id="L164">            String[] parts = sourcePath.split(&quot;[/\\\\]&quot;);</span>
<span class="nc bnc" id="L165" title="All 2 branches missed.">            return parts.length &gt; 0 ? parts[parts.length - 1] : null;</span>
        }
    }
    
    /**
     * Get the effective name for the document.
     * Uses provided name or extracts from source path.
     * 
     * @return effective document name
     */
    public String getEffectiveName() {
<span class="fc bfc" id="L176" title="All 2 branches covered.">        return name != null ? name : extractFileName();</span>
    }
    
    /**
     * Validate the input parameters.
     * 
     * @throws IllegalArgumentException if validation fails
     */
    public void validate() {
<span class="fc bfc" id="L185" title="All 4 branches covered.">        if (sourcePath == null || sourcePath.trim().isEmpty()) {</span>
<span class="fc" id="L186">            throw new IllegalArgumentException(&quot;Source path is required&quot;);</span>
        }
        
        // Basic security validation - check for actual path traversal patterns
        // Allow Windows 8.3 short filenames (which contain ~) but block actual traversal attempts
<span class="fc" id="L191">        String normalizedPath = sourcePath.replace('\\', '/');</span>
<span class="pc bpc" id="L192" title="1 of 4 branches missed.">        if (normalizedPath.contains(&quot;../&quot;) || normalizedPath.contains(&quot;/..&quot;) ||</span>
<span class="pc bpc" id="L193" title="2 of 4 branches missed.">            normalizedPath.matches(&quot;.*\\.\\./?$&quot;) || normalizedPath.startsWith(&quot;../&quot;)) {</span>
<span class="fc" id="L194">            throw new IllegalArgumentException(&quot;Path traversal attempts are not allowed&quot;);</span>
        }
        
        // Validate URL format if it's a URL
<span class="pc bpc" id="L198" title="1 of 2 branches missed.">        if (isUrl()) {</span>
            try {
<span class="nc" id="L200">                new java.net.URL(sourcePath);</span>
<span class="nc" id="L201">            } catch (java.net.MalformedURLException e) {</span>
<span class="nc" id="L202">                throw new IllegalArgumentException(&quot;Invalid URL format: &quot; + e.getMessage());</span>
            }
        }
<span class="fc" id="L205">    }</span>
}
</pre><div class="footer"><span class="right">Created with <a href="http://www.jacoco.org/jacoco">JaCoCo</a> 0.8.10.202304240956</span></div></body></html>