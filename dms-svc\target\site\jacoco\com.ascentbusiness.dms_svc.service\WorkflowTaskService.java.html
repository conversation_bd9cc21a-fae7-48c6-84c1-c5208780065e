<?xml version="1.0" encoding="UTF-8"?><!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Strict//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-strict.dtd"><html xmlns="http://www.w3.org/1999/xhtml" lang="en"><head><meta http-equiv="Content-Type" content="text/html;charset=UTF-8"/><link rel="stylesheet" href="../jacoco-resources/report.css" type="text/css"/><link rel="shortcut icon" href="../jacoco-resources/report.gif" type="image/gif"/><title>WorkflowTaskService.java</title><link rel="stylesheet" href="../jacoco-resources/prettify.css" type="text/css"/><script type="text/javascript" src="../jacoco-resources/prettify.js"></script></head><body onload="window['PR_TAB_WIDTH']=4;prettyPrint()"><div class="breadcrumb" id="breadcrumb"><span class="info"><a href="../jacoco-sessions.html" class="el_session">Sessions</a></span><a href="../index.html" class="el_report">dms-svc</a> &gt; <a href="index.source.html" class="el_package">com.ascentbusiness.dms_svc.service</a> &gt; <span class="el_source">WorkflowTaskService.java</span></div><h1>WorkflowTaskService.java</h1><pre class="source lang-java linenums">package com.ascentbusiness.dms_svc.service;

import com.ascentbusiness.dms_svc.entity.WorkflowTask;
import com.ascentbusiness.dms_svc.entity.WorkflowInstance;
import com.ascentbusiness.dms_svc.entity.WorkflowStage;
import com.ascentbusiness.dms_svc.enums.TaskStatus;
import com.ascentbusiness.dms_svc.enums.WorkflowAction;
import com.ascentbusiness.dms_svc.repository.WorkflowTaskRepository;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.util.List;

/**
 * Service for managing workflow tasks
 */
@Service
<span class="fc" id="L21">@RequiredArgsConstructor</span>
<span class="fc" id="L22">@Slf4j</span>
@Transactional
public class WorkflowTaskService {

    private final WorkflowTaskRepository workflowTaskRepository;
    private final AuditService auditService;

    /**
     * Create tasks for a workflow stage
     */
    public List&lt;WorkflowTask&gt; createTasksForStage(WorkflowInstance workflowInstance, WorkflowStage stage) {
<span class="nc" id="L33">        log.info(&quot;Creating tasks for stage: {} in workflow instance: {}&quot;, stage.getStageName(), workflowInstance.getId());</span>
        
        // For now, create a single task per stage
        // In a real implementation, this would parse stage configuration to create multiple tasks
<span class="nc" id="L37">        WorkflowTask task = WorkflowTask.builder()</span>
<span class="nc" id="L38">                .workflowInstance(workflowInstance)</span>
<span class="nc" id="L39">                .workflowStage(stage)</span>
<span class="nc" id="L40">                .taskName(stage.getStageName())</span>
<span class="nc" id="L41">                .status(TaskStatus.PENDING)</span>
<span class="nc" id="L42">                .priority(workflowInstance.getPriority())</span>
<span class="nc" id="L43">                .assignedDate(LocalDateTime.now())</span>
<span class="nc" id="L44">                .build();</span>
        
        // Set due date based on stage timeout
<span class="nc bnc" id="L47" title="All 2 branches missed.">        if (stage.getTimeoutHours() != null) {</span>
<span class="nc" id="L48">            task.setDueDate(LocalDateTime.now().plusHours(stage.getTimeoutHours()));</span>
        }
        
        // Assign task based on stage configuration
<span class="nc" id="L52">        assignTask(task, stage);</span>
        
<span class="nc" id="L54">        WorkflowTask saved = workflowTaskRepository.save(task);</span>
        
<span class="nc" id="L56">        log.info(&quot;Created task with ID: {} for stage: {}&quot;, saved.getId(), stage.getStageName());</span>
<span class="nc" id="L57">        return List.of(saved);</span>
    }

    /**
     * Complete a task
     */
    public WorkflowTask completeTask(Long taskId, String completedBy, WorkflowAction action, 
                                   String comments, String delegateToUserId) {
<span class="nc" id="L65">        log.info(&quot;Completing task ID: {} by user: {} with action: {}&quot;, taskId, completedBy, action);</span>
        
<span class="nc" id="L67">        WorkflowTask task = getTaskById(taskId);</span>
        
<span class="nc bnc" id="L69" title="All 4 branches missed.">        if (task.getStatus() != TaskStatus.PENDING &amp;&amp; task.getStatus() != TaskStatus.IN_PROGRESS) {</span>
<span class="nc" id="L70">            throw new RuntimeException(&quot;Task is not in a state that can be completed&quot;);</span>
        }
        
        // Handle delegation
<span class="nc bnc" id="L74" title="All 4 branches missed.">        if (action == WorkflowAction.DELEGATE &amp;&amp; delegateToUserId != null) {</span>
<span class="nc" id="L75">            return delegateTask(taskId, delegateToUserId, completedBy, comments);</span>
        }
        
        // Complete the task
<span class="nc" id="L79">        task.setStatus(TaskStatus.COMPLETED);</span>
<span class="nc" id="L80">        task.setActionTaken(action);</span>
<span class="nc" id="L81">        task.setCompletedDate(LocalDateTime.now());</span>
<span class="nc" id="L82">        task.setCompletedByUserId(completedBy);</span>
<span class="nc" id="L83">        task.setComments(comments);</span>
        
<span class="nc" id="L85">        WorkflowTask saved = workflowTaskRepository.save(task);</span>
        
        // Create audit log
<span class="nc" id="L88">        auditService.logAudit(com.ascentbusiness.dms_svc.enums.AuditAction.UPDATE, completedBy, </span>
<span class="nc" id="L89">                             &quot;Task completed: &quot; + task.getTaskName() + &quot; with action: &quot; + action);</span>
        
<span class="nc" id="L91">        log.info(&quot;Completed task ID: {} with action: {}&quot;, saved.getId(), action);</span>
<span class="nc" id="L92">        return saved;</span>
    }

    /**
     * Reassign a task
     */
    public WorkflowTask reassignTask(Long taskId, String newAssigneeUserId, String reassignedBy) {
<span class="nc" id="L99">        log.info(&quot;Reassigning task ID: {} to user: {} by: {}&quot;, taskId, newAssigneeUserId, reassignedBy);</span>
        
<span class="nc" id="L101">        WorkflowTask task = getTaskById(taskId);</span>
        
<span class="nc bnc" id="L103" title="All 4 branches missed.">        if (task.getStatus() == TaskStatus.COMPLETED || task.getStatus() == TaskStatus.CANCELLED) {</span>
<span class="nc" id="L104">            throw new RuntimeException(&quot;Cannot reassign a completed or cancelled task&quot;);</span>
        }
        
<span class="nc" id="L107">        String previousAssignee = task.getAssignedToUserId();</span>
<span class="nc" id="L108">        task.setAssignedToUserId(newAssigneeUserId);</span>
<span class="nc" id="L109">        task.setAssignedDate(LocalDateTime.now());</span>
        
<span class="nc" id="L111">        WorkflowTask saved = workflowTaskRepository.save(task);</span>
        
        // Create audit log
<span class="nc" id="L114">        auditService.logAudit(com.ascentbusiness.dms_svc.enums.AuditAction.UPDATE, reassignedBy, </span>
                             &quot;Task reassigned from &quot; + previousAssignee + &quot; to &quot; + newAssigneeUserId);
        
<span class="nc" id="L117">        log.info(&quot;Reassigned task ID: {} from {} to {}&quot;, saved.getId(), previousAssignee, newAssigneeUserId);</span>
<span class="nc" id="L118">        return saved;</span>
    }

    /**
     * Escalate a task
     */
    public WorkflowTask escalateTask(Long taskId, String escalateToUserId, String escalatedBy, String reason) {
<span class="nc" id="L125">        log.info(&quot;Escalating task ID: {} to user: {} by: {}&quot;, taskId, escalateToUserId, escalatedBy);</span>
        
<span class="nc" id="L127">        WorkflowTask task = getTaskById(taskId);</span>
        
<span class="nc bnc" id="L129" title="All 4 branches missed.">        if (task.getStatus() == TaskStatus.COMPLETED || task.getStatus() == TaskStatus.CANCELLED) {</span>
<span class="nc" id="L130">            throw new RuntimeException(&quot;Cannot escalate a completed or cancelled task&quot;);</span>
        }
        
<span class="nc" id="L133">        String previousAssignee = task.getAssignedToUserId();</span>
<span class="nc" id="L134">        task.setAssignedToUserId(escalateToUserId);</span>
<span class="nc" id="L135">        task.setStatus(TaskStatus.ESCALATED);</span>
<span class="nc" id="L136">        task.setAssignedDate(LocalDateTime.now());</span>
<span class="nc" id="L137">        task.setComments(reason);</span>
        
<span class="nc" id="L139">        WorkflowTask saved = workflowTaskRepository.save(task);</span>
        
        // Create audit log
<span class="nc" id="L142">        auditService.logAudit(com.ascentbusiness.dms_svc.enums.AuditAction.UPDATE, escalatedBy, </span>
                             &quot;Task escalated from &quot; + previousAssignee + &quot; to &quot; + escalateToUserId + &quot;. Reason: &quot; + reason);
        
<span class="nc" id="L145">        log.info(&quot;Escalated task ID: {} from {} to {}&quot;, saved.getId(), previousAssignee, escalateToUserId);</span>
<span class="nc" id="L146">        return saved;</span>
    }

    /**
     * Delegate a task
     */
    public WorkflowTask delegateTask(Long taskId, String delegateToUserId, String delegatedBy, String comments) {
<span class="nc" id="L153">        log.info(&quot;Delegating task ID: {} to user: {} by: {}&quot;, taskId, delegateToUserId, delegatedBy);</span>
        
<span class="nc" id="L155">        WorkflowTask task = getTaskById(taskId);</span>
        
<span class="nc bnc" id="L157" title="All 4 branches missed.">        if (task.getStatus() == TaskStatus.COMPLETED || task.getStatus() == TaskStatus.CANCELLED) {</span>
<span class="nc" id="L158">            throw new RuntimeException(&quot;Cannot delegate a completed or cancelled task&quot;);</span>
        }
        
<span class="nc" id="L161">        String previousAssignee = task.getAssignedToUserId();</span>
<span class="nc" id="L162">        task.setAssignedToUserId(delegateToUserId);</span>
<span class="nc" id="L163">        task.setStatus(TaskStatus.DELEGATED);</span>
<span class="nc" id="L164">        task.setAssignedDate(LocalDateTime.now());</span>
<span class="nc" id="L165">        task.setComments(comments);</span>
        
<span class="nc" id="L167">        WorkflowTask saved = workflowTaskRepository.save(task);</span>
        
        // Create audit log
<span class="nc" id="L170">        auditService.logAudit(com.ascentbusiness.dms_svc.enums.AuditAction.UPDATE, delegatedBy, </span>
                             &quot;Task delegated from &quot; + previousAssignee + &quot; to &quot; + delegateToUserId);
        
<span class="nc" id="L173">        log.info(&quot;Delegated task ID: {} from {} to {}&quot;, saved.getId(), previousAssignee, delegateToUserId);</span>
<span class="nc" id="L174">        return saved;</span>
    }

    /**
     * Cancel pending tasks for a workflow instance
     */
    public void cancelPendingTasksForInstance(Long workflowInstanceId) {
<span class="nc" id="L181">        log.info(&quot;Cancelling pending tasks for workflow instance: {}&quot;, workflowInstanceId);</span>
        
<span class="nc" id="L183">        List&lt;WorkflowTask&gt; pendingTasks = workflowTaskRepository.findByWorkflowInstanceIdAndStatus(</span>
                workflowInstanceId, TaskStatus.PENDING);
        
<span class="nc bnc" id="L186" title="All 2 branches missed.">        for (WorkflowTask task : pendingTasks) {</span>
<span class="nc" id="L187">            task.setStatus(TaskStatus.CANCELLED);</span>
<span class="nc" id="L188">            task.setCompletedDate(LocalDateTime.now());</span>
<span class="nc" id="L189">            workflowTaskRepository.save(task);</span>
<span class="nc" id="L190">        }</span>
        
<span class="nc" id="L192">        log.info(&quot;Cancelled {} pending tasks for workflow instance: {}&quot;, pendingTasks.size(), workflowInstanceId);</span>
<span class="nc" id="L193">    }</span>

    /**
     * Get task by ID
     */
    @Transactional(readOnly = true)
    public WorkflowTask getTaskById(Long taskId) {
<span class="nc" id="L200">        return workflowTaskRepository.findById(taskId)</span>
<span class="nc" id="L201">                .orElseThrow(() -&gt; new RuntimeException(&quot;Task not found with ID: &quot; + taskId));</span>
    }

    /**
     * Get tasks for a workflow instance
     */
    @Transactional(readOnly = true)
    public List&lt;WorkflowTask&gt; getTasksForInstance(Long workflowInstanceId) {
<span class="nc" id="L209">        return workflowTaskRepository.findByWorkflowInstanceId(workflowInstanceId);</span>
    }

    /**
     * Get tasks assigned to a user
     */
    @Transactional(readOnly = true)
    public List&lt;WorkflowTask&gt; getTasksAssignedToUser(String userId) {
<span class="nc" id="L217">        return workflowTaskRepository.findByAssignedToUserIdAndStatusIn(</span>
<span class="nc" id="L218">                userId, List.of(TaskStatus.PENDING, TaskStatus.IN_PROGRESS));</span>
    }

    /**
     * Get overdue tasks
     */
    @Transactional(readOnly = true)
    public List&lt;WorkflowTask&gt; getOverdueTasks() {
<span class="nc" id="L226">        return workflowTaskRepository.findOverdueTasks(LocalDateTime.now(), </span>
<span class="nc" id="L227">                List.of(TaskStatus.PENDING, TaskStatus.IN_PROGRESS));</span>
    }

    // Private helper methods

    private void assignTask(WorkflowTask task, WorkflowStage stage) {
        // This is a simplified assignment logic
        // In a real implementation, this would parse stage configuration
        // to determine how to assign tasks (by user, role, department, etc.)
        
        // For now, just set a placeholder
<span class="nc" id="L238">        task.setAssignedToUserId(&quot;admin&quot;); // This should be configurable</span>
<span class="nc" id="L239">        task.setStatus(TaskStatus.PENDING);</span>
<span class="nc" id="L240">    }</span>
}
</pre><div class="footer"><span class="right">Created with <a href="http://www.jacoco.org/jacoco">JaCoCo</a> 0.8.10.202304240956</span></div></body></html>