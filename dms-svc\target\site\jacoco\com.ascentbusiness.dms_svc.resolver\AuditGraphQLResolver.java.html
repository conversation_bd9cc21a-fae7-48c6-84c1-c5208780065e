<?xml version="1.0" encoding="UTF-8"?><!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Strict//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-strict.dtd"><html xmlns="http://www.w3.org/1999/xhtml" lang="en"><head><meta http-equiv="Content-Type" content="text/html;charset=UTF-8"/><link rel="stylesheet" href="../jacoco-resources/report.css" type="text/css"/><link rel="shortcut icon" href="../jacoco-resources/report.gif" type="image/gif"/><title>AuditGraphQLResolver.java</title><link rel="stylesheet" href="../jacoco-resources/prettify.css" type="text/css"/><script type="text/javascript" src="../jacoco-resources/prettify.js"></script></head><body onload="window['PR_TAB_WIDTH']=4;prettyPrint()"><div class="breadcrumb" id="breadcrumb"><span class="info"><a href="../jacoco-sessions.html" class="el_session">Sessions</a></span><a href="../index.html" class="el_report">dms-svc</a> &gt; <a href="index.source.html" class="el_package">com.ascentbusiness.dms_svc.resolver</a> &gt; <span class="el_source">AuditGraphQLResolver.java</span></div><h1>AuditGraphQLResolver.java</h1><pre class="source lang-java linenums">package com.ascentbusiness.dms_svc.resolver;

import com.ascentbusiness.dms_svc.entity.AuditLog;
import com.ascentbusiness.dms_svc.entity.AuditVerificationLog;
import com.ascentbusiness.dms_svc.service.AuditService;
import com.ascentbusiness.dms_svc.service.AuditExportService;
import com.ascentbusiness.dms_svc.service.AuditVerificationService;
import com.ascentbusiness.dms_svc.dto.*;
import com.ascentbusiness.dms_svc.enums.AuditAction;
import com.ascentbusiness.dms_svc.repository.AuditLogRepository;
import com.ascentbusiness.dms_svc.repository.AuditVerificationLogRepository;
import com.ascentbusiness.dms_svc.security.UserContext;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Sort;
import org.springframework.graphql.data.method.annotation.Argument;
import org.springframework.graphql.data.method.annotation.MutationMapping;
import org.springframework.graphql.data.method.annotation.QueryMapping;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.stereotype.Controller;

import java.time.LocalDateTime;
import java.time.OffsetDateTime;
import java.time.temporal.ChronoUnit;
import java.util.*;
import java.util.stream.Collectors;

/**
 * GraphQL Resolver for Audit Operations
 * Replaces AuditController REST endpoints with GraphQL queries and mutations
 * 
 * Covers the following operations:
 * - GET /api/audit/logs -&gt; getAuditLogs query
 * - GET /api/audit/compliance -&gt; getComplianceAuditLogs query  
 * - POST /api/audit/export -&gt; requestAuditExport mutation
 * - POST /api/audit/verify/{auditLogId} -&gt; verifyAuditLog mutation
 * - GET /api/audit/tampering -&gt; getTamperingDetections query
 */
@Controller
<span class="fc" id="L43">@RequiredArgsConstructor</span>
<span class="fc" id="L44">@Slf4j</span>
public class AuditGraphQLResolver {

    private final AuditService auditService;
    private final AuditExportService auditExportService;
    private final AuditVerificationService auditVerificationService;
    private final AuditLogRepository auditLogRepository;
    private final AuditVerificationLogRepository auditVerificationLogRepository;
    private final UserContext userContext;

    // ===== QUERY OPERATIONS =====

    /**
     * Get audit logs with filtering and pagination
     * Replaces: GET /api/audit/logs
     */
    @QueryMapping
    @PreAuthorize(&quot;hasRole('USER') or hasRole('ADMIN')&quot;)
    public AuditLogPageResponse getAuditLogs(
            @Argument AuditLogFilterInput filter,
            @Argument AuditPaginationInput pagination) {
        
<span class="nc" id="L66">        log.info(&quot;GraphQL Query: getAuditLogs with filter: {} and pagination: {}&quot;, filter, pagination);</span>
        
        try {
<span class="nc" id="L69">            Pageable pageable = createPageable(pagination);</span>
            
            // Extract filter parameters
<span class="nc bnc" id="L72" title="All 2 branches missed.">            Long documentId = filter != null ? filter.getDocumentId() : null;</span>
<span class="nc bnc" id="L73" title="All 2 branches missed.">            String userId = filter != null ? filter.getUserId() : null;</span>
<span class="nc bnc" id="L74" title="All 2 branches missed.">            AuditAction action = filter != null ? filter.getAction() : null;</span>
<span class="nc bnc" id="L75" title="All 4 branches missed.">            LocalDateTime dateFrom = filter != null &amp;&amp; filter.getDateFrom() != null ?</span>
<span class="nc" id="L76">                filter.getDateFrom().toLocalDateTime() : null;</span>
<span class="nc bnc" id="L77" title="All 4 branches missed.">            LocalDateTime dateTo = filter != null &amp;&amp; filter.getDateTo() != null ?</span>
<span class="nc" id="L78">                filter.getDateTo().toLocalDateTime() : null;</span>
            
<span class="nc" id="L80">            Page&lt;AuditLog&gt; auditLogPage = auditService.getAuditLogs(</span>
                documentId, userId, action, dateFrom, dateTo, pageable);
            
<span class="nc" id="L83">            return AuditLogPageResponse.fromPage(auditLogPage);</span>
<span class="nc" id="L84">        } catch (Exception e) {</span>
<span class="nc" id="L85">            log.error(&quot;Error retrieving audit logs&quot;, e);</span>
<span class="nc" id="L86">            throw new RuntimeException(&quot;Failed to retrieve audit logs: &quot; + e.getMessage());</span>
        }
    }

    /**
     * Get compliance-specific audit logs
     * Replaces: GET /api/audit/compliance
     */
    @QueryMapping
    @PreAuthorize(&quot;hasRole('COMPLIANCE_OFFICER')&quot;)
    public ComplianceAuditPage getComplianceAuditLogs(
            @Argument ComplianceAuditFilterInput filter,
            @Argument AuditPaginationInput pagination) {

<span class="nc" id="L100">        log.info(&quot;GraphQL Query: getComplianceAuditLogs with filter: {} and pagination: {}&quot;, filter, pagination);</span>

        try {
<span class="nc" id="L103">            Pageable pageable = createPageable(pagination);</span>

            // Extract filter parameters for compliance
<span class="nc bnc" id="L106" title="All 4 branches missed.">            LocalDateTime dateFrom = filter != null &amp;&amp; filter.getDateFrom() != null ?</span>
<span class="nc" id="L107">                filter.getDateFrom().toLocalDateTime() : null;</span>
<span class="nc bnc" id="L108" title="All 4 branches missed.">            LocalDateTime dateTo = filter != null &amp;&amp; filter.getDateTo() != null ?</span>
<span class="nc" id="L109">                filter.getDateTo().toLocalDateTime() : null;</span>
<span class="nc bnc" id="L110" title="All 2 branches missed.">            String riskLevel = filter != null ? filter.getSeverity() : null;</span>
<span class="nc bnc" id="L111" title="All 2 branches missed.">            Long complianceFrameworkId = filter != null ? filter.getDocumentId() : null;</span>

<span class="nc" id="L113">            Page&lt;AuditLog&gt; complianceAuditPage = auditService.getComplianceAuditLogs(</span>
                null, null, null, null, null, riskLevel, complianceFrameworkId,
                null, dateFrom, dateTo, pageable);

<span class="nc" id="L117">            return ComplianceAuditPage.fromPage(complianceAuditPage);</span>
<span class="nc" id="L118">        } catch (Exception e) {</span>
<span class="nc" id="L119">            log.error(&quot;Error retrieving compliance audit logs&quot;, e);</span>
<span class="nc" id="L120">            throw new RuntimeException(&quot;Failed to retrieve compliance audit logs: &quot; + e.getMessage());</span>
        }
    }

    /**
     * Get tampering detections
     * Replaces: GET /api/audit/tampering
     */
    @QueryMapping
    @PreAuthorize(&quot;hasRole('SECURITY_OFFICER')&quot;)
    public List&lt;AuditVerificationLog&gt; getTamperingDetections(
            @Argument String severity,
            @Argument Boolean resolved,
            @Argument OffsetDateTime dateFrom,
            @Argument OffsetDateTime dateTo) {
<span class="nc" id="L135">        log.info(&quot;GraphQL Query: getTamperingDetections with severity: {}, resolved: {}, dateFrom: {}, dateTo: {}&quot;,</span>
                severity, resolved, dateFrom, dateTo);

        try {
            // Convert OffsetDateTime to LocalDateTime for service layer
<span class="nc bnc" id="L140" title="All 2 branches missed.">            LocalDateTime localDateFrom = dateFrom != null ? dateFrom.toLocalDateTime() : null;</span>
<span class="nc bnc" id="L141" title="All 2 branches missed.">            LocalDateTime localDateTo = dateTo != null ? dateTo.toLocalDateTime() : null;</span>

            // Note: severity parameter is ignored for now as the entity doesn't have a severity field
<span class="nc" id="L144">            return auditVerificationService.getTamperingDetections(resolved, localDateFrom, localDateTo);</span>
<span class="nc" id="L145">        } catch (Exception e) {</span>
<span class="nc" id="L146">            log.error(&quot;Error retrieving tampering detections&quot;, e);</span>
<span class="nc" id="L147">            throw new RuntimeException(&quot;Failed to retrieve tampering detections: &quot; + e.getMessage());</span>
        }
    }

    /**
     * Get audit statistics
     * Implements getAuditStatistics query from audit-schema.graphqls
     */
    @QueryMapping
    @PreAuthorize(&quot;hasRole('USER')&quot;)
    public AuditStatistics getAuditStatistics(
            @Argument LocalDateTime dateFrom,
            @Argument LocalDateTime dateTo) {
<span class="nc" id="L160">        log.info(&quot;GraphQL Query: getAuditStatistics from {} to {}&quot;, dateFrom, dateTo);</span>

        try {
            // Set default date range if not provided
<span class="nc bnc" id="L164" title="All 2 branches missed.">            if (dateFrom == null) {</span>
<span class="nc" id="L165">                dateFrom = LocalDateTime.now().minusMonths(1);</span>
            }
<span class="nc bnc" id="L167" title="All 2 branches missed.">            if (dateTo == null) {</span>
<span class="nc" id="L168">                dateTo = LocalDateTime.now();</span>
            }

<span class="nc" id="L171">            return buildAuditStatistics(dateFrom, dateTo);</span>
<span class="nc" id="L172">        } catch (Exception e) {</span>
<span class="nc" id="L173">            log.error(&quot;Failed to get audit statistics&quot;, e);</span>
<span class="nc" id="L174">            throw new RuntimeException(&quot;Failed to retrieve audit statistics&quot;, e);</span>
        }
    }

    /**
     * Get document audit trail
     */
    @QueryMapping
    @PreAuthorize(&quot;hasRole('USER')&quot;)
    public AuditLogPageResponse getDocumentAuditTrail(
            @Argument Long documentId,
            @Argument AuditPaginationInput pagination) {
        
<span class="nc" id="L187">        log.info(&quot;GraphQL Query: getDocumentAuditTrail for document: {}&quot;, documentId);</span>
        
        try {
<span class="nc" id="L190">            Pageable pageable = createPageable(pagination);</span>
<span class="nc" id="L191">            Page&lt;AuditLog&gt; auditLogPage = auditService.getDocumentAuditTrail(documentId, pageable);</span>
            
<span class="nc" id="L193">            return AuditLogPageResponse.fromPage(auditLogPage);</span>
<span class="nc" id="L194">        } catch (Exception e) {</span>
<span class="nc" id="L195">            log.error(&quot;Error retrieving document audit trail for document: {}&quot;, documentId, e);</span>
<span class="nc" id="L196">            throw new RuntimeException(&quot;Failed to retrieve document audit trail: &quot; + e.getMessage());</span>
        }
    }

    // ===== MUTATION OPERATIONS =====

    /**
     * Request audit export (async operation)
     * Replaces: POST /api/audit/export
     */
    @MutationMapping
    @PreAuthorize(&quot;hasRole('AUDITOR')&quot;)
    public AuditExportResponse requestAuditExport(@Argument AuditExportRequest request) {
<span class="nc" id="L209">        log.info(&quot;GraphQL Mutation: requestAuditExport with format: {}&quot;, request.getFormat());</span>

        try {
            // Use the format field (ExportFormat enum) and convert to string, fallback to exportFormat field
<span class="nc bnc" id="L213" title="All 2 branches missed.">            String formatString = request.getFormat() != null ? request.getFormat().toString() : request.getExportFormat();</span>

<span class="nc" id="L215">            String exportId = auditExportService.requestAuditExport(</span>
<span class="nc bnc" id="L216" title="All 2 branches missed.">                request.getExportType() != null ? request.getExportType() : &quot;REGULATORY_REPORT&quot;,</span>
                formatString,
<span class="nc" id="L218">                request.getDateFrom(),</span>
<span class="nc" id="L219">                request.getDateTo(),</span>
<span class="nc" id="L220">                request.getDocumentId(),</span>
<span class="nc" id="L221">                request.getUserId(),</span>
<span class="nc" id="L222">                request.getComplianceFrameworkId(),</span>
<span class="nc" id="L223">                request.getRegulatoryRequirement(),</span>
<span class="nc" id="L224">                request.getIncludeSensitiveData()</span>
            );

<span class="nc" id="L227">            AuditExportResponse response = new AuditExportResponse();</span>
<span class="nc" id="L228">            response.setExportId(exportId);</span>
<span class="nc" id="L229">            response.setMessage(&quot;Export request submitted successfully&quot;);</span>
<span class="nc" id="L230">            response.setStatus(com.ascentbusiness.dms_svc.enums.ExportStatus.PENDING);</span>
<span class="nc bnc" id="L231" title="All 2 branches missed.">            response.setFormat(request.getFormat() != null ? request.getFormat() : com.ascentbusiness.dms_svc.enums.ExportFormat.CSV);</span>
<span class="nc" id="L232">            response.setRequestedAt(java.time.OffsetDateTime.now());</span>
<span class="nc" id="L233">            response.setRequestedBy(userContext.getCurrentUserIdOrNull());</span>

<span class="nc" id="L235">            return response;</span>
<span class="nc" id="L236">        } catch (Exception e) {</span>
<span class="nc" id="L237">            log.error(&quot;Error requesting audit export&quot;, e);</span>
<span class="nc" id="L238">            throw new RuntimeException(&quot;Failed to request audit export: &quot; + e.getMessage());</span>
        }
    }

    /**
     * Verify audit log integrity
     * Replaces: POST /api/audit/verify/{auditLogId}
     */
    @MutationMapping
    @PreAuthorize(&quot;hasAnyRole('SYSTEM_ADMIN', 'ADMIN')&quot;)
    public AuditVerificationResponse verifyAuditLog(@Argument Long auditLogId) {
<span class="nc" id="L249">        log.info(&quot;GraphQL Mutation: verifyAuditLog for auditLogId: {}&quot;, auditLogId);</span>

        try {
            // Get current user for verification
<span class="nc" id="L253">            String verifiedBy = userContext.getCurrentUserIdOrNull();</span>
<span class="nc bnc" id="L254" title="All 2 branches missed.">            if (verifiedBy == null) {</span>
<span class="nc" id="L255">                verifiedBy = &quot;SYSTEM_ADMIN&quot;; // fallback for test scenarios</span>
            }

<span class="nc" id="L258">            String verificationId = auditVerificationService.verifyAuditLog(auditLogId, verifiedBy);</span>

            // Retrieve the verification log to populate the response
<span class="nc" id="L261">            var verificationLogOpt = auditVerificationLogRepository.findByVerificationId(verificationId);</span>
<span class="nc bnc" id="L262" title="All 2 branches missed.">            if (verificationLogOpt.isEmpty()) {</span>
<span class="nc" id="L263">                throw new RuntimeException(&quot;Verification log not found for ID: &quot; + verificationId);</span>
            }

<span class="nc" id="L266">            var verificationLog = verificationLogOpt.get();</span>

<span class="nc" id="L268">            AuditVerificationResponse response = new AuditVerificationResponse();</span>
<span class="nc" id="L269">            response.setVerificationId(verificationId);</span>
<span class="nc" id="L270">            response.setAuditLogId(String.valueOf(auditLogId));</span>

            // Map database status to GraphQL enum values
<span class="nc" id="L273">            String graphqlStatus = mapVerificationStatusToGraphQL(verificationLog.getOverallStatus(), verificationLog.isTamperingDetected());</span>
<span class="nc" id="L274">            response.setVerificationStatus(graphqlStatus);</span>

<span class="nc" id="L276">            response.setVerifiedBy(verificationLog.getVerifiedBy());</span>
<span class="nc" id="L277">            response.setVerifiedAt(verificationLog.getVerificationDate().atOffset(java.time.ZoneOffset.UTC));</span>
<span class="nc" id="L278">            response.setIsValid(verificationLog.isVerificationSuccessful());</span>
<span class="nc" id="L279">            response.setTamperingDetected(verificationLog.isTamperingDetected());</span>
<span class="nc bnc" id="L280" title="All 2 branches missed.">            response.setMessage(verificationLog.isTamperingDetected() ?</span>
<span class="nc" id="L281">                &quot;Tampering detected in audit log&quot; : &quot;Audit log verification completed successfully&quot;);</span>

<span class="nc" id="L283">            return response;</span>
<span class="nc" id="L284">        } catch (Exception e) {</span>
<span class="nc" id="L285">            log.error(&quot;Error verifying audit log: {}&quot;, auditLogId, e);</span>
<span class="nc" id="L286">            throw new RuntimeException(&quot;Failed to verify audit log: &quot; + e.getMessage());</span>
        }
    }

    /**
     * Bulk verify audit logs
     * Replaces: POST /api/audit/verify/bulk
     */
    @MutationMapping
    @PreAuthorize(&quot;hasAnyRole('SYSTEM_ADMIN', 'ADMIN')&quot;)
    public BulkAuditVerificationResponse bulkVerifyAuditLogs(@Argument List&lt;String&gt; auditLogIds) {
<span class="nc" id="L297">        log.info(&quot;GraphQL Mutation: bulkVerifyAuditLogs for {} audit logs&quot;, auditLogIds.size());</span>

        try {
            // Get current user for verification
<span class="nc" id="L301">            String verifiedBy = userContext.getCurrentUserIdOrNull();</span>
<span class="nc bnc" id="L302" title="All 2 branches missed.">            if (verifiedBy == null) {</span>
<span class="nc" id="L303">                verifiedBy = &quot;SYSTEM_ADMIN&quot;; // fallback for test scenarios</span>
            }

<span class="nc" id="L306">            List&lt;AuditVerificationResponse&gt; verificationResults = new ArrayList&lt;&gt;();</span>
<span class="nc" id="L307">            int verifiedCount = 0;</span>
<span class="nc" id="L308">            boolean anyTamperingDetected = false;</span>

<span class="nc bnc" id="L310" title="All 2 branches missed.">            for (String auditLogIdStr : auditLogIds) {</span>
                try {
<span class="nc" id="L312">                    Long auditLogId = Long.parseLong(auditLogIdStr);</span>
<span class="nc" id="L313">                    String verificationId = auditVerificationService.verifyAuditLog(auditLogId, verifiedBy);</span>

                    // Retrieve the verification log to populate the response
<span class="nc" id="L316">                    var verificationLogOpt = auditVerificationLogRepository.findByVerificationId(verificationId);</span>
<span class="nc bnc" id="L317" title="All 2 branches missed.">                    if (verificationLogOpt.isPresent()) {</span>
<span class="nc" id="L318">                        var verificationLog = verificationLogOpt.get();</span>

<span class="nc" id="L320">                        AuditVerificationResponse response = new AuditVerificationResponse();</span>
<span class="nc" id="L321">                        response.setVerificationId(verificationId);</span>
<span class="nc" id="L322">                        response.setAuditLogId(auditLogIdStr);</span>

                        // Map database status to GraphQL enum values
<span class="nc" id="L325">                        String graphqlStatus = mapVerificationStatusToGraphQL(verificationLog.getOverallStatus(), verificationLog.isTamperingDetected());</span>
<span class="nc" id="L326">                        response.setVerificationStatus(graphqlStatus);</span>

<span class="nc" id="L328">                        response.setVerifiedBy(verificationLog.getVerifiedBy());</span>
<span class="nc" id="L329">                        response.setVerifiedAt(verificationLog.getVerificationDate().atOffset(java.time.ZoneOffset.UTC));</span>
<span class="nc" id="L330">                        response.setIsValid(verificationLog.isVerificationSuccessful());</span>
<span class="nc" id="L331">                        response.setTamperingDetected(verificationLog.isTamperingDetected());</span>
<span class="nc bnc" id="L332" title="All 2 branches missed.">                        response.setMessage(verificationLog.isTamperingDetected() ?</span>
<span class="nc" id="L333">                            &quot;Tampering detected in audit log&quot; : &quot;Audit log verification completed successfully&quot;);</span>

<span class="nc" id="L335">                        verificationResults.add(response);</span>
<span class="nc" id="L336">                        verifiedCount++;</span>

<span class="nc bnc" id="L338" title="All 2 branches missed.">                        if (verificationLog.isTamperingDetected()) {</span>
<span class="nc" id="L339">                            anyTamperingDetected = true;</span>
                        }
                    }
<span class="nc" id="L342">                } catch (NumberFormatException e) {</span>
<span class="nc" id="L343">                    log.warn(&quot;Invalid audit log ID format: {}&quot;, auditLogIdStr);</span>
<span class="nc" id="L344">                } catch (Exception e) {</span>
<span class="nc" id="L345">                    log.warn(&quot;Failed to verify audit log {}: {}&quot;, auditLogIdStr, e.getMessage());</span>
<span class="nc" id="L346">                }</span>
<span class="nc" id="L347">            }</span>

<span class="nc bnc" id="L349" title="All 2 branches missed.">            return BulkAuditVerificationResponse.builder()</span>
<span class="nc" id="L350">                .success(verifiedCount &gt; 0)</span>
<span class="nc" id="L351">                .verifiedCount(verifiedCount)</span>
<span class="nc" id="L352">                .tamperingDetected(anyTamperingDetected)</span>
<span class="nc" id="L353">                .message(String.format(&quot;Verified %d out of %d audit logs. %s&quot;,</span>
<span class="nc" id="L354">                    verifiedCount, auditLogIds.size(),</span>
<span class="nc bnc" id="L355" title="All 2 branches missed.">                    anyTamperingDetected ? &quot;Tampering detected in some logs.&quot; : &quot;All verified logs are clean.&quot;))</span>
<span class="nc" id="L356">                .verificationResults(verificationResults)</span>
<span class="nc" id="L357">                .build();</span>

<span class="nc" id="L359">        } catch (Exception e) {</span>
<span class="nc" id="L360">            log.error(&quot;Error bulk verifying audit logs&quot;, e);</span>
<span class="nc" id="L361">            throw new RuntimeException(&quot;Failed to bulk verify audit logs: &quot; + e.getMessage());</span>
        }
    }

    // ===== HELPER METHODS =====

    /**
     * Map database verification status to GraphQL enum values
     */
    private String mapVerificationStatusToGraphQL(String dbStatus, boolean tamperingDetected) {
<span class="nc bnc" id="L371" title="All 2 branches missed.">        if (tamperingDetected) {</span>
<span class="nc" id="L372">            return &quot;TAMPERED&quot;;</span>
        }

<span class="nc bnc" id="L375" title="All 7 branches missed.">        return switch (dbStatus != null ? dbStatus.toUpperCase() : &quot;UNKNOWN&quot;) {</span>
<span class="nc" id="L376">            case &quot;VERIFIED&quot; -&gt; &quot;VERIFIED&quot;;</span>
<span class="nc bnc" id="L377" title="All 2 branches missed.">            case &quot;FAILED&quot; -&gt; tamperingDetected ? &quot;TAMPERED&quot; : &quot;CORRUPTED&quot;;</span>
<span class="nc" id="L378">            case &quot;PENDING&quot; -&gt; &quot;PENDING_VERIFICATION&quot;;</span>
<span class="nc" id="L379">            case &quot;MISSING&quot; -&gt; &quot;MISSING&quot;;</span>
<span class="nc" id="L380">            default -&gt; &quot;CORRUPTED&quot;; // fallback for unknown statuses</span>
        };
    }

    private Pageable createPageable(AuditPaginationInput pagination) {
<span class="nc bnc" id="L385" title="All 2 branches missed.">        if (pagination == null) {</span>
<span class="nc" id="L386">            return PageRequest.of(0, 20, Sort.by(Sort.Direction.DESC, &quot;timestamp&quot;));</span>
        }
        
<span class="nc bnc" id="L389" title="All 2 branches missed.">        Sort.Direction direction = &quot;ASC&quot;.equalsIgnoreCase(pagination.getSortDirection()) </span>
<span class="nc" id="L390">            ? Sort.Direction.ASC </span>
<span class="nc" id="L391">            : Sort.Direction.DESC;</span>
        
<span class="nc" id="L393">        return PageRequest.of(</span>
<span class="nc" id="L394">            pagination.getPage(),</span>
<span class="nc" id="L395">            pagination.getSize(),</span>
<span class="nc" id="L396">            Sort.by(direction, pagination.getSortBy())</span>
        );
    }

    // ===== INPUT CLASSES =====

<span class="nc" id="L402">    public static class AuditLogFilterInput {</span>
        private Long documentId;
        private String userId;
        private AuditAction action;
        private OffsetDateTime dateFrom;
        private OffsetDateTime dateTo;
        private String ipAddress;
        private String correlationId;
        private String sessionId;
        private String entityType;
        private String entityId;
        private Boolean success;
        private String searchTerm;

        // Getters and setters
<span class="nc" id="L417">        public Long getDocumentId() { return documentId; }</span>
<span class="nc" id="L418">        public void setDocumentId(Long documentId) { this.documentId = documentId; }</span>
<span class="nc" id="L419">        public String getUserId() { return userId; }</span>
<span class="nc" id="L420">        public void setUserId(String userId) { this.userId = userId; }</span>
<span class="nc" id="L421">        public AuditAction getAction() { return action; }</span>
<span class="nc" id="L422">        public void setAction(AuditAction action) { this.action = action; }</span>
<span class="nc" id="L423">        public OffsetDateTime getDateFrom() { return dateFrom; }</span>
<span class="nc" id="L424">        public void setDateFrom(OffsetDateTime dateFrom) { this.dateFrom = dateFrom; }</span>
<span class="nc" id="L425">        public OffsetDateTime getDateTo() { return dateTo; }</span>
<span class="nc" id="L426">        public void setDateTo(OffsetDateTime dateTo) { this.dateTo = dateTo; }</span>
<span class="nc" id="L427">        public String getIpAddress() { return ipAddress; }</span>
<span class="nc" id="L428">        public void setIpAddress(String ipAddress) { this.ipAddress = ipAddress; }</span>
<span class="nc" id="L429">        public String getCorrelationId() { return correlationId; }</span>
<span class="nc" id="L430">        public void setCorrelationId(String correlationId) { this.correlationId = correlationId; }</span>
<span class="nc" id="L431">        public String getSessionId() { return sessionId; }</span>
<span class="nc" id="L432">        public void setSessionId(String sessionId) { this.sessionId = sessionId; }</span>
<span class="nc" id="L433">        public String getEntityType() { return entityType; }</span>
<span class="nc" id="L434">        public void setEntityType(String entityType) { this.entityType = entityType; }</span>
<span class="nc" id="L435">        public String getEntityId() { return entityId; }</span>
<span class="nc" id="L436">        public void setEntityId(String entityId) { this.entityId = entityId; }</span>
<span class="nc" id="L437">        public Boolean getSuccess() { return success; }</span>
<span class="nc" id="L438">        public void setSuccess(Boolean success) { this.success = success; }</span>
<span class="nc" id="L439">        public String getSearchTerm() { return searchTerm; }</span>
<span class="nc" id="L440">        public void setSearchTerm(String searchTerm) { this.searchTerm = searchTerm; }</span>
    }

<span class="nc" id="L443">    public static class ComplianceAuditFilterInput {</span>
        private String complianceStandard;
        private String auditRelevance;
        private String controlId;
        private OffsetDateTime dateFrom;
        private OffsetDateTime dateTo;
        private String userId;
        private Long documentId;
        private String severity;
        private String status;

        // Getters and setters
<span class="nc" id="L455">        public String getComplianceStandard() { return complianceStandard; }</span>
<span class="nc" id="L456">        public void setComplianceStandard(String complianceStandard) { this.complianceStandard = complianceStandard; }</span>
<span class="nc" id="L457">        public String getAuditRelevance() { return auditRelevance; }</span>
<span class="nc" id="L458">        public void setAuditRelevance(String auditRelevance) { this.auditRelevance = auditRelevance; }</span>
<span class="nc" id="L459">        public String getControlId() { return controlId; }</span>
<span class="nc" id="L460">        public void setControlId(String controlId) { this.controlId = controlId; }</span>
<span class="nc" id="L461">        public OffsetDateTime getDateFrom() { return dateFrom; }</span>
<span class="nc" id="L462">        public void setDateFrom(OffsetDateTime dateFrom) { this.dateFrom = dateFrom; }</span>
<span class="nc" id="L463">        public OffsetDateTime getDateTo() { return dateTo; }</span>
<span class="nc" id="L464">        public void setDateTo(OffsetDateTime dateTo) { this.dateTo = dateTo; }</span>
<span class="nc" id="L465">        public String getUserId() { return userId; }</span>
<span class="nc" id="L466">        public void setUserId(String userId) { this.userId = userId; }</span>
<span class="nc" id="L467">        public Long getDocumentId() { return documentId; }</span>
<span class="nc" id="L468">        public void setDocumentId(Long documentId) { this.documentId = documentId; }</span>
<span class="nc" id="L469">        public String getSeverity() { return severity; }</span>
<span class="nc" id="L470">        public void setSeverity(String severity) { this.severity = severity; }</span>
<span class="nc" id="L471">        public String getStatus() { return status; }</span>
<span class="nc" id="L472">        public void setStatus(String status) { this.status = status; }</span>
    }

<span class="nc" id="L475">    public static class AuditPaginationInput {</span>
<span class="nc" id="L476">        private Integer page = 0;</span>
<span class="nc" id="L477">        private Integer size = 20;</span>
<span class="nc" id="L478">        private String sortBy = &quot;timestamp&quot;;</span>
<span class="nc" id="L479">        private String sortDirection = &quot;DESC&quot;;</span>

        // Getters and setters
<span class="nc" id="L482">        public Integer getPage() { return page; }</span>
<span class="nc" id="L483">        public void setPage(Integer page) { this.page = page; }</span>
<span class="nc" id="L484">        public Integer getSize() { return size; }</span>
<span class="nc" id="L485">        public void setSize(Integer size) { this.size = size; }</span>
<span class="nc" id="L486">        public String getSortBy() { return sortBy; }</span>
<span class="nc" id="L487">        public void setSortBy(String sortBy) { this.sortBy = sortBy; }</span>
<span class="nc" id="L488">        public String getSortDirection() { return sortDirection; }</span>
<span class="nc" id="L489">        public void setSortDirection(String sortDirection) { this.sortDirection = sortDirection; }</span>
    }

    // ===== HELPER METHODS =====

    /**
     * Build audit statistics for the given date range
     */
    private AuditStatistics buildAuditStatistics(LocalDateTime dateFrom, LocalDateTime dateTo) {
        try {
            // Calculate basic counts
<span class="nc" id="L500">            Long totalRecords = auditLogRepository.count();</span>
<span class="nc" id="L501">            Long recordsToday = countRecordsForPeriod(LocalDateTime.now().truncatedTo(ChronoUnit.DAYS), LocalDateTime.now());</span>
<span class="nc" id="L502">            Long recordsThisWeek = countRecordsForPeriod(LocalDateTime.now().minusWeeks(1), LocalDateTime.now());</span>
<span class="nc" id="L503">            Long recordsThisMonth = countRecordsForPeriod(LocalDateTime.now().minusMonths(1), LocalDateTime.now());</span>

            // Get action statistics
<span class="nc" id="L506">            List&lt;ActionCount&gt; actionStatistics = buildActionStatistics(dateFrom, dateTo);</span>

            // Get user statistics (top 10 most active users)
<span class="nc" id="L509">            List&lt;UserActivityCount&gt; userStatistics = buildUserStatistics(dateFrom, dateTo);</span>

            // Get hourly activity (last 24 hours)
<span class="nc" id="L512">            List&lt;HourlyActivityCount&gt; hourlyActivity = buildHourlyActivity();</span>

            // Get top documents (top 10 most accessed)
<span class="nc" id="L515">            List&lt;DocumentActivityCount&gt; topDocuments = buildTopDocuments(dateFrom, dateTo);</span>

            // Count security and compliance events
<span class="nc" id="L518">            Long securityEvents = countSecurityEvents(dateFrom, dateTo);</span>
<span class="nc" id="L519">            Long complianceEvents = countComplianceEvents(dateFrom, dateTo);</span>

<span class="nc" id="L521">            return AuditStatistics.builder()</span>
<span class="nc" id="L522">                    .totalRecords(totalRecords)</span>
<span class="nc" id="L523">                    .recordsToday(recordsToday)</span>
<span class="nc" id="L524">                    .recordsThisWeek(recordsThisWeek)</span>
<span class="nc" id="L525">                    .recordsThisMonth(recordsThisMonth)</span>
<span class="nc" id="L526">                    .actionStatistics(actionStatistics)</span>
<span class="nc" id="L527">                    .userStatistics(userStatistics)</span>
<span class="nc" id="L528">                    .hourlyActivity(hourlyActivity)</span>
<span class="nc" id="L529">                    .topDocuments(topDocuments)</span>
<span class="nc" id="L530">                    .securityEvents(securityEvents)</span>
<span class="nc" id="L531">                    .complianceEvents(complianceEvents)</span>
<span class="nc" id="L532">                    .build();</span>

<span class="nc" id="L534">        } catch (Exception e) {</span>
<span class="nc" id="L535">            log.error(&quot;Error building audit statistics&quot;, e);</span>
            // Return empty statistics on error
<span class="nc" id="L537">            return AuditStatistics.builder()</span>
<span class="nc" id="L538">                    .totalRecords(0L)</span>
<span class="nc" id="L539">                    .recordsToday(0L)</span>
<span class="nc" id="L540">                    .recordsThisWeek(0L)</span>
<span class="nc" id="L541">                    .recordsThisMonth(0L)</span>
<span class="nc" id="L542">                    .actionStatistics(Collections.emptyList())</span>
<span class="nc" id="L543">                    .userStatistics(Collections.emptyList())</span>
<span class="nc" id="L544">                    .hourlyActivity(Collections.emptyList())</span>
<span class="nc" id="L545">                    .topDocuments(Collections.emptyList())</span>
<span class="nc" id="L546">                    .securityEvents(0L)</span>
<span class="nc" id="L547">                    .complianceEvents(0L)</span>
<span class="nc" id="L548">                    .build();</span>
        }
    }

    private Long countRecordsForPeriod(LocalDateTime from, LocalDateTime to) {
        try {
<span class="nc" id="L554">            OffsetDateTime offsetFrom = from.atOffset(java.time.ZoneOffset.UTC);</span>
<span class="nc" id="L555">            OffsetDateTime offsetTo = to.atOffset(java.time.ZoneOffset.UTC);</span>
<span class="nc" id="L556">            List&lt;AuditLog&gt; logs = auditLogRepository.findByTimestampBetween(offsetFrom, offsetTo);</span>
<span class="nc" id="L557">            return (long) logs.size();</span>
<span class="nc" id="L558">        } catch (Exception e) {</span>
<span class="nc" id="L559">            log.warn(&quot;Failed to count records for period {} to {}&quot;, from, to, e);</span>
<span class="nc" id="L560">            return 0L;</span>
        }
    }

    private List&lt;ActionCount&gt; buildActionStatistics(LocalDateTime dateFrom, LocalDateTime dateTo) {
        try {
            // Get all logs in the date range and group by action
<span class="nc" id="L567">            OffsetDateTime offsetFrom = dateFrom.atOffset(java.time.ZoneOffset.UTC);</span>
<span class="nc" id="L568">            OffsetDateTime offsetTo = dateTo.atOffset(java.time.ZoneOffset.UTC);</span>
<span class="nc" id="L569">            List&lt;AuditLog&gt; logs = auditLogRepository.findByTimestampBetween(offsetFrom, offsetTo);</span>
<span class="nc" id="L570">            Map&lt;AuditAction, Long&gt; actionCounts = logs.stream()</span>
<span class="nc" id="L571">                    .collect(Collectors.groupingBy(AuditLog::getAction, Collectors.counting()));</span>

<span class="nc" id="L573">            Long totalActions = (long) logs.size();</span>

<span class="nc" id="L575">            return actionCounts.entrySet().stream()</span>
<span class="nc" id="L576">                    .map(entry -&gt; {</span>
<span class="nc" id="L577">                        AuditAction action = entry.getKey();</span>
<span class="nc" id="L578">                        Long count = entry.getValue();</span>
<span class="nc bnc" id="L579" title="All 2 branches missed.">                        Float percentage = totalActions &gt; 0 ? (count.floatValue() / totalActions.floatValue()) * 100 : 0f;</span>

<span class="nc" id="L581">                        return ActionCount.builder()</span>
<span class="nc" id="L582">                                .action(action)</span>
<span class="nc" id="L583">                                .count(count)</span>
<span class="nc" id="L584">                                .percentage(percentage)</span>
<span class="nc" id="L585">                                .build();</span>
                    })
<span class="nc" id="L587">                    .sorted((a, b) -&gt; Long.compare(b.getCount(), a.getCount())) // Sort by count descending</span>
<span class="nc" id="L588">                    .collect(Collectors.toList());</span>
<span class="nc" id="L589">        } catch (Exception e) {</span>
<span class="nc" id="L590">            log.warn(&quot;Failed to build action statistics&quot;, e);</span>
<span class="nc" id="L591">            return Collections.emptyList();</span>
        }
    }

    private List&lt;UserActivityCount&gt; buildUserStatistics(LocalDateTime dateFrom, LocalDateTime dateTo) {
        try {
            // Get all logs in the date range and group by user
<span class="nc" id="L598">            OffsetDateTime offsetFrom = dateFrom.atOffset(java.time.ZoneOffset.UTC);</span>
<span class="nc" id="L599">            OffsetDateTime offsetTo = dateTo.atOffset(java.time.ZoneOffset.UTC);</span>
<span class="nc" id="L600">            List&lt;AuditLog&gt; logs = auditLogRepository.findByTimestampBetween(offsetFrom, offsetTo);</span>
<span class="nc" id="L601">            Map&lt;String, List&lt;AuditLog&gt;&gt; userLogs = logs.stream()</span>
<span class="nc" id="L602">                    .collect(Collectors.groupingBy(AuditLog::getUserId));</span>

<span class="nc" id="L604">            return userLogs.entrySet().stream()</span>
<span class="nc" id="L605">                    .map(entry -&gt; {</span>
<span class="nc" id="L606">                        String userId = entry.getKey();</span>
<span class="nc" id="L607">                        List&lt;AuditLog&gt; userAuditLogs = entry.getValue();</span>
<span class="nc" id="L608">                        Long actionCount = (long) userAuditLogs.size();</span>
<span class="nc" id="L609">                        LocalDateTime lastActivity = userAuditLogs.stream()</span>
<span class="nc" id="L610">                                .map(AuditLog::getTimestamp)</span>
<span class="nc" id="L611">                                .max(OffsetDateTime::compareTo)</span>
<span class="nc" id="L612">                                .map(OffsetDateTime::toLocalDateTime)</span>
<span class="nc" id="L613">                                .orElse(null);</span>

<span class="nc" id="L615">                        return UserActivityCount.builder()</span>
<span class="nc" id="L616">                                .userId(userId)</span>
<span class="nc" id="L617">                                .actionCount(actionCount)</span>
<span class="nc" id="L618">                                .lastActivity(lastActivity)</span>
<span class="nc" id="L619">                                .riskScore(calculateRiskScore(actionCount))</span>
<span class="nc" id="L620">                                .build();</span>
                    })
<span class="nc" id="L622">                    .sorted((a, b) -&gt; Long.compare(b.getActionCount(), a.getActionCount())) // Sort by activity descending</span>
<span class="nc" id="L623">                    .limit(10) // Top 10 users</span>
<span class="nc" id="L624">                    .collect(Collectors.toList());</span>
<span class="nc" id="L625">        } catch (Exception e) {</span>
<span class="nc" id="L626">            log.warn(&quot;Failed to build user statistics&quot;, e);</span>
<span class="nc" id="L627">            return Collections.emptyList();</span>
        }
    }

    private List&lt;HourlyActivityCount&gt; buildHourlyActivity() {
        try {
<span class="nc" id="L633">            LocalDateTime now = LocalDateTime.now();</span>
<span class="nc" id="L634">            LocalDateTime yesterday = now.minusHours(24);</span>
<span class="nc" id="L635">            OffsetDateTime offsetYesterday = yesterday.atOffset(java.time.ZoneOffset.UTC);</span>
<span class="nc" id="L636">            OffsetDateTime offsetNow = now.atOffset(java.time.ZoneOffset.UTC);</span>
<span class="nc" id="L637">            List&lt;AuditLog&gt; logs = auditLogRepository.findByTimestampBetween(offsetYesterday, offsetNow);</span>

<span class="nc" id="L639">            Map&lt;Integer, Long&gt; hourlyCount = logs.stream()</span>
<span class="nc" id="L640">                    .collect(Collectors.groupingBy(</span>
<span class="nc" id="L641">                            log -&gt; log.getTimestamp().getHour(),</span>
<span class="nc" id="L642">                            Collectors.counting()</span>
                    ));

<span class="nc" id="L645">            List&lt;HourlyActivityCount&gt; result = new ArrayList&lt;&gt;();</span>
<span class="nc bnc" id="L646" title="All 2 branches missed.">            for (int hour = 0; hour &lt; 24; hour++) {</span>
<span class="nc" id="L647">                result.add(HourlyActivityCount.builder()</span>
<span class="nc" id="L648">                        .hour(hour)</span>
<span class="nc" id="L649">                        .count(hourlyCount.getOrDefault(hour, 0L))</span>
<span class="nc" id="L650">                        .date(now.withHour(hour).truncatedTo(ChronoUnit.HOURS))</span>
<span class="nc" id="L651">                        .build());</span>
            }

<span class="nc" id="L654">            return result;</span>
<span class="nc" id="L655">        } catch (Exception e) {</span>
<span class="nc" id="L656">            log.warn(&quot;Failed to build hourly activity&quot;, e);</span>
<span class="nc" id="L657">            return Collections.emptyList();</span>
        }
    }

    private List&lt;DocumentActivityCount&gt; buildTopDocuments(LocalDateTime dateFrom, LocalDateTime dateTo) {
        try {
<span class="nc" id="L663">            OffsetDateTime offsetFrom = dateFrom.atOffset(java.time.ZoneOffset.UTC);</span>
<span class="nc" id="L664">            OffsetDateTime offsetTo = dateTo.atOffset(java.time.ZoneOffset.UTC);</span>
<span class="nc" id="L665">            List&lt;AuditLog&gt; logs = auditLogRepository.findByTimestampBetween(offsetFrom, offsetTo);</span>
<span class="nc" id="L666">            Map&lt;Long, List&lt;AuditLog&gt;&gt; documentLogs = logs.stream()</span>
<span class="nc bnc" id="L667" title="All 2 branches missed.">                    .filter(log -&gt; log.getDocumentId() != null)</span>
<span class="nc" id="L668">                    .collect(Collectors.groupingBy(AuditLog::getDocumentId));</span>

<span class="nc" id="L670">            return documentLogs.entrySet().stream()</span>
<span class="nc" id="L671">                    .map(entry -&gt; {</span>
<span class="nc" id="L672">                        Long documentId = entry.getKey();</span>
<span class="nc" id="L673">                        List&lt;AuditLog&gt; docLogs = entry.getValue();</span>
<span class="nc" id="L674">                        Long activityCount = (long) docLogs.size();</span>
<span class="nc" id="L675">                        LocalDateTime lastActivity = docLogs.stream()</span>
<span class="nc" id="L676">                                .map(AuditLog::getTimestamp)</span>
<span class="nc" id="L677">                                .max(OffsetDateTime::compareTo)</span>
<span class="nc" id="L678">                                .map(OffsetDateTime::toLocalDateTime)</span>
<span class="nc" id="L679">                                .orElse(null);</span>

<span class="nc" id="L681">                        return DocumentActivityCount.builder()</span>
<span class="nc" id="L682">                                .documentId(documentId)</span>
<span class="nc" id="L683">                                .documentName(&quot;Document &quot; + documentId) // Could be enhanced to fetch actual name</span>
<span class="nc" id="L684">                                .activityCount(activityCount)</span>
<span class="nc" id="L685">                                .lastActivity(lastActivity)</span>
<span class="nc" id="L686">                                .build();</span>
                    })
<span class="nc" id="L688">                    .sorted((a, b) -&gt; Long.compare(b.getActivityCount(), a.getActivityCount()))</span>
<span class="nc" id="L689">                    .limit(10)</span>
<span class="nc" id="L690">                    .collect(Collectors.toList());</span>
<span class="nc" id="L691">        } catch (Exception e) {</span>
<span class="nc" id="L692">            log.warn(&quot;Failed to build top documents&quot;, e);</span>
<span class="nc" id="L693">            return Collections.emptyList();</span>
        }
    }

    private Long countSecurityEvents(LocalDateTime dateFrom, LocalDateTime dateTo) {
        try {
<span class="nc" id="L699">            OffsetDateTime offsetFrom = dateFrom.atOffset(java.time.ZoneOffset.UTC);</span>
<span class="nc" id="L700">            OffsetDateTime offsetTo = dateTo.atOffset(java.time.ZoneOffset.UTC);</span>
<span class="nc" id="L701">            List&lt;AuditLog&gt; logs = auditLogRepository.findByTimestampBetween(offsetFrom, offsetTo);</span>
<span class="nc" id="L702">            return logs.stream()</span>
<span class="nc bnc" id="L703" title="All 2 branches missed.">                    .filter(log -&gt; log.getAction() == AuditAction.SECURITY_VIOLATION ||</span>
<span class="nc bnc" id="L704" title="All 2 branches missed.">                                   log.getAction() == AuditAction.TOKEN_VALIDATION_FAILED ||</span>
<span class="nc bnc" id="L705" title="All 2 branches missed.">                                   log.getAction() == AuditAction.RATE_LIMIT_EXCEEDED)</span>
<span class="nc" id="L706">                    .count();</span>
<span class="nc" id="L707">        } catch (Exception e) {</span>
<span class="nc" id="L708">            log.warn(&quot;Failed to count security events&quot;, e);</span>
<span class="nc" id="L709">            return 0L;</span>
        }
    }

    private Long countComplianceEvents(LocalDateTime dateFrom, LocalDateTime dateTo) {
        try {
<span class="nc" id="L715">            OffsetDateTime offsetFrom = dateFrom.atOffset(java.time.ZoneOffset.UTC);</span>
<span class="nc" id="L716">            OffsetDateTime offsetTo = dateTo.atOffset(java.time.ZoneOffset.UTC);</span>
<span class="nc" id="L717">            List&lt;AuditLog&gt; logs = auditLogRepository.findByTimestampBetween(offsetFrom, offsetTo);</span>
<span class="nc" id="L718">            return logs.stream()</span>
<span class="nc bnc" id="L719" title="All 2 branches missed.">                    .filter(log -&gt; log.getAction() == AuditAction.COMPLIANCE_AUDIT_STARTED ||</span>
<span class="nc bnc" id="L720" title="All 2 branches missed.">                                   log.getAction() == AuditAction.COMPLIANCE_AUDIT_COMPLETED ||</span>
<span class="nc bnc" id="L721" title="All 2 branches missed.">                                   log.getAction() == AuditAction.DOCUMENT_COMPLIANCE_VALIDATED ||</span>
<span class="nc bnc" id="L722" title="All 2 branches missed.">                                   log.getAction() == AuditAction.DOCUMENT_COMPLIANCE_VIOLATION_DETECTED ||</span>
<span class="nc bnc" id="L723" title="All 2 branches missed.">                                   log.getAction() == AuditAction.LEGAL_HOLD_APPLIED ||</span>
<span class="nc bnc" id="L724" title="All 2 branches missed.">                                   log.getAction() == AuditAction.LEGAL_HOLD_RELEASED ||</span>
<span class="nc bnc" id="L725" title="All 2 branches missed.">                                   log.getAction() == AuditAction.RETENTION_POLICY_CREATED ||</span>
<span class="nc bnc" id="L726" title="All 2 branches missed.">                                   log.getAction() == AuditAction.RETENTION_POLICY_ASSIGNED)</span>
<span class="nc" id="L727">                    .count();</span>
<span class="nc" id="L728">        } catch (Exception e) {</span>
<span class="nc" id="L729">            log.warn(&quot;Failed to count compliance events&quot;, e);</span>
<span class="nc" id="L730">            return 0L;</span>
        }
    }

    private Float calculateRiskScore(Long actionCount) {
        // Simple risk score calculation based on activity level
<span class="nc bnc" id="L736" title="All 4 branches missed.">        if (actionCount == null || actionCount == 0) {</span>
<span class="nc" id="L737">            return 0.0f;</span>
        }

        // Risk increases with activity level
<span class="nc bnc" id="L741" title="All 2 branches missed.">        if (actionCount &gt; 1000) {</span>
<span class="nc" id="L742">            return 9.0f; // Very high risk</span>
<span class="nc bnc" id="L743" title="All 2 branches missed.">        } else if (actionCount &gt; 500) {</span>
<span class="nc" id="L744">            return 7.0f; // High risk</span>
<span class="nc bnc" id="L745" title="All 2 branches missed.">        } else if (actionCount &gt; 100) {</span>
<span class="nc" id="L746">            return 5.0f; // Medium risk</span>
<span class="nc bnc" id="L747" title="All 2 branches missed.">        } else if (actionCount &gt; 50) {</span>
<span class="nc" id="L748">            return 3.0f; // Low-medium risk</span>
        } else {
<span class="nc" id="L750">            return 1.0f; // Low risk</span>
        }
    }
}
</pre><div class="footer"><span class="right">Created with <a href="http://www.jacoco.org/jacoco">JaCoCo</a> 0.8.10.202304240956</span></div></body></html>