# Migration Guide: From Individual to Shared Infrastructure

This guide helps you migrate from the old individual service deployments to the new shared infrastructure setup.

## 🔄 What Changed

### Before (Old Setup)
- Each service had its own Docker Compose file with all dependencies
- DMS Service: Own MySQL, Redis, Elasticsearch, Prometheus, Grafana
- Notification Service: Own MySQL, Redis, RabbitMQ
- Port conflicts when running both services
- Resource duplication and waste

### After (New Setup)
- Shared infrastructure services (MySQL, Redis, RabbitMQ, etc.)
- Independent deployment capability maintained
- Environment-specific configurations
- Proper resource management and monitoring

## 📁 New File Structure

```
grc-platform-v4/
├── 🆕 docker-compose.shared.yml      # Shared infrastructure
├── 🆕 docker-compose.services.yml    # Apps using shared infra
├── 🆕 docker-compose.uat.yml         # UAT-specific config
├── 🆕 .env.development               # Development environment
├── 🆕 .env.uat                       # UAT environment
├── 🆕 deploy-dev.sh                  # Development deployment
├── 🆕 deploy-uat.sh                  # UAT deployment
├── 🆕 deploy-standalone.sh           # Flexible deployment
├── 🆕 backup-uat.sh                  # UAT backup script
├── 🆕 DOCKER_DEPLOYMENT_GUIDE.md     # Comprehensive guide
├── ✅ clean-docker.sh                # Updated cleanup script
├── ✅ .env.template                  # Updated template
├── ✅ .env.aws-ec2.template          # Existing AWS template
├── ✅ .env.autoresilience.com        # Existing domain config
├── ✅ .env.local-dev                 # Existing local dev
├── 📁 dms-svc/
│   ├── ✅ docker-compose.yml         # Updated for standalone
│   └── ✅ docker-compose.prod.yml    # Existing production
└── 📁 notification-svc/
    ├── ✅ docker-compose.yml         # Updated for standalone
    └── ✅ docker-compose.prod.yml    # Existing production
```

## 🚀 Migration Steps

### 1. Stop Current Services
```bash
# Stop any running services
./clean-docker.sh

# Or manually stop individual services
cd dms-svc && docker-compose down
cd ../notification-svc && docker-compose down
```

### 2. Choose Your Deployment Strategy

#### Option A: Development with Shared Infrastructure (Recommended)
```bash
# Use the new shared infrastructure setup
./deploy-dev.sh
```

#### Option B: Individual Service Deployment (Legacy Support)
```bash
# Deploy DMS service only
./deploy-standalone.sh --service dms --env development

# Deploy Notification service only
./deploy-standalone.sh --service notification --env development
```

#### Option C: UAT Deployment on AWS EC2
```bash
# Configure UAT environment
cp .env.uat .env.uat.local
# Edit .env.uat.local with your AWS credentials

# Deploy to UAT
./deploy-uat.sh
```

### 3. Update Your Environment Configuration

#### For Development
```bash
# Use the new development environment
cp .env.development .env
```

#### For UAT/Production
```bash
# Use the UAT environment template
cp .env.uat .env
# Edit with your specific values
```

### 4. Verify Migration

#### Check Services
```bash
# Check all services are running
docker ps

# Check service health
curl http://localhost:9093/actuator/health  # DMS
curl http://localhost:9091/actuator/health  # Notification
```

#### Access Monitoring
- Grafana: http://localhost:3000 (admin/admin)
- Prometheus: http://localhost:9090
- RabbitMQ: http://localhost:15672 (admin/admin123)

## 🔧 Configuration Changes

### Environment Variables
The new setup uses standardized environment variable names:

| Old Variable | New Variable | Notes |
|--------------|--------------|-------|
| `MYSQL_DMS_PASSWORD` | `DMS_DB_PASSWORD` | Clearer naming |
| `MYSQL_NOTIFICATION_PASSWORD` | `NOTIFICATION_DB_PASSWORD` | Clearer naming |
| `RABBITMQ_DEFAULT_USER` | `RABBITMQ_USER` | Simplified |
| `RABBITMQ_DEFAULT_PASS` | `RABBITMQ_PASSWORD` | Simplified |
| `GF_SECURITY_ADMIN_PASSWORD` | `GRAFANA_ADMIN_PASSWORD` | Standardized |

### Port Mappings
Default ports remain the same, but now configurable via environment variables:

| Service | Port | Environment Variable |
|---------|------|---------------------|
| DMS Service | 9093 | `DMS_PORT` |
| Notification Service | 9091 | `NOTIFICATION_PORT` |
| MySQL | 3306 | `MYSQL_PORT` |
| Redis | 6379 | `REDIS_PORT` |
| RabbitMQ | 5672 | `RABBITMQ_PORT` |
| Grafana | 3000 | `GRAFANA_PORT` |
| Prometheus | 9090 | `PROMETHEUS_PORT` |

### Network Configuration
- **Shared Network**: `grc-shared-network` (**********/16)
- **DMS Standalone**: `dms-network` (172.20.0.0/16)
- **Notification Standalone**: `notification-network`

## 🗂️ Deprecated Files

The following files are now legacy but maintained for backward compatibility:

### Still Supported (Legacy)
- `dms-svc/docker-compose.yml` - Standalone DMS deployment
- `notification-svc/docker-compose.yml` - Standalone Notification deployment
- `dms-svc/docker-compose.prod.yml` - DMS production config
- `notification-svc/docker-compose.prod.yml` - Notification production config

### Environment Files Status
- ✅ `.env.template` - Updated and maintained
- ✅ `.env.aws-ec2.template` - Maintained for AWS deployments
- ✅ `.env.autoresilience.com` - Maintained for domain deployments
- ✅ `.env.local-dev` - Maintained for local development
- 🆕 `.env.development` - New development environment
- 🆕 `.env.uat` - New UAT environment

## 🔍 Troubleshooting Migration Issues

### Port Conflicts
If you encounter port conflicts:
```bash
# Check what's using the ports
netstat -tulpn | grep :9093
netstat -tulpn | grep :9091

# Stop conflicting services
docker stop $(docker ps -q)
```

### Database Connection Issues
```bash
# Check if shared MySQL is running
docker exec grc-mysql-shared mysqladmin ping -h localhost -u root -proot_password

# Verify databases exist
docker exec grc-mysql-shared mysql -u root -proot_password -e "SHOW DATABASES;"
```

### Network Issues
```bash
# Recreate the shared network
docker network rm grc-shared-network
docker network create grc-shared-network --driver bridge --subnet=**********/16
```

### Service Dependencies
```bash
# Check service logs
docker-compose -f docker-compose.shared.yml -f docker-compose.services.yml logs -f

# Restart services in order
docker-compose -f docker-compose.shared.yml restart
docker-compose -f docker-compose.services.yml restart
```

## 📊 Benefits of Migration

### Resource Efficiency
- **Before**: 2 MySQL instances, 2 Redis instances
- **After**: 1 shared MySQL, 1 shared Redis
- **Savings**: ~50% reduction in memory usage

### Monitoring
- **Before**: Separate monitoring for each service
- **After**: Unified monitoring dashboard
- **Benefit**: Single pane of glass for all services

### Deployment Flexibility
- **Before**: All-or-nothing deployment
- **After**: Deploy services independently or together
- **Benefit**: Better development and testing workflow

### Environment Management
- **Before**: Manual configuration per service
- **After**: Environment-specific configuration files
- **Benefit**: Consistent deployments across environments

## 🆘 Rollback Plan

If you need to rollback to the old setup:

```bash
# Stop new setup
docker-compose -f docker-compose.shared.yml -f docker-compose.services.yml down

# Use old individual deployments
cd dms-svc
docker-compose up -d

cd ../notification-svc
docker-compose up -d
```

## 📞 Support

For migration issues:
1. Check the logs: `docker-compose logs [service-name]`
2. Verify environment variables are set correctly
3. Ensure Docker network exists and is accessible
4. Check port availability
5. Refer to `DOCKER_DEPLOYMENT_GUIDE.md` for detailed instructions

## 🎯 Next Steps

After successful migration:
1. Update your CI/CD pipelines to use new deployment scripts
2. Update documentation to reference new environment files
3. Train team members on new deployment procedures
4. Set up monitoring alerts for the shared infrastructure
5. Plan for production deployment using UAT environment as template