<?xml version="1.0" encoding="UTF-8"?><!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Strict//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-strict.dtd"><html xmlns="http://www.w3.org/1999/xhtml" lang="en"><head><meta http-equiv="Content-Type" content="text/html;charset=UTF-8"/><link rel="stylesheet" href="../jacoco-resources/report.css" type="text/css"/><link rel="shortcut icon" href="../jacoco-resources/report.gif" type="image/gif"/><title>DocumentPermissionResolver</title><script type="text/javascript" src="../jacoco-resources/sort.js"></script></head><body onload="initialSort(['breadcrumb'])"><div class="breadcrumb" id="breadcrumb"><span class="info"><a href="../jacoco-sessions.html" class="el_session">Sessions</a></span><a href="../index.html" class="el_report">dms-svc</a> &gt; <a href="index.html" class="el_package">com.ascentbusiness.dms_svc.resolver</a> &gt; <span class="el_class">DocumentPermissionResolver</span></div><h1>DocumentPermissionResolver</h1><table class="coverage" cellspacing="0" id="coveragetable"><thead><tr><td class="sortable" id="a" onclick="toggleSort(this)">Element</td><td class="down sortable bar" id="b" onclick="toggleSort(this)">Missed Instructions</td><td class="sortable ctr2" id="c" onclick="toggleSort(this)">Cov.</td><td class="sortable bar" id="d" onclick="toggleSort(this)">Missed Branches</td><td class="sortable ctr2" id="e" onclick="toggleSort(this)">Cov.</td><td class="sortable ctr1" id="f" onclick="toggleSort(this)">Missed</td><td class="sortable ctr2" id="g" onclick="toggleSort(this)">Cxty</td><td class="sortable ctr1" id="h" onclick="toggleSort(this)">Missed</td><td class="sortable ctr2" id="i" onclick="toggleSort(this)">Lines</td><td class="sortable ctr1" id="j" onclick="toggleSort(this)">Missed</td><td class="sortable ctr2" id="k" onclick="toggleSort(this)">Methods</td></tr></thead><tfoot><tr><td>Total</td><td class="bar">379 of 389</td><td class="ctr2">2%</td><td class="bar">18 of 18</td><td class="ctr2">0%</td><td class="ctr1">21</td><td class="ctr2">23</td><td class="ctr1">92</td><td class="ctr2">94</td><td class="ctr1">12</td><td class="ctr2">14</td></tr></tfoot><tbody><tr><td id="a7"><a href="DocumentPermissionResolver.java.html#L134" class="el_method">grantBulkDocumentPermissions(BulkDocumentPermissionInput)</a></td><td class="bar" id="b0"><img src="../jacoco-resources/redbar.gif" width="120" height="10" title="120" alt="120"/></td><td class="ctr2" id="c2">0%</td><td class="bar" id="d0"><img src="../jacoco-resources/redbar.gif" width="120" height="10" title="8" alt="8"/></td><td class="ctr2" id="e0">0%</td><td class="ctr1" id="f0">5</td><td class="ctr2" id="g0">5</td><td class="ctr1" id="h0">31</td><td class="ctr2" id="i0">31</td><td class="ctr1" id="j0">1</td><td class="ctr2" id="k0">1</td></tr><tr><td id="a8"><a href="DocumentPermissionResolver.java.html#L102" class="el_method">grantDocumentPermission(DocumentPermissionInput)</a></td><td class="bar" id="b1"><img src="../jacoco-resources/redbar.gif" width="54" height="10" title="54" alt="54"/></td><td class="ctr2" id="c3">0%</td><td class="bar" id="d4"/><td class="ctr2" id="e4">n/a</td><td class="ctr1" id="f4">1</td><td class="ctr2" id="g4">1</td><td class="ctr1" id="h1">14</td><td class="ctr2" id="i1">14</td><td class="ctr1" id="j1">1</td><td class="ctr2" id="k1">1</td></tr><tr><td id="a11"><a href="DocumentPermissionResolver.java.html#L192" class="el_method">revokeDocumentPermission(RevokePermissionInput)</a></td><td class="bar" id="b2"><img src="../jacoco-resources/redbar.gif" width="52" height="10" title="52" alt="52"/></td><td class="ctr2" id="c4">0%</td><td class="bar" id="d3"><img src="../jacoco-resources/redbar.gif" width="30" height="10" title="2" alt="2"/></td><td class="ctr2" id="e1">0%</td><td class="ctr1" id="f3">2</td><td class="ctr2" id="g3">2</td><td class="ctr1" id="h2">13</td><td class="ctr2" id="i2">13</td><td class="ctr1" id="j2">1</td><td class="ctr2" id="k2">1</td></tr><tr><td id="a13"><a href="DocumentPermissionResolver.java.html#L223" class="el_method">updateDocumentPermission(Long, DocumentPermissionInput)</a></td><td class="bar" id="b3"><img src="../jacoco-resources/redbar.gif" width="35" height="10" title="35" alt="35"/></td><td class="ctr2" id="c5">0%</td><td class="bar" id="d5"/><td class="ctr2" id="e5">n/a</td><td class="ctr1" id="f5">1</td><td class="ctr2" id="g5">1</td><td class="ctr1" id="h3">10</td><td class="ctr2" id="i3">10</td><td class="ctr1" id="j3">1</td><td class="ctr2" id="k3">1</td></tr><tr><td id="a1"><a href="DocumentPermissionResolver.java.html#L252" class="el_method">expireDocumentPermission(Long)</a></td><td class="bar" id="b4"><img src="../jacoco-resources/redbar.gif" width="25" height="10" title="25" alt="25"/></td><td class="ctr2" id="c6">0%</td><td class="bar" id="d6"/><td class="ctr2" id="e6">n/a</td><td class="ctr1" id="f6">1</td><td class="ctr2" id="g6">1</td><td class="ctr1" id="h4">6</td><td class="ctr2" id="i4">6</td><td class="ctr1" id="j4">1</td><td class="ctr2" id="k4">1</td></tr><tr><td id="a6"><a href="DocumentPermissionResolver.java.html#L68" class="el_method">getUserDocumentPermission(Long, String)</a></td><td class="bar" id="b5"><img src="../jacoco-resources/redbar.gif" width="20" height="10" title="20" alt="20"/></td><td class="ctr2" id="c7">0%</td><td class="bar" id="d7"/><td class="ctr2" id="e7">n/a</td><td class="ctr1" id="f7">1</td><td class="ctr2" id="g7">1</td><td class="ctr1" id="h5">6</td><td class="ctr2" id="i5">6</td><td class="ctr1" id="j5">1</td><td class="ctr2" id="k5">1</td></tr><tr><td id="a5"><a href="DocumentPermissionResolver.java.html#L82" class="el_method">getRoleDocumentPermission(Long, String)</a></td><td class="bar" id="b6"><img src="../jacoco-resources/redbar.gif" width="20" height="10" title="20" alt="20"/></td><td class="ctr2" id="c8">0%</td><td class="bar" id="d8"/><td class="ctr2" id="e8">n/a</td><td class="ctr1" id="f8">1</td><td class="ctr2" id="g8">1</td><td class="ctr1" id="h6">6</td><td class="ctr2" id="i6">6</td><td class="ctr1" id="j6">1</td><td class="ctr2" id="k6">1</td></tr><tr><td id="a9"><a href="DocumentPermissionResolver.java.html#L85" class="el_method">lambda$getRoleDocumentPermission$1(String, DocumentPermission)</a></td><td class="bar" id="b7"><img src="../jacoco-resources/redbar.gif" width="13" height="10" title="13" alt="13"/></td><td class="ctr2" id="c9">0%</td><td class="bar" id="d1"><img src="../jacoco-resources/redbar.gif" width="60" height="10" title="4" alt="4"/></td><td class="ctr2" id="e2">0%</td><td class="ctr1" id="f1">3</td><td class="ctr2" id="g1">3</td><td class="ctr1" id="h10">1</td><td class="ctr2" id="i10">1</td><td class="ctr1" id="j7">1</td><td class="ctr2" id="k7">1</td></tr><tr><td id="a10"><a href="DocumentPermissionResolver.java.html#L71" class="el_method">lambda$getUserDocumentPermission$0(String, DocumentPermission)</a></td><td class="bar" id="b8"><img src="../jacoco-resources/redbar.gif" width="13" height="10" title="13" alt="13"/></td><td class="ctr2" id="c10">0%</td><td class="bar" id="d2"><img src="../jacoco-resources/redbar.gif" width="60" height="10" title="4" alt="4"/></td><td class="ctr2" id="e3">0%</td><td class="ctr1" id="f2">3</td><td class="ctr2" id="g2">3</td><td class="ctr1" id="h11">1</td><td class="ctr2" id="i11">1</td><td class="ctr1" id="j8">1</td><td class="ctr2" id="k8">1</td></tr><tr><td id="a2"><a href="DocumentPermissionResolver.java.html#L38" class="el_method">getDocumentPermissions(Long)</a></td><td class="bar" id="b9"><img src="../jacoco-resources/redbar.gif" width="9" height="10" title="9" alt="9"/></td><td class="ctr2" id="c11">0%</td><td class="bar" id="d9"/><td class="ctr2" id="e9">n/a</td><td class="ctr1" id="f9">1</td><td class="ctr2" id="g9">1</td><td class="ctr1" id="h7">2</td><td class="ctr2" id="i7">2</td><td class="ctr1" id="j9">1</td><td class="ctr2" id="k9">1</td></tr><tr><td id="a4"><a href="DocumentPermissionResolver.java.html#L48" class="el_method">getDocumentPermissionsByUser(String)</a></td><td class="bar" id="b10"><img src="../jacoco-resources/redbar.gif" width="9" height="10" title="9" alt="9"/></td><td class="ctr2" id="c12">0%</td><td class="bar" id="d10"/><td class="ctr2" id="e10">n/a</td><td class="ctr1" id="f10">1</td><td class="ctr2" id="g10">1</td><td class="ctr1" id="h8">2</td><td class="ctr2" id="i8">2</td><td class="ctr1" id="j10">1</td><td class="ctr2" id="k10">1</td></tr><tr><td id="a3"><a href="DocumentPermissionResolver.java.html#L58" class="el_method">getDocumentPermissionsByRole(String)</a></td><td class="bar" id="b11"><img src="../jacoco-resources/redbar.gif" width="9" height="10" title="9" alt="9"/></td><td class="ctr2" id="c13">0%</td><td class="bar" id="d11"/><td class="ctr2" id="e11">n/a</td><td class="ctr1" id="f11">1</td><td class="ctr2" id="g11">1</td><td class="ctr1" id="h9">2</td><td class="ctr2" id="i9">2</td><td class="ctr1" id="j11">1</td><td class="ctr2" id="k11">1</td></tr><tr><td id="a0"><a href="DocumentPermissionResolver.java.html#L23" class="el_method">DocumentPermissionResolver(DocumentPermissionService)</a></td><td class="bar" id="b12"><img src="../jacoco-resources/greenbar.gif" width="6" height="10" title="6" alt="6"/></td><td class="ctr2" id="c0">100%</td><td class="bar" id="d12"/><td class="ctr2" id="e12">n/a</td><td class="ctr1" id="f12">0</td><td class="ctr2" id="g12">1</td><td class="ctr1" id="h12">0</td><td class="ctr2" id="i12">1</td><td class="ctr1" id="j12">0</td><td class="ctr2" id="k12">1</td></tr><tr><td id="a12"><a href="DocumentPermissionResolver.java.html#L26" class="el_method">static {...}</a></td><td class="bar" id="b13"><img src="../jacoco-resources/greenbar.gif" width="4" height="10" title="4" alt="4"/></td><td class="ctr2" id="c1">100%</td><td class="bar" id="d13"/><td class="ctr2" id="e13">n/a</td><td class="ctr1" id="f13">0</td><td class="ctr2" id="g13">1</td><td class="ctr1" id="h13">0</td><td class="ctr2" id="i13">1</td><td class="ctr1" id="j13">0</td><td class="ctr2" id="k13">1</td></tr></tbody></table><div class="footer"><span class="right">Created with <a href="http://www.jacoco.org/jacoco">JaCoCo</a> 0.8.10.202304240956</span></div></body></html>