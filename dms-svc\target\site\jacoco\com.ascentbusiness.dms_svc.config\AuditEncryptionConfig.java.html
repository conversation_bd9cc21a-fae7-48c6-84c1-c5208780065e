<?xml version="1.0" encoding="UTF-8"?><!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Strict//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-strict.dtd"><html xmlns="http://www.w3.org/1999/xhtml" lang="en"><head><meta http-equiv="Content-Type" content="text/html;charset=UTF-8"/><link rel="stylesheet" href="../jacoco-resources/report.css" type="text/css"/><link rel="shortcut icon" href="../jacoco-resources/report.gif" type="image/gif"/><title>AuditEncryptionConfig.java</title><link rel="stylesheet" href="../jacoco-resources/prettify.css" type="text/css"/><script type="text/javascript" src="../jacoco-resources/prettify.js"></script></head><body onload="window['PR_TAB_WIDTH']=4;prettyPrint()"><div class="breadcrumb" id="breadcrumb"><span class="info"><a href="../jacoco-sessions.html" class="el_session">Sessions</a></span><a href="../index.html" class="el_report">dms-svc</a> &gt; <a href="index.source.html" class="el_package">com.ascentbusiness.dms_svc.config</a> &gt; <span class="el_source">AuditEncryptionConfig.java</span></div><h1>AuditEncryptionConfig.java</h1><pre class="source lang-java linenums">package com.ascentbusiness.dms_svc.config;

import com.ascentbusiness.dms_svc.service.AuditEncryptionService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.context.event.ApplicationReadyEvent;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.event.EventListener;
import org.springframework.scheduling.annotation.Scheduled;

/**
 * Configuration for audit log encryption
 */
@Configuration
<span class="fc" id="L16">public class AuditEncryptionConfig {</span>
    
<span class="fc" id="L18">    private static final Logger logger = LoggerFactory.getLogger(AuditEncryptionConfig.class);</span>
    
    @Autowired
    private AuditEncryptionService auditEncryptionService;
    
    /**
     * Initialize audit encryption service when application is ready
     */
    @EventListener(ApplicationReadyEvent.class)
    public void initializeAuditEncryption() {
        try {
<span class="fc" id="L29">            auditEncryptionService.initialize();</span>
<span class="fc" id="L30">            logger.info(&quot;Audit encryption service initialized successfully&quot;);</span>
<span class="nc" id="L31">        } catch (Exception e) {</span>
<span class="nc" id="L32">            logger.error(&quot;Failed to initialize audit encryption service&quot;, e);</span>
<span class="fc" id="L33">        }</span>
<span class="fc" id="L34">    }</span>
    
    /**
     * Scheduled task to clean up old encryption keys
     * Runs daily at 2 AM
     */
    @Scheduled(cron = &quot;0 0 2 * * ?&quot;)
    public void cleanupOldEncryptionKeys() {
        try {
            // Keep keys for 30 days for decryption purposes
<span class="nc" id="L44">            long thirtyDaysInMillis = 30L * 24L * 60L * 60L * 1000L;</span>
<span class="nc" id="L45">            auditEncryptionService.clearOldKeys(thirtyDaysInMillis);</span>
            
<span class="nc" id="L47">            logger.info(&quot;Completed cleanup of old audit encryption keys&quot;);</span>
<span class="nc" id="L48">        } catch (Exception e) {</span>
<span class="nc" id="L49">            logger.error(&quot;Failed to cleanup old audit encryption keys&quot;, e);</span>
<span class="nc" id="L50">        }</span>
<span class="nc" id="L51">    }</span>
}
</pre><div class="footer"><span class="right">Created with <a href="http://www.jacoco.org/jacoco">JaCoCo</a> 0.8.10.202304240956</span></div></body></html>