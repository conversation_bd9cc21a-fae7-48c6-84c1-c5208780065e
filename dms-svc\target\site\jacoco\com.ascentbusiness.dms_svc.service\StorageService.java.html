<?xml version="1.0" encoding="UTF-8"?><!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Strict//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-strict.dtd"><html xmlns="http://www.w3.org/1999/xhtml" lang="en"><head><meta http-equiv="Content-Type" content="text/html;charset=UTF-8"/><link rel="stylesheet" href="../jacoco-resources/report.css" type="text/css"/><link rel="shortcut icon" href="../jacoco-resources/report.gif" type="image/gif"/><title>StorageService.java</title><link rel="stylesheet" href="../jacoco-resources/prettify.css" type="text/css"/><script type="text/javascript" src="../jacoco-resources/prettify.js"></script></head><body onload="window['PR_TAB_WIDTH']=4;prettyPrint()"><div class="breadcrumb" id="breadcrumb"><span class="info"><a href="../jacoco-sessions.html" class="el_session">Sessions</a></span><a href="../index.html" class="el_report">dms-svc</a> &gt; <a href="index.source.html" class="el_package">com.ascentbusiness.dms_svc.service</a> &gt; <span class="el_source">StorageService.java</span></div><h1>StorageService.java</h1><pre class="source lang-java linenums">package com.ascentbusiness.dms_svc.service;

import com.ascentbusiness.dms_svc.config.StorageConfigurationProperties;
import com.ascentbusiness.dms_svc.entity.Document;
import com.ascentbusiness.dms_svc.enums.StorageProvider;
import lombok.RequiredArgsConstructor;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.web.multipart.MultipartFile;

import java.io.IOException;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;

/**
 * Service class for multi-provider document storage management in the DMS system.
 *
 * &lt;p&gt;This service provides a unified interface for document storage across multiple
 * storage providers including local filesystem, AWS S3, and SharePoint. It handles
 * automatic fallback mechanisms, organized file structure, and provider-specific
 * optimizations for different storage backends.&lt;/p&gt;
 *
 * &lt;p&gt;Key features:&lt;/p&gt;
 * &lt;ul&gt;
 *   &lt;li&gt;&lt;strong&gt;Multi-Provider Support&lt;/strong&gt;: Local, S3, and SharePoint storage backends&lt;/li&gt;
 *   &lt;li&gt;&lt;strong&gt;Automatic Fallback&lt;/strong&gt;: Graceful degradation when providers are unavailable&lt;/li&gt;
 *   &lt;li&gt;&lt;strong&gt;Organized Structure&lt;/strong&gt;: Year/month-based file organization&lt;/li&gt;
 *   &lt;li&gt;&lt;strong&gt;Path Management&lt;/strong&gt;: Consistent path handling across providers&lt;/li&gt;
 *   &lt;li&gt;&lt;strong&gt;File Operations&lt;/strong&gt;: Upload, download, delete, and metadata operations&lt;/li&gt;
 *   &lt;li&gt;&lt;strong&gt;Error Handling&lt;/strong&gt;: Comprehensive error handling and logging&lt;/li&gt;
 * &lt;/ul&gt;
 *
 * &lt;p&gt;Supported storage providers:&lt;/p&gt;
 * &lt;ul&gt;
 *   &lt;li&gt;&lt;strong&gt;LOCAL&lt;/strong&gt;: Local filesystem storage with configurable base directory&lt;/li&gt;
 *   &lt;li&gt;&lt;strong&gt;S3&lt;/strong&gt;: Amazon S3 cloud storage with bucket management&lt;/li&gt;
 *   &lt;li&gt;&lt;strong&gt;SHAREPOINT&lt;/strong&gt;: Microsoft SharePoint document libraries&lt;/li&gt;
 * &lt;/ul&gt;
 *
 * &lt;p&gt;The service automatically organizes files in a year/month structure for better
 * organization and performance. It provides fallback to local storage when cloud
 * providers are unavailable, ensuring system reliability.&lt;/p&gt;
 *
 * <AUTHOR> Development Team
 * @version 1.0.0
 * @since 1.0.0
 * @see StorageProvider
 * @see S3StorageService
 * @see SharePointStorageService
 */
@Service
<span class="fc" id="L57">@RequiredArgsConstructor</span>
public class StorageService {

<span class="fc" id="L60">    private static final Logger logger = LoggerFactory.getLogger(StorageService.class);</span>

    private final StorageConfigurationProperties storageConfig;
    
    @Autowired(required = false)
    private S3StorageService s3StorageService;
    
    @Autowired(required = false)
    private SharePointStorageService sharePointStorageService;

    /**
     * Stores a file using the specified storage provider with automatic fallback.
     *
     * &lt;p&gt;This method provides a unified interface for storing files across different
     * storage providers. It automatically handles provider-specific logic and falls
     * back to local storage when cloud providers are unavailable.&lt;/p&gt;
     *
     * @param file the multipart file to store
     * @param storageProvider the target storage provider (LOCAL, S3, SHAREPOINT)
     * @return the storage path where the file was stored
     * @throws IOException if file storage fails on all available providers
     */
    public String storeFile(MultipartFile file, StorageProvider storageProvider) throws IOException {
<span class="nc bnc" id="L83" title="All 4 branches missed.">        switch (storageProvider) {</span>
            case LOCAL:
<span class="nc" id="L85">                return storeFileLocally(file);</span>
            case S3:
<span class="nc bnc" id="L87" title="All 2 branches missed.">                if (s3StorageService != null) {</span>
<span class="nc" id="L88">                    return s3StorageService.storeFile(file);</span>
                } else {
<span class="nc" id="L90">                    logger.warn(&quot;S3 storage service not available, falling back to local storage&quot;);</span>
<span class="nc" id="L91">                    return storeFileLocally(file);</span>
                }
            case SHAREPOINT:
<span class="nc bnc" id="L94" title="All 2 branches missed.">                if (sharePointStorageService != null) {</span>
<span class="nc" id="L95">                    return sharePointStorageService.storeFile(file);</span>
                } else {
<span class="nc" id="L97">                    logger.warn(&quot;SharePoint storage service not available, falling back to local storage&quot;);</span>
<span class="nc" id="L98">                    return storeFileLocally(file);</span>
                }
            default:
<span class="nc" id="L101">                throw new IllegalArgumentException(&quot;Unsupported storage provider: &quot; + storageProvider);</span>
        }
    }

    public String storeFileVersion(MultipartFile file, Document originalDocument) throws IOException {
        // Store new version in the same directory as original
<span class="nc" id="L107">        String originalPath = originalDocument.getStoragePath();</span>
<span class="nc" id="L108">        Path originalDir = Paths.get(originalPath).getParent();</span>
        
<span class="nc" id="L110">        String versionFileName = generateVersionFileName(file.getOriginalFilename(), getNextVersionNumber(originalDocument));</span>
<span class="nc" id="L111">        Path versionPath = originalDir.resolve(versionFileName);</span>
        
<span class="nc" id="L113">        Files.createDirectories(versionPath.getParent());</span>
<span class="nc" id="L114">        Files.copy(file.getInputStream(), versionPath);</span>
        
<span class="nc" id="L116">        logger.info(&quot;File version stored successfully: {}&quot;, versionPath.toString());</span>
<span class="nc" id="L117">        return versionPath.toString();</span>
    }

    public String storeFileFromSourcePath(String sourceFilePath, StorageProvider storageProvider) throws IOException {
<span class="nc bnc" id="L121" title="All 4 branches missed.">        switch (storageProvider) {</span>
            case LOCAL:
<span class="nc" id="L123">                return storeFileFromSourcePathLocally(sourceFilePath);</span>
            case S3:
<span class="nc bnc" id="L125" title="All 2 branches missed.">                if (s3StorageService != null) {</span>
<span class="nc" id="L126">                    return s3StorageService.storeFileFromPath(sourceFilePath);</span>
                } else {
<span class="nc" id="L128">                    logger.warn(&quot;S3 storage service not available, falling back to local storage&quot;);</span>
<span class="nc" id="L129">                    return storeFileFromSourcePathLocally(sourceFilePath);</span>
                }
            case SHAREPOINT:
<span class="nc bnc" id="L132" title="All 2 branches missed.">                if (sharePointStorageService != null) {</span>
<span class="nc" id="L133">                    return sharePointStorageService.storeFileFromPath(sourceFilePath);</span>
                } else {
<span class="nc" id="L135">                    logger.warn(&quot;SharePoint storage service not available, falling back to local storage&quot;);</span>
<span class="nc" id="L136">                    return storeFileFromSourcePathLocally(sourceFilePath);</span>
                }
            default:
<span class="nc" id="L139">                throw new IllegalArgumentException(&quot;Unsupported storage provider: &quot; + storageProvider);</span>
        }
    }

    /**
     * Downloads a file from the specified storage provider with automatic fallback.
     *
     * &lt;p&gt;This method retrieves file content from the specified storage path using
     * the appropriate storage provider. It provides automatic fallback to local
     * storage when cloud providers are unavailable.&lt;/p&gt;
     *
     * @param storagePath the path where the file is stored
     * @param storageProvider the storage provider where the file is located
     * @return the file content as a byte array
     * @throws RuntimeException if the file cannot be downloaded from any provider
     */
    public byte[] downloadFile(String storagePath, StorageProvider storageProvider) {
        try {
<span class="nc bnc" id="L157" title="All 4 branches missed.">            switch (storageProvider) {</span>
                case LOCAL:
<span class="nc" id="L159">                    return downloadFileLocally(storagePath);</span>
                case S3:
<span class="nc bnc" id="L161" title="All 2 branches missed.">                    if (s3StorageService != null) {</span>
<span class="nc" id="L162">                        return s3StorageService.downloadFile(storagePath);</span>
                    } else {
<span class="nc" id="L164">                        logger.warn(&quot;S3 storage service not available, falling back to local storage&quot;);</span>
<span class="nc" id="L165">                        return downloadFileLocally(storagePath);</span>
                    }
                case SHAREPOINT:
<span class="nc bnc" id="L168" title="All 2 branches missed.">                    if (sharePointStorageService != null) {</span>
<span class="nc" id="L169">                        return sharePointStorageService.downloadFile(storagePath);</span>
                    } else {
<span class="nc" id="L171">                        logger.warn(&quot;SharePoint storage service not available, falling back to local storage&quot;);</span>
<span class="nc" id="L172">                        return downloadFileLocally(storagePath);</span>
                    }
                default:
<span class="nc" id="L175">                    throw new IllegalArgumentException(&quot;Unsupported storage provider: &quot; + storageProvider);</span>
            }
<span class="nc" id="L177">        } catch (IOException e) {</span>
<span class="nc" id="L178">            logger.error(&quot;Failed to download file: {}&quot;, storagePath, e);</span>
<span class="nc" id="L179">            throw new RuntimeException(&quot;Failed to download file&quot;, e);</span>
        }
    }

    private String storeFileLocally(MultipartFile file) throws IOException {
        // Create year/month directory structure
<span class="nc" id="L185">        LocalDateTime now = LocalDateTime.now();</span>
<span class="nc" id="L186">        String yearMonth = now.format(DateTimeFormatter.ofPattern(&quot;yyyy/MM&quot;));</span>
        
<span class="nc" id="L188">        Path uploadDir = Paths.get(storageConfig.getLocal().getBasePath(), yearMonth);</span>
<span class="nc" id="L189">        Files.createDirectories(uploadDir);</span>
        
        // Generate unique filename to avoid conflicts
<span class="nc" id="L192">        String fileName = generateUniqueFileName(file.getOriginalFilename());</span>
<span class="nc" id="L193">        Path filePath = uploadDir.resolve(fileName);</span>
        
        // Save file
<span class="nc" id="L196">        Files.copy(file.getInputStream(), filePath);</span>
        
<span class="nc" id="L198">        logger.info(&quot;File stored locally: {}&quot;, filePath.toString());</span>
<span class="nc" id="L199">        return filePath.toString();</span>
    }

    private byte[] downloadFileLocally(String storagePath) throws IOException {
<span class="nc" id="L203">        Path filePath = Paths.get(storagePath);</span>
<span class="nc bnc" id="L204" title="All 2 branches missed.">        if (!Files.exists(filePath)) {</span>
<span class="nc" id="L205">            throw new RuntimeException(&quot;File not found: &quot; + storagePath);</span>
        }
<span class="nc" id="L207">        return Files.readAllBytes(filePath);</span>
    }

    private String generateUniqueFileName(String originalFileName) {
<span class="nc" id="L211">        String timestamp = LocalDateTime.now().format(DateTimeFormatter.ofPattern(&quot;yyyyMMdd_HHmmss_SSS&quot;));</span>
<span class="nc" id="L212">        String extension = getFileExtension(originalFileName);</span>
<span class="nc" id="L213">        String baseName = getBaseName(originalFileName);</span>
<span class="nc" id="L214">        return String.format(&quot;%s_%s%s&quot;, baseName, timestamp, extension);</span>
    }

    private String generateVersionFileName(String originalFileName, int versionNumber) {
<span class="nc" id="L218">        String extension = getFileExtension(originalFileName);</span>
<span class="nc" id="L219">        String baseName = getBaseName(originalFileName);</span>
<span class="nc" id="L220">        return String.format(&quot;%s_v%d%s&quot;, baseName, versionNumber, extension);</span>
    }

    private String getFileExtension(String fileName) {
<span class="nc bnc" id="L224" title="All 4 branches missed.">        if (fileName == null || fileName.isEmpty()) {</span>
<span class="nc" id="L225">            return &quot;&quot;;</span>
        }
<span class="nc" id="L227">        int lastDotIndex = fileName.lastIndexOf('.');</span>
<span class="nc bnc" id="L228" title="All 2 branches missed.">        return (lastDotIndex == -1) ? &quot;&quot; : fileName.substring(lastDotIndex);</span>
    }

    private String getBaseName(String fileName) {
<span class="nc bnc" id="L232" title="All 4 branches missed.">        if (fileName == null || fileName.isEmpty()) {</span>
<span class="nc" id="L233">            return &quot;file&quot;;</span>
        }
<span class="nc" id="L235">        int lastDotIndex = fileName.lastIndexOf('.');</span>
<span class="nc bnc" id="L236" title="All 2 branches missed.">        return (lastDotIndex == -1) ? fileName : fileName.substring(0, lastDotIndex);</span>
    }

    private int getNextVersionNumber(Document document) {
        // This is a simplified version number calculation
        // In a real implementation, you might want to check existing versions
<span class="nc" id="L242">        return document.getVersion() + 1;</span>
    }

    private String storeFileFromSourcePathLocally(String sourceFilePath) throws IOException {
        // Validate source file exists
<span class="nc" id="L247">        Path sourcePath = Paths.get(sourceFilePath);</span>
<span class="nc bnc" id="L248" title="All 2 branches missed.">        if (!Files.exists(sourcePath)) {</span>
<span class="nc" id="L249">            throw new IOException(&quot;Source file not found: &quot; + sourceFilePath);</span>
        }
        
<span class="nc bnc" id="L252" title="All 2 branches missed.">        if (!Files.isReadable(sourcePath)) {</span>
<span class="nc" id="L253">            throw new IOException(&quot;Source file is not readable: &quot; + sourceFilePath);</span>
        }
        
        // Create year/month directory structure
<span class="nc" id="L257">        LocalDateTime now = LocalDateTime.now();</span>
<span class="nc" id="L258">        String yearMonth = now.format(DateTimeFormatter.ofPattern(&quot;yyyy/MM&quot;));</span>
        
<span class="nc" id="L260">        Path uploadDir = Paths.get(storageConfig.getLocal().getBasePath(), yearMonth);</span>
<span class="nc" id="L261">        Files.createDirectories(uploadDir);</span>
        
        // Generate unique filename to avoid conflicts
<span class="nc" id="L264">        String originalFileName = sourcePath.getFileName().toString();</span>
<span class="nc" id="L265">        String fileName = generateUniqueFileName(originalFileName);</span>
<span class="nc" id="L266">        Path destinationPath = uploadDir.resolve(fileName);</span>
        
        // Copy file from source to organized storage location
<span class="nc" id="L269">        Files.copy(sourcePath, destinationPath);</span>
        
<span class="nc" id="L271">        logger.info(&quot;File copied from source path {} to organized storage: {}&quot;, sourceFilePath, destinationPath.toString());</span>
<span class="nc" id="L272">        return destinationPath.toString();</span>
    }
}
</pre><div class="footer"><span class="right">Created with <a href="http://www.jacoco.org/jacoco">JaCoCo</a> 0.8.10.202304240956</span></div></body></html>