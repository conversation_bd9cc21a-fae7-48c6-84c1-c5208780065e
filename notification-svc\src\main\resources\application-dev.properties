# Development Environment Overrides
# This file contains only development-specific settings that override application.properties

# Environment Identifier
ENVIRONMENT=development

# Database Configuration - Enable SQL logging for development
spring.jpa.show-sql=true
spring.jpa.properties.hibernate.format_sql=true

# Logging Configuration - Debug levels for development
logging.level.com.ascentbusiness.notification_svc=DEBUG
logging.level.org.springframework.security=DEBUG
logging.level.org.hibernate.SQL=DEBUG
logging.level.org.springframework.web=DEBUG
logging.level.org.springframework.amqp=DEBUG

# GraphQL Configuration - Enable GraphiQL for development
spring.graphql.graphiql.enabled=true
spring.graphql.schema.printer.enabled=true

# Security Configuration - Relaxed CORS for development
security.cors.allowed-origins=http://localhost:4200,http://localhost:3000,http://localhost:8080
spring.graphql.cors.allowed-origins=http://localhost:4200,http://localhost:3000,http://localhost:8080

# Cache Configuration - Shorter TTL for development
spring.cache.redis.time-to-live=300
spring.cache.redis.key-prefix=notification:dev:

# Notification Configuration - Mock email for development
notification.email.mock=true
notification.email.from=noreply@localhost

# Actuator Configuration - More endpoints exposed for development
management.endpoints.web.exposure.include=health,info,metrics,prometheus,env,configprops
management.endpoint.health.show-details=always
management.endpoint.health.show-components=always

# Development Features - Enable for development
spring.devtools.restart.enabled=true
spring.devtools.livereload.enabled=true

# Smaller batch sizes for development
notification.processing.batch-size=10
spring.jpa.properties.hibernate.jdbc.batch_size=10

# Smaller connection pools for development
spring.datasource.hikari.maximum-pool-size=5
spring.datasource.hikari.minimum-idle=2
spring.data.redis.lettuce.pool.max-active=5
spring.data.redis.lettuce.pool.max-idle=3
spring.data.redis.lettuce.pool.min-idle=1

# Server Configuration - Development optimized
server.tomcat.max-connections=200
server.tomcat.threads.max=50
server.tomcat.threads.min-spare=5

# Async Configuration - Smaller pools for development
spring.task.execution.pool.core-size=5
spring.task.execution.pool.max-size=10
spring.task.execution.pool.queue-capacity=50
spring.task.execution.thread-name-prefix=notification-async-dev-

# Template Configuration - Shorter cache for development
notification.template.cache-ttl=300

# RabbitMQ Configuration - Keep enabled but reduce connection retry noise
spring.rabbitmq.connection-timeout=5000
spring.rabbitmq.requested-heartbeat=30
logging.level.org.springframework.amqp.rabbit.connection=WARN
logging.level.org.springframework.amqp.rabbit.listener=WARN