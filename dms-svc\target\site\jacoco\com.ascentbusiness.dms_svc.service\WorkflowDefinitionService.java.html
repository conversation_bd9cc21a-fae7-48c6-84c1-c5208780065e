<?xml version="1.0" encoding="UTF-8"?><!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Strict//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-strict.dtd"><html xmlns="http://www.w3.org/1999/xhtml" lang="en"><head><meta http-equiv="Content-Type" content="text/html;charset=UTF-8"/><link rel="stylesheet" href="../jacoco-resources/report.css" type="text/css"/><link rel="shortcut icon" href="../jacoco-resources/report.gif" type="image/gif"/><title>WorkflowDefinitionService.java</title><link rel="stylesheet" href="../jacoco-resources/prettify.css" type="text/css"/><script type="text/javascript" src="../jacoco-resources/prettify.js"></script></head><body onload="window['PR_TAB_WIDTH']=4;prettyPrint()"><div class="breadcrumb" id="breadcrumb"><span class="info"><a href="../jacoco-sessions.html" class="el_session">Sessions</a></span><a href="../index.html" class="el_report">dms-svc</a> &gt; <a href="index.source.html" class="el_package">com.ascentbusiness.dms_svc.service</a> &gt; <span class="el_source">WorkflowDefinitionService.java</span></div><h1>WorkflowDefinitionService.java</h1><pre class="source lang-java linenums">package com.ascentbusiness.dms_svc.service;

import com.ascentbusiness.dms_svc.entity.WorkflowDefinition;
import com.ascentbusiness.dms_svc.enums.WorkflowType;
import com.ascentbusiness.dms_svc.repository.WorkflowDefinitionRepository;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.List;
import java.util.Optional;

/**
 * Service for managing workflow definitions
 */
@Service
<span class="fc" id="L21">@RequiredArgsConstructor</span>
<span class="fc" id="L22">@Slf4j</span>
@Transactional
public class WorkflowDefinitionService {

    private final WorkflowDefinitionRepository workflowDefinitionRepository;
    private final AuditService auditService;

    /**
     * Create a new workflow definition
     */
    public WorkflowDefinition createWorkflowDefinition(WorkflowDefinition workflowDefinition, String createdBy) {
<span class="nc" id="L33">        log.info(&quot;Creating workflow definition: {} by user: {}&quot;, workflowDefinition.getName(), createdBy);</span>
        
        // Validate workflow definition
<span class="nc" id="L36">        validateWorkflowDefinition(workflowDefinition);</span>
        
        // Audit fields are handled by BaseEntity @CreatedBy and @LastModifiedBy annotations
        
        // Check if this should be the default workflow for its type
<span class="nc bnc" id="L41" title="All 2 branches missed.">        if (workflowDefinition.getIsDefault()) {</span>
<span class="nc" id="L42">            clearDefaultWorkflowForType(workflowDefinition.getWorkflowType());</span>
        }
        
<span class="nc" id="L45">        WorkflowDefinition saved = workflowDefinitionRepository.save(workflowDefinition);</span>
        
        // Create audit log
<span class="nc" id="L48">        auditService.logWorkflowDefinitionCreated(saved, createdBy);</span>
        
<span class="nc" id="L50">        log.info(&quot;Created workflow definition with ID: {}&quot;, saved.getId());</span>
<span class="nc" id="L51">        return saved;</span>
    }

    /**
     * Update an existing workflow definition
     */
    public WorkflowDefinition updateWorkflowDefinition(Long id, WorkflowDefinition updatedDefinition, String modifiedBy) {
<span class="nc" id="L58">        log.info(&quot;Updating workflow definition ID: {} by user: {}&quot;, id, modifiedBy);</span>
        
<span class="nc" id="L60">        WorkflowDefinition existing = getWorkflowDefinitionById(id);</span>
        
        // Validate updated definition
<span class="nc" id="L63">        validateWorkflowDefinition(updatedDefinition);</span>
        
        // Update fields
<span class="nc" id="L66">        existing.setName(updatedDefinition.getName());</span>
<span class="nc" id="L67">        existing.setDescription(updatedDefinition.getDescription());</span>
<span class="nc" id="L68">        existing.setWorkflowType(updatedDefinition.getWorkflowType());</span>
<span class="nc" id="L69">        existing.setConfigurationJson(updatedDefinition.getConfigurationJson());</span>
<span class="nc" id="L70">        existing.setApprovalType(updatedDefinition.getApprovalType());</span>
<span class="nc" id="L71">        existing.setAutoStart(updatedDefinition.getAutoStart());</span>
<span class="nc" id="L72">        existing.setTimeoutHours(updatedDefinition.getTimeoutHours());</span>
<span class="nc" id="L73">        existing.setEscalationEnabled(updatedDefinition.getEscalationEnabled());</span>
<span class="nc" id="L74">        existing.setEscalationHours(updatedDefinition.getEscalationHours());</span>
<span class="nc" id="L75">        existing.setTriggerConditions(updatedDefinition.getTriggerConditions());</span>
<span class="nc" id="L76">        existing.setDocumentTypes(updatedDefinition.getDocumentTypes());</span>
<span class="nc" id="L77">        existing.setDepartmentRestrictions(updatedDefinition.getDepartmentRestrictions());</span>
<span class="nc" id="L78">        existing.setClassificationRequirements(updatedDefinition.getClassificationRequirements());</span>
        // Last modified fields are handled by BaseEntity @LastModifiedBy annotation
        
        // Handle default workflow change
<span class="nc bnc" id="L82" title="All 4 branches missed.">        if (updatedDefinition.getIsDefault() &amp;&amp; !existing.getIsDefault()) {</span>
<span class="nc" id="L83">            clearDefaultWorkflowForType(existing.getWorkflowType());</span>
<span class="nc" id="L84">            existing.setIsDefault(true);</span>
<span class="nc bnc" id="L85" title="All 4 branches missed.">        } else if (!updatedDefinition.getIsDefault() &amp;&amp; existing.getIsDefault()) {</span>
<span class="nc" id="L86">            existing.setIsDefault(false);</span>
        }
        
<span class="nc" id="L89">        WorkflowDefinition saved = workflowDefinitionRepository.save(existing);</span>
        
        // Create audit log
<span class="nc" id="L92">        auditService.logWorkflowDefinitionUpdated(saved, modifiedBy);</span>
        
<span class="nc" id="L94">        log.info(&quot;Updated workflow definition ID: {}&quot;, saved.getId());</span>
<span class="nc" id="L95">        return saved;</span>
    }

    /**
     * Get workflow definition by ID
     */
    @Transactional(readOnly = true)
    public WorkflowDefinition getWorkflowDefinitionById(Long id) {
<span class="nc" id="L103">        return workflowDefinitionRepository.findById(id)</span>
<span class="nc" id="L104">                .orElseThrow(() -&gt; new RuntimeException(&quot;Workflow definition not found with ID: &quot; + id));</span>
    }

    /**
     * Get all active workflow definitions
     */
    @Transactional(readOnly = true)
    public List&lt;WorkflowDefinition&gt; getAllActiveWorkflowDefinitions() {
<span class="nc" id="L112">        return workflowDefinitionRepository.findByIsActiveTrue();</span>
    }

    /**
     * Get workflow definitions by type
     */
    @Transactional(readOnly = true)
    public List&lt;WorkflowDefinition&gt; getWorkflowDefinitionsByType(WorkflowType workflowType) {
<span class="nc" id="L120">        return workflowDefinitionRepository.findByWorkflowTypeAndIsActiveTrue(workflowType);</span>
    }

    /**
     * Get default workflow definition for type
     */
    @Transactional(readOnly = true)
    public Optional&lt;WorkflowDefinition&gt; getDefaultWorkflowDefinition(WorkflowType workflowType) {
<span class="nc" id="L128">        return workflowDefinitionRepository.findByWorkflowTypeAndIsDefaultTrueAndIsActiveTrue(workflowType);</span>
    }

    /**
     * Search workflow definitions by name
     */
    @Transactional(readOnly = true)
    public List&lt;WorkflowDefinition&gt; searchWorkflowDefinitions(String name) {
<span class="nc" id="L136">        return workflowDefinitionRepository.findByNameContainingIgnoreCase(name);</span>
    }

    /**
     * Get workflow definitions with pagination
     */
    @Transactional(readOnly = true)
    public Page&lt;WorkflowDefinition&gt; getWorkflowDefinitions(Pageable pageable) {
<span class="nc" id="L144">        return workflowDefinitionRepository.findByIsActiveTrue(pageable);</span>
    }

    /**
     * Get workflow definitions by creator
     */
    @Transactional(readOnly = true)
    public List&lt;WorkflowDefinition&gt; getWorkflowDefinitionsByCreator(String createdBy) {
<span class="nc" id="L152">        return workflowDefinitionRepository.findByCreatedBy(createdBy);</span>
    }

    /**
     * Activate workflow definition
     */
    public WorkflowDefinition activateWorkflowDefinition(Long id, String modifiedBy) {
<span class="nc" id="L159">        log.info(&quot;Activating workflow definition ID: {} by user: {}&quot;, id, modifiedBy);</span>
        
<span class="nc" id="L161">        WorkflowDefinition definition = getWorkflowDefinitionById(id);</span>
<span class="nc" id="L162">        definition.setIsActive(true);</span>
        // Last modified fields are handled by BaseEntity @LastModifiedBy annotation
        
<span class="nc" id="L165">        WorkflowDefinition saved = workflowDefinitionRepository.save(definition);</span>
        
        // Create audit log
<span class="nc" id="L168">        auditService.logWorkflowDefinitionActivated(saved, modifiedBy);</span>
        
<span class="nc" id="L170">        return saved;</span>
    }

    /**
     * Deactivate workflow definition
     */
    public WorkflowDefinition deactivateWorkflowDefinition(Long id, String modifiedBy) {
<span class="nc" id="L177">        log.info(&quot;Deactivating workflow definition ID: {} by user: {}&quot;, id, modifiedBy);</span>
        
<span class="nc" id="L179">        WorkflowDefinition definition = getWorkflowDefinitionById(id);</span>
<span class="nc" id="L180">        definition.setIsActive(false);</span>
<span class="nc" id="L181">        definition.setIsDefault(false); // Can't be default if inactive</span>
        // Last modified fields are handled by BaseEntity @LastModifiedBy annotation
        
<span class="nc" id="L184">        WorkflowDefinition saved = workflowDefinitionRepository.save(definition);</span>
        
        // Create audit log
<span class="nc" id="L187">        auditService.logWorkflowDefinitionDeactivated(saved, modifiedBy);</span>
        
<span class="nc" id="L189">        return saved;</span>
    }

    /**
     * Set workflow definition as default for its type
     */
    public WorkflowDefinition setAsDefault(Long id, String modifiedBy) {
<span class="nc" id="L196">        log.info(&quot;Setting workflow definition ID: {} as default by user: {}&quot;, id, modifiedBy);</span>
        
<span class="nc" id="L198">        WorkflowDefinition definition = getWorkflowDefinitionById(id);</span>
        
<span class="nc bnc" id="L200" title="All 2 branches missed.">        if (!definition.isCurrentlyActive()) {</span>
<span class="nc" id="L201">            throw new RuntimeException(&quot;Cannot set inactive workflow as default&quot;);</span>
        }
        
        // Clear existing default for this type
<span class="nc" id="L205">        clearDefaultWorkflowForType(definition.getWorkflowType());</span>
        
<span class="nc" id="L207">        definition.setIsDefault(true);</span>
        // Last modified fields are handled by BaseEntity @LastModifiedBy annotation
        
<span class="nc" id="L210">        WorkflowDefinition saved = workflowDefinitionRepository.save(definition);</span>
        
        // Create audit log
<span class="nc" id="L213">        auditService.logWorkflowDefinitionSetAsDefault(saved, modifiedBy);</span>
        
<span class="nc" id="L215">        return saved;</span>
    }

    /**
     * Delete workflow definition (soft delete)
     */
    public void deleteWorkflowDefinition(Long id, String deletedBy) {
<span class="nc" id="L222">        log.info(&quot;Deleting workflow definition ID: {} by user: {}&quot;, id, deletedBy);</span>
        
<span class="nc" id="L224">        WorkflowDefinition definition = getWorkflowDefinitionById(id);</span>
        
        // Check if there are active instances
<span class="nc bnc" id="L227" title="All 2 branches missed.">        if (hasActiveInstances(definition)) {</span>
<span class="nc" id="L228">            throw new RuntimeException(&quot;Cannot delete workflow definition with active instances&quot;);</span>
        }
        
<span class="nc" id="L231">        definition.setIsActive(false);</span>
<span class="nc" id="L232">        definition.setIsDefault(false);</span>
        // Last modified fields are handled by BaseEntity @LastModifiedBy annotation
        
<span class="nc" id="L235">        workflowDefinitionRepository.save(definition);</span>
        
        // Create audit log
<span class="nc" id="L238">        auditService.logWorkflowDefinitionDeleted(definition, deletedBy);</span>
<span class="nc" id="L239">    }</span>

    /**
     * Get workflow definitions that can auto-start
     */
    @Transactional(readOnly = true)
    public List&lt;WorkflowDefinition&gt; getAutoStartWorkflowDefinitions() {
<span class="nc" id="L246">        return workflowDefinitionRepository.findByAutoStartTrueAndIsActiveTrue();</span>
    }

    /**
     * Get workflow definitions applicable to document type
     */
    @Transactional(readOnly = true)
    public List&lt;WorkflowDefinition&gt; getApplicableWorkflowDefinitions(String documentType, String department) {
<span class="nc" id="L254">        List&lt;WorkflowDefinition&gt; byDocumentType = workflowDefinitionRepository.findByDocumentType(&quot;\&quot;&quot; + documentType + &quot;\&quot;&quot;);</span>
<span class="nc" id="L255">        List&lt;WorkflowDefinition&gt; byDepartment = workflowDefinitionRepository.findByDepartment(&quot;\&quot;&quot; + department + &quot;\&quot;&quot;);</span>

        // Combine and deduplicate results using a mutable list
<span class="nc" id="L258">        List&lt;WorkflowDefinition&gt; combined = new ArrayList&lt;&gt;(byDocumentType);</span>
<span class="nc" id="L259">        combined.addAll(byDepartment);</span>
<span class="nc" id="L260">        return combined.stream().distinct().toList();</span>
    }

    // Private helper methods

    private void validateWorkflowDefinition(WorkflowDefinition definition) {
<span class="nc bnc" id="L266" title="All 4 branches missed.">        if (definition.getName() == null || definition.getName().trim().isEmpty()) {</span>
<span class="nc" id="L267">            throw new RuntimeException(&quot;Workflow definition name is required&quot;);</span>
        }
        
<span class="nc bnc" id="L270" title="All 2 branches missed.">        if (definition.getWorkflowType() == null) {</span>
<span class="nc" id="L271">            throw new RuntimeException(&quot;Workflow type is required&quot;);</span>
        }
        
<span class="nc bnc" id="L274" title="All 2 branches missed.">        if (definition.getApprovalType() == null) {</span>
<span class="nc" id="L275">            throw new RuntimeException(&quot;Approval type is required&quot;);</span>
        }
        
        // Check for duplicate name and version
<span class="nc bnc" id="L279" title="All 2 branches missed.">        if (workflowDefinitionRepository.existsByNameAndVersion(definition.getName(), definition.getVersion())) {</span>
<span class="nc" id="L280">            throw new RuntimeException(&quot;Workflow definition with name '&quot; + definition.getName() + </span>
<span class="nc" id="L281">                                     &quot;' and version '&quot; + definition.getVersion() + &quot;' already exists&quot;);</span>
        }
<span class="nc" id="L283">    }</span>

    private void clearDefaultWorkflowForType(WorkflowType workflowType) {
<span class="nc" id="L286">        Optional&lt;WorkflowDefinition&gt; existingDefault = workflowDefinitionRepository</span>
<span class="nc" id="L287">                .findByWorkflowTypeAndIsDefaultTrueAndIsActiveTrue(workflowType);</span>
        
<span class="nc bnc" id="L289" title="All 2 branches missed.">        if (existingDefault.isPresent()) {</span>
<span class="nc" id="L290">            WorkflowDefinition existing = existingDefault.get();</span>
<span class="nc" id="L291">            existing.setIsDefault(false);</span>
<span class="nc" id="L292">            workflowDefinitionRepository.save(existing);</span>
        }
<span class="nc" id="L294">    }</span>

    private boolean hasActiveInstances(WorkflowDefinition definition) {
        // This would check if there are active workflow instances
        // Implementation depends on WorkflowInstanceService
<span class="nc" id="L299">        return false; // Placeholder</span>
    }
}
</pre><div class="footer"><span class="right">Created with <a href="http://www.jacoco.org/jacoco">JaCoCo</a> 0.8.10.202304240956</span></div></body></html>