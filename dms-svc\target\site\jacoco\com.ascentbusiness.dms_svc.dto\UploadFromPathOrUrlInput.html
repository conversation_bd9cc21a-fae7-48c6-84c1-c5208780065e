<?xml version="1.0" encoding="UTF-8"?><!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Strict//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-strict.dtd"><html xmlns="http://www.w3.org/1999/xhtml" lang="en"><head><meta http-equiv="Content-Type" content="text/html;charset=UTF-8"/><link rel="stylesheet" href="../jacoco-resources/report.css" type="text/css"/><link rel="shortcut icon" href="../jacoco-resources/report.gif" type="image/gif"/><title>UploadFromPathOrUrlInput</title><script type="text/javascript" src="../jacoco-resources/sort.js"></script></head><body onload="initialSort(['breadcrumb'])"><div class="breadcrumb" id="breadcrumb"><span class="info"><a href="../jacoco-sessions.html" class="el_session">Sessions</a></span><a href="../index.html" class="el_report">dms-svc</a> &gt; <a href="index.html" class="el_package">com.ascentbusiness.dms_svc.dto</a> &gt; <span class="el_class">UploadFromPathOrUrlInput</span></div><h1>UploadFromPathOrUrlInput</h1><table class="coverage" cellspacing="0" id="coveragetable"><thead><tr><td class="sortable" id="a" onclick="toggleSort(this)">Element</td><td class="down sortable bar" id="b" onclick="toggleSort(this)">Missed Instructions</td><td class="sortable ctr2" id="c" onclick="toggleSort(this)">Cov.</td><td class="sortable bar" id="d" onclick="toggleSort(this)">Missed Branches</td><td class="sortable ctr2" id="e" onclick="toggleSort(this)">Cov.</td><td class="sortable ctr1" id="f" onclick="toggleSort(this)">Missed</td><td class="sortable ctr2" id="g" onclick="toggleSort(this)">Cxty</td><td class="sortable ctr1" id="h" onclick="toggleSort(this)">Missed</td><td class="sortable ctr2" id="i" onclick="toggleSort(this)">Lines</td><td class="sortable ctr1" id="j" onclick="toggleSort(this)">Missed</td><td class="sortable ctr2" id="k" onclick="toggleSort(this)">Methods</td></tr></thead><tfoot><tr><td>Total</td><td class="bar">35 of 194</td><td class="ctr2">81%</td><td class="bar">12 of 42</td><td class="ctr2">71%</td><td class="ctr1">11</td><td class="ctr2">28</td><td class="ctr1">8</td><td class="ctr2">34</td><td class="ctr1">0</td><td class="ctr2">7</td></tr></tfoot><tbody><tr><td id="a0"><a href="UploadFromPathOrUrlInput.java.html#L145" class="el_method">extractFileName()</a></td><td class="bar" id="b0"><img src="../jacoco-resources/redbar.gif" width="36" height="10" title="20" alt="20"/><img src="../jacoco-resources/greenbar.gif" width="83" height="10" title="45" alt="45"/></td><td class="ctr2" id="c6">69%</td><td class="bar" id="d1"><img src="../jacoco-resources/redbar.gif" width="25" height="10" title="3" alt="3"/><img src="../jacoco-resources/greenbar.gif" width="42" height="10" title="5" alt="5"/></td><td class="ctr2" id="e5">62%</td><td class="ctr1" id="f1">2</td><td class="ctr2" id="g1">5</td><td class="ctr1" id="h0">4</td><td class="ctr2" id="i0">12</td><td class="ctr1" id="j0">0</td><td class="ctr2" id="k0">1</td></tr><tr><td id="a6"><a href="UploadFromPathOrUrlInput.java.html#L185" class="el_method">validate()</a></td><td class="bar" id="b1"><img src="../jacoco-resources/redbar.gif" width="24" height="10" title="13" alt="13"/><img src="../jacoco-resources/greenbar.gif" width="81" height="10" title="44" alt="44"/></td><td class="ctr2" id="c5">77%</td><td class="bar" id="d0"><img src="../jacoco-resources/redbar.gif" width="34" height="10" title="4" alt="4"/><img src="../jacoco-resources/greenbar.gif" width="85" height="10" title="10" alt="10"/></td><td class="ctr2" id="e3">71%</td><td class="ctr1" id="f0">4</td><td class="ctr2" id="g0">8</td><td class="ctr1" id="h1">3</td><td class="ctr2" id="i1">11</td><td class="ctr1" id="j1">0</td><td class="ctr2" id="k1">1</td></tr><tr><td id="a3"><a href="UploadFromPathOrUrlInput.java.html#L129" class="el_method">isLocalPath()</a></td><td class="bar" id="b2"><img src="../jacoco-resources/redbar.gif" width="3" height="10" title="2" alt="2"/><img src="../jacoco-resources/greenbar.gif" width="27" height="10" title="15" alt="15"/></td><td class="ctr2" id="c4">88%</td><td class="bar" id="d5"><img src="../jacoco-resources/redbar.gif" width="8" height="10" title="1" alt="1"/><img src="../jacoco-resources/greenbar.gif" width="8" height="10" title="1" alt="1"/></td><td class="ctr2" id="e6">50%</td><td class="ctr1" id="f3">1</td><td class="ctr2" id="g5">2</td><td class="ctr1" id="h2">1</td><td class="ctr2" id="i2">5</td><td class="ctr1" id="j2">0</td><td class="ctr2" id="k2">1</td></tr><tr><td id="a5"><a href="UploadFromPathOrUrlInput.java.html#L100" class="el_method">isUrl()</a></td><td class="bar" id="b3"><img src="../jacoco-resources/greenbar.gif" width="31" height="10" title="17" alt="17"/></td><td class="ctr2" id="c0">100%</td><td class="bar" id="d2"><img src="../jacoco-resources/redbar.gif" width="17" height="10" title="2" alt="2"/><img src="../jacoco-resources/greenbar.gif" width="34" height="10" title="4" alt="4"/></td><td class="ctr2" id="e4">66%</td><td class="ctr1" id="f2">2</td><td class="ctr2" id="g2">4</td><td class="ctr1" id="h3">0</td><td class="ctr2" id="i3">2</td><td class="ctr1" id="j3">0</td><td class="ctr2" id="k3">1</td></tr><tr><td id="a4"><a href="UploadFromPathOrUrlInput.java.html#L110" class="el_method">isNetworkPath()</a></td><td class="bar" id="b4"><img src="../jacoco-resources/greenbar.gif" width="31" height="10" title="17" alt="17"/></td><td class="ctr2" id="c1">100%</td><td class="bar" id="d3"><img src="../jacoco-resources/redbar.gif" width="8" height="10" title="1" alt="1"/><img src="../jacoco-resources/greenbar.gif" width="42" height="10" title="5" alt="5"/></td><td class="ctr2" id="e1">83%</td><td class="ctr1" id="f4">1</td><td class="ctr2" id="g3">4</td><td class="ctr1" id="h4">0</td><td class="ctr2" id="i4">2</td><td class="ctr1" id="j4">0</td><td class="ctr2" id="k4">1</td></tr><tr><td id="a2"><a href="UploadFromPathOrUrlInput.java.html#L120" class="el_method">isFileUri()</a></td><td class="bar" id="b5"><img src="../jacoco-resources/greenbar.gif" width="22" height="10" title="12" alt="12"/></td><td class="ctr2" id="c2">100%</td><td class="bar" id="d4"><img src="../jacoco-resources/redbar.gif" width="8" height="10" title="1" alt="1"/><img src="../jacoco-resources/greenbar.gif" width="25" height="10" title="3" alt="3"/></td><td class="ctr2" id="e2">75%</td><td class="ctr1" id="f5">1</td><td class="ctr2" id="g4">3</td><td class="ctr1" id="h5">0</td><td class="ctr2" id="i5">1</td><td class="ctr1" id="j5">0</td><td class="ctr2" id="k5">1</td></tr><tr><td id="a1"><a href="UploadFromPathOrUrlInput.java.html#L176" class="el_method">getEffectiveName()</a></td><td class="bar" id="b6"><img src="../jacoco-resources/greenbar.gif" width="16" height="10" title="9" alt="9"/></td><td class="ctr2" id="c3">100%</td><td class="bar" id="d6"><img src="../jacoco-resources/greenbar.gif" width="17" height="10" title="2" alt="2"/></td><td class="ctr2" id="e0">100%</td><td class="ctr1" id="f6">0</td><td class="ctr2" id="g6">2</td><td class="ctr1" id="h6">0</td><td class="ctr2" id="i6">1</td><td class="ctr1" id="j6">0</td><td class="ctr2" id="k6">1</td></tr></tbody></table><div class="footer"><span class="right">Created with <a href="http://www.jacoco.org/jacoco">JaCoCo</a> 0.8.10.202304240956</span></div></body></html>