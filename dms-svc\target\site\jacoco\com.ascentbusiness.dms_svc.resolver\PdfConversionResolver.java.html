<?xml version="1.0" encoding="UTF-8"?><!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Strict//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-strict.dtd"><html xmlns="http://www.w3.org/1999/xhtml" lang="en"><head><meta http-equiv="Content-Type" content="text/html;charset=UTF-8"/><link rel="stylesheet" href="../jacoco-resources/report.css" type="text/css"/><link rel="shortcut icon" href="../jacoco-resources/report.gif" type="image/gif"/><title>PdfConversionResolver.java</title><link rel="stylesheet" href="../jacoco-resources/prettify.css" type="text/css"/><script type="text/javascript" src="../jacoco-resources/prettify.js"></script></head><body onload="window['PR_TAB_WIDTH']=4;prettyPrint()"><div class="breadcrumb" id="breadcrumb"><span class="info"><a href="../jacoco-sessions.html" class="el_session">Sessions</a></span><a href="../index.html" class="el_report">dms-svc</a> &gt; <a href="index.source.html" class="el_package">com.ascentbusiness.dms_svc.resolver</a> &gt; <span class="el_source">PdfConversionResolver.java</span></div><h1>PdfConversionResolver.java</h1><pre class="source lang-java linenums">package com.ascentbusiness.dms_svc.resolver;

import com.ascentbusiness.dms_svc.dto.PdfConversionResult;
import com.ascentbusiness.dms_svc.enums.AuditAction;
import com.ascentbusiness.dms_svc.enums.VirusScannerType;
import com.ascentbusiness.dms_svc.exception.DmsBusinessException;
import com.ascentbusiness.dms_svc.exception.PdfConversionException;
import com.ascentbusiness.dms_svc.service.AuditService;
import com.ascentbusiness.dms_svc.service.PdfToWordConversionService;
import com.ascentbusiness.dms_svc.util.CorrelationIdUtil;
import com.ascentbusiness.dms_svc.util.SecurityValidationUtil;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.graphql.data.method.annotation.Argument;
import org.springframework.graphql.data.method.annotation.MutationMapping;
import org.springframework.stereotype.Controller;
import org.springframework.web.multipart.MultipartFile;

import java.util.Map;

/**
 * GraphQL resolver for PDF to Word conversion operations.
 * Provides mutations for converting PDF files to Word documents.
 */
@Controller
<span class="fc" id="L27">public class PdfConversionResolver {</span>

<span class="fc" id="L29">    private static final Logger logger = LoggerFactory.getLogger(PdfConversionResolver.class);</span>

    @Autowired
    private PdfToWordConversionService pdfConversionService;

    @Autowired
    private AuditService auditService;

    /**
     * Convert PDF to Word document from multipart file upload.
     * 
     * @param input the conversion input containing file and scanner type
     * @return conversion result with download path
     */
    @MutationMapping
    public PdfConversionResult convertPdfToWordMultipart(@Argument Map&lt;String, Object&gt; input) {
<span class="nc" id="L45">        String correlationId = CorrelationIdUtil.getCurrentCorrelationId();</span>
<span class="nc" id="L46">        logger.info(&quot;GraphQL mutation: convertPdfToWordMultipart() [{}]&quot;, correlationId);</span>

        try {
            // Extract input parameters
<span class="nc" id="L50">            MultipartFile file = (MultipartFile) input.get(&quot;file&quot;);</span>
<span class="nc" id="L51">            VirusScannerType scannerType = null;</span>
            
<span class="nc bnc" id="L53" title="All 4 branches missed.">            if (input.containsKey(&quot;scannerType&quot;) &amp;&amp; input.get(&quot;scannerType&quot;) != null) {</span>
<span class="nc" id="L54">                scannerType = VirusScannerType.valueOf(input.get(&quot;scannerType&quot;).toString());</span>
            }

            // Validate input
<span class="nc" id="L58">            validateMultipartInput(file);</span>

            // Get user ID (for now using a default since no security context required)
<span class="nc" id="L61">            String userId = getCurrentUserId();</span>

            // Log conversion request
<span class="nc" id="L64">            auditService.logAudit(AuditAction.CONVERSION_INITIATED, null, userId,</span>
<span class="nc" id="L65">                    String.format(&quot;PDF to Word conversion requested via multipart upload: %s (scanner: %s)&quot;, </span>
<span class="nc" id="L66">                                SecurityValidationUtil.sanitizeForLogging(file.getOriginalFilename()), scannerType));</span>

            // Perform conversion
<span class="nc" id="L69">            PdfConversionResult result = pdfConversionService.convertPdfToWordFromMultipart(file, userId, scannerType);</span>

            // Log successful conversion
<span class="nc" id="L72">            auditService.logAudit(AuditAction.CONVERSION_FILE_DOWNLOADED, null, userId,</span>
<span class="nc" id="L73">                    String.format(&quot;PDF to Word conversion file ready for download: %s -&gt; %s (session: %s)&quot;, </span>
<span class="nc" id="L74">                                SecurityValidationUtil.sanitizeForLogging(result.getOriginalFileName()),</span>
<span class="nc" id="L75">                                SecurityValidationUtil.sanitizeForLogging(result.getConvertedFileName()),</span>
<span class="nc" id="L76">                                result.getSessionId()));</span>

<span class="nc" id="L78">            logger.info(&quot;PDF to Word conversion completed successfully via multipart upload: {} -&gt; {} [{}]&quot;, </span>
<span class="nc" id="L79">                       SecurityValidationUtil.sanitizeForLogging(file.getOriginalFilename()),</span>
<span class="nc" id="L80">                       SecurityValidationUtil.sanitizeForLogging(result.getConvertedFileName()),</span>
                       correlationId);

<span class="nc" id="L83">            return result;</span>

<span class="nc" id="L85">        } catch (PdfConversionException e) {</span>
<span class="nc" id="L86">            logger.error(&quot;PDF to Word conversion failed via multipart upload [{}]&quot;, correlationId, e);</span>
<span class="nc" id="L87">            throw e;</span>
<span class="nc" id="L88">        } catch (DmsBusinessException e) {</span>
            // Re-throw DmsBusinessException with original message to preserve specific error details
<span class="nc" id="L90">            logger.error(&quot;Business error during PDF to Word conversion via multipart upload [{}]&quot;, correlationId, e);</span>
<span class="nc" id="L91">            throw e;</span>
<span class="nc" id="L92">        } catch (Exception e) {</span>
<span class="nc" id="L93">            logger.error(&quot;Unexpected error during PDF to Word conversion via multipart upload [{}]&quot;, correlationId, e);</span>
<span class="nc" id="L94">            throw new DmsBusinessException(&quot;PDF to Word conversion failed: &quot; + e.getMessage(), &quot;PDF_CONVERSION_FAILED&quot;, Map.of(&quot;error&quot;, e.getMessage()), e);</span>
        }
    }

    /**
     * Convert PDF to Word document from file path.
     * 
     * @param input the conversion input containing file path and scanner type
     * @return conversion result with download path
     */
    @MutationMapping
    public PdfConversionResult convertPdfToWordFromPath(@Argument Map&lt;String, Object&gt; input) {
<span class="nc" id="L106">        String correlationId = CorrelationIdUtil.getCurrentCorrelationId();</span>
<span class="nc" id="L107">        logger.info(&quot;GraphQL mutation: convertPdfToWordFromPath() [{}]&quot;, correlationId);</span>

        try {
            // Extract input parameters
<span class="nc" id="L111">            String filePath = (String) input.get(&quot;filePath&quot;);</span>
<span class="nc" id="L112">            VirusScannerType scannerType = null;</span>
            
<span class="nc bnc" id="L114" title="All 4 branches missed.">            if (input.containsKey(&quot;scannerType&quot;) &amp;&amp; input.get(&quot;scannerType&quot;) != null) {</span>
<span class="nc" id="L115">                scannerType = VirusScannerType.valueOf(input.get(&quot;scannerType&quot;).toString());</span>
            }

            // Validate input
<span class="nc" id="L119">            validatePathInput(filePath);</span>

            // Get user ID (for now using a default since no security context required)
<span class="nc" id="L122">            String userId = getCurrentUserId();</span>

            // Log conversion request
<span class="nc" id="L125">            auditService.logAudit(AuditAction.CONVERSION_INITIATED, null, userId,</span>
<span class="nc" id="L126">                    String.format(&quot;PDF to Word conversion requested via file path: %s (scanner: %s)&quot;, </span>
<span class="nc" id="L127">                                SecurityValidationUtil.sanitizeForLogging(filePath), scannerType));</span>

            // Perform conversion
<span class="nc" id="L130">            PdfConversionResult result = pdfConversionService.convertPdfToWordFromPath(filePath, userId, scannerType);</span>

            // Log successful conversion
<span class="nc" id="L133">            auditService.logAudit(AuditAction.CONVERSION_FILE_DOWNLOADED, null, userId,</span>
<span class="nc" id="L134">                    String.format(&quot;PDF to Word conversion file ready for download: %s -&gt; %s (session: %s)&quot;, </span>
<span class="nc" id="L135">                                SecurityValidationUtil.sanitizeForLogging(result.getOriginalFileName()),</span>
<span class="nc" id="L136">                                SecurityValidationUtil.sanitizeForLogging(result.getConvertedFileName()),</span>
<span class="nc" id="L137">                                result.getSessionId()));</span>

<span class="nc" id="L139">            logger.info(&quot;PDF to Word conversion completed successfully via file path: {} -&gt; {} [{}]&quot;, </span>
<span class="nc" id="L140">                       SecurityValidationUtil.sanitizeForLogging(filePath),</span>
<span class="nc" id="L141">                       SecurityValidationUtil.sanitizeForLogging(result.getConvertedFileName()),</span>
                       correlationId);

<span class="nc" id="L144">            return result;</span>

<span class="nc" id="L146">        } catch (PdfConversionException e) {</span>
<span class="nc" id="L147">            logger.error(&quot;PDF to Word conversion failed via file path [{}]&quot;, correlationId, e);</span>
<span class="nc" id="L148">            throw e;</span>
<span class="nc" id="L149">        } catch (DmsBusinessException e) {</span>
            // Re-throw DmsBusinessException with original message to preserve specific error details
<span class="nc" id="L151">            logger.error(&quot;Business error during PDF to Word conversion via file path [{}]&quot;, correlationId, e);</span>
<span class="nc" id="L152">            throw e;</span>
<span class="nc" id="L153">        } catch (Exception e) {</span>
<span class="nc" id="L154">            logger.error(&quot;Unexpected error during PDF to Word conversion via file path [{}]&quot;, correlationId, e);</span>
            // Handle null correlationId to avoid NullPointerException
<span class="nc bnc" id="L156" title="All 2 branches missed.">            Map&lt;String, Object&gt; errorDetails = correlationId != null ?</span>
<span class="nc" id="L157">                Map.of(&quot;correlationId&quot;, correlationId) :</span>
<span class="nc" id="L158">                Map.of();</span>
<span class="nc" id="L159">            throw new DmsBusinessException(&quot;PDF to Word conversion failed: &quot; + e.getMessage(), &quot;PDF_CONVERSION_ERROR&quot;, errorDetails, e);</span>
        }
    }

    /**
     * Validate multipart file input.
     */
    private void validateMultipartInput(MultipartFile file) {
<span class="nc bnc" id="L167" title="All 2 branches missed.">        if (file == null) {</span>
<span class="nc" id="L168">            throw new DmsBusinessException(&quot;File is required for PDF conversion&quot;);</span>
        }

<span class="nc bnc" id="L171" title="All 2 branches missed.">        if (file.isEmpty()) {</span>
<span class="nc" id="L172">            throw new DmsBusinessException(&quot;File cannot be empty&quot;);</span>
        }

<span class="nc" id="L175">        String fileName = file.getOriginalFilename();</span>
<span class="nc bnc" id="L176" title="All 4 branches missed.">        if (fileName == null || fileName.trim().isEmpty()) {</span>
<span class="nc" id="L177">            throw new DmsBusinessException(&quot;File name is required&quot;);</span>
        }

<span class="nc bnc" id="L180" title="All 2 branches missed.">        if (!fileName.toLowerCase().endsWith(&quot;.pdf&quot;)) {</span>
<span class="nc" id="L181">            throw new DmsBusinessException(&quot;Only PDF files are supported for conversion&quot;);</span>
        }
<span class="nc" id="L183">    }</span>

    /**
     * Validate file path input.
     */
    private void validatePathInput(String filePath) {
<span class="nc bnc" id="L189" title="All 4 branches missed.">        if (filePath == null || filePath.trim().isEmpty()) {</span>
<span class="nc" id="L190">            throw new DmsBusinessException(&quot;File path is required for PDF conversion&quot;);</span>
        }

<span class="nc bnc" id="L193" title="All 2 branches missed.">        if (!filePath.toLowerCase().endsWith(&quot;.pdf&quot;)) {</span>
<span class="nc" id="L194">            throw new DmsBusinessException(&quot;Only PDF files are supported for conversion&quot;);</span>
        }

        // Basic path traversal protection
<span class="nc bnc" id="L198" title="All 8 branches missed.">        if (filePath.contains(&quot;..&quot;) || filePath.contains(&quot;~/&quot;) || filePath.contains(&quot;~\\&quot;) || filePath.startsWith(&quot;~&quot;)) {</span>
<span class="nc" id="L199">            throw new DmsBusinessException(&quot;Invalid file path: path traversal not allowed&quot;);</span>
        }
<span class="nc" id="L201">    }</span>

    /**
     * Get current user ID. Since no security context is required per requirements,
     * we'll use a default user ID for audit logging purposes.
     */
    private String getCurrentUserId() {
        // Since no security context/RBAC is required, use a default user ID
        // In a real implementation, this would extract from JWT or security context
<span class="nc" id="L210">        return &quot;pdf-conversion-user&quot;;</span>
    }
}
</pre><div class="footer"><span class="right">Created with <a href="http://www.jacoco.org/jacoco">JaCoCo</a> 0.8.10.202304240956</span></div></body></html>