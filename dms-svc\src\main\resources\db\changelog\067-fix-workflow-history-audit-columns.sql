--liquibase formatted sql

--changeset system:067-fix-workflow-history-audit-columns
--comment: Add missing BaseEntity audit columns to workflow_history table

-- Add BaseEntity audit columns to workflow_history table
ALTER TABLE workflow_history 
ADD COLUMN created_date TIMES<PERSON>MP NOT NULL DEFAULT CURRENT_TIMESTAMP,
ADD COLUMN last_modified_date TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
ADD COLUMN created_by VARCHAR(100) NOT NULL DEFAULT 'system',
ADD COLUMN last_modified_by VARCHAR(100);

-- Add indexes for the audit columns
CREATE INDEX idx_workflow_history_created_by ON workflow_history(created_by);
CREATE INDEX idx_workflow_history_created_date ON workflow_history(created_date);
CREATE INDEX idx_workflow_history_last_modified_date ON workflow_history(last_modified_date);

-- Add comment for documentation
ALTER TABLE workflow_history COMMENT = 'Tracks workflow history events with proper BaseEntity audit columns';