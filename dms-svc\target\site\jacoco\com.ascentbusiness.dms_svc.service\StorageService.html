<?xml version="1.0" encoding="UTF-8"?><!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Strict//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-strict.dtd"><html xmlns="http://www.w3.org/1999/xhtml" lang="en"><head><meta http-equiv="Content-Type" content="text/html;charset=UTF-8"/><link rel="stylesheet" href="../jacoco-resources/report.css" type="text/css"/><link rel="shortcut icon" href="../jacoco-resources/report.gif" type="image/gif"/><title>StorageService</title><script type="text/javascript" src="../jacoco-resources/sort.js"></script></head><body onload="initialSort(['breadcrumb'])"><div class="breadcrumb" id="breadcrumb"><span class="info"><a href="../jacoco-sessions.html" class="el_session">Sessions</a></span><a href="../index.html" class="el_report">dms-svc</a> &gt; <a href="index.html" class="el_package">com.ascentbusiness.dms_svc.service</a> &gt; <span class="el_class">StorageService</span></div><h1>StorageService</h1><table class="coverage" cellspacing="0" id="coveragetable"><thead><tr><td class="sortable" id="a" onclick="toggleSort(this)">Element</td><td class="down sortable bar" id="b" onclick="toggleSort(this)">Missed Instructions</td><td class="sortable ctr2" id="c" onclick="toggleSort(this)">Cov.</td><td class="sortable bar" id="d" onclick="toggleSort(this)">Missed Branches</td><td class="sortable ctr2" id="e" onclick="toggleSort(this)">Cov.</td><td class="sortable ctr1" id="f" onclick="toggleSort(this)">Missed</td><td class="sortable ctr2" id="g" onclick="toggleSort(this)">Cxty</td><td class="sortable ctr1" id="h" onclick="toggleSort(this)">Missed</td><td class="sortable ctr2" id="i" onclick="toggleSort(this)">Lines</td><td class="sortable ctr1" id="j" onclick="toggleSort(this)">Missed</td><td class="sortable ctr2" id="k" onclick="toggleSort(this)">Methods</td></tr></thead><tfoot><tr><td>Total</td><td class="bar">438 of 448</td><td class="ctr2">2%</td><td class="bar">42 of 42</td><td class="ctr2">0%</td><td class="ctr1">36</td><td class="ctr2">38</td><td class="ctr1">88</td><td class="ctr2">90</td><td class="ctr1">12</td><td class="ctr2">14</td></tr></tfoot><tbody><tr><td id="a11"><a href="StorageService.java.html#L247" class="el_method">storeFileFromSourcePathLocally(String)</a></td><td class="bar" id="b0"><img src="../jacoco-resources/redbar.gif" width="120" height="10" title="76" alt="76"/></td><td class="ctr2" id="c2">0%</td><td class="bar" id="d5"><img src="../jacoco-resources/redbar.gif" width="60" height="10" title="4" alt="4"/></td><td class="ctr2" id="e0">0%</td><td class="ctr1" id="f5">3</td><td class="ctr2" id="g5">3</td><td class="ctr1" id="h0">15</td><td class="ctr2" id="i0">15</td><td class="ctr1" id="j0">1</td><td class="ctr2" id="k0">1</td></tr><tr><td id="a0"><a href="StorageService.java.html#L157" class="el_method">downloadFile(String, StorageProvider)</a></td><td class="bar" id="b1"><img src="../jacoco-resources/redbar.gif" width="91" height="10" title="58" alt="58"/></td><td class="ctr2" id="c3">0%</td><td class="bar" id="d0"><img src="../jacoco-resources/redbar.gif" width="120" height="10" title="8" alt="8"/></td><td class="ctr2" id="e1">0%</td><td class="ctr1" id="f0">6</td><td class="ctr2" id="g0">6</td><td class="ctr1" id="h1">14</td><td class="ctr2" id="i1">14</td><td class="ctr1" id="j1">1</td><td class="ctr2" id="k1">1</td></tr><tr><td id="a12"><a href="StorageService.java.html#L185" class="el_method">storeFileLocally(MultipartFile)</a></td><td class="bar" id="b2"><img src="../jacoco-resources/redbar.gif" width="75" height="10" title="48" alt="48"/></td><td class="ctr2" id="c4">0%</td><td class="bar" id="d7"/><td class="ctr2" id="e7">n/a</td><td class="ctr1" id="f7">1</td><td class="ctr2" id="g7">1</td><td class="ctr1" id="h4">9</td><td class="ctr2" id="i4">9</td><td class="ctr1" id="j2">1</td><td class="ctr2" id="k2">1</td></tr><tr><td id="a9"><a href="StorageService.java.html#L83" class="el_method">storeFile(MultipartFile, StorageProvider)</a></td><td class="bar" id="b3"><img src="../jacoco-resources/redbar.gif" width="72" height="10" title="46" alt="46"/></td><td class="ctr2" id="c5">0%</td><td class="bar" id="d1"><img src="../jacoco-resources/redbar.gif" width="120" height="10" title="8" alt="8"/></td><td class="ctr2" id="e2">0%</td><td class="ctr1" id="f1">6</td><td class="ctr2" id="g1">6</td><td class="ctr1" id="h2">11</td><td class="ctr2" id="i2">11</td><td class="ctr1" id="j3">1</td><td class="ctr2" id="k3">1</td></tr><tr><td id="a10"><a href="StorageService.java.html#L121" class="el_method">storeFileFromSourcePath(String, StorageProvider)</a></td><td class="bar" id="b4"><img src="../jacoco-resources/redbar.gif" width="72" height="10" title="46" alt="46"/></td><td class="ctr2" id="c6">0%</td><td class="bar" id="d2"><img src="../jacoco-resources/redbar.gif" width="120" height="10" title="8" alt="8"/></td><td class="ctr2" id="e3">0%</td><td class="ctr1" id="f2">6</td><td class="ctr2" id="g2">6</td><td class="ctr1" id="h3">11</td><td class="ctr2" id="i3">11</td><td class="ctr1" id="j4">1</td><td class="ctr2" id="k4">1</td></tr><tr><td id="a13"><a href="StorageService.java.html#L107" class="el_method">storeFileVersion(MultipartFile, Document)</a></td><td class="bar" id="b5"><img src="../jacoco-resources/redbar.gif" width="66" height="10" title="42" alt="42"/></td><td class="ctr2" id="c7">0%</td><td class="bar" id="d8"/><td class="ctr2" id="e8">n/a</td><td class="ctr1" id="f8">1</td><td class="ctr2" id="g8">1</td><td class="ctr1" id="h5">8</td><td class="ctr2" id="i5">8</td><td class="ctr1" id="j5">1</td><td class="ctr2" id="k5">1</td></tr><tr><td id="a2"><a href="StorageService.java.html#L211" class="el_method">generateUniqueFileName(String)</a></td><td class="bar" id="b6"><img src="../jacoco-resources/redbar.gif" width="47" height="10" title="30" alt="30"/></td><td class="ctr2" id="c8">0%</td><td class="bar" id="d9"/><td class="ctr2" id="e9">n/a</td><td class="ctr1" id="f9">1</td><td class="ctr2" id="g9">1</td><td class="ctr1" id="h6">4</td><td class="ctr2" id="i6">4</td><td class="ctr1" id="j6">1</td><td class="ctr2" id="k6">1</td></tr><tr><td id="a3"><a href="StorageService.java.html#L218" class="el_method">generateVersionFileName(String, int)</a></td><td class="bar" id="b7"><img src="../jacoco-resources/redbar.gif" width="41" height="10" title="26" alt="26"/></td><td class="ctr2" id="c9">0%</td><td class="bar" id="d10"/><td class="ctr2" id="e10">n/a</td><td class="ctr1" id="f10">1</td><td class="ctr2" id="g10">1</td><td class="ctr1" id="h10">3</td><td class="ctr2" id="i10">3</td><td class="ctr1" id="j7">1</td><td class="ctr2" id="k7">1</td></tr><tr><td id="a4"><a href="StorageService.java.html#L232" class="el_method">getBaseName(String)</a></td><td class="bar" id="b8"><img src="../jacoco-resources/redbar.gif" width="33" height="10" title="21" alt="21"/></td><td class="ctr2" id="c10">0%</td><td class="bar" id="d3"><img src="../jacoco-resources/redbar.gif" width="90" height="10" title="6" alt="6"/></td><td class="ctr2" id="e4">0%</td><td class="ctr1" id="f3">4</td><td class="ctr2" id="g3">4</td><td class="ctr1" id="h7">4</td><td class="ctr2" id="i7">4</td><td class="ctr1" id="j8">1</td><td class="ctr2" id="k8">1</td></tr><tr><td id="a5"><a href="StorageService.java.html#L224" class="el_method">getFileExtension(String)</a></td><td class="bar" id="b9"><img src="../jacoco-resources/redbar.gif" width="31" height="10" title="20" alt="20"/></td><td class="ctr2" id="c11">0%</td><td class="bar" id="d4"><img src="../jacoco-resources/redbar.gif" width="90" height="10" title="6" alt="6"/></td><td class="ctr2" id="e5">0%</td><td class="ctr1" id="f4">4</td><td class="ctr2" id="g4">4</td><td class="ctr1" id="h8">4</td><td class="ctr2" id="i8">4</td><td class="ctr1" id="j9">1</td><td class="ctr2" id="k9">1</td></tr><tr><td id="a1"><a href="StorageService.java.html#L203" class="el_method">downloadFileLocally(String)</a></td><td class="bar" id="b10"><img src="../jacoco-resources/redbar.gif" width="30" height="10" title="19" alt="19"/></td><td class="ctr2" id="c12">0%</td><td class="bar" id="d6"><img src="../jacoco-resources/redbar.gif" width="30" height="10" title="2" alt="2"/></td><td class="ctr2" id="e6">0%</td><td class="ctr1" id="f6">2</td><td class="ctr2" id="g6">2</td><td class="ctr1" id="h9">4</td><td class="ctr2" id="i9">4</td><td class="ctr1" id="j10">1</td><td class="ctr2" id="k10">1</td></tr><tr><td id="a6"><a href="StorageService.java.html#L242" class="el_method">getNextVersionNumber(Document)</a></td><td class="bar" id="b11"><img src="../jacoco-resources/redbar.gif" width="9" height="10" title="6" alt="6"/></td><td class="ctr2" id="c13">0%</td><td class="bar" id="d11"/><td class="ctr2" id="e11">n/a</td><td class="ctr1" id="f11">1</td><td class="ctr2" id="g11">1</td><td class="ctr1" id="h11">1</td><td class="ctr2" id="i11">1</td><td class="ctr1" id="j11">1</td><td class="ctr2" id="k11">1</td></tr><tr><td id="a8"><a href="StorageService.java.html#L57" class="el_method">StorageService(StorageConfigurationProperties)</a></td><td class="bar" id="b12"><img src="../jacoco-resources/greenbar.gif" width="9" height="10" title="6" alt="6"/></td><td class="ctr2" id="c0">100%</td><td class="bar" id="d12"/><td class="ctr2" id="e12">n/a</td><td class="ctr1" id="f12">0</td><td class="ctr2" id="g12">1</td><td class="ctr1" id="h12">0</td><td class="ctr2" id="i12">1</td><td class="ctr1" id="j12">0</td><td class="ctr2" id="k12">1</td></tr><tr><td id="a7"><a href="StorageService.java.html#L60" class="el_method">static {...}</a></td><td class="bar" id="b13"><img src="../jacoco-resources/greenbar.gif" width="6" height="10" title="4" alt="4"/></td><td class="ctr2" id="c1">100%</td><td class="bar" id="d13"/><td class="ctr2" id="e13">n/a</td><td class="ctr1" id="f13">0</td><td class="ctr2" id="g13">1</td><td class="ctr1" id="h13">0</td><td class="ctr2" id="i13">1</td><td class="ctr1" id="j13">0</td><td class="ctr2" id="k13">1</td></tr></tbody></table><div class="footer"><span class="right">Created with <a href="http://www.jacoco.org/jacoco">JaCoCo</a> 0.8.10.202304240956</span></div></body></html>