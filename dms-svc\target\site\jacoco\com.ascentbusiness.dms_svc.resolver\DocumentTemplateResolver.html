<?xml version="1.0" encoding="UTF-8"?><!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Strict//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-strict.dtd"><html xmlns="http://www.w3.org/1999/xhtml" lang="en"><head><meta http-equiv="Content-Type" content="text/html;charset=UTF-8"/><link rel="stylesheet" href="../jacoco-resources/report.css" type="text/css"/><link rel="shortcut icon" href="../jacoco-resources/report.gif" type="image/gif"/><title>DocumentTemplateResolver</title><script type="text/javascript" src="../jacoco-resources/sort.js"></script></head><body onload="initialSort(['breadcrumb'])"><div class="breadcrumb" id="breadcrumb"><span class="info"><a href="../jacoco-sessions.html" class="el_session">Sessions</a></span><a href="../index.html" class="el_report">dms-svc</a> &gt; <a href="index.html" class="el_package">com.ascentbusiness.dms_svc.resolver</a> &gt; <span class="el_class">DocumentTemplateResolver</span></div><h1>DocumentTemplateResolver</h1><table class="coverage" cellspacing="0" id="coveragetable"><thead><tr><td class="sortable" id="a" onclick="toggleSort(this)">Element</td><td class="down sortable bar" id="b" onclick="toggleSort(this)">Missed Instructions</td><td class="sortable ctr2" id="c" onclick="toggleSort(this)">Cov.</td><td class="sortable bar" id="d" onclick="toggleSort(this)">Missed Branches</td><td class="sortable ctr2" id="e" onclick="toggleSort(this)">Cov.</td><td class="sortable ctr1" id="f" onclick="toggleSort(this)">Missed</td><td class="sortable ctr2" id="g" onclick="toggleSort(this)">Cxty</td><td class="sortable ctr1" id="h" onclick="toggleSort(this)">Missed</td><td class="sortable ctr2" id="i" onclick="toggleSort(this)">Lines</td><td class="sortable ctr1" id="j" onclick="toggleSort(this)">Missed</td><td class="sortable ctr2" id="k" onclick="toggleSort(this)">Methods</td></tr></thead><tfoot><tr><td>Total</td><td class="bar">1,655 of 1,668</td><td class="ctr2">0%</td><td class="bar">151 of 151</td><td class="ctr2">0%</td><td class="ctr1">131</td><td class="ctr2">133</td><td class="ctr1">412</td><td class="ctr2">414</td><td class="ctr1">53</td><td class="ctr2">55</td></tr></tfoot><tbody><tr><td id="a2"><a href="DocumentTemplateResolver.java.html#L674" class="el_method">bulkTemplateAction(BulkActionInput)</a></td><td class="bar" id="b0"><img src="../jacoco-resources/redbar.gif" width="120" height="10" title="187" alt="187"/></td><td class="ctr2" id="c2">0%</td><td class="bar" id="d3"><img src="../jacoco-resources/redbar.gif" width="36" height="10" title="11" alt="11"/></td><td class="ctr2" id="e0">0%</td><td class="ctr1" id="f3">8</td><td class="ctr2" id="g3">8</td><td class="ctr1" id="h1">51</td><td class="ctr2" id="i1">51</td><td class="ctr1" id="j0">1</td><td class="ctr2" id="k0">1</td></tr><tr><td id="a54"><a href="DocumentTemplateResolver.java.html#L361" class="el_method">validateTemplate(Long)</a></td><td class="bar" id="b1"><img src="../jacoco-resources/redbar.gif" width="111" height="10" title="174" alt="174"/></td><td class="ctr2" id="c3">0%</td><td class="bar" id="d2"><img src="../jacoco-resources/redbar.gif" width="66" height="10" title="20" alt="20"/></td><td class="ctr2" id="e1">0%</td><td class="ctr1" id="f2">11</td><td class="ctr2" id="g2">11</td><td class="ctr1" id="h0">63</td><td class="ctr2" id="i0">63</td><td class="ctr1" id="j1">1</td><td class="ctr2" id="k1">1</td></tr><tr><td id="a48"><a href="DocumentTemplateResolver.java.html#L180" class="el_method">searchTemplates(TemplateSearchInput)</a></td><td class="bar" id="b2"><img src="../jacoco-resources/redbar.gif" width="90" height="10" title="141" alt="141"/></td><td class="ctr2" id="c4">0%</td><td class="bar" id="d1"><img src="../jacoco-resources/redbar.gif" width="80" height="10" title="24" alt="24"/></td><td class="ctr2" id="e2">0%</td><td class="ctr1" id="f1">13</td><td class="ctr2" id="g1">13</td><td class="ctr1" id="h2">35</td><td class="ctr2" id="i2">35</td><td class="ctr1" id="j2">1</td><td class="ctr2" id="k2">1</td></tr><tr><td id="a21"><a href="DocumentTemplateResolver.java.html#L455" class="el_method">getTemplateHealth(Long)</a></td><td class="bar" id="b3"><img src="../jacoco-resources/redbar.gif" width="88" height="10" title="138" alt="138"/></td><td class="ctr2" id="c5">0%</td><td class="bar" id="d0"><img src="../jacoco-resources/redbar.gif" width="120" height="10" title="36" alt="36"/></td><td class="ctr2" id="e3">0%</td><td class="ctr1" id="f0">19</td><td class="ctr2" id="g0">19</td><td class="ctr1" id="h3">29</td><td class="ctr2" id="i3">29</td><td class="ctr1" id="j3">1</td><td class="ctr2" id="k3">1</td></tr><tr><td id="a3"><a href="DocumentTemplateResolver.java.html#L307" class="el_method">convertToTemplateStatistics(DocumentTemplateService.TemplateStatistics)</a></td><td class="bar" id="b4"><img src="../jacoco-resources/redbar.gif" width="53" height="10" title="83" alt="83"/></td><td class="ctr2" id="c6">0%</td><td class="bar" id="d22"/><td class="ctr2" id="e22">n/a</td><td class="ctr1" id="f22">1</td><td class="ctr2" id="g22">1</td><td class="ctr1" id="h4">28</td><td class="ctr2" id="i4">28</td><td class="ctr1" id="j4">1</td><td class="ctr2" id="k4">1</td></tr><tr><td id="a1"><a href="DocumentTemplateResolver.java.html#L261" class="el_method">buildSearchFacets(List)</a></td><td class="bar" id="b5"><img src="../jacoco-resources/redbar.gif" width="44" height="10" title="70" alt="70"/></td><td class="ctr2" id="c7">0%</td><td class="bar" id="d23"/><td class="ctr2" id="e23">n/a</td><td class="ctr1" id="f23">1</td><td class="ctr2" id="g23">1</td><td class="ctr1" id="h5">25</td><td class="ctr2" id="i5">25</td><td class="ctr1" id="j5">1</td><td class="ctr2" id="k5">1</td></tr><tr><td id="a45"><a href="DocumentTemplateResolver.java.html#L614" class="el_method">previewTemplate(TemplatePreviewInput)</a></td><td class="bar" id="b6"><img src="../jacoco-resources/redbar.gif" width="44" height="10" title="69" alt="69"/></td><td class="ctr2" id="c8">0%</td><td class="bar" id="d24"/><td class="ctr2" id="e24">n/a</td><td class="ctr1" id="f24">1</td><td class="ctr2" id="g24">1</td><td class="ctr1" id="h6">19</td><td class="ctr2" id="i6">19</td><td class="ctr1" id="j6">1</td><td class="ctr2" id="k6">1</td></tr><tr><td id="a6"><a href="DocumentTemplateResolver.java.html#L772" class="el_method">createPageable(DocumentTemplateResolver.TemplatePaginationInput)</a></td><td class="bar" id="b7"><img src="../jacoco-resources/redbar.gif" width="36" height="10" title="57" alt="57"/></td><td class="ctr2" id="c9">0%</td><td class="bar" id="d4"><img src="../jacoco-resources/redbar.gif" width="33" height="10" title="10" alt="10"/></td><td class="ctr2" id="e4">0%</td><td class="ctr1" id="f4">6</td><td class="ctr2" id="g4">6</td><td class="ctr1" id="h13">8</td><td class="ctr2" id="i13">8</td><td class="ctr1" id="j7">1</td><td class="ctr2" id="k7">1</td></tr><tr><td id="a23"><a href="DocumentTemplateResolver.java.html#L116" class="el_method">getTemplatesByOwner(String, DocumentTemplateResolver.TemplatePaginationInput)</a></td><td class="bar" id="b8"><img src="../jacoco-resources/redbar.gif" width="34" height="10" title="53" alt="53"/></td><td class="ctr2" id="c10">0%</td><td class="bar" id="d11"><img src="../jacoco-resources/redbar.gif" width="6" height="10" title="2" alt="2"/></td><td class="ctr2" id="e5">0%</td><td class="ctr1" id="f11">2</td><td class="ctr2" id="g11">2</td><td class="ctr1" id="h8">13</td><td class="ctr2" id="i8">13</td><td class="ctr1" id="j8">1</td><td class="ctr2" id="k8">1</td></tr><tr><td id="a10"><a href="DocumentTemplateResolver.java.html#L644" class="el_method">duplicateTemplate(Long, String)</a></td><td class="bar" id="b9"><img src="../jacoco-resources/redbar.gif" width="30" height="10" title="48" alt="48"/></td><td class="ctr2" id="c11">0%</td><td class="bar" id="d25"/><td class="ctr2" id="e25">n/a</td><td class="ctr1" id="f25">1</td><td class="ctr2" id="g25">1</td><td class="ctr1" id="h7">14</td><td class="ctr2" id="i7">14</td><td class="ctr1" id="j9">1</td><td class="ctr2" id="k9">1</td></tr><tr><td id="a14"><a href="DocumentTemplateResolver.java.html#L67" class="el_method">getDocumentTemplates(DocumentTemplateResolver.TemplatePaginationInput)</a></td><td class="bar" id="b10"><img src="../jacoco-resources/redbar.gif" width="28" height="10" title="44" alt="44"/></td><td class="ctr2" id="c12">0%</td><td class="bar" id="d26"/><td class="ctr2" id="e26">n/a</td><td class="ctr1" id="f26">1</td><td class="ctr2" id="g26">1</td><td class="ctr1" id="h11">12</td><td class="ctr2" id="i11">12</td><td class="ctr1" id="j10">1</td><td class="ctr2" id="k10">1</td></tr><tr><td id="a5"><a href="DocumentTemplateResolver.java.html#L524" class="el_method">createDocumentTemplate(DocumentTemplateResolver.DocumentTemplateInput)</a></td><td class="bar" id="b11"><img src="../jacoco-resources/redbar.gif" width="28" height="10" title="44" alt="44"/></td><td class="ctr2" id="c13">0%</td><td class="bar" id="d12"><img src="../jacoco-resources/redbar.gif" width="6" height="10" title="2" alt="2"/></td><td class="ctr2" id="e6">0%</td><td class="ctr1" id="f12">2</td><td class="ctr2" id="g12">2</td><td class="ctr1" id="h9">13</td><td class="ctr2" id="i9">13</td><td class="ctr1" id="j11">1</td><td class="ctr2" id="k11">1</td></tr><tr><td id="a53"><a href="DocumentTemplateResolver.java.html#L546" class="el_method">updateDocumentTemplate(Long, DocumentTemplateResolver.DocumentTemplateInput)</a></td><td class="bar" id="b12"><img src="../jacoco-resources/redbar.gif" width="28" height="10" title="44" alt="44"/></td><td class="ctr2" id="c14">0%</td><td class="bar" id="d13"><img src="../jacoco-resources/redbar.gif" width="6" height="10" title="2" alt="2"/></td><td class="ctr2" id="e7">0%</td><td class="ctr1" id="f13">2</td><td class="ctr2" id="g13">2</td><td class="ctr1" id="h10">13</td><td class="ctr2" id="i10">13</td><td class="ctr1" id="j12">1</td><td class="ctr2" id="k12">1</td></tr><tr><td id="a44"><a href="DocumentTemplateResolver.java.html#L960" class="el_method">popularityScore(DocumentTemplate)</a></td><td class="bar" id="b13"><img src="../jacoco-resources/redbar.gif" width="28" height="10" title="44" alt="44"/></td><td class="ctr2" id="c15">0%</td><td class="bar" id="d5"><img src="../jacoco-resources/redbar.gif" width="26" height="10" title="8" alt="8"/></td><td class="ctr2" id="e8">0%</td><td class="ctr1" id="f5">5</td><td class="ctr2" id="g5">5</td><td class="ctr1" id="h12">11</td><td class="ctr2" id="i12">11</td><td class="ctr1" id="j13">1</td><td class="ctr2" id="k13">1</td></tr><tr><td id="a20"><a href="DocumentTemplateResolver.java.html#L246" class="el_method">getTemplateComparator(String)</a></td><td class="bar" id="b14"><img src="../jacoco-resources/redbar.gif" width="18" height="10" title="29" alt="29"/></td><td class="ctr2" id="c16">0%</td><td class="bar" id="d6"><img src="../jacoco-resources/redbar.gif" width="13" height="10" title="4" alt="4"/></td><td class="ctr2" id="e9">0%</td><td class="ctr1" id="f6">4</td><td class="ctr2" id="g6">4</td><td class="ctr1" id="h17">5</td><td class="ctr2" id="i17">5</td><td class="ctr1" id="j14">1</td><td class="ctr2" id="k14">1</td></tr><tr><td id="a11"><a href="DocumentTemplateResolver.java.html#L86" class="el_method">getAccessibleTemplates(String, String)</a></td><td class="bar" id="b15"><img src="../jacoco-resources/redbar.gif" width="17" height="10" title="27" alt="27"/></td><td class="ctr2" id="c17">0%</td><td class="bar" id="d7"><img src="../jacoco-resources/redbar.gif" width="13" height="10" title="4" alt="4"/></td><td class="ctr2" id="e10">0%</td><td class="ctr1" id="f7">3</td><td class="ctr2" id="g7">3</td><td class="ctr1" id="h19">4</td><td class="ctr2" id="i19">4</td><td class="ctr1" id="j15">1</td><td class="ctr2" id="k15">1</td></tr><tr><td id="a26"><a href="DocumentTemplateResolver.java.html#L168" class="el_method">getTemplateStatistics(OffsetDateTime, OffsetDateTime, List)</a></td><td class="bar" id="b16"><img src="../jacoco-resources/redbar.gif" width="16" height="10" title="25" alt="25"/></td><td class="ctr2" id="c18">0%</td><td class="bar" id="d27"/><td class="ctr2" id="e27">n/a</td><td class="ctr1" id="f27">1</td><td class="ctr2" id="g27">1</td><td class="ctr1" id="h23">3</td><td class="ctr2" id="i23">3</td><td class="ctr1" id="j16">1</td><td class="ctr2" id="k16">1</td></tr><tr><td id="a36"><a href="DocumentTemplateResolver.java.html#L318" class="el_method">lambda$convertToTemplateStatistics$14(Object[])</a></td><td class="bar" id="b17"><img src="../jacoco-resources/redbar.gif" width="13" height="10" title="21" alt="21"/></td><td class="ctr2" id="c19">0%</td><td class="bar" id="d28"/><td class="ctr2" id="e28">n/a</td><td class="ctr1" id="f28">1</td><td class="ctr2" id="g28">1</td><td class="ctr1" id="h14">6</td><td class="ctr2" id="i14">6</td><td class="ctr1" id="j17">1</td><td class="ctr2" id="k17">1</td></tr><tr><td id="a35"><a href="DocumentTemplateResolver.java.html#L308" class="el_method">lambda$convertToTemplateStatistics$13(Object[])</a></td><td class="bar" id="b18"><img src="../jacoco-resources/redbar.gif" width="13" height="10" title="21" alt="21"/></td><td class="ctr2" id="c20">0%</td><td class="bar" id="d29"/><td class="ctr2" id="e29">n/a</td><td class="ctr1" id="f29">1</td><td class="ctr2" id="g29">1</td><td class="ctr1" id="h15">6</td><td class="ctr2" id="i15">6</td><td class="ctr1" id="j18">1</td><td class="ctr2" id="k18">1</td></tr><tr><td id="a4"><a href="DocumentTemplateResolver.java.html#L598" class="el_method">createDocumentFromTemplate(DocumentTemplateResolver.CreateDocumentFromTemplateInput)</a></td><td class="bar" id="b19"><img src="../jacoco-resources/redbar.gif" width="12" height="10" title="20" alt="20"/></td><td class="ctr2" id="c21">0%</td><td class="bar" id="d30"/><td class="ctr2" id="e30">n/a</td><td class="ctr1" id="f30">1</td><td class="ctr2" id="g30">1</td><td class="ctr1" id="h16">6</td><td class="ctr2" id="i16">6</td><td class="ctr1" id="j19">1</td><td class="ctr2" id="k19">1</td></tr><tr><td id="a37"><a href="DocumentTemplateResolver.java.html#L328" class="el_method">lambda$convertToTemplateStatistics$15(Object[])</a></td><td class="bar" id="b20"><img src="../jacoco-resources/redbar.gif" width="11" height="10" title="18" alt="18"/></td><td class="ctr2" id="c22">0%</td><td class="bar" id="d31"/><td class="ctr2" id="e31">n/a</td><td class="ctr1" id="f31">1</td><td class="ctr2" id="g31">1</td><td class="ctr1" id="h18">5</td><td class="ctr2" id="i18">5</td><td class="ctr1" id="j20">1</td><td class="ctr2" id="k20">1</td></tr><tr><td id="a16"><a href="DocumentTemplateResolver.java.html#L135" class="el_method">getPopularTemplates(Integer)</a></td><td class="bar" id="b21"><img src="../jacoco-resources/redbar.gif" width="10" height="10" title="16" alt="16"/></td><td class="ctr2" id="c23">0%</td><td class="bar" id="d14"><img src="../jacoco-resources/redbar.gif" width="6" height="10" title="2" alt="2"/></td><td class="ctr2" id="e11">0%</td><td class="ctr1" id="f14">2</td><td class="ctr2" id="g14">2</td><td class="ctr1" id="h24">3</td><td class="ctr2" id="i24">3</td><td class="ctr1" id="j21">1</td><td class="ctr2" id="k21">1</td></tr><tr><td id="a19"><a href="DocumentTemplateResolver.java.html#L143" class="el_method">getRecentlyUsedTemplates(Integer)</a></td><td class="bar" id="b22"><img src="../jacoco-resources/redbar.gif" width="10" height="10" title="16" alt="16"/></td><td class="ctr2" id="c24">0%</td><td class="bar" id="d15"><img src="../jacoco-resources/redbar.gif" width="6" height="10" title="2" alt="2"/></td><td class="ctr2" id="e12">0%</td><td class="ctr1" id="f15">2</td><td class="ctr2" id="g15">2</td><td class="ctr1" id="h25">3</td><td class="ctr2" id="i25">3</td><td class="ctr1" id="j22">1</td><td class="ctr2" id="k22">1</td></tr><tr><td id="a8"><a href="DocumentTemplateResolver.java.html#L666" class="el_method">deleteDocumentTemplate(Long)</a></td><td class="bar" id="b23"><img src="../jacoco-resources/redbar.gif" width="8" height="10" title="14" alt="14"/></td><td class="ctr2" id="c25">0%</td><td class="bar" id="d32"/><td class="ctr2" id="e32">n/a</td><td class="ctr1" id="f32">1</td><td class="ctr2" id="g32">1</td><td class="ctr1" id="h26">3</td><td class="ctr2" id="i26">3</td><td class="ctr1" id="j23">1</td><td class="ctr2" id="k23">1</td></tr><tr><td id="a42"><a href="DocumentTemplateResolver.java.html#L212" class="el_method">lambda$searchTemplates$3(TemplateSearchInput, DocumentTemplate)</a></td><td class="bar" id="b24"><img src="../jacoco-resources/redbar.gif" width="8" height="10" title="14" alt="14"/></td><td class="ctr2" id="c26">0%</td><td class="bar" id="d8"><img src="../jacoco-resources/redbar.gif" width="13" height="10" title="4" alt="4"/></td><td class="ctr2" id="e13">0%</td><td class="ctr1" id="f8">3</td><td class="ctr2" id="g8">3</td><td class="ctr1" id="h27">2</td><td class="ctr2" id="i27">2</td><td class="ctr1" id="j24">1</td><td class="ctr2" id="k24">1</td></tr><tr><td id="a41"><a href="DocumentTemplateResolver.java.html#L204" class="el_method">lambda$searchTemplates$2(TemplateSearchInput, DocumentTemplate)</a></td><td class="bar" id="b25"><img src="../jacoco-resources/redbar.gif" width="8" height="10" title="14" alt="14"/></td><td class="ctr2" id="c27">0%</td><td class="bar" id="d9"><img src="../jacoco-resources/redbar.gif" width="13" height="10" title="4" alt="4"/></td><td class="ctr2" id="e14">0%</td><td class="ctr1" id="f9">3</td><td class="ctr2" id="g9">3</td><td class="ctr1" id="h28">2</td><td class="ctr2" id="i28">2</td><td class="ctr1" id="j25">1</td><td class="ctr2" id="k25">1</td></tr><tr><td id="a40"><a href="DocumentTemplateResolver.java.html#L196" class="el_method">lambda$searchTemplates$1(TemplateSearchInput, DocumentTemplate)</a></td><td class="bar" id="b26"><img src="../jacoco-resources/redbar.gif" width="8" height="10" title="14" alt="14"/></td><td class="ctr2" id="c28">0%</td><td class="bar" id="d10"><img src="../jacoco-resources/redbar.gif" width="13" height="10" title="4" alt="4"/></td><td class="ctr2" id="e15">0%</td><td class="ctr1" id="f10">3</td><td class="ctr2" id="g10">3</td><td class="ctr1" id="h29">2</td><td class="ctr2" id="i29">2</td><td class="ctr1" id="j26">1</td><td class="ctr2" id="k26">1</td></tr><tr><td id="a0"><a href="DocumentTemplateResolver.java.html#L575" class="el_method">approveTemplate(Long, String)</a></td><td class="bar" id="b27"><img src="../jacoco-resources/redbar.gif" width="8" height="10" title="13" alt="13"/></td><td class="ctr2" id="c29">0%</td><td class="bar" id="d33"/><td class="ctr2" id="e33">n/a</td><td class="ctr1" id="f33">1</td><td class="ctr2" id="g33">1</td><td class="ctr1" id="h30">2</td><td class="ctr2" id="i30">2</td><td class="ctr1" id="j27">1</td><td class="ctr2" id="k27">1</td></tr><tr><td id="a46"><a href="DocumentTemplateResolver.java.html#L568" class="el_method">publishTemplate(Long)</a></td><td class="bar" id="b28"><img src="../jacoco-resources/redbar.gif" width="7" height="10" title="12" alt="12"/></td><td class="ctr2" id="c30">0%</td><td class="bar" id="d34"/><td class="ctr2" id="e34">n/a</td><td class="ctr1" id="f34">1</td><td class="ctr2" id="g34">1</td><td class="ctr1" id="h31">2</td><td class="ctr2" id="i31">2</td><td class="ctr1" id="j28">1</td><td class="ctr2" id="k28">1</td></tr><tr><td id="a47"><a href="DocumentTemplateResolver.java.html#L582" class="el_method">rejectTemplate(Long)</a></td><td class="bar" id="b29"><img src="../jacoco-resources/redbar.gif" width="7" height="10" title="12" alt="12"/></td><td class="ctr2" id="c31">0%</td><td class="bar" id="d35"/><td class="ctr2" id="e35">n/a</td><td class="ctr1" id="f35">1</td><td class="ctr2" id="g35">1</td><td class="ctr1" id="h32">2</td><td class="ctr2" id="i32">2</td><td class="ctr1" id="j29">1</td><td class="ctr2" id="k29">1</td></tr><tr><td id="a7"><a href="DocumentTemplateResolver.java.html#L589" class="el_method">deactivateTemplate(Long)</a></td><td class="bar" id="b30"><img src="../jacoco-resources/redbar.gif" width="7" height="10" title="12" alt="12"/></td><td class="ctr2" id="c32">0%</td><td class="bar" id="d36"/><td class="ctr2" id="e36">n/a</td><td class="ctr1" id="f36">1</td><td class="ctr2" id="g36">1</td><td class="ctr1" id="h33">2</td><td class="ctr2" id="i33">2</td><td class="ctr1" id="j30">1</td><td class="ctr2" id="k30">1</td></tr><tr><td id="a29"><a href="DocumentTemplateResolver.java.html#L290" class="el_method">lambda$buildSearchFacets$12(Map.Entry)</a></td><td class="bar" id="b31"><img src="../jacoco-resources/redbar.gif" width="7" height="10" title="11" alt="11"/></td><td class="ctr2" id="c33">0%</td><td class="bar" id="d37"/><td class="ctr2" id="e37">n/a</td><td class="ctr1" id="f37">1</td><td class="ctr2" id="g37">1</td><td class="ctr1" id="h20">4</td><td class="ctr2" id="i20">4</td><td class="ctr1" id="j31">1</td><td class="ctr2" id="k31">1</td></tr><tr><td id="a34"><a href="DocumentTemplateResolver.java.html#L278" class="el_method">lambda$buildSearchFacets$9(Map.Entry)</a></td><td class="bar" id="b32"><img src="../jacoco-resources/redbar.gif" width="7" height="10" title="11" alt="11"/></td><td class="ctr2" id="c34">0%</td><td class="bar" id="d38"/><td class="ctr2" id="e38">n/a</td><td class="ctr1" id="f38">1</td><td class="ctr2" id="g38">1</td><td class="ctr1" id="h21">4</td><td class="ctr2" id="i21">4</td><td class="ctr1" id="j32">1</td><td class="ctr2" id="k32">1</td></tr><tr><td id="a31"><a href="DocumentTemplateResolver.java.html#L266" class="el_method">lambda$buildSearchFacets$6(Map.Entry)</a></td><td class="bar" id="b33"><img src="../jacoco-resources/redbar.gif" width="7" height="10" title="11" alt="11"/></td><td class="ctr2" id="c35">0%</td><td class="bar" id="d39"/><td class="ctr2" id="e39">n/a</td><td class="ctr1" id="f39">1</td><td class="ctr2" id="g39">1</td><td class="ctr1" id="h22">4</td><td class="ctr2" id="i22">4</td><td class="ctr1" id="j33">1</td><td class="ctr2" id="k33">1</td></tr><tr><td id="a15"><a href="DocumentTemplateResolver.java.html#L755" class="el_method">getFields(DocumentTemplate)</a></td><td class="bar" id="b34"><img src="../jacoco-resources/redbar.gif" width="6" height="10" title="10" alt="10"/></td><td class="ctr2" id="c36">0%</td><td class="bar" id="d16"><img src="../jacoco-resources/redbar.gif" width="6" height="10" title="2" alt="2"/></td><td class="ctr2" id="e16">0%</td><td class="ctr1" id="f16">2</td><td class="ctr2" id="g16">2</td><td class="ctr1" id="h40">1</td><td class="ctr2" id="i40">1</td><td class="ctr1" id="j34">1</td><td class="ctr2" id="k34">1</td></tr><tr><td id="a12"><a href="DocumentTemplateResolver.java.html#L760" class="el_method">getDocumentsCreated(DocumentTemplate)</a></td><td class="bar" id="b35"><img src="../jacoco-resources/redbar.gif" width="6" height="10" title="10" alt="10"/></td><td class="ctr2" id="c37">0%</td><td class="bar" id="d17"><img src="../jacoco-resources/redbar.gif" width="6" height="10" title="2" alt="2"/></td><td class="ctr2" id="e17">0%</td><td class="ctr1" id="f17">2</td><td class="ctr2" id="g17">2</td><td class="ctr1" id="h41">1</td><td class="ctr2" id="i41">1</td><td class="ctr1" id="j35">1</td><td class="ctr2" id="k35">1</td></tr><tr><td id="a13"><a href="DocumentTemplateResolver.java.html#L60" class="el_method">getDocumentTemplate(Long)</a></td><td class="bar" id="b36"><img src="../jacoco-resources/redbar.gif" width="5" height="10" title="9" alt="9"/></td><td class="ctr2" id="c38">0%</td><td class="bar" id="d40"/><td class="ctr2" id="e40">n/a</td><td class="ctr1" id="f40">1</td><td class="ctr2" id="g40">1</td><td class="ctr1" id="h34">2</td><td class="ctr2" id="i34">2</td><td class="ctr1" id="j36">1</td><td class="ctr2" id="k36">1</td></tr><tr><td id="a22"><a href="DocumentTemplateResolver.java.html#L95" class="el_method">getTemplatesByCategory(String)</a></td><td class="bar" id="b37"><img src="../jacoco-resources/redbar.gif" width="5" height="10" title="9" alt="9"/></td><td class="ctr2" id="c39">0%</td><td class="bar" id="d41"/><td class="ctr2" id="e41">n/a</td><td class="ctr1" id="f41">1</td><td class="ctr2" id="g41">1</td><td class="ctr1" id="h35">2</td><td class="ctr2" id="i35">2</td><td class="ctr1" id="j37">1</td><td class="ctr2" id="k37">1</td></tr><tr><td id="a24"><a href="DocumentTemplateResolver.java.html#L102" class="el_method">getTemplatesByType(TemplateType)</a></td><td class="bar" id="b38"><img src="../jacoco-resources/redbar.gif" width="5" height="10" title="9" alt="9"/></td><td class="ctr2" id="c40">0%</td><td class="bar" id="d42"/><td class="ctr2" id="e42">n/a</td><td class="ctr1" id="f42">1</td><td class="ctr2" id="g42">1</td><td class="ctr1" id="h36">2</td><td class="ctr2" id="i36">2</td><td class="ctr1" id="j38">1</td><td class="ctr2" id="k38">1</td></tr><tr><td id="a49"><a href="DocumentTemplateResolver.java.html#L109" class="el_method">searchTemplatesByName(String)</a></td><td class="bar" id="b39"><img src="../jacoco-resources/redbar.gif" width="5" height="10" title="9" alt="9"/></td><td class="ctr2" id="c41">0%</td><td class="bar" id="d43"/><td class="ctr2" id="e43">n/a</td><td class="ctr1" id="f43">1</td><td class="ctr2" id="g43">1</td><td class="ctr1" id="h37">2</td><td class="ctr2" id="i37">2</td><td class="ctr1" id="j39">1</td><td class="ctr2" id="k39">1</td></tr><tr><td id="a38"><a href="DocumentTemplateResolver.java.html#L353" class="el_method">lambda$convertToTemplateStatistics$16(TemplateApprovalStats)</a></td><td class="bar" id="b40"><img src="../jacoco-resources/redbar.gif" width="5" height="10" title="8" alt="8"/></td><td class="ctr2" id="c42">0%</td><td class="bar" id="d18"><img src="../jacoco-resources/redbar.gif" width="6" height="10" title="2" alt="2"/></td><td class="ctr2" id="e18">0%</td><td class="ctr1" id="f18">2</td><td class="ctr2" id="g18">2</td><td class="ctr1" id="h42">1</td><td class="ctr2" id="i42">1</td><td class="ctr1" id="j40">1</td><td class="ctr2" id="k40">1</td></tr><tr><td id="a18"><a href="DocumentTemplateResolver.java.html#L151" class="el_method">getPublishedTemplates()</a></td><td class="bar" id="b41"><img src="../jacoco-resources/redbar.gif" width="4" height="10" title="7" alt="7"/></td><td class="ctr2" id="c43">0%</td><td class="bar" id="d44"/><td class="ctr2" id="e44">n/a</td><td class="ctr1" id="f44">1</td><td class="ctr2" id="g44">1</td><td class="ctr1" id="h38">2</td><td class="ctr2" id="i38">2</td><td class="ctr1" id="j41">1</td><td class="ctr2" id="k41">1</td></tr><tr><td id="a25"><a href="DocumentTemplateResolver.java.html#L158" class="el_method">getTemplatesPendingApproval()</a></td><td class="bar" id="b42"><img src="../jacoco-resources/redbar.gif" width="4" height="10" title="7" alt="7"/></td><td class="ctr2" id="c44">0%</td><td class="bar" id="d45"/><td class="ctr2" id="e45">n/a</td><td class="ctr1" id="f45">1</td><td class="ctr2" id="g45">1</td><td class="ctr1" id="h39">2</td><td class="ctr2" id="i39">2</td><td class="ctr1" id="j42">1</td><td class="ctr2" id="k42">1</td></tr><tr><td id="a27"><a href="DocumentTemplateResolver.java.html#L286" class="el_method">lambda$buildSearchFacets$10(DocumentTemplate)</a></td><td class="bar" id="b43"><img src="../jacoco-resources/redbar.gif" width="4" height="10" title="7" alt="7"/></td><td class="ctr2" id="c45">0%</td><td class="bar" id="d19"><img src="../jacoco-resources/redbar.gif" width="6" height="10" title="2" alt="2"/></td><td class="ctr2" id="e19">0%</td><td class="ctr1" id="f19">2</td><td class="ctr2" id="g19">2</td><td class="ctr1" id="h43">1</td><td class="ctr2" id="i43">1</td><td class="ctr1" id="j43">1</td><td class="ctr2" id="k43">1</td></tr><tr><td id="a32"><a href="DocumentTemplateResolver.java.html#L274" class="el_method">lambda$buildSearchFacets$7(DocumentTemplate)</a></td><td class="bar" id="b44"><img src="../jacoco-resources/redbar.gif" width="4" height="10" title="7" alt="7"/></td><td class="ctr2" id="c46">0%</td><td class="bar" id="d20"><img src="../jacoco-resources/redbar.gif" width="6" height="10" title="2" alt="2"/></td><td class="ctr2" id="e20">0%</td><td class="ctr1" id="f20">2</td><td class="ctr2" id="g20">2</td><td class="ctr1" id="h44">1</td><td class="ctr2" id="i44">1</td><td class="ctr1" id="j44">1</td><td class="ctr2" id="k44">1</td></tr><tr><td id="a30"><a href="DocumentTemplateResolver.java.html#L262" class="el_method">lambda$buildSearchFacets$5(DocumentTemplate)</a></td><td class="bar" id="b45"><img src="../jacoco-resources/redbar.gif" width="4" height="10" title="7" alt="7"/></td><td class="ctr2" id="c47">0%</td><td class="bar" id="d21"><img src="../jacoco-resources/redbar.gif" width="6" height="10" title="2" alt="2"/></td><td class="ctr2" id="e21">0%</td><td class="ctr1" id="f21">2</td><td class="ctr2" id="g21">2</td><td class="ctr1" id="h45">1</td><td class="ctr2" id="i45">1</td><td class="ctr1" id="j45">1</td><td class="ctr2" id="k45">1</td></tr><tr><td id="a43"><a href="DocumentTemplateResolver.java.html#L220" class="el_method">lambda$searchTemplates$4(TemplateSearchInput, DocumentTemplate)</a></td><td class="bar" id="b46"><img src="../jacoco-resources/redbar.gif" width="3" height="10" title="6" alt="6"/></td><td class="ctr2" id="c48">0%</td><td class="bar" id="d46"/><td class="ctr2" id="e46">n/a</td><td class="ctr1" id="f46">1</td><td class="ctr2" id="g46">1</td><td class="ctr1" id="h46">1</td><td class="ctr2" id="i46">1</td><td class="ctr1" id="j46">1</td><td class="ctr2" id="k46">1</td></tr><tr><td id="a39"><a href="DocumentTemplateResolver.java.html#L189" class="el_method">lambda$searchTemplates$0(TemplateSearchInput, DocumentTemplate)</a></td><td class="bar" id="b47"><img src="../jacoco-resources/redbar.gif" width="3" height="10" title="6" alt="6"/></td><td class="ctr2" id="c49">0%</td><td class="bar" id="d47"/><td class="ctr2" id="e47">n/a</td><td class="ctr1" id="f47">1</td><td class="ctr2" id="g47">1</td><td class="ctr1" id="h47">1</td><td class="ctr2" id="i47">1</td><td class="ctr1" id="j47">1</td><td class="ctr2" id="k47">1</td></tr><tr><td id="a28"><a href="DocumentTemplateResolver.java.html#L287" class="el_method">lambda$buildSearchFacets$11(DocumentTemplate)</a></td><td class="bar" id="b48"><img src="../jacoco-resources/redbar.gif" width="2" height="10" title="4" alt="4"/></td><td class="ctr2" id="c50">0%</td><td class="bar" id="d48"/><td class="ctr2" id="e48">n/a</td><td class="ctr1" id="f48">1</td><td class="ctr2" id="g48">1</td><td class="ctr1" id="h48">1</td><td class="ctr2" id="i48">1</td><td class="ctr1" id="j48">1</td><td class="ctr2" id="k48">1</td></tr><tr><td id="a33"><a href="DocumentTemplateResolver.java.html#L275" class="el_method">lambda$buildSearchFacets$8(DocumentTemplate)</a></td><td class="bar" id="b49"><img src="../jacoco-resources/redbar.gif" width="2" height="10" title="4" alt="4"/></td><td class="ctr2" id="c51">0%</td><td class="bar" id="d49"/><td class="ctr2" id="e49">n/a</td><td class="ctr1" id="f49">1</td><td class="ctr2" id="g49">1</td><td class="ctr1" id="h49">1</td><td class="ctr2" id="i49">1</td><td class="ctr1" id="j49">1</td><td class="ctr2" id="k49">1</td></tr><tr><td id="a51"><a href="DocumentTemplateResolver.java.html#L745" class="el_method">templateCreatedDate(DocumentTemplate)</a></td><td class="bar" id="b50"><img src="../jacoco-resources/redbar.gif" width="1" height="10" title="3" alt="3"/></td><td class="ctr2" id="c52">0%</td><td class="bar" id="d50"/><td class="ctr2" id="e50">n/a</td><td class="ctr1" id="f50">1</td><td class="ctr2" id="g50">1</td><td class="ctr1" id="h50">1</td><td class="ctr2" id="i50">1</td><td class="ctr1" id="j50">1</td><td class="ctr2" id="k50">1</td></tr><tr><td id="a52"><a href="DocumentTemplateResolver.java.html#L750" class="el_method">templateLastModifiedDate(DocumentTemplate)</a></td><td class="bar" id="b51"><img src="../jacoco-resources/redbar.gif" width="1" height="10" title="3" alt="3"/></td><td class="ctr2" id="c53">0%</td><td class="bar" id="d51"/><td class="ctr2" id="e51">n/a</td><td class="ctr1" id="f51">1</td><td class="ctr2" id="g51">1</td><td class="ctr1" id="h51">1</td><td class="ctr2" id="i51">1</td><td class="ctr1" id="j51">1</td><td class="ctr2" id="k51">1</td></tr><tr><td id="a17"><a href="DocumentTemplateResolver.java.html#L766" class="el_method">getPublishedBy(DocumentTemplate)</a></td><td class="bar" id="b52"><img src="../jacoco-resources/redbar.gif" width="1" height="10" title="3" alt="3"/></td><td class="ctr2" id="c54">0%</td><td class="bar" id="d52"/><td class="ctr2" id="e52">n/a</td><td class="ctr1" id="f52">1</td><td class="ctr2" id="g52">1</td><td class="ctr1" id="h52">1</td><td class="ctr2" id="i52">1</td><td class="ctr1" id="j52">1</td><td class="ctr2" id="k52">1</td></tr><tr><td id="a9"><a href="DocumentTemplateResolver.java.html#L48" class="el_method">DocumentTemplateResolver(DocumentTemplateService, UserContext)</a></td><td class="bar" id="b53"><img src="../jacoco-resources/greenbar.gif" width="5" height="10" title="9" alt="9"/></td><td class="ctr2" id="c0">100%</td><td class="bar" id="d53"/><td class="ctr2" id="e53">n/a</td><td class="ctr1" id="f53">0</td><td class="ctr2" id="g53">1</td><td class="ctr1" id="h53">0</td><td class="ctr2" id="i53">1</td><td class="ctr1" id="j53">0</td><td class="ctr2" id="k53">1</td></tr><tr><td id="a50"><a href="DocumentTemplateResolver.java.html#L49" class="el_method">static {...}</a></td><td class="bar" id="b54"><img src="../jacoco-resources/greenbar.gif" width="2" height="10" title="4" alt="4"/></td><td class="ctr2" id="c1">100%</td><td class="bar" id="d54"/><td class="ctr2" id="e54">n/a</td><td class="ctr1" id="f54">0</td><td class="ctr2" id="g54">1</td><td class="ctr1" id="h54">0</td><td class="ctr2" id="i54">1</td><td class="ctr1" id="j54">0</td><td class="ctr2" id="k54">1</td></tr></tbody></table><div class="footer"><span class="right">Created with <a href="http://www.jacoco.org/jacoco">JaCoCo</a> 0.8.10.202304240956</span></div></body></html>