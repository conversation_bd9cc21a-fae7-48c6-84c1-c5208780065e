<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>DMS Service Test Page</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
        }
        .section {
            margin-bottom: 30px;
            padding: 20px;
            border: 1px solid #ddd;
            border-radius: 5px;
        }
        .form-group {
            margin-bottom: 15px;
        }
        label {
            display: block;
            margin-bottom: 5px;
            font-weight: bold;
        }
        input, textarea, select, button {
            width: 100%;
            padding: 8px;
            margin-bottom: 10px;
            border: 1px solid #ddd;
            border-radius: 3px;
        }
        button {
            background-color: #007bff;
            color: white;
            border: none;
            cursor: pointer;
        }
        button:hover {
            background-color: #0056b3;
        }
        button:disabled {
            background-color: #ccc;
            cursor: not-allowed;
        }
        .result {
            background-color: #f8f9fa;
            padding: 10px;
            border-radius: 3px;
            margin-top: 10px;
            white-space: pre-wrap;
            font-family: monospace;
        }
        .error {
            background-color: #f8d7da;
            color: #721c24;
        }
        .success {
            background-color: #d4edda;
            color: #155724;
        }
        .hidden {
            display: none;
        }
    </style>
</head>
<body>
    <h1>DMS Service Test Page</h1>
    
    <!-- Authentication Section -->
    <div class="section">
        <h2>1. Authentication</h2>
        <div class="form-group">
            <label for="username">Username:</label>
            <input type="text" id="username" value="test-user" />
        </div>
        <button onclick="generateToken()">Generate Test Token</button>
        <div id="tokenResult" class="result hidden"></div>
    </div>

    <!-- Upload Section -->
    <div class="section">
        <h2>2. Upload Document</h2>
        <div class="form-group">
            <label for="fileInput">Select File:</label>
            <input type="file" id="fileInput" />
        </div>
        <div class="form-group">
            <label for="docName">Document Name:</label>
            <input type="text" id="docName" placeholder="Enter document name" />
        </div>
        <div class="form-group">
            <label for="docDescription">Description:</label>
            <textarea id="docDescription" placeholder="Enter description"></textarea>
        </div>
        <div class="form-group">
            <label for="docKeywords">Keywords (comma separated):</label>
            <input type="text" id="docKeywords" placeholder="keyword1, keyword2" />
        </div>
        <button onclick="uploadDocument()" id="uploadBtn" disabled>Upload Document</button>
        <div id="uploadResult" class="result hidden"></div>
    </div>

    <!-- Search Section -->
    <div class="section">
        <h2>3. Search Documents</h2>
        <div class="form-group">
            <label for="searchName">Search by Name:</label>
            <input type="text" id="searchName" placeholder="Enter document name to search" />
        </div>
        <button onclick="searchDocuments()">Search Documents</button>
        <div id="searchResult" class="result hidden"></div>
    </div>

    <!-- Download Section -->
    <div class="section">
        <h2>4. Download Document</h2>
        <div class="form-group">
            <label for="docId">Document ID:</label>
            <input type="text" id="docId" placeholder="Enter document ID" />
        </div>
        <button onclick="getDocumentInfo()">Get Document Info</button>
        <button onclick="downloadDocument()">Download Document</button>
        <div id="downloadResult" class="result hidden"></div>
    </div>

    <script>
        const GRAPHQL_URL = 'http://localhost:9093/graphql';
        const DOWNLOAD_URL = 'http://localhost:9093/api/v2/documents';
        let authToken = null;

        // Check if token exists in localStorage
        window.onload = function() {
            const savedToken = localStorage.getItem('dms_token');
            if (savedToken) {
                authToken = savedToken;
                updateAuthStatus();
            }
        };

        function updateAuthStatus() {
            const uploadBtn = document.getElementById('uploadBtn');
            if (authToken) {
                uploadBtn.disabled = false;
                showResult('tokenResult', 'Token loaded successfully', 'success');
            } else {
                uploadBtn.disabled = true;
            }
        }

        function showResult(elementId, message, type = '') {
            const element = document.getElementById(elementId);
            element.textContent = message;
            element.className = `result ${type}`;
            element.classList.remove('hidden');
        }

        async function generateToken() {
            const username = document.getElementById('username').value;
            
            const query = `
                mutation GenerateTestToken($input: JwtTokenRequest!) {
                    generateTestToken(input: $input) {
                        token
                        tokenType
                        expiresAt
                    }
                }
            `;

            const variables = {
                input: {
                    username: username,
                    roles: ["USER"],
                    permissions: ["READ", "WRITE"]
                }
            };

            try {
                const response = await fetch(GRAPHQL_URL, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({ query, variables })
                });

                const result = await response.json();
                
                if (result.data && result.data.generateTestToken) {
                    authToken = result.data.generateTestToken.token;
                    localStorage.setItem('dms_token', authToken);
                    showResult('tokenResult', `Token generated successfully!\nExpires: ${result.data.generateTestToken.expiresAt}`, 'success');
                    updateAuthStatus();
                } else {
                    showResult('tokenResult', `Error: ${JSON.stringify(result.errors || result)}`, 'error');
                }
            } catch (error) {
                showResult('tokenResult', `Network error: ${error.message}`, 'error');
            }
        }

        async function uploadDocument() {
            const fileInput = document.getElementById('fileInput');
            const file = fileInput.files[0];
            
            if (!file) {
                showResult('uploadResult', 'Please select a file', 'error');
                return;
            }

            if (!authToken) {
                showResult('uploadResult', 'Please generate a token first', 'error');
                return;
            }

            const docName = document.getElementById('docName').value || file.name;
            const docDescription = document.getElementById('docDescription').value;
            const docKeywords = document.getElementById('docKeywords').value.split(',').map(k => k.trim()).filter(k => k);

            const formData = new FormData();
            
            const operations = {
                query: `
                    mutation UploadDocumentEnhanced($input: EnhancedDocumentUploadInput!) {
                        uploadDocumentEnhanced(input: $input) {
                            success
                            document {
                                id
                                name
                                version
                                status
                                fileSize
                                mimeType
                                createdDate
                            }
                            uploadId
                            fileName
                            message
                        }
                    }
                `,
                variables: {
                    input: {
                        file: null,
                        name: docName,
                        description: docDescription,
                        keywords: docKeywords,
                        storageProvider: "LOCAL",
                        overrideFile: false,
                        validateFileType: true,
                        enableProgressTracking: true
                    }
                }
            };

            const map = { "0": ["variables.input.file"] };

            formData.append('operations', JSON.stringify(operations));
            formData.append('map', JSON.stringify(map));
            formData.append('0', file);

            try {
                const response = await fetch(GRAPHQL_URL, {
                    method: 'POST',
                    headers: {
                        'Authorization': `Bearer ${authToken}`
                    },
                    body: formData
                });

                const result = await response.json();
                
                if (result.data && result.data.uploadDocumentEnhanced) {
                    const uploadResult = result.data.uploadDocumentEnhanced;
                    if (uploadResult.success) {
                        showResult('uploadResult', 
                            `Upload successful!\nDocument ID: ${uploadResult.document.id}\nName: ${uploadResult.document.name}\nSize: ${uploadResult.document.fileSize} bytes\nMessage: ${uploadResult.message}`, 
                            'success'
                        );
                        // Auto-fill document ID for download test
                        document.getElementById('docId').value = uploadResult.document.id;
                    } else {
                        showResult('uploadResult', `Upload failed: ${uploadResult.message}`, 'error');
                    }
                } else {
                    showResult('uploadResult', `Error: ${JSON.stringify(result.errors || result)}`, 'error');
                }
            } catch (error) {
                showResult('uploadResult', `Network error: ${error.message}`, 'error');
            }
        }

        async function searchDocuments() {
            if (!authToken) {
                showResult('searchResult', 'Please generate a token first', 'error');
                return;
            }

            const searchName = document.getElementById('searchName').value;
            
            const query = `
                query SearchDocuments($filter: DocumentSearchInput, $pagination: PaginationInput) {
                    searchDocuments(filter: $filter, pagination: $pagination) {
                        content {
                            id
                            name
                            version
                            status
                            fileSize
                            mimeType
                            createdBy
                            createdDate
                        }
                        totalElements
                        totalPages
                    }
                }
            `;

            const variables = {
                filter: searchName ? { name: searchName } : {},
                pagination: { page: 0, size: 10, sortBy: "createdDate", sortDirection: "DESC" }
            };

            try {
                const response = await fetch(GRAPHQL_URL, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'Authorization': `Bearer ${authToken}`
                    },
                    body: JSON.stringify({ query, variables })
                });

                const result = await response.json();
                
                if (result.data && result.data.searchDocuments) {
                    const searchResult = result.data.searchDocuments;
                    const documents = searchResult.content;
                    
                    let resultText = `Found ${searchResult.totalElements} documents:\n\n`;
                    documents.forEach(doc => {
                        resultText += `ID: ${doc.id}\nName: ${doc.name}\nVersion: ${doc.version}\nStatus: ${doc.status}\nSize: ${doc.fileSize} bytes\nCreated: ${doc.createdDate}\n\n`;
                    });
                    
                    showResult('searchResult', resultText, 'success');
                } else {
                    showResult('searchResult', `Error: ${JSON.stringify(result.errors || result)}`, 'error');
                }
            } catch (error) {
                showResult('searchResult', `Network error: ${error.message}`, 'error');
            }
        }

        async function getDocumentInfo() {
            const docId = document.getElementById('docId').value;
            
            if (!docId) {
                showResult('downloadResult', 'Please enter a document ID', 'error');
                return;
            }

            if (!authToken) {
                showResult('downloadResult', 'Please generate a token first', 'error');
                return;
            }

            try {
                const response = await fetch(`${DOWNLOAD_URL}/${docId}/download/info`, {
                    headers: {
                        'Authorization': `Bearer ${authToken}`
                    }
                });

                if (response.ok) {
                    const info = await response.json();
                    showResult('downloadResult', JSON.stringify(info, null, 2), 'success');
                } else {
                    const error = await response.json();
                    showResult('downloadResult', `Error: ${JSON.stringify(error)}`, 'error');
                }
            } catch (error) {
                showResult('downloadResult', `Network error: ${error.message}`, 'error');
            }
        }

        async function downloadDocument() {
            const docId = document.getElementById('docId').value;
            
            if (!docId) {
                showResult('downloadResult', 'Please enter a document ID', 'error');
                return;
            }

            if (!authToken) {
                showResult('downloadResult', 'Please generate a token first', 'error');
                return;
            }

            try {
                const response = await fetch(`${DOWNLOAD_URL}/${docId}/download`, {
                    headers: {
                        'Authorization': `Bearer ${authToken}`
                    }
                });

                if (response.ok) {
                    const blob = await response.blob();
                    const contentDisposition = response.headers.get('content-disposition');
                    let filename = 'document';
                    
                    if (contentDisposition) {
                        const match = contentDisposition.match(/filename="(.+)"/);
                        if (match) {
                            filename = decodeURIComponent(match[1]);
                        }
                    }

                    const url = window.URL.createObjectURL(blob);
                    const a = document.createElement('a');
                    a.href = url;
                    a.download = filename;
                    document.body.appendChild(a);
                    a.click();
                    window.URL.revokeObjectURL(url);
                    document.body.removeChild(a);
                    
                    showResult('downloadResult', `Download started: ${filename}`, 'success');
                } else {
                    const error = await response.json();
                    showResult('downloadResult', `Error: ${JSON.stringify(error)}`, 'error');
                }
            } catch (error) {
                showResult('downloadResult', `Network error: ${error.message}`, 'error');
            }
        }
    </script>
</body>
</html>