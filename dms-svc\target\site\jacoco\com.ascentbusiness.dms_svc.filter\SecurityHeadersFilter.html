<?xml version="1.0" encoding="UTF-8"?><!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Strict//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-strict.dtd"><html xmlns="http://www.w3.org/1999/xhtml" lang="en"><head><meta http-equiv="Content-Type" content="text/html;charset=UTF-8"/><link rel="stylesheet" href="../jacoco-resources/report.css" type="text/css"/><link rel="shortcut icon" href="../jacoco-resources/report.gif" type="image/gif"/><title>SecurityHeadersFilter</title><script type="text/javascript" src="../jacoco-resources/sort.js"></script></head><body onload="initialSort(['breadcrumb'])"><div class="breadcrumb" id="breadcrumb"><span class="info"><a href="../jacoco-sessions.html" class="el_session">Sessions</a></span><a href="../index.html" class="el_report">dms-svc</a> &gt; <a href="index.html" class="el_package">com.ascentbusiness.dms_svc.filter</a> &gt; <span class="el_class">SecurityHeadersFilter</span></div><h1>SecurityHeadersFilter</h1><table class="coverage" cellspacing="0" id="coveragetable"><thead><tr><td class="sortable" id="a" onclick="toggleSort(this)">Element</td><td class="down sortable bar" id="b" onclick="toggleSort(this)">Missed Instructions</td><td class="sortable ctr2" id="c" onclick="toggleSort(this)">Cov.</td><td class="sortable bar" id="d" onclick="toggleSort(this)">Missed Branches</td><td class="sortable ctr2" id="e" onclick="toggleSort(this)">Cov.</td><td class="sortable ctr1" id="f" onclick="toggleSort(this)">Missed</td><td class="sortable ctr2" id="g" onclick="toggleSort(this)">Cxty</td><td class="sortable ctr1" id="h" onclick="toggleSort(this)">Missed</td><td class="sortable ctr2" id="i" onclick="toggleSort(this)">Lines</td><td class="sortable ctr1" id="j" onclick="toggleSort(this)">Missed</td><td class="sortable ctr2" id="k" onclick="toggleSort(this)">Methods</td></tr></thead><tfoot><tr><td>Total</td><td class="bar">350 of 365</td><td class="ctr2">4%</td><td class="bar">56 of 56</td><td class="ctr2">0%</td><td class="ctr1">34</td><td class="ctr2">38</td><td class="ctr1">77</td><td class="ctr2">83</td><td class="ctr1">6</td><td class="ctr2">10</td></tr></tfoot><tbody><tr><td id="a0"><a href="SecurityHeadersFilter.java.html#L88" class="el_method">addRequestSpecificHeaders(HttpServletRequest, HttpServletResponse)</a></td><td class="bar" id="b0"><img src="../jacoco-resources/redbar.gif" width="120" height="10" title="101" alt="101"/></td><td class="ctr2" id="c4">0%</td><td class="bar" id="d1"><img src="../jacoco-resources/redbar.gif" width="108" height="10" title="18" alt="18"/></td><td class="ctr2" id="e0">0%</td><td class="ctr1" id="f1">10</td><td class="ctr2" id="g1">10</td><td class="ctr1" id="h0">27</td><td class="ctr2" id="i0">27</td><td class="ctr1" id="j0">1</td><td class="ctr2" id="k0">1</td></tr><tr><td id="a7"><a href="SecurityHeadersFilter.java.html#L155" class="el_method">logSecurityHeaders(HttpServletRequest, HttpServletResponse)</a></td><td class="bar" id="b1"><img src="../jacoco-resources/redbar.gif" width="118" height="10" title="100" alt="100"/></td><td class="ctr2" id="c5">0%</td><td class="bar" id="d3"><img src="../jacoco-resources/redbar.gif" width="24" height="10" title="4" alt="4"/></td><td class="ctr2" id="e1">0%</td><td class="ctr1" id="f3">3</td><td class="ctr2" id="g3">3</td><td class="ctr1" id="h3">11</td><td class="ctr2" id="i3">11</td><td class="ctr1" id="j1">1</td><td class="ctr2" id="k1">1</td></tr><tr><td id="a6"><a href="SecurityHeadersFilter.java.html#L188" class="el_method">isTrustedSource(HttpServletRequest)</a></td><td class="bar" id="b2"><img src="../jacoco-resources/redbar.gif" width="64" height="10" title="54" alt="54"/></td><td class="ctr2" id="c6">0%</td><td class="bar" id="d0"><img src="../jacoco-resources/redbar.gif" width="120" height="10" title="20" alt="20"/></td><td class="ctr2" id="e2">0%</td><td class="ctr1" id="f0">11</td><td class="ctr2" id="g0">11</td><td class="ctr1" id="h1">13</td><td class="ctr2" id="i1">13</td><td class="ctr1" id="j2">1</td><td class="ctr2" id="k2">1</td></tr><tr><td id="a1"><a href="SecurityHeadersFilter.java.html#L63" class="el_method">addSecurityHeaders(HttpServletRequest, HttpServletResponse)</a></td><td class="bar" id="b3"><img src="../jacoco-resources/redbar.gif" width="51" height="10" title="43" alt="43"/></td><td class="ctr2" id="c7">0%</td><td class="bar" id="d4"><img src="../jacoco-resources/redbar.gif" width="24" height="10" title="4" alt="4"/></td><td class="ctr2" id="e3">0%</td><td class="ctr1" id="f4">3</td><td class="ctr2" id="g4">3</td><td class="ctr1" id="h2">12</td><td class="ctr2" id="i2">12</td><td class="ctr1" id="j3">1</td><td class="ctr2" id="k3">1</td></tr><tr><td id="a4"><a href="SecurityHeadersFilter.java.html#L217" class="el_method">getClientIpAddress(HttpServletRequest)</a></td><td class="bar" id="b4"><img src="../jacoco-resources/redbar.gif" width="35" height="10" title="30" alt="30"/></td><td class="ctr2" id="c8">0%</td><td class="bar" id="d2"><img src="../jacoco-resources/redbar.gif" width="48" height="10" title="8" alt="8"/></td><td class="ctr2" id="e4">0%</td><td class="ctr1" id="f2">5</td><td class="ctr2" id="g2">5</td><td class="ctr1" id="h4">7</td><td class="ctr2" id="i4">7</td><td class="ctr1" id="j4">1</td><td class="ctr2" id="k4">1</td></tr><tr><td id="a3"><a href="SecurityHeadersFilter.java.html#L38" class="el_method">doFilter(ServletRequest, ServletResponse, FilterChain)</a></td><td class="bar" id="b5"><img src="../jacoco-resources/redbar.gif" width="26" height="10" title="22" alt="22"/></td><td class="ctr2" id="c9">0%</td><td class="bar" id="d5"><img src="../jacoco-resources/redbar.gif" width="12" height="10" title="2" alt="2"/></td><td class="ctr2" id="e5">0%</td><td class="ctr1" id="f5">2</td><td class="ctr2" id="g5">2</td><td class="ctr1" id="h5">7</td><td class="ctr2" id="i5">7</td><td class="ctr1" id="j5">1</td><td class="ctr2" id="k5">1</td></tr><tr><td id="a5"><a href="SecurityHeadersFilter.java.html#L31" class="el_method">init(FilterConfig)</a></td><td class="bar" id="b6"><img src="../jacoco-resources/greenbar.gif" width="4" height="10" title="4" alt="4"/></td><td class="ctr2" id="c0">100%</td><td class="bar" id="d6"/><td class="ctr2" id="e6">n/a</td><td class="ctr1" id="f6">0</td><td class="ctr2" id="g6">1</td><td class="ctr1" id="h6">0</td><td class="ctr2" id="i6">2</td><td class="ctr1" id="j6">0</td><td class="ctr2" id="k6">1</td></tr><tr><td id="a2"><a href="SecurityHeadersFilter.java.html#L55" class="el_method">destroy()</a></td><td class="bar" id="b7"><img src="../jacoco-resources/greenbar.gif" width="4" height="10" title="4" alt="4"/></td><td class="ctr2" id="c1">100%</td><td class="bar" id="d7"/><td class="ctr2" id="e7">n/a</td><td class="ctr1" id="f7">0</td><td class="ctr2" id="g7">1</td><td class="ctr1" id="h7">0</td><td class="ctr2" id="i7">2</td><td class="ctr1" id="j7">0</td><td class="ctr2" id="k7">1</td></tr><tr><td id="a9"><a href="SecurityHeadersFilter.java.html#L24" class="el_method">static {...}</a></td><td class="bar" id="b8"><img src="../jacoco-resources/greenbar.gif" width="4" height="10" title="4" alt="4"/></td><td class="ctr2" id="c2">100%</td><td class="bar" id="d8"/><td class="ctr2" id="e8">n/a</td><td class="ctr1" id="f8">0</td><td class="ctr2" id="g8">1</td><td class="ctr1" id="h8">0</td><td class="ctr2" id="i8">1</td><td class="ctr1" id="j8">0</td><td class="ctr2" id="k8">1</td></tr><tr><td id="a8"><a href="SecurityHeadersFilter.java.html#L22" class="el_method">SecurityHeadersFilter()</a></td><td class="bar" id="b9"><img src="../jacoco-resources/greenbar.gif" width="3" height="10" title="3" alt="3"/></td><td class="ctr2" id="c3">100%</td><td class="bar" id="d9"/><td class="ctr2" id="e9">n/a</td><td class="ctr1" id="f9">0</td><td class="ctr2" id="g9">1</td><td class="ctr1" id="h9">0</td><td class="ctr2" id="i9">1</td><td class="ctr1" id="j9">0</td><td class="ctr2" id="k9">1</td></tr></tbody></table><div class="footer"><span class="right">Created with <a href="http://www.jacoco.org/jacoco">JaCoCo</a> 0.8.10.202304240956</span></div></body></html>