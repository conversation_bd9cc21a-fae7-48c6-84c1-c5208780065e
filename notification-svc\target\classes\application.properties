# Notification Service - Consolidated Configuration
# This file contains all common configurations with environment variable support
# Environment-specific files should only contain overrides

# Application Configuration
spring.application.name=notification-svc
server.port=${SERVER_PORT:9091}

# Database Configuration
spring.datasource.url=${DB_URL:*******************************************************************************************************************}
spring.datasource.username=${DB_USERNAME:root}
spring.datasource.password=${DB_PASSWORD:root}
spring.datasource.driver-class-name=com.mysql.cj.jdbc.Driver

# HikariCP Connection Pool Configuration
spring.datasource.hikari.maximum-pool-size=${DB_POOL_MAX_SIZE:10}
spring.datasource.hikari.minimum-idle=${DB_POOL_MIN_IDLE:2}
spring.datasource.hikari.connection-timeout=${DB_POOL_CONNECTION_TIMEOUT:30000}
spring.datasource.hikari.idle-timeout=${DB_POOL_IDLE_TIMEOUT:300000}
spring.datasource.hikari.max-lifetime=${DB_POOL_MAX_LIFETIME:900000}

# JPA Configuration
spring.jpa.hibernate.ddl-auto=${JPA_DDL_AUTO:update}
spring.jpa.show-sql=${JPA_SHOW_SQL:false}
spring.jpa.properties.hibernate.format_sql=${HIBERNATE_FORMAT_SQL:false}
spring.jpa.properties.hibernate.dialect=org.hibernate.dialect.MySQL8Dialect
spring.jpa.properties.hibernate.enable_lazy_load_no_trans=true

# Hibernate Performance Optimizations
spring.jpa.properties.hibernate.jdbc.batch_size=${HIBERNATE_BATCH_SIZE:25}
spring.jpa.properties.hibernate.order_inserts=true
spring.jpa.properties.hibernate.order_updates=true
spring.jpa.properties.hibernate.jdbc.batch_versioned_data=true

# Flyway Configuration
spring.flyway.enabled=${FLYWAY_ENABLED:false}
spring.flyway.baseline-on-migrate=true
spring.flyway.locations=classpath:db/migration

# GraphQL Configuration
spring.graphql.graphiql.enabled=${GRAPHIQL_ENABLED:true}
spring.graphql.schema.locations=classpath:graphql/
spring.graphql.schema.printer.enabled=${GRAPHQL_SCHEMA_PRINTER_ENABLED:false}
spring.graphql.path=/graphql
spring.graphql.graphiql.path=/graphiql


# CORS Configuration for GraphQL
spring.graphql.cors.allowed-origins=${GRAPHQL_CORS_ALLOWED_ORIGINS:}
spring.graphql.cors.allowed-methods=${GRAPHQL_CORS_ALLOWED_METHODS:GET,POST,OPTIONS}
spring.graphql.cors.allowed-headers=${GRAPHQL_CORS_ALLOWED_HEADERS:Content-Type,Authorization,X-Correlation-ID,Accept}
spring.graphql.cors.allow-credentials=${GRAPHQL_CORS_ALLOW_CREDENTIALS:true}
spring.graphql.cors.max-age=${GRAPHQL_CORS_MAX_AGE:3600}

# RabbitMQ Configuration
spring.rabbitmq.host=${RABBITMQ_HOST:localhost}
spring.rabbitmq.port=${RABBITMQ_PORT:5672}
spring.rabbitmq.username=${RABBITMQ_USERNAME:guest}
spring.rabbitmq.password=${RABBITMQ_PASSWORD:guest}
spring.rabbitmq.virtual-host=${RABBITMQ_VIRTUAL_HOST:/}
spring.rabbitmq.connection-timeout=${RABBITMQ_CONNECTION_TIMEOUT:30000}
spring.rabbitmq.requested-heartbeat=${RABBITMQ_HEARTBEAT:60}
spring.rabbitmq.publisher-confirm-type=${RABBITMQ_PUBLISHER_CONFIRM:correlated}
spring.rabbitmq.publisher-returns=${RABBITMQ_PUBLISHER_RETURNS:true}

# Redis Configuration
spring.redis.host=${REDIS_HOST:localhost}
spring.redis.port=${REDIS_PORT:6379}
spring.redis.password=${REDIS_PASSWORD:}
spring.redis.database=${REDIS_DATABASE:0}
spring.redis.timeout=${REDIS_TIMEOUT:5000ms}
spring.redis.connect-timeout=${REDIS_CONNECT_TIMEOUT:3000ms}

# Cache Configuration
spring.cache.type=redis
spring.cache.redis.time-to-live=${CACHE_TTL:3600}
spring.cache.redis.cache-null-values=${CACHE_NULL_VALUES:false}
spring.cache.redis.use-key-prefix=${CACHE_USE_KEY_PREFIX:true}
spring.cache.redis.key-prefix=${CACHE_KEY_PREFIX:notification:}

# Redis Connection Pool Configuration (Lettuce)
spring.data.redis.lettuce.pool.max-active=${REDIS_POOL_MAX_ACTIVE:10}
spring.data.redis.lettuce.pool.max-idle=${REDIS_POOL_MAX_IDLE:5}
spring.data.redis.lettuce.pool.min-idle=${REDIS_POOL_MIN_IDLE:1}
spring.data.redis.lettuce.pool.max-wait=${REDIS_POOL_MAX_WAIT:2000ms}
spring.data.redis.lettuce.pool.time-between-eviction-runs=${REDIS_POOL_EVICTION_RUNS:30s}

# Email Configuration - NEVER hardcode credentials
spring.mail.host=${MAIL_HOST:email-smtp.ap-south-1.amazonaws.com}
spring.mail.port=${MAIL_PORT:587}
spring.mail.username=${MAIL_USERNAME:AKIA2KPAUIIKU4IGE2PD}
spring.mail.password=${MAIL_PASSWORD:BFHjDlCJ0dDtC7JaVd2HDY9hY5d8jY0eLTToxP/OWrl5}
spring.mail.properties.mail.smtp.auth=${MAIL_SMTP_AUTH:true}
spring.mail.properties.mail.smtp.starttls.enable=${MAIL_SMTP_STARTTLS:true}
spring.mail.properties.mail.smtp.starttls.required=${MAIL_SMTP_STARTTLS_REQUIRED:true}
spring.mail.properties.mail.smtp.ssl.trust=${MAIL_SMTP_SSL_TRUST:*}

# Notification Configuration
notification.email.from=${NOTIFICATION_FROM_EMAIL:noreply@localhost}
notification.email.enabled=${EMAIL_ENABLED:true}
notification.email.mock=${EMAIL_MOCK:false}
notification.email.templates.path=classpath:templates/email/

# JWT Configuration - NEVER hardcode secrets
jwt.public-key.path=classpath:keys/public_key.pem
jwt.issuer=${JWT_ISSUER:notification-service}
jwt.audience=${JWT_AUDIENCE:notification-clients}
jwt.secret=${JWT_SECRET:}
jwt.expiration=${JWT_EXPIRATION:3600000}
jwt.token.expiration=${JWT_TOKEN_EXPIRATION:3600}

# Security Configuration
security.jwt.enabled=${JWT_ENABLED:true}
security.cors.allowed-origins=${CORS_ALLOWED_ORIGINS:}
security.cors.allowed-methods=${CORS_ALLOWED_METHODS:GET,POST,PUT,DELETE,OPTIONS}
security.cors.allowed-headers=${CORS_ALLOWED_HEADERS:Content-Type,Authorization,X-Correlation-ID,Accept}
security.cors.allow-credentials=${CORS_ALLOW_CREDENTIALS:true}
security.cors.max-age=${CORS_MAX_AGE:3600}

# Actuator Configuration
management.endpoints.web.exposure.include=${ACTUATOR_ENDPOINTS:health,info,metrics,prometheus}
management.endpoint.health.show-details=${ACTUATOR_HEALTH_SHOW_DETAILS:when-authorized}
management.endpoint.health.show-components=${ACTUATOR_HEALTH_SHOW_COMPONENTS:when-authorized}
management.health.defaults.enabled=true

# Health Indicators
management.health.db.enabled=${HEALTH_DB_ENABLED:true}
management.health.diskspace.enabled=${HEALTH_DISKSPACE_ENABLED:true}
management.health.redis.enabled=${HEALTH_REDIS_ENABLED:true}
management.health.rabbitmq.enabled=${HEALTH_RABBITMQ_ENABLED:true}
management.health.ping.enabled=${HEALTH_PING_ENABLED:true}
management.health.livenessstate.enabled=${HEALTH_LIVENESS_ENABLED:true}
management.health.readinessstate.enabled=${HEALTH_READINESS_ENABLED:true}

# Metrics Configuration
management.metrics.export.prometheus.enabled=${METRICS_PROMETHEUS_ENABLED:true}
management.metrics.distribution.percentiles-histogram.http.server.requests=${METRICS_PERCENTILES_HISTOGRAM:false}

# Logging Configuration
logging.level.com.ascentbusiness.notification_svc=${LOG_LEVEL_APP:INFO}
logging.level.org.springframework.web=${LOG_LEVEL_SPRING_WEB:INFO}
logging.level.org.hibernate=${LOG_LEVEL_HIBERNATE:WARN}
logging.level.org.springframework.security=${LOG_LEVEL_SPRING_SECURITY:WARN}
logging.level.org.springframework.amqp=${LOG_LEVEL_SPRING_AMQP:INFO}
logging.pattern.console=${LOG_PATTERN_CONSOLE:%d{yyyy-MM-dd HH:mm:ss.SSS} [%thread] %-5level [%X{correlationId}] %logger{36} - %msg%n}
logging.pattern.file=${LOG_PATTERN_FILE:%d{yyyy-MM-dd HH:mm:ss.SSS} [%thread] %-5level [%X{correlationId}] %logger{36} - %msg%n}

# Server Configuration
server.compression.enabled=${SERVER_COMPRESSION_ENABLED:false}
server.compression.mime-types=${SERVER_COMPRESSION_MIME_TYPES:text/html,text/xml,text/plain,text/css,text/javascript,application/javascript,application/json}
server.compression.min-response-size=${SERVER_COMPRESSION_MIN_SIZE:1024}
server.tomcat.connection-timeout=${SERVER_TOMCAT_CONNECTION_TIMEOUT:20000}
server.tomcat.max-connections=${SERVER_TOMCAT_MAX_CONNECTIONS:200}
server.tomcat.threads.max=${SERVER_TOMCAT_THREADS_MAX:50}
server.tomcat.threads.min-spare=${SERVER_TOMCAT_THREADS_MIN_SPARE:5}

# Async Configuration
spring.task.execution.pool.core-size=${ASYNC_POOL_CORE_SIZE:5}
spring.task.execution.pool.max-size=${ASYNC_POOL_MAX_SIZE:10}
spring.task.execution.pool.queue-capacity=${ASYNC_POOL_QUEUE_CAPACITY:50}
spring.task.execution.thread-name-prefix=${ASYNC_THREAD_NAME_PREFIX:notification-async-}

# Notification Processing Configuration
notification.processing.batch-size=${NOTIFICATION_BATCH_SIZE:25}
notification.processing.retry-attempts=${NOTIFICATION_RETRY_ATTEMPTS:3}
notification.processing.retry-delay=${NOTIFICATION_RETRY_DELAY:5000}

# Template Configuration
notification.template.cache-enabled=${TEMPLATE_CACHE_ENABLED:true}
notification.template.cache-ttl=${TEMPLATE_CACHE_TTL:3600}

# Webhook Configuration
notification.webhook.enabled=${WEBHOOK_ENABLED:true}
notification.webhook.timeout=${WEBHOOK_TIMEOUT:30000}
notification.webhook.retry-attempts=${WEBHOOK_RETRY_ATTEMPTS:3}

# Cross-service Communication
dms.service.url=${DMS_SERVICE_URL:http://localhost:9093}

# Java 21 Performance Optimizations
spring.threads.virtual.enabled=${VIRTUAL_THREADS_ENABLED:true}

# Spring Configuration
spring.main.allow-bean-definition-overriding=${ALLOW_BEAN_DEFINITION_OVERRIDING:true}

# Development Features (disabled by default)
spring.devtools.restart.enabled=${DEVTOOLS_RESTART_ENABLED:false}
spring.devtools.livereload.enabled=${DEVTOOLS_LIVERELOAD_ENABLED:false}
