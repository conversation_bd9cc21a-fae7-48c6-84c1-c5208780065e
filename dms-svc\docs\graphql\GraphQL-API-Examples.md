# DMS Service - Exhaustive GraphQL API Examples

This document provides comprehensive GraphQL examples for all upload, download, and search operations in the DMS service.

## Table of Contents

1. [Authentication](#authentication)
2. [Document Upload Operations](#document-upload-operations)
3. [Document Download Operations](#document-download-operations)
4. [Document Search Operations](#document-search-operations)
5. [File Conversion Operations](#file-conversion-operations)
6. [Document Sharing Operations](#document-sharing-operations)
7. [Bulk Operations](#bulk-operations)
8. [Advanced Features](#advanced-features)
9. [Progress Tracking](#progress-tracking)
10. [Error Handling](#error-handling)
11. [Best Practices](#best-practices)

## Authentication

All GraphQL operations require JWT authentication. Include the token in the Authorization header:

```
Authorization: Bearer <your-jwt-token>
```

### Generate Test Token

```graphql
mutation GenerateTestToken {
  generateTestToken(input: {
    username: "test-user"
    roles: ["USER", "ADMIN"]
    permissions: ["READ", "WRITE", "DELETE"]
  }) {
    token
    tokenType
    expiresAt
  }
}
```

## Document Upload Operations

### 1. Basic Document Upload

```graphql
mutation UploadDocument {
  uploadDocument(input: {
    name: "Project Report"
    description: "Q4 2024 Project Status Report"
    file: null  # File upload via multipart form data
    keywords: ["project", "report", "q4", "2024"]
    storageProvider: LOCAL
    overrideFile: false
    scannerType: CLAMAV
  }) {
    id
    name
    version
    status
    storageProvider
    filePath
    mimeType
    fileSize
    createdBy
    createdDate
    lastModifiedDate
    keywords
  }
}
```

### 2. Enhanced Document Upload

```graphql
mutation UploadDocumentEnhanced {
  uploadDocumentEnhanced(input: {
    name: "Financial Report"
    description: "Annual financial analysis"
    file: null  # File upload via multipart form data
    keywords: ["finance", "annual", "report"]
    storageProvider: S3
    overrideFile: false
    skipVirusScan: false
    scannerType: SOPHOS
    validateFileType: true
    allowedMimeTypes: ["application/pdf", "application/vnd.openxmlformats-officedocument.wordprocessingml.document"]
    maxFileSize: 52428800  # 50MB
    forceProcessingStrategy: DIRECT
    enableProgressTracking: true
    classificationMetadata: {
      module: "Finance"
      subModule: "Reporting"
      businessUnit: "Corporate"
      regionLocation: "North America"
      confidentialityLevel: "CONFIDENTIAL"
      tagsKeywords: ["financial", "annual", "confidential"]
      language: "English"
      documentType: "Report"
    }
    ownershipMetadata: {
      owner: "<EMAIL>"
      approver: "<EMAIL>"
      status: "PENDING_APPROVAL"
      expiryDate: "2025-12-31T23:59:59Z"
      renewalReminder: "2025-11-01T09:00:00Z"
      retentionPeriod: "7 years"
      archived: false
    }
    complianceMetadata: {
      complianceStandard: "SOX"
      auditRelevance: "HIGH"
      linkedRisksControls: "Financial Reporting Controls"
      controlId: "FRC-001"
      thirdPartyId: "AUDIT-2024-001"
      policyId: "POL-FIN-001"
    }
    notifyOnCompletion: true
    notificationEmail: "<EMAIL>"
  }) {
    success
    document {
      id
      name
      version
      status
      storageProvider
      filePath
      mimeType
      fileSize
      createdBy
      createdDate
      classificationMetadata {
        id
        module
        subModule
        businessUnit
        confidentialityLevel
      }
      ownershipMetadata {
        id
        owner
        approver
        status
        expiryDate
      }
      complianceMetadata {
        id
        complianceStandard
        auditRelevance
        controlId
      }
    }
    uploadId
    fileName
    fileSize
    processingStrategy
    processingStatus
    message
    uploadedAt
  }
}
```

### 3. Upload Document from Server Path

```graphql
mutation UploadDocumentFromPath {
  uploadDocumentFromPath(input: {
    name: "Server Document"
    description: "Document uploaded from server path"
    sourceFilePath: "/tmp/documents/server-document.pdf"
    keywords: ["server", "upload", "automated"]
    storageProvider: LOCAL
    overrideFile: true
    scannerType: WINDOWS_DEFENDER
    classificationMetadata: {
      module: "IT"
      subModule: "Automation"
      businessUnit: "Technology"
      documentType: "System Generated"
    }
  }) {
    id
    name
    version
    status
    filePath
    fileSize
    mimeType
    createdBy
    createdDate
  }
}
```

### 4. Upload New Document Version

```graphql
mutation UploadNewVersion {
  uploadDocumentNewVersion(input: {
    documentId: "123"
    file: null  # File upload via multipart form data
    name: "Updated Project Report"
    description: "Updated version with Q1 2025 data"
    keywords: ["project", "report", "q1", "2025", "updated"]
    overrideFile: false
    comment: "Added Q1 2025 financial data and updated projections"
    classificationMetadata: {
      module: "Finance"
      subModule: "Reporting"
      confidentialityLevel: "CONFIDENTIAL"
    }
  }) {
    id
    name
    version
    status
    filePath
    fileSize
    createdBy
    createdDate
    lastModifiedDate
  }
}
```

### 5. Chunked Upload Operations

#### Initialize Chunked Upload

```graphql
mutation InitializeChunkedUpload {
  initializeChunkedUpload(input: {
    fileName: "large-document.pdf"
    totalSize: 104857600  # 100MB
    chunkSize: 1048576    # 1MB chunks
    mimeType: "application/pdf"
    metadata: {
      description: "Large document uploaded in chunks"
      keywords: ["large", "chunked", "upload"]
      classificationMetadata: {
        module: "Documents"
        documentType: "Large File"
      }
    }
  }) {
    sessionId
    fileName
    totalSize
    chunkSize
    totalChunks
    uploadedChunks
    progress
    status
    createdAt
    expiresAt
  }
}
```

#### Upload Chunk

```graphql
mutation UploadChunk {
  uploadChunk(input: {
    sessionId: "chunk-session-123"
    chunkNumber: 1
    chunk: null  # Binary chunk data via multipart form data
    isLastChunk: false
    chunkChecksum: "sha256-checksum-here"
  }) {
    sessionId
    fileName
    totalSize
    uploadedChunks
    totalChunks
    progress
    status
    lastActivityAt
  }
}
```

#### Complete Chunked Upload

```graphql
mutation CompleteChunkedUpload {
  completeChunkedUpload(input: {
    sessionId: "chunk-session-123"
    finalFileName: "completed-large-document.pdf"
    metadata: {
      description: "Large document successfully uploaded"
      keywords: ["completed", "large", "document"]
    }
    validateIntegrity: true
  }) {
    success
    document {
      id
      name
      version
      status
      fileSize
      mimeType
    }
    uploadId
    fileName
    fileSize
    processingStrategy
    processingStatus
    message
  }
}
```

### 6. File Validation

```graphql
query ValidateFile {
  validateFile(
    file: null  # File via multipart form data
    validationOptions: {
      skipVirusScan: false
      scannerType: CLAMAV
      validateFileType: true
      allowedMimeTypes: ["application/pdf", "image/jpeg", "image/png"]
      maxFileSize: 10485760  # 10MB
      allowDuplicates: false
    }
  ) {
    isValid
    validationErrors {
      code
      message
      severity
      field
    }
    warnings
    fileInfo {
      originalFileName
      fileSize
      mimeType
      extension
      isEncrypted
      checksum
      virusScanResult {
        isClean
        scannerUsed
        scanDate
        threatDetails
        quarantined
      }
    }
  }
}
```

## Document Download Operations

### 1. Get Document by ID

```graphql
query GetDocument {
  getDocument(id: "123") {
    id
    name
    version
    status
    storageProvider
    filePath
    fileContent  # Base64 encoded content (deprecated for large files)
    mimeType
    fileSize
    createdBy
    createdDate
    lastModifiedDate
    keywords
    classificationMetadata {
      id
      module
      subModule
      businessUnit
      confidentialityLevel
      documentType
      language
    }
    ownershipMetadata {
      id
      owner
      approver
      status
      expiryDate
      archived
    }
    complianceMetadata {
      id
      complianceStandard
      auditRelevance
      controlId
      policyId
    }
  }
}
```

### 2. Download Document (Deprecated - Use REST API)

```graphql
query DownloadDocument {
  downloadDocument(id: "123") {
    id
    name
    version
    filePath
    fileContent  # Base64 encoded content
    mimeType
    fileSize
    # Note: This GraphQL endpoint is deprecated for file downloads
    # Use REST API endpoints instead:
    # GET /api/v2/documents/{id}/download
  }
}
```

### 3. Get Document Versions

```graphql
query ListDocumentVersions {
  listDocumentVersions(documentId: "123") {
    id
    version
    status
    createdDate
    lastModifiedDate
    filePath
  }
}
```

## Document Search Operations

### 1. Basic Document Search

```graphql
query SearchDocuments {
  searchDocuments(
    filter: {
      name: "project"
      creator: "<EMAIL>"
      storageProvider: LOCAL
      status: ACTIVE
      keywords: ["project", "report"]
    }
    pagination: {
      page: 0
      size: 10
      sortBy: "createdDate"
      sortDirection: "DESC"
    }
  ) {
    content {
      id
      name
      version
      status
      createdBy
      createdDate
      fileSize
      mimeType
      keywords
    }
    totalElements
    totalPages
    size
    number
    first
    last
  }
}
```

### 2. Advanced Elasticsearch Search

```graphql
query AdvancedSearch {
  advancedSearch(
    input: {
      query: "financial report 2024"
      searchType: MULTI_FIELD
      fuzzySearch: true
      phraseSearch: false
      includeContent: true
      contentOnly: false
      
      # Filters
      name: "report"
      creator: "finance-team"
      storageProvider: S3
      status: ACTIVE
      mimeTypes: ["application/pdf", "application/vnd.openxmlformats-officedocument.wordprocessingml.document"]
      
      # Date range filters
      createdDateFrom: "2024-01-01T00:00:00Z"
      createdDateTo: "2024-12-31T23:59:59Z"
      modifiedDateFrom: "2024-06-01T00:00:00Z"
      
      # Metadata filters
      module: "Finance"
      subModule: "Reporting"
      businessUnit: "Corporate"
      regionLocation: "North America"
      documentType: "Report"
      language: "English"
      confidentialityLevel: "CONFIDENTIAL"
      
      # Ownership filters
      owner: "<EMAIL>"
      approver: "<EMAIL>"
      ownershipStatus: "APPROVED"
      
      # Compliance filters
      complianceStandard: "SOX"
      auditRelevance: "HIGH"
      controlId: "FRC-001"
      
      # Tags and keywords
      tags: ["financial", "annual"]
      keywords: ["revenue", "profit", "analysis"]
      
      # Version filters
      includeHistoricalVersions: false
      currentVersionOnly: true
      
      # File size filters
      minFileSize: 1024      # 1KB
      maxFileSize: 52428800  # 50MB
      
      # Security filters
      includeConfidential: true
    }
    pagination: {
      page: 0
      size: 20
      sortBy: "relevanceScore"
      sortDirection: "DESC"
    }
  ) {
    documents {
      content {
        id
        name
        description
        version
        status
        storageProvider
        filePath
        createdBy
        createdDate
        lastModifiedDate
        keywords
        tags
        relevanceScore
        highlights {
          name
          description
          content
          keywords
        }
        classificationMetadata {
          module
          subModule
          businessUnit
          confidentialityLevel
        }
        ownershipMetadata {
          owner
          approver
          status
        }
        complianceMetadata {
          complianceStandard
          auditRelevance
          controlId
        }
      }
      totalElements
      totalPages
      size
      number
      first
      last
    }
    facets {
      documentTypes {
        value
        count
      }
      storageProviders {
        value
        count
      }
      creators {
        value
        count
      }
      modules {
        value
        count
      }
      businessUnits {
        value
        count
      }
      confidentialityLevels {
        value
        count
      }
      mimeTypes {
        value
        count
      }
      tags {
        value
        count
      }
      languages {
        value
        count
      }
      years {
        value
        count
      }
    }
    suggestions
    totalTime
    maxScore
  }
}
```

### 3. Search Suggestions

```graphql
query SearchSuggestions {
  searchSuggestions(query: "financi", limit: 10)
}
```

### 4. Get Search Facets

```graphql
query GetSearchFacets {
  getSearchFacets(input: {
    query: "report"
    module: "Finance"
    createdDateFrom: "2024-01-01T00:00:00Z"
    createdDateTo: "2024-12-31T23:59:59Z"
  }) {
    documentTypes {
      value
      count
    }
    storageProviders {
      value
      count
    }
    creators {
      value
      count
    }
    modules {
      value
      count
    }
    businessUnits {
      value
      count
    }
    confidentialityLevels {
      value
      count
    }
    mimeTypes {
      value
      count
    }
    tags {
      value
      count
    }
    languages {
      value
      count
    }
    years {
      value
      count
    }
  }
}
```

### 5. Metadata-based Search

#### Search by Classification

```graphql
query SearchByClassification {
  searchDocumentsByClassification(
    module: "Finance"
    subModule: "Reporting"
    businessUnit: "Corporate"
    confidentialityLevel: "CONFIDENTIAL"
  ) {
    id
    name
    version
    createdBy
    createdDate
    classificationMetadata {
      module
      subModule
      businessUnit
      confidentialityLevel
    }
  }
}
```

#### Search by Ownership

```graphql
query SearchByOwnership {
  searchDocumentsByOwnership(
    owner: "<EMAIL>"
    approver: "<EMAIL>"
    status: "APPROVED"
    archived: false
  ) {
    id
    name
    version
    createdBy
    createdDate
    ownershipMetadata {
      owner
      approver
      status
      expiryDate
      archived
    }
  }
}
```

#### Search by Compliance

```graphql
query SearchByCompliance {
  searchDocumentsByCompliance(
    complianceStandard: "SOX"
    controlId: "FRC-001"
    thirdPartyId: "AUDIT-2024-001"
    policyId: "POL-FIN-001"
  ) {
    id
    name
    version
    createdBy
    createdDate
    complianceMetadata {
      complianceStandard
      auditRelevance
      controlId
      thirdPartyId
      policyId
    }
  }
}
```

### 6. Date-based Queries

#### Documents Expiring Soon

```graphql
query DocumentsExpiringBefore {
  getDocumentsExpiringBefore(date: "2025-03-31T23:59:59Z") {
    id
    name
    version
    createdBy
    ownershipMetadata {
      owner
      expiryDate
      renewalReminder
    }
  }
}
```

#### Documents for Renewal Reminder

```graphql
query DocumentsForRenewalReminder {
  getDocumentsForRenewalReminder(date: "2025-02-01T00:00:00Z") {
    id
    name
    version
    createdBy
    ownershipMetadata {
      owner
      expiryDate
      renewalReminder
    }
  }
}
```

## File Conversion Operations

### 1. Markdown to Word Conversion

```graphql
mutation ConvertMarkdownToWord {
  convertMarkdownToWord(input: {
    file: null  # Markdown file via multipart form data
    scannerType: CLAMAV
    options: {
      quality: HIGH
      preserveFormatting: true
      includeImages: true
      includeMetadata: true
      pageSize: "A4"
      orientation: PORTRAIT
      margins: {
        top: 2.5
        bottom: 2.5
        left: 2.5
        right: 2.5
        unit: "cm"
      }
    }
    outputFormat: "docx"
  }) {
    sessionId
    conversionType
    originalFileName
    convertedFileName
    downloadPath
    downloadUrl
    fileSize
    status
    success
    message
    errorDetails
    startedAt
    completedAt
    processingTimeMs
    conversionMethod
    usedPandoc
    virusScanResponse {
      scanId
      scanResult
      scannerType
      detectedThreats
      scanDurationMs
      scanTimestamp
      isClean
    }
    metadata {
      originalMimeType
      convertedMimeType
      originalSize
      convertedSize
      compressionRatio
      qualitySettings
      conversionOptions
      engineVersion
      processingNode
    }
  }
}
```

### 2. PDF to Word Conversion

```graphql
mutation ConvertPdfToWord {
  convertPdfToWord(input: {
    filePath: "/tmp/documents/document.pdf"
    scannerType: SOPHOS
    options: {
      quality: STANDARD
      preserveFormatting: true
      includeImages: true
    }
    outputFormat: "docx"
  }) {
    sessionId
    conversionType
    originalFileName
    convertedFileName
    downloadPath
    fileSize
    status
    success
    message
    startedAt
    completedAt
    processingTimeMs
    conversionMethod
    usedPandoc
  }
}
```

### 3. Word to PDF Conversion

```graphql
mutation ConvertWordToPdf {
  convertWordToPdf(input: {
    file: null  # Word document via multipart form data
    scannerType: VIRUS_TOTAL
    options: {
      quality: HIGH
      compressionLevel: 5
      preserveFormatting: true
      includeImages: true
      includeMetadata: true
      pageSize: "A4"
      orientation: PORTRAIT
    }
    outputFormat: "pdf"
  }) {
    sessionId
    conversionType
    originalFileName
    convertedFileName
    downloadPath
    fileSize
    status
    success
    message
    processingTimeMs
    conversionMethod
  }
}
```

### 4. Generic File Conversion

```graphql
mutation ConvertFile {
  convertFile(input: {
    file: null  # File via multipart form data
    fromFormat: "markdown"
    toFormat: "pdf"
    scannerType: CLAMAV
    options: {
      quality: HIGH
      preserveFormatting: true
      includeImages: true
      pageSize: "A4"
      orientation: PORTRAIT
    }
    priority: HIGH
  }) {
    sessionId
    conversionType
    originalFileName
    convertedFileName
    downloadPath
    fileSize
    status
    success
    message
    processingTimeMs
    conversionMethod
    usedPandoc
  }
}
```

### 5. Batch File Conversion

```graphql
mutation BatchConvertFiles {
  batchConvertFiles(input: {
    files: null  # Multiple files via multipart form data
    filePaths: [
      "/tmp/documents/doc1.md",
      "/tmp/documents/doc2.md",
      "/tmp/documents/doc3.md"
    ]
    conversionType: MARKDOWN_TO_WORD
    scannerType: CLAMAV
    options: {
      quality: STANDARD
      preserveFormatting: true
      includeImages: true
    }
    outputFormat: "docx"
    maxConcurrentConversions: 3
    continueOnError: true
    notifyOnCompletion: true
    notificationEmail: "<EMAIL>"
  }) {
    batchId
    totalFiles
    completedFiles
    failedFiles
    status
    results {
      sessionId
      conversionType
      originalFileName
      convertedFileName
      downloadPath
      fileSize
      status
      success
      message
      processingTimeMs
    }
    startedAt
    completedAt
    totalProcessingTime
    progress
    estimatedCompletionTime
    errors {
      fileName
      errorCode
      errorMessage
      details
      timestamp
    }
  }
}
```

## Document Sharing Operations

### 1. Create Share Link

```graphql
mutation CreateDocumentShareLink {
  createDocumentShareLink(
    documentId: 123
    input: {
      permission: READ
      targetUserId: "user123"
      expiresAt: "2025-12-31T23:59:59Z"
      password: "secure-password"
      maxUses: 10
      notes: "Shared for review purposes"
    }
  ) {
    success
    message
    shareLink {
      id
      linkId
      document {
        id
        name
        version
      }
      createdByUserId
      permission
      targetUserId
      createdAt
      expiresAt
      isActive
      hasPassword
      maxUses
      useCount
      notes
      shareUrl
    }
  }
}
```

### 2. Access Shared Document

```graphql
query AccessSharedDocument {
  accessSharedDocument(input: {
    linkId: "share-link-123"
    password: "secure-password"
  }) {
    success
    message
    document {
      id
      name
      version
      status
      fileSize
      mimeType
      createdBy
      createdDate
    }
    requiresPassword
    permission
  }
}
```

### 3. Bulk Share Documents

```graphql
mutation BulkShareDocuments {
  bulkShareDocuments(input: {
    documentIds: [123, 124, 125]
    recipientIds: ["user1", "user2", "user3"]
    roleNames: ["REVIEWER", "APPROVER"]
    permission: READ
    expiresAt: "2025-06-30T23:59:59Z"
    notes: "Bulk shared for quarterly review"
  }) {
    success
    message
    operation {
      id
      operationId
      createdByUserId
      createdAt
      permission
      expiresAt
      notes
      isCompleted
      completedAt
      totalDocuments
      totalRecipients
      successCount
      failureCount
      items {
        documentId
        recipientId
        isRole
        isSuccessful
        errorMessage
        shareLinkId
        shareUrl
      }
    }
  }
}
```

## Bulk Operations

### 1. Bulk Document Upload

```graphql
mutation BulkUploadDocuments {
  bulkUploadDocuments(input: {
    files: null  # Multiple files via multipart form data
    commonMetadata: {
      description: "Bulk uploaded documents"
      keywords: ["bulk", "upload", "batch"]
      storageProvider: S3
      classificationMetadata: {
        module: "Documents"
        businessUnit: "Operations"
        documentType: "Bulk Upload"
      }
    }
    processingOptions: {
      processingStrategy: DIRECT
      maxConcurrentUploads: 5
      continueOnError: true
      notifyOnCompletion: true
      notificationEmail: "<EMAIL>"
    }
    validationOptions: {
      skipVirusScan: false
      scannerType: CLAMAV
      validateFileType: true
      allowedMimeTypes: ["application/pdf", "image/jpeg", "image/png"]
      maxFileSize: 10485760  # 10MB
      allowDuplicates: false
    }
  }) {
    totalFiles
    successfulUploads
    failedUploads
    results {
      success
      document {
        id
        name
        version
        fileSize
        mimeType
      }
      uploadId
      fileName
      fileSize
      processingStrategy
      processingStatus
      message
      errorMessage
      uploadedAt
    }
    overallStatus
    startedAt
    completedAt
    processingTimeMs
    errors {
      fileName
      errorCode
      errorMessage
      details
    }
  }
}
```

## Advanced Features

### 1. Upload Statistics

```graphql
query GetUploadStatistics {
  getUploadStatistics(
    dateFrom: "2024-01-01T00:00:00Z"
    dateTo: "2024-12-31T23:59:59Z"
    userId: "user123"
  ) {
    totalUploads
    successfulUploads
    failedUploads
    totalSizeUploaded
    averageFileSize
    uploadsByStrategy {
      strategy
      count
      percentage
    }
    uploadsByMimeType {
      mimeType
      count
      totalSize
    }
    uploadTrends {
      date
      uploadCount
      totalSize
      averageProcessingTime
    }
  }
}
```

### 2. Active Upload Sessions

```graphql
query GetActiveUploadSessions {
  getActiveUploadSessions(userId: "user123") {
    sessionId
    fileName
    totalSize
    chunkSize
    totalChunks
    uploadedChunks
    progress
    status
    createdAt
    expiresAt
    lastActivityAt
  }
}
```

### 3. Upload Management

```graphql
mutation CancelUpload {
  cancelUpload(uploadId: "upload-123")
}

mutation PauseUpload {
  pauseUpload(uploadId: "upload-123")
}

mutation ResumeUpload {
  resumeUpload(uploadId: "upload-123")
}
```

### 4. Cleanup Operations

```graphql
mutation CleanupExpiredSessions {
  cleanupExpiredSessions(olderThanHours: 24)
}

mutation CleanupFailedUploads {
  cleanupFailedUploads(olderThanDays: 7)
}

mutation CleanupCompletedConversions {
  cleanupCompletedConversions(olderThanDays: 7)
}

mutation CleanupFailedConversions {
  cleanupFailedConversions(olderThanDays: 3)
}
```

## Progress Tracking

### 1. Upload Progress

```graphql
query GetUploadProgress {
  getUploadProgress(uploadId: "upload-123") {
    uploadId
    fileName
    totalSize
    uploadedSize
    progress
    status
    startedAt
    lastUpdatedAt
    estimatedTimeRemaining
    transferRate
    errorMessage
  }
}
```

### 2. Chunked Upload Status

```graphql
query GetChunkedUploadStatus {
  chunkedUploadStatus(sessionId: "session-123") {
    sessionId
    fileName
    totalSize
    receivedChunks
    totalChunks
    receivedBytes
    progress
    status
    createdAt
    lastActivityAt
    completedAt
    errorMessage
    document {
      id
      name
      version
    }
  }
}
```

### 3. Document Processing Status

```graphql
query GetDocumentProcessingStatus {
  documentProcessingStatus(jobId: "job-123") {
    jobId
    status
    fileName
    fileSize
    progress
    estimatedTimeRemaining
    startedAt
    completedAt
    errorMessage
    document {
      id
      name
      version
      status
    }
  }
}
```

## Error Handling

### 1. Common Error Responses

```json
// Authentication Error
{
  "errors": [
    {
      "message": "Authentication required. Please provide a valid JWT token.",
      "locations": [{"line": 2, "column": 3}],
      "path": ["uploadDocument"],
      "extensions": {
        "code": "UNAUTHENTICATED",
        "exception": {
          "stacktrace": ["UnauthorizedException: Authentication required..."]
        }
      }
    }
  ],
  "data": null
}

// Validation Error
{
  "errors": [
    {
      "message": "File size exceeds maximum limit of 100MB",
      "locations": [{"line": 2, "column": 3}],
      "path": ["uploadDocument"],
      "extensions": {
        "code": "BAD_USER_INPUT",
        "validationErrors": [
          {
            "field": "file",
            "code": "FILE_TOO_LARGE",
            "message": "File size exceeds maximum limit of 100MB"
          }
        ]
      }
    }
  ],
  "data": null
}

// Rate Limit Error
{
  "errors": [
    {
      "message": "Document upload rate limit exceeded. Please wait before uploading again.",
      "locations": [{"line": 2, "column": 3}],
      "path": ["uploadDocument"],
      "extensions": {
        "code": "RATE_LIMITED",
        "retryAfter": 300
      }
    }
  ],
  "data": null
}
```

### 2. Partial Success Responses

```json
// Bulk Upload with Some Failures
{
  "data": {
    "bulkUploadDocuments": {
      "totalFiles": 5,
      "successfulUploads": 3,
      "failedUploads": 2,
      "results": [
        {
          "success": true,
          "document": {
            "id": "123",
            "name": "document1.pdf",
            "version": 1,
            "fileSize": 1024000,
            "mimeType": "application/pdf"
          },
          "uploadId": "upload-123",
          "fileName": "document1.pdf",
          "processingStatus": "COMPLETED",
          "message": "Upload successful"
        },
        {
          "success": false,
          "document": null,
          "uploadId": "upload-124",
          "fileName": "document2.pdf",
          "processingStatus": "FAILED",
          "message": "Upload failed",
          "errorMessage": "Virus detected in file"
        }
      ],
      "overallStatus": "PARTIAL_SUCCESS",
      "errors": [
        {
          "fileName": "document2.pdf",
          "errorCode": "VIRUS_DETECTED",
          "errorMessage": "File contains malicious content",
          "details": "Trojan.Generic.123456"
        }
      ]
    }
  }
}
```

### 3. Error Handling Best Practices

```graphql
# Always check for errors in the response
query SearchDocumentsWithErrorHandling {
  searchDocuments(
    filter: {
      name: "report"
    }
    pagination: {
      page: 0
      size: 10
    }
  ) {
    content {
      id
      name
      version
      status
    }
    totalElements
    totalPages
  }
}
```

```javascript
// Client-side error handling example
const response = await graphqlClient.request(SEARCH_DOCUMENTS_QUERY, variables);

if (response.errors) {
  response.errors.forEach(error => {
    console.error('GraphQL Error:', error.message);
    
    // Handle specific error types
    switch (error.extensions?.code) {
      case 'UNAUTHENTICATED':
        // Redirect to login
        break;
      case 'RATE_LIMITED':
        // Show rate limit message and retry after delay
        const retryAfter = error.extensions.retryAfter;
        setTimeout(() => retryRequest(), retryAfter * 1000);
        break;
      case 'BAD_USER_INPUT':
        // Show validation errors
        const validationErrors = error.extensions.validationErrors;
        showValidationErrors(validationErrors);
        break;
      default:
        // Show generic error message
        showErrorMessage(error.message);
    }
  });
}
```

## Best Practices

### 1. Authentication and Security

```graphql
# Always include proper authentication headers
# Authorization: Bearer <jwt-token>

# Use HTTPS in production
# Validate JWT tokens on server side
# Implement proper CORS policies
```

### 2. File Upload Best Practices

```graphql
# For large files, use chunked upload
mutation UploadLargeFile {
  # Step 1: Initialize chunked upload
  initializeChunkedUpload(input: {
    fileName: "large-file.pdf"
    totalSize: 104857600  # 100MB
    chunkSize: 1048576    # 1MB chunks
  }) {
    sessionId
    totalChunks
  }
  
  # Step 2: Upload chunks sequentially or in parallel
  # Step 3: Complete the upload
}

# Always validate files before upload
query ValidateBeforeUpload {
  validateFile(
    file: null
    validationOptions: {
      skipVirusScan: false
      validateFileType: true
      maxFileSize: 52428800  # 50MB
    }
  ) {
    isValid
    validationErrors {
      code
      message
    }
  }
}
```

### 3. Search Optimization

```graphql
# Use pagination for large result sets
query OptimizedSearch {
  searchDocuments(
    filter: {
      name: "report"
    }
    pagination: {
      page: 0
      size: 20  # Reasonable page size
      sortBy: "relevanceScore"
      sortDirection: "DESC"
    }
  ) {
    content {
      # Only request fields you need
      id
      name
      version
      createdDate
    }
    totalElements
  }
}

# Use faceted search for better user experience
query FacetedSearch {
  advancedSearch(
    input: {
      query: "financial report"
      # Include facet filters
    }
  ) {
    documents {
      content {
        id
        name
        relevanceScore
      }
    }
    facets {
      documentTypes {
        value
        count
      }
      creators {
        value
        count
      }
    }
  }
}
```

### 4. Performance Optimization

```graphql
# Request only necessary fields
query OptimizedDocumentQuery {
  getDocument(id: "123") {
    id
    name
    version
    fileSize
    # Don't request fileContent for large files
    # Use REST API for file downloads instead
  }
}

# Use batch operations when possible
mutation BatchOperations {
  bulkUploadDocuments(input: {
    # Upload multiple files at once
  }) {
    totalFiles
    successfulUploads
  }
}
```

### 5. Error Handling and Retry Logic

```javascript
// Implement exponential backoff for retries
async function uploadWithRetry(uploadFunction, maxRetries = 3) {
  for (let attempt = 1; attempt <= maxRetries; attempt++) {
    try {
      return await uploadFunction();
    } catch (error) {
      if (attempt === maxRetries) throw error;
      
      // Exponential backoff: 1s, 2s, 4s
      const delay = Math.pow(2, attempt - 1) * 1000;
      await new Promise(resolve => setTimeout(resolve, delay));
    }
  }
}

// Handle specific error scenarios
function handleUploadError(error) {
  switch (error.extensions?.code) {
    case 'FILE_TOO_LARGE':
      // Suggest chunked upload
      return 'File is too large. Please use chunked upload for files over 50MB.';
    case 'VIRUS_DETECTED':
      // Security warning
      return 'File contains malicious content and cannot be uploaded.';
    case 'UNSUPPORTED_FILE_TYPE':
      // File type guidance
      return 'File type not supported. Please upload PDF, Word, or image files.';
    default:
      return 'Upload failed. Please try again.';
  }
}
```

### 6. Monitoring and Logging

```graphql
# Track upload progress for user feedback
query TrackUploadProgress {
  getUploadProgress(uploadId: "upload-123") {
    progress
    status
    estimatedTimeRemaining
  }
}

# Monitor system health
query SystemHealth {
  getUploadStatistics(
    dateFrom: "2024-01-01T00:00:00Z"
    dateTo: "2024-12-31T23:59:59Z"
  ) {
    totalUploads
    successfulUploads
    failedUploads
    averageProcessingTime
  }
}
```

### 7. Rate Limiting and Throttling

```javascript
// Implement client-side rate limiting
class RateLimiter {
  constructor(maxRequests = 10, windowMs = 60000) {
    this.maxRequests = maxRequests;
    this.windowMs = windowMs;
    this.requests = [];
  }
  
  async throttle() {
    const now = Date.now();
    this.requests = this.requests.filter(time => now - time < this.windowMs);
    
    if (this.requests.length >= this.maxRequests) {
      const oldestRequest = Math.min(...this.requests);
      const waitTime = this.windowMs - (now - oldestRequest);
      await new Promise(resolve => setTimeout(resolve, waitTime));
    }
    
    this.requests.push(now);
  }
}

// Usage
const rateLimiter = new RateLimiter(5, 60000); // 5 requests per minute

async function uploadDocument(input) {
  await rateLimiter.throttle();
  return await graphqlClient.request(UPLOAD_DOCUMENT_MUTATION, { input });
}
```

### 8. Caching Strategies

```javascript
// Cache search results and document metadata
const cache = new Map();

async function searchDocumentsWithCache(filter, pagination) {
  const cacheKey = JSON.stringify({ filter, pagination });
  
  if (cache.has(cacheKey)) {
    const cached = cache.get(cacheKey);
    if (Date.now() - cached.timestamp < 300000) { // 5 minutes
      return cached.data;
    }
  }
  
  const result = await graphqlClient.request(SEARCH_DOCUMENTS_QUERY, {
    filter,
    pagination
  });
  
  cache.set(cacheKey, {
    data: result,
    timestamp: Date.now()
  });
  
  return result;
}
```

### 9. Testing Examples

```javascript
// Unit test example for GraphQL operations
describe('Document Upload', () => {
  test('should upload document successfully', async () => {
    const mockFile = new File(['test content'], 'test.pdf', {
      type: 'application/pdf'
    });
    
    const input = {
      name: 'Test Document',
      description: 'Test upload',
      file: mockFile,
      keywords: ['test'],
      storageProvider: 'LOCAL'
    };
    
    const result = await uploadDocument(input);
    
    expect(result.uploadDocument.success).toBe(true);
    expect(result.uploadDocument.document.name).toBe('Test Document');
  });
  
  test('should handle upload errors gracefully', async () => {
    const oversizedFile = new File(['x'.repeat(100000000)], 'large.pdf', {
      type: 'application/pdf'
    });
    
    const input = {
      name: 'Large Document',
      file: oversizedFile
    };
    
    await expect(uploadDocument(input)).rejects.toThrow('FILE_TOO_LARGE');
  });
});
```

### 10. Production Deployment Considerations

```yaml
# Environment-specific configurations
production:
  graphql:
    max_query_depth: 10
    max_query_complexity: 1000
    timeout: 30s
    rate_limiting:
      requests_per_minute: 100
      burst_size: 20
  
  file_upload:
    max_file_size: 100MB
    chunk_size: 1MB
    allowed_mime_types:
      - application/pdf
      - application/msword
      - image/jpeg
      - image/png
    virus_scanning: true
    storage_provider: S3
  
  security:
    jwt_expiration: 24h
    cors_origins:
      - https://app.company.com
      - https://admin.company.com
    https_only: true
```

---

## Summary

This document provides exhaustive GraphQL examples for the DMS service covering:

- **Upload Operations**: Basic uploads, enhanced uploads, chunked uploads, server path uploads, version uploads
- **Download Operations**: Document retrieval, version management, content access
- **Search Operations**: Basic search, advanced Elasticsearch search, metadata-based search, faceted search
- **File Conversion**: Markdown to Word, PDF to Word, Word to PDF, batch conversions
- **Document Sharing**: Share links, bulk sharing, access control
- **Bulk Operations**: Batch uploads, bulk processing, concurrent operations
- **Advanced Features**: Progress tracking, statistics, session management
- **Error Handling**: Comprehensive error scenarios and recovery strategies
- **Best Practices**: Security, performance, monitoring, testing, and deployment guidelines

All examples include proper authentication, error handling, and follow GraphQL best practices for production use.