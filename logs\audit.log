2025-07-21 15:28:33.261 [tomcat-handler-8] INFO  [graphiql-test-123] [958967e2-8cba-4aca-8aa4-e995371c4751] [] [graphiql] AUDIT - AUDIT_EVENT: {"result":"SUCCESS","variables":"{}","requestId":"958967e2-8cba-4aca-8aa4-e995371c4751","entityType":"GraphQL","action":"API_CALL_RECEIVED","correlationId":"graphiql-test-123","entityId":"mutation","details":"GraphQL operation received","sourceService":"graphiql","userId":null,"operation":"mutation","timestamp":[2025,7,21,15,28,33,260565000]}
2025-07-21 15:28:35.159 [task-1] INFO  [] [] [<EMAIL>] [] AUDIT - AUDIT_EVENT: {"result":"SUCCESS","templateName":"welcome","requestId":null,"entityType":"Email","recipient":"<EMAIL>","action":"EMAIL_SENT","correlationId":null,"entityId":"<EMAIL>","details":"Email sent successfully","sourceService":null,"userId":"<EMAIL>","timestamp":[2025,7,21,15,28,35,159160500]}
2025-07-21 15:28:35.164 [task-1] INFO  [] [] [<EMAIL>] [] AUDIT - AUDIT_EVENT: {"result":"SUCCESS","templateName":"welcome","requestId":null,"entityType":"Email","recipient":"<EMAIL>","action":"EMAIL_SENT","correlationId":null,"entityId":"<EMAIL>","details":"Email sent successfully","sourceService":null,"userId":"<EMAIL>","timestamp":[2025,7,21,15,28,35,163167700]}
2025-07-21 15:28:35.195 [task-1] INFO  [] [] [<EMAIL>] [] AUDIT - AUDIT_EVENT: {"entityType":"Notification","entityId":"pending","notificationType":"EMAIL","userId":"<EMAIL>","result":"SUCCESS","sender":"<EMAIL>","requestId":null,"recipients":"<EMAIL>,<EMAIL>","action":"NOTIFICATION_SENT","correlationId":null,"details":"Notification sent successfully","sourceService":null,"timestamp":[2025,7,21,15,28,35,195052500]}
2025-07-21 15:28:35.211 [task-1] INFO  [graphiql-test-123] [ae167d74-9d0e-440c-8a3b-3935f0541e65] [] [graphiql] AUDIT - AUDIT_EVENT: {"result":"SUCCESS","duration":"2084ms","requestId":"ae167d74-9d0e-440c-8a3b-3935f0541e65","entityType":"GraphQL","action":"API_CALL_COMPLETED","correlationId":"graphiql-test-123","entityId":"mutation","details":"GraphQL operation completed successfully","sourceService":"graphiql","userId":null,"operation":"mutation","timestamp":[2025,7,21,15,28,35,211534600]}
