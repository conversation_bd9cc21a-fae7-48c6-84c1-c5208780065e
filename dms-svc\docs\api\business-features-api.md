# DMS Business Features API Documentation

**Last Updated:** July 23, 2024  
**Version:** 1.0  
**Generated:** 2024-07-23T11:35:00+05:30

## Table of Contents

1. [Overview](#overview)
2. [Authentication](#authentication)
3. [Document Management](#document-management)
4. [Search and Discovery](#search-and-discovery)
5. [User Management](#user-management)
6. [Workflow Management](#workflow-management)
7. [Reporting and Analytics](#reporting-and-analytics)
8. [Integration APIs](#integration-apis)
9. [Error Handling](#error-handling)
10. [Rate Limiting](#rate-limiting)

## Overview

The DMS Business Features API provides comprehensive document management capabilities designed for enterprise use. This API enables organizations to manage documents, implement workflows, control access, and integrate with existing business systems.

### Base URL
```
https://api.dms.ascentbusiness.com/v1
```

### API Versioning
- Current Version: v1
- Version Header: `API-Version: v1`
- Backward Compatibility: Maintained for 2 major versions

## Authentication

### JWT Token Authentication
All API requests require a valid JWT token in the Authorization header.

```http
Authorization: Bearer <jwt-token>
```

### Token Generation
```http
POST /auth/token
Content-Type: application/json

{
  "username": "<EMAIL>",
  "password": "secure_password"
}
```

**Response:**
```json
{
  "access_token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...",
  "token_type": "Bearer",
  "expires_in": 3600,
  "refresh_token": "refresh_token_here"
}
```

### Test Token Generation
For development and testing purposes:

```bash
curl -X POST https://api.dms.ascentbusiness.com/v1/auth/generateTestToken \
  -H "Content-Type: application/json" \
  -d '{"userId": "test-user", "permissions": ["READ", "WRITE"]}'
```

## Document Management

### Upload Document

#### Single File Upload
```http
POST /documents/upload
Authorization: Bearer <token>
Content-Type: multipart/form-data

file: <binary-data>
title: "Financial Report Q1 2024"
description: "Quarterly financial analysis and projections"
tags: ["finance", "quarterly", "2024"]
category: "reports"
```

**Response:**
```json
{
  "id": 12345,
  "title": "Financial Report Q1 2024",
  "filename": "financial_report_q1_2024.pdf",
  "size": 2048576,
  "contentType": "application/pdf",
  "uploadDate": "2024-07-23T11:35:00Z",
  "status": "PROCESSED",
  "downloadUrl": "/documents/12345/download",
  "metadata": {
    "pages": 25,
    "author": "Finance Team",
    "created": "2024-07-20T09:00:00Z"
  }
}
```

#### Chunked Upload (Large Files)
```http
POST /documents/upload/chunked/init
Authorization: Bearer <token>
Content-Type: application/json

{
  "filename": "large_video.mp4",
  "totalSize": 1073741824,
  "chunkSize": 5242880,
  "contentType": "video/mp4"
}
```

**Response:**
```json
{
  "sessionId": "upload-session-123",
  "totalChunks": 205,
  "expiresAt": "2024-07-24T11:35:00Z"
}
```

### Download Document
```http
GET /documents/{documentId}/download
Authorization: Bearer <token>
```

**Response:** Binary file content with appropriate headers

### Document Metadata
```http
GET /documents/{documentId}
Authorization: Bearer <token>
```

**Response:**
```json
{
  "id": 12345,
  "title": "Financial Report Q1 2024",
  "description": "Quarterly financial analysis and projections",
  "filename": "financial_report_q1_2024.pdf",
  "size": 2048576,
  "contentType": "application/pdf",
  "uploadDate": "2024-07-23T11:35:00Z",
  "lastModified": "2024-07-23T11:35:00Z",
  "version": 1,
  "status": "ACTIVE",
  "tags": ["finance", "quarterly", "2024"],
  "category": "reports",
  "permissions": {
    "read": true,
    "write": true,
    "delete": false,
    "share": true
  },
  "audit": {
    "createdBy": "<EMAIL>",
    "createdDate": "2024-07-23T11:35:00Z",
    "lastModifiedBy": "<EMAIL>",
    "lastModifiedDate": "2024-07-23T11:35:00Z"
  }
}
```

### Update Document Metadata
```http
PUT /documents/{documentId}/metadata
Authorization: Bearer <token>
Content-Type: application/json

{
  "title": "Updated Financial Report Q1 2024",
  "description": "Updated quarterly financial analysis",
  "tags": ["finance", "quarterly", "2024", "updated"],
  "category": "reports"
}
```

### Delete Document
```http
DELETE /documents/{documentId}
Authorization: Bearer <token>
```

**Response:**
```json
{
  "message": "Document deleted successfully",
  "deletedAt": "2024-07-23T11:35:00Z"
}
```

## Search and Discovery

### Full-Text Search
```http
GET /documents/search?q=financial%20report&category=reports&page=0&size=20
Authorization: Bearer <token>
```

**Response:**
```json
{
  "content": [
    {
      "id": 12345,
      "title": "Financial Report Q1 2024",
      "snippet": "...quarterly financial analysis and projections...",
      "relevanceScore": 0.95,
      "highlightedText": "...quarterly <em>financial</em> analysis..."
    }
  ],
  "page": {
    "size": 20,
    "number": 0,
    "totalElements": 150,
    "totalPages": 8
  },
  "facets": {
    "categories": {
      "reports": 45,
      "contracts": 30,
      "invoices": 25
    },
    "fileTypes": {
      "pdf": 80,
      "docx": 40,
      "xlsx": 30
    }
  }
}
```

### Advanced Search
```http
POST /documents/search/advanced
Authorization: Bearer <token>
Content-Type: application/json

{
  "query": "financial report",
  "filters": {
    "category": ["reports", "finance"],
    "dateRange": {
      "from": "2024-01-01",
      "to": "2024-12-31"
    },
    "fileType": ["pdf", "docx"],
    "tags": ["quarterly"]
  },
  "sort": [
    {"field": "relevance", "direction": "desc"},
    {"field": "uploadDate", "direction": "desc"}
  ],
  "pagination": {
    "page": 0,
    "size": 20
  }
}
```

## User Management

### Get User Profile
```http
GET /users/profile
Authorization: Bearer <token>
```

**Response:**
```json
{
  "id": "user-123",
  "username": "<EMAIL>",
  "firstName": "John",
  "lastName": "Doe",
  "email": "<EMAIL>",
  "department": "Finance",
  "role": "MANAGER",
  "permissions": ["READ", "WRITE", "DELETE", "ADMIN"],
  "preferences": {
    "language": "en",
    "timezone": "America/New_York",
    "notifications": {
      "email": true,
      "push": false
    }
  },
  "quotas": {
    "storageUsed": 1073741824,
    "storageLimit": 10737418240,
    "monthlyUploads": 150,
    "monthlyUploadLimit": 1000
  }
}
```

### Update User Preferences
```http
PUT /users/profile/preferences
Authorization: Bearer <token>
Content-Type: application/json

{
  "language": "en",
  "timezone": "America/New_York",
  "notifications": {
    "email": true,
    "push": true
  }
}
```

## Workflow Management

### Create Approval Workflow
```http
POST /workflows/approval
Authorization: Bearer <token>
Content-Type: application/json

{
  "documentId": 12345,
  "workflowType": "DOCUMENT_APPROVAL",
  "approvers": [
    {
      "userId": "manager-123",
      "order": 1,
      "required": true
    },
    {
      "userId": "director-456",
      "order": 2,
      "required": true
    }
  ],
  "deadline": "2024-07-30T17:00:00Z",
  "instructions": "Please review and approve the quarterly financial report"
}
```

**Response:**
```json
{
  "workflowId": "workflow-789",
  "status": "PENDING",
  "createdDate": "2024-07-23T11:35:00Z",
  "currentStep": 1,
  "totalSteps": 2,
  "nextApprover": {
    "userId": "manager-123",
    "name": "Jane Manager",
    "email": "<EMAIL>"
  }
}
```

### Get Workflow Status
```http
GET /workflows/{workflowId}
Authorization: Bearer <token>
```

**Response:**
```json
{
  "workflowId": "workflow-789",
  "documentId": 12345,
  "status": "IN_PROGRESS",
  "createdDate": "2024-07-23T11:35:00Z",
  "currentStep": 2,
  "totalSteps": 2,
  "steps": [
    {
      "order": 1,
      "approver": "manager-123",
      "status": "APPROVED",
      "approvedDate": "2024-07-23T14:30:00Z",
      "comments": "Looks good, approved for next level"
    },
    {
      "order": 2,
      "approver": "director-456",
      "status": "PENDING",
      "assignedDate": "2024-07-23T14:30:00Z"
    }
  ]
}
```

### Approve/Reject Document
```http
POST /workflows/{workflowId}/approve
Authorization: Bearer <token>
Content-Type: application/json

{
  "action": "APPROVE",
  "comments": "Financial projections look accurate and well-researched"
}
```

## Reporting and Analytics

### Document Usage Analytics
```http
GET /analytics/documents/usage?period=30d&groupBy=category
Authorization: Bearer <token>
```

**Response:**
```json
{
  "period": "30d",
  "totalDocuments": 1250,
  "totalDownloads": 5430,
  "totalUploads": 180,
  "breakdown": {
    "reports": {
      "documents": 450,
      "downloads": 2100,
      "uploads": 65
    },
    "contracts": {
      "documents": 320,
      "downloads": 1800,
      "uploads": 45
    },
    "invoices": {
      "documents": 480,
      "downloads": 1530,
      "uploads": 70
    }
  },
  "trends": {
    "daily": [
      {"date": "2024-07-01", "uploads": 8, "downloads": 185},
      {"date": "2024-07-02", "uploads": 12, "downloads": 203}
    ]
  }
}
```

### Storage Analytics
```http
GET /analytics/storage
Authorization: Bearer <token>
```

**Response:**
```json
{
  "totalStorage": 107374182400,
  "usedStorage": 85899345920,
  "availableStorage": 21474836480,
  "utilizationPercentage": 80.0,
  "breakdown": {
    "byFileType": {
      "pdf": 34359738368,
      "docx": 21474836480,
      "xlsx": 17179869184,
      "images": 12884901888
    },
    "byCategory": {
      "reports": 25769803776,
      "contracts": 21474836480,
      "invoices": 17179869184,
      "other": 21474836480
    }
  },
  "growth": {
    "monthlyGrowth": 5368709120,
    "projectedFull": "2024-12-15"
  }
}
```

## Integration APIs

### Webhook Configuration
```http
POST /webhooks
Authorization: Bearer <token>
Content-Type: application/json

{
  "url": "https://your-system.com/dms-webhook",
  "events": ["document.uploaded", "document.approved", "workflow.completed"],
  "secret": "webhook-secret-key",
  "active": true
}
```

### Export Documents
```http
POST /documents/export
Authorization: Bearer <token>
Content-Type: application/json

{
  "format": "zip",
  "filters": {
    "category": ["reports"],
    "dateRange": {
      "from": "2024-01-01",
      "to": "2024-03-31"
    }
  },
  "includeMetadata": true,
  "notificationEmail": "<EMAIL>"
}
```

**Response:**
```json
{
  "exportId": "export-123",
  "status": "PROCESSING",
  "estimatedCompletion": "2024-07-23T12:00:00Z",
  "downloadUrl": null
}
```

## Error Handling

### Standard HTTP Status Codes

- **200 OK**: Request successful
- **201 Created**: Resource created successfully
- **400 Bad Request**: Invalid request parameters
- **401 Unauthorized**: Authentication required or invalid
- **403 Forbidden**: Insufficient permissions
- **404 Not Found**: Resource not found
- **409 Conflict**: Resource conflict (e.g., duplicate)
- **413 Payload Too Large**: File size exceeds limits
- **429 Too Many Requests**: Rate limit exceeded
- **500 Internal Server Error**: Server error

### Error Response Format
```json
{
  "error": {
    "code": "DOCUMENT_NOT_FOUND",
    "message": "The requested document could not be found",
    "details": {
      "documentId": 12345,
      "timestamp": "2024-07-23T11:35:00Z",
      "requestId": "req-abc-123"
    }
  }
}
```

### Custom Error Codes

- **DOCUMENT_NOT_FOUND**: Document does not exist
- **UNAUTHORIZED**: Invalid or expired token
- **INSUFFICIENT_PERMISSIONS**: User lacks required permissions
- **FILE_TOO_LARGE**: File exceeds size limits
- **INVALID_FILE_TYPE**: File type not supported
- **QUOTA_EXCEEDED**: Storage or upload quota exceeded
- **WORKFLOW_ERROR**: Workflow processing error
- **VIRUS_DETECTED**: File failed security scan

## Rate Limiting

### Rate Limits
- **Standard Users**: 1000 requests/hour
- **Premium Users**: 5000 requests/hour
- **Enterprise Users**: 10000 requests/hour

### Rate Limit Headers
```http
X-RateLimit-Limit: 1000
X-RateLimit-Remaining: 999
X-RateLimit-Reset: 1627123456
```

### Rate Limit Exceeded Response
```json
{
  "error": {
    "code": "RATE_LIMIT_EXCEEDED",
    "message": "Rate limit exceeded. Please try again later.",
    "details": {
      "limit": 1000,
      "resetTime": "2024-07-23T12:00:00Z"
    }
  }
}
```

## Pagination

### Standard Pagination Parameters
- **page**: Page number (0-based, default: 0)
- **size**: Page size (default: 20, max: 100)

### Pagination Response Format
```json
{
  "content": [...],
  "page": {
    "size": 20,
    "number": 0,
    "totalElements": 150,
    "totalPages": 8,
    "first": true,
    "last": false
  }
}
```

## SDK and Client Libraries

### Official SDKs
- **Java**: `com.ascentbusiness:dms-java-sdk:1.0.0`
- **JavaScript/Node.js**: `@ascentbusiness/dms-js-sdk`
- **Python**: `ascentbusiness-dms-python`
- **C#**: `AscentBusiness.DMS.SDK`

### Example Usage (Java)
```java
DMSClient client = new DMSClient("your-api-key");
Document document = client.documents().upload(file, metadata);
SearchResult results = client.search().query("financial report");
```

---

## Support and Resources

- **API Documentation**: https://docs.dms.ascentbusiness.com
- **Developer Portal**: https://developers.ascentbusiness.com
- **Support**: <EMAIL>
- **Status Page**: https://status.ascentbusiness.com

**Last Updated:** July 23, 2024  
**API Version:** 1.0