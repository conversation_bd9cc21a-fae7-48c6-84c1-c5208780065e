<?xml version="1.0" encoding="UTF-8"?><!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Strict//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-strict.dtd"><html xmlns="http://www.w3.org/1999/xhtml" lang="en"><head><meta http-equiv="Content-Type" content="text/html;charset=UTF-8"/><link rel="stylesheet" href="../jacoco-resources/report.css" type="text/css"/><link rel="shortcut icon" href="../jacoco-resources/report.gif" type="image/gif"/><title>StorageConfigurationProperties.java</title><link rel="stylesheet" href="../jacoco-resources/prettify.css" type="text/css"/><script type="text/javascript" src="../jacoco-resources/prettify.js"></script></head><body onload="window['PR_TAB_WIDTH']=4;prettyPrint()"><div class="breadcrumb" id="breadcrumb"><span class="info"><a href="../jacoco-sessions.html" class="el_session">Sessions</a></span><a href="../index.html" class="el_report">dms-svc</a> &gt; <a href="index.source.html" class="el_package">com.ascentbusiness.dms_svc.config</a> &gt; <span class="el_source">StorageConfigurationProperties.java</span></div><h1>StorageConfigurationProperties.java</h1><pre class="source lang-java linenums">package com.ascentbusiness.dms_svc.config;

import com.ascentbusiness.dms_svc.enums.StorageProvider;
import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;

<span class="pc bpc" id="L8" title="34 of 38 branches missed.">@Data</span>
@Component
@ConfigurationProperties(prefix = &quot;dms.storage&quot;)
public class StorageConfigurationProperties {
    
    /**
     * Default storage provider for documents
     */
<span class="fc" id="L16">    private StorageProvider provider = StorageProvider.LOCAL;</span>
    
    /**
     * Local storage configuration
     */
<span class="fc" id="L21">    private Local local = new Local();</span>
    
    /**
     * S3 storage configuration
     */
<span class="fc" id="L26">    private S3 s3 = new S3();</span>

    /**
     * SharePoint storage configuration
     */
<span class="fc" id="L31">    private SharePoint sharepoint = new SharePoint();</span>

<span class="pc bpc" id="L33" title="13 of 14 branches missed.">    @Data</span>
    public static class Local {
        /**
         * Base path for local file storage
         */
<span class="fc" id="L38">        private String basePath = &quot;./storage/documents&quot;;</span>
    }
    
<span class="pc bpc" id="L41" title="41 of 46 branches missed.">    @Data</span>
    public static class S3 {
        /**
         * S3 bucket name
         */
<span class="fc" id="L46">        private String bucketName = &quot;grc-dms-bucket&quot;;</span>
        
        /**
         * AWS region
         */
<span class="fc" id="L51">        private String region = &quot;us-east-1&quot;;</span>
        
        /**
         * AWS access key
         */
<span class="fc" id="L56">        private String accessKey;</span>
        
        /**
         * AWS secret key
         */
<span class="fc" id="L61">        private String secretKey;</span>
        
        /**
         * S3 endpoint (for localstack or custom endpoints)
         */
<span class="fc" id="L66">        private String endpoint;</span>
    }
    
<span class="pc bpc" id="L69" title="86 of 98 branches missed.">    @Data</span>
    public static class SharePoint {
        /**
         * Azure AD Tenant ID
         */
<span class="fc" id="L74">        private String tenantId;</span>
        
        /**
         * Azure App Registration Client ID
         */
<span class="fc" id="L79">        private String clientId;</span>
        
        /**
         * Azure App Registration Client Secret
         */
<span class="fc" id="L84">        private String clientSecret;</span>
        
        /**
         * OAuth2 scopes for Microsoft Graph API
         */
<span class="fc" id="L89">        private String scopes = &quot;User.Read,Sites.ReadWrite.All,Files.ReadWrite.All&quot;;</span>
        
        /**
         * Microsoft Graph API base URL
         */
<span class="fc" id="L94">        private String graphApiUrl = &quot;https://graph.microsoft.com/v1.0&quot;;</span>
        
        /**
         * OAuth2 token URL for authentication
         */
<span class="fc" id="L99">        private String tokenUrl;</span>
        
        /**
         * SharePoint site URL
         */
<span class="fc" id="L104">        private String siteUrl;</span>
        
        /**
         * SharePoint Document Library name
         */
<span class="fc" id="L109">        private String documentLibrary = &quot;DMS&quot;;</span>
        
        /**
         * Connection timeout in milliseconds
         */
<span class="fc" id="L114">        private int connectionTimeout = 30000;</span>
        
        /**
         * Read timeout in milliseconds
         */
<span class="fc" id="L119">        private int readTimeout = 60000;</span>
        
        /**
         * Maximum retry attempts for failed operations
         */
<span class="fc" id="L124">        private int maxRetries = 3;</span>
        
        /**
         * Delay between retry attempts in milliseconds
         */
<span class="fc" id="L129">        private int retryDelayMs = 1000;</span>
        
        /**
         * Enable circuit breaker pattern
         */
<span class="fc" id="L134">        private boolean enableCircuitBreaker = true;</span>
        
        /**
         * Circuit breaker failure threshold
         */
<span class="fc" id="L139">        private int circuitBreakerThreshold = 5;</span>
        
        /**
         * Circuit breaker timeout in milliseconds
         */
<span class="fc" id="L144">        private int circuitBreakerTimeoutMs = 60000;</span>
        
        /**
         * Enable debug logging
         */
<span class="fc" id="L149">        private boolean debug = false;</span>
        
        /**
         * Enable request/response logging
         */
<span class="fc" id="L154">        private boolean enableRequestLogging = false;</span>
        
        /**
         * Enable fail-safe startup (application starts even if SharePoint connection fails)
         */
<span class="fc" id="L159">        private boolean failSafeStartup = false;</span>
    }
}
</pre><div class="footer"><span class="right">Created with <a href="http://www.jacoco.org/jacoco">JaCoCo</a> 0.8.10.202304240956</span></div></body></html>