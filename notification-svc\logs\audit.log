2025-07-23 22:20:04.839 [tomcat-handler-2] INFO  [test-upload-123] [e661589d-ed69-45a7-bc52-35229840e461] [] [graphiql] AUDIT - AUDIT_EVENT: {"result":"SUCCESS","variables":"{}","requestId":"e661589d-ed69-45a7-bc52-35229840e461","entityType":"GraphQL","action":"API_CALL_RECEIVED","correlationId":"test-upload-123","entityId":"mutation","details":"GraphQL operation received","sourceService":"graphiql","userId":null,"operation":"mutation","timestamp":[2025,7,23,22,20,4,837902500]}
2025-07-23 22:20:05.325 [notification-async-1] INFO  [] [] [<EMAIL>] [] AUDIT - AUDIT_EVENT: {"entityType":"Email","errorMessage":"Authentication failed","entityId":"<EMAIL>","userId":"<EMAIL>","result":"FAILURE","templateName":"welcome","requestId":null,"recipient":"<EMAIL>","action":"EMAIL_FAILED","correlationId":null,"details":"Email sending failed","sourceService":null,"timestamp":[2025,7,23,22,20,5,325861400]}
2025-07-23 22:20:05.328 [notification-async-1] INFO  [] [] [<EMAIL>] [] AUDIT - AUDIT_EVENT: {"entityType":"Email","errorMessage":"Authentication failed","entityId":"<EMAIL>","userId":"<EMAIL>","result":"FAILURE","templateName":"welcome","requestId":null,"recipient":"<EMAIL>","action":"EMAIL_FAILED","correlationId":null,"details":"Email sending failed","sourceService":null,"timestamp":[2025,7,23,22,20,5,328863800]}
2025-07-23 22:20:05.360 [notification-async-1] INFO  [] [] [<EMAIL>] [] AUDIT - AUDIT_EVENT: {"entityType":"Notification","errorMessage":"Failed to send notification: Failed to send enhanced email notification: Authentication failed","entityId":"failed","notificationType":"EMAIL","userId":"<EMAIL>","result":"FAILURE","sender":"<EMAIL>","requestId":null,"recipients":"<EMAIL>,<EMAIL>","action":"NOTIFICATION_SENT","correlationId":null,"details":"Notification sending failed","sourceService":null,"timestamp":[2025,7,23,22,20,5,360236700]}
2025-07-23 22:20:05.399 [notification-async-1] INFO  [test-upload-123] [3bfd913a-3bf5-4461-8988-b53f825e74da] [] [graphiql] AUDIT - AUDIT_EVENT: {"entityType":"GraphQL","errorMessage":"[INTERNAL_ERROR for 2237c4d1-a22e-6621-af1c-ad29246cc6ba]","entityId":"mutation","userId":null,"result":"FAILURE","duration":"707ms","requestId":"3bfd913a-3bf5-4461-8988-b53f825e74da","action":"API_CALL_FAILED","correlationId":"test-upload-123","details":"GraphQL operation failed","sourceService":"graphiql","operation":"mutation","timestamp":[2025,7,23,22,20,5,399342600]}
2025-07-23 22:22:17.907 [tomcat-handler-5] INFO  [test-upload-123] [8af9eee5-e6f2-4c86-9834-8663d14ff317] [] [graphiql] AUDIT - AUDIT_EVENT: {"result":"SUCCESS","variables":"{}","requestId":"8af9eee5-e6f2-4c86-9834-8663d14ff317","entityType":"GraphQL","action":"API_CALL_RECEIVED","correlationId":"test-upload-123","entityId":"mutation","details":"GraphQL operation received","sourceService":"graphiql","userId":null,"operation":"mutation","timestamp":[2025,7,23,22,22,17,907318200]}
2025-07-23 22:22:17.946 [notification-async-2] INFO  [] [] [<EMAIL>] [] AUDIT - AUDIT_EVENT: {"entityType":"Email","errorMessage":"Authentication failed","entityId":"<EMAIL>","userId":"<EMAIL>","result":"FAILURE","templateName":"welcome","requestId":null,"recipient":"<EMAIL>","action":"EMAIL_FAILED","correlationId":null,"details":"Email sending failed","sourceService":null,"timestamp":[2025,7,23,22,22,17,946298500]}
2025-07-23 22:22:17.951 [notification-async-2] INFO  [] [] [<EMAIL>] [] AUDIT - AUDIT_EVENT: {"entityType":"Email","errorMessage":"Authentication failed","entityId":"<EMAIL>","userId":"<EMAIL>","result":"FAILURE","templateName":"welcome","requestId":null,"recipient":"<EMAIL>","action":"EMAIL_FAILED","correlationId":null,"details":"Email sending failed","sourceService":null,"timestamp":[2025,7,23,22,22,17,951895100]}
2025-07-23 22:22:17.977 [notification-async-2] INFO  [] [] [<EMAIL>] [] AUDIT - AUDIT_EVENT: {"entityType":"Notification","errorMessage":"Failed to send notification: Failed to send enhanced email notification: Authentication failed","entityId":"failed","notificationType":"EMAIL","userId":"<EMAIL>","result":"FAILURE","sender":"<EMAIL>","requestId":null,"recipients":"<EMAIL>,<EMAIL>","action":"NOTIFICATION_SENT","correlationId":null,"details":"Notification sending failed","sourceService":null,"timestamp":[2025,7,23,22,22,17,976968000]}
2025-07-23 22:22:18.007 [notification-async-2] INFO  [test-upload-123] [12022fc9-a257-4317-8d5c-16b957a2e646] [] [graphiql] AUDIT - AUDIT_EVENT: {"entityType":"GraphQL","errorMessage":"[INTERNAL_ERROR for c848ec7d-349c-1d0c-6b1e-32f6383040a4]","entityId":"mutation","userId":null,"result":"FAILURE","duration":"94ms","requestId":"12022fc9-a257-4317-8d5c-16b957a2e646","action":"API_CALL_FAILED","correlationId":"test-upload-123","details":"GraphQL operation failed","sourceService":"graphiql","operation":"mutation","timestamp":[2025,7,23,22,22,18,6993700]}
2025-07-23 22:23:04.093 [tomcat-handler-8] INFO  [test-upload-123] [d2a708f4-c4e3-4da6-9fe3-e58233d5feb7] [] [graphiql] AUDIT - AUDIT_EVENT: {"result":"SUCCESS","variables":"{}","requestId":"d2a708f4-c4e3-4da6-9fe3-e58233d5feb7","entityType":"GraphQL","action":"API_CALL_RECEIVED","correlationId":"test-upload-123","entityId":"mutation","details":"GraphQL operation received","sourceService":"graphiql","userId":null,"operation":"mutation","timestamp":[2025,7,23,22,23,4,92864700]}
2025-07-23 22:23:04.147 [notification-async-3] INFO  [] [] [<EMAIL>] [] AUDIT - AUDIT_EVENT: {"entityType":"Email","errorMessage":"Authentication failed","entityId":"<EMAIL>","userId":"<EMAIL>","result":"FAILURE","templateName":"welcome","requestId":null,"recipient":"<EMAIL>","action":"EMAIL_FAILED","correlationId":null,"details":"Email sending failed","sourceService":null,"timestamp":[2025,7,23,22,23,4,147982400]}
2025-07-23 22:23:04.153 [notification-async-3] INFO  [] [] [<EMAIL>] [] AUDIT - AUDIT_EVENT: {"entityType":"Email","errorMessage":"Authentication failed","entityId":"<EMAIL>","userId":"<EMAIL>","result":"FAILURE","templateName":"welcome","requestId":null,"recipient":"<EMAIL>","action":"EMAIL_FAILED","correlationId":null,"details":"Email sending failed","sourceService":null,"timestamp":[2025,7,23,22,23,4,153418200]}
2025-07-23 22:23:04.177 [notification-async-3] INFO  [] [] [<EMAIL>] [] AUDIT - AUDIT_EVENT: {"entityType":"Notification","errorMessage":"Failed to send notification: Failed to send enhanced email notification: Authentication failed","entityId":"failed","notificationType":"EMAIL","userId":"<EMAIL>","result":"FAILURE","sender":"<EMAIL>","requestId":null,"recipients":"<EMAIL>,<EMAIL>","action":"NOTIFICATION_SENT","correlationId":null,"details":"Notification sending failed","sourceService":null,"timestamp":[2025,7,23,22,23,4,177110900]}
2025-07-23 22:23:04.189 [notification-async-3] INFO  [test-upload-123] [d2841716-22f6-4ef5-ac2a-e8c601c8d185] [] [graphiql] AUDIT - AUDIT_EVENT: {"entityType":"GraphQL","errorMessage":"[INTERNAL_ERROR for ba560a38-7c2a-2444-7fd7-cf1f785cc243]","entityId":"mutation","userId":null,"result":"FAILURE","duration":"104ms","requestId":"d2841716-22f6-4ef5-ac2a-e8c601c8d185","action":"API_CALL_FAILED","correlationId":"test-upload-123","details":"GraphQL operation failed","sourceService":"graphiql","operation":"mutation","timestamp":[2025,7,23,22,23,4,189869200]}
2025-07-23 22:30:52.583 [tomcat-handler-0] INFO  [test-upload-123] [32838d0c-be13-459d-a1b0-9ee9154f9813] [] [graphiql] AUDIT - AUDIT_EVENT: {"result":"SUCCESS","variables":"{}","requestId":"32838d0c-be13-459d-a1b0-9ee9154f9813","entityType":"GraphQL","action":"API_CALL_RECEIVED","correlationId":"test-upload-123","entityId":"mutation","details":"GraphQL operation received","sourceService":"graphiql","userId":null,"operation":"mutation","timestamp":[2025,7,23,22,30,52,581864900]}
2025-07-23 22:30:54.080 [notification-async-1] INFO  [] [] [<EMAIL>] [] AUDIT - AUDIT_EVENT: {"result":"SUCCESS","templateName":"welcome","requestId":null,"entityType":"Email","recipient":"<EMAIL>","action":"EMAIL_SENT","correlationId":null,"entityId":"<EMAIL>","details":"Email sent successfully","sourceService":null,"userId":"<EMAIL>","timestamp":[2025,7,23,22,30,54,80041900]}
2025-07-23 22:30:54.083 [notification-async-1] INFO  [] [] [<EMAIL>] [] AUDIT - AUDIT_EVENT: {"result":"SUCCESS","templateName":"welcome","requestId":null,"entityType":"Email","recipient":"<EMAIL>","action":"EMAIL_SENT","correlationId":null,"entityId":"<EMAIL>","details":"Email sent successfully","sourceService":null,"userId":"<EMAIL>","timestamp":[2025,7,23,22,30,54,83043500]}
2025-07-23 22:30:54.101 [notification-async-1] INFO  [] [] [<EMAIL>] [] AUDIT - AUDIT_EVENT: {"entityType":"Notification","entityId":"pending","notificationType":"EMAIL","userId":"<EMAIL>","result":"SUCCESS","sender":"<EMAIL>","requestId":null,"recipients":"<EMAIL>,<EMAIL>","action":"NOTIFICATION_SENT","correlationId":null,"details":"Notification sent successfully","sourceService":null,"timestamp":[2025,7,23,22,30,54,101349200]}
2025-07-23 22:30:54.115 [notification-async-1] INFO  [test-upload-123] [543702fa-4773-43dd-8524-f29e6b847563] [] [graphiql] AUDIT - AUDIT_EVENT: {"result":"SUCCESS","duration":"1617ms","requestId":"543702fa-4773-43dd-8524-f29e6b847563","entityType":"GraphQL","action":"API_CALL_COMPLETED","correlationId":"test-upload-123","entityId":"mutation","details":"GraphQL operation completed successfully","sourceService":"graphiql","userId":null,"operation":"mutation","timestamp":[2025,7,23,22,30,54,115986600]}
