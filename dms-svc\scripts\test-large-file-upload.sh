#!/bin/bash

# Test script for large file upload using UploadDocumentFromPathEx
# This script tests the 61MB file upload functionality

echo "Testing large file upload with UploadDocumentFromPathEx..."

# Configuration
SERVER_URL="http://localhost:8080/graphql"
FILE_PATH="/path/to/your/61MB/file.pdf"  # Update this path
JWT_TOKEN="your-jwt-token-here"  # Update with your JWT token

# GraphQL mutation for UploadDocumentFromPathEx
MUTATION='
mutation UploadDocumentFromPathEx($input: UploadDocumentFromPathExInput!) {
  uploadDocumentFromPathEx(input: $input) {
    document {
      id
      name
      originalFileName
      fileSize
      status
      processingStrategy
      processingStatus
      storageProvider
      storagePath
    }
    processingStrategy
    processingStatus
    jobId
  }
}
'

# Input variables
VARIABLES='{
  "input": {
    "sourceFilePath": "'$FILE_PATH'",
    "name": "Test Large File Upload",
    "description": "Testing 61MB file upload with chunked processing",
    "fileSize": 64000000,
    "filename": "large-test-file.pdf",
    "storageProvider": "LOCAL",
    "overrideFile": true
  }
}'

# Make the GraphQL request
echo "Making GraphQL request..."
curl -X POST \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer $JWT_TOKEN" \
  -d "{\"query\": \"$MUTATION\", \"variables\": $VARIABLES}" \
  $SERVER_URL

echo ""
echo "Test completed. Check the response above for results."