<?xml version="1.0" encoding="UTF-8"?><!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Strict//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-strict.dtd"><html xmlns="http://www.w3.org/1999/xhtml" lang="en"><head><meta http-equiv="Content-Type" content="text/html;charset=UTF-8"/><link rel="stylesheet" href="../jacoco-resources/report.css" type="text/css"/><link rel="shortcut icon" href="../jacoco-resources/report.gif" type="image/gif"/><title>WorkflowDefinitionResolver.java</title><link rel="stylesheet" href="../jacoco-resources/prettify.css" type="text/css"/><script type="text/javascript" src="../jacoco-resources/prettify.js"></script></head><body onload="window['PR_TAB_WIDTH']=4;prettyPrint()"><div class="breadcrumb" id="breadcrumb"><span class="info"><a href="../jacoco-sessions.html" class="el_session">Sessions</a></span><a href="../index.html" class="el_report">dms-svc</a> &gt; <a href="index.source.html" class="el_package">com.ascentbusiness.dms_svc.resolver</a> &gt; <span class="el_source">WorkflowDefinitionResolver.java</span></div><h1>WorkflowDefinitionResolver.java</h1><pre class="source lang-java linenums">package com.ascentbusiness.dms_svc.resolver;

import com.ascentbusiness.dms_svc.entity.WorkflowDefinition;
import com.ascentbusiness.dms_svc.entity.WorkflowStage;
import com.ascentbusiness.dms_svc.entity.WorkflowInstance;
import com.ascentbusiness.dms_svc.enums.WorkflowType;
import com.ascentbusiness.dms_svc.service.WorkflowDefinitionService;
import com.ascentbusiness.dms_svc.service.WorkflowInstanceService;
import com.ascentbusiness.dms_svc.security.UserContext;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Sort;
import org.springframework.graphql.data.method.annotation.Argument;
import org.springframework.graphql.data.method.annotation.MutationMapping;
import org.springframework.graphql.data.method.annotation.QueryMapping;
import org.springframework.graphql.data.method.annotation.SchemaMapping;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.stereotype.Controller;

import java.util.List;
import java.util.Optional;

/**
 * GraphQL resolver for WorkflowDefinition operations
 */
@Controller
<span class="fc" id="L30">@RequiredArgsConstructor</span>
<span class="fc" id="L31">@Slf4j</span>
public class WorkflowDefinitionResolver {

    private final WorkflowDefinitionService workflowDefinitionService;
    private final WorkflowInstanceService workflowInstanceService;
    private final UserContext userContext;

    // ===== QUERIES =====

    @QueryMapping
    @PreAuthorize(&quot;hasAnyRole('ADMIN', 'MANAGER', 'USER')&quot;)
    public WorkflowDefinition getWorkflowDefinition(@Argument Long id) {
<span class="nc" id="L43">        log.info(&quot;Getting workflow definition with ID: {}&quot;, id);</span>
<span class="nc" id="L44">        return workflowDefinitionService.getWorkflowDefinitionById(id);</span>
    }

    @QueryMapping
    @PreAuthorize(&quot;hasAnyRole('ADMIN', 'MANAGER', 'USER')&quot;)
    public WorkflowDefinitionPage getWorkflowDefinitions(@Argument WorkflowPaginationInput pagination) {
<span class="nc" id="L50">        log.info(&quot;Getting workflow definitions with pagination: {}&quot;, pagination);</span>
        
<span class="nc" id="L52">        Pageable pageable = createPageable(pagination);</span>
<span class="nc" id="L53">        Page&lt;WorkflowDefinition&gt; page = workflowDefinitionService.getWorkflowDefinitions(pageable);</span>
        
<span class="nc" id="L55">        return WorkflowDefinitionPage.builder()</span>
<span class="nc" id="L56">                .content(page.getContent())</span>
<span class="nc" id="L57">                .totalElements((int) page.getTotalElements())</span>
<span class="nc" id="L58">                .totalPages(page.getTotalPages())</span>
<span class="nc" id="L59">                .size(page.getSize())</span>
<span class="nc" id="L60">                .number(page.getNumber())</span>
<span class="nc" id="L61">                .first(page.isFirst())</span>
<span class="nc" id="L62">                .last(page.isLast())</span>
<span class="nc" id="L63">                .build();</span>
    }

    @QueryMapping
    @PreAuthorize(&quot;hasAnyRole('ADMIN', 'MANAGER', 'USER')&quot;)
    public List&lt;WorkflowDefinition&gt; getWorkflowDefinitionsByType(@Argument WorkflowType workflowType) {
<span class="nc" id="L69">        log.info(&quot;Getting workflow definitions by type: {}&quot;, workflowType);</span>
<span class="nc" id="L70">        return workflowDefinitionService.getWorkflowDefinitionsByType(workflowType);</span>
    }

    @QueryMapping
    @PreAuthorize(&quot;hasAnyRole('ADMIN', 'MANAGER', 'USER')&quot;)
    public List&lt;WorkflowDefinition&gt; getActiveWorkflowDefinitions() {
<span class="nc" id="L76">        log.info(&quot;Getting active workflow definitions&quot;);</span>
<span class="nc" id="L77">        return workflowDefinitionService.getAllActiveWorkflowDefinitions();</span>
    }

    @QueryMapping
    @PreAuthorize(&quot;hasAnyRole('ADMIN', 'MANAGER', 'USER')&quot;)
    public WorkflowDefinition getDefaultWorkflowDefinition(@Argument WorkflowType workflowType) {
<span class="nc" id="L83">        log.info(&quot;Getting default workflow definition for type: {}&quot;, workflowType);</span>
<span class="nc" id="L84">        Optional&lt;WorkflowDefinition&gt; definition = workflowDefinitionService.getDefaultWorkflowDefinition(workflowType);</span>
<span class="nc" id="L85">        return definition.orElse(null);</span>
    }

    @QueryMapping
    @PreAuthorize(&quot;hasAnyRole('ADMIN', 'MANAGER', 'USER')&quot;)
    public List&lt;WorkflowDefinition&gt; searchWorkflowDefinitions(@Argument String name) {
<span class="nc" id="L91">        log.info(&quot;Searching workflow definitions with name: {}&quot;, name);</span>
<span class="nc" id="L92">        return workflowDefinitionService.searchWorkflowDefinitions(name);</span>
    }



    // ===== MUTATIONS =====

    @MutationMapping
    @PreAuthorize(&quot;hasAnyRole('ADMIN', 'MANAGER')&quot;)
    public WorkflowDefinition createWorkflowDefinition(@Argument WorkflowDefinitionInput input) {
<span class="nc" id="L102">        log.info(&quot;Creating workflow definition: {}&quot;, input.getName());</span>
        
<span class="nc" id="L104">        WorkflowDefinition definition = WorkflowDefinition.builder()</span>
<span class="nc" id="L105">                .name(input.getName())</span>
<span class="nc" id="L106">                .description(input.getDescription())</span>
<span class="nc" id="L107">                .workflowType(input.getWorkflowType())</span>
<span class="nc" id="L108">                .approvalType(input.getApprovalType())</span>
<span class="nc" id="L109">                .autoStart(input.getAutoStart())</span>
<span class="nc" id="L110">                .timeoutHours(input.getTimeoutHours())</span>
<span class="nc" id="L111">                .escalationEnabled(input.getEscalationEnabled())</span>
<span class="nc" id="L112">                .escalationHours(input.getEscalationHours())</span>
<span class="nc" id="L113">                .build();</span>
        
<span class="nc" id="L115">        return workflowDefinitionService.createWorkflowDefinition(definition, userContext.getUserId());</span>
    }

    @MutationMapping
    @PreAuthorize(&quot;hasAnyRole('ADMIN', 'MANAGER')&quot;)
    public WorkflowDefinition updateWorkflowDefinition(@Argument Long id, @Argument WorkflowDefinitionInput input) {
<span class="nc" id="L121">        log.info(&quot;Updating workflow definition ID: {}&quot;, id);</span>
        
<span class="nc" id="L123">        WorkflowDefinition definition = WorkflowDefinition.builder()</span>
<span class="nc" id="L124">                .name(input.getName())</span>
<span class="nc" id="L125">                .description(input.getDescription())</span>
<span class="nc" id="L126">                .workflowType(input.getWorkflowType())</span>
<span class="nc" id="L127">                .approvalType(input.getApprovalType())</span>
<span class="nc" id="L128">                .autoStart(input.getAutoStart())</span>
<span class="nc" id="L129">                .timeoutHours(input.getTimeoutHours())</span>
<span class="nc" id="L130">                .escalationEnabled(input.getEscalationEnabled())</span>
<span class="nc" id="L131">                .escalationHours(input.getEscalationHours())</span>
<span class="nc" id="L132">                .build();</span>
        
<span class="nc" id="L134">        return workflowDefinitionService.updateWorkflowDefinition(id, definition, userContext.getUserId());</span>
    }

    @MutationMapping
    @PreAuthorize(&quot;hasAnyRole('ADMIN', 'MANAGER')&quot;)
    public WorkflowDefinition activateWorkflowDefinition(@Argument Long id) {
<span class="nc" id="L140">        log.info(&quot;Activating workflow definition ID: {}&quot;, id);</span>
<span class="nc" id="L141">        return workflowDefinitionService.activateWorkflowDefinition(id, userContext.getUserId());</span>
    }

    @MutationMapping
    @PreAuthorize(&quot;hasAnyRole('ADMIN', 'MANAGER')&quot;)
    public WorkflowDefinition deactivateWorkflowDefinition(@Argument Long id) {
<span class="nc" id="L147">        log.info(&quot;Deactivating workflow definition ID: {}&quot;, id);</span>
<span class="nc" id="L148">        return workflowDefinitionService.deactivateWorkflowDefinition(id, userContext.getUserId());</span>
    }

    @MutationMapping
    @PreAuthorize(&quot;hasAnyRole('ADMIN', 'MANAGER')&quot;)
    public WorkflowDefinition setWorkflowDefinitionAsDefault(@Argument Long id) {
<span class="nc" id="L154">        log.info(&quot;Setting workflow definition ID: {} as default&quot;, id);</span>
<span class="nc" id="L155">        return workflowDefinitionService.setAsDefault(id, userContext.getUserId());</span>
    }

    @MutationMapping
    @PreAuthorize(&quot;hasRole('ADMIN')&quot;)
    public Boolean deleteWorkflowDefinition(@Argument Long id) {
<span class="nc" id="L161">        log.info(&quot;Deleting workflow definition ID: {}&quot;, id);</span>
<span class="nc" id="L162">        workflowDefinitionService.deleteWorkflowDefinition(id, userContext.getUserId());</span>
<span class="nc" id="L163">        return true;</span>
    }

    // ===== FIELD RESOLVERS =====

    @SchemaMapping(typeName = &quot;WorkflowDefinition&quot;, field = &quot;stages&quot;)
    public List&lt;WorkflowStage&gt; getStages(WorkflowDefinition workflowDefinition) {
        // This would be loaded via JPA relationships or a separate service call
<span class="nc bnc" id="L171" title="All 2 branches missed.">        return workflowDefinition.getStages() != null ? </span>
<span class="nc" id="L172">               workflowDefinition.getStages().stream().toList() : List.of();</span>
    }

    @SchemaMapping(typeName = &quot;WorkflowDefinition&quot;, field = &quot;instances&quot;)
    public List&lt;WorkflowInstance&gt; getInstances(WorkflowDefinition workflowDefinition) {
<span class="nc" id="L177">        return workflowInstanceService.getWorkflowInstancesForDefinition(workflowDefinition.getId());</span>
    }

    @SchemaMapping(typeName = &quot;WorkflowDefinition&quot;, field = &quot;stageCount&quot;)
    public Integer getStageCount(WorkflowDefinition workflowDefinition) {
<span class="nc" id="L182">        return workflowDefinition.getStageCount();</span>
    }

    @SchemaMapping(typeName = &quot;WorkflowDefinition&quot;, field = &quot;estimatedCompletionHours&quot;)
    public Integer getEstimatedCompletionHours(WorkflowDefinition workflowDefinition) {
<span class="nc" id="L187">        return workflowDefinition.getEstimatedCompletionHours();</span>
    }

    @SchemaMapping(typeName = &quot;WorkflowDefinition&quot;, field = &quot;supportsParallelProcessing&quot;)
    public Boolean getSupportsParallelProcessing(WorkflowDefinition workflowDefinition) {
<span class="nc" id="L192">        return workflowDefinition.supportsParallelProcessing();</span>
    }

    // ===== HELPER METHODS =====

    private Pageable createPageable(WorkflowPaginationInput pagination) {
<span class="nc bnc" id="L198" title="All 2 branches missed.">        if (pagination == null) {</span>
<span class="nc" id="L199">            return PageRequest.of(0, 10, Sort.by(Sort.Direction.DESC, &quot;createdDate&quot;));</span>
        }
        
<span class="nc bnc" id="L202" title="All 2 branches missed.">        Sort.Direction direction = &quot;ASC&quot;.equalsIgnoreCase(pagination.getSortDirection()) ? </span>
<span class="nc" id="L203">                                  Sort.Direction.ASC : Sort.Direction.DESC;</span>
<span class="nc bnc" id="L204" title="All 2 branches missed.">        Sort sort = Sort.by(direction, pagination.getSortBy() != null ? pagination.getSortBy() : &quot;createdDate&quot;);</span>
        
<span class="nc" id="L206">        return PageRequest.of(</span>
<span class="nc bnc" id="L207" title="All 2 branches missed.">                pagination.getPage() != null ? pagination.getPage() : 0,</span>
<span class="nc bnc" id="L208" title="All 2 branches missed.">                pagination.getSize() != null ? pagination.getSize() : 10,</span>
                sort
        );
    }

    // ===== INPUT/OUTPUT CLASSES =====

<span class="nc" id="L215">    public static class WorkflowDefinitionInput {</span>
        private String name;
        private String description;
        private WorkflowType workflowType;
        private com.ascentbusiness.dms_svc.enums.ApprovalType approvalType;
        private Boolean autoStart;
        private Integer timeoutHours;
        private Boolean escalationEnabled;
        private Integer escalationHours;
        
        // Getters and setters
<span class="nc" id="L226">        public String getName() { return name; }</span>
<span class="nc" id="L227">        public void setName(String name) { this.name = name; }</span>
<span class="nc" id="L228">        public String getDescription() { return description; }</span>
<span class="nc" id="L229">        public void setDescription(String description) { this.description = description; }</span>
<span class="nc" id="L230">        public WorkflowType getWorkflowType() { return workflowType; }</span>
<span class="nc" id="L231">        public void setWorkflowType(WorkflowType workflowType) { this.workflowType = workflowType; }</span>
<span class="nc" id="L232">        public com.ascentbusiness.dms_svc.enums.ApprovalType getApprovalType() { return approvalType; }</span>
<span class="nc" id="L233">        public void setApprovalType(com.ascentbusiness.dms_svc.enums.ApprovalType approvalType) { this.approvalType = approvalType; }</span>
<span class="nc" id="L234">        public Boolean getAutoStart() { return autoStart; }</span>
<span class="nc" id="L235">        public void setAutoStart(Boolean autoStart) { this.autoStart = autoStart; }</span>
<span class="nc" id="L236">        public Integer getTimeoutHours() { return timeoutHours; }</span>
<span class="nc" id="L237">        public void setTimeoutHours(Integer timeoutHours) { this.timeoutHours = timeoutHours; }</span>
<span class="nc" id="L238">        public Boolean getEscalationEnabled() { return escalationEnabled; }</span>
<span class="nc" id="L239">        public void setEscalationEnabled(Boolean escalationEnabled) { this.escalationEnabled = escalationEnabled; }</span>
<span class="nc" id="L240">        public Integer getEscalationHours() { return escalationHours; }</span>
<span class="nc" id="L241">        public void setEscalationHours(Integer escalationHours) { this.escalationHours = escalationHours; }</span>
    }

<span class="nc" id="L244">    public static class WorkflowPaginationInput {</span>
        private Integer page;
        private Integer size;
        private String sortBy;
        private String sortDirection;
        
        // Getters and setters
<span class="nc" id="L251">        public Integer getPage() { return page; }</span>
<span class="nc" id="L252">        public void setPage(Integer page) { this.page = page; }</span>
<span class="nc" id="L253">        public Integer getSize() { return size; }</span>
<span class="nc" id="L254">        public void setSize(Integer size) { this.size = size; }</span>
<span class="nc" id="L255">        public String getSortBy() { return sortBy; }</span>
<span class="nc" id="L256">        public void setSortBy(String sortBy) { this.sortBy = sortBy; }</span>
<span class="nc" id="L257">        public String getSortDirection() { return sortDirection; }</span>
<span class="nc" id="L258">        public void setSortDirection(String sortDirection) { this.sortDirection = sortDirection; }</span>
    }

<span class="nc" id="L261">    public static class WorkflowDefinitionPage {</span>
        private List&lt;WorkflowDefinition&gt; content;
        private Integer totalElements;
        private Integer totalPages;
        private Integer size;
        private Integer number;
        private Boolean first;
        private Boolean last;
        
        public static WorkflowDefinitionPageBuilder builder() {
<span class="nc" id="L271">            return new WorkflowDefinitionPageBuilder();</span>
        }
        
<span class="nc" id="L274">        public static class WorkflowDefinitionPageBuilder {</span>
            private List&lt;WorkflowDefinition&gt; content;
            private Integer totalElements;
            private Integer totalPages;
            private Integer size;
            private Integer number;
            private Boolean first;
            private Boolean last;
            
            public WorkflowDefinitionPageBuilder content(List&lt;WorkflowDefinition&gt; content) {
<span class="nc" id="L284">                this.content = content;</span>
<span class="nc" id="L285">                return this;</span>
            }
            
            public WorkflowDefinitionPageBuilder totalElements(Integer totalElements) {
<span class="nc" id="L289">                this.totalElements = totalElements;</span>
<span class="nc" id="L290">                return this;</span>
            }
            
            public WorkflowDefinitionPageBuilder totalPages(Integer totalPages) {
<span class="nc" id="L294">                this.totalPages = totalPages;</span>
<span class="nc" id="L295">                return this;</span>
            }
            
            public WorkflowDefinitionPageBuilder size(Integer size) {
<span class="nc" id="L299">                this.size = size;</span>
<span class="nc" id="L300">                return this;</span>
            }
            
            public WorkflowDefinitionPageBuilder number(Integer number) {
<span class="nc" id="L304">                this.number = number;</span>
<span class="nc" id="L305">                return this;</span>
            }
            
            public WorkflowDefinitionPageBuilder first(Boolean first) {
<span class="nc" id="L309">                this.first = first;</span>
<span class="nc" id="L310">                return this;</span>
            }
            
            public WorkflowDefinitionPageBuilder last(Boolean last) {
<span class="nc" id="L314">                this.last = last;</span>
<span class="nc" id="L315">                return this;</span>
            }
            
            public WorkflowDefinitionPage build() {
<span class="nc" id="L319">                WorkflowDefinitionPage page = new WorkflowDefinitionPage();</span>
<span class="nc" id="L320">                page.content = this.content;</span>
<span class="nc" id="L321">                page.totalElements = this.totalElements;</span>
<span class="nc" id="L322">                page.totalPages = this.totalPages;</span>
<span class="nc" id="L323">                page.size = this.size;</span>
<span class="nc" id="L324">                page.number = this.number;</span>
<span class="nc" id="L325">                page.first = this.first;</span>
<span class="nc" id="L326">                page.last = this.last;</span>
<span class="nc" id="L327">                return page;</span>
            }
        }
        
        // Getters
<span class="nc" id="L332">        public List&lt;WorkflowDefinition&gt; getContent() { return content; }</span>
<span class="nc" id="L333">        public Integer getTotalElements() { return totalElements; }</span>
<span class="nc" id="L334">        public Integer getTotalPages() { return totalPages; }</span>
<span class="nc" id="L335">        public Integer getSize() { return size; }</span>
<span class="nc" id="L336">        public Integer getNumber() { return number; }</span>
<span class="nc" id="L337">        public Boolean getFirst() { return first; }</span>
<span class="nc" id="L338">        public Boolean getLast() { return last; }</span>
    }
}
</pre><div class="footer"><span class="right">Created with <a href="http://www.jacoco.org/jacoco">JaCoCo</a> 0.8.10.202304240956</span></div></body></html>