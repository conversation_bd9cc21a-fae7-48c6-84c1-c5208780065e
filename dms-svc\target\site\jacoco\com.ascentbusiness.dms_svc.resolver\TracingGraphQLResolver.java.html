<?xml version="1.0" encoding="UTF-8"?><!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Strict//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-strict.dtd"><html xmlns="http://www.w3.org/1999/xhtml" lang="en"><head><meta http-equiv="Content-Type" content="text/html;charset=UTF-8"/><link rel="stylesheet" href="../jacoco-resources/report.css" type="text/css"/><link rel="shortcut icon" href="../jacoco-resources/report.gif" type="image/gif"/><title>TracingGraphQLResolver.java</title><link rel="stylesheet" href="../jacoco-resources/prettify.css" type="text/css"/><script type="text/javascript" src="../jacoco-resources/prettify.js"></script></head><body onload="window['PR_TAB_WIDTH']=4;prettyPrint()"><div class="breadcrumb" id="breadcrumb"><span class="info"><a href="../jacoco-sessions.html" class="el_session">Sessions</a></span><a href="../index.html" class="el_report">dms-svc</a> &gt; <a href="index.source.html" class="el_package">com.ascentbusiness.dms_svc.resolver</a> &gt; <span class="el_source">TracingGraphQLResolver.java</span></div><h1>TracingGraphQLResolver.java</h1><pre class="source lang-java linenums">package com.ascentbusiness.dms_svc.resolver;

import com.ascentbusiness.dms_svc.util.TracingUtil;
import io.micrometer.tracing.Tracer;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.graphql.data.method.annotation.Argument;
import org.springframework.graphql.data.method.annotation.MutationMapping;
import org.springframework.graphql.data.method.annotation.QueryMapping;
import org.springframework.stereotype.Controller;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * GraphQL resolver for tracing operations and testing.
 * This resolver implements tracing functionality to replace TracingTestController REST endpoints.
 * 
 * Provides comprehensive tracing functionality including:
 * - Basic tracing operations and span creation
 * - Error tracing and exception handling
 * - Nested span testing and context propagation
 * - Tracing context management and metadata
 * - Performance monitoring through tracing
 */
@Controller
<span class="fc" id="L30">@Slf4j</span>
<span class="fc" id="L31">public class TracingGraphQLResolver {</span>

    @Autowired
    private TracingUtil tracingUtil;
    
    @Autowired
    private Tracer tracer;

    // ===== TRACING TEST QUERIES =====

    /**
     * Test basic tracing functionality.
     * Implements testTracing query - replaces GET /api/test/tracing
     */
    @QueryMapping
    public Map&lt;String, Object&gt; testTracing() {
<span class="nc" id="L47">        log.info(&quot;GraphQL testTracing called&quot;);</span>
        
<span class="nc" id="L49">        return tracingUtil.executeInSpan(&quot;graphql-test-tracing-operation&quot;, () -&gt; {</span>
<span class="nc" id="L50">            Map&lt;String, Object&gt; response = new HashMap&lt;&gt;();</span>
            
            // Add some business context
<span class="nc" id="L53">            tracingUtil.addBusinessContext(&quot;test&quot;, &quot;graphql-tracing&quot;, &quot;test-001&quot;);</span>
<span class="nc" id="L54">            tracingUtil.addTagToCurrentSpan(&quot;test.type&quot;, &quot;graphql-integration&quot;);</span>
<span class="nc" id="L55">            tracingUtil.addTagToCurrentSpan(&quot;api.type&quot;, &quot;graphql&quot;);</span>
            
            // Get current trace information
<span class="nc" id="L58">            String traceId = tracingUtil.getCurrentTraceId();</span>
<span class="nc" id="L59">            String spanId = tracingUtil.getCurrentSpanId();</span>
            
<span class="nc" id="L61">            response.put(&quot;message&quot;, &quot;GraphQL OpenTelemetry tracing is working!&quot;);</span>
<span class="nc" id="L62">            response.put(&quot;traceId&quot;, traceId);</span>
<span class="nc" id="L63">            response.put(&quot;spanId&quot;, spanId);</span>
<span class="nc" id="L64">            response.put(&quot;timestamp&quot;, LocalDateTime.now());</span>
<span class="nc" id="L65">            response.put(&quot;apiType&quot;, &quot;GraphQL&quot;);</span>
            
            // Simulate some work
            try {
<span class="nc" id="L69">                Thread.sleep(100);</span>
<span class="nc" id="L70">            } catch (InterruptedException e) {</span>
<span class="nc" id="L71">                Thread.currentThread().interrupt();</span>
<span class="nc" id="L72">                tracingUtil.addErrorToCurrentSpan(e);</span>
<span class="nc" id="L73">            }</span>
            
<span class="nc" id="L75">            return response;</span>
        });
    }

    /**
     * Test error tracing functionality.
     * Implements testTracingWithError query - replaces GET /api/test/tracing/error
     */
    @QueryMapping
    public Map&lt;String, Object&gt; testTracingWithError() {
<span class="nc" id="L85">        log.info(&quot;GraphQL testTracingWithError called&quot;);</span>
        
<span class="nc" id="L87">        return tracingUtil.executeInSpan(&quot;graphql-test-error-operation&quot;, () -&gt; {</span>
            try {
                // Add context before error
<span class="nc" id="L90">                tracingUtil.addTagToCurrentSpan(&quot;test.error.expected&quot;, &quot;true&quot;);</span>
<span class="nc" id="L91">                tracingUtil.addTagToCurrentSpan(&quot;api.type&quot;, &quot;graphql&quot;);</span>
                
                // Simulate an error
<span class="nc" id="L94">                throw new RuntimeException(&quot;Test error for GraphQL tracing&quot;);</span>
<span class="nc" id="L95">            } catch (Exception e) {</span>
<span class="nc" id="L96">                tracingUtil.addErrorToCurrentSpan(e);</span>
                
<span class="nc" id="L98">                Map&lt;String, Object&gt; response = new HashMap&lt;&gt;();</span>
<span class="nc" id="L99">                response.put(&quot;message&quot;, &quot;GraphQL error traced successfully&quot;);</span>
<span class="nc" id="L100">                response.put(&quot;error&quot;, e.getMessage());</span>
<span class="nc" id="L101">                response.put(&quot;errorType&quot;, e.getClass().getSimpleName());</span>
<span class="nc" id="L102">                response.put(&quot;traceId&quot;, tracingUtil.getCurrentTraceId());</span>
<span class="nc" id="L103">                response.put(&quot;spanId&quot;, tracingUtil.getCurrentSpanId());</span>
<span class="nc" id="L104">                response.put(&quot;timestamp&quot;, LocalDateTime.now());</span>
<span class="nc" id="L105">                response.put(&quot;apiType&quot;, &quot;GraphQL&quot;);</span>
                
<span class="nc" id="L107">                return response;</span>
            }
        });
    }

    /**
     * Test nested spans functionality.
     * Implements testNestedSpans query - replaces GET /api/test/tracing/nested
     */
    @QueryMapping
    public Map&lt;String, Object&gt; testNestedSpans(@Argument Map&lt;String, Object&gt; input) {
<span class="nc" id="L118">        log.info(&quot;GraphQL testNestedSpans called&quot;);</span>

<span class="nc" id="L120">        String parentOperationName = (String) input.get(&quot;parentOperationName&quot;);</span>
        @SuppressWarnings(&quot;unchecked&quot;)
<span class="nc" id="L122">        List&lt;Map&lt;String, Object&gt;&gt; childOperations = (List&lt;Map&lt;String, Object&gt;&gt;) input.get(&quot;childOperations&quot;);</span>

<span class="nc bnc" id="L124" title="All 2 branches missed.">        return tracingUtil.executeInSpan(parentOperationName != null ? parentOperationName : &quot;graphql-parent-operation&quot;, () -&gt; {</span>
<span class="nc" id="L125">            Map&lt;String, Object&gt; response = new HashMap&lt;&gt;();</span>
<span class="nc" id="L126">            List&lt;Map&lt;String, Object&gt;&gt; childSpans = new ArrayList&lt;&gt;();</span>

            // Add parent span context
<span class="nc" id="L129">            tracingUtil.addTagToCurrentSpan(&quot;operation.type&quot;, &quot;nested-test&quot;);</span>
<span class="nc" id="L130">            tracingUtil.addTagToCurrentSpan(&quot;api.type&quot;, &quot;graphql&quot;);</span>

            // Process child operations if provided
<span class="nc bnc" id="L133" title="All 4 branches missed.">            if (childOperations != null &amp;&amp; !childOperations.isEmpty()) {</span>
<span class="nc bnc" id="L134" title="All 2 branches missed.">                for (int i = 0; i &lt; childOperations.size(); i++) {</span>
<span class="nc" id="L135">                    final int index = i; // Make effectively final for lambda</span>
<span class="nc" id="L136">                    Map&lt;String, Object&gt; childOp = childOperations.get(i);</span>
<span class="nc" id="L137">                    String operationName = (String) childOp.get(&quot;operationName&quot;);</span>
<span class="nc" id="L138">                    String step = (String) childOp.get(&quot;step&quot;);</span>

<span class="nc bnc" id="L140" title="All 2 branches missed.">                    String result = tracingUtil.executeInSpan(operationName != null ? operationName : &quot;child-operation-&quot; + (index + 1), () -&gt; {</span>
<span class="nc bnc" id="L141" title="All 2 branches missed.">                        tracingUtil.addTagToCurrentSpan(&quot;operation.step&quot;, step != null ? step : String.valueOf(index + 1));</span>
<span class="nc" id="L142">                        tracingUtil.addTagToCurrentSpan(&quot;step.name&quot;, &quot;data-processing-&quot; + (index + 1));</span>
                        try {
<span class="nc" id="L144">                            Thread.sleep(50 + (index * 25));</span>
<span class="nc" id="L145">                        } catch (InterruptedException e) {</span>
<span class="nc" id="L146">                            Thread.currentThread().interrupt();</span>
<span class="nc" id="L147">                            tracingUtil.addErrorToCurrentSpan(e);</span>
<span class="nc" id="L148">                        }</span>
<span class="nc" id="L149">                        return &quot;GraphQL Step &quot; + (index + 1) + &quot; completed&quot;;</span>
                    });

                    // Add child span info to response
<span class="nc" id="L153">                    Map&lt;String, Object&gt; spanInfo = new HashMap&lt;&gt;();</span>
<span class="nc" id="L154">                    spanInfo.put(&quot;spanId&quot;, tracingUtil.getCurrentSpanId());</span>
<span class="nc bnc" id="L155" title="All 2 branches missed.">                    spanInfo.put(&quot;operationName&quot;, operationName != null ? operationName : &quot;child-operation-&quot; + (index + 1));</span>
<span class="nc" id="L156">                    spanInfo.put(&quot;startTime&quot;, LocalDateTime.now().toString());</span>
<span class="nc" id="L157">                    spanInfo.put(&quot;endTime&quot;, LocalDateTime.now().toString());</span>
<span class="nc" id="L158">                    spanInfo.put(&quot;duration&quot;, 50L + (index * 25));</span>
<span class="nc" id="L159">                    childSpans.add(spanInfo);</span>
                }
            } else {
                // Default child operations for backward compatibility
<span class="nc bnc" id="L163" title="All 2 branches missed.">                for (int i = 1; i &lt;= 3; i++) {</span>
<span class="nc" id="L164">                    final int index = i; // Make effectively final for lambda</span>
<span class="nc" id="L165">                    String result = tracingUtil.executeInSpan(&quot;graphql-child-operation-&quot; + index, () -&gt; {</span>
<span class="nc" id="L166">                        tracingUtil.addTagToCurrentSpan(&quot;operation.step&quot;, String.valueOf(index));</span>
<span class="nc" id="L167">                        tracingUtil.addTagToCurrentSpan(&quot;step.name&quot;, &quot;data-processing-&quot; + index);</span>
                        try {
<span class="nc" id="L169">                            Thread.sleep(50 + (index * 25));</span>
<span class="nc" id="L170">                        } catch (InterruptedException e) {</span>
<span class="nc" id="L171">                            Thread.currentThread().interrupt();</span>
<span class="nc" id="L172">                            tracingUtil.addErrorToCurrentSpan(e);</span>
<span class="nc" id="L173">                        }</span>
<span class="nc" id="L174">                        return &quot;GraphQL Step &quot; + index + &quot; completed&quot;;</span>
                    });

                    // Add child span info to response
<span class="nc" id="L178">                    Map&lt;String, Object&gt; spanInfo = new HashMap&lt;&gt;();</span>
<span class="nc" id="L179">                    spanInfo.put(&quot;spanId&quot;, tracingUtil.getCurrentSpanId());</span>
<span class="nc" id="L180">                    spanInfo.put(&quot;operationName&quot;, &quot;graphql-child-operation-&quot; + index);</span>
<span class="nc" id="L181">                    spanInfo.put(&quot;startTime&quot;, LocalDateTime.now().toString());</span>
<span class="nc" id="L182">                    spanInfo.put(&quot;endTime&quot;, LocalDateTime.now().toString());</span>
<span class="nc" id="L183">                    spanInfo.put(&quot;duration&quot;, 50L + (index * 25));</span>
<span class="nc" id="L184">                    childSpans.add(spanInfo);</span>
                }
            }
            
            // Build response according to NestedSpanResult schema
<span class="nc" id="L189">            response.put(&quot;message&quot;, &quot;GraphQL nested spans test completed&quot;);</span>
<span class="nc" id="L190">            response.put(&quot;traceId&quot;, tracingUtil.getCurrentTraceId());</span>
<span class="nc" id="L191">            response.put(&quot;parentSpanId&quot;, tracingUtil.getCurrentSpanId());</span>
<span class="nc" id="L192">            response.put(&quot;childSpans&quot;, childSpans);</span>
<span class="nc" id="L193">            response.put(&quot;totalSpans&quot;, childSpans.size());</span>
<span class="nc" id="L194">            response.put(&quot;executionTime&quot;, 150L + (childSpans.size() * 25));</span>
            
<span class="nc" id="L196">            return response;</span>
        });
    }

    // ===== TRACING UTILITY QUERIES =====

    /**
     * Get current tracing context information.
     * Implements getCurrentTracingContext query
     */
    @QueryMapping
    public Map&lt;String, Object&gt; getCurrentTracingContext() {
<span class="nc" id="L208">        log.info(&quot;GraphQL getCurrentTracingContext called&quot;);</span>
        
<span class="nc" id="L210">        return tracingUtil.executeInSpan(&quot;get-tracing-context&quot;, () -&gt; {</span>
<span class="nc" id="L211">            Map&lt;String, Object&gt; context = new HashMap&lt;&gt;();</span>
            
<span class="nc" id="L213">            tracingUtil.addTagToCurrentSpan(&quot;operation.type&quot;, &quot;context-retrieval&quot;);</span>
<span class="nc" id="L214">            tracingUtil.addTagToCurrentSpan(&quot;api.type&quot;, &quot;graphql&quot;);</span>
            
<span class="nc" id="L216">            context.put(&quot;traceId&quot;, tracingUtil.getCurrentTraceId());</span>
<span class="nc" id="L217">            context.put(&quot;spanId&quot;, tracingUtil.getCurrentSpanId());</span>
<span class="nc" id="L218">            context.put(&quot;timestamp&quot;, LocalDateTime.now());</span>
<span class="nc" id="L219">            context.put(&quot;apiType&quot;, &quot;GraphQL&quot;);</span>
<span class="nc bnc" id="L220" title="All 2 branches missed.">            context.put(&quot;tracingEnabled&quot;, tracer != null);</span>
            
            // Add some metadata about the current span
<span class="nc bnc" id="L223" title="All 2 branches missed.">            if (tracer.currentSpan() != null) {</span>
<span class="nc" id="L224">                context.put(&quot;spanName&quot;, tracer.currentSpan().context().spanId());</span>
<span class="nc bnc" id="L225" title="All 2 branches missed.">                context.put(&quot;hasParent&quot;, tracer.currentSpan().context().traceId() != null);</span>
            }
            
<span class="nc" id="L228">            return context;</span>
        });
    }

    // ===== TRACING MANAGEMENT MUTATIONS =====

    /**
     * Create a custom span with specified name and tags.
     * Implements createCustomSpan mutation
     */
    @MutationMapping
    public Map&lt;String, Object&gt; createCustomSpan(
            @Argument String spanName, 
            @Argument Map&lt;String, String&gt; tags,
            @Argument Integer durationMs) {
        
<span class="nc" id="L244">        log.info(&quot;GraphQL createCustomSpan called with name: {}&quot;, spanName);</span>
        
<span class="nc" id="L246">        return tracingUtil.executeInSpan(spanName, () -&gt; {</span>
<span class="nc" id="L247">            Map&lt;String, Object&gt; response = new HashMap&lt;&gt;();</span>
            
            // Add provided tags
<span class="nc bnc" id="L250" title="All 2 branches missed.">            if (tags != null) {</span>
<span class="nc bnc" id="L251" title="All 2 branches missed.">                for (Map.Entry&lt;String, String&gt; tag : tags.entrySet()) {</span>
<span class="nc" id="L252">                    tracingUtil.addTagToCurrentSpan(tag.getKey(), tag.getValue());</span>
<span class="nc" id="L253">                }</span>
            }
            
            // Add standard tags
<span class="nc" id="L257">            tracingUtil.addTagToCurrentSpan(&quot;span.custom&quot;, &quot;true&quot;);</span>
<span class="nc" id="L258">            tracingUtil.addTagToCurrentSpan(&quot;api.type&quot;, &quot;graphql&quot;);</span>
            
            // Simulate work for specified duration
<span class="nc bnc" id="L261" title="All 4 branches missed.">            if (durationMs != null &amp;&amp; durationMs &gt; 0) {</span>
                try {
<span class="nc" id="L263">                    Thread.sleep(Math.min(durationMs, 5000)); // Max 5 seconds</span>
<span class="nc" id="L264">                } catch (InterruptedException e) {</span>
<span class="nc" id="L265">                    Thread.currentThread().interrupt();</span>
<span class="nc" id="L266">                    tracingUtil.addErrorToCurrentSpan(e);</span>
<span class="nc" id="L267">                }</span>
            }
            
<span class="nc" id="L270">            response.put(&quot;message&quot;, &quot;Custom span created successfully&quot;);</span>
<span class="nc" id="L271">            response.put(&quot;spanName&quot;, spanName);</span>
<span class="nc" id="L272">            response.put(&quot;traceId&quot;, tracingUtil.getCurrentTraceId());</span>
<span class="nc" id="L273">            response.put(&quot;spanId&quot;, tracingUtil.getCurrentSpanId());</span>
<span class="nc" id="L274">            response.put(&quot;timestamp&quot;, LocalDateTime.now());</span>
<span class="nc" id="L275">            response.put(&quot;durationMs&quot;, durationMs);</span>
<span class="nc bnc" id="L276" title="All 2 branches missed.">            response.put(&quot;tagsApplied&quot;, tags != null ? tags.size() : 0);</span>
            
<span class="nc" id="L278">            return response;</span>
        });
    }

    /**
     * Test tracing performance with multiple operations.
     * Implements testTracingPerformance mutation
     */
    @MutationMapping
    public Map&lt;String, Object&gt; testTracingPerformance(@Argument Integer operationCount) {
<span class="nc" id="L288">        log.info(&quot;GraphQL testTracingPerformance called with operationCount: {}&quot;, operationCount);</span>
        
<span class="nc" id="L290">        return tracingUtil.executeInSpan(&quot;performance-test&quot;, () -&gt; {</span>
<span class="nc bnc" id="L291" title="All 2 branches missed.">            int count = operationCount != null ? Math.min(operationCount, 100) : 10; // Max 100 operations</span>
<span class="nc" id="L292">            long startTime = System.currentTimeMillis();</span>
            
<span class="nc" id="L294">            tracingUtil.addTagToCurrentSpan(&quot;test.type&quot;, &quot;performance&quot;);</span>
<span class="nc" id="L295">            tracingUtil.addTagToCurrentSpan(&quot;operation.count&quot;, String.valueOf(count));</span>
<span class="nc" id="L296">            tracingUtil.addTagToCurrentSpan(&quot;api.type&quot;, &quot;graphql&quot;);</span>
            
            // Execute multiple nested operations
<span class="nc bnc" id="L299" title="All 2 branches missed.">            for (int i = 0; i &lt; count; i++) {</span>
<span class="nc" id="L300">                final int operationIndex = i;</span>
<span class="nc" id="L301">                tracingUtil.executeInSpan(&quot;performance-operation-&quot; + i, () -&gt; {</span>
<span class="nc" id="L302">                    tracingUtil.addTagToCurrentSpan(&quot;operation.index&quot;, String.valueOf(operationIndex));</span>
                    
                    // Simulate some work
                    try {
<span class="nc" id="L306">                        Thread.sleep(10); // 10ms per operation</span>
<span class="nc" id="L307">                    } catch (InterruptedException e) {</span>
<span class="nc" id="L308">                        Thread.currentThread().interrupt();</span>
<span class="nc" id="L309">                        tracingUtil.addErrorToCurrentSpan(e);</span>
<span class="nc" id="L310">                    }</span>
                    
<span class="nc" id="L312">                    return &quot;Operation &quot; + operationIndex + &quot; completed&quot;;</span>
                });
            }
            
<span class="nc" id="L316">            long endTime = System.currentTimeMillis();</span>
<span class="nc" id="L317">            long totalDuration = endTime - startTime;</span>
            
<span class="nc" id="L319">            Map&lt;String, Object&gt; response = new HashMap&lt;&gt;();</span>
<span class="nc" id="L320">            response.put(&quot;message&quot;, &quot;Performance test completed&quot;);</span>
<span class="nc" id="L321">            response.put(&quot;operationsExecuted&quot;, count);</span>
<span class="nc" id="L322">            response.put(&quot;totalDurationMs&quot;, totalDuration);</span>
<span class="nc" id="L323">            response.put(&quot;averageDurationMs&quot;, (double) totalDuration / count);</span>
<span class="nc" id="L324">            response.put(&quot;traceId&quot;, tracingUtil.getCurrentTraceId());</span>
<span class="nc" id="L325">            response.put(&quot;spanId&quot;, tracingUtil.getCurrentSpanId());</span>
<span class="nc" id="L326">            response.put(&quot;timestamp&quot;, LocalDateTime.now());</span>
<span class="nc" id="L327">            response.put(&quot;apiType&quot;, &quot;GraphQL&quot;);</span>
            
<span class="nc" id="L329">            return response;</span>
        });
    }
}
</pre><div class="footer"><span class="right">Created with <a href="http://www.jacoco.org/jacoco">JaCoCo</a> 0.8.10.202304240956</span></div></body></html>