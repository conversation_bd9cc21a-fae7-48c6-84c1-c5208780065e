<?xml version="1.0" encoding="UTF-8"?><!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Strict//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-strict.dtd"><html xmlns="http://www.w3.org/1999/xhtml" lang="en"><head><meta http-equiv="Content-Type" content="text/html;charset=UTF-8"/><link rel="stylesheet" href="../jacoco-resources/report.css" type="text/css"/><link rel="shortcut icon" href="../jacoco-resources/report.gif" type="image/gif"/><title>WordToPdfConversionService</title><script type="text/javascript" src="../jacoco-resources/sort.js"></script></head><body onload="initialSort(['breadcrumb'])"><div class="breadcrumb" id="breadcrumb"><span class="info"><a href="../jacoco-sessions.html" class="el_session">Sessions</a></span><a href="../index.html" class="el_report">dms-svc</a> &gt; <a href="index.html" class="el_package">com.ascentbusiness.dms_svc.service</a> &gt; <span class="el_class">WordToPdfConversionService</span></div><h1>WordToPdfConversionService</h1><table class="coverage" cellspacing="0" id="coveragetable"><thead><tr><td class="sortable" id="a" onclick="toggleSort(this)">Element</td><td class="down sortable bar" id="b" onclick="toggleSort(this)">Missed Instructions</td><td class="sortable ctr2" id="c" onclick="toggleSort(this)">Cov.</td><td class="sortable bar" id="d" onclick="toggleSort(this)">Missed Branches</td><td class="sortable ctr2" id="e" onclick="toggleSort(this)">Cov.</td><td class="sortable ctr1" id="f" onclick="toggleSort(this)">Missed</td><td class="sortable ctr2" id="g" onclick="toggleSort(this)">Cxty</td><td class="sortable ctr1" id="h" onclick="toggleSort(this)">Missed</td><td class="sortable ctr2" id="i" onclick="toggleSort(this)">Lines</td><td class="sortable ctr1" id="j" onclick="toggleSort(this)">Missed</td><td class="sortable ctr2" id="k" onclick="toggleSort(this)">Methods</td></tr></thead><tfoot><tr><td>Total</td><td class="bar">945 of 952</td><td class="ctr2">0%</td><td class="bar">78 of 78</td><td class="ctr2">0%</td><td class="ctr1">50</td><td class="ctr2">52</td><td class="ctr1">187</td><td class="ctr2">189</td><td class="ctr1">11</td><td class="ctr2">13</td></tr></tfoot><tbody><tr><td id="a2"><a href="WordToPdfConversionService.java.html#L240" class="el_method">convertWordContentToPdfLegacy(byte[], String, String)</a></td><td class="bar" id="b0"><img src="../jacoco-resources/redbar.gif" width="120" height="10" title="195" alt="195"/></td><td class="ctr2" id="c2">0%</td><td class="bar" id="d0"><img src="../jacoco-resources/redbar.gif" width="120" height="10" title="22" alt="22"/></td><td class="ctr2" id="e0">0%</td><td class="ctr1" id="f0">12</td><td class="ctr2" id="g0">12</td><td class="ctr1" id="h0">49</td><td class="ctr2" id="i0">49</td><td class="ctr1" id="j0">1</td><td class="ctr2" id="k0">1</td></tr><tr><td id="a6"><a href="WordToPdfConversionService.java.html#L136" class="el_method">performConversion(byte[], String, String, String, VirusScannerType)</a></td><td class="bar" id="b1"><img src="../jacoco-resources/redbar.gif" width="103" height="10" title="168" alt="168"/></td><td class="ctr2" id="c3">0%</td><td class="bar" id="d6"><img src="../jacoco-resources/redbar.gif" width="10" height="10" title="2" alt="2"/></td><td class="ctr2" id="e1">0%</td><td class="ctr1" id="f6">2</td><td class="ctr2" id="g6">2</td><td class="ctr1" id="h1">29</td><td class="ctr2" id="i1">29</td><td class="ctr1" id="j1">1</td><td class="ctr2" id="k1">1</td></tr><tr><td id="a0"><a href="WordToPdfConversionService.java.html#L329" class="el_method">addTextToPdfDocument(Document, String)</a></td><td class="bar" id="b2"><img src="../jacoco-resources/redbar.gif" width="60" height="10" title="99" alt="99"/></td><td class="ctr2" id="c4">0%</td><td class="bar" id="d1"><img src="../jacoco-resources/redbar.gif" width="87" height="10" title="16" alt="16"/></td><td class="ctr2" id="e2">0%</td><td class="ctr1" id="f1">9</td><td class="ctr2" id="g1">9</td><td class="ctr1" id="h2">20</td><td class="ctr2" id="i2">20</td><td class="ctr1" id="j2">1</td><td class="ctr2" id="k2">1</td></tr><tr><td id="a4"><a href="WordToPdfConversionService.java.html#L107" class="el_method">convertWordToPdfFromPath(String, String, VirusScannerType)</a></td><td class="bar" id="b3"><img src="../jacoco-resources/redbar.gif" width="59" height="10" title="97" alt="97"/></td><td class="ctr2" id="c5">0%</td><td class="bar" id="d8"/><td class="ctr2" id="e8">n/a</td><td class="ctr1" id="f8">1</td><td class="ctr2" id="g8">1</td><td class="ctr1" id="h4">13</td><td class="ctr2" id="i4">13</td><td class="ctr1" id="j3">1</td><td class="ctr2" id="k3">1</td></tr><tr><td id="a3"><a href="WordToPdfConversionService.java.html#L72" class="el_method">convertWordToPdfFromMultipart(MultipartFile, String, VirusScannerType)</a></td><td class="bar" id="b4"><img src="../jacoco-resources/redbar.gif" width="54" height="10" title="89" alt="89"/></td><td class="ctr2" id="c6">0%</td><td class="bar" id="d9"/><td class="ctr2" id="e9">n/a</td><td class="ctr1" id="f9">1</td><td class="ctr2" id="g9">1</td><td class="ctr1" id="h5">13</td><td class="ctr2" id="i5">13</td><td class="ctr1" id="j4">1</td><td class="ctr2" id="k4">1</td></tr><tr><td id="a10"><a href="WordToPdfConversionService.java.html#L420" class="el_method">validateFilePath(String)</a></td><td class="bar" id="b5"><img src="../jacoco-resources/redbar.gif" width="51" height="10" title="83" alt="83"/></td><td class="ctr2" id="c7">0%</td><td class="bar" id="d2"><img src="../jacoco-resources/redbar.gif" width="76" height="10" title="14" alt="14"/></td><td class="ctr2" id="e3">0%</td><td class="ctr1" id="f2">8</td><td class="ctr2" id="g2">8</td><td class="ctr1" id="h3">18</td><td class="ctr2" id="i3">18</td><td class="ctr1" id="j5">1</td><td class="ctr2" id="k5">1</td></tr><tr><td id="a7"><a href="WordToPdfConversionService.java.html#L192" class="el_method">performVirusScanning(byte[], String, String, String, VirusScannerType)</a></td><td class="bar" id="b6"><img src="../jacoco-resources/redbar.gif" width="39" height="10" title="64" alt="64"/></td><td class="ctr2" id="c8">0%</td><td class="bar" id="d5"><img src="../jacoco-resources/redbar.gif" width="21" height="10" title="4" alt="4"/></td><td class="ctr2" id="e4">0%</td><td class="ctr1" id="f5">3</td><td class="ctr2" id="g5">3</td><td class="ctr1" id="h6">12</td><td class="ctr2" id="i6">12</td><td class="ctr1" id="j6">1</td><td class="ctr2" id="k6">1</td></tr><tr><td id="a11"><a href="WordToPdfConversionService.java.html#L396" class="el_method">validateMultipartFile(MultipartFile)</a></td><td class="bar" id="b7"><img src="../jacoco-resources/redbar.gif" width="35" height="10" title="57" alt="57"/></td><td class="ctr2" id="c9">0%</td><td class="bar" id="d3"><img src="../jacoco-resources/redbar.gif" width="65" height="10" title="12" alt="12"/></td><td class="ctr2" id="e5">0%</td><td class="ctr1" id="f3">7</td><td class="ctr2" id="g3">7</td><td class="ctr1" id="h7">12</td><td class="ctr2" id="i7">12</td><td class="ctr1" id="j7">1</td><td class="ctr2" id="k7">1</td></tr><tr><td id="a1"><a href="WordToPdfConversionService.java.html#L217" class="el_method">convertWordContentToPdf(byte[], String, String)</a></td><td class="bar" id="b8"><img src="../jacoco-resources/redbar.gif" width="24" height="10" title="39" alt="39"/></td><td class="ctr2" id="c10">0%</td><td class="bar" id="d7"><img src="../jacoco-resources/redbar.gif" width="10" height="10" title="2" alt="2"/></td><td class="ctr2" id="e6">0%</td><td class="ctr1" id="f7">2</td><td class="ctr2" id="g7">2</td><td class="ctr1" id="h8">9</td><td class="ctr2" id="i8">9</td><td class="ctr1" id="j8">1</td><td class="ctr2" id="k8">1</td></tr><tr><td id="a5"><a href="WordToPdfConversionService.java.html#L379" class="el_method">generatePdfFileName(String)</a></td><td class="bar" id="b9"><img src="../jacoco-resources/redbar.gif" width="21" height="10" title="35" alt="35"/></td><td class="ctr2" id="c11">0%</td><td class="bar" id="d4"><img src="../jacoco-resources/redbar.gif" width="32" height="10" title="6" alt="6"/></td><td class="ctr2" id="e7">0%</td><td class="ctr1" id="f4">4</td><td class="ctr2" id="g4">4</td><td class="ctr1" id="h9">8</td><td class="ctr2" id="i9">8</td><td class="ctr1" id="j9">1</td><td class="ctr2" id="k9">1</td></tr><tr><td id="a8"><a href="WordToPdfConversionService.java.html#L368" class="el_method">saveToDownloadDirectory(byte[], String, String)</a></td><td class="bar" id="b10"><img src="../jacoco-resources/redbar.gif" width="11" height="10" title="19" alt="19"/></td><td class="ctr2" id="c12">0%</td><td class="bar" id="d10"/><td class="ctr2" id="e10">n/a</td><td class="ctr1" id="f10">1</td><td class="ctr2" id="g10">1</td><td class="ctr1" id="h10">4</td><td class="ctr2" id="i10">4</td><td class="ctr1" id="j10">1</td><td class="ctr2" id="k10">1</td></tr><tr><td id="a9"><a href="WordToPdfConversionService.java.html#L44" class="el_method">static {...}</a></td><td class="bar" id="b11"><img src="../jacoco-resources/greenbar.gif" width="2" height="10" title="4" alt="4"/></td><td class="ctr2" id="c0">100%</td><td class="bar" id="d11"/><td class="ctr2" id="e11">n/a</td><td class="ctr1" id="f11">0</td><td class="ctr2" id="g11">1</td><td class="ctr1" id="h11">0</td><td class="ctr2" id="i11">1</td><td class="ctr1" id="j11">0</td><td class="ctr2" id="k11">1</td></tr><tr><td id="a12"><a href="WordToPdfConversionService.java.html#L42" class="el_method">WordToPdfConversionService()</a></td><td class="bar" id="b12"><img src="../jacoco-resources/greenbar.gif" width="1" height="10" title="3" alt="3"/></td><td class="ctr2" id="c1">100%</td><td class="bar" id="d12"/><td class="ctr2" id="e12">n/a</td><td class="ctr1" id="f12">0</td><td class="ctr2" id="g12">1</td><td class="ctr1" id="h12">0</td><td class="ctr2" id="i12">1</td><td class="ctr1" id="j12">0</td><td class="ctr2" id="k12">1</td></tr></tbody></table><div class="footer"><span class="right">Created with <a href="http://www.jacoco.org/jacoco">JaCoCo</a> 0.8.10.202304240956</span></div></body></html>