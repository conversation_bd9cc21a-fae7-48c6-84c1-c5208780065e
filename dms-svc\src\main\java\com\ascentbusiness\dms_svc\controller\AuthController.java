package com.ascentbusiness.dms_svc.controller;

import com.ascentbusiness.dms_svc.security.JwtTokenProvider;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.security.authentication.AuthenticationManager;
import org.springframework.security.authentication.UsernamePasswordAuthenticationToken;
import org.springframework.security.core.Authentication;
import org.springframework.security.core.AuthenticationException;
import org.springframework.web.bind.annotation.*;

import java.util.HashMap;
import java.util.Map;

/**
 * Authentication controller for handling login requests and JWT token generation.
 * Provides endpoints for user authentication and token management.
 *
 * <AUTHOR> Development Team
 * @version 1.0
 * @since 1.0
 */
@RestController
@RequestMapping("/auth")
@CrossOrigin(origins = "*", maxAge = 3600)
public class AuthController {

    private static final Logger logger = LoggerFactory.getLogger(AuthController.class);

    @Autowired
    private AuthenticationManager authenticationManager;

    @Autowired
    private JwtTokenProvider jwtTokenProvider;

    /**
     * Login request DTO
     */
    public static class LoginRequest {
        private String username;
        private String password;

        public LoginRequest() {}

        public LoginRequest(String username, String password) {
            this.username = username;
            this.password = password;
        }

        public String getUsername() {
            return username;
        }

        public void setUsername(String username) {
            this.username = username;
        }

        public String getPassword() {
            return password;
        }

        public void setPassword(String password) {
            this.password = password;
        }
    }

    /**
     * JWT response DTO
     */
    public static class JwtResponse {
        private String token;
        private String type = "Bearer";
        private String username;
        private long expiresIn;

        public JwtResponse(String accessToken, String username, long expiresIn) {
            this.token = accessToken;
            this.username = username;
            this.expiresIn = expiresIn;
        }

        public String getToken() {
            return token;
        }

        public void setToken(String token) {
            this.token = token;
        }

        public String getType() {
            return type;
        }

        public void setType(String type) {
            this.type = type;
        }

        public String getUsername() {
            return username;
        }

        public void setUsername(String username) {
            this.username = username;
        }

        public long getExpiresIn() {
            return expiresIn;
        }

        public void setExpiresIn(long expiresIn) {
            this.expiresIn = expiresIn;
        }
    }

    /**
     * Authenticates user and generates JWT token.
     *
     * @param loginRequest Login credentials
     * @return JWT token response
     */
    @PostMapping("/login")
    public ResponseEntity<?> authenticateUser(@RequestBody LoginRequest loginRequest) {
        logger.info("Login attempt for username: {}", loginRequest.getUsername());

        try {
            // For demo purposes, accept any username/password combination
            // In production, this should authenticate against a real user store
            if (loginRequest.getUsername() == null || loginRequest.getUsername().trim().isEmpty()) {
                logger.warn("Login failed: Username is required");
                return ResponseEntity.badRequest()
                    .body(createErrorResponse("Username is required"));
            }

            if (loginRequest.getPassword() == null || loginRequest.getPassword().trim().isEmpty()) {
                logger.warn("Login failed: Password is required");
                return ResponseEntity.badRequest()
                    .body(createErrorResponse("Password is required"));
            }

            // Create authentication token
            UsernamePasswordAuthenticationToken authToken = 
                new UsernamePasswordAuthenticationToken(
                    loginRequest.getUsername(), 
                    loginRequest.getPassword()
                );

            // For demo purposes, we'll create a simple authentication without actual validation
            // In production, you would authenticate against a user service or database
            logger.info("Generating JWT token for user: {}", loginRequest.getUsername());
            
            // Generate JWT token
            String jwt = jwtTokenProvider.generateTokenFromUsername(loginRequest.getUsername());
            long expirationTime = System.currentTimeMillis() + (24 * 60 * 60 * 1000); // 24 hours in milliseconds

            logger.info("JWT token generated successfully for user: {}", loginRequest.getUsername());

            return ResponseEntity.ok(new JwtResponse(jwt, loginRequest.getUsername(), expirationTime));

        } catch (AuthenticationException e) {
            logger.error("Authentication failed for user: {}", loginRequest.getUsername(), e);
            return ResponseEntity.badRequest()
                .body(createErrorResponse("Invalid username or password"));
        } catch (Exception e) {
            logger.error("Unexpected error during authentication for user: {}", loginRequest.getUsername(), e);
            return ResponseEntity.internalServerError()
                .body(createErrorResponse("Authentication service temporarily unavailable"));
        }
    }

    /**
     * Validates JWT token.
     *
     * @param token JWT token to validate
     * @return Token validation response
     */
    @PostMapping("/validate")
    public ResponseEntity<?> validateToken(@RequestParam String token) {
        logger.info("Token validation request received");

        try {
            if (jwtTokenProvider.validateToken(token)) {
                String username = jwtTokenProvider.getUsernameFromToken(token);
                logger.info("Token validation successful for user: {}", username);
                
                Map<String, Object> response = new HashMap<>();
                response.put("valid", true);
                response.put("username", username);
                response.put("message", "Token is valid");
                
                return ResponseEntity.ok(response);
            } else {
                logger.warn("Token validation failed: Invalid token");
                return ResponseEntity.badRequest()
                    .body(createErrorResponse("Invalid token"));
            }
        } catch (Exception e) {
            logger.error("Error during token validation", e);
            return ResponseEntity.internalServerError()
                .body(createErrorResponse("Token validation service temporarily unavailable"));
        }
    }

    /**
     * Health check endpoint for authentication service.
     *
     * @return Service health status
     */
    @GetMapping("/health")
    public ResponseEntity<?> health() {
        Map<String, Object> response = new HashMap<>();
        response.put("status", "UP");
        response.put("service", "Authentication Service");
        response.put("timestamp", System.currentTimeMillis());
        
        return ResponseEntity.ok(response);
    }

    /**
     * Creates a standardized error response.
     *
     * @param message Error message
     * @return Error response map
     */
    private Map<String, Object> createErrorResponse(String message) {
        Map<String, Object> response = new HashMap<>();
        response.put("error", true);
        response.put("message", message);
        response.put("timestamp", System.currentTimeMillis());
        return response;
    }
}