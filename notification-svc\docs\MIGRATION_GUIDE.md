# Notification Service Configuration Migration Guide

This guide provides step-by-step instructions for migrating from the old configuration structure to the new simplified configuration.

## Overview of Changes

### Before (Old Structure)
- **4 files with 590 lines** and extensive duplication
- **Hardcoded sensitive values** (AWS credentials, JWT secrets)
- **Inconsistent environment variable usage**
- **Mixed environment-specific and common configurations**

### After (New Structure)
- **4 files with ~260 lines** (56% reduction) and minimal duplication
- **All sensitive values externalized** to environment variables
- **Consistent environment variable patterns**
- **Clear separation of concerns**

## Breaking Changes

### 1. Profile Name Changes
| Old Profile | New Profile | Description |
|-------------|-------------|-------------|
| `local-dev` | `dev` | Development environment |
| `docker` | `uat` | UAT/Testing environment |
| `aws-ec2` | `prod` | Production environment |

### 2. Required Environment Variables
The following environment variables are now **REQUIRED** and must be set before deployment:

#### Critical Security Variables
```bash
# JWT Secret (REQUIRED - minimum 32 characters)
JWT_SECRET=your_secure_jwt_secret_at_least_32_characters_long

# Database Password (REQUIRED for production)
DB_PASSWORD=your_secure_database_password

# Email Credentials (REQUIRED if email is enabled)
MAIL_HOST=your.smtp.server.com
MAIL_USERNAME=your_email_username
MAIL_PASSWORD=your_email_password

# Redis Password (REQUIRED for production)
REDIS_PASSWORD=your_secure_redis_password

# RabbitMQ Credentials (REQUIRED for production)
RABBITMQ_USERNAME=your_rabbitmq_username
RABBITMQ_PASSWORD=your_secure_rabbitmq_password

# CORS Origins (REQUIRED for production)
CORS_ALLOWED_ORIGINS=https://your-frontend-domain.com,https://your-admin-domain.com
```

## Migration Steps

### Step 1: Backup Current Configuration
```bash
# Backup existing files (already done if following this guide)
cp application.properties application.properties.backup
cp application-local-dev.properties application-local-dev.properties.backup
cp application-docker.properties application-docker.properties.backup
cp application-aws-ec2.properties application-aws-ec2.properties.backup
```

### Step 2: Update Deployment Scripts

#### Docker Compose Changes
Update your `docker-compose.yml` files to use the new profile names:

**Before:**
```yaml
services:
  notification-service:
    environment:
      - SPRING_PROFILES_ACTIVE=docker
```

**After:**
```yaml
services:
  notification-service:
    environment:
      - SPRING_PROFILES_ACTIVE=uat
      - JWT_SECRET=${JWT_SECRET}
      - DB_PASSWORD=${DB_PASSWORD}
      - REDIS_PASSWORD=${REDIS_PASSWORD}
      - RABBITMQ_USERNAME=${RABBITMQ_USERNAME}
      - RABBITMQ_PASSWORD=${RABBITMQ_PASSWORD}
      - CORS_ALLOWED_ORIGINS=${CORS_ALLOWED_ORIGINS}
```

#### Kubernetes Deployment Changes
Update your Kubernetes deployment manifests:

**Before:**
```yaml
env:
  - name: SPRING_PROFILES_ACTIVE
    value: "aws-ec2"
```

**After:**
```yaml
env:
  - name: SPRING_PROFILES_ACTIVE
    value: "prod"
  - name: JWT_SECRET
    valueFrom:
      secretKeyRef:
        name: notification-secrets
        key: jwt-secret
  - name: DB_PASSWORD
    valueFrom:
      secretKeyRef:
        name: notification-secrets
        key: db-password
  - name: REDIS_PASSWORD
    valueFrom:
      secretKeyRef:
        name: notification-secrets
        key: redis-password
  - name: RABBITMQ_USERNAME
    valueFrom:
      secretKeyRef:
        name: notification-secrets
        key: rabbitmq-username
  - name: RABBITMQ_PASSWORD
    valueFrom:
      secretKeyRef:
        name: notification-secrets
        key: rabbitmq-password
  - name: CORS_ALLOWED_ORIGINS
    value: "https://app.yourdomain.com,https://admin.yourdomain.com"
```

### Step 3: Create Environment-Specific Configuration

#### Development Environment (.env.dev)
```bash
# Database
DB_USERNAME=dev_user
DB_PASSWORD=dev_password
DB_URL=***************************************************************************************

# Security
JWT_SECRET=development_jwt_secret_at_least_32_characters_long_for_security
CORS_ALLOWED_ORIGINS=http://localhost:4200,http://localhost:3000,http://localhost:8080

# Email (Mock for development)
EMAIL_MOCK=true
NOTIFICATION_FROM_EMAIL=dev@localhost

# Redis
REDIS_HOST=localhost
REDIS_PASSWORD=dev_redis_password

# RabbitMQ
RABBITMQ_HOST=localhost
RABBITMQ_USERNAME=guest
RABBITMQ_PASSWORD=guest

# GraphQL
GRAPHIQL_ENABLED=true

# Logging
LOG_LEVEL_APP=DEBUG
```

#### UAT Environment (.env.uat)
```bash
# Database
DB_USERNAME=notification_uat_user
DB_PASSWORD=uat_secure_password
DB_URL=********************************************************

# Security
JWT_SECRET=uat_jwt_secret_at_least_32_characters_long_and_secure
CORS_ALLOWED_ORIGINS=https://uat.yourdomain.com,https://uat-admin.yourdomain.com

# Email
MAIL_HOST=smtp.yourdomain.com
MAIL_USERNAME=<EMAIL>
MAIL_PASSWORD=uat_email_password
NOTIFICATION_FROM_EMAIL=<EMAIL>
EMAIL_MOCK=false

# Redis
REDIS_HOST=uat-redis
REDIS_PASSWORD=uat_redis_password

# RabbitMQ
RABBITMQ_HOST=uat-rabbitmq
RABBITMQ_USERNAME=notification_uat_user
RABBITMQ_PASSWORD=uat_rabbitmq_password

# GraphQL
GRAPHIQL_ENABLED=true

# Logging
LOG_LEVEL_APP=INFO
```

#### Production Environment (.env.prod)
```bash
# Database
DB_USERNAME=notification_prod_user
DB_PASSWORD=super_secure_production_password
DB_URL=**************************************************************************

# Security
JWT_SECRET=production_jwt_secret_minimum_32_characters_extremely_secure_random_string
CORS_ALLOWED_ORIGINS=https://app.yourdomain.com,https://admin.yourdomain.com

# Email
MAIL_HOST=smtp.yourdomain.com
MAIL_USERNAME=<EMAIL>
MAIL_PASSWORD=production_email_password
NOTIFICATION_FROM_EMAIL=<EMAIL>
EMAIL_MOCK=false

# Redis
REDIS_HOST=prod-redis
REDIS_PASSWORD=production_redis_password

# RabbitMQ
RABBITMQ_HOST=prod-rabbitmq
RABBITMQ_USERNAME=notification_prod_user
RABBITMQ_PASSWORD=production_rabbitmq_password

# GraphQL
GRAPHIQL_ENABLED=false

# Logging
LOG_LEVEL_APP=INFO

# Performance
SERVER_COMPRESSION_ENABLED=true
SERVER_TOMCAT_MAX_CONNECTIONS=8192
SERVER_TOMCAT_THREADS_MAX=200
```

### Step 4: Update CI/CD Pipelines

#### GitHub Actions Example
```yaml
name: Deploy Notification Service

on:
  push:
    branches: [main]

jobs:
  deploy:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v3
      
      - name: Deploy to Production
        env:
          JWT_SECRET: ${{ secrets.JWT_SECRET }}
          DB_PASSWORD: ${{ secrets.DB_PASSWORD }}
          REDIS_PASSWORD: ${{ secrets.REDIS_PASSWORD }}
          RABBITMQ_USERNAME: ${{ secrets.RABBITMQ_USERNAME }}
          RABBITMQ_PASSWORD: ${{ secrets.RABBITMQ_PASSWORD }}
          MAIL_USERNAME: ${{ secrets.MAIL_USERNAME }}
          MAIL_PASSWORD: ${{ secrets.MAIL_PASSWORD }}
          CORS_ALLOWED_ORIGINS: ${{ secrets.CORS_ALLOWED_ORIGINS }}
        run: |
          # Your deployment script here
          docker-compose -f docker-compose.prod.yml up -d
```

#### Jenkins Pipeline Example
```groovy
pipeline {
    agent any
    
    environment {
        JWT_SECRET = credentials('notification-jwt-secret')
        DB_PASSWORD = credentials('notification-db-password')
        REDIS_PASSWORD = credentials('notification-redis-password')
        RABBITMQ_USERNAME = credentials('notification-rabbitmq-username')
        RABBITMQ_PASSWORD = credentials('notification-rabbitmq-password')
        MAIL_USERNAME = credentials('notification-mail-username')
        MAIL_PASSWORD = credentials('notification-mail-password')
        CORS_ALLOWED_ORIGINS = credentials('notification-cors-origins')
    }
    
    stages {
        stage('Deploy') {
            steps {
                script {
                    sh '''
                        export SPRING_PROFILES_ACTIVE=prod
                        docker-compose -f docker-compose.prod.yml up -d
                    '''
                }
            }
        }
    }
}
```

## Validation Steps

### Step 1: Configuration Validation
Before deploying, validate your configuration:

```bash
# Check that all required environment variables are set
echo "JWT_SECRET: ${JWT_SECRET:0:10}..." # Should show first 10 chars
echo "DB_PASSWORD: ${DB_PASSWORD:+SET}" # Should show "SET"
echo "REDIS_PASSWORD: ${REDIS_PASSWORD:+SET}" # Should show "SET"
echo "CORS_ALLOWED_ORIGINS: $CORS_ALLOWED_ORIGINS" # Should show your domains
```

### Step 2: Application Startup Test
```bash
# Test with new configuration
java -jar notification-service.jar --spring.profiles.active=dev

# Check logs for successful startup
tail -f logs/application.log | grep -E "(Started|ERROR|WARN)"
```

### Step 3: Health Check Validation
```bash
# Check application health
curl http://localhost:9091/actuator/health

# Expected response:
# {"status":"UP","components":{"db":{"status":"UP"},"redis":{"status":"UP"}}}
```

### Step 4: Functionality Testing
```bash
# Test GraphQL endpoint (if enabled)
curl -X POST http://localhost:9091/graphql \
  -H "Content-Type: application/json" \
  -d '{"query":"query { __schema { types { name } } }"}'

# Test notification sending (replace with actual mutation)
curl -X POST http://localhost:9091/graphql \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer YOUR_JWT_TOKEN" \
  -d '{"query":"mutation { sendNotification(input: {...}) { success } }"}'
```

## Rollback Plan

If issues occur during migration, you can quickly rollback:

### Quick Rollback
```bash
# Restore original configuration files
cp application.properties.backup application.properties
cp application-local-dev.properties.backup application-local-dev.properties
cp application-docker.properties.backup application-docker.properties
cp application-aws-ec2.properties.backup application-aws-ec2.properties

# Restart application with old profile names
export SPRING_PROFILES_ACTIVE=aws-ec2  # or docker, local-dev
java -jar notification-service.jar
```

### Docker Rollback
```bash
# Rollback to previous image version
docker-compose down
docker-compose -f docker-compose.old.yml up -d
```

## Common Issues and Solutions

### Issue 1: JWT Secret Too Short
**Error:** `JWT secret must be at least 256 bits`
**Solution:** Ensure JWT_SECRET is at least 32 characters long

### Issue 2: CORS Errors in Production
**Error:** `CORS policy: No 'Access-Control-Allow-Origin' header`
**Solution:** Set CORS_ALLOWED_ORIGINS with exact domain names (no wildcards)

### Issue 3: Database Connection Failed
**Error:** `Access denied for user`
**Solution:** Verify DB_USERNAME and DB_PASSWORD are correctly set

### Issue 4: Redis Connection Failed
**Error:** `Unable to connect to Redis`
**Solution:** Check REDIS_HOST, REDIS_PORT, and REDIS_PASSWORD

### Issue 5: Email Sending Failed
**Error:** `Authentication failed`
**Solution:** Verify MAIL_USERNAME and MAIL_PASSWORD, ensure app-specific passwords for Gmail

## Security Checklist

- [ ] All sensitive values moved to environment variables
- [ ] JWT secrets are at least 32 characters long and unique per environment
- [ ] Database passwords are strong and unique
- [ ] CORS origins are explicitly defined (no wildcards in production)
- [ ] Email credentials use app-specific passwords where possible
- [ ] Redis and RabbitMQ have strong passwords
- [ ] Actuator endpoints are limited in production
- [ ] GraphiQL is disabled in production
- [ ] SSL/TLS is enabled for all external connections

## Performance Improvements

The new configuration includes several performance optimizations:

1. **Connection Pooling**: Optimized for each environment
2. **Batch Processing**: Environment-specific batch sizes
3. **Caching**: Improved Redis configuration
4. **Compression**: Enabled in production
5. **Virtual Threads**: Java 21 performance features

## Support and Troubleshooting

For issues during migration:

1. Check the [Environment Variables Reference](ENVIRONMENT_VARIABLES.md)
2. Validate configuration with the health endpoint
3. Review application logs for specific error messages
4. Use the rollback plan if critical issues occur

## Post-Migration Tasks

After successful migration:

1. [ ] Update monitoring dashboards with new metrics
2. [ ] Update documentation with new configuration patterns
3. [ ] Train team members on new environment variable patterns
4. [ ] Remove backup files after validation period
5. [ ] Update disaster recovery procedures

This migration guide ensures a smooth transition to the new simplified configuration structure while maintaining all functionality and improving security.