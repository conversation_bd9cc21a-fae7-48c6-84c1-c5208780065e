# Database Indexing Strategy for Notification Service

## Overview

This document outlines the comprehensive database indexing strategy implemented for the notification service to optimize query performance and ensure scalability.

## Current Index Implementation

### Migration V7: Performance Indexes

The indexing strategy is implemented in [`V7__add_performance_indexes.sql`](../src/main/resources/db/migration/V7__add_performance_indexes.sql) and includes:

## Index Categories

### 1. Single Column Indexes

#### Notifications Table
- `idx_notifications_sender_id` - Optimizes sender-based queries
- `idx_notifications_type` - Filters EMAIL vs IN_APP notifications
- `idx_notifications_created_date` - Supports date ordering (DESC)
- `idx_notifications_message_type` - Filters by message type
- `idx_notifications_importance` - Filters by importance level
- `idx_notifications_template_name` - Template lookups

#### Notification Recipients Table
- `idx_notification_recipients_recipient_id` - Primary user queries
- `idx_notification_recipients_is_read` - Read status filtering
- `idx_notification_recipients_delivered` - Delivery status filtering
- `idx_notification_recipients_type` - Recipient type (TO/CC/BCC)
- `idx_notification_recipients_created_date` - Date ordering
- `idx_notification_recipients_notification_id` - Foreign key lookups

#### Notification Templates Table
- `idx_notification_templates_alias` - Template alias lookups
- `idx_notification_templates_created_date` - Date ordering

### 2. Composite Indexes

#### Query Pattern Optimizations
- `idx_notifications_sender_type_created` - Sender + type + date queries
- `idx_notifications_type_message_importance` - Multi-field filtering
- `idx_recipients_recipient_created` - User notifications with date ordering
- `idx_recipients_recipient_unread` - Unread notifications per user
- `idx_recipients_complex_query` - Complex filtering with date ordering
- `idx_recipients_notification_recipient` - Notification-recipient lookups
- `idx_recipients_recipient_delivered` - Delivery status per user
- `idx_recipients_read_delivered` - Read/delivery status combinations

### 3. Covering Indexes

#### High-Performance Queries
- `idx_recipients_covering_list` - Includes all columns for recipient list queries
- `idx_notifications_covering_details` - Covers notification detail queries

## Query Optimization Targets

### Repository Method Mappings

| Repository Method | Optimized By Index |
|-------------------|-------------------|
| `findByRecipientIdOrderByNotificationCreatedDateDesc` | `idx_recipients_recipient_created` |
| `findByRecipientIdAndIsReadFalse` | `idx_recipients_recipient_unread` |
| `findByRecipientIdAndIsReadFalseAndNotificationTypeOrderByNotificationCreatedDateDesc` | `idx_recipients_complex_query` |
| `findByNotificationIdAndRecipientId` | `idx_recipients_notification_recipient` |
| `findByNotificationId` | `idx_notification_recipients_notification_id` |

### Common Query Patterns

1. **User Notification Retrieval**
   ```sql
   SELECT * FROM notification_recipients nr 
   JOIN notifications n ON nr.notification_id = n.id 
   WHERE nr.recipient_id = ? 
   ORDER BY n.created_date DESC
   ```
   - Optimized by: `idx_recipients_recipient_created` + `idx_notifications_covering_details`

2. **Unread Notifications**
   ```sql
   SELECT * FROM notification_recipients 
   WHERE recipient_id = ? AND is_read = false
   ```
   - Optimized by: `idx_recipients_recipient_unread`

3. **Type-Specific Notifications**
   ```sql
   SELECT * FROM notification_recipients nr 
   JOIN notifications n ON nr.notification_id = n.id 
   WHERE nr.recipient_id = ? AND nr.is_read = false AND n.type = ?
   ORDER BY n.created_date DESC
   ```
   - Optimized by: `idx_recipients_complex_query` + `idx_notifications_type`

## Performance Benefits

### Expected Improvements

1. **User Notification Queries**: 80-95% performance improvement
2. **Unread Notification Filtering**: 70-90% improvement
3. **Sender-based Queries**: 60-85% improvement
4. **Date Range Queries**: 75-90% improvement
5. **Template Lookups**: 85-95% improvement

### Index Size Considerations

- Single column indexes: ~5-15% of table size
- Composite indexes: ~10-25% of table size
- Covering indexes: ~20-40% of table size
- Total estimated overhead: ~40-80% of base table sizes

## Maintenance Guidelines

### Regular Monitoring

1. **Index Usage Analysis**
   ```sql
   -- Check index usage statistics
   SELECT 
       table_name,
       index_name,
       cardinality,
       sub_part,
       packed,
       nullable,
       index_type
   FROM information_schema.statistics 
   WHERE table_schema = DATABASE() 
   AND table_name IN ('notifications', 'notification_recipients', 'notification_templates');
   ```

2. **Query Performance Monitoring**
   ```sql
   -- Analyze slow queries
   EXPLAIN ANALYZE SELECT * FROM notification_recipients 
   WHERE recipient_id = '<EMAIL>' 
   ORDER BY created_date DESC;
   ```

### Maintenance Tasks

#### Weekly
- Monitor slow query log for unoptimized queries
- Check index usage statistics
- Verify query execution plans

#### Monthly
- Run `ANALYZE TABLE` on all notification tables
- Review index cardinality and selectivity
- Check for unused indexes

#### Quarterly
- Evaluate new query patterns from application logs
- Consider additional indexes for new features
- Review and optimize existing indexes

### Index Maintenance Commands

```sql
-- Update table statistics
ANALYZE TABLE notifications;
ANALYZE TABLE notification_recipients;
ANALYZE TABLE notification_templates;

-- Check index fragmentation
SHOW TABLE STATUS LIKE 'notifications';
SHOW TABLE STATUS LIKE 'notification_recipients';

-- Rebuild indexes if needed (MySQL 8.0+)
ALTER TABLE notifications ALGORITHM=INPLACE, LOCK=NONE;
ALTER TABLE notification_recipients ALGORITHM=INPLACE, LOCK=NONE;
```

## Testing Strategy

### Performance Testing

1. **Baseline Measurements**
   - Record query execution times before index implementation
   - Measure database CPU and I/O usage
   - Document current query plans

2. **Post-Implementation Testing**
   - Compare query execution times
   - Verify index usage in query plans
   - Monitor overall database performance

3. **Load Testing**
   - Test with realistic data volumes
   - Simulate concurrent user scenarios
   - Measure performance under peak loads

### Test Scenarios

1. **High-Volume User Queries**
   ```java
   // Test retrieving notifications for active users
   List<NotificationRecipient> notifications = 
       notificationRecipientRepository.findByRecipientIdOrderByNotificationCreatedDateDesc(userId);
   ```

2. **Bulk Unread Queries**
   ```java
   // Test filtering unread notifications
   List<NotificationRecipient> unread = 
       notificationRecipientRepository.findByRecipientIdAndIsReadFalse(userId);
   ```

3. **Complex Filtering**
   ```java
   // Test type-specific unread notifications
   List<NotificationRecipient> inAppUnread = 
       notificationRecipientRepository.findByRecipientIdAndIsReadFalseAndNotificationTypeOrderByNotificationCreatedDateDesc(
           userId, NotificationType.IN_APP);
   ```

## Migration Deployment

### Pre-Deployment Checklist

- [ ] Backup database before migration
- [ ] Test migration on staging environment
- [ ] Verify application compatibility
- [ ] Plan for rollback if needed

### Deployment Steps

1. **Apply Migration**
   ```bash
   # Run Flyway migration
   mvn flyway:migrate
   ```

2. **Verify Index Creation**
   ```sql
   SHOW INDEXES FROM notifications;
   SHOW INDEXES FROM notification_recipients;
   SHOW INDEXES FROM notification_templates;
   ```

3. **Monitor Performance**
   - Check query execution plans
   - Monitor database metrics
   - Verify application performance

### Rollback Plan

If performance degrades or issues arise:

```sql
-- Drop indexes if needed (in reverse order)
DROP INDEX idx_recipients_covering_list ON notification_recipients;
DROP INDEX idx_notifications_covering_details ON notifications;
-- ... continue with other indexes
```

## Future Considerations

### Scaling Strategies

1. **Partitioning**
   - Consider date-based partitioning for large datasets
   - Partition by notification type if needed

2. **Archiving**
   - Implement data archiving for old notifications
   - Maintain indexes on active data only

3. **Read Replicas**
   - Use read replicas for reporting queries
   - Optimize indexes differently on replicas

### Monitoring and Alerting

1. **Performance Metrics**
   - Query response times
   - Index usage statistics
   - Database resource utilization

2. **Alerts**
   - Slow query detection
   - Index usage drops
   - Database performance degradation

## Conclusion

This comprehensive indexing strategy addresses the current performance bottlenecks in the notification service while providing a foundation for future scalability. Regular monitoring and maintenance will ensure continued optimal performance as the system grows.