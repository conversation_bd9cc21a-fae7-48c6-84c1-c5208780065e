2025-07-21 15:26:34.615 [background-preinit] INFO  [] [] [] [] o.h.validator.internal.util.Version - HV000001: Hibernate Validator 8.0.2.Final
2025-07-21 15:26:34.705 [restartedMain] INFO  [] [] [] [] c.a.n.NotificationSvcApplication - Starting NotificationSvcApplication using Java 21.0.7 with PID 1872 (D:\grc-platform-v4\notification-svc\target\classes started by AnuragVerma in D:\grc-platform-v4)
2025-07-21 15:26:34.707 [restartedMain] DEBUG [] [] [] [] c.a.n.NotificationSvcApplication - Running with Spring Boot v3.5.0, Spring v6.2.7
2025-07-21 15:26:34.709 [restartedMain] INFO  [] [] [] [] c.a.n.NotificationSvcApplication - No active profile set, falling back to 1 default profile: "default"
2025-07-21 15:26:34.772 [restartedMain] INFO  [] [] [] [] o.s.b.d.e.DevToolsPropertyDefaultsPostProcessor - Devtools property defaults active! Set 'spring.devtools.add-properties' to 'false' to disable
2025-07-21 15:26:34.773 [restartedMain] INFO  [] [] [] [] o.s.b.d.e.DevToolsPropertyDefaultsPostProcessor - For additional web related logging consider setting the 'logging.level.web' property to 'DEBUG'
2025-07-21 15:26:36.921 [restartedMain] INFO  [] [] [] [] o.s.d.r.c.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode
2025-07-21 15:26:36.925 [restartedMain] INFO  [] [] [] [] o.s.d.r.c.RepositoryConfigurationDelegate - Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-07-21 15:26:37.150 [restartedMain] INFO  [] [] [] [] o.s.d.r.c.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 209 ms. Found 4 JPA repository interfaces.
2025-07-21 15:26:37.172 [restartedMain] INFO  [] [] [] [] o.s.d.r.c.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode
2025-07-21 15:26:37.174 [restartedMain] INFO  [] [] [] [] o.s.d.r.c.RepositoryConfigurationDelegate - Bootstrapping Spring Data Redis repositories in DEFAULT mode.
2025-07-21 15:26:37.201 [restartedMain] INFO  [] [] [] [] o.s.d.r.c.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.ascentbusiness.notification_svc.repository.AuditLogRepository; If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository
2025-07-21 15:26:37.205 [restartedMain] INFO  [] [] [] [] o.s.d.r.c.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.ascentbusiness.notification_svc.repository.NotificationRecipientRepository; If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository
2025-07-21 15:26:37.206 [restartedMain] INFO  [] [] [] [] o.s.d.r.c.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.ascentbusiness.notification_svc.repository.NotificationRepository; If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository
2025-07-21 15:26:37.207 [restartedMain] INFO  [] [] [] [] o.s.d.r.c.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.ascentbusiness.notification_svc.repository.NotificationTemplateRepository; If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository
2025-07-21 15:26:37.208 [restartedMain] INFO  [] [] [] [] o.s.d.r.c.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 21 ms. Found 0 Redis repository interfaces.
2025-07-21 15:26:39.026 [restartedMain] INFO  [] [] [] [] o.s.b.w.e.tomcat.TomcatWebServer - Tomcat initialized with port 9091 (http)
2025-07-21 15:26:39.048 [restartedMain] INFO  [] [] [] [] o.a.coyote.http11.Http11NioProtocol - Initializing ProtocolHandler ["http-nio-9091"]
2025-07-21 15:26:39.053 [restartedMain] INFO  [] [] [] [] o.a.catalina.core.StandardService - Starting service [Tomcat]
2025-07-21 15:26:39.054 [restartedMain] INFO  [] [] [] [] o.a.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/10.1.41]
2025-07-21 15:26:39.134 [restartedMain] INFO  [] [] [] [] o.a.c.c.C.[Tomcat].[localhost].[/] - Initializing Spring embedded WebApplicationContext
2025-07-21 15:26:39.134 [restartedMain] INFO  [] [] [] [] o.s.b.w.s.c.ServletWebServerApplicationContext - Root WebApplicationContext: initialization completed in 4360 ms
2025-07-21 15:26:39.494 [restartedMain] DEBUG [] [] [] [] c.a.n.s.JwtAuthenticationFilter - Filter 'jwtAuthenticationFilter' configured for use
2025-07-21 15:26:39.495 [restartedMain] DEBUG [] [] [] [] c.a.n.config.LoggingConfig$1 - Filter 'correlationIdFilter' configured for use
2025-07-21 15:26:39.764 [restartedMain] INFO  [] [] [] [] o.h.jpa.internal.util.LogHelper - HHH000204: Processing PersistenceUnitInfo [name: default]
2025-07-21 15:26:39.861 [restartedMain] INFO  [] [] [] [] org.hibernate.Version - HHH000412: Hibernate ORM core version 6.6.15.Final
2025-07-21 15:26:39.919 [restartedMain] INFO  [] [] [] [] o.h.c.i.RegionFactoryInitiator - HHH000026: Second-level cache disabled
2025-07-21 15:26:40.262 [restartedMain] INFO  [] [] [] [] o.s.o.j.p.SpringPersistenceUnitInfo - No LoadTimeWeaver setup: ignoring JPA class transformer
2025-07-21 15:26:40.298 [restartedMain] INFO  [] [] [] [] com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Starting...
2025-07-21 15:26:40.613 [restartedMain] INFO  [] [] [] [] com.zaxxer.hikari.pool.HikariPool - HikariPool-1 - Added connection com.mysql.cj.jdbc.ConnectionImpl@772510ef
2025-07-21 15:26:40.616 [restartedMain] INFO  [] [] [] [] com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Start completed.
2025-07-21 15:26:40.685 [restartedMain] WARN  [] [] [] [] org.hibernate.orm.deprecation - HHH90000025: MySQL8Dialect does not need to be specified explicitly using 'hibernate.dialect' (remove the property setting and it will be selected by default)
2025-07-21 15:26:40.687 [restartedMain] WARN  [] [] [] [] org.hibernate.orm.deprecation - HHH90000026: MySQL8Dialect has been deprecated; use org.hibernate.dialect.MySQLDialect instead
2025-07-21 15:26:40.713 [restartedMain] INFO  [] [] [] [] o.hibernate.orm.connections.pooling - HHH10001005: Database info:
	Database JDBC URL [Connecting through datasource 'HikariDataSource (HikariPool-1)']
	Database driver: undefined/unknown
	Database version: 8.0
	Autocommit mode: undefined/unknown
	Isolation level: undefined/unknown
	Minimum pool size: undefined/unknown
	Maximum pool size: undefined/unknown
2025-07-21 15:26:42.058 [restartedMain] INFO  [] [] [] [] o.h.e.t.j.p.i.JtaPlatformInitiator - HHH000489: No JTA platform available (set 'hibernate.transaction.jta.platform' to enable JTA platform integration)
2025-07-21 15:26:42.222 [restartedMain] INFO  [] [] [] [] o.s.o.j.LocalContainerEntityManagerFactoryBean - Initialized JPA EntityManagerFactory for persistence unit 'default'
2025-07-21 15:26:42.779 [restartedMain] INFO  [] [] [] [] o.s.d.j.r.query.QueryEnhancerFactory - Hibernate is in classpath; If applicable, HQL parser will be used.
2025-07-21 15:26:45.817 [restartedMain] WARN  [] [] [] [] o.s.b.a.o.j.JpaBaseConfiguration$JpaWebConfiguration - spring.jpa.open-in-view is enabled by default. Therefore, database queries may be performed during view rendering. Explicitly configure spring.jpa.open-in-view to disable this warning
2025-07-21 15:26:46.639 [restartedMain] INFO  [] [] [] [] o.s.g.e.DefaultSchemaResourceGraphQlSourceBuilder - Loaded 1 resource(s) in the GraphQL schema.
2025-07-21 15:26:47.024 [restartedMain] INFO  [] [] [] [] o.s.b.a.g.GraphQlAutoConfiguration - GraphQL schema inspection:
	Unmapped fields: {AuditLogPage=[pageNumber, pageSize]}
	Unmapped registrations: {}
	Unmapped arguments: {}
	Skipped types: []
2025-07-21 15:26:47.078 [restartedMain] INFO  [] [] [] [] o.s.b.a.g.s.GraphQlWebMvcAutoConfiguration - GraphQL endpoint HTTP POST /graphql
2025-07-21 15:26:47.405 [restartedMain] INFO  [] [] [] [] o.s.b.a.e.web.EndpointLinksResolver - Exposing 3 endpoints beneath base path '/actuator'
2025-07-21 15:26:47.486 [restartedMain] WARN  [] [] [] [] o.s.b.a.s.s.UserDetailsServiceAutoConfiguration - 

Using generated security password: 359574f0-8278-4141-9d99-30359219a36d

This generated password is for development use only. Your security configuration must be updated before running your application in production.

2025-07-21 15:26:47.509 [restartedMain] INFO  [] [] [] [] o.s.s.c.a.a.c.InitializeUserDetailsBeanManagerConfigurer$InitializeUserDetailsManagerConfigurer - Global AuthenticationManager configured with UserDetailsService bean with name inMemoryUserDetailsManager
2025-07-21 15:26:47.799 [restartedMain] DEBUG [] [] [] [] o.s.s.web.DefaultSecurityFilterChain - Will secure any request with filters: DisableEncodeUrlFilter, WebAsyncManagerIntegrationFilter, SecurityContextHolderFilter, HeaderWriterFilter, CorsFilter, LogoutFilter, JwtAuthenticationFilter, RequestCacheAwareFilter, SecurityContextHolderAwareRequestFilter, AnonymousAuthenticationFilter, SessionManagementFilter, ExceptionTranslationFilter, AuthorizationFilter
2025-07-21 15:26:48.690 [restartedMain] INFO  [] [] [] [] o.s.b.d.a.OptionalLiveReloadServer - LiveReload server is running on port 35729
2025-07-21 15:26:48.810 [restartedMain] INFO  [] [] [] [] o.a.coyote.http11.Http11NioProtocol - Starting ProtocolHandler ["http-nio-9091"]
2025-07-21 15:26:48.838 [restartedMain] INFO  [] [] [] [] o.s.b.w.e.tomcat.TomcatWebServer - Tomcat started on port 9091 (http) with context path '/'
2025-07-21 15:26:48.842 [restartedMain] INFO  [] [] [] [] o.s.a.r.c.CachingConnectionFactory - Attempting to connect to: [localhost:5672]
2025-07-21 15:26:48.872 [restartedMain] INFO  [] [] [] [] o.s.a.r.l.SimpleMessageListenerContainer - Broker not available; cannot force queue declarations during start: java.net.ConnectException: Connection refused: getsockopt
2025-07-21 15:26:48.893 [rabbit-simple-0] INFO  [] [] [] [] o.s.a.r.c.CachingConnectionFactory - Attempting to connect to: [localhost:5672]
2025-07-21 15:26:48.895 [rabbit-simple-0] ERROR [] [] [] [] o.s.a.r.l.SimpleMessageListenerContainer - Failed to check/redeclare auto-delete queue(s).
org.springframework.amqp.AmqpConnectException: java.net.ConnectException: Connection refused: getsockopt
	at org.springframework.amqp.rabbit.support.RabbitExceptionTranslator.convertRabbitAccessException(RabbitExceptionTranslator.java:61)
	at org.springframework.amqp.rabbit.connection.AbstractConnectionFactory.createBareConnection(AbstractConnectionFactory.java:632)
	at org.springframework.amqp.rabbit.connection.CachingConnectionFactory.createConnection(CachingConnectionFactory.java:726)
	at org.springframework.amqp.rabbit.connection.ConnectionFactoryUtils.createConnection(ConnectionFactoryUtils.java:257)
	at org.springframework.amqp.rabbit.core.RabbitTemplate.doExecute(RabbitTemplate.java:2262)
	at org.springframework.amqp.rabbit.core.RabbitTemplate.execute(RabbitTemplate.java:2235)
	at org.springframework.amqp.rabbit.core.RabbitTemplate.execute(RabbitTemplate.java:2215)
	at org.springframework.amqp.rabbit.core.RabbitAdmin.getQueueInfo(RabbitAdmin.java:467)
	at org.springframework.amqp.rabbit.core.RabbitAdmin.getQueueProperties(RabbitAdmin.java:451)
	at org.springframework.amqp.rabbit.listener.AbstractMessageListenerContainer.attemptDeclarations(AbstractMessageListenerContainer.java:1972)
	at org.springframework.amqp.rabbit.listener.AbstractMessageListenerContainer.redeclareElementsIfNecessary(AbstractMessageListenerContainer.java:1936)
	at org.springframework.amqp.rabbit.listener.SimpleMessageListenerContainer$AsyncMessageProcessingConsumer.initialize(SimpleMessageListenerContainer.java:1481)
	at org.springframework.amqp.rabbit.listener.SimpleMessageListenerContainer$AsyncMessageProcessingConsumer.run(SimpleMessageListenerContainer.java:1322)
	at java.base/java.lang.VirtualThread.run(VirtualThread.java:329)
Caused by: java.net.ConnectException: Connection refused: getsockopt
	at java.base/sun.nio.ch.Net.pollConnect(Native Method)
	at java.base/sun.nio.ch.Net.pollConnectNow(Net.java:682)
	at java.base/sun.nio.ch.NioSocketImpl.timedFinishConnect(NioSocketImpl.java:542)
	at java.base/sun.nio.ch.NioSocketImpl.connect(NioSocketImpl.java:592)
	at java.base/java.net.SocksSocketImpl.connect(SocksSocketImpl.java:327)
	at java.base/java.net.Socket.connect(Socket.java:751)
	at com.rabbitmq.client.impl.SocketFrameHandlerFactory.create(SocketFrameHandlerFactory.java:61)
	at com.rabbitmq.client.ConnectionFactory.newConnection(ConnectionFactory.java:1249)
	at com.rabbitmq.client.ConnectionFactory.newConnection(ConnectionFactory.java:1198)
	at org.springframework.amqp.rabbit.connection.AbstractConnectionFactory.connectAddresses(AbstractConnectionFactory.java:678)
	at org.springframework.amqp.rabbit.connection.AbstractConnectionFactory.connect(AbstractConnectionFactory.java:647)
	at org.springframework.amqp.rabbit.connection.AbstractConnectionFactory.createBareConnection(AbstractConnectionFactory.java:593)
	... 12 common frames omitted
2025-07-21 15:26:48.904 [rabbit-simple-0] INFO  [] [] [] [] o.s.a.r.c.CachingConnectionFactory - Attempting to connect to: [localhost:5672]
2025-07-21 15:26:48.931 [restartedMain] INFO  [] [] [] [] c.a.n.NotificationSvcApplication - Started NotificationSvcApplication in 15.032 seconds (process running for 15.744)
2025-07-21 15:26:54.025 [rabbit-simple-0] WARN  [] [] [] [] o.s.a.r.l.SimpleMessageListenerContainer - Consumer raised exception, processing can restart if the connection factory supports it. Exception summary: org.springframework.amqp.AmqpConnectException: java.net.ConnectException: Connection refused: getsockopt
2025-07-21 15:26:54.035 [rabbit-simple-0] INFO  [] [] [] [] o.s.a.r.l.SimpleMessageListenerContainer - Restarting Consumer@5290f855: tags=[[]], channel=null, acknowledgeMode=AUTO local queue size=0
2025-07-21 15:26:54.039 [rabbit-simple-1] INFO  [] [] [] [] o.s.a.r.c.CachingConnectionFactory - Attempting to connect to: [localhost:5672]
2025-07-21 15:26:54.055 [rabbit-simple-1] ERROR [] [] [] [] o.s.a.r.l.SimpleMessageListenerContainer - Failed to check/redeclare auto-delete queue(s).
2025-07-21 15:26:54.055 [rabbit-simple-1] INFO  [] [] [] [] o.s.a.r.c.CachingConnectionFactory - Attempting to connect to: [localhost:5672]
2025-07-21 15:26:59.151 [rabbit-simple-1] WARN  [] [] [] [] o.s.a.r.l.SimpleMessageListenerContainer - Consumer raised exception, processing can restart if the connection factory supports it. Exception summary: org.springframework.amqp.AmqpConnectException: java.net.ConnectException: Connection refused: getsockopt
2025-07-21 15:26:59.153 [rabbit-simple-1] INFO  [] [] [] [] o.s.a.r.l.SimpleMessageListenerContainer - Restarting Consumer@74bb4f2d: tags=[[]], channel=null, acknowledgeMode=AUTO local queue size=0
2025-07-21 15:26:59.157 [rabbit-simple-2] INFO  [] [] [] [] o.s.a.r.c.CachingConnectionFactory - Attempting to connect to: [localhost:5672]
2025-07-21 15:26:59.161 [rabbit-simple-2] ERROR [] [] [] [] o.s.a.r.l.SimpleMessageListenerContainer - Failed to check/redeclare auto-delete queue(s).
2025-07-21 15:26:59.163 [rabbit-simple-2] INFO  [] [] [] [] o.s.a.r.c.CachingConnectionFactory - Attempting to connect to: [localhost:5672]
2025-07-21 15:27:04.271 [rabbit-simple-2] WARN  [] [] [] [] o.s.a.r.l.SimpleMessageListenerContainer - Consumer raised exception, processing can restart if the connection factory supports it. Exception summary: org.springframework.amqp.AmqpConnectException: java.net.ConnectException: Connection refused: getsockopt
2025-07-21 15:27:04.272 [rabbit-simple-2] INFO  [] [] [] [] o.s.a.r.l.SimpleMessageListenerContainer - Restarting Consumer@1727aaa8: tags=[[]], channel=null, acknowledgeMode=AUTO local queue size=0
2025-07-21 15:27:04.273 [rabbit-simple-3] INFO  [] [] [] [] o.s.a.r.c.CachingConnectionFactory - Attempting to connect to: [localhost:5672]
2025-07-21 15:27:04.296 [rabbit-simple-3] ERROR [] [] [] [] o.s.a.r.l.SimpleMessageListenerContainer - Failed to check/redeclare auto-delete queue(s).
2025-07-21 15:27:04.296 [rabbit-simple-3] INFO  [] [] [] [] o.s.a.r.c.CachingConnectionFactory - Attempting to connect to: [localhost:5672]
2025-07-21 15:27:09.428 [rabbit-simple-3] WARN  [] [] [] [] o.s.a.r.l.SimpleMessageListenerContainer - Consumer raised exception, processing can restart if the connection factory supports it. Exception summary: org.springframework.amqp.AmqpConnectException: java.net.ConnectException: Connection refused: getsockopt
2025-07-21 15:27:09.429 [rabbit-simple-3] INFO  [] [] [] [] o.s.a.r.l.SimpleMessageListenerContainer - Restarting Consumer@164eaf3d: tags=[[]], channel=null, acknowledgeMode=AUTO local queue size=0
2025-07-21 15:27:09.432 [rabbit-simple-4] INFO  [] [] [] [] o.s.a.r.c.CachingConnectionFactory - Attempting to connect to: [localhost:5672]
2025-07-21 15:27:09.434 [rabbit-simple-4] ERROR [] [] [] [] o.s.a.r.l.SimpleMessageListenerContainer - Failed to check/redeclare auto-delete queue(s).
2025-07-21 15:27:09.435 [rabbit-simple-4] INFO  [] [] [] [] o.s.a.r.c.CachingConnectionFactory - Attempting to connect to: [localhost:5672]
2025-07-21 15:27:14.580 [rabbit-simple-4] WARN  [] [] [] [] o.s.a.r.l.SimpleMessageListenerContainer - Consumer raised exception, processing can restart if the connection factory supports it. Exception summary: org.springframework.amqp.AmqpConnectException: java.net.ConnectException: Connection refused: getsockopt
2025-07-21 15:27:14.582 [rabbit-simple-4] INFO  [] [] [] [] o.s.a.r.l.SimpleMessageListenerContainer - Restarting Consumer@19e0463c: tags=[[]], channel=null, acknowledgeMode=AUTO local queue size=0
2025-07-21 15:27:14.586 [rabbit-simple-5] INFO  [] [] [] [] o.s.a.r.c.CachingConnectionFactory - Attempting to connect to: [localhost:5672]
2025-07-21 15:27:14.590 [rabbit-simple-5] ERROR [] [] [] [] o.s.a.r.l.SimpleMessageListenerContainer - Failed to check/redeclare auto-delete queue(s).
2025-07-21 15:27:14.591 [rabbit-simple-5] INFO  [] [] [] [] o.s.a.r.c.CachingConnectionFactory - Attempting to connect to: [localhost:5672]
2025-07-21 15:27:19.687 [rabbit-simple-5] WARN  [] [] [] [] o.s.a.r.l.SimpleMessageListenerContainer - Consumer raised exception, processing can restart if the connection factory supports it. Exception summary: org.springframework.amqp.AmqpConnectException: java.net.ConnectException: Connection refused: getsockopt
2025-07-21 15:27:19.688 [rabbit-simple-5] INFO  [] [] [] [] o.s.a.r.l.SimpleMessageListenerContainer - Restarting Consumer@382258fe: tags=[[]], channel=null, acknowledgeMode=AUTO local queue size=0
2025-07-21 15:27:19.690 [rabbit-simple-6] INFO  [] [] [] [] o.s.a.r.c.CachingConnectionFactory - Attempting to connect to: [localhost:5672]
2025-07-21 15:27:19.703 [rabbit-simple-6] ERROR [] [] [] [] o.s.a.r.l.SimpleMessageListenerContainer - Failed to check/redeclare auto-delete queue(s).
2025-07-21 15:27:19.703 [rabbit-simple-6] INFO  [] [] [] [] o.s.a.r.c.CachingConnectionFactory - Attempting to connect to: [localhost:5672]
2025-07-21 15:27:24.879 [rabbit-simple-6] WARN  [] [] [] [] o.s.a.r.l.SimpleMessageListenerContainer - Consumer raised exception, processing can restart if the connection factory supports it. Exception summary: org.springframework.amqp.AmqpConnectException: java.net.ConnectException: Connection refused: getsockopt
2025-07-21 15:27:24.882 [rabbit-simple-6] INFO  [] [] [] [] o.s.a.r.l.SimpleMessageListenerContainer - Restarting Consumer@1ddfc1d8: tags=[[]], channel=null, acknowledgeMode=AUTO local queue size=0
2025-07-21 15:27:24.886 [rabbit-simple-7] INFO  [] [] [] [] o.s.a.r.c.CachingConnectionFactory - Attempting to connect to: [localhost:5672]
2025-07-21 15:27:24.927 [rabbit-simple-7] ERROR [] [] [] [] o.s.a.r.l.SimpleMessageListenerContainer - Failed to check/redeclare auto-delete queue(s).
2025-07-21 15:27:24.928 [rabbit-simple-7] INFO  [] [] [] [] o.s.a.r.c.CachingConnectionFactory - Attempting to connect to: [localhost:5672]
2025-07-21 15:27:25.483 [tomcat-handler-0] INFO  [] [] [] [] o.a.c.c.C.[Tomcat].[localhost].[/] - Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-07-21 15:27:25.483 [tomcat-handler-0] INFO  [] [] [] [] o.s.web.servlet.DispatcherServlet - Initializing Servlet 'dispatcherServlet'
2025-07-21 15:27:25.488 [tomcat-handler-0] INFO  [] [] [] [] o.s.web.servlet.DispatcherServlet - Completed initialization in 4 ms
2025-07-21 15:27:25.540 [tomcat-handler-0] DEBUG [] [] [] [] o.s.security.web.FilterChainProxy - Securing GET /graphiql?path=/graphql
2025-07-21 15:27:25.563 [tomcat-handler-0] DEBUG [] [] [] [] o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-07-21 15:27:25.573 [tomcat-handler-0] DEBUG [] [] [] [] o.s.security.web.FilterChainProxy - Secured GET /graphiql?path=/graphql
2025-07-21 15:27:25.574 [tomcat-handler-0] INFO  [b0f36623-da0c-46ee-8ba9-75611834e59c] [742858f6-1517-4ae7-a579-02d8536fae01] [] [] c.a.n.config.LoggingConfig - Incoming request - Method: GET, URI: /graphiql, RemoteAddr: 0:0:0:0:0:0:0:1, UserAgent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
2025-07-21 15:27:25.605 [tomcat-handler-0] INFO  [b0f36623-da0c-46ee-8ba9-75611834e59c] [742858f6-1517-4ae7-a579-02d8536fae01] [] [] c.a.n.config.LoggingConfig - Outgoing response - Status: 200, Duration: 30ms, ContentType: text/html
2025-07-21 15:27:26.664 [tomcat-handler-1] DEBUG [] [] [] [] o.s.security.web.FilterChainProxy - Securing POST /graphql
2025-07-21 15:27:26.680 [tomcat-handler-1] DEBUG [] [] [] [] c.a.n.s.JwtAuthenticationFilter - No valid Authorization header found for request: /graphql
2025-07-21 15:27:27.409 [tomcat-handler-2] DEBUG [] [] [] [] o.s.security.web.FilterChainProxy - Securing POST /graphql
2025-07-21 15:27:27.412 [tomcat-handler-2] DEBUG [] [] [] [] c.a.n.s.JwtAuthenticationFilter - No valid Authorization header found for request: /graphql
2025-07-21 15:27:27.418 [tomcat-handler-3] DEBUG [] [] [] [] o.s.security.web.FilterChainProxy - Securing GET /favicon.ico
2025-07-21 15:27:27.419 [tomcat-handler-3] DEBUG [] [] [] [] c.a.n.s.JwtAuthenticationFilter - No valid Authorization header found for request: /favicon.ico
2025-07-21 15:27:30.047 [rabbit-simple-7] WARN  [] [] [] [] o.s.a.r.l.SimpleMessageListenerContainer - Consumer raised exception, processing can restart if the connection factory supports it. Exception summary: org.springframework.amqp.AmqpConnectException: java.net.ConnectException: Connection refused: getsockopt
2025-07-21 15:27:30.047 [rabbit-simple-7] INFO  [] [] [] [] o.s.a.r.l.SimpleMessageListenerContainer - Restarting Consumer@5cb42df6: tags=[[]], channel=null, acknowledgeMode=AUTO local queue size=0
2025-07-21 15:27:30.048 [rabbit-simple-8] INFO  [] [] [] [] o.s.a.r.c.CachingConnectionFactory - Attempting to connect to: [localhost:5672]
2025-07-21 15:27:30.050 [rabbit-simple-8] ERROR [] [] [] [] o.s.a.r.l.SimpleMessageListenerContainer - Failed to check/redeclare auto-delete queue(s).
2025-07-21 15:27:30.051 [rabbit-simple-8] INFO  [] [] [] [] o.s.a.r.c.CachingConnectionFactory - Attempting to connect to: [localhost:5672]
2025-07-21 15:27:35.184 [rabbit-simple-8] WARN  [] [] [] [] o.s.a.r.l.SimpleMessageListenerContainer - Consumer raised exception, processing can restart if the connection factory supports it. Exception summary: org.springframework.amqp.AmqpConnectException: java.net.ConnectException: Connection refused: getsockopt
2025-07-21 15:27:35.185 [rabbit-simple-8] INFO  [] [] [] [] o.s.a.r.l.SimpleMessageListenerContainer - Restarting Consumer@6e9f41be: tags=[[]], channel=null, acknowledgeMode=AUTO local queue size=0
2025-07-21 15:27:35.186 [rabbit-simple-9] INFO  [] [] [] [] o.s.a.r.c.CachingConnectionFactory - Attempting to connect to: [localhost:5672]
2025-07-21 15:27:35.200 [rabbit-simple-9] ERROR [] [] [] [] o.s.a.r.l.SimpleMessageListenerContainer - Failed to check/redeclare auto-delete queue(s).
2025-07-21 15:27:35.200 [rabbit-simple-9] INFO  [] [] [] [] o.s.a.r.c.CachingConnectionFactory - Attempting to connect to: [localhost:5672]
2025-07-21 15:27:40.324 [rabbit-simple-9] WARN  [] [] [] [] o.s.a.r.l.SimpleMessageListenerContainer - Consumer raised exception, processing can restart if the connection factory supports it. Exception summary: org.springframework.amqp.AmqpConnectException: java.net.ConnectException: Connection refused: getsockopt
2025-07-21 15:27:40.325 [rabbit-simple-9] INFO  [] [] [] [] o.s.a.r.l.SimpleMessageListenerContainer - Restarting Consumer@3a9c336a: tags=[[]], channel=null, acknowledgeMode=AUTO local queue size=0
2025-07-21 15:27:40.326 [rabbit-simple-10] INFO  [] [] [] [] o.s.a.r.c.CachingConnectionFactory - Attempting to connect to: [localhost:5672]
2025-07-21 15:27:40.340 [rabbit-simple-10] ERROR [] [] [] [] o.s.a.r.l.SimpleMessageListenerContainer - Failed to check/redeclare auto-delete queue(s).
2025-07-21 15:27:40.340 [rabbit-simple-10] INFO  [] [] [] [] o.s.a.r.c.CachingConnectionFactory - Attempting to connect to: [localhost:5672]
2025-07-21 15:27:43.909 [tomcat-handler-4] DEBUG [] [] [] [] o.s.security.web.FilterChainProxy - Securing POST /graphql
2025-07-21 15:27:43.910 [tomcat-handler-4] DEBUG [] [] [] [] c.a.n.s.JwtAuthenticationFilter - No valid Authorization header found for request: /graphql
2025-07-21 15:27:45.440 [rabbit-simple-10] WARN  [] [] [] [] o.s.a.r.l.SimpleMessageListenerContainer - Consumer raised exception, processing can restart if the connection factory supports it. Exception summary: org.springframework.amqp.AmqpConnectException: java.net.ConnectException: Connection refused: getsockopt
2025-07-21 15:27:45.441 [rabbit-simple-10] INFO  [] [] [] [] o.s.a.r.l.SimpleMessageListenerContainer - Restarting Consumer@393e9dee: tags=[[]], channel=null, acknowledgeMode=AUTO local queue size=0
2025-07-21 15:27:45.442 [rabbit-simple-11] INFO  [] [] [] [] o.s.a.r.c.CachingConnectionFactory - Attempting to connect to: [localhost:5672]
2025-07-21 15:27:45.466 [rabbit-simple-11] ERROR [] [] [] [] o.s.a.r.l.SimpleMessageListenerContainer - Failed to check/redeclare auto-delete queue(s).
2025-07-21 15:27:45.467 [rabbit-simple-11] INFO  [] [] [] [] o.s.a.r.c.CachingConnectionFactory - Attempting to connect to: [localhost:5672]
2025-07-21 15:27:50.567 [rabbit-simple-11] WARN  [] [] [] [] o.s.a.r.l.SimpleMessageListenerContainer - Consumer raised exception, processing can restart if the connection factory supports it. Exception summary: org.springframework.amqp.AmqpConnectException: java.net.ConnectException: Connection refused: getsockopt
2025-07-21 15:27:50.570 [rabbit-simple-11] INFO  [] [] [] [] o.s.a.r.l.SimpleMessageListenerContainer - Restarting Consumer@59484443: tags=[[]], channel=null, acknowledgeMode=AUTO local queue size=0
2025-07-21 15:27:50.575 [rabbit-simple-12] INFO  [] [] [] [] o.s.a.r.c.CachingConnectionFactory - Attempting to connect to: [localhost:5672]
2025-07-21 15:27:50.580 [rabbit-simple-12] ERROR [] [] [] [] o.s.a.r.l.SimpleMessageListenerContainer - Failed to check/redeclare auto-delete queue(s).
2025-07-21 15:27:50.581 [rabbit-simple-12] INFO  [] [] [] [] o.s.a.r.c.CachingConnectionFactory - Attempting to connect to: [localhost:5672]
2025-07-21 15:27:55.699 [rabbit-simple-12] WARN  [] [] [] [] o.s.a.r.l.SimpleMessageListenerContainer - Consumer raised exception, processing can restart if the connection factory supports it. Exception summary: org.springframework.amqp.AmqpConnectException: java.net.ConnectException: Connection refused: getsockopt
2025-07-21 15:27:55.700 [rabbit-simple-12] INFO  [] [] [] [] o.s.a.r.l.SimpleMessageListenerContainer - Restarting Consumer@2e73b12e: tags=[[]], channel=null, acknowledgeMode=AUTO local queue size=0
2025-07-21 15:27:55.702 [rabbit-simple-13] INFO  [] [] [] [] o.s.a.r.c.CachingConnectionFactory - Attempting to connect to: [localhost:5672]
2025-07-21 15:27:55.715 [rabbit-simple-13] ERROR [] [] [] [] o.s.a.r.l.SimpleMessageListenerContainer - Failed to check/redeclare auto-delete queue(s).
2025-07-21 15:27:55.716 [rabbit-simple-13] INFO  [] [] [] [] o.s.a.r.c.CachingConnectionFactory - Attempting to connect to: [localhost:5672]
2025-07-21 15:27:58.763 [tomcat-handler-5] DEBUG [] [] [] [] o.s.security.web.FilterChainProxy - Securing GET /api/test/jwt/generate/quick
2025-07-21 15:27:58.764 [tomcat-handler-5] DEBUG [] [] [] [] o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-07-21 15:27:58.766 [tomcat-handler-5] DEBUG [] [] [] [] o.s.security.web.FilterChainProxy - Secured GET /api/test/jwt/generate/quick
2025-07-21 15:27:58.766 [tomcat-handler-5] INFO  [033cc2ff-173c-4226-a482-31b44819b99c] [6c57ed38-81b6-483e-bc32-11102a5717f1] [] [] c.a.n.config.LoggingConfig - Incoming request - Method: GET, URI: /api/test/jwt/generate/quick, RemoteAddr: 0:0:0:0:0:0:0:1, UserAgent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
2025-07-21 15:27:59.076 [tomcat-handler-5] WARN  [033cc2ff-173c-4226-a482-31b44819b99c] [6c57ed38-81b6-483e-bc32-11102a5717f1] [] [] c.a.n.s.JwtTokenGeneratorService - Generated new key pair for testing. In production, use proper key management.
2025-07-21 15:27:59.223 [tomcat-handler-5] INFO  [033cc2ff-173c-4226-a482-31b44819b99c] [6c57ed38-81b6-483e-bc32-11102a5717f1] [] [] c.a.n.controller.JwtTestController - Generated JWT token for client: test-client
2025-07-21 15:27:59.253 [tomcat-handler-5] INFO  [033cc2ff-173c-4226-a482-31b44819b99c] [6c57ed38-81b6-483e-bc32-11102a5717f1] [] [] c.a.n.config.LoggingConfig - Outgoing response - Status: 200, Duration: 486ms, ContentType: application/json
2025-07-21 15:28:00.325 [tomcat-handler-6] DEBUG [] [] [] [] o.s.security.web.FilterChainProxy - Securing GET /favicon.ico
2025-07-21 15:28:00.326 [tomcat-handler-6] DEBUG [] [] [] [] c.a.n.s.JwtAuthenticationFilter - No valid Authorization header found for request: /favicon.ico
2025-07-21 15:28:00.870 [rabbit-simple-13] WARN  [] [] [] [] o.s.a.r.l.SimpleMessageListenerContainer - Consumer raised exception, processing can restart if the connection factory supports it. Exception summary: org.springframework.amqp.AmqpConnectException: java.net.ConnectException: Connection refused: getsockopt
2025-07-21 15:28:00.871 [rabbit-simple-13] INFO  [] [] [] [] o.s.a.r.l.SimpleMessageListenerContainer - Restarting Consumer@7387123c: tags=[[]], channel=null, acknowledgeMode=AUTO local queue size=0
2025-07-21 15:28:00.876 [rabbit-simple-14] INFO  [] [] [] [] o.s.a.r.c.CachingConnectionFactory - Attempting to connect to: [localhost:5672]
2025-07-21 15:28:00.902 [rabbit-simple-14] ERROR [] [] [] [] o.s.a.r.l.SimpleMessageListenerContainer - Failed to check/redeclare auto-delete queue(s).
2025-07-21 15:28:00.903 [rabbit-simple-14] INFO  [] [] [] [] o.s.a.r.c.CachingConnectionFactory - Attempting to connect to: [localhost:5672]
2025-07-21 15:28:06.025 [rabbit-simple-14] WARN  [] [] [] [] o.s.a.r.l.SimpleMessageListenerContainer - Consumer raised exception, processing can restart if the connection factory supports it. Exception summary: org.springframework.amqp.AmqpConnectException: java.net.ConnectException: Connection refused: getsockopt
2025-07-21 15:28:06.027 [rabbit-simple-14] INFO  [] [] [] [] o.s.a.r.l.SimpleMessageListenerContainer - Restarting Consumer@3c27a16e: tags=[[]], channel=null, acknowledgeMode=AUTO local queue size=0
2025-07-21 15:28:06.032 [rabbit-simple-15] INFO  [] [] [] [] o.s.a.r.c.CachingConnectionFactory - Attempting to connect to: [localhost:5672]
2025-07-21 15:28:06.067 [rabbit-simple-15] ERROR [] [] [] [] o.s.a.r.l.SimpleMessageListenerContainer - Failed to check/redeclare auto-delete queue(s).
2025-07-21 15:28:06.068 [rabbit-simple-15] INFO  [] [] [] [] o.s.a.r.c.CachingConnectionFactory - Attempting to connect to: [localhost:5672]
2025-07-21 15:28:11.135 [rabbit-simple-15] WARN  [] [] [] [] o.s.a.r.l.SimpleMessageListenerContainer - Consumer raised exception, processing can restart if the connection factory supports it. Exception summary: org.springframework.amqp.AmqpConnectException: java.net.ConnectException: Connection refused: getsockopt
2025-07-21 15:28:11.136 [rabbit-simple-15] INFO  [] [] [] [] o.s.a.r.l.SimpleMessageListenerContainer - Restarting Consumer@31d36566: tags=[[]], channel=null, acknowledgeMode=AUTO local queue size=0
2025-07-21 15:28:11.138 [rabbit-simple-16] INFO  [] [] [] [] o.s.a.r.c.CachingConnectionFactory - Attempting to connect to: [localhost:5672]
2025-07-21 15:28:11.150 [rabbit-simple-16] ERROR [] [] [] [] o.s.a.r.l.SimpleMessageListenerContainer - Failed to check/redeclare auto-delete queue(s).
2025-07-21 15:28:11.150 [rabbit-simple-16] INFO  [] [] [] [] o.s.a.r.c.CachingConnectionFactory - Attempting to connect to: [localhost:5672]
2025-07-21 15:28:16.267 [rabbit-simple-16] WARN  [] [] [] [] o.s.a.r.l.SimpleMessageListenerContainer - Consumer raised exception, processing can restart if the connection factory supports it. Exception summary: org.springframework.amqp.AmqpConnectException: java.net.ConnectException: Connection refused: getsockopt
2025-07-21 15:28:16.268 [rabbit-simple-16] INFO  [] [] [] [] o.s.a.r.l.SimpleMessageListenerContainer - Restarting Consumer@45402baa: tags=[[]], channel=null, acknowledgeMode=AUTO local queue size=0
2025-07-21 15:28:16.270 [rabbit-simple-17] INFO  [] [] [] [] o.s.a.r.c.CachingConnectionFactory - Attempting to connect to: [localhost:5672]
2025-07-21 15:28:16.271 [rabbit-simple-17] ERROR [] [] [] [] o.s.a.r.l.SimpleMessageListenerContainer - Failed to check/redeclare auto-delete queue(s).
2025-07-21 15:28:16.272 [rabbit-simple-17] INFO  [] [] [] [] o.s.a.r.c.CachingConnectionFactory - Attempting to connect to: [localhost:5672]
2025-07-21 15:28:21.360 [rabbit-simple-17] WARN  [] [] [] [] o.s.a.r.l.SimpleMessageListenerContainer - Consumer raised exception, processing can restart if the connection factory supports it. Exception summary: org.springframework.amqp.AmqpConnectException: java.net.ConnectException: Connection refused: getsockopt
2025-07-21 15:28:21.361 [rabbit-simple-17] INFO  [] [] [] [] o.s.a.r.l.SimpleMessageListenerContainer - Restarting Consumer@3f149d2c: tags=[[]], channel=null, acknowledgeMode=AUTO local queue size=0
2025-07-21 15:28:21.362 [rabbit-simple-18] INFO  [] [] [] [] o.s.a.r.c.CachingConnectionFactory - Attempting to connect to: [localhost:5672]
2025-07-21 15:28:21.363 [rabbit-simple-18] ERROR [] [] [] [] o.s.a.r.l.SimpleMessageListenerContainer - Failed to check/redeclare auto-delete queue(s).
2025-07-21 15:28:21.363 [rabbit-simple-18] INFO  [] [] [] [] o.s.a.r.c.CachingConnectionFactory - Attempting to connect to: [localhost:5672]
2025-07-21 15:28:26.435 [rabbit-simple-18] WARN  [] [] [] [] o.s.a.r.l.SimpleMessageListenerContainer - Consumer raised exception, processing can restart if the connection factory supports it. Exception summary: org.springframework.amqp.AmqpConnectException: java.net.ConnectException: Connection refused: getsockopt
2025-07-21 15:28:26.436 [rabbit-simple-18] INFO  [] [] [] [] o.s.a.r.l.SimpleMessageListenerContainer - Restarting Consumer@9cf8494: tags=[[]], channel=null, acknowledgeMode=AUTO local queue size=0
2025-07-21 15:28:26.437 [rabbit-simple-19] INFO  [] [] [] [] o.s.a.r.c.CachingConnectionFactory - Attempting to connect to: [localhost:5672]
2025-07-21 15:28:26.438 [rabbit-simple-19] ERROR [] [] [] [] o.s.a.r.l.SimpleMessageListenerContainer - Failed to check/redeclare auto-delete queue(s).
2025-07-21 15:28:26.438 [rabbit-simple-19] INFO  [] [] [] [] o.s.a.r.c.CachingConnectionFactory - Attempting to connect to: [localhost:5672]
2025-07-21 15:28:31.538 [rabbit-simple-19] WARN  [] [] [] [] o.s.a.r.l.SimpleMessageListenerContainer - Consumer raised exception, processing can restart if the connection factory supports it. Exception summary: org.springframework.amqp.AmqpConnectException: java.net.ConnectException: Connection refused: getsockopt
2025-07-21 15:28:31.539 [rabbit-simple-19] INFO  [] [] [] [] o.s.a.r.l.SimpleMessageListenerContainer - Restarting Consumer@1cce003f: tags=[[]], channel=null, acknowledgeMode=AUTO local queue size=0
2025-07-21 15:28:31.542 [rabbit-simple-20] INFO  [] [] [] [] o.s.a.r.c.CachingConnectionFactory - Attempting to connect to: [localhost:5672]
2025-07-21 15:28:31.543 [rabbit-simple-20] ERROR [] [] [] [] o.s.a.r.l.SimpleMessageListenerContainer - Failed to check/redeclare auto-delete queue(s).
2025-07-21 15:28:31.543 [rabbit-simple-20] INFO  [] [] [] [] o.s.a.r.c.CachingConnectionFactory - Attempting to connect to: [localhost:5672]
2025-07-21 15:28:33.040 [tomcat-handler-8] DEBUG [] [] [] [] o.s.security.web.FilterChainProxy - Securing POST /graphql
2025-07-21 15:28:33.042 [tomcat-handler-8] WARN  [] [] [] [] c.a.n.service.SimpleJwtService - JWT validation is in testing mode - signature verification disabled
2025-07-21 15:28:33.060 [tomcat-handler-8] DEBUG [] [] [] [] c.a.n.service.SimpleJwtService - JWT token validated successfully for subject: test-client
2025-07-21 15:28:33.061 [tomcat-handler-8] DEBUG [] [] [] [] c.a.n.s.JwtAuthenticationFilter - JWT authentication successful for client: test-client
2025-07-21 15:28:33.063 [tomcat-handler-8] DEBUG [] [] [] [] o.s.security.web.FilterChainProxy - Secured POST /graphql
2025-07-21 15:28:33.063 [tomcat-handler-8] INFO  [graphiql-test-123] [958967e2-8cba-4aca-8aa4-e995371c4751] [] [] c.a.n.config.LoggingConfig - Incoming request - Method: POST, URI: /graphql, RemoteAddr: 0:0:0:0:0:0:0:1, UserAgent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
2025-07-21 15:28:33.173 [tomcat-handler-8] DEBUG [graphiql-test-123] [958967e2-8cba-4aca-8aa4-e995371c4751] [] [graphiql] org.hibernate.SQL - 
    insert 
    into
        notification_audit_logs
        (action, additional_data, correlation_id, details, entity_id, entity_type, request_id, result, source_service, timestamp, user_id) 
    values
        (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
2025-07-21 15:28:33.262 [tomcat-handler-8] INFO  [graphiql-test-123] [958967e2-8cba-4aca-8aa4-e995371c4751] [] [graphiql] c.a.n.config.GraphQLAuditInterceptor - GraphQL operation started - Operation: mutation, Variables: {}, CorrelationId: graphiql-test-123
2025-07-21 15:28:33.262 [tomcat-handler-8] DEBUG [graphiql-test-123] [958967e2-8cba-4aca-8aa4-e995371c4751] [] [graphiql] c.a.n.security.GraphQLJwtInterceptor - Using provided correlation ID: graphiql-test-123
2025-07-21 15:28:33.263 [tomcat-handler-8] DEBUG [graphiql-test-123] [958967e2-8cba-4aca-8aa4-e995371c4751] [] [graphiql] c.a.n.security.GraphQLJwtInterceptor - Allowing GraphiQL request without JWT validation, correlationId: graphiql-test-123
2025-07-21 15:28:33.263 [tomcat-handler-8] DEBUG [graphiql-test-123] [958967e2-8cba-4aca-8aa4-e995371c4751] [] [graphiql] c.a.n.security.GraphQLJwtInterceptor - Using provided correlation ID: graphiql-test-123
2025-07-21 15:28:33.264 [tomcat-handler-8] DEBUG [graphiql-test-123] [958967e2-8cba-4aca-8aa4-e995371c4751] [] [graphiql] c.a.n.security.GraphQLJwtInterceptor - Allowing GraphiQL request without JWT validation, correlationId: graphiql-test-123
2025-07-21 15:28:33.264 [tomcat-handler-8] INFO  [graphiql-test-123] [958967e2-8cba-4aca-8aa4-e995371c4751] [] [graphiql] c.a.n.config.GraphQLConfig - Extracted correlation ID from HTTP request: graphiql-test-123
2025-07-21 15:28:33.264 [tomcat-handler-8] DEBUG [graphiql-test-123] [958967e2-8cba-4aca-8aa4-e995371c4751] [] [graphiql] c.a.n.config.GraphQLConfig - No source service header found, defaulting to 'graphiql'
2025-07-21 15:28:33.264 [tomcat-handler-8] INFO  [graphiql-test-123] [958967e2-8cba-4aca-8aa4-e995371c4751] [] [graphiql] c.a.n.config.GraphQLConfig - GraphQL request intercepted - CorrelationId: graphiql-test-123, RequestId: ae167d74-9d0e-440c-8a3b-3935f0541e65, SourceService: graphiql
2025-07-21 15:28:33.411 [task-1] INFO  [] [] [] [] c.a.n.c.NotificationController - GraphQL mutation - sendNotification called with input: SendNotificationInput(sender=<EMAIL>, type=EMAIL, content=Please find the attached documents.-Anurag, templateName=welcome, messageType=null, importance=null, recipients=null, recipientDetails=[RecipientInput(email=<EMAIL>, type=TO, variables=[VariableInput(key=firstName, value=Deeksha), VariableInput(key=companyName, value=Ascent Business), VariableInput(key=role, value=Developer)], attachments=null), RecipientInput(email=<EMAIL>, type=TO, variables=[VariableInput(key=firstName, value=Anurag), VariableInput(key=companyName, value=Ascent Business), VariableInput(key=role, value=Developer)], attachments=null)], variables=null, commonAttachments=null)
2025-07-21 15:28:33.413 [task-1] INFO  [] [] [] [] c.a.n.c.NotificationController - Captured correlation ID: null, request ID: null for audit logging
2025-07-21 15:28:33.416 [task-1] INFO  [] [] [<EMAIL>] [] c.a.n.service.NotificationService - Processing sendNotification - Sender: <EMAIL>, Type: EMAIL, Content: Please find the attached documents.-Anurag
2025-07-21 15:28:33.417 [task-1] INFO  [] [] [<EMAIL>] [] c.a.n.service.NotificationService - Using new recipient format with 2 detailed recipients
2025-07-21 15:28:33.418 [task-1] INFO  [] [] [<EMAIL>] [] c.a.n.service.NotificationService - Processing sender: <EMAIL>
2025-07-21 15:28:33.426 [tomcat-handler-8] INFO  [graphiql-test-123] [ae167d74-9d0e-440c-8a3b-3935f0541e65] [] [graphiql] c.a.n.config.GraphQLConfig - Set MDC context - CorrelationId: graphiql-test-123, RequestId: ae167d74-9d0e-440c-8a3b-3935f0541e65, SourceService: graphiql
2025-07-21 15:28:33.437 [tomcat-handler-8] INFO  [graphiql-test-123] [ae167d74-9d0e-440c-8a3b-3935f0541e65] [] [graphiql] c.a.n.config.LoggingConfig - Outgoing response - Status: 200, Duration: 374ms, ContentType: null
2025-07-21 15:28:33.437 [tomcat-handler-8] DEBUG [graphiql-test-123] [ae167d74-9d0e-440c-8a3b-3935f0541e65] [] [graphiql] c.a.n.config.LoggingConfig - Preserving MDC context for GraphQL request
2025-07-21 15:28:33.459 [task-1] DEBUG [] [] [<EMAIL>] [] org.hibernate.SQL - 
    insert 
    into
        notifications
        (common_attachments, content, created_date, importance, last_modified_date, message_type, sender_id, template_name, type, variables) 
    values
        (?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
2025-07-21 15:28:33.475 [task-1] INFO  [] [] [<EMAIL>] [] c.a.n.service.NotificationService - Created notification with ID: 128
2025-07-21 15:28:33.476 [task-1] INFO  [] [] [<EMAIL>] [] c.a.n.service.NotificationService - Sending email with enhanced features (attachments/CC/BCC) for notification ID: 128
2025-07-21 15:28:33.477 [task-1] INFO  [] [] [<EMAIL>] [] c.a.n.service.NotificationService - No recipient-specific attachments, sending single email for CC/BCC visibility
2025-07-21 15:28:33.477 [task-1] INFO  [] [] [<EMAIL>] [] c.a.n.service.NotificationService - Sending single email to 2 recipients
2025-07-21 15:28:33.479 [task-1] INFO  [] [] [<EMAIL>] [] c.a.n.service.EmailService - Sending email with proper CC/BCC visibility using template welcome
2025-07-21 15:28:33.480 [task-1] INFO  [] [] [<EMAIL>] [] c.a.n.service.TemplateService - Synchronizing template: welcome
2025-07-21 15:28:33.482 [task-1] DEBUG [] [] [<EMAIL>] [] c.a.n.service.TemplateService - Successfully loaded template 'welcome' from classpath
2025-07-21 15:28:33.545 [task-1] DEBUG [] [] [<EMAIL>] [] org.hibernate.SQL - 
    select
        nt1_0.id,
        nt1_0.alias,
        nt1_0.body,
        nt1_0.created_date,
        nt1_0.last_modified_date,
        nt1_0.subject 
    from
        notification_templates nt1_0 
    where
        nt1_0.alias=?
2025-07-21 15:28:33.574 [task-1] INFO  [] [] [<EMAIL>] [] c.a.n.service.TemplateService - Template 'welcome' content is consistent between classpath and database
2025-07-21 15:28:33.574 [task-1] INFO  [] [] [<EMAIL>] [] c.a.n.service.EmailService - Using synchronized email template: welcome
2025-07-21 15:28:33.655 [task-1] INFO  [] [] [<EMAIL>] [] c.a.n.service.EmailService - Template processed successfully with merged variables
2025-07-21 15:28:33.704 [task-1] INFO  [] [] [<EMAIL>] [] c.a.n.service.EmailService - Added 2 TO recipients: <EMAIL>, <EMAIL>
2025-07-21 15:28:33.704 [task-1] INFO  [] [] [<EMAIL>] [] c.a.n.service.EmailService - Sending <NAME_EMAIL> to 2 total recipients (TO: 2, CC: 0, BCC: 0)
2025-07-21 15:28:35.124 [task-1] INFO  [] [] [<EMAIL>] [] c.a.n.service.EmailService - Email sent successfully with proper CC/BCC visibility - TO recipients can see CC, BCC is hidden from all
2025-07-21 15:28:35.135 [task-1] DEBUG [] [] [<EMAIL>] [] org.hibernate.SQL - 
    insert 
    into
        notification_recipients
        (attachments, created_date, delivered, is_read, last_modified_date, notification_id, recipient_id, recipient_type, variables) 
    values
        (?, ?, ?, ?, ?, ?, ?, ?, ?)
2025-07-21 15:28:35.146 [task-1] DEBUG [] [] [<EMAIL>] [] org.hibernate.SQL - 
    insert 
    into
        notification_recipients
        (attachments, created_date, delivered, is_read, last_modified_date, notification_id, recipient_id, recipient_type, variables) 
    values
        (?, ?, ?, ?, ?, ?, ?, ?, ?)
2025-07-21 15:28:35.152 [task-1] INFO  [] [] [<EMAIL>] [] c.a.n.service.NotificationService - Created 2 notification recipients for tracking with recipient-specific data
2025-07-21 15:28:35.152 [task-1] INFO  [] [] [<EMAIL>] [] c.a.n.service.NotificationService - Single email sent successfully to 2 recipients
2025-07-21 15:28:35.154 [task-1] DEBUG [] [] [<EMAIL>] [] org.hibernate.SQL - 
    insert 
    into
        notification_audit_logs
        (action, additional_data, correlation_id, details, entity_id, entity_type, request_id, result, source_service, timestamp, user_id) 
    values
        (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
2025-07-21 15:28:35.160 [task-1] DEBUG [] [] [<EMAIL>] [] org.hibernate.SQL - 
    insert 
    into
        notification_audit_logs
        (action, additional_data, correlation_id, details, entity_id, entity_type, request_id, result, source_service, timestamp, user_id) 
    values
        (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
2025-07-21 15:28:35.164 [task-1] INFO  [] [] [<EMAIL>] [] c.a.n.service.NotificationService - Successfully sent enhanced email notification for 2 recipients
2025-07-21 15:28:35.178 [task-1] INFO  [] [] [<EMAIL>] [] c.a.n.c.NotificationController - Notification sent successfully: true
2025-07-21 15:28:35.179 [task-1] DEBUG [] [] [<EMAIL>] [] org.hibernate.SQL - 
    insert 
    into
        notification_audit_logs
        (action, additional_data, correlation_id, details, entity_id, entity_type, request_id, result, source_service, timestamp, user_id) 
    values
        (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
2025-07-21 15:28:35.200 [task-1] INFO  [graphiql-test-123] [ae167d74-9d0e-440c-8a3b-3935f0541e65] [] [graphiql] c.a.n.config.GraphQLConfig - GraphQL response processed - CorrelationId: graphiql-test-123, RequestId: ae167d74-9d0e-440c-8a3b-3935f0541e65, SourceService: graphiql
2025-07-21 15:28:35.202 [task-1] INFO  [graphiql-test-123] [ae167d74-9d0e-440c-8a3b-3935f0541e65] [] [graphiql] c.a.n.config.GraphQLAuditInterceptor - GraphQL operation completed - Operation: mutation, Duration: 2084ms, CorrelationId: graphiql-test-123
2025-07-21 15:28:35.204 [task-1] DEBUG [graphiql-test-123] [ae167d74-9d0e-440c-8a3b-3935f0541e65] [] [graphiql] org.hibernate.SQL - 
    insert 
    into
        notification_audit_logs
        (action, additional_data, correlation_id, details, entity_id, entity_type, request_id, result, source_service, timestamp, user_id) 
    values
        (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
2025-07-21 15:28:35.221 [tomcat-handler-9] DEBUG [] [] [] [] o.s.security.web.FilterChainProxy - Securing POST /graphql
2025-07-21 15:28:35.223 [tomcat-handler-9] DEBUG [] [] [] [] o.s.security.web.FilterChainProxy - Secured POST /graphql
2025-07-21 15:28:35.227 [tomcat-handler-9] DEBUG [] [] [] [] o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-07-21 15:28:36.682 [rabbit-simple-20] WARN  [] [] [] [] o.s.a.r.l.SimpleMessageListenerContainer - Consumer raised exception, processing can restart if the connection factory supports it. Exception summary: org.springframework.amqp.AmqpConnectException: java.net.ConnectException: Connection refused: getsockopt
2025-07-21 15:28:36.683 [rabbit-simple-20] INFO  [] [] [] [] o.s.a.r.l.SimpleMessageListenerContainer - Restarting Consumer@3b2b0e16: tags=[[]], channel=null, acknowledgeMode=AUTO local queue size=0
2025-07-21 15:28:36.685 [rabbit-simple-21] INFO  [] [] [] [] o.s.a.r.c.CachingConnectionFactory - Attempting to connect to: [localhost:5672]
2025-07-21 15:28:36.698 [rabbit-simple-21] ERROR [] [] [] [] o.s.a.r.l.SimpleMessageListenerContainer - Failed to check/redeclare auto-delete queue(s).
2025-07-21 15:28:36.699 [rabbit-simple-21] INFO  [] [] [] [] o.s.a.r.c.CachingConnectionFactory - Attempting to connect to: [localhost:5672]
2025-07-21 15:28:41.793 [rabbit-simple-21] WARN  [] [] [] [] o.s.a.r.l.SimpleMessageListenerContainer - Consumer raised exception, processing can restart if the connection factory supports it. Exception summary: org.springframework.amqp.AmqpConnectException: java.net.ConnectException: Connection refused: getsockopt
2025-07-21 15:28:41.794 [rabbit-simple-21] INFO  [] [] [] [] o.s.a.r.l.SimpleMessageListenerContainer - Restarting Consumer@769179d2: tags=[[]], channel=null, acknowledgeMode=AUTO local queue size=0
2025-07-21 15:28:41.795 [rabbit-simple-22] INFO  [] [] [] [] o.s.a.r.c.CachingConnectionFactory - Attempting to connect to: [localhost:5672]
2025-07-21 15:28:41.809 [rabbit-simple-22] ERROR [] [] [] [] o.s.a.r.l.SimpleMessageListenerContainer - Failed to check/redeclare auto-delete queue(s).
2025-07-21 15:28:41.810 [rabbit-simple-22] INFO  [] [] [] [] o.s.a.r.c.CachingConnectionFactory - Attempting to connect to: [localhost:5672]
2025-07-21 15:28:46.950 [rabbit-simple-22] WARN  [] [] [] [] o.s.a.r.l.SimpleMessageListenerContainer - Consumer raised exception, processing can restart if the connection factory supports it. Exception summary: org.springframework.amqp.AmqpConnectException: java.net.ConnectException: Connection refused: getsockopt
2025-07-21 15:28:46.952 [rabbit-simple-22] INFO  [] [] [] [] o.s.a.r.l.SimpleMessageListenerContainer - Restarting Consumer@e214a27: tags=[[]], channel=null, acknowledgeMode=AUTO local queue size=0
2025-07-21 15:28:46.956 [rabbit-simple-23] INFO  [] [] [] [] o.s.a.r.c.CachingConnectionFactory - Attempting to connect to: [localhost:5672]
2025-07-21 15:28:46.981 [rabbit-simple-23] ERROR [] [] [] [] o.s.a.r.l.SimpleMessageListenerContainer - Failed to check/redeclare auto-delete queue(s).
2025-07-21 15:28:46.982 [rabbit-simple-23] INFO  [] [] [] [] o.s.a.r.c.CachingConnectionFactory - Attempting to connect to: [localhost:5672]
2025-07-21 15:28:52.101 [rabbit-simple-23] WARN  [] [] [] [] o.s.a.r.l.SimpleMessageListenerContainer - Consumer raised exception, processing can restart if the connection factory supports it. Exception summary: org.springframework.amqp.AmqpConnectException: java.net.ConnectException: Connection refused: getsockopt
2025-07-21 15:28:52.102 [rabbit-simple-23] INFO  [] [] [] [] o.s.a.r.l.SimpleMessageListenerContainer - Restarting Consumer@55fd3fea: tags=[[]], channel=null, acknowledgeMode=AUTO local queue size=0
2025-07-21 15:28:52.103 [rabbit-simple-24] INFO  [] [] [] [] o.s.a.r.c.CachingConnectionFactory - Attempting to connect to: [localhost:5672]
2025-07-21 15:28:52.115 [rabbit-simple-24] ERROR [] [] [] [] o.s.a.r.l.SimpleMessageListenerContainer - Failed to check/redeclare auto-delete queue(s).
2025-07-21 15:28:52.116 [rabbit-simple-24] INFO  [] [] [] [] o.s.a.r.c.CachingConnectionFactory - Attempting to connect to: [localhost:5672]
2025-07-21 15:28:57.231 [rabbit-simple-24] WARN  [] [] [] [] o.s.a.r.l.SimpleMessageListenerContainer - Consumer raised exception, processing can restart if the connection factory supports it. Exception summary: org.springframework.amqp.AmqpConnectException: java.net.ConnectException: Connection refused: getsockopt
2025-07-21 15:28:57.233 [rabbit-simple-24] INFO  [] [] [] [] o.s.a.r.l.SimpleMessageListenerContainer - Restarting Consumer@576563f1: tags=[[]], channel=null, acknowledgeMode=AUTO local queue size=0
2025-07-21 15:28:57.235 [rabbit-simple-25] INFO  [] [] [] [] o.s.a.r.c.CachingConnectionFactory - Attempting to connect to: [localhost:5672]
2025-07-21 15:28:57.262 [rabbit-simple-25] ERROR [] [] [] [] o.s.a.r.l.SimpleMessageListenerContainer - Failed to check/redeclare auto-delete queue(s).
2025-07-21 15:28:57.263 [rabbit-simple-25] INFO  [] [] [] [] o.s.a.r.c.CachingConnectionFactory - Attempting to connect to: [localhost:5672]
2025-07-21 15:29:02.409 [rabbit-simple-25] WARN  [] [] [] [] o.s.a.r.l.SimpleMessageListenerContainer - Consumer raised exception, processing can restart if the connection factory supports it. Exception summary: org.springframework.amqp.AmqpConnectException: java.net.ConnectException: Connection refused: getsockopt
2025-07-21 15:29:02.411 [rabbit-simple-25] INFO  [] [] [] [] o.s.a.r.l.SimpleMessageListenerContainer - Restarting Consumer@4e0c034a: tags=[[]], channel=null, acknowledgeMode=AUTO local queue size=0
2025-07-21 15:29:02.415 [rabbit-simple-26] INFO  [] [] [] [] o.s.a.r.c.CachingConnectionFactory - Attempting to connect to: [localhost:5672]
2025-07-21 15:29:02.441 [rabbit-simple-26] ERROR [] [] [] [] o.s.a.r.l.SimpleMessageListenerContainer - Failed to check/redeclare auto-delete queue(s).
2025-07-21 15:29:02.442 [rabbit-simple-26] INFO  [] [] [] [] o.s.a.r.c.CachingConnectionFactory - Attempting to connect to: [localhost:5672]
2025-07-21 15:29:05.369 [rabbit-simple-27] INFO  [] [] [] [] o.s.a.r.l.SimpleMessageListenerContainer - Waiting for workers to finish.
2025-07-21 15:29:05.369 [rabbit-simple-27] INFO  [] [] [] [] o.s.a.r.l.SimpleMessageListenerContainer - Successfully waited for workers to finish.
2025-07-21 15:29:05.370 [SpringApplicationShutdownHook] INFO  [] [] [] [] o.s.b.w.e.tomcat.GracefulShutdown - Commencing graceful shutdown. Waiting for active requests to complete
2025-07-21 15:29:05.377 [tomcat-shutdown] INFO  [] [] [] [] o.s.b.w.e.tomcat.GracefulShutdown - Graceful shutdown complete
2025-07-21 15:29:05.504 [SpringApplicationShutdownHook] INFO  [] [] [] [] o.s.o.j.LocalContainerEntityManagerFactoryBean - Closing JPA EntityManagerFactory for persistence unit 'default'
2025-07-21 15:29:05.508 [SpringApplicationShutdownHook] INFO  [] [] [] [] com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Shutdown initiated...
2025-07-21 15:29:05.512 [SpringApplicationShutdownHook] INFO  [] [] [] [] com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Shutdown completed.
