<?xml version="1.0" encoding="UTF-8"?><!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Strict//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-strict.dtd"><html xmlns="http://www.w3.org/1999/xhtml" lang="en"><head><meta http-equiv="Content-Type" content="text/html;charset=UTF-8"/><link rel="stylesheet" href="../jacoco-resources/report.css" type="text/css"/><link rel="shortcut icon" href="../jacoco-resources/report.gif" type="image/gif"/><title>WordConversionConfig.java</title><link rel="stylesheet" href="../jacoco-resources/prettify.css" type="text/css"/><script type="text/javascript" src="../jacoco-resources/prettify.js"></script></head><body onload="window['PR_TAB_WIDTH']=4;prettyPrint()"><div class="breadcrumb" id="breadcrumb"><span class="info"><a href="../jacoco-sessions.html" class="el_session">Sessions</a></span><a href="../index.html" class="el_report">dms-svc</a> &gt; <a href="index.source.html" class="el_package">com.ascentbusiness.dms_svc.config</a> &gt; <span class="el_source">WordConversionConfig.java</span></div><h1>WordConversionConfig.java</h1><pre class="source lang-java linenums">package com.ascentbusiness.dms_svc.config;

import com.ascentbusiness.dms_svc.enums.VirusScannerType;
import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Configuration;

/**
 * Configuration properties for Word to PDF conversion functionality.
 */
<span class="pc bpc" id="L11" title="29 of 32 branches missed.">@Data</span>
@Configuration
@ConfigurationProperties(prefix = &quot;dms.word-conversion&quot;)
public class WordConversionConfig {

    /**
     * Maximum file size for Word conversion in bytes.
     * Default: 52428800 (50MB)
     */
<span class="fc" id="L20">    private long maxFileSize = 52428800L;</span>

    /**
     * Conversion timeout in seconds.
     * Default: 300 (5 minutes)
     */
<span class="fc" id="L26">    private int timeoutSeconds = 300;</span>

    /**
     * Default virus scanner type for Word conversion.
     * Default: MOCK
     */
<span class="fc" id="L32">    private VirusScannerType virusScanner = VirusScannerType.MOCK;</span>

    /**
     * Whether Word conversion feature is enabled.
     * Default: true
     */
<span class="fc" id="L38">    private boolean enabled = true;</span>

    /**
     * Temporary directory for conversion operations.
     * If empty, uses system temp directory.
     */
<span class="fc" id="L44">    private String tempDirectory = &quot;&quot;;</span>

    /**
     * Hours after which temporary files are cleaned up.
     * Default: 24 hours
     */
<span class="fc" id="L50">    private int cleanupAfterHours = 24;</span>

    /**
     * Get formatted file size string for display.
     * 
     * @return formatted file size (e.g., &quot;50.0 MB&quot;, &quot;1.5 GB&quot;)
     */
    public String getMaxFileSizeFormatted() {
<span class="nc bnc" id="L58" title="All 2 branches missed.">        if (maxFileSize &lt; 1024) {</span>
<span class="nc" id="L59">            return maxFileSize + &quot; bytes&quot;;</span>
<span class="nc bnc" id="L60" title="All 2 branches missed.">        } else if (maxFileSize &lt; 1024 * 1024) {</span>
<span class="nc" id="L61">            return String.format(&quot;%.1f KB&quot;, maxFileSize / 1024.0);</span>
<span class="nc bnc" id="L62" title="All 2 branches missed.">        } else if (maxFileSize &lt; 1024 * 1024 * 1024) {</span>
<span class="nc" id="L63">            return String.format(&quot;%.1f MB&quot;, maxFileSize / (1024.0 * 1024.0));</span>
        } else {
<span class="nc" id="L65">            return String.format(&quot;%.1f GB&quot;, maxFileSize / (1024.0 * 1024.0 * 1024.0));</span>
        }
    }
}
</pre><div class="footer"><span class="right">Created with <a href="http://www.jacoco.org/jacoco">JaCoCo</a> 0.8.10.202304240956</span></div></body></html>