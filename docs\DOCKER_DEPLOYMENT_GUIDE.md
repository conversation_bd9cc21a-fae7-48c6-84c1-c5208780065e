# GRC Platform Docker Deployment Guide

This guide explains how to deploy the GRC Platform services using the new shared infrastructure setup.

## 📋 Overview

The GRC Platform consists of two main services:
- **DMS Service** (Document Management System) - Port 9093
- **Notification Service** - Port 9091

Both services can be deployed:
1. **Independently** with their own dependencies
2. **Together** using shared infrastructure
3. In different **environments** (Development, UAT)

## 🏗️ Architecture

### Shared Infrastructure Services
- **MySQL Database** - Shared database with separate schemas
- **Redis Cache** - Shared caching layer
- **RabbitMQ** - Message broker for notifications
- **Elasticsearch** - Search engine for DMS
- **Prometheus** - Metrics collection
- **Grafana** - Monitoring dashboards
- **Zipkin** - Distributed tracing

### Network Configuration
- **Shared Network**: `grc-shared-network` (**********/16)
- **DMS Standalone**: `dms-network` (**********/16)
- **Notification Standalone**: `notification-network`

## 🚀 Deployment Options

### 1. Development Environment (Shared Infrastructure)

```bash
# Load development environment
cp .env.development .env

# Deploy shared infrastructure + both services
./deploy-dev.sh

# Or manually:
docker network create grc-shared-network --driver bridge --subnet=**********/16
docker-compose -f docker-compose.shared.yml up -d
docker-compose -f docker-compose.services.yml up -d
```

**Access URLs:**
- DMS Service: http://localhost:9093
- Notification Service: http://localhost:9091
- Grafana: http://localhost:3000 (admin/admin)
- Prometheus: http://localhost:9090
- RabbitMQ Management: http://localhost:15672 (admin/admin123)

### 2. UAT Environment (AWS EC2)

```bash
# Load UAT environment
cp .env.uat .env

# Configure AWS credentials and environment variables
export AWS_ACCESS_KEY_ID="your-access-key"
export AWS_SECRET_ACCESS_KEY="your-secret-key"
export AWS_REGION="us-east-1"
# ... other required variables

# Deploy to UAT
./deploy-uat.sh

# Or manually:
docker-compose -f docker-compose.shared.yml up -d
docker-compose -f docker-compose.uat.yml up -d
```

### 3. Standalone Service Deployment

```bash
# Deploy only DMS service
./deploy-standalone.sh --service dms --env development

# Deploy only Notification service
./deploy-standalone.sh --service notification --env development

# Deploy both services with shared infrastructure
./deploy-standalone.sh --service both --env development
```

### 4. Individual Service Deployment

```bash
# DMS Service only (with its own dependencies)
cd dms-svc
docker-compose up -d

# Notification Service only (with its own dependencies)
cd notification-svc
docker-compose up -d
```

## 📁 File Structure

```
grc-platform-v4/
├── 📄 docker-compose.shared.yml      # Shared infrastructure services
├── 📄 docker-compose.services.yml    # Application services using shared infra
├── 📄 docker-compose.uat.yml         # UAT-specific configuration
├── 📄 .env.development               # Development environment variables
├── 📄 .env.uat                       # UAT environment variables
├── 📄 deploy-dev.sh                  # Development deployment script
├── 📄 deploy-uat.sh                  # UAT deployment script
├── 📄 deploy-standalone.sh           # Standalone deployment script
├── 📄 backup-uat.sh                  # UAT backup script
├── 📄 clean-docker.sh                # Cleanup script
├── 📁 dms-svc/
│   ├── 📄 docker-compose.yml         # DMS standalone deployment
│   └── 📄 docker-compose.prod.yml    # DMS production config
├── 📁 notification-svc/
│   ├── 📄 docker-compose.yml         # Notification standalone deployment
│   └── 📄 docker-compose.prod.yml    # Notification production config
└── 📁 docker/                        # Shared configurations
    ├── 📁 mysql/shared-init/          # Database initialization
    ├── 📁 prometheus/                 # Monitoring configs
    └── 📁 grafana/                    # Dashboard configs
```

## 🔧 Environment Variables

### Development (.env.development)
- Pre-configured for local development
- Uses default passwords and settings
- All services on standard ports

### UAT (.env.uat)
- Requires AWS credentials
- Uses S3 for file storage
- Production-like resource limits
- Configurable via environment variables

### Required UAT Variables
```bash
# AWS Configuration
AWS_ACCESS_KEY_ID=your-access-key
AWS_SECRET_ACCESS_KEY=your-secret-key
AWS_REGION=us-east-1
S3_BUCKET_NAME=your-s3-bucket

# Database Credentials
MYSQL_ROOT_PASSWORD=secure-password
DMS_DB_PASSWORD=secure-password
NOTIFICATION_DB_PASSWORD=secure-password

# Security
JWT_SECRET=your-jwt-secret
GRAFANA_ADMIN_PASSWORD=secure-password
REDIS_PASSWORD=secure-password
RABBITMQ_PASSWORD=secure-password
```

## 🔍 Monitoring & Health Checks

### Health Check Endpoints
- DMS Service: `http://localhost:9093/actuator/health`
- Notification Service: `http://localhost:9091/actuator/health`

### Monitoring Stack
- **Grafana**: Pre-configured dashboards for both services
- **Prometheus**: Metrics collection from all services
- **Zipkin**: Distributed tracing for request flows

### Log Management
```bash
# View all logs
docker-compose -f docker-compose.shared.yml -f docker-compose.services.yml logs -f

# View specific service logs
docker-compose -f docker-compose.shared.yml -f docker-compose.services.yml logs -f dms-svc
docker-compose -f docker-compose.shared.yml -f docker-compose.services.yml logs -f notification-svc
```

## 💾 Backup & Recovery

### UAT Backup
```bash
# Create backup
./backup-uat.sh

# Backup includes:
# - MySQL databases
# - Redis data
# - RabbitMQ configuration
# - Application logs
```

### Manual Backup
```bash
# Database backup
docker exec grc-mysql-shared mysqldump -u root -p[password] --all-databases > backup.sql

# Redis backup
docker exec grc-redis-shared redis-cli -a [password] --rdb /data/dump.rdb
```

## 🛑 Stopping Services

```bash
# Stop shared infrastructure deployment
docker-compose -f docker-compose.shared.yml -f docker-compose.services.yml down

# Stop UAT deployment
docker-compose -f docker-compose.shared.yml -f docker-compose.uat.yml down

# Stop standalone services
docker-compose -f dms-svc/docker-compose.yml down
docker-compose -f notification-svc/docker-compose.yml down

# Complete cleanup (removes volumes)
./clean-docker.sh
```

## 🔧 Troubleshooting

### Common Issues

1. **Port Conflicts**
   - Check if ports are already in use: `netstat -an | grep :9093`
   - Modify port mappings in environment files

2. **Network Issues**
   - Ensure Docker network exists: `docker network ls`
   - Recreate network: `docker network rm grc-shared-network && docker network create grc-shared-network --driver bridge --subnet=**********/16`

3. **Service Dependencies**
   - Check service health: `docker-compose ps`
   - View service logs: `docker-compose logs [service-name]`

4. **Database Connection Issues**
   - Verify MySQL is running: `docker exec grc-mysql-shared mysqladmin ping`
   - Check database creation: `docker exec grc-mysql-shared mysql -u root -p -e "SHOW DATABASES;"`

### Performance Tuning

1. **Resource Limits**
   - Adjust memory limits in UAT configuration
   - Monitor resource usage: `docker stats`

2. **Database Optimization**
   - Tune MySQL configuration for production workloads
   - Monitor slow queries

3. **Cache Configuration**
   - Adjust Redis memory limits
   - Configure cache eviction policies

## 📞 Support

For issues or questions:
1. Check service logs first
2. Verify environment configuration
3. Ensure all required environment variables are set
4. Check network connectivity between services