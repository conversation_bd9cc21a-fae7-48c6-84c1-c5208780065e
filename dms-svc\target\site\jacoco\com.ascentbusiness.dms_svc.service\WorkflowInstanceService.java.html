<?xml version="1.0" encoding="UTF-8"?><!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Strict//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-strict.dtd"><html xmlns="http://www.w3.org/1999/xhtml" lang="en"><head><meta http-equiv="Content-Type" content="text/html;charset=UTF-8"/><link rel="stylesheet" href="../jacoco-resources/report.css" type="text/css"/><link rel="shortcut icon" href="../jacoco-resources/report.gif" type="image/gif"/><title>WorkflowInstanceService.java</title><link rel="stylesheet" href="../jacoco-resources/prettify.css" type="text/css"/><script type="text/javascript" src="../jacoco-resources/prettify.js"></script></head><body onload="window['PR_TAB_WIDTH']=4;prettyPrint()"><div class="breadcrumb" id="breadcrumb"><span class="info"><a href="../jacoco-sessions.html" class="el_session">Sessions</a></span><a href="../index.html" class="el_report">dms-svc</a> &gt; <a href="index.source.html" class="el_package">com.ascentbusiness.dms_svc.service</a> &gt; <span class="el_source">WorkflowInstanceService.java</span></div><h1>WorkflowInstanceService.java</h1><pre class="source lang-java linenums">package com.ascentbusiness.dms_svc.service;

import com.ascentbusiness.dms_svc.entity.*;
import com.ascentbusiness.dms_svc.enums.WorkflowStatus;
import com.ascentbusiness.dms_svc.enums.TaskStatus;
import com.ascentbusiness.dms_svc.repository.WorkflowInstanceRepository;
import com.ascentbusiness.dms_svc.repository.DocumentRepository;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.util.List;
import java.util.UUID;

/**
 * Service for managing workflow instances
 */
@Service
<span class="fc" id="L23">@RequiredArgsConstructor</span>
<span class="fc" id="L24">@Slf4j</span>
@Transactional
public class WorkflowInstanceService {

    private final WorkflowInstanceRepository workflowInstanceRepository;
    private final DocumentRepository documentRepository;
    private final WorkflowDefinitionService workflowDefinitionService;
    private final WorkflowTaskService workflowTaskService;
    private final WorkflowHistoryService workflowHistoryService;
    private final WorkflowNotificationService workflowNotificationService;
    private final AuditService auditService;

    /**
     * Start a new workflow instance for a document
     */
    public WorkflowInstance startWorkflow(Long documentId, Long workflowDefinitionId, String initiatorUserId) {
<span class="nc" id="L40">        log.info(&quot;Starting workflow for document ID: {} with definition ID: {} by user: {}&quot;, </span>
                documentId, workflowDefinitionId, initiatorUserId);
        
        // Get document and workflow definition
<span class="nc" id="L44">        Document document = documentRepository.findById(documentId)</span>
<span class="nc" id="L45">                .orElseThrow(() -&gt; new RuntimeException(&quot;Document not found with ID: &quot; + documentId));</span>
        
<span class="nc" id="L47">        WorkflowDefinition workflowDefinition = workflowDefinitionService.getWorkflowDefinitionById(workflowDefinitionId);</span>
        
        // Check if document already has an active workflow
<span class="nc bnc" id="L50" title="All 2 branches missed.">        if (hasActiveWorkflow(documentId)) {</span>
<span class="nc" id="L51">            throw new RuntimeException(&quot;Document already has an active workflow&quot;);</span>
        }
        
        // Create workflow instance
<span class="nc" id="L55">        WorkflowInstance instance = WorkflowInstance.builder()</span>
<span class="nc" id="L56">                .workflowDefinition(workflowDefinition)</span>
<span class="nc" id="L57">                .document(document)</span>
<span class="nc" id="L58">                .instanceName(generateInstanceName(document, workflowDefinition))</span>
<span class="nc" id="L59">                .status(WorkflowStatus.PENDING)</span>
<span class="nc" id="L60">                .priority(&quot;MEDIUM&quot;)</span>
<span class="nc" id="L61">                .startedDate(LocalDateTime.now())</span>
<span class="nc" id="L62">                .initiatorUserId(initiatorUserId)</span>
<span class="nc" id="L63">                .correlationId(UUID.randomUUID().toString())</span>
<span class="nc" id="L64">                .build();</span>
        
        // Calculate due date
<span class="nc bnc" id="L67" title="All 2 branches missed.">        if (workflowDefinition.getTimeoutHours() != null) {</span>
<span class="nc" id="L68">            instance.setDueDate(LocalDateTime.now().plusHours(workflowDefinition.getTimeoutHours()));</span>
        }
        
<span class="nc" id="L71">        WorkflowInstance saved = workflowInstanceRepository.save(instance);</span>
        
        // Update document workflow status
<span class="nc" id="L74">        document.startWorkflow(saved);</span>
<span class="nc" id="L75">        documentRepository.save(document);</span>
        
        // Create workflow history entry
<span class="nc" id="L78">        workflowHistoryService.logWorkflowStarted(saved, initiatorUserId);</span>
        
        // Start the first stage
<span class="nc" id="L81">        startFirstStage(saved);</span>
        
        // Create audit log
<span class="nc" id="L84">        auditService.logWorkflowStarted(saved, initiatorUserId);</span>
        
<span class="nc" id="L86">        log.info(&quot;Started workflow instance with ID: {}&quot;, saved.getId());</span>
<span class="nc" id="L87">        return saved;</span>
    }

    /**
     * Complete a workflow instance
     */
    public WorkflowInstance completeWorkflow(Long instanceId, String completionReason, String completedBy) {
<span class="nc" id="L94">        log.info(&quot;Completing workflow instance ID: {} by user: {}&quot;, instanceId, completedBy);</span>
        
<span class="nc" id="L96">        WorkflowInstance instance = getWorkflowInstanceById(instanceId);</span>
        
<span class="nc bnc" id="L98" title="All 2 branches missed.">        if (!instance.isActive()) {</span>
<span class="nc" id="L99">            throw new RuntimeException(&quot;Workflow instance is not active&quot;);</span>
        }
        
        // Update instance status
<span class="nc" id="L103">        instance.setStatus(WorkflowStatus.COMPLETED);</span>
<span class="nc" id="L104">        instance.setCompletedDate(LocalDateTime.now());</span>
<span class="nc" id="L105">        instance.setCompletionReason(completionReason);</span>
        
<span class="nc" id="L107">        WorkflowInstance saved = workflowInstanceRepository.save(instance);</span>
        
        // Update document workflow status
<span class="nc" id="L110">        Document document = instance.getDocument();</span>
<span class="nc" id="L111">        document.completeWorkflow(completionReason);</span>
<span class="nc" id="L112">        documentRepository.save(document);</span>
        
        // Cancel any pending tasks
<span class="nc" id="L115">        workflowTaskService.cancelPendingTasksForInstance(instanceId);</span>
        
        // Create workflow history entry
<span class="nc" id="L118">        workflowHistoryService.logWorkflowCompleted(saved, completedBy, completionReason);</span>
        
        // Send completion notifications
<span class="nc" id="L121">        workflowNotificationService.sendWorkflowCompletionNotification(saved);</span>
        
        // Create audit log
<span class="nc" id="L124">        auditService.logWorkflowCompleted(saved, completedBy);</span>
        
<span class="nc" id="L126">        log.info(&quot;Completed workflow instance ID: {}&quot;, saved.getId());</span>
<span class="nc" id="L127">        return saved;</span>
    }

    /**
     * Cancel a workflow instance
     */
    public WorkflowInstance cancelWorkflow(Long instanceId, String cancellationReason, String cancelledBy) {
<span class="nc" id="L134">        log.info(&quot;Cancelling workflow instance ID: {} by user: {}&quot;, instanceId, cancelledBy);</span>
        
<span class="nc" id="L136">        WorkflowInstance instance = getWorkflowInstanceById(instanceId);</span>
        
<span class="nc bnc" id="L138" title="All 2 branches missed.">        if (!instance.isActive()) {</span>
<span class="nc" id="L139">            throw new RuntimeException(&quot;Workflow instance is not active&quot;);</span>
        }
        
        // Update instance status
<span class="nc" id="L143">        instance.setStatus(WorkflowStatus.CANCELLED);</span>
<span class="nc" id="L144">        instance.setCompletedDate(LocalDateTime.now());</span>
<span class="nc" id="L145">        instance.setCompletionReason(cancellationReason);</span>
        
<span class="nc" id="L147">        WorkflowInstance saved = workflowInstanceRepository.save(instance);</span>
        
        // Update document workflow status
<span class="nc" id="L150">        Document document = instance.getDocument();</span>
<span class="nc" id="L151">        document.cancelWorkflow(cancellationReason);</span>
<span class="nc" id="L152">        documentRepository.save(document);</span>
        
        // Cancel any pending tasks
<span class="nc" id="L155">        workflowTaskService.cancelPendingTasksForInstance(instanceId);</span>
        
        // Create workflow history entry
<span class="nc" id="L158">        workflowHistoryService.logWorkflowCancelled(saved, cancelledBy, cancellationReason);</span>
        
        // Create audit log
<span class="nc" id="L161">        auditService.logWorkflowCancelled(saved, cancelledBy);</span>
        
<span class="nc" id="L163">        log.info(&quot;Cancelled workflow instance ID: {}&quot;, saved.getId());</span>
<span class="nc" id="L164">        return saved;</span>
    }

    /**
     * Get workflow instance by ID
     */
    @Transactional(readOnly = true)
    public WorkflowInstance getWorkflowInstanceById(Long id) {
<span class="nc" id="L172">        return workflowInstanceRepository.findById(id)</span>
<span class="nc" id="L173">                .orElseThrow(() -&gt; new RuntimeException(&quot;Workflow instance not found with ID: &quot; + id));</span>
    }

    /**
     * Get workflow instances for a document
     */
    @Transactional(readOnly = true)
    public List&lt;WorkflowInstance&gt; getWorkflowInstancesForDocument(Long documentId) {
<span class="nc" id="L181">        return workflowInstanceRepository.findByDocumentId(documentId);</span>
    }

    /**
     * Get active workflow instance for a document
     */
    @Transactional(readOnly = true)
    public WorkflowInstance getActiveWorkflowForDocument(Long documentId) {
<span class="nc" id="L189">        List&lt;WorkflowStatus&gt; activeStatuses = List.of(WorkflowStatus.PENDING, WorkflowStatus.IN_PROGRESS);</span>
<span class="nc" id="L190">        return workflowInstanceRepository.findByDocumentIdAndStatusIn(documentId, activeStatuses)</span>
<span class="nc" id="L191">                .orElse(null);</span>
    }

    /**
     * Get workflow instances by status
     */
    @Transactional(readOnly = true)
    public List&lt;WorkflowInstance&gt; getWorkflowInstancesByStatus(WorkflowStatus status) {
<span class="nc" id="L199">        return workflowInstanceRepository.findByStatus(status);</span>
    }

    /**
     * Get workflow instances by initiator
     */
    @Transactional(readOnly = true)
    public Page&lt;WorkflowInstance&gt; getWorkflowInstancesByInitiator(String initiatorUserId, Pageable pageable) {
<span class="nc" id="L207">        return workflowInstanceRepository.findByInitiatorUserId(initiatorUserId, pageable);</span>
    }

    /**
     * Get overdue workflow instances
     */
    @Transactional(readOnly = true)
    public List&lt;WorkflowInstance&gt; getOverdueWorkflowInstances() {
<span class="nc" id="L215">        List&lt;WorkflowStatus&gt; activeStatuses = List.of(WorkflowStatus.PENDING, WorkflowStatus.IN_PROGRESS);</span>
<span class="nc" id="L216">        return workflowInstanceRepository.findOverdueInstances(LocalDateTime.now(), activeStatuses);</span>
    }

    /**
     * Get workflow instances due soon
     */
    @Transactional(readOnly = true)
    public List&lt;WorkflowInstance&gt; getWorkflowInstancesDueSoon(int hoursAhead) {
<span class="nc" id="L224">        List&lt;WorkflowStatus&gt; activeStatuses = List.of(WorkflowStatus.PENDING, WorkflowStatus.IN_PROGRESS);</span>
<span class="nc" id="L225">        LocalDateTime currentTime = LocalDateTime.now();</span>
<span class="nc" id="L226">        LocalDateTime dueSoonTime = currentTime.plusHours(hoursAhead);</span>
<span class="nc" id="L227">        return workflowInstanceRepository.findInstancesDueSoon(currentTime, dueSoonTime, activeStatuses);</span>
    }

    /**
     * Advance workflow to next stage
     */
    public WorkflowInstance advanceToNextStage(Long instanceId, String advancedBy) {
<span class="nc" id="L234">        log.info(&quot;Advancing workflow instance ID: {} to next stage by user: {}&quot;, instanceId, advancedBy);</span>
        
<span class="nc" id="L236">        WorkflowInstance instance = getWorkflowInstanceById(instanceId);</span>
        
<span class="nc bnc" id="L238" title="All 2 branches missed.">        if (!instance.isActive()) {</span>
<span class="nc" id="L239">            throw new RuntimeException(&quot;Workflow instance is not active&quot;);</span>
        }
        
        // Get next stage based on workflow definition logic
<span class="nc" id="L243">        WorkflowStage nextStage = determineNextStage(instance);</span>
        
<span class="nc bnc" id="L245" title="All 2 branches missed.">        if (nextStage == null) {</span>
            // No more stages, complete the workflow
<span class="nc" id="L247">            return completeWorkflow(instanceId, &quot;All stages completed&quot;, advancedBy);</span>
        }
        
        // Update current stage
<span class="nc" id="L251">        instance.setCurrentStage(nextStage);</span>
<span class="nc" id="L252">        instance.setStatus(WorkflowStatus.IN_PROGRESS);</span>
        
<span class="nc" id="L254">        WorkflowInstance saved = workflowInstanceRepository.save(instance);</span>
        
        // Create tasks for the new stage
<span class="nc" id="L257">        workflowTaskService.createTasksForStage(saved, nextStage);</span>
        
        // Create workflow history entry
<span class="nc" id="L260">        workflowHistoryService.logStageAdvanced(saved, nextStage, advancedBy);</span>
        
<span class="nc" id="L262">        log.info(&quot;Advanced workflow instance ID: {} to stage: {}&quot;, saved.getId(), nextStage.getStageName());</span>
<span class="nc" id="L263">        return saved;</span>
    }

    /**
     * Check if document has active workflow
     */
    @Transactional(readOnly = true)
    public boolean hasActiveWorkflow(Long documentId) {
<span class="nc bnc" id="L271" title="All 2 branches missed.">        return getActiveWorkflowForDocument(documentId) != null;</span>
    }

    /**
     * Get workflow statistics
     */
    @Transactional(readOnly = true)
    public WorkflowStatistics getWorkflowStatistics() {
<span class="nc" id="L279">        List&lt;Object[]&gt; statusStats = workflowInstanceRepository.getWorkflowStatisticsByStatus();</span>
<span class="nc" id="L280">        List&lt;WorkflowStatus&gt; activeStatuses = List.of(WorkflowStatus.PENDING, WorkflowStatus.IN_PROGRESS);</span>
<span class="nc" id="L281">        List&lt;Object[]&gt; priorityStats = workflowInstanceRepository.getWorkflowStatisticsByPriority(activeStatuses);</span>

<span class="nc" id="L283">        return WorkflowStatistics.builder()</span>
<span class="nc" id="L284">                .statusStatistics(statusStats)</span>
<span class="nc" id="L285">                .priorityStatistics(priorityStats)</span>
<span class="nc" id="L286">                .totalActive(workflowInstanceRepository.countByStatus(WorkflowStatus.IN_PROGRESS))</span>
<span class="nc" id="L287">                .totalCompleted(workflowInstanceRepository.countByStatus(WorkflowStatus.COMPLETED))</span>
<span class="nc" id="L288">                .totalCancelled(workflowInstanceRepository.countByStatus(WorkflowStatus.CANCELLED))</span>
<span class="nc" id="L289">                .build();</span>
    }

    /**
     * Start workflow with additional parameters
     */
    public WorkflowInstance startWorkflow(Long documentId, Long workflowDefinitionId, String initiatorUserId,
                                        String priority, LocalDateTime dueDate, String comments) {
<span class="nc" id="L297">        log.info(&quot;Starting workflow for document ID: {} with definition ID: {} by user: {} with priority: {}&quot;,</span>
                documentId, workflowDefinitionId, initiatorUserId, priority);

        // Get document and workflow definition
<span class="nc" id="L301">        Document document = documentRepository.findById(documentId)</span>
<span class="nc" id="L302">                .orElseThrow(() -&gt; new RuntimeException(&quot;Document not found with ID: &quot; + documentId));</span>

<span class="nc" id="L304">        WorkflowDefinition workflowDefinition = workflowDefinitionService.getWorkflowDefinitionById(workflowDefinitionId);</span>

        // Check if document already has an active workflow
<span class="nc bnc" id="L307" title="All 2 branches missed.">        if (hasActiveWorkflow(documentId)) {</span>
<span class="nc" id="L308">            throw new RuntimeException(&quot;Document already has an active workflow&quot;);</span>
        }

        // Create workflow instance
<span class="nc" id="L312">        WorkflowInstance instance = WorkflowInstance.builder()</span>
<span class="nc" id="L313">                .workflowDefinition(workflowDefinition)</span>
<span class="nc" id="L314">                .document(document)</span>
<span class="nc" id="L315">                .instanceName(generateInstanceName(document, workflowDefinition))</span>
<span class="nc" id="L316">                .status(WorkflowStatus.PENDING)</span>
<span class="nc bnc" id="L317" title="All 2 branches missed.">                .priority(priority != null ? priority : &quot;MEDIUM&quot;)</span>
<span class="nc" id="L318">                .startedDate(LocalDateTime.now())</span>
<span class="nc" id="L319">                .dueDate(dueDate)</span>
<span class="nc" id="L320">                .initiatorUserId(initiatorUserId)</span>
<span class="nc" id="L321">                .correlationId(UUID.randomUUID().toString())</span>
<span class="nc" id="L322">                .build();</span>

        // Calculate due date if not provided
<span class="nc bnc" id="L325" title="All 4 branches missed.">        if (dueDate == null &amp;&amp; workflowDefinition.getTimeoutHours() != null) {</span>
<span class="nc" id="L326">            instance.setDueDate(LocalDateTime.now().plusHours(workflowDefinition.getTimeoutHours()));</span>
        }

<span class="nc" id="L329">        WorkflowInstance saved = workflowInstanceRepository.save(instance);</span>

        // Update document workflow status
<span class="nc" id="L332">        document.startWorkflow(saved);</span>
<span class="nc" id="L333">        documentRepository.save(document);</span>

        // Create workflow history entry
<span class="nc" id="L336">        workflowHistoryService.logWorkflowStarted(saved, initiatorUserId);</span>

        // Start the first stage
<span class="nc" id="L339">        startFirstStage(saved);</span>

        // Create audit log
<span class="nc" id="L342">        auditService.logWorkflowStarted(saved, initiatorUserId);</span>

<span class="nc" id="L344">        log.info(&quot;Started workflow instance with ID: {}&quot;, saved.getId());</span>
<span class="nc" id="L345">        return saved;</span>
    }

    /**
     * Get all workflow instances with pagination
     */
    @Transactional(readOnly = true)
    public Page&lt;WorkflowInstance&gt; getWorkflowInstances(Pageable pageable) {
<span class="nc" id="L353">        return workflowInstanceRepository.findAll(pageable);</span>
    }

    /**
     * Get active workflow instances
     */
    @Transactional(readOnly = true)
    public List&lt;WorkflowInstance&gt; getActiveWorkflowInstances() {
<span class="nc" id="L361">        List&lt;WorkflowStatus&gt; activeStatuses = List.of(WorkflowStatus.PENDING, WorkflowStatus.IN_PROGRESS);</span>
<span class="nc" id="L362">        return workflowInstanceRepository.findByStatusIn(activeStatuses);</span>
    }

    /**
     * Get workflow instances for a workflow definition
     */
    @Transactional(readOnly = true)
    public List&lt;WorkflowInstance&gt; getWorkflowInstancesForDefinition(Long workflowDefinitionId) {
<span class="nc" id="L370">        return workflowInstanceRepository.findByWorkflowDefinitionId(workflowDefinitionId);</span>
    }

    /**
     * Get workflow instances due within specified hours
     */
    @Transactional(readOnly = true)
    public List&lt;WorkflowInstance&gt; getWorkflowInstancesDueWithin(int hours) {
<span class="nc" id="L378">        return getWorkflowInstancesDueSoon(hours);</span>
    }

    /**
     * Complete a task and advance workflow if needed
     */
    public WorkflowInstance completeTask(Long taskId, String completedBy, com.ascentbusiness.dms_svc.enums.WorkflowAction action,
                                       String comments, String delegateToUserId) {
<span class="nc" id="L386">        log.info(&quot;Completing task ID: {} by user: {} with action: {}&quot;, taskId, completedBy, action);</span>

        // Complete the task through WorkflowTaskService
<span class="nc" id="L389">        WorkflowTask completedTask = workflowTaskService.completeTask(taskId, completedBy, action, comments, delegateToUserId);</span>

<span class="nc" id="L391">        WorkflowInstance instance = completedTask.getWorkflowInstance();</span>

        // Check if stage is complete and advance if needed
<span class="nc bnc" id="L394" title="All 2 branches missed.">        if (isStageComplete(instance, completedTask.getWorkflowStage())) {</span>
<span class="nc" id="L395">            return advanceToNextStage(instance.getId(), completedBy);</span>
        }

<span class="nc" id="L398">        return instance;</span>
    }

    /**
     * Cancel workflow with reason (alternative signature)
     */
    public WorkflowInstance cancelWorkflowByUser(Long instanceId, String cancelledBy, String reason) {
<span class="nc" id="L405">        return cancelWorkflow(instanceId, reason, cancelledBy);</span>
    }

    /**
     * Suspend workflow
     */
    public WorkflowInstance suspendWorkflow(Long instanceId, String suspendedBy, String reason) {
<span class="nc" id="L412">        log.info(&quot;Suspending workflow instance ID: {} by user: {}&quot;, instanceId, suspendedBy);</span>

<span class="nc" id="L414">        WorkflowInstance instance = getWorkflowInstanceById(instanceId);</span>

<span class="nc bnc" id="L416" title="All 2 branches missed.">        if (!instance.isActive()) {</span>
<span class="nc" id="L417">            throw new RuntimeException(&quot;Workflow instance is not active&quot;);</span>
        }

<span class="nc" id="L420">        instance.setStatus(WorkflowStatus.SUSPENDED);</span>
<span class="nc" id="L421">        WorkflowInstance saved = workflowInstanceRepository.save(instance);</span>

        // Create workflow history entry
<span class="nc" id="L424">        workflowHistoryService.logWorkflowSuspended(saved, suspendedBy, reason);</span>

<span class="nc" id="L426">        log.info(&quot;Suspended workflow instance ID: {}&quot;, saved.getId());</span>
<span class="nc" id="L427">        return saved;</span>
    }

    /**
     * Resume workflow
     */
    public WorkflowInstance resumeWorkflow(Long instanceId, String resumedBy, String reason) {
<span class="nc" id="L434">        log.info(&quot;Resuming workflow instance ID: {} by user: {}&quot;, instanceId, resumedBy);</span>

<span class="nc" id="L436">        WorkflowInstance instance = getWorkflowInstanceById(instanceId);</span>

<span class="nc bnc" id="L438" title="All 2 branches missed.">        if (instance.getStatus() != WorkflowStatus.SUSPENDED) {</span>
<span class="nc" id="L439">            throw new RuntimeException(&quot;Workflow instance is not suspended&quot;);</span>
        }

<span class="nc" id="L442">        instance.setStatus(WorkflowStatus.IN_PROGRESS);</span>
<span class="nc" id="L443">        WorkflowInstance saved = workflowInstanceRepository.save(instance);</span>

        // Create workflow history entry
<span class="nc" id="L446">        workflowHistoryService.logWorkflowResumed(saved, resumedBy, reason);</span>

<span class="nc" id="L448">        log.info(&quot;Resumed workflow instance ID: {}&quot;, saved.getId());</span>
<span class="nc" id="L449">        return saved;</span>
    }

    /**
     * Reassign task
     */
    public WorkflowTask reassignTask(Long taskId, String newAssigneeUserId, String reassignedBy) {
<span class="nc" id="L456">        log.info(&quot;Reassigning task ID: {} to user: {} by: {}&quot;, taskId, newAssigneeUserId, reassignedBy);</span>
<span class="nc" id="L457">        return workflowTaskService.reassignTask(taskId, newAssigneeUserId, reassignedBy);</span>
    }

    /**
     * Escalate task
     */
    public WorkflowTask escalateTask(Long taskId, String escalateToUserId, String escalatedBy, String reason) {
<span class="nc" id="L464">        log.info(&quot;Escalating task ID: {} to user: {} by: {}&quot;, taskId, escalateToUserId, escalatedBy);</span>
<span class="nc" id="L465">        return workflowTaskService.escalateTask(taskId, escalateToUserId, escalatedBy, reason);</span>
    }

    // Private helper methods

    private String generateInstanceName(Document document, WorkflowDefinition workflowDefinition) {
<span class="nc" id="L471">        return String.format(&quot;%s - %s&quot;, workflowDefinition.getName(), document.getName());</span>
    }

    private void startFirstStage(WorkflowInstance instance) {
        // Get the first stage of the workflow definition
<span class="nc" id="L476">        WorkflowStage firstStage = getFirstStage(instance.getWorkflowDefinition());</span>
        
<span class="nc bnc" id="L478" title="All 2 branches missed.">        if (firstStage != null) {</span>
<span class="nc" id="L479">            instance.setCurrentStage(firstStage);</span>
<span class="nc" id="L480">            instance.setStatus(WorkflowStatus.IN_PROGRESS);</span>
<span class="nc" id="L481">            workflowInstanceRepository.save(instance);</span>
            
            // Create tasks for the first stage
<span class="nc" id="L484">            workflowTaskService.createTasksForStage(instance, firstStage);</span>
        }
<span class="nc" id="L486">    }</span>

    private WorkflowStage getFirstStage(WorkflowDefinition workflowDefinition) {
<span class="nc" id="L489">        return workflowDefinition.getStages().stream()</span>
<span class="nc" id="L490">                .min((s1, s2) -&gt; Integer.compare(s1.getStageOrder(), s2.getStageOrder()))</span>
<span class="nc" id="L491">                .orElse(null);</span>
    }

    private WorkflowStage determineNextStage(WorkflowInstance instance) {
<span class="nc" id="L495">        WorkflowStage currentStage = instance.getCurrentStage();</span>
<span class="nc bnc" id="L496" title="All 2 branches missed.">        if (currentStage == null) return null;</span>

<span class="nc" id="L498">        return instance.getWorkflowDefinition().getStages().stream()</span>
<span class="nc bnc" id="L499" title="All 2 branches missed.">                .filter(stage -&gt; stage.getStageOrder() &gt; currentStage.getStageOrder())</span>
<span class="nc" id="L500">                .min((s1, s2) -&gt; Integer.compare(s1.getStageOrder(), s2.getStageOrder()))</span>
<span class="nc" id="L501">                .orElse(null);</span>
    }

    private boolean isStageComplete(WorkflowInstance instance, WorkflowStage stage) {
        // Check if all required tasks for this stage are completed
<span class="nc" id="L506">        List&lt;WorkflowTask&gt; stageTasks = instance.getTasks().stream()</span>
<span class="nc" id="L507">                .filter(task -&gt; task.getWorkflowStage().getId().equals(stage.getId()))</span>
<span class="nc" id="L508">                .toList();</span>

<span class="nc bnc" id="L510" title="All 2 branches missed.">        if (stageTasks.isEmpty()) {</span>
<span class="nc" id="L511">            return true; // No tasks means stage is complete</span>
        }

        // For parallel stages, check if minimum approvals are met
<span class="nc bnc" id="L515" title="All 2 branches missed.">        if (stage.getIsParallel()) {</span>
<span class="nc" id="L516">            long completedTasks = stageTasks.stream()</span>
<span class="nc bnc" id="L517" title="All 2 branches missed.">                    .filter(task -&gt; task.getStatus() == TaskStatus.COMPLETED)</span>
<span class="nc" id="L518">                    .count();</span>

<span class="nc bnc" id="L520" title="All 2 branches missed.">            int minRequired = stage.getMinApprovalsRequired() != null ? stage.getMinApprovalsRequired() : 1;</span>
<span class="nc bnc" id="L521" title="All 2 branches missed.">            return completedTasks &gt;= minRequired;</span>
        } else {
            // For sequential stages, all tasks must be completed
<span class="nc" id="L524">            return stageTasks.stream()</span>
<span class="nc bnc" id="L525" title="All 2 branches missed.">                    .allMatch(task -&gt; task.getStatus() == TaskStatus.COMPLETED);</span>
        }
    }

    // Inner class for workflow statistics
<span class="nc" id="L530">    public static class WorkflowStatistics {</span>
        private List&lt;Object[]&gt; statusStatistics;
        private List&lt;Object[]&gt; priorityStatistics;
        private long totalActive;
        private long totalCompleted;
        private long totalCancelled;
        
        // Builder pattern implementation would go here
        public static WorkflowStatisticsBuilder builder() {
<span class="nc" id="L539">            return new WorkflowStatisticsBuilder();</span>
        }
        
<span class="nc" id="L542">        public static class WorkflowStatisticsBuilder {</span>
            private List&lt;Object[]&gt; statusStatistics;
            private List&lt;Object[]&gt; priorityStatistics;
            private long totalActive;
            private long totalCompleted;
            private long totalCancelled;
            
            public WorkflowStatisticsBuilder statusStatistics(List&lt;Object[]&gt; statusStatistics) {
<span class="nc" id="L550">                this.statusStatistics = statusStatistics;</span>
<span class="nc" id="L551">                return this;</span>
            }
            
            public WorkflowStatisticsBuilder priorityStatistics(List&lt;Object[]&gt; priorityStatistics) {
<span class="nc" id="L555">                this.priorityStatistics = priorityStatistics;</span>
<span class="nc" id="L556">                return this;</span>
            }
            
            public WorkflowStatisticsBuilder totalActive(long totalActive) {
<span class="nc" id="L560">                this.totalActive = totalActive;</span>
<span class="nc" id="L561">                return this;</span>
            }
            
            public WorkflowStatisticsBuilder totalCompleted(long totalCompleted) {
<span class="nc" id="L565">                this.totalCompleted = totalCompleted;</span>
<span class="nc" id="L566">                return this;</span>
            }
            
            public WorkflowStatisticsBuilder totalCancelled(long totalCancelled) {
<span class="nc" id="L570">                this.totalCancelled = totalCancelled;</span>
<span class="nc" id="L571">                return this;</span>
            }
            
            public WorkflowStatistics build() {
<span class="nc" id="L575">                WorkflowStatistics stats = new WorkflowStatistics();</span>
<span class="nc" id="L576">                stats.statusStatistics = this.statusStatistics;</span>
<span class="nc" id="L577">                stats.priorityStatistics = this.priorityStatistics;</span>
<span class="nc" id="L578">                stats.totalActive = this.totalActive;</span>
<span class="nc" id="L579">                stats.totalCompleted = this.totalCompleted;</span>
<span class="nc" id="L580">                stats.totalCancelled = this.totalCancelled;</span>
<span class="nc" id="L581">                return stats;</span>
            }
        }
        
        // Getters
<span class="nc" id="L586">        public List&lt;Object[]&gt; getStatusStatistics() { return statusStatistics; }</span>
<span class="nc" id="L587">        public List&lt;Object[]&gt; getPriorityStatistics() { return priorityStatistics; }</span>
<span class="nc" id="L588">        public long getTotalActive() { return totalActive; }</span>
<span class="nc" id="L589">        public long getTotalCompleted() { return totalCompleted; }</span>
<span class="nc" id="L590">        public long getTotalCancelled() { return totalCancelled; }</span>
    }
}
</pre><div class="footer"><span class="right">Created with <a href="http://www.jacoco.org/jacoco">JaCoCo</a> 0.8.10.202304240956</span></div></body></html>