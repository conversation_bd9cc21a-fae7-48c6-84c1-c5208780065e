<?xml version="1.0" encoding="UTF-8"?><!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Strict//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-strict.dtd"><html xmlns="http://www.w3.org/1999/xhtml" lang="en"><head><meta http-equiv="Content-Type" content="text/html;charset=UTF-8"/><link rel="stylesheet" href="../jacoco-resources/report.css" type="text/css"/><link rel="shortcut icon" href="../jacoco-resources/report.gif" type="image/gif"/><title>AuditGraphQLResolver</title><script type="text/javascript" src="../jacoco-resources/sort.js"></script></head><body onload="initialSort(['breadcrumb'])"><div class="breadcrumb" id="breadcrumb"><span class="info"><a href="../jacoco-sessions.html" class="el_session">Sessions</a></span><a href="../index.html" class="el_report">dms-svc</a> &gt; <a href="index.html" class="el_package">com.ascentbusiness.dms_svc.resolver</a> &gt; <span class="el_class">AuditGraphQLResolver</span></div><h1>AuditGraphQLResolver</h1><table class="coverage" cellspacing="0" id="coveragetable"><thead><tr><td class="sortable" id="a" onclick="toggleSort(this)">Element</td><td class="down sortable bar" id="b" onclick="toggleSort(this)">Missed Instructions</td><td class="sortable ctr2" id="c" onclick="toggleSort(this)">Cov.</td><td class="sortable bar" id="d" onclick="toggleSort(this)">Missed Branches</td><td class="sortable ctr2" id="e" onclick="toggleSort(this)">Cov.</td><td class="sortable ctr1" id="f" onclick="toggleSort(this)">Missed</td><td class="sortable ctr2" id="g" onclick="toggleSort(this)">Cxty</td><td class="sortable ctr1" id="h" onclick="toggleSort(this)">Missed</td><td class="sortable ctr2" id="i" onclick="toggleSort(this)">Lines</td><td class="sortable ctr1" id="j" onclick="toggleSort(this)">Missed</td><td class="sortable ctr2" id="k" onclick="toggleSort(this)">Methods</td></tr></thead><tfoot><tr><td>Total</td><td class="bar">1,355 of 1,380</td><td class="ctr2">1%</td><td class="bar">115 of 115</td><td class="ctr2">0%</td><td class="ctr1">88</td><td class="ctr2">90</td><td class="ctr1">336</td><td class="ctr2">338</td><td class="ctr1">29</td><td class="ctr2">31</td></tr></tfoot><tbody><tr><td id="a6"><a href="AuditGraphQLResolver.java.html#L297" class="el_method">bulkVerifyAuditLogs(List)</a></td><td class="bar" id="b0"><img src="../jacoco-resources/redbar.gif" width="120" height="10" title="181" alt="181"/></td><td class="ctr2" id="c2">0%</td><td class="bar" id="d1"><img src="../jacoco-resources/redbar.gif" width="105" height="10" title="14" alt="14"/></td><td class="ctr2" id="e0">0%</td><td class="ctr1" id="f1">8</td><td class="ctr2" id="g1">8</td><td class="ctr1" id="h0">46</td><td class="ctr2" id="i0">46</td><td class="ctr1" id="j0">1</td><td class="ctr2" id="k0">1</td></tr><tr><td id="a2"><a href="AuditGraphQLResolver.java.html#L500" class="el_method">buildAuditStatistics(LocalDateTime, LocalDateTime)</a></td><td class="bar" id="b1"><img src="../jacoco-resources/redbar.gif" width="73" height="10" title="111" alt="111"/></td><td class="ctr2" id="c3">0%</td><td class="bar" id="d15"/><td class="ctr2" id="e15">n/a</td><td class="ctr1" id="f15">1</td><td class="ctr2" id="g15">1</td><td class="ctr1" id="h1">36</td><td class="ctr2" id="i1">36</td><td class="ctr1" id="j1">1</td><td class="ctr2" id="k1">1</td></tr><tr><td id="a30"><a href="AuditGraphQLResolver.java.html#L249" class="el_method">verifyAuditLog(Long)</a></td><td class="bar" id="b2"><img src="../jacoco-resources/redbar.gif" width="66" height="10" title="100" alt="100"/></td><td class="ctr2" id="c4">0%</td><td class="bar" id="d6"><img src="../jacoco-resources/redbar.gif" width="45" height="10" title="6" alt="6"/></td><td class="ctr2" id="e1">0%</td><td class="ctr1" id="f6">4</td><td class="ctr2" id="g6">4</td><td class="ctr1" id="h2">24</td><td class="ctr2" id="i2">24</td><td class="ctr1" id="j2">1</td><td class="ctr2" id="k2">1</td></tr><tr><td id="a28"><a href="AuditGraphQLResolver.java.html#L209" class="el_method">requestAuditExport(AuditExportRequest)</a></td><td class="bar" id="b3"><img src="../jacoco-resources/redbar.gif" width="56" height="10" title="85" alt="85"/></td><td class="ctr2" id="c5">0%</td><td class="bar" id="d7"><img src="../jacoco-resources/redbar.gif" width="45" height="10" title="6" alt="6"/></td><td class="ctr2" id="e2">0%</td><td class="ctr1" id="f7">4</td><td class="ctr2" id="g7">4</td><td class="ctr1" id="h3">22</td><td class="ctr2" id="i3">22</td><td class="ctr1" id="j3">1</td><td class="ctr2" id="k3">1</td></tr><tr><td id="a12"><a href="AuditGraphQLResolver.java.html#L66" class="el_method">getAuditLogs(AuditGraphQLResolver.AuditLogFilterInput, AuditGraphQLResolver.AuditPaginationInput)</a></td><td class="bar" id="b4"><img src="../jacoco-resources/redbar.gif" width="51" height="10" title="77" alt="77"/></td><td class="ctr2" id="c6">0%</td><td class="bar" id="d2"><img src="../jacoco-resources/redbar.gif" width="105" height="10" title="14" alt="14"/></td><td class="ctr2" id="e3">0%</td><td class="ctr1" id="f2">8</td><td class="ctr2" id="g2">8</td><td class="ctr1" id="h5">14</td><td class="ctr2" id="i5">14</td><td class="ctr1" id="j4">1</td><td class="ctr2" id="k4">1</td></tr><tr><td id="a14"><a href="AuditGraphQLResolver.java.html#L100" class="el_method">getComplianceAuditLogs(AuditGraphQLResolver.ComplianceAuditFilterInput, AuditGraphQLResolver.AuditPaginationInput)</a></td><td class="bar" id="b5"><img src="../jacoco-resources/redbar.gif" width="49" height="10" title="75" alt="75"/></td><td class="ctr2" id="c7">0%</td><td class="bar" id="d3"><img src="../jacoco-resources/redbar.gif" width="90" height="10" title="12" alt="12"/></td><td class="ctr2" id="e4">0%</td><td class="ctr1" id="f4">7</td><td class="ctr2" id="g4">7</td><td class="ctr1" id="h9">13</td><td class="ctr2" id="i9">13</td><td class="ctr1" id="j5">1</td><td class="ctr2" id="k5">1</td></tr><tr><td id="a3"><a href="AuditGraphQLResolver.java.html#L633" class="el_method">buildHourlyActivity()</a></td><td class="bar" id="b6"><img src="../jacoco-resources/redbar.gif" width="46" height="10" title="70" alt="70"/></td><td class="ctr2" id="c8">0%</td><td class="bar" id="d12"><img src="../jacoco-resources/redbar.gif" width="15" height="10" title="2" alt="2"/></td><td class="ctr2" id="e5">0%</td><td class="ctr1" id="f12">2</td><td class="ctr2" id="g12">2</td><td class="ctr1" id="h4">19</td><td class="ctr2" id="i4">19</td><td class="ctr1" id="j6">1</td><td class="ctr2" id="k6">1</td></tr><tr><td id="a16"><a href="AuditGraphQLResolver.java.html#L135" class="el_method">getTamperingDetections(String, Boolean, OffsetDateTime, OffsetDateTime)</a></td><td class="bar" id="b7"><img src="../jacoco-resources/redbar.gif" width="35" height="10" title="54" alt="54"/></td><td class="ctr2" id="c9">0%</td><td class="bar" id="d9"><img src="../jacoco-resources/redbar.gif" width="30" height="10" title="4" alt="4"/></td><td class="ctr2" id="e6">0%</td><td class="ctr1" id="f9">3</td><td class="ctr2" id="g9">3</td><td class="ctr1" id="h20">7</td><td class="ctr2" id="i20">7</td><td class="ctr1" id="j7">1</td><td class="ctr2" id="k7">1</td></tr><tr><td id="a1"><a href="AuditGraphQLResolver.java.html#L567" class="el_method">buildActionStatistics(LocalDateTime, LocalDateTime)</a></td><td class="bar" id="b8"><img src="../jacoco-resources/redbar.gif" width="30" height="10" title="46" alt="46"/></td><td class="ctr2" id="c10">0%</td><td class="bar" id="d16"/><td class="ctr2" id="e16">n/a</td><td class="ctr1" id="f16">1</td><td class="ctr2" id="g16">1</td><td class="ctr1" id="h10">13</td><td class="ctr2" id="i10">13</td><td class="ctr1" id="j8">1</td><td class="ctr2" id="k8">1</td></tr><tr><td id="a7"><a href="AuditGraphQLResolver.java.html#L736" class="el_method">calculateRiskScore(Long)</a></td><td class="bar" id="b9"><img src="../jacoco-resources/redbar.gif" width="29" height="10" title="45" alt="45"/></td><td class="ctr2" id="c11">0%</td><td class="bar" id="d4"><img src="../jacoco-resources/redbar.gif" width="90" height="10" title="12" alt="12"/></td><td class="ctr2" id="e7">0%</td><td class="ctr1" id="f5">7</td><td class="ctr2" id="g5">7</td><td class="ctr1" id="h12">11</td><td class="ctr2" id="i12">11</td><td class="ctr1" id="j9">1</td><td class="ctr2" id="k9">1</td></tr><tr><td id="a4"><a href="AuditGraphQLResolver.java.html#L663" class="el_method">buildTopDocuments(LocalDateTime, LocalDateTime)</a></td><td class="bar" id="b10"><img src="../jacoco-resources/redbar.gif" width="28" height="10" title="43" alt="43"/></td><td class="ctr2" id="c12">0%</td><td class="bar" id="d17"/><td class="ctr2" id="e17">n/a</td><td class="ctr1" id="f17">1</td><td class="ctr2" id="g17">1</td><td class="ctr1" id="h6">14</td><td class="ctr2" id="i6">14</td><td class="ctr1" id="j10">1</td><td class="ctr2" id="k10">1</td></tr><tr><td id="a5"><a href="AuditGraphQLResolver.java.html#L598" class="el_method">buildUserStatistics(LocalDateTime, LocalDateTime)</a></td><td class="bar" id="b11"><img src="../jacoco-resources/redbar.gif" width="27" height="10" title="42" alt="42"/></td><td class="ctr2" id="c13">0%</td><td class="bar" id="d18"/><td class="ctr2" id="e18">n/a</td><td class="ctr1" id="f18">1</td><td class="ctr2" id="g18">1</td><td class="ctr1" id="h11">13</td><td class="ctr2" id="i11">13</td><td class="ctr1" id="j11">1</td><td class="ctr2" id="k11">1</td></tr><tr><td id="a11"><a href="AuditGraphQLResolver.java.html#L385" class="el_method">createPageable(AuditGraphQLResolver.AuditPaginationInput)</a></td><td class="bar" id="b12"><img src="../jacoco-resources/redbar.gif" width="26" height="10" title="40" alt="40"/></td><td class="ctr2" id="c14">0%</td><td class="bar" id="d10"><img src="../jacoco-resources/redbar.gif" width="30" height="10" title="4" alt="4"/></td><td class="ctr2" id="e8">0%</td><td class="ctr1" id="f10">3</td><td class="ctr2" id="g10">3</td><td class="ctr1" id="h13">9</td><td class="ctr2" id="i13">9</td><td class="ctr1" id="j12">1</td><td class="ctr2" id="k12">1</td></tr><tr><td id="a9"><a href="AuditGraphQLResolver.java.html#L554" class="el_method">countRecordsForPeriod(LocalDateTime, LocalDateTime)</a></td><td class="bar" id="b13"><img src="../jacoco-resources/redbar.gif" width="26" height="10" title="40" alt="40"/></td><td class="ctr2" id="c15">0%</td><td class="bar" id="d19"/><td class="ctr2" id="e19">n/a</td><td class="ctr1" id="f19">1</td><td class="ctr2" id="g19">1</td><td class="ctr1" id="h21">7</td><td class="ctr2" id="i21">7</td><td class="ctr1" id="j13">1</td><td class="ctr2" id="k13">1</td></tr><tr><td id="a23"><a href="AuditGraphQLResolver.java.html#L606" class="el_method">lambda$buildUserStatistics$2(Map.Entry)</a></td><td class="bar" id="b14"><img src="../jacoco-resources/redbar.gif" width="25" height="10" title="38" alt="38"/></td><td class="ctr2" id="c16">0%</td><td class="bar" id="d20"/><td class="ctr2" id="e20">n/a</td><td class="ctr1" id="f20">1</td><td class="ctr2" id="g20">1</td><td class="ctr1" id="h7">14</td><td class="ctr2" id="i7">14</td><td class="ctr1" id="j14">1</td><td class="ctr2" id="k14">1</td></tr><tr><td id="a21"><a href="AuditGraphQLResolver.java.html#L672" class="el_method">lambda$buildTopDocuments$6(Map.Entry)</a></td><td class="bar" id="b15"><img src="../jacoco-resources/redbar.gif" width="24" height="10" title="37" alt="37"/></td><td class="ctr2" id="c17">0%</td><td class="bar" id="d21"/><td class="ctr2" id="e21">n/a</td><td class="ctr1" id="f21">1</td><td class="ctr2" id="g21">1</td><td class="ctr1" id="h8">14</td><td class="ctr2" id="i8">14</td><td class="ctr1" id="j15">1</td><td class="ctr2" id="k15">1</td></tr><tr><td id="a25"><a href="AuditGraphQLResolver.java.html#L719" class="el_method">lambda$countComplianceEvents$9(AuditLog)</a></td><td class="bar" id="b16"><img src="../jacoco-resources/redbar.gif" width="23" height="10" title="36" alt="36"/></td><td class="ctr2" id="c18">0%</td><td class="bar" id="d0"><img src="../jacoco-resources/redbar.gif" width="120" height="10" title="16" alt="16"/></td><td class="ctr2" id="e9">0%</td><td class="ctr1" id="f0">9</td><td class="ctr2" id="g0">9</td><td class="ctr1" id="h17">8</td><td class="ctr2" id="i17">8</td><td class="ctr1" id="j16">1</td><td class="ctr2" id="k16">1</td></tr><tr><td id="a17"><a href="AuditGraphQLResolver.java.html#L577" class="el_method">lambda$buildActionStatistics$0(Long, Map.Entry)</a></td><td class="bar" id="b17"><img src="../jacoco-resources/redbar.gif" width="21" height="10" title="33" alt="33"/></td><td class="ctr2" id="c19">0%</td><td class="bar" id="d13"><img src="../jacoco-resources/redbar.gif" width="15" height="10" title="2" alt="2"/></td><td class="ctr2" id="e10">0%</td><td class="ctr1" id="f13">2</td><td class="ctr2" id="g13">2</td><td class="ctr1" id="h18">8</td><td class="ctr2" id="i18">8</td><td class="ctr1" id="j17">1</td><td class="ctr2" id="k17">1</td></tr><tr><td id="a13"><a href="AuditGraphQLResolver.java.html#L160" class="el_method">getAuditStatistics(LocalDateTime, LocalDateTime)</a></td><td class="bar" id="b18"><img src="../jacoco-resources/redbar.gif" width="20" height="10" title="31" alt="31"/></td><td class="ctr2" id="c20">0%</td><td class="bar" id="d11"><img src="../jacoco-resources/redbar.gif" width="30" height="10" title="4" alt="4"/></td><td class="ctr2" id="e11">0%</td><td class="ctr1" id="f11">3</td><td class="ctr2" id="g11">3</td><td class="ctr1" id="h14">9</td><td class="ctr2" id="i14">9</td><td class="ctr1" id="j18">1</td><td class="ctr2" id="k18">1</td></tr><tr><td id="a27"><a href="AuditGraphQLResolver.java.html#L371" class="el_method">mapVerificationStatusToGraphQL(String, boolean)</a></td><td class="bar" id="b19"><img src="../jacoco-resources/redbar.gif" width="20" height="10" title="31" alt="31"/></td><td class="ctr2" id="c21">0%</td><td class="bar" id="d5"><img src="../jacoco-resources/redbar.gif" width="82" height="10" title="11" alt="11"/></td><td class="ctr2" id="e12">0%</td><td class="ctr1" id="f3">8</td><td class="ctr2" id="g3">8</td><td class="ctr1" id="h19">8</td><td class="ctr2" id="i19">8</td><td class="ctr1" id="j19">1</td><td class="ctr2" id="k19">1</td></tr><tr><td id="a15"><a href="AuditGraphQLResolver.java.html#L187" class="el_method">getDocumentAuditTrail(Long, AuditGraphQLResolver.AuditPaginationInput)</a></td><td class="bar" id="b20"><img src="../jacoco-resources/redbar.gif" width="19" height="10" title="30" alt="30"/></td><td class="ctr2" id="c22">0%</td><td class="bar" id="d22"/><td class="ctr2" id="e22">n/a</td><td class="ctr1" id="f22">1</td><td class="ctr2" id="g22">1</td><td class="ctr1" id="h22">7</td><td class="ctr2" id="i22">7</td><td class="ctr1" id="j20">1</td><td class="ctr2" id="k20">1</td></tr><tr><td id="a10"><a href="AuditGraphQLResolver.java.html#L699" class="el_method">countSecurityEvents(LocalDateTime, LocalDateTime)</a></td><td class="bar" id="b21"><img src="../jacoco-resources/redbar.gif" width="19" height="10" title="29" alt="29"/></td><td class="ctr2" id="c23">0%</td><td class="bar" id="d23"/><td class="ctr2" id="e23">n/a</td><td class="ctr1" id="f23">1</td><td class="ctr2" id="g23">1</td><td class="ctr1" id="h15">9</td><td class="ctr2" id="i15">9</td><td class="ctr1" id="j21">1</td><td class="ctr2" id="k21">1</td></tr><tr><td id="a8"><a href="AuditGraphQLResolver.java.html#L715" class="el_method">countComplianceEvents(LocalDateTime, LocalDateTime)</a></td><td class="bar" id="b22"><img src="../jacoco-resources/redbar.gif" width="19" height="10" title="29" alt="29"/></td><td class="ctr2" id="c24">0%</td><td class="bar" id="d24"/><td class="ctr2" id="e24">n/a</td><td class="ctr1" id="f24">1</td><td class="ctr2" id="g24">1</td><td class="ctr1" id="h16">9</td><td class="ctr2" id="i16">9</td><td class="ctr1" id="j22">1</td><td class="ctr2" id="k22">1</td></tr><tr><td id="a26"><a href="AuditGraphQLResolver.java.html#L703" class="el_method">lambda$countSecurityEvents$8(AuditLog)</a></td><td class="bar" id="b23"><img src="../jacoco-resources/redbar.gif" width="10" height="10" title="16" alt="16"/></td><td class="ctr2" id="c25">0%</td><td class="bar" id="d8"><img src="../jacoco-resources/redbar.gif" width="45" height="10" title="6" alt="6"/></td><td class="ctr2" id="e13">0%</td><td class="ctr1" id="f8">4</td><td class="ctr2" id="g8">4</td><td class="ctr1" id="h23">3</td><td class="ctr2" id="i23">3</td><td class="ctr1" id="j23">1</td><td class="ctr2" id="k23">1</td></tr><tr><td id="a22"><a href="AuditGraphQLResolver.java.html#L688" class="el_method">lambda$buildTopDocuments$7(DocumentActivityCount, DocumentActivityCount)</a></td><td class="bar" id="b24"><img src="../jacoco-resources/redbar.gif" width="5" height="10" title="8" alt="8"/></td><td class="ctr2" id="c26">0%</td><td class="bar" id="d25"/><td class="ctr2" id="e25">n/a</td><td class="ctr1" id="f25">1</td><td class="ctr2" id="g25">1</td><td class="ctr1" id="h24">1</td><td class="ctr2" id="i24">1</td><td class="ctr1" id="j24">1</td><td class="ctr2" id="k24">1</td></tr><tr><td id="a24"><a href="AuditGraphQLResolver.java.html#L622" class="el_method">lambda$buildUserStatistics$3(UserActivityCount, UserActivityCount)</a></td><td class="bar" id="b25"><img src="../jacoco-resources/redbar.gif" width="5" height="10" title="8" alt="8"/></td><td class="ctr2" id="c27">0%</td><td class="bar" id="d26"/><td class="ctr2" id="e26">n/a</td><td class="ctr1" id="f26">1</td><td class="ctr2" id="g26">1</td><td class="ctr1" id="h25">1</td><td class="ctr2" id="i25">1</td><td class="ctr1" id="j25">1</td><td class="ctr2" id="k25">1</td></tr><tr><td id="a18"><a href="AuditGraphQLResolver.java.html#L587" class="el_method">lambda$buildActionStatistics$1(ActionCount, ActionCount)</a></td><td class="bar" id="b26"><img src="../jacoco-resources/redbar.gif" width="5" height="10" title="8" alt="8"/></td><td class="ctr2" id="c28">0%</td><td class="bar" id="d27"/><td class="ctr2" id="e27">n/a</td><td class="ctr1" id="f27">1</td><td class="ctr2" id="g27">1</td><td class="ctr1" id="h26">1</td><td class="ctr2" id="i26">1</td><td class="ctr1" id="j26">1</td><td class="ctr2" id="k26">1</td></tr><tr><td id="a20"><a href="AuditGraphQLResolver.java.html#L667" class="el_method">lambda$buildTopDocuments$5(AuditLog)</a></td><td class="bar" id="b27"><img src="../jacoco-resources/redbar.gif" width="4" height="10" title="7" alt="7"/></td><td class="ctr2" id="c29">0%</td><td class="bar" id="d14"><img src="../jacoco-resources/redbar.gif" width="15" height="10" title="2" alt="2"/></td><td class="ctr2" id="e14">0%</td><td class="ctr1" id="f14">2</td><td class="ctr2" id="g14">2</td><td class="ctr1" id="h27">1</td><td class="ctr2" id="i27">1</td><td class="ctr1" id="j27">1</td><td class="ctr2" id="k27">1</td></tr><tr><td id="a19"><a href="AuditGraphQLResolver.java.html#L641" class="el_method">lambda$buildHourlyActivity$4(AuditLog)</a></td><td class="bar" id="b28"><img src="../jacoco-resources/redbar.gif" width="3" height="10" title="5" alt="5"/></td><td class="ctr2" id="c30">0%</td><td class="bar" id="d28"/><td class="ctr2" id="e28">n/a</td><td class="ctr1" id="f28">1</td><td class="ctr2" id="g28">1</td><td class="ctr1" id="h28">1</td><td class="ctr2" id="i28">1</td><td class="ctr1" id="j28">1</td><td class="ctr2" id="k28">1</td></tr><tr><td id="a0"><a href="AuditGraphQLResolver.java.html#L43" class="el_method">AuditGraphQLResolver(AuditService, AuditExportService, AuditVerificationService, AuditLogRepository, AuditVerificationLogRepository, UserContext)</a></td><td class="bar" id="b29"><img src="../jacoco-resources/greenbar.gif" width="13" height="10" title="21" alt="21"/></td><td class="ctr2" id="c0">100%</td><td class="bar" id="d29"/><td class="ctr2" id="e29">n/a</td><td class="ctr1" id="f29">0</td><td class="ctr2" id="g29">1</td><td class="ctr1" id="h29">0</td><td class="ctr2" id="i29">1</td><td class="ctr1" id="j29">0</td><td class="ctr2" id="k29">1</td></tr><tr><td id="a29"><a href="AuditGraphQLResolver.java.html#L44" class="el_method">static {...}</a></td><td class="bar" id="b30"><img src="../jacoco-resources/greenbar.gif" width="2" height="10" title="4" alt="4"/></td><td class="ctr2" id="c1">100%</td><td class="bar" id="d30"/><td class="ctr2" id="e30">n/a</td><td class="ctr1" id="f30">0</td><td class="ctr2" id="g30">1</td><td class="ctr1" id="h30">0</td><td class="ctr2" id="i30">1</td><td class="ctr1" id="j30">0</td><td class="ctr2" id="k30">1</td></tr></tbody></table><div class="footer"><span class="right">Created with <a href="http://www.jacoco.org/jacoco">JaCoCo</a> 0.8.10.202304240956</span></div></body></html>