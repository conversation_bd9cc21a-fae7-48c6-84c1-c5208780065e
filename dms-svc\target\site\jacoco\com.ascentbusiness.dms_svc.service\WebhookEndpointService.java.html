<?xml version="1.0" encoding="UTF-8"?><!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Strict//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-strict.dtd"><html xmlns="http://www.w3.org/1999/xhtml" lang="en"><head><meta http-equiv="Content-Type" content="text/html;charset=UTF-8"/><link rel="stylesheet" href="../jacoco-resources/report.css" type="text/css"/><link rel="shortcut icon" href="../jacoco-resources/report.gif" type="image/gif"/><title>WebhookEndpointService.java</title><link rel="stylesheet" href="../jacoco-resources/prettify.css" type="text/css"/><script type="text/javascript" src="../jacoco-resources/prettify.js"></script></head><body onload="window['PR_TAB_WIDTH']=4;prettyPrint()"><div class="breadcrumb" id="breadcrumb"><span class="info"><a href="../jacoco-sessions.html" class="el_session">Sessions</a></span><a href="../index.html" class="el_report">dms-svc</a> &gt; <a href="index.source.html" class="el_package">com.ascentbusiness.dms_svc.service</a> &gt; <span class="el_source">WebhookEndpointService.java</span></div><h1>WebhookEndpointService.java</h1><pre class="source lang-java linenums">package com.ascentbusiness.dms_svc.service;

import com.ascentbusiness.dms_svc.entity.WebhookEndpoint;
import com.ascentbusiness.dms_svc.entity.WebhookDelivery;
import com.ascentbusiness.dms_svc.enums.DeliveryStatus;
import com.ascentbusiness.dms_svc.repository.WebhookEndpointRepository;
import com.ascentbusiness.dms_svc.repository.WebhookDeliveryRepository;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Optional;

/**
 * Service for managing webhook endpoints
 */
@Service
<span class="fc" id="L23">@RequiredArgsConstructor</span>
<span class="fc" id="L24">@Slf4j</span>
@Transactional
public class WebhookEndpointService {

    private final WebhookEndpointRepository webhookEndpointRepository;
    private final WebhookDeliveryRepository webhookDeliveryRepository;
    private final AuditService auditService;

    /**
     * Create a new webhook endpoint
     */
    public WebhookEndpoint createWebhookEndpoint(WebhookEndpoint webhookEndpoint, String createdBy) {
<span class="nc" id="L36">        log.info(&quot;Creating webhook endpoint: {} by user: {}&quot;, webhookEndpoint.getName(), createdBy);</span>
        
        // Validate webhook endpoint
<span class="nc" id="L39">        validateWebhookEndpoint(webhookEndpoint);</span>
        
        // Check for duplicate URL
<span class="nc bnc" id="L42" title="All 2 branches missed.">        if (webhookEndpointRepository.existsByUrl(webhookEndpoint.getUrl())) {</span>
<span class="nc" id="L43">            throw new RuntimeException(&quot;Webhook endpoint with URL '&quot; + webhookEndpoint.getUrl() + &quot;' already exists&quot;);</span>
        }
        
        // Generate verification token and secret key
<span class="nc" id="L47">        webhookEndpoint.generateVerificationToken();</span>
<span class="nc" id="L48">        webhookEndpoint.generateSecretKey();</span>
        
<span class="nc" id="L50">        WebhookEndpoint saved = webhookEndpointRepository.save(webhookEndpoint);</span>
        
        // Create audit log
<span class="nc" id="L53">        auditService.logWebhookEndpointCreated(saved, createdBy);</span>
        
<span class="nc" id="L55">        log.info(&quot;Created webhook endpoint with ID: {}&quot;, saved.getId());</span>
<span class="nc" id="L56">        return saved;</span>
    }

    /**
     * Update an existing webhook endpoint
     */
    public WebhookEndpoint updateWebhookEndpoint(Long id, WebhookEndpoint updatedEndpoint, String modifiedBy) {
<span class="nc" id="L63">        log.info(&quot;Updating webhook endpoint ID: {} by user: {}&quot;, id, modifiedBy);</span>
        
<span class="nc" id="L65">        WebhookEndpoint existing = getWebhookEndpointById(id);</span>
        
        // Validate updated endpoint
<span class="nc" id="L68">        validateWebhookEndpoint(updatedEndpoint);</span>
        
        // Check for duplicate URL (excluding current endpoint)
<span class="nc" id="L71">        Optional&lt;WebhookEndpoint&gt; existingByUrl = webhookEndpointRepository.findByUrl(updatedEndpoint.getUrl());</span>
<span class="nc bnc" id="L72" title="All 4 branches missed.">        if (existingByUrl.isPresent() &amp;&amp; !existingByUrl.get().getId().equals(id)) {</span>
<span class="nc" id="L73">            throw new RuntimeException(&quot;Webhook endpoint with URL '&quot; + updatedEndpoint.getUrl() + &quot;' already exists&quot;);</span>
        }
        
        // Update fields
<span class="nc" id="L77">        existing.setName(updatedEndpoint.getName());</span>
<span class="nc" id="L78">        existing.setDescription(updatedEndpoint.getDescription());</span>
<span class="nc" id="L79">        existing.setUrl(updatedEndpoint.getUrl());</span>
<span class="nc" id="L80">        existing.setHttpMethod(updatedEndpoint.getHttpMethod());</span>
<span class="nc" id="L81">        existing.setContentType(updatedEndpoint.getContentType());</span>
<span class="nc" id="L82">        existing.setEventTypes(updatedEndpoint.getEventTypes());</span>
<span class="nc" id="L83">        existing.setEventFilters(updatedEndpoint.getEventFilters());</span>
<span class="nc" id="L84">        existing.setCustomHeaders(updatedEndpoint.getCustomHeaders());</span>
<span class="nc" id="L85">        existing.setPayloadTemplate(updatedEndpoint.getPayloadTemplate());</span>
<span class="nc" id="L86">        existing.setAuthType(updatedEndpoint.getAuthType());</span>
<span class="nc" id="L87">        existing.setAuthConfig(updatedEndpoint.getAuthConfig());</span>
<span class="nc" id="L88">        existing.setTimeoutSeconds(updatedEndpoint.getTimeoutSeconds());</span>
<span class="nc" id="L89">        existing.setMaxRetries(updatedEndpoint.getMaxRetries());</span>
<span class="nc" id="L90">        existing.setRateLimitPerMinute(updatedEndpoint.getRateLimitPerMinute());</span>
        
        // If URL changed, mark as unverified and generate new tokens
<span class="nc bnc" id="L93" title="All 2 branches missed.">        if (!existing.getUrl().equals(updatedEndpoint.getUrl())) {</span>
<span class="nc" id="L94">            existing.setIsVerified(false);</span>
<span class="nc" id="L95">            existing.generateVerificationToken();</span>
<span class="nc" id="L96">            existing.generateSecretKey();</span>
        }
        
<span class="nc" id="L99">        WebhookEndpoint saved = webhookEndpointRepository.save(existing);</span>
        
        // Create audit log
<span class="nc" id="L102">        auditService.logWebhookEndpointUpdated(saved, modifiedBy);</span>
        
<span class="nc" id="L104">        log.info(&quot;Updated webhook endpoint ID: {}&quot;, saved.getId());</span>
<span class="nc" id="L105">        return saved;</span>
    }

    /**
     * Get webhook endpoint by ID
     */
    @Transactional(readOnly = true)
    public WebhookEndpoint getWebhookEndpointById(Long id) {
<span class="nc" id="L113">        return webhookEndpointRepository.findById(id)</span>
<span class="nc" id="L114">                .orElseThrow(() -&gt; new RuntimeException(&quot;Webhook endpoint not found with ID: &quot; + id));</span>
    }

    /**
     * Get all active webhook endpoints
     */
    @Transactional(readOnly = true)
    public List&lt;WebhookEndpoint&gt; getAllActiveWebhookEndpoints() {
<span class="nc" id="L122">        return webhookEndpointRepository.findByIsActiveTrue();</span>
    }

    /**
     * Get webhook endpoints by creator
     */
    @Transactional(readOnly = true)
    public Page&lt;WebhookEndpoint&gt; getWebhookEndpointsByCreator(String createdBy, Pageable pageable) {
<span class="nc" id="L130">        return webhookEndpointRepository.findByCreatedBy(createdBy, pageable);</span>
    }

    /**
     * Search webhook endpoints by name
     */
    @Transactional(readOnly = true)
    public List&lt;WebhookEndpoint&gt; searchWebhookEndpoints(String name) {
<span class="nc" id="L138">        return webhookEndpointRepository.findByNameContainingIgnoreCase(name);</span>
    }

    /**
     * Get webhook endpoints for event type
     */
    @Transactional(readOnly = true)
    public List&lt;WebhookEndpoint&gt; getWebhookEndpointsForEventType(String eventType) {
<span class="nc" id="L146">        return webhookEndpointRepository.findByEventType(&quot;\&quot;&quot; + eventType + &quot;\&quot;&quot;);</span>
    }

    /**
     * Verify webhook endpoint
     */
    public WebhookEndpoint verifyWebhookEndpoint(String verificationToken) {
<span class="nc" id="L153">        log.info(&quot;Verifying webhook endpoint with token: {}&quot;, verificationToken);</span>
        
<span class="nc" id="L155">        WebhookEndpoint endpoint = webhookEndpointRepository.findByVerificationToken(verificationToken)</span>
<span class="nc" id="L156">                .orElseThrow(() -&gt; new RuntimeException(&quot;Invalid verification token&quot;));</span>
        
<span class="nc" id="L158">        endpoint.setIsVerified(true);</span>
<span class="nc" id="L159">        endpoint.setVerificationToken(null); // Clear token after verification</span>
        
<span class="nc" id="L161">        WebhookEndpoint saved = webhookEndpointRepository.save(endpoint);</span>
        
        // Create audit log
<span class="nc" id="L164">        auditService.logWebhookEndpointVerified(saved);</span>
        
<span class="nc" id="L166">        log.info(&quot;Verified webhook endpoint ID: {}&quot;, saved.getId());</span>
<span class="nc" id="L167">        return saved;</span>
    }

    /**
     * Activate webhook endpoint
     */
    public WebhookEndpoint activateWebhookEndpoint(Long id, String modifiedBy) {
<span class="nc" id="L174">        log.info(&quot;Activating webhook endpoint ID: {} by user: {}&quot;, id, modifiedBy);</span>
        
<span class="nc" id="L176">        WebhookEndpoint endpoint = getWebhookEndpointById(id);</span>
<span class="nc" id="L177">        endpoint.setIsActive(true);</span>
        
<span class="nc" id="L179">        WebhookEndpoint saved = webhookEndpointRepository.save(endpoint);</span>
        
        // Create audit log
<span class="nc" id="L182">        auditService.logWebhookEndpointActivated(saved, modifiedBy);</span>
        
<span class="nc" id="L184">        return saved;</span>
    }

    /**
     * Deactivate webhook endpoint
     */
    public WebhookEndpoint deactivateWebhookEndpoint(Long id, String modifiedBy) {
<span class="nc" id="L191">        log.info(&quot;Deactivating webhook endpoint ID: {} by user: {}&quot;, id, modifiedBy);</span>
        
<span class="nc" id="L193">        WebhookEndpoint endpoint = getWebhookEndpointById(id);</span>
<span class="nc" id="L194">        endpoint.setIsActive(false);</span>
        
<span class="nc" id="L196">        WebhookEndpoint saved = webhookEndpointRepository.save(endpoint);</span>
        
        // Create audit log
<span class="nc" id="L199">        auditService.logWebhookEndpointDeactivated(saved, modifiedBy);</span>
        
<span class="nc" id="L201">        return saved;</span>
    }

    /**
     * Record successful webhook delivery
     */
    public void recordSuccessfulDelivery(Long endpointId) {
<span class="nc" id="L208">        WebhookEndpoint endpoint = getWebhookEndpointById(endpointId);</span>
<span class="nc" id="L209">        endpoint.recordSuccess();</span>
<span class="nc" id="L210">        webhookEndpointRepository.save(endpoint);</span>
<span class="nc" id="L211">    }</span>

    /**
     * Record failed webhook delivery
     */
    public void recordFailedDelivery(Long endpointId, String reason) {
<span class="nc" id="L217">        WebhookEndpoint endpoint = getWebhookEndpointById(endpointId);</span>
<span class="nc" id="L218">        endpoint.recordFailure(reason);</span>
<span class="nc" id="L219">        webhookEndpointRepository.save(endpoint);</span>
<span class="nc" id="L220">    }</span>

    /**
     * Get webhook endpoints with high failure rate
     */
    @Transactional(readOnly = true)
    public List&lt;WebhookEndpoint&gt; getWebhookEndpointsWithHighFailureRate(double threshold) {
<span class="nc" id="L227">        return webhookEndpointRepository.findWithHighFailureRate(threshold);</span>
    }

    /**
     * Get unused webhook endpoints
     */
    @Transactional(readOnly = true)
    public List&lt;WebhookEndpoint&gt; getUnusedWebhookEndpoints(int daysBack) {
<span class="nc" id="L235">        LocalDateTime cutoffDate = LocalDateTime.now().minusDays(daysBack);</span>
<span class="nc" id="L236">        return webhookEndpointRepository.findUnusedEndpoints(cutoffDate);</span>
    }

    /**
     * Get webhook statistics
     */
    @Transactional(readOnly = true)
    public WebhookStatistics getWebhookStatistics() {
<span class="nc" id="L244">        Object[] stats = webhookEndpointRepository.getWebhookStatistics();</span>
<span class="nc" id="L245">        long totalActive = webhookEndpointRepository.countByIsActiveTrue();</span>
<span class="nc" id="L246">        long totalVerified = webhookEndpointRepository.countByIsVerifiedTrue();</span>
        
<span class="nc" id="L248">        return WebhookStatistics.builder()</span>
<span class="nc" id="L249">                .totalActive(totalActive)</span>
<span class="nc" id="L250">                .totalVerified(totalVerified)</span>
<span class="nc bnc" id="L251" title="All 2 branches missed.">                .totalSuccesses(stats[0] != null ? ((Number) stats[0]).longValue() : 0)</span>
<span class="nc bnc" id="L252" title="All 2 branches missed.">                .totalFailures(stats[1] != null ? ((Number) stats[1]).longValue() : 0)</span>
<span class="nc" id="L253">                .build();</span>
    }

    /**
     * Get failed webhook deliveries
     */
    @Transactional(readOnly = true)
    public List&lt;WebhookDelivery&gt; getFailedWebhookDeliveries() {
<span class="nc" id="L261">        log.info(&quot;Getting failed webhook deliveries&quot;);</span>
<span class="nc" id="L262">        return webhookDeliveryRepository.findFailedDeliveries();</span>
    }

    /**
     * Get webhook deliveries for a specific endpoint
     */
    @Transactional(readOnly = true)
    public List&lt;WebhookDelivery&gt; getWebhookDeliveriesForEndpoint(Long webhookEndpointId) {
<span class="nc" id="L270">        log.info(&quot;Getting webhook deliveries for endpoint ID: {}&quot;, webhookEndpointId);</span>
<span class="nc" id="L271">        return webhookDeliveryRepository.findByWebhookEndpointId(webhookEndpointId);</span>
    }

    /**
     * Get webhook deliveries for a specific system event
     */
    @Transactional(readOnly = true)
    public List&lt;WebhookDelivery&gt; getWebhookDeliveriesForEvent(Long systemEventId) {
<span class="nc" id="L279">        log.info(&quot;Getting webhook deliveries for event ID: {}&quot;, systemEventId);</span>
<span class="nc" id="L280">        return webhookDeliveryRepository.findBySystemEventId(systemEventId);</span>
    }

    /**
     * Get average response time across all webhook deliveries
     */
    @Transactional(readOnly = true)
    public Double getAverageResponseTime() {
<span class="nc" id="L288">        List&lt;Object[]&gt; results = webhookDeliveryRepository.getAverageResponseTimeByEndpoint();</span>
<span class="nc bnc" id="L289" title="All 4 branches missed.">        if (results == null || results.isEmpty()) {</span>
<span class="nc" id="L290">            return 0.0;</span>
        }

<span class="nc" id="L293">        double totalResponseTime = 0.0;</span>
<span class="nc" id="L294">        int count = 0;</span>

<span class="nc bnc" id="L296" title="All 2 branches missed.">        for (Object[] result : results) {</span>
<span class="nc bnc" id="L297" title="All 4 branches missed.">            if (result != null &amp;&amp; result[2] != null) {</span>
<span class="nc" id="L298">                totalResponseTime += ((Number) result[2]).doubleValue();</span>
<span class="nc" id="L299">                count++;</span>
            }
<span class="nc" id="L301">        }</span>

<span class="nc bnc" id="L303" title="All 2 branches missed.">        return count &gt; 0 ? totalResponseTime / count : 0.0;</span>
    }

    /**
     * Get last delivery time across all webhook endpoints
     */
    @Transactional(readOnly = true)
    public LocalDateTime getLastDeliveryTime() {
<span class="nc" id="L311">        Optional&lt;WebhookDelivery&gt; delivery = webhookDeliveryRepository.findTopByOrderByScheduledDateDesc();</span>
<span class="nc bnc" id="L312" title="All 2 branches missed.">        if (delivery == null) {</span>
<span class="nc" id="L313">            return null;</span>
        }
<span class="nc" id="L315">        return delivery.map(WebhookDelivery::getScheduledDate).orElse(null);</span>
    }

    /**
     * Get count of healthy webhook endpoints
     */
    @Transactional(readOnly = true)
    public long getHealthyEndpointsCount() {
<span class="nc" id="L323">        List&lt;WebhookEndpoint&gt; activeEndpoints = getAllActiveWebhookEndpoints();</span>
<span class="nc" id="L324">        return activeEndpoints.stream()</span>
<span class="nc" id="L325">                .filter(WebhookEndpoint::isHealthy)</span>
<span class="nc" id="L326">                .count();</span>
    }

    /**
     * Get count of unhealthy webhook endpoints
     */
    @Transactional(readOnly = true)
    public long getUnhealthyEndpointsCount() {
<span class="nc" id="L334">        List&lt;WebhookEndpoint&gt; activeEndpoints = getAllActiveWebhookEndpoints();</span>
<span class="nc" id="L335">        return activeEndpoints.stream()</span>
<span class="nc bnc" id="L336" title="All 2 branches missed.">                .filter(endpoint -&gt; !endpoint.isHealthy())</span>
<span class="nc" id="L337">                .count();</span>
    }

    // Private helper methods

    private void validateWebhookEndpoint(WebhookEndpoint endpoint) {
<span class="nc bnc" id="L343" title="All 4 branches missed.">        if (endpoint.getName() == null || endpoint.getName().trim().isEmpty()) {</span>
<span class="nc" id="L344">            throw new RuntimeException(&quot;Webhook endpoint name is required&quot;);</span>
        }
        
<span class="nc bnc" id="L347" title="All 4 branches missed.">        if (endpoint.getUrl() == null || endpoint.getUrl().trim().isEmpty()) {</span>
<span class="nc" id="L348">            throw new RuntimeException(&quot;Webhook endpoint URL is required&quot;);</span>
        }
        
<span class="nc bnc" id="L351" title="All 4 branches missed.">        if (!endpoint.getUrl().startsWith(&quot;http://&quot;) &amp;&amp; !endpoint.getUrl().startsWith(&quot;https://&quot;)) {</span>
<span class="nc" id="L352">            throw new RuntimeException(&quot;Webhook endpoint URL must start with http:// or https://&quot;);</span>
        }
        
<span class="nc bnc" id="L355" title="All 4 branches missed.">        if (endpoint.getHttpMethod() == null || endpoint.getHttpMethod().trim().isEmpty()) {</span>
<span class="nc" id="L356">            throw new RuntimeException(&quot;HTTP method is required&quot;);</span>
        }
        
<span class="nc bnc" id="L359" title="All 4 branches missed.">        if (endpoint.getContentType() == null || endpoint.getContentType().trim().isEmpty()) {</span>
<span class="nc" id="L360">            throw new RuntimeException(&quot;Content type is required&quot;);</span>
        }
<span class="nc" id="L362">    }</span>

    // Inner class for webhook statistics
<span class="nc" id="L365">    public static class WebhookStatistics {</span>
        private long totalActive;
        private long totalVerified;
        private long totalSuccesses;
        private long totalFailures;
        
        public static WebhookStatisticsBuilder builder() {
<span class="nc" id="L372">            return new WebhookStatisticsBuilder();</span>
        }
        
<span class="nc" id="L375">        public static class WebhookStatisticsBuilder {</span>
            private long totalActive;
            private long totalVerified;
            private long totalSuccesses;
            private long totalFailures;
            
            public WebhookStatisticsBuilder totalActive(long totalActive) {
<span class="nc" id="L382">                this.totalActive = totalActive;</span>
<span class="nc" id="L383">                return this;</span>
            }
            
            public WebhookStatisticsBuilder totalVerified(long totalVerified) {
<span class="nc" id="L387">                this.totalVerified = totalVerified;</span>
<span class="nc" id="L388">                return this;</span>
            }
            
            public WebhookStatisticsBuilder totalSuccesses(long totalSuccesses) {
<span class="nc" id="L392">                this.totalSuccesses = totalSuccesses;</span>
<span class="nc" id="L393">                return this;</span>
            }
            
            public WebhookStatisticsBuilder totalFailures(long totalFailures) {
<span class="nc" id="L397">                this.totalFailures = totalFailures;</span>
<span class="nc" id="L398">                return this;</span>
            }
            
            public WebhookStatistics build() {
<span class="nc" id="L402">                WebhookStatistics stats = new WebhookStatistics();</span>
<span class="nc" id="L403">                stats.totalActive = this.totalActive;</span>
<span class="nc" id="L404">                stats.totalVerified = this.totalVerified;</span>
<span class="nc" id="L405">                stats.totalSuccesses = this.totalSuccesses;</span>
<span class="nc" id="L406">                stats.totalFailures = this.totalFailures;</span>
<span class="nc" id="L407">                return stats;</span>
            }
        }
        
        // Getters
<span class="nc" id="L412">        public long getTotalActive() { return totalActive; }</span>
<span class="nc" id="L413">        public long getTotalVerified() { return totalVerified; }</span>
<span class="nc" id="L414">        public long getTotalSuccesses() { return totalSuccesses; }</span>
<span class="nc" id="L415">        public long getTotalFailures() { return totalFailures; }</span>
    }
}
</pre><div class="footer"><span class="right">Created with <a href="http://www.jacoco.org/jacoco">JaCoCo</a> 0.8.10.202304240956</span></div></body></html>