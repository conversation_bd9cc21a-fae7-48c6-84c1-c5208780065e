# ========================================
# DMS SERVICE - BASE CONFIGURATION
# ========================================
# This file contains common configurations shared across all environments.
# Environment-specific overrides are in application-{profile}.properties

# Application Configuration
spring.application.name=dms-svc
server.port=${SERVER_PORT:9093}

# Document Sharing Configuration
dms.application.base-url=${DMS_BASE_URL:http://localhost:9093}

# ========================================
# DATABASE CONFIGURATION
# ========================================
spring.datasource.url=${DB_URL:*************************************************************************************************************************************************************************************************}
spring.datasource.username=${DB_USERNAME:root}
spring.datasource.password=${DB_PASSWORD:root}
spring.datasource.driver-class-name=com.mysql.cj.jdbc.Driver

# HikariCP Connection Pool Configuration
spring.datasource.hikari.pool-name=DmsHikariPool
spring.datasource.hikari.maximum-pool-size=${DB_POOL_MAX_SIZE:20}
spring.datasource.hikari.minimum-idle=${DB_POOL_MIN_IDLE:5}
spring.datasource.hikari.idle-timeout=${DB_POOL_IDLE_TIMEOUT:300000}
spring.datasource.hikari.max-lifetime=${DB_POOL_MAX_LIFETIME:1800000}
spring.datasource.hikari.connection-timeout=${DB_POOL_CONNECTION_TIMEOUT:30000}
spring.datasource.hikari.validation-timeout=${DB_POOL_VALIDATION_TIMEOUT:5000}
spring.datasource.hikari.leak-detection-threshold=${DB_POOL_LEAK_DETECTION:60000}
spring.datasource.hikari.connection-test-query=SELECT 1

# ========================================
# JPA CONFIGURATION
# ========================================
spring.jpa.hibernate.ddl-auto=${JPA_DDL_AUTO:update}
spring.jpa.database-platform=org.hibernate.dialect.MySQLDialect
spring.jpa.show-sql=${JPA_SHOW_SQL:false}
spring.jpa.open-in-view=false
spring.jpa.properties.hibernate.format_sql=${HIBERNATE_FORMAT_SQL:false}
spring.jpa.properties.hibernate.jdbc.lob.non_contextual_creation=true

# Hibernate Performance Optimizations
spring.jpa.properties.hibernate.jdbc.batch_size=25
spring.jpa.properties.hibernate.order_inserts=true
spring.jpa.properties.hibernate.order_updates=true
spring.jpa.properties.hibernate.jdbc.batch_versioned_data=true
spring.jpa.properties.hibernate.cache.use_second_level_cache=false
spring.jpa.properties.hibernate.cache.use_query_cache=false
spring.jpa.properties.hibernate.generate_statistics=${HIBERNATE_STATS:false}
spring.jpa.properties.hibernate.session.events.log.LOG_QUERIES_SLOWER_THAN_MS=1000

# ========================================
# LIQUIBASE CONFIGURATION
# ========================================
spring.liquibase.change-log=classpath:db/changelog/db.changelog-master.xml
spring.liquibase.drop-first=false
spring.liquibase.contexts=default
spring.liquibase.enabled=false

# ========================================
# REDIS CONFIGURATION
# ========================================
spring.data.redis.host=${REDIS_HOST:localhost}
spring.data.redis.port=${REDIS_PORT:6379}
spring.data.redis.password=${REDIS_PASSWORD:}
spring.data.redis.database=${REDIS_DATABASE:0}
spring.data.redis.timeout=5000ms
spring.data.redis.connect-timeout=3000ms

# Redis Connection Pool Configuration
spring.data.redis.lettuce.pool.max-active=20
spring.data.redis.lettuce.pool.max-idle=10
spring.data.redis.lettuce.pool.min-idle=2
spring.data.redis.lettuce.pool.max-wait=2000ms
spring.data.redis.lettuce.pool.time-between-eviction-runs=30s

# Redis health check - environment specific
management.health.redis.enabled=${REDIS_HEALTH_ENABLED:false}

# ========================================
# GRAPHQL CONFIGURATION
# ========================================
spring.graphql.graphiql.enabled=${GRAPHIQL_ENABLED:true}
spring.graphql.graphiql.path=/graphiql
spring.graphql.path=/graphql
spring.graphql.schema.printer.enabled=${GRAPHQL_SCHEMA_PRINTER_ENABLED:true}
spring.graphql.schema.locations=classpath:graphql/
spring.graphql.schema.file-extensions=.graphqls,.gqls
spring.graphql.websocket.path=/graphql

# GraphiQL specific configuration
spring.graphql.graphiql.properties.request.globalHeaders={"Content-Type":"application/json"}

# CORS Configuration
spring.graphql.cors.allowed-origins=${CORS_ALLOWED_ORIGINS:http://localhost:9093}
spring.graphql.cors.allowed-methods=${CORS_ALLOWED_METHODS:GET,POST,OPTIONS}
spring.graphql.cors.allowed-headers=${CORS_ALLOWED_HEADERS:Content-Type,Authorization,X-Correlation-ID}
spring.graphql.cors.allow-credentials=${CORS_ALLOW_CREDENTIALS:true}

# Disable multipart GraphQL Upload scalar auto-registration
spring.graphql.multipart.springboot.enabled=false

# ========================================
# FILE UPLOAD CONFIGURATION
# ========================================
spring.servlet.multipart.enabled=true
spring.servlet.multipart.max-file-size=${MAX_FILE_SIZE:100MB}
spring.servlet.multipart.max-request-size=${MAX_REQUEST_SIZE:100MB}

# ========================================
# STORAGE CONFIGURATION
# ========================================
# Storage provider is environment-specific
dms.storage.provider=${STORAGE_PROVIDER:LOCAL}

# Local Storage Configuration
dms.storage.local.base-path=${LOCAL_STORAGE_PATH:./storage/documents}
dms.storage.local.create-directories=true

# S3 Storage Configuration
dms.storage.s3.bucket-name=${S3_BUCKET_NAME:}
dms.storage.s3.region=${S3_REGION:}
dms.storage.s3.access-key=${S3_ACCESS_KEY:}
dms.storage.s3.secret-key=${S3_SECRET_KEY:}
dms.storage.s3.endpoint=${S3_ENDPOINT:}

# SharePoint Storage Configuration
dms.storage.sharepoint.client-id=${SHAREPOINT_CLIENT_ID:}
dms.storage.sharepoint.client-secret=${SHAREPOINT_CLIENT_SECRET:}
dms.storage.sharepoint.tenant-id=${SHAREPOINT_TENANT_ID:}
dms.storage.sharepoint.scopes=User.Read,Sites.ReadWrite.All,Files.ReadWrite.All
dms.storage.sharepoint.graph-api-url=https://graph.microsoft.com/v1.0
dms.storage.sharepoint.token-url=https://login.microsoftonline.com/${SHAREPOINT_TENANT_ID}/oauth2/v2.0/token
dms.storage.sharepoint.site-url=${SHAREPOINT_SITE_URL:}
dms.storage.sharepoint.document-library=Documents

# SharePoint Performance Configuration
dms.storage.sharepoint.connection-timeout=30000
dms.storage.sharepoint.read-timeout=60000
dms.storage.sharepoint.max-retries=3
dms.storage.sharepoint.retry-delay-ms=1000

# SharePoint Resilience Configuration
dms.storage.sharepoint.enable-circuit-breaker=true
dms.storage.sharepoint.circuit-breaker-threshold=5
dms.storage.sharepoint.circuit-breaker-timeout-ms=60000
dms.storage.sharepoint.fail-safe-startup=true

# SharePoint Debugging Configuration
dms.storage.sharepoint.debug=false
dms.storage.sharepoint.enable-request-logging=false

# ========================================
# SECURITY CONFIGURATION
# ========================================
# JWT Configuration
dms.jwt.secret=${JWT_SECRET:mySecretKey123456789012345678901234567890}
dms.jwt.expiration=${JWT_EXPIRATION:86400000}
dms.jwt.header=Authorization
dms.jwt.prefix=Bearer

# Security Headers Configuration
dms.security.headers.csp.enabled=${SECURITY_HEADERS_CSP_ENABLED:true}
dms.security.headers.csp.policy=${SECURITY_HEADERS_CSP_POLICY:default-src 'self'; script-src 'self' 'unsafe-inline' 'unsafe-eval'; style-src 'self' 'unsafe-inline'; img-src 'self' data: https:; font-src 'self' data:; connect-src 'self' ws: wss:; frame-ancestors 'self'; base-uri 'self'; form-action 'self'}
dms.security.headers.csp.report-only=${SECURITY_HEADERS_CSP_REPORT_ONLY:false}
dms.security.headers.hsts.enabled=${SECURITY_HEADERS_HSTS_ENABLED:true}
dms.security.headers.hsts.max-age=${SECURITY_HEADERS_HSTS_MAX_AGE:31536000}
dms.security.headers.hsts.include-subdomains=${SECURITY_HEADERS_HSTS_INCLUDE_SUBDOMAINS:true}
dms.security.headers.hsts.preload=${SECURITY_HEADERS_HSTS_PRELOAD:true}
dms.security.headers.frame-options=${SECURITY_HEADERS_FRAME_OPTIONS:SAMEORIGIN}
dms.security.headers.content-type-options=${SECURITY_HEADERS_CONTENT_TYPE_OPTIONS:nosniff}
dms.security.headers.referrer-policy=${SECURITY_HEADERS_REFERRER_POLICY:strict-origin-when-cross-origin}

# ========================================
# ACTUATOR CONFIGURATION
# ========================================
management.endpoints.web.exposure.include=${ACTUATOR_ENDPOINTS:health,info,metrics,prometheus}
management.endpoint.health.show-details=${ACTUATOR_HEALTH_DETAILS:always}
management.endpoint.health.show-components=always
management.health.defaults.enabled=true
management.health.db.enabled=true
management.health.diskspace.enabled=true
management.health.diskspace.threshold=100MB
management.metrics.export.prometheus.enabled=true

# ========================================
# OBSERVABILITY CONFIGURATION
# ========================================
# OpenTelemetry Configuration
otel.service.name=dms-svc
otel.service.version=1.0.0
otel.resource.attributes.service.name=dms-svc
otel.resource.attributes.service.version=1.0.0
otel.resource.attributes.deployment.environment=${ENVIRONMENT:local}

# Tracing Configuration
otel.traces.exporter=${OTEL_TRACES_EXPORTER:zipkin}
otel.exporter.zipkin.endpoint=${ZIPKIN_ENDPOINT:http://localhost:9411/api/v2/spans}
otel.traces.sampler=traceidratio
otel.traces.sampler.arg=${OTEL_TRACES_SAMPLING:0.1}

# Metrics Configuration - Disable console output to reduce noise
otel.metrics.exporter=${OTEL_METRICS_EXPORTER:none}
otel.metric.export.interval=60s

# Logs Configuration - Disable console output to reduce noise
otel.logs.exporter=${OTEL_LOGS_EXPORTER:none}

# Spring Boot Actuator Tracing
management.tracing.enabled=true
management.tracing.sampling.probability=${TRACING_SAMPLING_PROBABILITY:0.1}
management.tracing.baggage.correlation.enabled=true
management.tracing.baggage.correlation.fields=correlationId,userId
management.zipkin.tracing.endpoint=${ZIPKIN_ENDPOINT:http://localhost:9411/api/v2/spans}
management.tracing.zipkin.enabled=${ZIPKIN_ENABLED:true}

# ========================================
# LOGGING CONFIGURATION
# ========================================
logging.level.com.ascentbusiness.dms_svc=${LOG_LEVEL_DMS:INFO}
logging.level.org.springframework.security=${LOG_LEVEL_SECURITY:WARN}
logging.pattern.console=%d{yyyy-MM-dd HH:mm:ss.SSS} [%thread] %-5level [%X{correlationId}] %logger{36} - %msg%n
logging.pattern.file=%d{yyyy-MM-dd HH:mm:ss.SSS} [%thread] %-5level [%X{correlationId}] %logger{36} - %msg%n

# Correlation ID Configuration
dms.correlation.header-name=X-Correlation-ID

# ========================================
# CACHE CONFIGURATION
# ========================================
spring.cache.type=${CACHE_TYPE:redis}
spring.cache.redis.time-to-live=${CACHE_TTL:1800000}
spring.cache.redis.cache-null-values=false
spring.cache.redis.use-key-prefix=true
spring.cache.redis.key-prefix=${CACHE_KEY_PREFIX:dms:}

# ========================================
# RATE LIMITING CONFIGURATION
# ========================================
dms.rate-limit.enabled=${RATE_LIMIT_ENABLED:true}
dms.rate-limit.use-redis=${RATE_LIMIT_USE_REDIS:true}

# GraphQL Rate Limiting
dms.rate-limit.graphql.enabled=${GRAPHQL_RATE_LIMIT_ENABLED:true}
dms.rate-limit.graphql.query.limit=${GRAPHQL_QUERY_RATE_LIMIT:100}
dms.rate-limit.graphql.query.window=${GRAPHQL_QUERY_RATE_WINDOW:60}
dms.rate-limit.graphql.mutation.limit=${GRAPHQL_MUTATION_RATE_LIMIT:50}
dms.rate-limit.graphql.mutation.window=${GRAPHQL_MUTATION_RATE_WINDOW:60}

# Operation-specific Rate Limiting
dms.rate-limit.document.upload.limit=${DOCUMENT_UPLOAD_RATE_LIMIT:10}
dms.rate-limit.document.upload.window=${DOCUMENT_UPLOAD_RATE_WINDOW:300}
dms.rate-limit.document.download.limit=${DOCUMENT_DOWNLOAD_RATE_LIMIT:50}
dms.rate-limit.document.download.window=${DOCUMENT_DOWNLOAD_RATE_WINDOW:60}
dms.rate-limit.search.limit=${SEARCH_RATE_LIMIT:30}
dms.rate-limit.search.window=${SEARCH_RATE_WINDOW:60}
dms.rate-limit.permission.limit=${PERMISSION_RATE_LIMIT:20}
dms.rate-limit.permission.window=${PERMISSION_RATE_WINDOW:300}

# ========================================
# ENCRYPTION CONFIGURATION
# ========================================
# Audit Log Encryption
dms.audit.encryption.enabled=${AUDIT_ENCRYPTION_ENABLED:false}
dms.audit.encryption.key-rotation.enabled=${AUDIT_ENCRYPTION_KEY_ROTATION_ENABLED:true}
dms.audit.encryption.key-rotation.interval-hours=${AUDIT_ENCRYPTION_KEY_ROTATION_INTERVAL_HOURS:24}
dms.audit.encryption.master-key=${AUDIT_ENCRYPTION_MASTER_KEY:}
dms.audit.encryption.sensitive-events-only=${AUDIT_ENCRYPTION_SENSITIVE_EVENTS_ONLY:true}
dms.audit.encryption.performance-mode=${AUDIT_ENCRYPTION_PERFORMANCE_MODE:true}

# PII Field-Level Encryption
dms.pii.encryption.enabled=${PII_ENCRYPTION_ENABLED:false}
dms.pii.encryption.key-rotation.enabled=${PII_ENCRYPTION_KEY_ROTATION_ENABLED:true}
dms.pii.encryption.key-rotation.interval-hours=${PII_ENCRYPTION_KEY_ROTATION_INTERVAL_HOURS:168}
dms.pii.encryption.master-key=${PII_ENCRYPTION_MASTER_KEY:}
dms.pii.encryption.auto-detect=${PII_ENCRYPTION_AUTO_DETECT:true}

# ========================================
# API VERSIONING CONFIGURATION
# ========================================
dms.api.version.current=${API_VERSION_CURRENT:1.0}
dms.api.version.supported=${API_VERSION_SUPPORTED:1.0,1.1}
dms.api.version.deprecation-warning=${API_VERSION_DEPRECATION_WARNING:true}
dms.api.version.strict-mode=${API_VERSION_STRICT_MODE:false}

# ========================================
# ELASTICSEARCH CONFIGURATION
# ========================================
elasticsearch.enabled=${ELASTICSEARCH_ENABLED:false}
elasticsearch.host=${ELASTICSEARCH_HOST:localhost}
elasticsearch.port=${ELASTICSEARCH_PORT:9200}
elasticsearch.protocol=${ELASTICSEARCH_PROTOCOL:http}
elasticsearch.username=${ELASTICSEARCH_USERNAME:elastic}
elasticsearch.password=${ELASTICSEARCH_PASSWORD:}
elasticsearch.connection-timeout=${ELASTICSEARCH_CONNECTION_TIMEOUT:10000}
elasticsearch.socket-timeout=${ELASTICSEARCH_SOCKET_TIMEOUT:30000}

# ========================================
# RETENTION POLICY CONFIGURATION
# ========================================
dms.retention.processing.enabled=true
dms.retention.processing.batch-size=100
dms.retention.processing.cron=0 0 2 * * *
dms.retention.notification.enabled=true
dms.retention.notification.days-before-expiry=30,7,1
dms.retention.auto-assignment.enabled=true
dms.retention.legal-hold.enabled=true

# ========================================
# FILE PROCESSING CONFIGURATION
# ========================================
dms.file-processing.direct-processing-threshold=1048576
dms.file-processing.async-processing-threshold=52428800
dms.file-processing.max-file-size=1073741824
dms.file-processing.default-chunk-size=5242880
dms.file-processing.max-chunk-size=52428800
dms.file-processing.min-chunk-size=1048576
dms.file-processing.max-concurrent-async-jobs=10
dms.file-processing.async-job-timeout-seconds=3600
dms.file-processing.chunk-session-timeout-seconds=86400
dms.file-processing.cleanup-interval-seconds=3600
dms.file-processing.temp-directory=./temp/processing
dms.file-processing.enable-auto-cleanup=true
dms.file-processing.enable-progress-tracking=true
dms.file-processing.enable-detailed-logging=false

# ========================================
# DOCUMENT CONVERSION CONFIGURATION
# ========================================
# PDF to Word Conversion
dms.pdf-conversion.max-file-size=52428800
dms.pdf-conversion.timeout-seconds=300
dms.pdf-conversion.virus-scanner=${PDF_CONVERSION_VIRUS_SCANNER:MOCK}
dms.pdf-conversion.enabled=true
dms.pdf-conversion.temp-directory=
dms.pdf-conversion.cleanup-after-hours=24

# Word to PDF Conversion
dms.word-conversion.max-file-size=52428800
dms.word-conversion.timeout-seconds=300
dms.word-conversion.virus-scanner=${WORD_CONVERSION_VIRUS_SCANNER:MOCK}
dms.word-conversion.enabled=true
dms.word-conversion.temp-directory=
dms.word-conversion.cleanup-after-hours=24

# Pandoc Configuration
dms.pandoc.executable-path=pandoc
dms.pandoc.enabled=true
dms.pandoc.enable-fallback=true
dms.pandoc.timeout-seconds=300
dms.pandoc.max-file-size=52428800
dms.pandoc.virus-scanner=${PANDOC_VIRUS_SCANNER:MOCK}
dms.pandoc.temp-directory=
dms.pandoc.cleanup-after-hours=24
dms.pandoc.check-availability-on-startup=true
dms.pandoc.additional-args=

# ========================================
# VIRUS SCANNING CONFIGURATION
# ========================================
dms.virus-scanning.enabled=${VIRUS_SCANNING_ENABLED:true}
dms.virus-scanning.fail-on-unavailable=${VIRUS_SCANNING_FAIL_ON_UNAVAILABLE:true}
dms.virus-scanning.default-scanner=${VIRUS_SCANNING_DEFAULT_SCANNER:MOCK}

# Mock Scanner Configuration
dms.virus-scanner.mock.enabled=${VIRUS_SCANNER_MOCK_ENABLED:true}

# ClamAV Scanner Configuration
dms.virus-scanner.clamav.enabled=${VIRUS_SCANNER_CLAMAV_ENABLED:false}
dms.virus-scanner.clamav.host=${VIRUS_SCANNER_CLAMAV_HOST:localhost}
dms.virus-scanner.clamav.port=${VIRUS_SCANNER_CLAMAV_PORT:3310}
dms.virus-scanner.clamav.timeout=${VIRUS_SCANNER_CLAMAV_TIMEOUT:10000}
dms.virus-scanner.clamav.chunk-size=${VIRUS_SCANNER_CLAMAV_CHUNK_SIZE:8192}
dms.virus-scanner.clamav.max-file-size=${VIRUS_SCANNER_CLAMAV_MAX_FILE_SIZE:104857600}

# Windows Defender Scanner Configuration
dms.virus-scanner.windows-defender.enabled=${VIRUS_SCANNER_WINDOWS_DEFENDER_ENABLED:false}
dms.virus-scanner.windows-defender.executable-path=${VIRUS_SCANNER_WINDOWS_DEFENDER_EXECUTABLE:C:\\Program Files\\Windows Defender\\MpCmdRun.exe}
dms.virus-scanner.windows-defender.timeout=${VIRUS_SCANNER_WINDOWS_DEFENDER_TIMEOUT:60}
dms.virus-scanner.windows-defender.temp-dir=${VIRUS_SCANNER_WINDOWS_DEFENDER_TEMP_DIR:${java.io.tmpdir}}
dms.virus-scanner.windows-defender.max-file-size=${VIRUS_SCANNER_WINDOWS_DEFENDER_MAX_FILE_SIZE:104857600}

# ========================================
# URL UPLOAD CONFIGURATION
# ========================================
dms.upload.url.enabled=${URL_UPLOAD_ENABLED:true}
dms.upload.url.timeout-ms=${URL_UPLOAD_TIMEOUT_MS:30000}
dms.upload.url.max-file-size=${URL_UPLOAD_MAX_FILE_SIZE:104857600}
dms.upload.url.max-redirects=${URL_UPLOAD_MAX_REDIRECTS:5}
dms.upload.url.user-agent=${URL_UPLOAD_USER_AGENT:DMS-Service/1.0}
dms.upload.url.allowed-domains=${URL_UPLOAD_ALLOWED_DOMAINS:}
dms.upload.network-path.enabled=${NETWORK_PATH_UPLOAD_ENABLED:true}
dms.upload.network-path.timeout-ms=${NETWORK_PATH_TIMEOUT_MS:60000}
dms.upload.url.validate-ssl=${URL_UPLOAD_VALIDATE_SSL:true}
dms.upload.url.allow-private-ips=${URL_UPLOAD_ALLOW_PRIVATE_IPS:false}
dms.upload.url.blocked-ports=${URL_UPLOAD_BLOCKED_PORTS:22,23,25,53,135,139,445,1433,1521,3306,3389,5432,6379}
dms.upload.url.allowed-content-types=${URL_UPLOAD_ALLOWED_CONTENT_TYPES:}
dms.upload.url.blocked-content-types=${URL_UPLOAD_BLOCKED_CONTENT_TYPES:text/html,application/javascript,text/javascript}

# ========================================
# MIGRATION SETTINGS
# ========================================
dms.migration.batch-size=100
dms.migration.verify-transfers=true
dms.migration.cleanup-after-migration=false

# ========================================
# TEST CONFIGURATION
# ========================================
testcase.directory=tests/test-cases
dms.storage.test.create-sample-configurations=false
dms.storage.test.run-tests-on-startup=false
dms.storage.initialize-default-configuration=false
