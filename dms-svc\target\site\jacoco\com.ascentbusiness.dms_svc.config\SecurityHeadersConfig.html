<?xml version="1.0" encoding="UTF-8"?><!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Strict//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-strict.dtd"><html xmlns="http://www.w3.org/1999/xhtml" lang="en"><head><meta http-equiv="Content-Type" content="text/html;charset=UTF-8"/><link rel="stylesheet" href="../jacoco-resources/report.css" type="text/css"/><link rel="shortcut icon" href="../jacoco-resources/report.gif" type="image/gif"/><title>SecurityHeadersConfig</title><script type="text/javascript" src="../jacoco-resources/sort.js"></script></head><body onload="initialSort(['breadcrumb'])"><div class="breadcrumb" id="breadcrumb"><span class="info"><a href="../jacoco-sessions.html" class="el_session">Sessions</a></span><a href="../index.html" class="el_report">dms-svc</a> &gt; <a href="index.html" class="el_package">com.ascentbusiness.dms_svc.config</a> &gt; <span class="el_class">SecurityHeadersConfig</span></div><h1>SecurityHeadersConfig</h1><table class="coverage" cellspacing="0" id="coveragetable"><thead><tr><td class="sortable" id="a" onclick="toggleSort(this)">Element</td><td class="down sortable bar" id="b" onclick="toggleSort(this)">Missed Instructions</td><td class="sortable ctr2" id="c" onclick="toggleSort(this)">Cov.</td><td class="sortable bar" id="d" onclick="toggleSort(this)">Missed Branches</td><td class="sortable ctr2" id="e" onclick="toggleSort(this)">Cov.</td><td class="sortable ctr1" id="f" onclick="toggleSort(this)">Missed</td><td class="sortable ctr2" id="g" onclick="toggleSort(this)">Cxty</td><td class="sortable ctr1" id="h" onclick="toggleSort(this)">Missed</td><td class="sortable ctr2" id="i" onclick="toggleSort(this)">Lines</td><td class="sortable ctr1" id="j" onclick="toggleSort(this)">Missed</td><td class="sortable ctr2" id="k" onclick="toggleSort(this)">Methods</td></tr></thead><tfoot><tr><td>Total</td><td class="bar">355 of 462</td><td class="ctr2">23%</td><td class="bar">41 of 47</td><td class="ctr2">12%</td><td class="ctr1">39</td><td class="ctr2">44</td><td class="ctr1">66</td><td class="ctr2">78</td><td class="ctr1">12</td><td class="ctr2">17</td></tr></tfoot><tbody><tr><td id="a1"><a href="SecurityHeadersConfig.java.html#L115" class="el_method">getAllSecurityHeaders()</a></td><td class="bar" id="b0"><img src="../jacoco-resources/redbar.gif" width="75" height="10" title="130" alt="130"/></td><td class="ctr2" id="c5">0%</td><td class="bar" id="d2"><img src="../jacoco-resources/redbar.gif" width="48" height="10" title="8" alt="8"/></td><td class="ctr2" id="e1">0%</td><td class="ctr1" id="f2">5</td><td class="ctr2" id="g2">5</td><td class="ctr1" id="h0">25</td><td class="ctr2" id="i0">25</td><td class="ctr1" id="j0">1</td><td class="ctr2" id="k0">1</td></tr><tr><td id="a0"><a href="SecurityHeadersConfig.java.html#L199" class="el_method">additionalSecurityHeadersWriter()</a></td><td class="bar" id="b1"><img src="../jacoco-resources/redbar.gif" width="64" height="10" title="111" alt="111"/><img src="../jacoco-resources/greenbar.gif" width="55" height="10" title="95" alt="95"/></td><td class="ctr2" id="c4">46%</td><td class="bar" id="d0"><img src="../jacoco-resources/redbar.gif" width="84" height="10" title="14" alt="14"/><img src="../jacoco-resources/greenbar.gif" width="36" height="10" title="6" alt="6"/></td><td class="ctr2" id="e0">30%</td><td class="ctr1" id="f0">10</td><td class="ctr2" id="g0">11</td><td class="ctr1" id="h3">8</td><td class="ctr2" id="i1">16</td><td class="ctr1" id="j12">0</td><td class="ctr2" id="k1">1</td></tr><tr><td id="a6"><a href="SecurityHeadersConfig.java.html#L91" class="el_method">getHSTSHeaders()</a></td><td class="bar" id="b2"><img src="../jacoco-resources/redbar.gif" width="23" height="10" title="40" alt="40"/></td><td class="ctr2" id="c6">0%</td><td class="bar" id="d3"><img src="../jacoco-resources/redbar.gif" width="36" height="10" title="6" alt="6"/></td><td class="ctr2" id="e2">0%</td><td class="ctr1" id="f3">4</td><td class="ctr2" id="g3">4</td><td class="ctr1" id="h1">10</td><td class="ctr2" id="i2">10</td><td class="ctr1" id="j1">1</td><td class="ctr2" id="k2">1</td></tr><tr><td id="a10"><a href="SecurityHeadersConfig.java.html#L171" class="el_method">getReferrerPolicyEnum()</a></td><td class="bar" id="b3"><img src="../jacoco-resources/redbar.gif" width="16" height="10" title="28" alt="28"/></td><td class="ctr2" id="c7">0%</td><td class="bar" id="d1"><img src="../jacoco-resources/redbar.gif" width="54" height="10" title="9" alt="9"/></td><td class="ctr2" id="e3">0%</td><td class="ctr1" id="f1">9</td><td class="ctr2" id="g1">9</td><td class="ctr1" id="h2">10</td><td class="ctr2" id="i3">10</td><td class="ctr1" id="j2">1</td><td class="ctr2" id="k3">1</td></tr><tr><td id="a3"><a href="SecurityHeadersConfig.java.html#L77" class="el_method">getCSPHeaders()</a></td><td class="bar" id="b4"><img src="../jacoco-resources/redbar.gif" width="12" height="10" title="22" alt="22"/></td><td class="ctr2" id="c8">0%</td><td class="bar" id="d4"><img src="../jacoco-resources/redbar.gif" width="24" height="10" title="4" alt="4"/></td><td class="ctr2" id="e4">0%</td><td class="ctr1" id="f4">3</td><td class="ctr2" id="g4">3</td><td class="ctr1" id="h4">5</td><td class="ctr2" id="i4">5</td><td class="ctr1" id="j3">1</td><td class="ctr2" id="k4">1</td></tr><tr><td id="a4"><a href="SecurityHeadersConfig.java.html#L250" class="el_method">getCspPolicy()</a></td><td class="bar" id="b5"><img src="../jacoco-resources/redbar.gif" width="1" height="10" title="3" alt="3"/></td><td class="ctr2" id="c9">0%</td><td class="bar" id="d5"/><td class="ctr2" id="e5">n/a</td><td class="ctr1" id="f5">1</td><td class="ctr2" id="g5">1</td><td class="ctr1" id="h5">1</td><td class="ctr2" id="i5">1</td><td class="ctr1" id="j4">1</td><td class="ctr2" id="k5">1</td></tr><tr><td id="a12"><a href="SecurityHeadersConfig.java.html#L251" class="el_method">isCspReportOnly()</a></td><td class="bar" id="b6"><img src="../jacoco-resources/redbar.gif" width="1" height="10" title="3" alt="3"/></td><td class="ctr2" id="c10">0%</td><td class="bar" id="d6"/><td class="ctr2" id="e6">n/a</td><td class="ctr1" id="f6">1</td><td class="ctr2" id="g6">1</td><td class="ctr1" id="h6">1</td><td class="ctr2" id="i6">1</td><td class="ctr1" id="j5">1</td><td class="ctr2" id="k6">1</td></tr><tr><td id="a7"><a href="SecurityHeadersConfig.java.html#L253" class="el_method">getHstsMaxAge()</a></td><td class="bar" id="b7"><img src="../jacoco-resources/redbar.gif" width="1" height="10" title="3" alt="3"/></td><td class="ctr2" id="c11">0%</td><td class="bar" id="d7"/><td class="ctr2" id="e7">n/a</td><td class="ctr1" id="f7">1</td><td class="ctr2" id="g7">1</td><td class="ctr1" id="h7">1</td><td class="ctr2" id="i7">1</td><td class="ctr1" id="j6">1</td><td class="ctr2" id="k7">1</td></tr><tr><td id="a14"><a href="SecurityHeadersConfig.java.html#L254" class="el_method">isHstsIncludeSubdomains()</a></td><td class="bar" id="b8"><img src="../jacoco-resources/redbar.gif" width="1" height="10" title="3" alt="3"/></td><td class="ctr2" id="c12">0%</td><td class="bar" id="d8"/><td class="ctr2" id="e8">n/a</td><td class="ctr1" id="f8">1</td><td class="ctr2" id="g8">1</td><td class="ctr1" id="h8">1</td><td class="ctr2" id="i8">1</td><td class="ctr1" id="j7">1</td><td class="ctr2" id="k8">1</td></tr><tr><td id="a15"><a href="SecurityHeadersConfig.java.html#L255" class="el_method">isHstsPreload()</a></td><td class="bar" id="b9"><img src="../jacoco-resources/redbar.gif" width="1" height="10" title="3" alt="3"/></td><td class="ctr2" id="c13">0%</td><td class="bar" id="d9"/><td class="ctr2" id="e9">n/a</td><td class="ctr1" id="f9">1</td><td class="ctr2" id="g9">1</td><td class="ctr1" id="h9">1</td><td class="ctr2" id="i9">1</td><td class="ctr1" id="j8">1</td><td class="ctr2" id="k9">1</td></tr><tr><td id="a2"><a href="SecurityHeadersConfig.java.html#L257" class="el_method">getContentTypeOptions()</a></td><td class="bar" id="b10"><img src="../jacoco-resources/redbar.gif" width="1" height="10" title="3" alt="3"/></td><td class="ctr2" id="c14">0%</td><td class="bar" id="d10"/><td class="ctr2" id="e10">n/a</td><td class="ctr1" id="f10">1</td><td class="ctr2" id="g10">1</td><td class="ctr1" id="h10">1</td><td class="ctr2" id="i10">1</td><td class="ctr1" id="j9">1</td><td class="ctr2" id="k10">1</td></tr><tr><td id="a9"><a href="SecurityHeadersConfig.java.html#L258" class="el_method">getReferrerPolicy()</a></td><td class="bar" id="b11"><img src="../jacoco-resources/redbar.gif" width="1" height="10" title="3" alt="3"/></td><td class="ctr2" id="c15">0%</td><td class="bar" id="d11"/><td class="ctr2" id="e11">n/a</td><td class="ctr1" id="f11">1</td><td class="ctr2" id="g11">1</td><td class="ctr1" id="h11">1</td><td class="ctr2" id="i11">1</td><td class="ctr1" id="j10">1</td><td class="ctr2" id="k11">1</td></tr><tr><td id="a8"><a href="SecurityHeadersConfig.java.html#L259" class="el_method">getPermissionsPolicy()</a></td><td class="bar" id="b12"><img src="../jacoco-resources/redbar.gif" width="1" height="10" title="3" alt="3"/></td><td class="ctr2" id="c16">0%</td><td class="bar" id="d12"/><td class="ctr2" id="e12">n/a</td><td class="ctr1" id="f12">1</td><td class="ctr2" id="g12">1</td><td class="ctr1" id="h12">1</td><td class="ctr2" id="i12">1</td><td class="ctr1" id="j11">1</td><td class="ctr2" id="k12">1</td></tr><tr><td id="a16"><a href="SecurityHeadersConfig.java.html#L17" class="el_method">SecurityHeadersConfig()</a></td><td class="bar" id="b13"><img src="../jacoco-resources/greenbar.gif" width="1" height="10" title="3" alt="3"/></td><td class="ctr2" id="c0">100%</td><td class="bar" id="d13"/><td class="ctr2" id="e13">n/a</td><td class="ctr1" id="f13">0</td><td class="ctr2" id="g13">1</td><td class="ctr1" id="h13">0</td><td class="ctr2" id="i13">1</td><td class="ctr1" id="j13">0</td><td class="ctr2" id="k13">1</td></tr><tr><td id="a11"><a href="SecurityHeadersConfig.java.html#L249" class="el_method">isCspEnabled()</a></td><td class="bar" id="b14"><img src="../jacoco-resources/greenbar.gif" width="1" height="10" title="3" alt="3"/></td><td class="ctr2" id="c1">100%</td><td class="bar" id="d14"/><td class="ctr2" id="e14">n/a</td><td class="ctr1" id="f14">0</td><td class="ctr2" id="g14">1</td><td class="ctr1" id="h14">0</td><td class="ctr2" id="i14">1</td><td class="ctr1" id="j14">0</td><td class="ctr2" id="k14">1</td></tr><tr><td id="a13"><a href="SecurityHeadersConfig.java.html#L252" class="el_method">isHstsEnabled()</a></td><td class="bar" id="b15"><img src="../jacoco-resources/greenbar.gif" width="1" height="10" title="3" alt="3"/></td><td class="ctr2" id="c2">100%</td><td class="bar" id="d15"/><td class="ctr2" id="e15">n/a</td><td class="ctr1" id="f15">0</td><td class="ctr2" id="g15">1</td><td class="ctr1" id="h15">0</td><td class="ctr2" id="i15">1</td><td class="ctr1" id="j15">0</td><td class="ctr2" id="k15">1</td></tr><tr><td id="a5"><a href="SecurityHeadersConfig.java.html#L256" class="el_method">getFrameOptions()</a></td><td class="bar" id="b16"><img src="../jacoco-resources/greenbar.gif" width="1" height="10" title="3" alt="3"/></td><td class="ctr2" id="c3">100%</td><td class="bar" id="d16"/><td class="ctr2" id="e16">n/a</td><td class="ctr1" id="f16">0</td><td class="ctr2" id="g16">1</td><td class="ctr1" id="h16">0</td><td class="ctr2" id="i16">1</td><td class="ctr1" id="j16">0</td><td class="ctr2" id="k16">1</td></tr></tbody></table><div class="footer"><span class="right">Created with <a href="http://www.jacoco.org/jacoco">JaCoCo</a> 0.8.10.202304240956</span></div></body></html>