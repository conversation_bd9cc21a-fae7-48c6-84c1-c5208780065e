# Shared Infrastructure Docker Compose
# This file contains common services that can be shared between DMS and Notification services
version: '3.8'

services:
  # Shared MySQL Database
  grc-mysql-shared:
    image: mysql:8.0
    container_name: grc-mysql-shared
    environment:
      - MYSQL_ROOT_PASSWORD=${MYSQL_ROOT_PASSWORD:-root_password}
      - MYSQL_DATABASE=${MYSQL_DATABASE:-grc_platform}
    ports:
      - "${MYSQL_PORT:-3306}:3306"
    volumes:
      - mysql_shared_data:/var/lib/mysql
      - ./docker/mysql/shared-init:/docker-entrypoint-initdb.d:ro
    networks:
      - grc-shared-network
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "mysqladmin", "ping", "-h", "localhost", "-u", "root", "-p${MYSQL_ROOT_PASSWORD:-root_password}"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 30s
    command: --default-authentication-plugin=mysql_native_password

  # Shared Redis Cache
  grc-redis-shared:
    image: redis:7-alpine
    container_name: grc-redis-shared
    command: redis-server --requirepass ${REDIS_PASSWORD:-redis_password} --appendonly yes
    ports:
      - "${REDIS_PORT:-6379}:6379"
    volumes:
      - redis_shared_data:/data
    networks:
      - grc-shared-network
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "redis-cli", "-a", "${REDIS_PASSWORD:-redis_password}", "ping"]
      interval: 30s
      timeout: 10s
      retries: 3

  # Shared RabbitMQ Message Broker
  grc-rabbitmq-shared:
    image: rabbitmq:3.12-management-alpine
    container_name: grc-rabbitmq-shared
    environment:
      - RABBITMQ_DEFAULT_USER=${RABBITMQ_USER:-admin}
      - RABBITMQ_DEFAULT_PASS=${RABBITMQ_PASSWORD:-admin123}
      - RABBITMQ_DEFAULT_VHOST=${RABBITMQ_VHOST:-/}
    ports:
      - "${RABBITMQ_PORT:-5672}:5672"
      - "${RABBITMQ_MANAGEMENT_PORT:-15672}:15672"
    volumes:
      - rabbitmq_shared_data:/var/lib/rabbitmq
    networks:
      - grc-shared-network
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "rabbitmq-diagnostics", "ping"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 30s

  # Shared Elasticsearch for Search
  grc-elasticsearch-shared:
    image: docker.elastic.co/elasticsearch/elasticsearch:8.11.0
    container_name: grc-elasticsearch-shared
    environment:
      - discovery.type=single-node
      - xpack.security.enabled=false
      - "ES_JAVA_OPTS=-Xms${ELASTICSEARCH_HEAP:-512m} -Xmx${ELASTICSEARCH_HEAP:-512m}"
    ports:
      - "${ELASTICSEARCH_PORT:-9200}:9200"
      - "${ELASTICSEARCH_TRANSPORT_PORT:-9300}:9300"
    volumes:
      - elasticsearch_shared_data:/usr/share/elasticsearch/data
    networks:
      - grc-shared-network
    restart: unless-stopped
    healthcheck:
      test: ["CMD-SHELL", "curl -f http://localhost:9200/_cluster/health || exit 1"]
      interval: 30s
      timeout: 10s
      retries: 3

  # Shared Zipkin for Distributed Tracing
  grc-zipkin-shared:
    image: openzipkin/zipkin:latest
    container_name: grc-zipkin-shared
    ports:
      - "${ZIPKIN_PORT:-9411}:9411"
    environment:
      - JAVA_OPTS=-Xms256m -Xmx512m
    networks:
      - grc-shared-network
    restart: unless-stopped
    healthcheck:
      test: ["CMD-SHELL", "curl -f http://localhost:9411/health || exit 1"]
      interval: 30s
      timeout: 10s
      retries: 5
      start_period: 60s
    deploy:
      resources:
        limits:
          cpus: '0.5'
          memory: 768M
        reservations:
          cpus: '0.1'
          memory: 256M

  # Shared Prometheus for Metrics
  grc-prometheus-shared:
    image: prom/prometheus:latest
    container_name: grc-prometheus-shared
    ports:
      - "${PROMETHEUS_PORT:-9090}:9090"
    volumes:
      - ./docker/prometheus/shared-prometheus.yml:/etc/prometheus/prometheus.yml:ro
      - ./docker/prometheus/alerts.yml:/etc/prometheus/alerts.yml:ro
      - prometheus_shared_data:/prometheus
    command:
      - '--config.file=/etc/prometheus/prometheus.yml'
      - '--storage.tsdb.path=/prometheus'
      - '--web.console.libraries=/etc/prometheus/console_libraries'
      - '--web.console.templates=/etc/prometheus/consoles'
      - '--storage.tsdb.retention.time=${PROMETHEUS_RETENTION:-200h}'
      - '--web.enable-lifecycle'
    networks:
      - grc-shared-network
    restart: unless-stopped

  # Shared Grafana for Dashboards
  grc-grafana-shared:
    image: grafana/grafana:latest
    container_name: grc-grafana-shared
    ports:
      - "${GRAFANA_PORT:-3000}:3000"
    environment:
      - GF_SECURITY_ADMIN_PASSWORD=${GRAFANA_ADMIN_PASSWORD:-admin}
      - GF_SECURITY_SECRET_KEY=${GRAFANA_SECRET_KEY:-grafana_secret}
      - GF_USERS_ALLOW_SIGN_UP=false
    volumes:
      - grafana_shared_data:/var/lib/grafana
      - ./docker/grafana/provisioning:/etc/grafana/provisioning:ro
      - ./docker/grafana/provisioning/dashboards:/var/lib/grafana/dashboards:ro
    networks:
      - grc-shared-network
    restart: unless-stopped

  # Shared Nginx Reverse Proxy
  grc-nginx-shared:
    image: nginx:alpine
    container_name: grc-nginx-shared
    ports:
      - "${NGINX_HTTP_PORT:-80}:80"
      - "${NGINX_HTTPS_PORT:-443}:443"
    volumes:
      - ./nginx/nginx.conf:/etc/nginx/nginx.conf:ro
      - ./nginx/conf.d:/etc/nginx/conf.d:ro
      - ./ssl:/etc/nginx/ssl:ro
      - nginx_shared_logs:/var/log/nginx
    networks:
      - grc-shared-network
    restart: unless-stopped
    deploy:
      resources:
        limits:
          cpus: '0.5'
          memory: 512M
        reservations:
          cpus: '0.1'
          memory: 128M

volumes:
  mysql_shared_data:
    driver: local
  redis_shared_data:
    driver: local
  rabbitmq_shared_data:
    driver: local
  elasticsearch_shared_data:
    driver: local
  prometheus_shared_data:
    driver: local
  grafana_shared_data:
    driver: local
  nginx_shared_logs:
    driver: local

networks:
  grc-shared-network:
    external: true