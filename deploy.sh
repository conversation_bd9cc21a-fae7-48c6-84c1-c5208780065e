#!/bin/bash

# Unified GRC Platform Deployment Script
# This script simplifies deployment for both development and UAT environments
# Usage: ./deploy.sh [dev|uat] [--clean] [--build]

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Function to print colored output
print_status() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Function to show usage
show_usage() {
    echo "Usage: $0 [dev|uat] [--clean] [--build]"
    echo ""
    echo "Arguments:"
    echo "  dev     Deploy in development environment"
    echo "  uat     Deploy in UAT environment"
    echo ""
    echo "Options:"
    echo "  --clean    Clean Docker resources before deployment"
    echo "  --build    Force rebuild of Docker images"
    echo ""
    echo "Examples:"
    echo "  $0 dev                    # Deploy development environment"
    echo "  $0 uat --clean           # Deploy UAT with cleanup"
    echo "  $0 dev --clean --build   # Deploy dev with cleanup and rebuild"
}

# Parse arguments
ENVIRONMENT=""
CLEAN_MODE=false
BUILD_MODE=false

for arg in "$@"; do
    case $arg in
        dev|uat)
            ENVIRONMENT=$arg
            ;;
        --clean)
            CLEAN_MODE=true
            ;;
        --build)
            BUILD_MODE=true
            ;;
        --help|-h)
            show_usage
            exit 0
            ;;
        *)
            print_error "Unknown argument: $arg"
            show_usage
            exit 1
            ;;
    esac
done

# Validate environment argument
if [ -z "$ENVIRONMENT" ]; then
    print_error "Environment argument is required!"
    show_usage
    exit 1
fi

# Set environment-specific variables
if [ "$ENVIRONMENT" = "dev" ]; then
    ENV_FILE=".env.dev"
    COMPOSE_FILES="-f docker-compose.shared.yml -f docker-compose.services.yml"
    SPRING_PROFILE="dev"
    print_status "🚀 Starting GRC Platform Development Deployment..."
elif [ "$ENVIRONMENT" = "uat" ]; then
    ENV_FILE=".env.uat"
    COMPOSE_FILES="-f docker-compose.shared.yml -f docker-compose.uat.yml"
    SPRING_PROFILE="uat"
    print_status "🚀 Starting GRC Platform UAT Deployment..."
fi

# Check if environment file exists
if [ ! -f "$ENV_FILE" ]; then
    print_error "$ENV_FILE file not found!"
    exit 1
fi

# Load environment variables
print_status "📋 Loading environment variables from $ENV_FILE..."
set -a  # automatically export all variables
source $ENV_FILE
set +a  # disable automatic export

# Check if Docker is running
if ! docker info > /dev/null 2>&1; then
    print_error "Docker is not running. Please start Docker and try again."
    exit 1
fi

# Clean Docker resources if requested
if [ "$CLEAN_MODE" = true ]; then
    print_status "🧹 Cleaning Docker resources..."
    
    # Stop existing services
    print_status "Stopping existing services..."
    docker compose --env-file $ENV_FILE $COMPOSE_FILES down --remove-orphans 2>/dev/null || true
    
    # Clean up Docker resources
    print_status "Removing unused containers..."
    docker container prune -f || print_warning "Container cleanup had issues"
    
    print_status "Removing unused images..."
    docker image prune -a -f || print_warning "Image cleanup had issues"
    
    print_status "Removing unused volumes..."
    docker volume prune -f || print_warning "Volume cleanup had issues"
    
    print_status "Removing unused networks..."
    docker network prune -f || print_warning "Network cleanup had issues"
    
    print_success "Docker cleanup completed"
fi

# Create shared network
print_status "🌐 Creating shared network..."
docker network create grc-shared-network 2>/dev/null || print_warning "Network already exists or creation failed"

# Build and start services
if [ "$BUILD_MODE" = true ]; then
    print_status "🏗️ Building and starting services with forced rebuild..."
    docker compose --env-file $ENV_FILE $COMPOSE_FILES up -d --build --force-recreate
else
    print_status "🏗️ Starting services..."
    docker compose --env-file $ENV_FILE $COMPOSE_FILES up -d
fi

# Wait for shared services to be ready
print_status "⏳ Waiting for shared services to be ready..."
sleep 30

# Health checks for shared services
print_status "🔍 Checking shared services health..."

# Check MySQL
max_attempts=30
attempt=0
until docker exec grc-mysql-shared mysqladmin ping -h localhost -u root -p${DB_PASSWORD} --silent 2>/dev/null; do
    attempt=$((attempt + 1))
    if [ $attempt -eq $max_attempts ]; then
        print_warning "MySQL health check timeout after $max_attempts attempts"
        break
    fi
    echo "Waiting for MySQL... (attempt $attempt/$max_attempts)"
    sleep 5
done

# Check Redis
attempt=0
until docker exec grc-redis-shared redis-cli -a ${REDIS_PASSWORD} ping 2>/dev/null | grep -q PONG; do
    attempt=$((attempt + 1))
    if [ $attempt -eq $max_attempts ]; then
        print_warning "Redis health check timeout after $max_attempts attempts"
        break
    fi
    echo "Waiting for Redis... (attempt $attempt/$max_attempts)"
    sleep 3
done

# Wait for application services
print_status "⏳ Waiting for application services to start..."
sleep 45

# Health checks for application services
print_status "🔍 Checking application services health..."

# Check DMS Service
if curl -f -s http://localhost:9093/actuator/health > /dev/null; then
    print_success "✅ DMS service is healthy"
else
    print_warning "⚠️ DMS service health check failed"
fi

# Check Notification Service
if curl -f -s http://localhost:9091/actuator/health > /dev/null; then
    print_success "✅ Notification service is healthy"
else
    print_warning "⚠️ Notification service health check failed"
fi

# Show service status
print_status "📊 Service Status:"
docker compose --env-file $ENV_FILE $COMPOSE_FILES ps

echo ""
print_success "✅ $ENVIRONMENT deployment completed!"
echo ""
print_status "📊 Service URLs:"
if [ "$ENVIRONMENT" = "dev" ]; then
    echo "  🏢 DMS Service:           http://localhost:9093"
    echo "  🏢 DMS GraphiQL:          http://localhost:9093/graphiql"
    echo "  📧 Notification Service:  http://localhost:9091"
    echo "  📈 Grafana Dashboard:     http://localhost:3000 (admin/admin)"
    echo "  📊 Prometheus:            http://localhost:9090"
    echo "  🔍 Zipkin Tracing:        http://localhost:9411"
    echo "  🐰 RabbitMQ Management:   http://localhost:15672 (admin/admin123)"
elif [ "$ENVIRONMENT" = "uat" ]; then
    PUBLIC_IP=$(curl -s http://***************/latest/meta-data/public-ipv4 2>/dev/null || echo "localhost")
    echo "  🏢 DMS Service:           http://$PUBLIC_IP:9093"
    echo "  📧 Notification Service:  http://$PUBLIC_IP:9091"
    echo "  📈 Grafana Dashboard:     http://$PUBLIC_IP:3000"
    echo "  📊 Prometheus:            http://$PUBLIC_IP:9090"
    echo "  🔍 Zipkin Tracing:        http://$PUBLIC_IP:9411"
    echo "  🐰 RabbitMQ Management:   http://$PUBLIC_IP:15672"
fi

echo ""
print_status "📋 Useful Commands:"
echo "  View logs:    docker compose --env-file $ENV_FILE $COMPOSE_FILES logs -f"
echo "  Stop services: docker compose --env-file $ENV_FILE $COMPOSE_FILES down"
echo "  Restart:      $0 $ENVIRONMENT"
echo "  Clean restart: $0 $ENVIRONMENT --clean"

echo ""
print_status "🔧 Configuration:"
echo "  Environment:   $ENVIRONMENT"
echo "  Spring Profile: $SPRING_PROFILE"
echo "  Config File:   $ENV_FILE"
echo "  Compose Files: $COMPOSE_FILES"