<?xml version="1.0" encoding="UTF-8"?><!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Strict//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-strict.dtd"><html xmlns="http://www.w3.org/1999/xhtml" lang="en"><head><meta http-equiv="Content-Type" content="text/html;charset=UTF-8"/><link rel="stylesheet" href="../jacoco-resources/report.css" type="text/css"/><link rel="shortcut icon" href="../jacoco-resources/report.gif" type="image/gif"/><title>WebhookService</title><script type="text/javascript" src="../jacoco-resources/sort.js"></script></head><body onload="initialSort(['breadcrumb'])"><div class="breadcrumb" id="breadcrumb"><span class="info"><a href="../jacoco-sessions.html" class="el_session">Sessions</a></span><a href="../index.html" class="el_report">dms-svc</a> &gt; <a href="index.html" class="el_package">com.ascentbusiness.dms_svc.service</a> &gt; <span class="el_class">WebhookService</span></div><h1>WebhookService</h1><table class="coverage" cellspacing="0" id="coveragetable"><thead><tr><td class="sortable" id="a" onclick="toggleSort(this)">Element</td><td class="down sortable bar" id="b" onclick="toggleSort(this)">Missed Instructions</td><td class="sortable ctr2" id="c" onclick="toggleSort(this)">Cov.</td><td class="sortable bar" id="d" onclick="toggleSort(this)">Missed Branches</td><td class="sortable ctr2" id="e" onclick="toggleSort(this)">Cov.</td><td class="sortable ctr1" id="f" onclick="toggleSort(this)">Missed</td><td class="sortable ctr2" id="g" onclick="toggleSort(this)">Cxty</td><td class="sortable ctr1" id="h" onclick="toggleSort(this)">Missed</td><td class="sortable ctr2" id="i" onclick="toggleSort(this)">Lines</td><td class="sortable ctr1" id="j" onclick="toggleSort(this)">Missed</td><td class="sortable ctr2" id="k" onclick="toggleSort(this)">Methods</td></tr></thead><tfoot><tr><td>Total</td><td class="bar">880 of 902</td><td class="ctr2">2%</td><td class="bar">72 of 72</td><td class="ctr2">0%</td><td class="ctr1">63</td><td class="ctr2">65</td><td class="ctr1">201</td><td class="ctr2">203</td><td class="ctr1">27</td><td class="ctr2">29</td></tr></tfoot><tbody><tr><td id="a2"><a href="WebhookService.java.html#L251" class="el_method">attemptWebhookDelivery(WebhookDelivery)</a></td><td class="bar" id="b0"><img src="../jacoco-resources/redbar.gif" width="120" height="10" title="123" alt="123"/></td><td class="ctr2" id="c2">0%</td><td class="bar" id="d9"><img src="../jacoco-resources/redbar.gif" width="15" height="10" title="2" alt="2"/></td><td class="ctr2" id="e0">0%</td><td class="ctr1" id="f9">2</td><td class="ctr2" id="g9">2</td><td class="ctr1" id="h1">25</td><td class="ctr2" id="i1">25</td><td class="ctr1" id="j0">1</td><td class="ctr2" id="k0">1</td></tr><tr><td id="a25"><a href="WebhookService.java.html#L73" class="el_method">updateWebhookEndpoint(Long, WebhookEndpoint, String)</a></td><td class="bar" id="b1"><img src="../jacoco-resources/redbar.gif" width="113" height="10" title="116" alt="116"/></td><td class="ctr2" id="c3">0%</td><td class="bar" id="d10"><img src="../jacoco-resources/redbar.gif" width="15" height="10" title="2" alt="2"/></td><td class="ctr2" id="e1">0%</td><td class="ctr1" id="f10">2</td><td class="ctr2" id="g10">2</td><td class="ctr1" id="h0">29</td><td class="ctr2" id="i0">29</td><td class="ctr1" id="j1">1</td><td class="ctr2" id="k1">1</td></tr><tr><td id="a22"><a href="WebhookService.java.html#L343" class="el_method">shouldDeliverToEndpoint(WebhookEndpoint, SystemEvent)</a></td><td class="bar" id="b2"><img src="../jacoco-resources/redbar.gif" width="52" height="10" title="54" alt="54"/></td><td class="ctr2" id="c4">0%</td><td class="bar" id="d0"><img src="../jacoco-resources/redbar.gif" width="120" height="10" title="16" alt="16"/></td><td class="ctr2" id="e2">0%</td><td class="ctr1" id="f0">9</td><td class="ctr2" id="g0">9</td><td class="ctr1" id="h2">14</td><td class="ctr2" id="i2">14</td><td class="ctr1" id="j2">1</td><td class="ctr2" id="k2">1</td></tr><tr><td id="a26"><a href="WebhookService.java.html#L323" class="el_method">validateWebhookEndpoint(WebhookEndpoint)</a></td><td class="bar" id="b3"><img src="../jacoco-resources/redbar.gif" width="51" height="10" title="53" alt="53"/></td><td class="ctr2" id="c5">0%</td><td class="bar" id="d1"><img src="../jacoco-resources/redbar.gif" width="105" height="10" title="14" alt="14"/></td><td class="ctr2" id="e3">0%</td><td class="ctr1" id="f1">8</td><td class="ctr2" id="g1">8</td><td class="ctr1" id="h8">9</td><td class="ctr2" id="i8">9</td><td class="ctr1" id="j3">1</td><td class="ctr2" id="k3">1</td></tr><tr><td id="a4"><a href="WebhookService.java.html#L422" class="el_method">createDefaultPayload(SystemEvent)</a></td><td class="bar" id="b4"><img src="../jacoco-resources/redbar.gif" width="48" height="10" title="50" alt="50"/></td><td class="ctr2" id="c6">0%</td><td class="bar" id="d4"><img src="../jacoco-resources/redbar.gif" width="30" height="10" title="4" alt="4"/></td><td class="ctr2" id="e4">0%</td><td class="ctr1" id="f4">3</td><td class="ctr2" id="g4">3</td><td class="ctr1" id="h4">12</td><td class="ctr2" id="i4">12</td><td class="ctr1" id="j4">1</td><td class="ctr2" id="k4">1</td></tr><tr><td id="a13"><a href="WebhookService.java.html#L308" class="el_method">getWebhookStatistics()</a></td><td class="bar" id="b5"><img src="../jacoco-resources/redbar.gif" width="45" height="10" title="47" alt="47"/></td><td class="ctr2" id="c7">0%</td><td class="bar" id="d2"><img src="../jacoco-resources/redbar.gif" width="60" height="10" title="8" alt="8"/></td><td class="ctr2" id="e5">0%</td><td class="ctr1" id="f2">5</td><td class="ctr2" id="g2">5</td><td class="ctr1" id="h9">9</td><td class="ctr2" id="i9">9</td><td class="ctr1" id="j5">1</td><td class="ctr2" id="k5">1</td></tr><tr><td id="a20"><a href="WebhookService.java.html#L444" class="el_method">scheduleRetryDelivery(WebhookDelivery)</a></td><td class="bar" id="b6"><img src="../jacoco-resources/redbar.gif" width="42" height="10" title="44" alt="44"/></td><td class="ctr2" id="c8">0%</td><td class="bar" id="d12"/><td class="ctr2" id="e12">n/a</td><td class="ctr1" id="f12">1</td><td class="ctr2" id="g12">1</td><td class="ctr1" id="h3">13</td><td class="ctr2" id="i3">13</td><td class="ctr1" id="j6">1</td><td class="ctr2" id="k6">1</td></tr><tr><td id="a5"><a href="WebhookService.java.html#L45" class="el_method">createWebhookEndpoint(WebhookEndpoint, String)</a></td><td class="bar" id="b7"><img src="../jacoco-resources/redbar.gif" width="41" height="10" title="43" alt="43"/></td><td class="ctr2" id="c9">0%</td><td class="bar" id="d13"/><td class="ctr2" id="e13">n/a</td><td class="ctr1" id="f13">1</td><td class="ctr2" id="g13">1</td><td class="ctr1" id="h5">12</td><td class="ctr2" id="i5">12</td><td class="ctr1" id="j7">1</td><td class="ctr2" id="k7">1</td></tr><tr><td id="a1"><a href="WebhookService.java.html#L395" class="el_method">addAuthenticationHeaders(HttpHeaders, WebhookEndpoint)</a></td><td class="bar" id="b8"><img src="../jacoco-resources/redbar.gif" width="40" height="10" title="41" alt="41"/></td><td class="ctr2" id="c10">0%</td><td class="bar" id="d3"><img src="../jacoco-resources/redbar.gif" width="60" height="10" title="8" alt="8"/></td><td class="ctr2" id="e6">0%</td><td class="ctr1" id="f3">5</td><td class="ctr2" id="g3">5</td><td class="ctr1" id="h10">8</td><td class="ctr2" id="i10">8</td><td class="ctr1" id="j8">1</td><td class="ctr2" id="k8">1</td></tr><tr><td id="a27"><a href="WebhookService.java.html#L153" class="el_method">verifyWebhookEndpoint(String)</a></td><td class="bar" id="b9"><img src="../jacoco-resources/redbar.gif" width="38" height="10" title="39" alt="39"/></td><td class="ctr2" id="c11">0%</td><td class="bar" id="d14"/><td class="ctr2" id="e14">n/a</td><td class="ctr1" id="f14">1</td><td class="ctr2" id="g14">1</td><td class="ctr1" id="h7">10</td><td class="ctr2" id="i7">10</td><td class="ctr1" id="j9">1</td><td class="ctr2" id="k9">1</td></tr><tr><td id="a7"><a href="WebhookService.java.html#L213" class="el_method">deliverWebhook(SystemEvent)</a></td><td class="bar" id="b10"><img src="../jacoco-resources/redbar.gif" width="34" height="10" title="35" alt="35"/></td><td class="ctr2" id="c12">0%</td><td class="bar" id="d5"><img src="../jacoco-resources/redbar.gif" width="30" height="10" title="4" alt="4"/></td><td class="ctr2" id="e7">0%</td><td class="ctr1" id="f5">3</td><td class="ctr2" id="g5">3</td><td class="ctr1" id="h13">7</td><td class="ctr2" id="i13">7</td><td class="ctr1" id="j10">1</td><td class="ctr2" id="k10">1</td></tr><tr><td id="a21"><a href="WebhookService.java.html#L228" class="el_method">scheduleWebhookDelivery(WebhookEndpoint, SystemEvent)</a></td><td class="bar" id="b11"><img src="../jacoco-resources/redbar.gif" width="34" height="10" title="35" alt="35"/></td><td class="ctr2" id="c13">0%</td><td class="bar" id="d15"/><td class="ctr2" id="e15">n/a</td><td class="ctr1" id="f15">1</td><td class="ctr2" id="g15">1</td><td class="ctr1" id="h6">12</td><td class="ctr2" id="i6">12</td><td class="ctr1" id="j11">1</td><td class="ctr2" id="k11">1</td></tr><tr><td id="a0"><a href="WebhookService.java.html#L175" class="el_method">activateWebhookEndpoint(Long, String)</a></td><td class="bar" id="b12"><img src="../jacoco-resources/redbar.gif" width="31" height="10" title="32" alt="32"/></td><td class="ctr2" id="c14">0%</td><td class="bar" id="d16"/><td class="ctr2" id="e16">n/a</td><td class="ctr1" id="f16">1</td><td class="ctr2" id="g16">1</td><td class="ctr1" id="h11">8</td><td class="ctr2" id="i11">8</td><td class="ctr1" id="j12">1</td><td class="ctr2" id="k12">1</td></tr><tr><td id="a6"><a href="WebhookService.java.html#L194" class="el_method">deactivateWebhookEndpoint(Long, String)</a></td><td class="bar" id="b13"><img src="../jacoco-resources/redbar.gif" width="31" height="10" title="32" alt="32"/></td><td class="ctr2" id="c15">0%</td><td class="bar" id="d17"/><td class="ctr2" id="e17">n/a</td><td class="ctr1" id="f17">1</td><td class="ctr2" id="g17">1</td><td class="ctr1" id="h12">8</td><td class="ctr2" id="i12">8</td><td class="ctr1" id="j13">1</td><td class="ctr2" id="k13">1</td></tr><tr><td id="a17"><a href="WebhookService.java.html#L376" class="el_method">prepareHeaders(WebhookEndpoint)</a></td><td class="bar" id="b14"><img src="../jacoco-resources/redbar.gif" width="26" height="10" title="27" alt="27"/></td><td class="ctr2" id="c16">0%</td><td class="bar" id="d6"><img src="../jacoco-resources/redbar.gif" width="30" height="10" title="4" alt="4"/></td><td class="ctr2" id="e8">0%</td><td class="ctr1" id="f6">3</td><td class="ctr2" id="g6">3</td><td class="ctr1" id="h14">7</td><td class="ctr2" id="i14">7</td><td class="ctr1" id="j14">1</td><td class="ctr2" id="k14">1</td></tr><tr><td id="a3"><a href="WebhookService.java.html#L462" class="el_method">calculateRetryDelay(WebhookDelivery)</a></td><td class="bar" id="b15"><img src="../jacoco-resources/redbar.gif" width="23" height="10" title="24" alt="24"/></td><td class="ctr2" id="c17">0%</td><td class="bar" id="d11"><img src="../jacoco-resources/redbar.gif" width="15" height="10" title="2" alt="2"/></td><td class="ctr2" id="e9">0%</td><td class="ctr1" id="f11">2</td><td class="ctr2" id="g11">2</td><td class="ctr1" id="h15">4</td><td class="ctr2" id="i15">4</td><td class="ctr1" id="j15">1</td><td class="ctr2" id="k15">1</td></tr><tr><td id="a18"><a href="WebhookService.java.html#L407" class="el_method">preparePayload(WebhookEndpoint, SystemEvent)</a></td><td class="bar" id="b16"><img src="../jacoco-resources/redbar.gif" width="17" height="10" title="18" alt="18"/></td><td class="ctr2" id="c18">0%</td><td class="bar" id="d7"><img src="../jacoco-resources/redbar.gif" width="30" height="10" title="4" alt="4"/></td><td class="ctr2" id="e10">0%</td><td class="ctr1" id="f7">3</td><td class="ctr2" id="g7">3</td><td class="ctr1" id="h16">3</td><td class="ctr2" id="i16">3</td><td class="ctr1" id="j16">1</td><td class="ctr2" id="k16">1</td></tr><tr><td id="a23"><a href="WebhookService.java.html#L439" class="el_method">shouldRetryDelivery(WebhookDelivery)</a></td><td class="bar" id="b17"><img src="../jacoco-resources/redbar.gif" width="14" height="10" title="15" alt="15"/></td><td class="ctr2" id="c19">0%</td><td class="bar" id="d8"><img src="../jacoco-resources/redbar.gif" width="30" height="10" title="4" alt="4"/></td><td class="ctr2" id="e11">0%</td><td class="ctr1" id="f8">3</td><td class="ctr2" id="g8">3</td><td class="ctr1" id="h17">2</td><td class="ctr2" id="i17">2</td><td class="ctr1" id="j17">1</td><td class="ctr2" id="k17">1</td></tr><tr><td id="a15"><a href="WebhookService.java.html#L387" class="el_method">lambda$prepareHeaders$2(HttpHeaders, Map.Entry)</a></td><td class="bar" id="b18"><img src="../jacoco-resources/redbar.gif" width="9" height="10" title="10" alt="10"/></td><td class="ctr2" id="c20">0%</td><td class="bar" id="d18"/><td class="ctr2" id="e18">n/a</td><td class="ctr1" id="f18">1</td><td class="ctr2" id="g18">1</td><td class="ctr1" id="h18">2</td><td class="ctr2" id="i18">2</td><td class="ctr1" id="j18">1</td><td class="ctr2" id="k18">1</td></tr><tr><td id="a10"><a href="WebhookService.java.html#L121" class="el_method">getWebhookEndpointById(Long)</a></td><td class="bar" id="b19"><img src="../jacoco-resources/redbar.gif" width="8" height="10" title="9" alt="9"/></td><td class="ctr2" id="c21">0%</td><td class="bar" id="d19"/><td class="ctr2" id="e19">n/a</td><td class="ctr1" id="f19">1</td><td class="ctr2" id="g19">1</td><td class="ctr1" id="h19">2</td><td class="ctr2" id="i19">2</td><td class="ctr1" id="j19">1</td><td class="ctr2" id="k19">1</td></tr><tr><td id="a11"><a href="WebhookService.java.html#L138" class="el_method">getWebhookEndpointsByCreator(String, Pageable)</a></td><td class="bar" id="b20"><img src="../jacoco-resources/redbar.gif" width="5" height="10" title="6" alt="6"/></td><td class="ctr2" id="c22">0%</td><td class="bar" id="d20"/><td class="ctr2" id="e20">n/a</td><td class="ctr1" id="f20">1</td><td class="ctr2" id="g20">1</td><td class="ctr1" id="h20">1</td><td class="ctr2" id="i20">1</td><td class="ctr1" id="j20">1</td><td class="ctr2" id="k20">1</td></tr><tr><td id="a12"><a href="WebhookService.java.html#L146" class="el_method">getWebhookEndpointsForEvent(String)</a></td><td class="bar" id="b21"><img src="../jacoco-resources/redbar.gif" width="5" height="10" title="6" alt="6"/></td><td class="ctr2" id="c23">0%</td><td class="bar" id="d21"/><td class="ctr2" id="e21">n/a</td><td class="ctr1" id="f21">1</td><td class="ctr2" id="g21">1</td><td class="ctr1" id="h21">1</td><td class="ctr2" id="i21">1</td><td class="ctr1" id="j21">1</td><td class="ctr2" id="k21">1</td></tr><tr><td id="a14"><a href="WebhookService.java.html#L122" class="el_method">lambda$getWebhookEndpointById$0(Long)</a></td><td class="bar" id="b22"><img src="../jacoco-resources/redbar.gif" width="5" height="10" title="6" alt="6"/></td><td class="ctr2" id="c24">0%</td><td class="bar" id="d22"/><td class="ctr2" id="e22">n/a</td><td class="ctr1" id="f22">1</td><td class="ctr2" id="g22">1</td><td class="ctr1" id="h22">1</td><td class="ctr2" id="i22">1</td><td class="ctr1" id="j22">1</td><td class="ctr2" id="k22">1</td></tr><tr><td id="a16"><a href="WebhookService.java.html#L156" class="el_method">lambda$verifyWebhookEndpoint$1()</a></td><td class="bar" id="b23"><img src="../jacoco-resources/redbar.gif" width="4" height="10" title="5" alt="5"/></td><td class="ctr2" id="c25">0%</td><td class="bar" id="d23"/><td class="ctr2" id="e23">n/a</td><td class="ctr1" id="f23">1</td><td class="ctr2" id="g23">1</td><td class="ctr1" id="h23">1</td><td class="ctr2" id="i23">1</td><td class="ctr1" id="j23">1</td><td class="ctr2" id="k23">1</td></tr><tr><td id="a9"><a href="WebhookService.java.html#L130" class="el_method">getActiveWebhookEndpoints()</a></td><td class="bar" id="b24"><img src="../jacoco-resources/redbar.gif" width="3" height="10" title="4" alt="4"/></td><td class="ctr2" id="c26">0%</td><td class="bar" id="d24"/><td class="ctr2" id="e24">n/a</td><td class="ctr1" id="f24">1</td><td class="ctr2" id="g24">1</td><td class="ctr1" id="h24">1</td><td class="ctr2" id="i24">1</td><td class="ctr1" id="j24">1</td><td class="ctr2" id="k24">1</td></tr><tr><td id="a19"><a href="WebhookService.java.html#L417" class="el_method">processPayloadTemplate(String, SystemEvent)</a></td><td class="bar" id="b25"><img src="../jacoco-resources/redbar.gif" width="3" height="10" title="4" alt="4"/></td><td class="ctr2" id="c27">0%</td><td class="bar" id="d25"/><td class="ctr2" id="e25">n/a</td><td class="ctr1" id="f25">1</td><td class="ctr2" id="g25">1</td><td class="ctr1" id="h25">1</td><td class="ctr2" id="i25">1</td><td class="ctr1" id="j25">1</td><td class="ctr2" id="k25">1</td></tr><tr><td id="a8"><a href="WebhookService.java.html#L372" class="el_method">evaluateEventFilters(JsonNode, SystemEvent)</a></td><td class="bar" id="b26"><img src="../jacoco-resources/redbar.gif" width="1" height="10" title="2" alt="2"/></td><td class="ctr2" id="c28">0%</td><td class="bar" id="d26"/><td class="ctr2" id="e26">n/a</td><td class="ctr1" id="f26">1</td><td class="ctr2" id="g26">1</td><td class="ctr1" id="h26">1</td><td class="ctr2" id="i26">1</td><td class="ctr1" id="j26">1</td><td class="ctr2" id="k26">1</td></tr><tr><td id="a28"><a href="WebhookService.java.html#L30" class="el_method">WebhookService(WebhookEndpointRepository, WebhookDeliveryRepository, AuditService, RestTemplate, ObjectMapper)</a></td><td class="bar" id="b27"><img src="../jacoco-resources/greenbar.gif" width="17" height="10" title="18" alt="18"/></td><td class="ctr2" id="c0">100%</td><td class="bar" id="d27"/><td class="ctr2" id="e27">n/a</td><td class="ctr1" id="f27">0</td><td class="ctr2" id="g27">1</td><td class="ctr1" id="h27">0</td><td class="ctr2" id="i27">1</td><td class="ctr1" id="j27">0</td><td class="ctr2" id="k27">1</td></tr><tr><td id="a24"><a href="WebhookService.java.html#L31" class="el_method">static {...}</a></td><td class="bar" id="b28"><img src="../jacoco-resources/greenbar.gif" width="3" height="10" title="4" alt="4"/></td><td class="ctr2" id="c1">100%</td><td class="bar" id="d28"/><td class="ctr2" id="e28">n/a</td><td class="ctr1" id="f28">0</td><td class="ctr2" id="g28">1</td><td class="ctr1" id="h28">0</td><td class="ctr2" id="i28">1</td><td class="ctr1" id="j28">0</td><td class="ctr2" id="k28">1</td></tr></tbody></table><div class="footer"><span class="right">Created with <a href="http://www.jacoco.org/jacoco">JaCoCo</a> 0.8.10.202304240956</span></div></body></html>