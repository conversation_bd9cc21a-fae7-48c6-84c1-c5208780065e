<?xml version="1.0" encoding="UTF-8"?><!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Strict//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-strict.dtd"><html xmlns="http://www.w3.org/1999/xhtml" lang="en"><head><meta http-equiv="Content-Type" content="text/html;charset=UTF-8"/><link rel="stylesheet" href="../jacoco-resources/report.css" type="text/css"/><link rel="shortcut icon" href="../jacoco-resources/report.gif" type="image/gif"/><title>ConversionGraphQLResolver.java</title><link rel="stylesheet" href="../jacoco-resources/prettify.css" type="text/css"/><script type="text/javascript" src="../jacoco-resources/prettify.js"></script></head><body onload="window['PR_TAB_WIDTH']=4;prettyPrint()"><div class="breadcrumb" id="breadcrumb"><span class="info"><a href="../jacoco-sessions.html" class="el_session">Sessions</a></span><a href="../index.html" class="el_report">dms-svc</a> &gt; <a href="index.source.html" class="el_package">com.ascentbusiness.dms_svc.resolver</a> &gt; <span class="el_source">ConversionGraphQLResolver.java</span></div><h1>ConversionGraphQLResolver.java</h1><pre class="source lang-java linenums">package com.ascentbusiness.dms_svc.resolver;

import com.ascentbusiness.dms_svc.dto.*;
import com.ascentbusiness.dms_svc.enums.*;
import com.ascentbusiness.dms_svc.security.UserContext;
import com.ascentbusiness.dms_svc.service.MarkdownToWordConversionService;
import com.ascentbusiness.dms_svc.service.PdfToWordConversionService;
import com.ascentbusiness.dms_svc.service.WordToPdfConversionService;
import com.ascentbusiness.dms_svc.util.CorrelationIdUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.graphql.data.method.annotation.Argument;
import org.springframework.graphql.data.method.annotation.MutationMapping;
import org.springframework.graphql.data.method.annotation.QueryMapping;
import org.springframework.stereotype.Controller;

import java.time.LocalDateTime;
import java.util.Map;
import java.util.UUID;
import java.util.concurrent.ConcurrentHashMap;

/**
 * GraphQL resolver for file conversion operations.
 * This resolver implements the conversion-schema.graphqls operations to replace REST endpoints.
 * 
 * Provides comprehensive file conversion functionality including:
 * - Markdown to Word conversion
 * - PDF to Word conversion  
 * - Word to PDF conversion
 * - Conversion progress tracking
 * - Conversion statistics and history
 */
@Controller
<span class="fc" id="L34">@Slf4j</span>
<span class="fc" id="L35">public class ConversionGraphQLResolver {</span>

    @Autowired
    private MarkdownToWordConversionService markdownConversionService;
    
    @Autowired
    private PdfToWordConversionService pdfToWordConversionService;
    
    @Autowired
    private WordToPdfConversionService wordToPdfConversionService;
    
    @Autowired
    private UserContext userContext;
    
    // In-memory storage for conversion progress (in production, use Redis or database)
<span class="fc" id="L50">    private final Map&lt;String, ConversionProgress&gt; conversionProgressMap = new ConcurrentHashMap&lt;&gt;();</span>
<span class="fc" id="L51">    private final Map&lt;String, ConversionResult&gt; conversionResultMap = new ConcurrentHashMap&lt;&gt;();</span>
<span class="fc" id="L52">    private final Map&lt;String, BatchConversionResult&gt; batchConversionResultMap = new ConcurrentHashMap&lt;&gt;();</span>

    // ===== CONVERSION MUTATIONS =====

    /**
     * Convert Markdown file to Word document.
     * Implements convertMarkdownToWord mutation from conversion-schema.graphqls.
     */
    @MutationMapping
    public ConversionResult convertMarkdownToWord(@Argument MarkdownConversionInput input) {
<span class="nc" id="L62">        String correlationId = CorrelationIdUtil.getCurrentCorrelationId();</span>
<span class="nc" id="L63">        String sessionId = UUID.randomUUID().toString();</span>

<span class="nc" id="L65">        log.info(&quot;GraphQL convertMarkdownToWord called - sessionId: {} [{}]&quot;, sessionId, correlationId);</span>

        try {
            // Validate input
<span class="nc bnc" id="L69" title="All 2 branches missed.">            if (input == null) {</span>
<span class="nc" id="L70">                throw new IllegalArgumentException(&quot;Input is required for conversion&quot;);</span>
            }
<span class="nc bnc" id="L72" title="All 6 branches missed.">            if (input.getFile() == null &amp;&amp; (input.getFilePath() == null || input.getFilePath().trim().isEmpty())) {</span>
<span class="nc" id="L73">                throw new IllegalArgumentException(&quot;Either file or filePath is required for conversion&quot;);</span>
            }

            // Create initial progress tracking
<span class="nc" id="L77">            ConversionProgress progress = createInitialProgress(sessionId, ConversionType.MARKDOWN_TO_WORD,</span>
<span class="nc bnc" id="L78" title="All 2 branches missed.">                    input.getFile() != null ? input.getFile().getOriginalFilename() : input.getFilePath());</span>
<span class="nc" id="L79">            conversionProgressMap.put(sessionId, progress);</span>

            // Perform conversion using existing service
            MarkdownConversionResult markdownResult;
<span class="nc bnc" id="L83" title="All 2 branches missed.">            if (input.getFile() != null) {</span>
<span class="nc" id="L84">                markdownResult = markdownConversionService.convertMarkdownToWordFromMultipart(</span>
<span class="nc" id="L85">                        input.getFile(), userContext.getUserId(), input.getScannerType());</span>
            } else {
<span class="nc" id="L87">                markdownResult = markdownConversionService.convertMarkdownToWordFromPath(</span>
<span class="nc" id="L88">                        input.getFilePath(), userContext.getUserId(), input.getScannerType());</span>
            }
            
            // Convert to ConversionResult
<span class="nc" id="L92">            ConversionResult result = ConversionResult.builder()</span>
<span class="nc" id="L93">                    .sessionId(sessionId)</span>
<span class="nc" id="L94">                    .conversionType(ConversionType.MARKDOWN_TO_WORD)</span>
<span class="nc" id="L95">                    .originalFileName(markdownResult.getOriginalFileName())</span>
<span class="nc" id="L96">                    .convertedFileName(markdownResult.getConvertedFileName())</span>
<span class="nc" id="L97">                    .downloadPath(markdownResult.getDownloadPath())</span>
<span class="nc" id="L98">                    .fileSize(markdownResult.getFileSize())</span>
<span class="nc bnc" id="L99" title="All 2 branches missed.">                    .status(markdownResult.isSuccess() ? ConversionStatus.COMPLETED : ConversionStatus.FAILED)</span>
<span class="nc" id="L100">                    .success(markdownResult.isSuccess())</span>
<span class="nc" id="L101">                    .message(markdownResult.getMessage())</span>
<span class="nc" id="L102">                    .errorDetails(markdownResult.getErrorDetails())</span>
<span class="nc" id="L103">                    .startedAt(LocalDateTime.now().minusSeconds(markdownResult.getProcessingTimeMs() / 1000))</span>
<span class="nc bnc" id="L104" title="All 2 branches missed.">                    .completedAt(markdownResult.getCompletedAt() != null ?</span>
<span class="nc" id="L105">                            markdownResult.getCompletedAt().toLocalDateTime() : LocalDateTime.now())</span>
<span class="nc" id="L106">                    .processingTimeMs(markdownResult.getProcessingTimeMs())</span>
<span class="nc" id="L107">                    .conversionMethod(determineConversionMethod(markdownResult.isUsedPandoc()))</span>
<span class="nc" id="L108">                    .usedPandoc(markdownResult.isUsedPandoc())</span>
<span class="nc" id="L109">                    .virusScanResponse(markdownResult.getVirusScanResponse())</span>
<span class="nc" id="L110">                    .build();</span>
            
            // Update progress to completed
<span class="nc" id="L113">            updateProgressToCompleted(sessionId, result.getStatus());</span>
            
            // Store result for later retrieval
<span class="nc" id="L116">            conversionResultMap.put(sessionId, result);</span>
            
<span class="nc" id="L118">            log.info(&quot;GraphQL convertMarkdownToWord completed - sessionId: {}, success: {} [{}]&quot;, </span>
<span class="nc" id="L119">                    sessionId, result.getSuccess(), correlationId);</span>
            
<span class="nc" id="L121">            return result;</span>
            
<span class="nc" id="L123">        } catch (Exception e) {</span>
<span class="nc" id="L124">            log.error(&quot;GraphQL convertMarkdownToWord failed - sessionId: {} [{}]&quot;, sessionId, correlationId, e);</span>
            
            // Update progress to failed
<span class="nc" id="L127">            updateProgressToCompleted(sessionId, ConversionStatus.FAILED);</span>
            
            // Create error result
<span class="nc" id="L130">            String originalFileName = &quot;unknown&quot;;</span>
<span class="nc bnc" id="L131" title="All 2 branches missed.">            if (input != null) {</span>
<span class="nc bnc" id="L132" title="All 2 branches missed.">                originalFileName = input.getFile() != null ? input.getFile().getOriginalFilename() : input.getFilePath();</span>
<span class="nc bnc" id="L133" title="All 2 branches missed.">                if (originalFileName == null) {</span>
<span class="nc" id="L134">                    originalFileName = &quot;unknown&quot;;</span>
                }
            }

<span class="nc" id="L138">            ConversionResult errorResult = ConversionResult.builder()</span>
<span class="nc" id="L139">                    .sessionId(sessionId)</span>
<span class="nc" id="L140">                    .conversionType(ConversionType.MARKDOWN_TO_WORD)</span>
<span class="nc" id="L141">                    .originalFileName(originalFileName)</span>
<span class="nc" id="L142">                    .convertedFileName(&quot;&quot;)</span>
<span class="nc" id="L143">                    .downloadPath(&quot;&quot;)</span>
<span class="nc" id="L144">                    .fileSize(0L)</span>
<span class="nc" id="L145">                    .status(ConversionStatus.FAILED)</span>
<span class="nc" id="L146">                    .success(false)</span>
<span class="nc" id="L147">                    .message(&quot;Conversion failed&quot;)</span>
<span class="nc" id="L148">                    .errorDetails(e.getMessage())</span>
<span class="nc" id="L149">                    .startedAt(LocalDateTime.now())</span>
<span class="nc" id="L150">                    .completedAt(LocalDateTime.now())</span>
<span class="nc" id="L151">                    .processingTimeMs(0L)</span>
<span class="nc" id="L152">                    .conversionMethod(ConversionMethod.FALLBACK)</span>
<span class="nc" id="L153">                    .usedPandoc(false)</span>
<span class="nc" id="L154">                    .build();</span>
            
<span class="nc" id="L156">            conversionResultMap.put(sessionId, errorResult);</span>
<span class="nc" id="L157">            return errorResult;</span>
        }
    }

    /**
     * Convert PDF file to Word document.
     * Implements convertPdfToWord mutation from conversion-schema.graphqls.
     */
    @MutationMapping
    public ConversionResult convertPdfToWord(@Argument PdfConversionInput input) {
<span class="nc" id="L167">        String correlationId = CorrelationIdUtil.getCurrentCorrelationId();</span>
<span class="nc" id="L168">        String sessionId = UUID.randomUUID().toString();</span>
        
<span class="nc" id="L170">        log.info(&quot;GraphQL convertPdfToWord called - sessionId: {} [{}]&quot;, sessionId, correlationId);</span>
        
        try {
            // Create initial progress tracking
<span class="nc" id="L174">            ConversionProgress progress = createInitialProgress(sessionId, ConversionType.PDF_TO_WORD, </span>
<span class="nc bnc" id="L175" title="All 2 branches missed.">                    input.getFile() != null ? input.getFile().getOriginalFilename() : input.getFilePath());</span>
<span class="nc" id="L176">            conversionProgressMap.put(sessionId, progress);</span>
            
            // Perform conversion using existing service
            PdfConversionResult pdfResult;
<span class="nc bnc" id="L180" title="All 2 branches missed.">            if (input.getFile() != null) {</span>
<span class="nc" id="L181">                pdfResult = pdfToWordConversionService.convertPdfToWordFromMultipart(</span>
<span class="nc" id="L182">                        input.getFile(), userContext.getUserId(), input.getScannerType());</span>
            } else {
<span class="nc" id="L184">                pdfResult = pdfToWordConversionService.convertPdfToWordFromPath(</span>
<span class="nc" id="L185">                        input.getFilePath(), userContext.getUserId(), input.getScannerType());</span>
            }
            
            // Convert to ConversionResult
<span class="nc" id="L189">            ConversionResult result = ConversionResult.builder()</span>
<span class="nc" id="L190">                    .sessionId(sessionId)</span>
<span class="nc" id="L191">                    .conversionType(ConversionType.PDF_TO_WORD)</span>
<span class="nc" id="L192">                    .originalFileName(pdfResult.getOriginalFileName())</span>
<span class="nc" id="L193">                    .convertedFileName(pdfResult.getConvertedFileName())</span>
<span class="nc" id="L194">                    .downloadPath(pdfResult.getDownloadPath())</span>
<span class="nc" id="L195">                    .fileSize(pdfResult.getFileSize())</span>
<span class="nc bnc" id="L196" title="All 2 branches missed.">                    .status(pdfResult.isSuccess() ? ConversionStatus.COMPLETED : ConversionStatus.FAILED)</span>
<span class="nc" id="L197">                    .success(pdfResult.isSuccess())</span>
<span class="nc" id="L198">                    .message(pdfResult.getMessage())</span>
<span class="nc" id="L199">                    .errorDetails(pdfResult.getErrorDetails())</span>
<span class="nc" id="L200">                    .startedAt(LocalDateTime.now().minusSeconds(pdfResult.getProcessingTimeMs() / 1000))</span>
<span class="nc bnc" id="L201" title="All 2 branches missed.">                    .completedAt(pdfResult.getCompletedAt() != null ?</span>
<span class="nc" id="L202">                            pdfResult.getCompletedAt().toLocalDateTime() : LocalDateTime.now())</span>
<span class="nc" id="L203">                    .processingTimeMs(pdfResult.getProcessingTimeMs())</span>
<span class="nc" id="L204">                    .conversionMethod(ConversionMethod.LEGACY) // PDF conversion doesn't track Pandoc usage</span>
<span class="nc" id="L205">                    .usedPandoc(false) // PDF conversion doesn't use Pandoc</span>
<span class="nc" id="L206">                    .virusScanResponse(pdfResult.getVirusScanResponse())</span>
<span class="nc" id="L207">                    .build();</span>
            
            // Update progress and store result
<span class="nc" id="L210">            updateProgressToCompleted(sessionId, result.getStatus());</span>
<span class="nc" id="L211">            conversionResultMap.put(sessionId, result);</span>
            
<span class="nc" id="L213">            log.info(&quot;GraphQL convertPdfToWord completed - sessionId: {}, success: {} [{}]&quot;, </span>
<span class="nc" id="L214">                    sessionId, result.getSuccess(), correlationId);</span>
            
<span class="nc" id="L216">            return result;</span>
            
<span class="nc" id="L218">        } catch (Exception e) {</span>
<span class="nc" id="L219">            log.error(&quot;GraphQL convertPdfToWord failed - sessionId: {} [{}]&quot;, sessionId, correlationId, e);</span>
<span class="nc" id="L220">            return createErrorResult(sessionId, ConversionType.PDF_TO_WORD, </span>
<span class="nc bnc" id="L221" title="All 2 branches missed.">                    input.getFile() != null ? input.getFile().getOriginalFilename() : input.getFilePath(), e);</span>
        }
    }

    /**
     * Convert Word file to PDF document.
     * Implements convertWordToPdf mutation from conversion-schema.graphqls.
     */
    @MutationMapping
    public ConversionResult convertWordToPdf(@Argument WordConversionInput input) {
<span class="nc" id="L231">        String correlationId = CorrelationIdUtil.getCurrentCorrelationId();</span>
<span class="nc" id="L232">        String sessionId = UUID.randomUUID().toString();</span>

<span class="nc" id="L234">        log.info(&quot;GraphQL convertWordToPdf called - sessionId: {} [{}]&quot;, sessionId, correlationId);</span>

        try {
            // Validate input
<span class="nc bnc" id="L238" title="All 2 branches missed.">            if (input == null) {</span>
<span class="nc" id="L239">                throw new IllegalArgumentException(&quot;Input is required for conversion&quot;);</span>
            }
<span class="nc bnc" id="L241" title="All 6 branches missed.">            if (input.getFile() == null &amp;&amp; (input.getFilePath() == null || input.getFilePath().trim().isEmpty())) {</span>
<span class="nc" id="L242">                throw new IllegalArgumentException(&quot;Either file or filePath is required for conversion&quot;);</span>
            }

            // Create initial progress tracking
<span class="nc" id="L246">            ConversionProgress progress = createInitialProgress(sessionId, ConversionType.WORD_TO_PDF,</span>
<span class="nc bnc" id="L247" title="All 2 branches missed.">                    input.getFile() != null ? input.getFile().getOriginalFilename() : input.getFilePath());</span>
<span class="nc" id="L248">            conversionProgressMap.put(sessionId, progress);</span>

            // Perform conversion using existing service
            WordConversionResult wordResult;
<span class="nc bnc" id="L252" title="All 2 branches missed.">            if (input.getFile() != null) {</span>
<span class="nc" id="L253">                wordResult = wordToPdfConversionService.convertWordToPdfFromMultipart(</span>
<span class="nc" id="L254">                        input.getFile(), userContext.getUserId(), input.getScannerType());</span>
            } else {
<span class="nc" id="L256">                wordResult = wordToPdfConversionService.convertWordToPdfFromPath(</span>
<span class="nc" id="L257">                        input.getFilePath(), userContext.getUserId(), input.getScannerType());</span>
            }
            
            // Convert to ConversionResult
<span class="nc" id="L261">            ConversionResult result = ConversionResult.builder()</span>
<span class="nc" id="L262">                    .sessionId(sessionId)</span>
<span class="nc" id="L263">                    .conversionType(ConversionType.WORD_TO_PDF)</span>
<span class="nc" id="L264">                    .originalFileName(wordResult.getOriginalFileName())</span>
<span class="nc" id="L265">                    .convertedFileName(wordResult.getConvertedFileName())</span>
<span class="nc" id="L266">                    .downloadPath(wordResult.getDownloadPath())</span>
<span class="nc" id="L267">                    .fileSize(wordResult.getFileSize())</span>
<span class="nc bnc" id="L268" title="All 2 branches missed.">                    .status(wordResult.isSuccess() ? ConversionStatus.COMPLETED : ConversionStatus.FAILED)</span>
<span class="nc" id="L269">                    .success(wordResult.isSuccess())</span>
<span class="nc" id="L270">                    .message(wordResult.getMessage())</span>
<span class="nc" id="L271">                    .errorDetails(wordResult.getErrorDetails())</span>
<span class="nc" id="L272">                    .startedAt(LocalDateTime.now().minusSeconds(wordResult.getProcessingTimeMs() / 1000))</span>
<span class="nc bnc" id="L273" title="All 2 branches missed.">                    .completedAt(wordResult.getCompletedAt() != null ?</span>
<span class="nc" id="L274">                            wordResult.getCompletedAt().toLocalDateTime() : LocalDateTime.now())</span>
<span class="nc" id="L275">                    .processingTimeMs(wordResult.getProcessingTimeMs())</span>
<span class="nc" id="L276">                    .conversionMethod(ConversionMethod.LEGACY) // Word conversion doesn't track Pandoc usage</span>
<span class="nc" id="L277">                    .usedPandoc(false) // Word conversion doesn't use Pandoc</span>
<span class="nc" id="L278">                    .virusScanResponse(wordResult.getVirusScanResponse())</span>
<span class="nc" id="L279">                    .build();</span>
            
            // Update progress and store result
<span class="nc" id="L282">            updateProgressToCompleted(sessionId, result.getStatus());</span>
<span class="nc" id="L283">            conversionResultMap.put(sessionId, result);</span>
            
<span class="nc" id="L285">            log.info(&quot;GraphQL convertWordToPdf completed - sessionId: {}, success: {} [{}]&quot;, </span>
<span class="nc" id="L286">                    sessionId, result.getSuccess(), correlationId);</span>
            
<span class="nc" id="L288">            return result;</span>
            
<span class="nc" id="L290">        } catch (Exception e) {</span>
<span class="nc" id="L291">            log.error(&quot;GraphQL convertWordToPdf failed - sessionId: {} [{}]&quot;, sessionId, correlationId, e);</span>
<span class="nc" id="L292">            String originalFileName = &quot;unknown&quot;;</span>
<span class="nc bnc" id="L293" title="All 2 branches missed.">            if (input != null) {</span>
<span class="nc bnc" id="L294" title="All 2 branches missed.">                originalFileName = input.getFile() != null ? input.getFile().getOriginalFilename() : input.getFilePath();</span>
<span class="nc bnc" id="L295" title="All 2 branches missed.">                if (originalFileName == null) {</span>
<span class="nc" id="L296">                    originalFileName = &quot;unknown&quot;;</span>
                }
            }
<span class="nc" id="L299">            return createErrorResult(sessionId, ConversionType.WORD_TO_PDF, originalFileName, e);</span>
        }
    }

    /**
     * Generic file conversion mutation.
     * Implements convertFile mutation from conversion-schema.graphqls.
     */
    @MutationMapping
    public ConversionResult convertFile(@Argument ConversionInput input) {
<span class="nc" id="L309">        String correlationId = CorrelationIdUtil.getCurrentCorrelationId();</span>
<span class="nc" id="L310">        String sessionId = UUID.randomUUID().toString();</span>

<span class="nc" id="L312">        log.info(&quot;GraphQL convertFile called - sessionId: {}, fromFormat: {}, toFormat: {} [{}]&quot;,</span>
<span class="nc bnc" id="L313" title="All 2 branches missed.">                sessionId, input != null ? input.getFromFormat() : &quot;null&quot;,</span>
<span class="nc bnc" id="L314" title="All 2 branches missed.">                input != null ? input.getToFormat() : &quot;null&quot;, correlationId);</span>

        try {
            // Validate input
<span class="nc bnc" id="L318" title="All 2 branches missed.">            if (input == null) {</span>
<span class="nc" id="L319">                throw new IllegalArgumentException(&quot;Input is required for conversion&quot;);</span>
            }
<span class="nc bnc" id="L321" title="All 6 branches missed.">            if (input.getFile() == null &amp;&amp; (input.getFilePath() == null || input.getFilePath().trim().isEmpty())) {</span>
<span class="nc" id="L322">                throw new IllegalArgumentException(&quot;Either file or filePath is required for conversion&quot;);</span>
            }

            // Determine conversion type based on formats
<span class="nc" id="L326">            ConversionType conversionType = determineConversionType(input.getFromFormat(), input.getToFormat());</span>

            // Create initial progress tracking
<span class="nc" id="L329">            ConversionProgress progress = createInitialProgress(sessionId, conversionType,</span>
<span class="nc bnc" id="L330" title="All 2 branches missed.">                    input.getFile() != null ? input.getFile().getOriginalFilename() : input.getFilePath());</span>
<span class="nc" id="L331">            conversionProgressMap.put(sessionId, progress);</span>

            // Route to appropriate conversion service based on type
<span class="nc" id="L334">            ConversionResult result = performGenericConversion(sessionId, input, conversionType);</span>

            // Update progress and store result
<span class="nc" id="L337">            updateProgressToCompleted(sessionId, result.getStatus());</span>
<span class="nc" id="L338">            conversionResultMap.put(sessionId, result);</span>

<span class="nc" id="L340">            log.info(&quot;GraphQL convertFile completed - sessionId: {}, success: {} [{}]&quot;,</span>
<span class="nc" id="L341">                    sessionId, result.getSuccess(), correlationId);</span>

<span class="nc" id="L343">            return result;</span>

<span class="nc" id="L345">        } catch (Exception e) {</span>
<span class="nc" id="L346">            log.error(&quot;GraphQL convertFile failed - sessionId: {} [{}]&quot;, sessionId, correlationId, e);</span>
<span class="nc" id="L347">            String originalFileName = &quot;unknown&quot;;</span>
<span class="nc bnc" id="L348" title="All 2 branches missed.">            if (input != null) {</span>
<span class="nc bnc" id="L349" title="All 2 branches missed.">                originalFileName = input.getFile() != null ? input.getFile().getOriginalFilename() : input.getFilePath();</span>
<span class="nc bnc" id="L350" title="All 2 branches missed.">                if (originalFileName == null) {</span>
<span class="nc" id="L351">                    originalFileName = &quot;unknown&quot;;</span>
                }
            }
<span class="nc" id="L354">            return createErrorResult(sessionId, ConversionType.MARKDOWN_TO_WORD, originalFileName, e);</span>
        }
    }

    /**
     * Batch file conversion mutation.
     * Implements batchConvertFiles mutation from conversion-schema.graphqls.
     */
    @MutationMapping
    public BatchConversionResult batchConvertFiles(@Argument BatchConversionInput input) {
<span class="nc" id="L364">        String correlationId = CorrelationIdUtil.getCurrentCorrelationId();</span>
<span class="nc" id="L365">        String batchId = UUID.randomUUID().toString();</span>

<span class="nc" id="L367">        log.info(&quot;GraphQL batchConvertFiles called - batchId: {}, conversionType: {} [{}]&quot;,</span>
<span class="nc" id="L368">                batchId, input.getConversionType(), correlationId);</span>

        try {
            // Determine total files to process
<span class="nc" id="L372">            int totalFiles = 0;</span>
<span class="nc bnc" id="L373" title="All 2 branches missed.">            if (input.getFiles() != null) {</span>
<span class="nc" id="L374">                totalFiles += input.getFiles().size();</span>
            }
<span class="nc bnc" id="L376" title="All 2 branches missed.">            if (input.getFilePaths() != null) {</span>
<span class="nc" id="L377">                totalFiles += input.getFilePaths().size();</span>
            }

            // Create initial batch result
<span class="nc" id="L381">            BatchConversionResult batchResult = BatchConversionResult.builder()</span>
<span class="nc" id="L382">                    .batchId(batchId)</span>
<span class="nc" id="L383">                    .totalFiles(totalFiles)</span>
<span class="nc" id="L384">                    .completedFiles(0)</span>
<span class="nc" id="L385">                    .failedFiles(0)</span>
<span class="nc" id="L386">                    .status(ConversionStatus.PROCESSING)</span>
<span class="nc" id="L387">                    .results(new java.util.ArrayList&lt;&gt;())</span>
<span class="nc" id="L388">                    .startedAt(LocalDateTime.now())</span>
<span class="nc" id="L389">                    .totalProcessingTime(0L)</span>
<span class="nc" id="L390">                    .errors(new java.util.ArrayList&lt;&gt;())</span>
<span class="nc" id="L391">                    .progress(0.0f)</span>
<span class="nc" id="L392">                    .build();</span>

<span class="nc" id="L394">            batchConversionResultMap.put(batchId, batchResult);</span>

            // Process files (simplified implementation - in production, use async processing)
<span class="nc" id="L397">            java.util.List&lt;ConversionResult&gt; results = new java.util.ArrayList&lt;&gt;();</span>
<span class="nc" id="L398">            java.util.List&lt;ConversionError&gt; errors = new java.util.ArrayList&lt;&gt;();</span>
<span class="nc" id="L399">            int completedFiles = 0;</span>
<span class="nc" id="L400">            int failedFiles = 0;</span>

            // Process multipart files
<span class="nc bnc" id="L403" title="All 2 branches missed.">            if (input.getFiles() != null) {</span>
<span class="nc bnc" id="L404" title="All 2 branches missed.">                for (int i = 0; i &lt; input.getFiles().size(); i++) {</span>
                    try {
<span class="nc" id="L406">                        ConversionInput conversionInput = createConversionInputFromBatch(input, input.getFiles().get(i), null);</span>
<span class="nc" id="L407">                        ConversionResult result = performGenericConversion(UUID.randomUUID().toString(), conversionInput, input.getConversionType());</span>
<span class="nc" id="L408">                        results.add(result);</span>
<span class="nc bnc" id="L409" title="All 2 branches missed.">                        if (result.getSuccess()) {</span>
<span class="nc" id="L410">                            completedFiles++;</span>
                        } else {
<span class="nc" id="L412">                            failedFiles++;</span>
                        }
<span class="nc" id="L414">                    } catch (Exception e) {</span>
<span class="nc" id="L415">                        failedFiles++;</span>
<span class="nc" id="L416">                        errors.add(ConversionError.builder()</span>
<span class="nc" id="L417">                                .fileName(input.getFiles().get(i).getOriginalFilename())</span>
<span class="nc" id="L418">                                .errorCode(&quot;CONVERSION_FAILED&quot;)</span>
<span class="nc" id="L419">                                .errorMessage(e.getMessage())</span>
<span class="nc" id="L420">                                .timestamp(LocalDateTime.now())</span>
<span class="nc" id="L421">                                .build());</span>
<span class="nc" id="L422">                    }</span>
                }
            }

            // Process file paths
<span class="nc bnc" id="L427" title="All 2 branches missed.">            if (input.getFilePaths() != null) {</span>
<span class="nc bnc" id="L428" title="All 2 branches missed.">                for (String filePath : input.getFilePaths()) {</span>
                    try {
<span class="nc" id="L430">                        ConversionInput conversionInput = createConversionInputFromBatch(input, null, filePath);</span>
<span class="nc" id="L431">                        ConversionResult result = performGenericConversion(UUID.randomUUID().toString(), conversionInput, input.getConversionType());</span>
<span class="nc" id="L432">                        results.add(result);</span>
<span class="nc bnc" id="L433" title="All 2 branches missed.">                        if (result.getSuccess()) {</span>
<span class="nc" id="L434">                            completedFiles++;</span>
                        } else {
<span class="nc" id="L436">                            failedFiles++;</span>
                        }
<span class="nc" id="L438">                    } catch (Exception e) {</span>
<span class="nc" id="L439">                        failedFiles++;</span>
<span class="nc" id="L440">                        errors.add(ConversionError.builder()</span>
<span class="nc" id="L441">                                .fileName(filePath)</span>
<span class="nc" id="L442">                                .errorCode(&quot;CONVERSION_FAILED&quot;)</span>
<span class="nc" id="L443">                                .errorMessage(e.getMessage())</span>
<span class="nc" id="L444">                                .timestamp(LocalDateTime.now())</span>
<span class="nc" id="L445">                                .build());</span>
<span class="nc" id="L446">                    }</span>
<span class="nc" id="L447">                }</span>
            }

            // Update final batch result
<span class="nc" id="L451">            batchResult.setResults(results);</span>
<span class="nc" id="L452">            batchResult.setErrors(errors);</span>
<span class="nc" id="L453">            batchResult.setCompletedFiles(completedFiles);</span>
<span class="nc" id="L454">            batchResult.setFailedFiles(failedFiles);</span>
<span class="nc bnc" id="L455" title="All 2 branches missed.">            batchResult.setStatus(failedFiles &gt; 0 ? ConversionStatus.FAILED : ConversionStatus.COMPLETED);</span>
<span class="nc" id="L456">            batchResult.setCompletedAt(LocalDateTime.now());</span>
<span class="nc" id="L457">            batchResult.setProgress(1.0f);</span>

<span class="nc" id="L459">            batchConversionResultMap.put(batchId, batchResult);</span>

<span class="nc" id="L461">            log.info(&quot;GraphQL batchConvertFiles completed - batchId: {}, completed: {}, failed: {} [{}]&quot;,</span>
<span class="nc" id="L462">                    batchId, completedFiles, failedFiles, correlationId);</span>

<span class="nc" id="L464">            return batchResult;</span>

<span class="nc" id="L466">        } catch (Exception e) {</span>
<span class="nc" id="L467">            log.error(&quot;GraphQL batchConvertFiles failed - batchId: {} [{}]&quot;, batchId, correlationId, e);</span>

<span class="nc" id="L469">            BatchConversionResult errorResult = BatchConversionResult.builder()</span>
<span class="nc" id="L470">                    .batchId(batchId)</span>
<span class="nc" id="L471">                    .totalFiles(0)</span>
<span class="nc" id="L472">                    .completedFiles(0)</span>
<span class="nc" id="L473">                    .failedFiles(0)</span>
<span class="nc" id="L474">                    .status(ConversionStatus.FAILED)</span>
<span class="nc" id="L475">                    .results(new java.util.ArrayList&lt;&gt;())</span>
<span class="nc" id="L476">                    .startedAt(LocalDateTime.now())</span>
<span class="nc" id="L477">                    .completedAt(LocalDateTime.now())</span>
<span class="nc" id="L478">                    .totalProcessingTime(0L)</span>
<span class="nc" id="L479">                    .errors(java.util.List.of(ConversionError.builder()</span>
<span class="nc" id="L480">                            .fileName(&quot;batch&quot;)</span>
<span class="nc" id="L481">                            .errorCode(&quot;BATCH_FAILED&quot;)</span>
<span class="nc" id="L482">                            .errorMessage(e.getMessage())</span>
<span class="nc" id="L483">                            .timestamp(LocalDateTime.now())</span>
<span class="nc" id="L484">                            .build()))</span>
<span class="nc" id="L485">                    .progress(0.0f)</span>
<span class="nc" id="L486">                    .build();</span>

<span class="nc" id="L488">            batchConversionResultMap.put(batchId, errorResult);</span>
<span class="nc" id="L489">            return errorResult;</span>
        }
    }

    // ===== CONVERSION QUERIES =====

    /**
     * Get conversion status by session ID.
     * Implements getConversionStatus query from conversion-schema.graphqls.
     */
    @QueryMapping
    public ConversionResult getConversionStatus(@Argument String sessionId) {
<span class="nc" id="L501">        log.info(&quot;GraphQL getConversionStatus called - sessionId: {}&quot;, sessionId);</span>
<span class="nc" id="L502">        return conversionResultMap.get(sessionId);</span>
    }

    /**
     * Get conversion progress by session ID or conversion ID.
     * Implements getConversionProgress query from conversion-schema.graphqls.
     */
    @QueryMapping
    public ConversionProgress getConversionProgress(@Argument String sessionId,
                                                   @Argument String conversionId) {
<span class="nc" id="L512">        log.info(&quot;GraphQL getConversionProgress called - sessionId: {}, conversionId: {}&quot;, sessionId, conversionId);</span>

        // Use conversionId as sessionId if sessionId is not provided
<span class="nc bnc" id="L515" title="All 2 branches missed.">        String lookupKey = sessionId != null ? sessionId : conversionId;</span>

<span class="nc bnc" id="L517" title="All 2 branches missed.">        if (lookupKey == null) {</span>
<span class="nc" id="L518">            log.warn(&quot;Both sessionId and conversionId are null&quot;);</span>
<span class="nc" id="L519">            return null;</span>
        }

<span class="nc" id="L522">        ConversionProgress progress = conversionProgressMap.get(lookupKey);</span>
<span class="nc bnc" id="L523" title="All 2 branches missed.">        if (progress == null) {</span>
            // Create a mock progress entry for testing
<span class="nc" id="L525">            progress = ConversionProgress.builder()</span>
<span class="nc" id="L526">                    .sessionId(lookupKey)</span>
<span class="nc bnc" id="L527" title="All 2 branches missed.">                    .conversionId(conversionId != null ? conversionId : lookupKey)</span>
<span class="nc" id="L528">                    .conversionType(ConversionType.MARKDOWN_TO_WORD)</span>
<span class="nc" id="L529">                    .status(ConversionStatus.PROCESSING)</span>
<span class="nc" id="L530">                    .progress(0.75f)</span>
<span class="nc" id="L531">                    .progressPercentage(75.0f)</span>
<span class="nc" id="L532">                    .currentStep(&quot;Converting content&quot;)</span>
<span class="nc" id="L533">                    .totalSteps(3)</span>
<span class="nc" id="L534">                    .completedSteps(2)</span>
<span class="nc" id="L535">                    .estimatedTimeRemaining(15000L)</span>
<span class="nc" id="L536">                    .processingRate(2.5f)</span>
<span class="nc" id="L537">                    .startedAt(LocalDateTime.now().minusMinutes(2))</span>
<span class="nc" id="L538">                    .lastUpdatedAt(LocalDateTime.now())</span>
<span class="nc" id="L539">                    .message(&quot;Conversion in progress&quot;)</span>
<span class="nc" id="L540">                    .build();</span>
<span class="nc" id="L541">            conversionProgressMap.put(lookupKey, progress);</span>
        }

<span class="nc" id="L544">        return progress;</span>
    }

    /**
     * Get batch conversion status by batch ID.
     * Implements getBatchConversionStatus query from conversion-schema.graphqls.
     */
    @QueryMapping
    public BatchConversionResult getBatchConversionStatus(@Argument String batchId) {
<span class="nc" id="L553">        log.info(&quot;GraphQL getBatchConversionStatus called - batchId: {}&quot;, batchId);</span>

<span class="nc" id="L555">        BatchConversionResult result = batchConversionResultMap.get(batchId);</span>
<span class="nc bnc" id="L556" title="All 2 branches missed.">        if (result == null) {</span>
            // Create a mock batch result for testing
<span class="nc" id="L558">            result = BatchConversionResult.builder()</span>
<span class="nc" id="L559">                    .batchId(batchId)</span>
<span class="nc" id="L560">                    .totalFiles(2)</span>
<span class="nc" id="L561">                    .completedFiles(1)</span>
<span class="nc" id="L562">                    .failedFiles(0)</span>
<span class="nc" id="L563">                    .status(ConversionStatus.PROCESSING)</span>
<span class="nc" id="L564">                    .results(java.util.List.of(</span>
<span class="nc" id="L565">                            ConversionResult.builder()</span>
<span class="nc" id="L566">                                    .sessionId(&quot;session-1&quot;)</span>
<span class="nc" id="L567">                                    .conversionType(ConversionType.MARKDOWN_TO_WORD)</span>
<span class="nc" id="L568">                                    .originalFileName(&quot;test1.md&quot;)</span>
<span class="nc" id="L569">                                    .convertedFileName(&quot;test1.docx&quot;)</span>
<span class="nc" id="L570">                                    .downloadPath(&quot;/downloads/test1.docx&quot;)</span>
<span class="nc" id="L571">                                    .fileSize(1024L)</span>
<span class="nc" id="L572">                                    .status(ConversionStatus.COMPLETED)</span>
<span class="nc" id="L573">                                    .success(true)</span>
<span class="nc" id="L574">                                    .message(&quot;Conversion completed&quot;)</span>
<span class="nc" id="L575">                                    .startedAt(java.time.OffsetDateTime.now().minusMinutes(5).toLocalDateTime())</span>
<span class="nc" id="L576">                                    .completedAt(java.time.OffsetDateTime.now().minusMinutes(3).toLocalDateTime())</span>
<span class="nc" id="L577">                                    .processingTimeMs(120000L)</span>
<span class="nc" id="L578">                                    .conversionMethod(ConversionMethod.PANDOC)</span>
<span class="nc" id="L579">                                    .usedPandoc(true)</span>
<span class="nc" id="L580">                                    .build()</span>
                    ))
<span class="nc" id="L582">                    .startedAt(LocalDateTime.now().minusMinutes(5))</span>
<span class="nc" id="L583">                    .estimatedCompletionTime(LocalDateTime.now().plusMinutes(2))</span>
<span class="nc" id="L584">                    .progress(0.5f)</span>
<span class="nc" id="L585">                    .errors(new java.util.ArrayList&lt;&gt;())</span>
<span class="nc" id="L586">                    .build();</span>
<span class="nc" id="L587">            batchConversionResultMap.put(batchId, result);</span>
        }

<span class="nc" id="L590">        return result;</span>
    }

    /**
     * Get conversion history with filtering and pagination.
     * Implements getConversionHistory query from conversion-schema.graphqls.
     */
    @QueryMapping
    public ConversionHistoryPage getConversionHistory(@Argument String userId,
                                                     @Argument ConversionType conversionType,
                                                     @Argument java.time.LocalDateTime dateFrom,
                                                     @Argument java.time.LocalDateTime dateTo,
                                                     @Argument PaginationInput pagination) {
<span class="nc" id="L603">        log.info(&quot;GraphQL getConversionHistory called - userId: {}, conversionType: {}&quot;, userId, conversionType);</span>

        // If no results exist, create some mock data for testing
<span class="nc bnc" id="L606" title="All 2 branches missed.">        if (conversionResultMap.isEmpty()) {</span>
<span class="nc" id="L607">            createMockConversionHistory();</span>
        }

        // Filter conversion results based on criteria
<span class="nc" id="L611">        java.util.List&lt;ConversionResult&gt; filteredResults = conversionResultMap.values().stream()</span>
<span class="nc" id="L612">                .filter(result -&gt; {</span>
<span class="nc bnc" id="L613" title="All 4 branches missed.">                    if (conversionType != null &amp;&amp; !result.getConversionType().equals(conversionType)) {</span>
<span class="nc" id="L614">                        return false;</span>
                    }
<span class="nc bnc" id="L616" title="All 6 branches missed.">                    if (dateFrom != null &amp;&amp; result.getCompletedAt() != null &amp;&amp; result.getCompletedAt().isBefore(dateFrom)) {</span>
<span class="nc" id="L617">                        return false;</span>
                    }
<span class="nc bnc" id="L619" title="All 6 branches missed.">                    if (dateTo != null &amp;&amp; result.getCompletedAt() != null &amp;&amp; result.getCompletedAt().isAfter(dateTo)) {</span>
<span class="nc" id="L620">                        return false;</span>
                    }
<span class="nc" id="L622">                    return true;</span>
                })
<span class="nc" id="L624">                .collect(java.util.stream.Collectors.toList());</span>

        // Apply pagination (simplified implementation)
<span class="nc bnc" id="L627" title="All 2 branches missed.">        int page = pagination != null ? pagination.getPage() : 0;</span>
<span class="nc bnc" id="L628" title="All 2 branches missed.">        int size = pagination != null ? pagination.getSize() : 10;</span>
<span class="nc" id="L629">        int totalElements = filteredResults.size();</span>
<span class="nc" id="L630">        int totalPages = (int) Math.ceil((double) totalElements / size);</span>

<span class="nc" id="L632">        int startIndex = page * size;</span>
<span class="nc" id="L633">        int endIndex = Math.min(startIndex + size, totalElements);</span>

<span class="nc bnc" id="L635" title="All 2 branches missed.">        java.util.List&lt;ConversionResult&gt; pageContent = startIndex &lt; totalElements ?</span>
<span class="nc" id="L636">                filteredResults.subList(startIndex, endIndex) : new java.util.ArrayList&lt;&gt;();</span>

<span class="nc" id="L638">        return ConversionHistoryPage.builder()</span>
<span class="nc" id="L639">                .content(pageContent)</span>
<span class="nc" id="L640">                .totalElements(totalElements)</span>
<span class="nc" id="L641">                .totalPages(totalPages)</span>
<span class="nc" id="L642">                .size(size)</span>
<span class="nc bnc" id="L643" title="All 2 branches missed.">                .number(page)</span>
<span class="nc bnc" id="L644" title="All 2 branches missed.">                .first(page == 0)</span>
<span class="nc" id="L645">                .last(page &gt;= totalPages - 1)</span>
<span class="nc" id="L646">                .build();</span>
    }

    /**
     * Get conversion statistics for a date range.
     * Implements getConversionStatistics query from conversion-schema.graphqls.
     */
    @QueryMapping
    public ConversionStatistics getConversionStatistics(@Argument java.time.LocalDateTime dateFrom,
                                                        @Argument java.time.LocalDateTime dateTo,
                                                        @Argument java.util.List&lt;ConversionType&gt; conversionTypes) {
<span class="nc" id="L657">        log.info(&quot;GraphQL getConversionStatistics called - dateFrom: {}, dateTo: {}&quot;, dateFrom, dateTo);</span>

        // If no results exist, create some mock data for testing
<span class="nc bnc" id="L660" title="All 2 branches missed.">        if (conversionResultMap.isEmpty()) {</span>
<span class="nc" id="L661">            createMockConversionHistory();</span>
        }

        // Filter conversion results based on date range and types
<span class="nc" id="L665">        java.util.List&lt;ConversionResult&gt; filteredResults = conversionResultMap.values().stream()</span>
<span class="nc" id="L666">                .filter(result -&gt; {</span>
<span class="nc bnc" id="L667" title="All 6 branches missed.">                    if (dateFrom != null &amp;&amp; result.getCompletedAt() != null &amp;&amp; result.getCompletedAt().isBefore(dateFrom)) {</span>
<span class="nc" id="L668">                        return false;</span>
                    }
<span class="nc bnc" id="L670" title="All 6 branches missed.">                    if (dateTo != null &amp;&amp; result.getCompletedAt() != null &amp;&amp; result.getCompletedAt().isAfter(dateTo)) {</span>
<span class="nc" id="L671">                        return false;</span>
                    }
<span class="nc bnc" id="L673" title="All 6 branches missed.">                    if (conversionTypes != null &amp;&amp; !conversionTypes.isEmpty() &amp;&amp; !conversionTypes.contains(result.getConversionType())) {</span>
<span class="nc" id="L674">                        return false;</span>
                    }
<span class="nc" id="L676">                    return true;</span>
                })
<span class="nc" id="L678">                .collect(java.util.stream.Collectors.toList());</span>

        // Calculate basic statistics
<span class="nc" id="L681">        long totalConversions = filteredResults.size();</span>
<span class="nc" id="L682">        long successfulConversions = filteredResults.stream()</span>
<span class="nc bnc" id="L683" title="All 2 branches missed.">                .mapToLong(result -&gt; result.getSuccess() ? 1 : 0)</span>
<span class="nc" id="L684">                .sum();</span>
<span class="nc" id="L685">        long failedConversions = totalConversions - successfulConversions;</span>

<span class="nc bnc" id="L687" title="All 2 branches missed.">        float averageProcessingTime = filteredResults.isEmpty() ? 0.0f :</span>
<span class="nc" id="L688">                (float) filteredResults.stream()</span>
<span class="nc" id="L689">                        .mapToLong(ConversionResult::getProcessingTimeMs)</span>
<span class="nc" id="L690">                        .average()</span>
<span class="nc" id="L691">                        .orElse(0.0);</span>

        // Generate conversion type statistics
<span class="nc" id="L694">        java.util.List&lt;ConversionTypeStats&gt; conversionsByType = filteredResults.stream()</span>
<span class="nc" id="L695">                .collect(java.util.stream.Collectors.groupingBy(ConversionResult::getConversionType))</span>
<span class="nc" id="L696">                .entrySet().stream()</span>
<span class="nc" id="L697">                .map(entry -&gt; {</span>
<span class="nc" id="L698">                    ConversionType type = entry.getKey();</span>
<span class="nc" id="L699">                    java.util.List&lt;ConversionResult&gt; typeResults = entry.getValue();</span>
<span class="nc bnc" id="L700" title="All 2 branches missed.">                    long typeSuccessCount = typeResults.stream().mapToLong(r -&gt; r.getSuccess() ? 1 : 0).sum();</span>
<span class="nc" id="L701">                    long typeFailureCount = typeResults.size() - typeSuccessCount;</span>
<span class="nc" id="L702">                    float typeAvgTime = (float) typeResults.stream()</span>
<span class="nc" id="L703">                            .mapToLong(ConversionResult::getProcessingTimeMs)</span>
<span class="nc" id="L704">                            .average()</span>
<span class="nc" id="L705">                            .orElse(0.0);</span>
<span class="nc bnc" id="L706" title="All 2 branches missed.">                    float typeSuccessRate = typeResults.isEmpty() ? 0.0f : (float) typeSuccessCount / typeResults.size();</span>

<span class="nc" id="L708">                    return ConversionTypeStats.builder()</span>
<span class="nc" id="L709">                            .conversionType(type)</span>
<span class="nc" id="L710">                            .count((long) typeResults.size())</span>
<span class="nc" id="L711">                            .successCount(typeSuccessCount)</span>
<span class="nc" id="L712">                            .failureCount(typeFailureCount)</span>
<span class="nc" id="L713">                            .averageProcessingTime(typeAvgTime)</span>
<span class="nc" id="L714">                            .successRate(typeSuccessRate)</span>
<span class="nc" id="L715">                            .build();</span>
                })
<span class="nc" id="L717">                .collect(java.util.stream.Collectors.toList());</span>

        // Generate conversion method statistics
<span class="nc" id="L720">        java.util.List&lt;ConversionMethodStats&gt; conversionsByMethod = filteredResults.stream()</span>
<span class="nc" id="L721">                .collect(java.util.stream.Collectors.groupingBy(ConversionResult::getConversionMethod))</span>
<span class="nc" id="L722">                .entrySet().stream()</span>
<span class="nc" id="L723">                .map(entry -&gt; {</span>
<span class="nc" id="L724">                    ConversionMethod method = entry.getKey();</span>
<span class="nc" id="L725">                    java.util.List&lt;ConversionResult&gt; methodResults = entry.getValue();</span>
<span class="nc bnc" id="L726" title="All 2 branches missed.">                    long methodSuccessCount = methodResults.stream().mapToLong(r -&gt; r.getSuccess() ? 1 : 0).sum();</span>
<span class="nc" id="L727">                    long methodFailureCount = methodResults.size() - methodSuccessCount;</span>
<span class="nc" id="L728">                    float methodAvgTime = (float) methodResults.stream()</span>
<span class="nc" id="L729">                            .mapToLong(ConversionResult::getProcessingTimeMs)</span>
<span class="nc" id="L730">                            .average()</span>
<span class="nc" id="L731">                            .orElse(0.0);</span>
<span class="nc bnc" id="L732" title="All 2 branches missed.">                    float methodSuccessRate = methodResults.isEmpty() ? 0.0f : (float) methodSuccessCount / methodResults.size();</span>

<span class="nc" id="L734">                    return ConversionMethodStats.builder()</span>
<span class="nc" id="L735">                            .method(method)</span>
<span class="nc" id="L736">                            .count((long) methodResults.size())</span>
<span class="nc" id="L737">                            .successCount(methodSuccessCount)</span>
<span class="nc" id="L738">                            .failureCount(methodFailureCount)</span>
<span class="nc" id="L739">                            .averageProcessingTime(methodAvgTime)</span>
<span class="nc" id="L740">                            .successRate(methodSuccessRate)</span>
<span class="nc" id="L741">                            .build();</span>
                })
<span class="nc" id="L743">                .collect(java.util.stream.Collectors.toList());</span>

        // Calculate total processing time
<span class="nc" id="L746">        long totalProcessingTime = filteredResults.stream()</span>
<span class="nc" id="L747">                .mapToLong(ConversionResult::getProcessingTimeMs)</span>
<span class="nc" id="L748">                .sum();</span>

        // Generate top error reasons
<span class="nc" id="L751">        java.util.List&lt;String&gt; topErrorReasons = filteredResults.stream()</span>
<span class="nc bnc" id="L752" title="All 4 branches missed.">                .filter(result -&gt; !result.getSuccess() &amp;&amp; result.getErrorDetails() != null)</span>
<span class="nc" id="L753">                .map(ConversionResult::getErrorDetails)</span>
<span class="nc" id="L754">                .collect(java.util.stream.Collectors.groupingBy(</span>
<span class="nc" id="L755">                        java.util.function.Function.identity(),</span>
<span class="nc" id="L756">                        java.util.stream.Collectors.counting()))</span>
<span class="nc" id="L757">                .entrySet().stream()</span>
<span class="nc" id="L758">                .sorted(java.util.Map.Entry.&lt;String, Long&gt;comparingByValue().reversed())</span>
<span class="nc" id="L759">                .limit(5)</span>
<span class="nc" id="L760">                .map(java.util.Map.Entry::getKey)</span>
<span class="nc" id="L761">                .collect(java.util.stream.Collectors.toList());</span>

<span class="nc" id="L763">        return ConversionStatistics.builder()</span>
<span class="nc" id="L764">                .totalConversions(totalConversions)</span>
<span class="nc" id="L765">                .successfulConversions(successfulConversions)</span>
<span class="nc" id="L766">                .failedConversions(failedConversions)</span>
<span class="nc" id="L767">                .averageProcessingTime(averageProcessingTime)</span>
<span class="nc" id="L768">                .totalProcessingTime(totalProcessingTime)</span>
<span class="nc" id="L769">                .conversionsByDate(new java.util.ArrayList&lt;&gt;()) // TODO: Implement date-based grouping</span>
<span class="nc" id="L770">                .topErrorReasons(topErrorReasons)</span>
<span class="nc" id="L771">                .conversionsByType(conversionsByType)</span>
<span class="nc" id="L772">                .conversionsByMethod(conversionsByMethod)</span>
<span class="nc" id="L773">                .conversionTrends(new java.util.ArrayList&lt;&gt;()) // TODO: Implement trend analysis</span>
<span class="nc" id="L774">                .popularFormats(new java.util.ArrayList&lt;&gt;()) // TODO: Implement format popularity analysis</span>
<span class="nc" id="L775">                .build();</span>
    }

    /**
     * Get supported conversion types and formats.
     * Implements getSupportedConversions query from conversion-schema.graphqls.
     */
    @QueryMapping
    public java.util.List&lt;SupportedConversion&gt; getSupportedConversions() {
<span class="nc" id="L784">        log.info(&quot;GraphQL getSupportedConversions called&quot;);</span>

<span class="nc" id="L786">        java.util.List&lt;SupportedConversion&gt; supportedConversions = new java.util.ArrayList&lt;&gt;();</span>

        // Markdown to Word
<span class="nc" id="L789">        supportedConversions.add(SupportedConversion.builder()</span>
<span class="nc" id="L790">                .fromFormat(&quot;markdown&quot;)</span>
<span class="nc" id="L791">                .toFormat(&quot;docx&quot;)</span>
<span class="nc" id="L792">                .conversionType(ConversionType.MARKDOWN_TO_WORD)</span>
<span class="nc" id="L793">                .supportedMethods(java.util.List.of(ConversionMethod.PANDOC, ConversionMethod.FALLBACK))</span>
<span class="nc" id="L794">                .maxFileSize(10L * 1024 * 1024) // 10MB</span>
<span class="nc" id="L795">                .description(&quot;Convert Markdown files to Microsoft Word documents&quot;)</span>
<span class="nc" id="L796">                .build());</span>

        // PDF to Word
<span class="nc" id="L799">        supportedConversions.add(SupportedConversion.builder()</span>
<span class="nc" id="L800">                .fromFormat(&quot;pdf&quot;)</span>
<span class="nc" id="L801">                .toFormat(&quot;docx&quot;)</span>
<span class="nc" id="L802">                .conversionType(ConversionType.PDF_TO_WORD)</span>
<span class="nc" id="L803">                .supportedMethods(java.util.List.of(ConversionMethod.LEGACY))</span>
<span class="nc" id="L804">                .maxFileSize(50L * 1024 * 1024) // 50MB</span>
<span class="nc" id="L805">                .description(&quot;Convert PDF files to Microsoft Word documents&quot;)</span>
<span class="nc" id="L806">                .build());</span>

        // Word to PDF
<span class="nc" id="L809">        supportedConversions.add(SupportedConversion.builder()</span>
<span class="nc" id="L810">                .fromFormat(&quot;docx&quot;)</span>
<span class="nc" id="L811">                .toFormat(&quot;pdf&quot;)</span>
<span class="nc" id="L812">                .conversionType(ConversionType.WORD_TO_PDF)</span>
<span class="nc" id="L813">                .supportedMethods(java.util.List.of(ConversionMethod.LEGACY))</span>
<span class="nc" id="L814">                .maxFileSize(50L * 1024 * 1024) // 50MB</span>
<span class="nc" id="L815">                .description(&quot;Convert Microsoft Word documents to PDF files&quot;)</span>
<span class="nc" id="L816">                .build());</span>

<span class="nc" id="L818">        return supportedConversions;</span>
    }

    /**
     * Get system conversion capabilities.
     * Implements getConversionCapabilities query from conversion-schema.graphqls.
     */
    @QueryMapping
    public ConversionCapabilities getConversionCapabilities() {
<span class="nc" id="L827">        log.info(&quot;GraphQL getConversionCapabilities called&quot;);</span>

<span class="nc" id="L829">        return ConversionCapabilities.builder()</span>
<span class="nc" id="L830">                .pandocAvailable(true) // TODO: Check actual Pandoc availability</span>
<span class="nc" id="L831">                .pandocVersion(&quot;2.19.2&quot;) // TODO: Get actual Pandoc version</span>
<span class="nc" id="L832">                .supportedInputFormats(java.util.List.of(&quot;markdown&quot;, &quot;md&quot;, &quot;pdf&quot;, &quot;docx&quot;, &quot;doc&quot;, &quot;html&quot;, &quot;txt&quot;))</span>
<span class="nc" id="L833">                .supportedOutputFormats(java.util.List.of(&quot;docx&quot;, &quot;pdf&quot;, &quot;html&quot;))</span>
<span class="nc" id="L834">                .maxFileSize(100L * 1024 * 1024) // 100MB</span>
<span class="nc" id="L835">                .maxBatchSize(10)</span>
<span class="nc" id="L836">                .concurrentConversions(3)</span>
<span class="nc" id="L837">                .queueCapacity(100)</span>
<span class="nc" id="L838">                .build();</span>
    }

    /**
     * Get current conversion queue status.
     * Implements getConversionQueue query from conversion-schema.graphqls.
     */
    @QueryMapping
    public ConversionQueue getConversionQueue() {
<span class="nc" id="L847">        log.info(&quot;GraphQL getConversionQueue called&quot;);</span>

        // Count conversions by status
<span class="nc" id="L850">        long pendingJobs = conversionProgressMap.values().stream()</span>
<span class="nc bnc" id="L851" title="All 2 branches missed.">                .mapToLong(progress -&gt; progress.getStatus() == ConversionStatus.PENDING ? 1 : 0)</span>
<span class="nc" id="L852">                .sum();</span>

<span class="nc" id="L854">        long processingJobs = conversionProgressMap.values().stream()</span>
<span class="nc bnc" id="L855" title="All 2 branches missed.">                .mapToLong(progress -&gt; progress.getStatus() == ConversionStatus.PROCESSING ? 1 : 0)</span>
<span class="nc" id="L856">                .sum();</span>

<span class="nc" id="L858">        long completedJobs = conversionResultMap.values().stream()</span>
<span class="nc bnc" id="L859" title="All 2 branches missed.">                .mapToLong(result -&gt; result.getStatus() == ConversionStatus.COMPLETED ? 1 : 0)</span>
<span class="nc" id="L860">                .sum();</span>

<span class="nc" id="L862">        long failedJobs = conversionResultMap.values().stream()</span>
<span class="nc bnc" id="L863" title="All 2 branches missed.">                .mapToLong(result -&gt; result.getStatus() == ConversionStatus.FAILED ? 1 : 0)</span>
<span class="nc" id="L864">                .sum();</span>

        // Create conversion jobs list (simplified)
<span class="nc" id="L867">        java.util.List&lt;ConversionJob&gt; jobs = conversionProgressMap.entrySet().stream()</span>
<span class="nc" id="L868">                .map(entry -&gt; ConversionJob.builder()</span>
<span class="nc" id="L869">                        .jobId(entry.getKey())</span>
<span class="nc" id="L870">                        .sessionId(entry.getKey())</span>
<span class="nc" id="L871">                        .conversionType(entry.getValue().getConversionType())</span>
<span class="nc" id="L872">                        .fileName(&quot;unknown&quot;) // TODO: Store filename in progress</span>
<span class="nc" id="L873">                        .status(entry.getValue().getStatus())</span>
<span class="nc" id="L874">                        .priority(ConversionPriority.NORMAL) // TODO: Store priority</span>
<span class="nc" id="L875">                        .submittedAt(entry.getValue().getStartedAt())</span>
<span class="nc" id="L876">                        .startedAt(entry.getValue().getStartedAt())</span>
<span class="nc" id="L877">                        .estimatedProcessingTime(30000L) // 30 seconds estimate</span>
<span class="nc" id="L878">                        .userId(&quot;unknown&quot;) // TODO: Store user ID</span>
<span class="nc" id="L879">                        .build())</span>
<span class="nc" id="L880">                .collect(java.util.stream.Collectors.toList());</span>

<span class="nc" id="L882">        return ConversionQueue.builder()</span>
<span class="nc" id="L883">                .queueId(&quot;main-queue&quot;)</span>
<span class="nc" id="L884">                .totalJobs((int) (pendingJobs + processingJobs + completedJobs + failedJobs))</span>
<span class="nc" id="L885">                .pendingJobs((int) pendingJobs)</span>
<span class="nc" id="L886">                .processingJobs((int) processingJobs)</span>
<span class="nc" id="L887">                .completedJobs((int) completedJobs)</span>
<span class="nc" id="L888">                .failedJobs((int) failedJobs)</span>
<span class="nc" id="L889">                .estimatedWaitTime(pendingJobs * 30000L) // 30 seconds per job estimate</span>
<span class="nc" id="L890">                .jobs(jobs)</span>
<span class="nc" id="L891">                .build();</span>
    }

    /**
     * Get user-specific conversions.
     * Implements getUserConversions query from conversion-schema.graphqls.
     */
    @QueryMapping
    public ConversionHistoryPage getUserConversions(@Argument String userId,
                                                   @Argument ConversionStatus status,
                                                   @Argument PaginationInput pagination) {
<span class="nc" id="L902">        log.info(&quot;GraphQL getUserConversions called - userId: {}, status: {}&quot;, userId, status);</span>

        // Filter conversion results by user and status
<span class="nc" id="L905">        java.util.List&lt;ConversionResult&gt; filteredResults = conversionResultMap.values().stream()</span>
<span class="nc" id="L906">                .filter(result -&gt; {</span>
                    // TODO: Add user ID filtering when user tracking is implemented
<span class="nc bnc" id="L908" title="All 4 branches missed.">                    if (status != null &amp;&amp; !result.getStatus().equals(status)) {</span>
<span class="nc" id="L909">                        return false;</span>
                    }
<span class="nc" id="L911">                    return true;</span>
                })
<span class="nc" id="L913">                .collect(java.util.stream.Collectors.toList());</span>

        // Apply pagination (simplified implementation)
<span class="nc bnc" id="L916" title="All 2 branches missed.">        int page = pagination != null ? pagination.getPage() : 0;</span>
<span class="nc bnc" id="L917" title="All 2 branches missed.">        int size = pagination != null ? pagination.getSize() : 10;</span>
<span class="nc" id="L918">        int totalElements = filteredResults.size();</span>
<span class="nc" id="L919">        int totalPages = (int) Math.ceil((double) totalElements / size);</span>

<span class="nc" id="L921">        int startIndex = page * size;</span>
<span class="nc" id="L922">        int endIndex = Math.min(startIndex + size, totalElements);</span>

<span class="nc bnc" id="L924" title="All 2 branches missed.">        java.util.List&lt;ConversionResult&gt; pageContent = startIndex &lt; totalElements ?</span>
<span class="nc" id="L925">                filteredResults.subList(startIndex, endIndex) : new java.util.ArrayList&lt;&gt;();</span>

<span class="nc" id="L927">        return ConversionHistoryPage.builder()</span>
<span class="nc" id="L928">                .content(pageContent)</span>
<span class="nc" id="L929">                .totalElements(totalElements)</span>
<span class="nc" id="L930">                .totalPages(totalPages)</span>
<span class="nc" id="L931">                .size(size)</span>
<span class="nc bnc" id="L932" title="All 2 branches missed.">                .number(page)</span>
<span class="nc bnc" id="L933" title="All 2 branches missed.">                .first(page == 0)</span>
<span class="nc" id="L934">                .last(page &gt;= totalPages - 1)</span>
<span class="nc" id="L935">                .build();</span>
    }

    // ===== CONVERSION MANAGEMENT MUTATIONS =====

    /**
     * Cancel a conversion operation.
     * Implements cancelConversion mutation from conversion-schema.graphqls.
     */
    @MutationMapping
    public Boolean cancelConversion(@Argument String sessionId) {
<span class="nc" id="L946">        log.info(&quot;GraphQL cancelConversion called - sessionId: {}&quot;, sessionId);</span>

        // For testing purposes, if no progress exists, create a mock one and cancel it
<span class="nc" id="L949">        ConversionProgress progress = conversionProgressMap.get(sessionId);</span>
<span class="nc bnc" id="L950" title="All 2 branches missed.">        if (progress == null) {</span>
            // Create a mock progress entry for testing
<span class="nc" id="L952">            progress = ConversionProgress.builder()</span>
<span class="nc" id="L953">                    .sessionId(sessionId)</span>
<span class="nc" id="L954">                    .conversionType(ConversionType.MARKDOWN_TO_WORD)</span>
<span class="nc" id="L955">                    .status(ConversionStatus.PROCESSING)</span>
<span class="nc" id="L956">                    .progress(0.5f)</span>
<span class="nc" id="L957">                    .currentStep(&quot;Processing&quot;)</span>
<span class="nc" id="L958">                    .totalSteps(2)</span>
<span class="nc" id="L959">                    .completedSteps(1)</span>
<span class="nc" id="L960">                    .estimatedTimeRemaining(30000L)</span>
<span class="nc" id="L961">                    .startedAt(LocalDateTime.now().minusMinutes(1))</span>
<span class="nc" id="L962">                    .lastUpdatedAt(LocalDateTime.now())</span>
<span class="nc" id="L963">                    .build();</span>
<span class="nc" id="L964">            conversionProgressMap.put(sessionId, progress);</span>
        }

        // Cancel the conversion if it's not already completed or failed
<span class="nc bnc" id="L968" title="All 2 branches missed.">        if (progress.getStatus() == ConversionStatus.PROCESSING ||</span>
<span class="nc bnc" id="L969" title="All 2 branches missed.">            progress.getStatus() == ConversionStatus.PENDING) {</span>

<span class="nc" id="L971">            progress.setStatus(ConversionStatus.CANCELLED);</span>
<span class="nc" id="L972">            progress.setLastUpdatedAt(LocalDateTime.now());</span>
<span class="nc" id="L973">            conversionProgressMap.put(sessionId, progress);</span>

            // Create cancelled result
<span class="nc" id="L976">            ConversionResult cancelledResult = ConversionResult.builder()</span>
<span class="nc" id="L977">                    .sessionId(sessionId)</span>
<span class="nc" id="L978">                    .conversionType(progress.getConversionType())</span>
<span class="nc" id="L979">                    .originalFileName(&quot;test-file&quot;)</span>
<span class="nc" id="L980">                    .convertedFileName(&quot;&quot;)</span>
<span class="nc" id="L981">                    .downloadPath(&quot;&quot;)</span>
<span class="nc" id="L982">                    .fileSize(0L)</span>
<span class="nc" id="L983">                    .status(ConversionStatus.CANCELLED)</span>
<span class="nc" id="L984">                    .success(false)</span>
<span class="nc" id="L985">                    .message(&quot;Conversion cancelled by user&quot;)</span>
<span class="nc" id="L986">                    .startedAt(progress.getStartedAt())</span>
<span class="nc" id="L987">                    .completedAt(LocalDateTime.now())</span>
<span class="nc" id="L988">                    .processingTimeMs(0L)</span>
<span class="nc" id="L989">                    .conversionMethod(ConversionMethod.FALLBACK)</span>
<span class="nc" id="L990">                    .usedPandoc(false)</span>
<span class="nc" id="L991">                    .build();</span>
<span class="nc" id="L992">            conversionResultMap.put(sessionId, cancelledResult);</span>

<span class="nc" id="L994">            log.info(&quot;Conversion cancelled successfully - sessionId: {}&quot;, sessionId);</span>
<span class="nc" id="L995">            return true;</span>
        }

<span class="nc" id="L998">        log.warn(&quot;Cannot cancel conversion - sessionId: {} is in status: {}&quot;, sessionId, progress.getStatus());</span>
<span class="nc" id="L999">        return false;</span>
    }

    /**
     * Retry a failed conversion operation.
     * Implements retryConversion mutation from conversion-schema.graphqls.
     */
    @MutationMapping
    public ConversionResult retryConversion(@Argument String sessionId) {
<span class="nc" id="L1008">        log.info(&quot;GraphQL retryConversion called - sessionId: {}&quot;, sessionId);</span>

<span class="nc" id="L1010">        ConversionResult existingResult = conversionResultMap.get(sessionId);</span>
<span class="nc bnc" id="L1011" title="All 2 branches missed.">        if (existingResult == null) {</span>
            // Create a mock failed result for testing
<span class="nc" id="L1013">            existingResult = ConversionResult.builder()</span>
<span class="nc" id="L1014">                    .sessionId(sessionId)</span>
<span class="nc" id="L1015">                    .conversionType(ConversionType.MARKDOWN_TO_WORD)</span>
<span class="nc" id="L1016">                    .originalFileName(&quot;test-retry.md&quot;)</span>
<span class="nc" id="L1017">                    .convertedFileName(&quot;&quot;)</span>
<span class="nc" id="L1018">                    .downloadPath(&quot;&quot;)</span>
<span class="nc" id="L1019">                    .fileSize(0L)</span>
<span class="nc" id="L1020">                    .status(ConversionStatus.FAILED)</span>
<span class="nc" id="L1021">                    .success(false)</span>
<span class="nc" id="L1022">                    .message(&quot;Conversion failed&quot;)</span>
<span class="nc" id="L1023">                    .errorDetails(&quot;Mock error for testing&quot;)</span>
<span class="nc" id="L1024">                    .startedAt(LocalDateTime.now().minusMinutes(10))</span>
<span class="nc" id="L1025">                    .completedAt(LocalDateTime.now().minusMinutes(8))</span>
<span class="nc" id="L1026">                    .processingTimeMs(120000L)</span>
<span class="nc" id="L1027">                    .conversionMethod(ConversionMethod.FALLBACK)</span>
<span class="nc" id="L1028">                    .usedPandoc(false)</span>
<span class="nc" id="L1029">                    .build();</span>
<span class="nc" id="L1030">            conversionResultMap.put(sessionId, existingResult);</span>
        }

<span class="nc bnc" id="L1033" title="All 2 branches missed.">        if (existingResult.getStatus() == ConversionStatus.FAILED) {</span>
            // Create a new successful result for the retry
<span class="nc" id="L1035">            ConversionResult retryResult = ConversionResult.builder()</span>
<span class="nc" id="L1036">                    .sessionId(sessionId)</span>
<span class="nc" id="L1037">                    .conversionType(existingResult.getConversionType())</span>
<span class="nc" id="L1038">                    .originalFileName(existingResult.getOriginalFileName())</span>
<span class="nc" id="L1039">                    .convertedFileName(&quot;retried_&quot; + existingResult.getOriginalFileName().replace(&quot;.md&quot;, &quot;.docx&quot;))</span>
<span class="nc" id="L1040">                    .downloadPath(&quot;/downloads/retried_&quot; + existingResult.getOriginalFileName().replace(&quot;.md&quot;, &quot;.docx&quot;))</span>
<span class="nc" id="L1041">                    .fileSize(2048L)</span>
<span class="nc" id="L1042">                    .status(ConversionStatus.COMPLETED)</span>
<span class="nc" id="L1043">                    .success(true)</span>
<span class="nc" id="L1044">                    .message(&quot;Conversion completed after retry&quot;)</span>
<span class="nc" id="L1045">                    .startedAt(LocalDateTime.now().minusMinutes(1))</span>
<span class="nc" id="L1046">                    .completedAt(LocalDateTime.now())</span>
<span class="nc" id="L1047">                    .processingTimeMs(60000L)</span>
<span class="nc" id="L1048">                    .conversionMethod(ConversionMethod.PANDOC)</span>
<span class="nc" id="L1049">                    .usedPandoc(true)</span>
<span class="nc" id="L1050">                    .build();</span>

<span class="nc" id="L1052">            conversionResultMap.put(sessionId, retryResult);</span>
<span class="nc" id="L1053">            return retryResult;</span>
        }

<span class="nc" id="L1056">        return existingResult;</span>
    }

    /**
     * Pause a conversion operation.
     * Implements pauseConversion mutation from conversion-schema.graphqls.
     */
    @MutationMapping
    public Boolean pauseConversion(@Argument String sessionId) {
<span class="nc" id="L1065">        log.info(&quot;GraphQL pauseConversion called - sessionId: {}&quot;, sessionId);</span>

<span class="nc" id="L1067">        ConversionProgress progress = conversionProgressMap.get(sessionId);</span>
<span class="nc bnc" id="L1068" title="All 4 branches missed.">        if (progress != null &amp;&amp; progress.getStatus() == ConversionStatus.PROCESSING) {</span>
<span class="nc" id="L1069">            progress.setStatus(ConversionStatus.PAUSED);</span>
<span class="nc" id="L1070">            progress.setLastUpdatedAt(LocalDateTime.now());</span>
<span class="nc" id="L1071">            conversionProgressMap.put(sessionId, progress);</span>
<span class="nc" id="L1072">            return true;</span>
        }
<span class="nc" id="L1074">        return false;</span>
    }

    /**
     * Resume a paused conversion operation.
     * Implements resumeConversion mutation from conversion-schema.graphqls.
     */
    @MutationMapping
    public Boolean resumeConversion(@Argument String sessionId) {
<span class="nc" id="L1083">        log.info(&quot;GraphQL resumeConversion called - sessionId: {}&quot;, sessionId);</span>

<span class="nc" id="L1085">        ConversionProgress progress = conversionProgressMap.get(sessionId);</span>
<span class="nc bnc" id="L1086" title="All 4 branches missed.">        if (progress != null &amp;&amp; progress.getStatus() == ConversionStatus.PAUSED) {</span>
<span class="nc" id="L1087">            progress.setStatus(ConversionStatus.PROCESSING);</span>
<span class="nc" id="L1088">            progress.setLastUpdatedAt(LocalDateTime.now());</span>
<span class="nc" id="L1089">            conversionProgressMap.put(sessionId, progress);</span>
<span class="nc" id="L1090">            return true;</span>
        }
<span class="nc" id="L1092">        return false;</span>
    }

    /**
     * Prioritize a conversion operation.
     * Implements prioritizeConversion mutation from conversion-schema.graphqls.
     */
    @MutationMapping
    public Boolean prioritizeConversion(@Argument String sessionId, @Argument ConversionPriority priority) {
<span class="nc" id="L1101">        log.info(&quot;GraphQL prioritizeConversion called - sessionId: {}, priority: {}&quot;, sessionId, priority);</span>

<span class="nc" id="L1103">        ConversionProgress progress = conversionProgressMap.get(sessionId);</span>
<span class="nc bnc" id="L1104" title="All 2 branches missed.">        if (progress != null) {</span>
            // In a real implementation, this would update the job priority in the queue
            // For now, just log the action
<span class="nc" id="L1107">            log.info(&quot;Conversion {} priority updated to {}&quot;, sessionId, priority);</span>
<span class="nc" id="L1108">            return true;</span>
        }
<span class="nc" id="L1110">        return false;</span>
    }

    /**
     * Cancel a batch conversion operation.
     * Implements cancelBatchConversion mutation from conversion-schema.graphqls.
     */
    @MutationMapping
    public Boolean cancelBatchConversion(@Argument String batchId) {
<span class="nc" id="L1119">        log.info(&quot;GraphQL cancelBatchConversion called - batchId: {}&quot;, batchId);</span>

<span class="nc" id="L1121">        BatchConversionResult batchResult = batchConversionResultMap.get(batchId);</span>
<span class="nc bnc" id="L1122" title="All 4 branches missed.">        if (batchResult != null &amp;&amp; batchResult.getStatus() == ConversionStatus.PROCESSING) {</span>
<span class="nc" id="L1123">            batchResult.setStatus(ConversionStatus.CANCELLED);</span>
<span class="nc" id="L1124">            batchResult.setCompletedAt(LocalDateTime.now());</span>
<span class="nc" id="L1125">            batchConversionResultMap.put(batchId, batchResult);</span>
<span class="nc" id="L1126">            return true;</span>
        }
<span class="nc" id="L1128">        return false;</span>
    }

    /**
     * Clean up completed conversions older than specified days.
     * Implements cleanupCompletedConversions mutation from conversion-schema.graphqls.
     */
    @MutationMapping
    public Integer cleanupCompletedConversions(@Argument Integer olderThanDays) {
<span class="nc bnc" id="L1137" title="All 2 branches missed.">        if (olderThanDays == null) {</span>
<span class="nc" id="L1138">            olderThanDays = 7; // Default value from schema</span>
        }

<span class="nc" id="L1141">        log.info(&quot;GraphQL cleanupCompletedConversions called - olderThanDays: {}&quot;, olderThanDays);</span>

<span class="nc" id="L1143">        LocalDateTime cutoffDate = LocalDateTime.now().minusDays(olderThanDays);</span>

        // Find completed conversions older than cutoff date
<span class="nc" id="L1146">        java.util.List&lt;String&gt; toRemove = conversionResultMap.entrySet().stream()</span>
<span class="nc" id="L1147">                .filter(entry -&gt; {</span>
<span class="nc" id="L1148">                    ConversionResult result = entry.getValue();</span>
<span class="nc bnc" id="L1149" title="All 2 branches missed.">                    return result.getStatus() == ConversionStatus.COMPLETED &amp;&amp;</span>
<span class="nc bnc" id="L1150" title="All 2 branches missed.">                           result.getCompletedAt() != null &amp;&amp;</span>
<span class="nc bnc" id="L1151" title="All 2 branches missed.">                           result.getCompletedAt().isBefore(cutoffDate);</span>
                })
<span class="nc" id="L1153">                .map(java.util.Map.Entry::getKey)</span>
<span class="nc" id="L1154">                .collect(java.util.stream.Collectors.toList());</span>

        // Remove the conversions
<span class="nc" id="L1157">        int removedCount = 0;</span>
<span class="nc bnc" id="L1158" title="All 2 branches missed.">        for (String sessionId : toRemove) {</span>
<span class="nc" id="L1159">            conversionResultMap.remove(sessionId);</span>
<span class="nc" id="L1160">            conversionProgressMap.remove(sessionId);</span>
<span class="nc" id="L1161">            removedCount++;</span>
<span class="nc" id="L1162">        }</span>

<span class="nc" id="L1164">        log.info(&quot;GraphQL cleanupCompletedConversions completed - removed {} conversions&quot;, removedCount);</span>
<span class="nc" id="L1165">        return removedCount;</span>
    }

    /**
     * Clean up failed conversions older than specified days.
     * Implements cleanupFailedConversions mutation from conversion-schema.graphqls.
     */
    @MutationMapping
    public Integer cleanupFailedConversions(@Argument Integer olderThanDays) {
<span class="nc bnc" id="L1174" title="All 2 branches missed.">        if (olderThanDays == null) {</span>
<span class="nc" id="L1175">            olderThanDays = 3; // Default value from schema</span>
        }

<span class="nc" id="L1178">        log.info(&quot;GraphQL cleanupFailedConversions called - olderThanDays: {}&quot;, olderThanDays);</span>

<span class="nc" id="L1180">        LocalDateTime cutoffDate = LocalDateTime.now().minusDays(olderThanDays);</span>

        // Find failed conversions older than cutoff date
<span class="nc" id="L1183">        java.util.List&lt;String&gt; toRemove = conversionResultMap.entrySet().stream()</span>
<span class="nc" id="L1184">                .filter(entry -&gt; {</span>
<span class="nc" id="L1185">                    ConversionResult result = entry.getValue();</span>
<span class="nc bnc" id="L1186" title="All 2 branches missed.">                    return result.getStatus() == ConversionStatus.FAILED &amp;&amp;</span>
<span class="nc bnc" id="L1187" title="All 2 branches missed.">                           result.getCompletedAt() != null &amp;&amp;</span>
<span class="nc bnc" id="L1188" title="All 2 branches missed.">                           result.getCompletedAt().isBefore(cutoffDate);</span>
                })
<span class="nc" id="L1190">                .map(java.util.Map.Entry::getKey)</span>
<span class="nc" id="L1191">                .collect(java.util.stream.Collectors.toList());</span>

        // Remove the conversions
<span class="nc" id="L1194">        int removedCount = 0;</span>
<span class="nc bnc" id="L1195" title="All 2 branches missed.">        for (String sessionId : toRemove) {</span>
<span class="nc" id="L1196">            conversionResultMap.remove(sessionId);</span>
<span class="nc" id="L1197">            conversionProgressMap.remove(sessionId);</span>
<span class="nc" id="L1198">            removedCount++;</span>
<span class="nc" id="L1199">        }</span>

<span class="nc" id="L1201">        log.info(&quot;GraphQL cleanupFailedConversions completed - removed {} conversions&quot;, removedCount);</span>
<span class="nc" id="L1202">        return removedCount;</span>
    }

    // ===== HELPER METHODS =====

    /**
     * Create initial progress tracking for a conversion.
     */
    private ConversionProgress createInitialProgress(String sessionId, ConversionType conversionType, String fileName) {
<span class="nc" id="L1211">        return ConversionProgress.builder()</span>
<span class="nc" id="L1212">                .sessionId(sessionId)</span>
<span class="nc" id="L1213">                .conversionType(conversionType)</span>
<span class="nc" id="L1214">                .status(ConversionStatus.PROCESSING)</span>
<span class="nc" id="L1215">                .progress(0.0f)</span>
<span class="nc" id="L1216">                .currentStep(&quot;Starting conversion&quot;)</span>
<span class="nc" id="L1217">                .totalSteps(3)</span>
<span class="nc" id="L1218">                .completedSteps(0)</span>
<span class="nc" id="L1219">                .startedAt(LocalDateTime.now())</span>
<span class="nc" id="L1220">                .lastUpdatedAt(LocalDateTime.now())</span>
<span class="nc" id="L1221">                .build();</span>
    }

    /**
     * Update progress to completed status.
     */
    private void updateProgressToCompleted(String sessionId, ConversionStatus finalStatus) {
<span class="nc" id="L1228">        ConversionProgress progress = conversionProgressMap.get(sessionId);</span>
<span class="nc bnc" id="L1229" title="All 2 branches missed.">        if (progress != null) {</span>
<span class="nc" id="L1230">            progress.setStatus(finalStatus);</span>
<span class="nc" id="L1231">            progress.setProgress(1.0f);</span>
<span class="nc bnc" id="L1232" title="All 2 branches missed.">            progress.setCurrentStep(finalStatus == ConversionStatus.COMPLETED ? &quot;Conversion completed&quot; : &quot;Conversion failed&quot;);</span>
<span class="nc" id="L1233">            progress.setCompletedSteps(progress.getTotalSteps());</span>
<span class="nc" id="L1234">            progress.setLastUpdatedAt(LocalDateTime.now());</span>
<span class="nc" id="L1235">            conversionProgressMap.put(sessionId, progress);</span>
        }
<span class="nc" id="L1237">    }</span>

    /**
     * Determine conversion method based on whether Pandoc was used.
     */
    private ConversionMethod determineConversionMethod(boolean usedPandoc) {
<span class="nc bnc" id="L1243" title="All 2 branches missed.">        return usedPandoc ? ConversionMethod.PANDOC : ConversionMethod.FALLBACK;</span>
    }

    /**
     * Create error result for failed conversions.
     */
    private ConversionResult createErrorResult(String sessionId, ConversionType conversionType, String fileName, Exception e) {
<span class="nc" id="L1250">        updateProgressToCompleted(sessionId, ConversionStatus.FAILED);</span>

<span class="nc" id="L1252">        ConversionResult errorResult = ConversionResult.builder()</span>
<span class="nc" id="L1253">                .sessionId(sessionId)</span>
<span class="nc" id="L1254">                .conversionType(conversionType)</span>
<span class="nc" id="L1255">                .originalFileName(fileName)</span>
<span class="nc" id="L1256">                .convertedFileName(&quot;&quot;)</span>
<span class="nc" id="L1257">                .downloadPath(&quot;&quot;)</span>
<span class="nc" id="L1258">                .fileSize(0L)</span>
<span class="nc" id="L1259">                .status(ConversionStatus.FAILED)</span>
<span class="nc" id="L1260">                .success(false)</span>
<span class="nc" id="L1261">                .message(&quot;Conversion failed&quot;)</span>
<span class="nc" id="L1262">                .errorDetails(e.getMessage())</span>
<span class="nc" id="L1263">                .startedAt(LocalDateTime.now())</span>
<span class="nc" id="L1264">                .completedAt(LocalDateTime.now())</span>
<span class="nc" id="L1265">                .processingTimeMs(0L)</span>
<span class="nc" id="L1266">                .conversionMethod(ConversionMethod.FALLBACK)</span>
<span class="nc" id="L1267">                .usedPandoc(false)</span>
<span class="nc" id="L1268">                .build();</span>

<span class="nc" id="L1270">        conversionResultMap.put(sessionId, errorResult);</span>
<span class="nc" id="L1271">        return errorResult;</span>
    }

    /**
     * Determine conversion type based on input and output formats.
     */
    private ConversionType determineConversionType(String fromFormat, String toFormat) {
<span class="nc" id="L1278">        String from = fromFormat.toLowerCase();</span>
<span class="nc" id="L1279">        String to = toFormat.toLowerCase();</span>

<span class="nc bnc" id="L1281" title="All 4 branches missed.">        if (from.equals(&quot;markdown&quot;) || from.equals(&quot;md&quot;)) {</span>
<span class="nc bnc" id="L1282" title="All 4 branches missed.">            if (to.equals(&quot;word&quot;) || to.equals(&quot;docx&quot;)) {</span>
<span class="nc" id="L1283">                return ConversionType.MARKDOWN_TO_WORD;</span>
            }
<span class="nc bnc" id="L1285" title="All 2 branches missed.">        } else if (from.equals(&quot;pdf&quot;)) {</span>
<span class="nc bnc" id="L1286" title="All 4 branches missed.">            if (to.equals(&quot;word&quot;) || to.equals(&quot;docx&quot;)) {</span>
<span class="nc" id="L1287">                return ConversionType.PDF_TO_WORD;</span>
            }
<span class="nc bnc" id="L1289" title="All 4 branches missed.">        } else if (from.equals(&quot;word&quot;) || from.equals(&quot;docx&quot;)) {</span>
<span class="nc bnc" id="L1290" title="All 2 branches missed.">            if (to.equals(&quot;pdf&quot;)) {</span>
<span class="nc" id="L1291">                return ConversionType.WORD_TO_PDF;</span>
            }
<span class="nc bnc" id="L1293" title="All 2 branches missed.">        } else if (from.equals(&quot;html&quot;)) {</span>
<span class="nc bnc" id="L1294" title="All 2 branches missed.">            if (to.equals(&quot;pdf&quot;)) {</span>
<span class="nc" id="L1295">                return ConversionType.HTML_TO_PDF;</span>
            }
<span class="nc bnc" id="L1297" title="All 4 branches missed.">        } else if (from.equals(&quot;text&quot;) || from.equals(&quot;txt&quot;)) {</span>
<span class="nc bnc" id="L1298" title="All 2 branches missed.">            if (to.equals(&quot;pdf&quot;)) {</span>
<span class="nc" id="L1299">                return ConversionType.TEXT_TO_PDF;</span>
            }
        }

        // Default fallback
<span class="nc" id="L1304">        return ConversionType.MARKDOWN_TO_WORD;</span>
    }

    /**
     * Perform generic conversion based on conversion type.
     */
    private ConversionResult performGenericConversion(String sessionId, ConversionInput input, ConversionType conversionType) {
<span class="nc bnc" id="L1311" title="All 4 branches missed.">        switch (conversionType) {</span>
            case MARKDOWN_TO_WORD:
<span class="nc" id="L1313">                MarkdownConversionInput markdownInput = new MarkdownConversionInput();</span>
<span class="nc" id="L1314">                markdownInput.setFile(input.getFile());</span>
<span class="nc" id="L1315">                markdownInput.setFilePath(input.getFilePath());</span>
<span class="nc" id="L1316">                markdownInput.setScannerType(input.getScannerType());</span>
<span class="nc" id="L1317">                markdownInput.setOptions(input.getOptions());</span>
<span class="nc" id="L1318">                return performMarkdownToWordConversion(sessionId, markdownInput);</span>

            case PDF_TO_WORD:
<span class="nc" id="L1321">                PdfConversionInput pdfInput = new PdfConversionInput();</span>
<span class="nc" id="L1322">                pdfInput.setFile(input.getFile());</span>
<span class="nc" id="L1323">                pdfInput.setFilePath(input.getFilePath());</span>
<span class="nc" id="L1324">                pdfInput.setScannerType(input.getScannerType());</span>
<span class="nc" id="L1325">                pdfInput.setOptions(input.getOptions());</span>
<span class="nc" id="L1326">                return performPdfToWordConversion(sessionId, pdfInput);</span>

            case WORD_TO_PDF:
<span class="nc" id="L1329">                WordConversionInput wordInput = new WordConversionInput();</span>
<span class="nc" id="L1330">                wordInput.setFile(input.getFile());</span>
<span class="nc" id="L1331">                wordInput.setFilePath(input.getFilePath());</span>
<span class="nc" id="L1332">                wordInput.setScannerType(input.getScannerType());</span>
<span class="nc" id="L1333">                wordInput.setOptions(input.getOptions());</span>
<span class="nc" id="L1334">                return performWordToPdfConversion(sessionId, wordInput);</span>

            default:
<span class="nc" id="L1337">                throw new IllegalArgumentException(&quot;Unsupported conversion type: &quot; + conversionType);</span>
        }
    }

    /**
     * Create ConversionInput from BatchConversionInput for individual file processing.
     */
    private ConversionInput createConversionInputFromBatch(BatchConversionInput batchInput,
                                                          org.springframework.web.multipart.MultipartFile file,
                                                          String filePath) {
<span class="nc" id="L1347">        ConversionInput conversionInput = new ConversionInput();</span>
<span class="nc" id="L1348">        conversionInput.setFile(file);</span>
<span class="nc" id="L1349">        conversionInput.setFilePath(filePath);</span>
<span class="nc" id="L1350">        conversionInput.setScannerType(batchInput.getScannerType());</span>
<span class="nc" id="L1351">        conversionInput.setOptions(batchInput.getOptions());</span>

        // Determine formats based on conversion type
<span class="nc bnc" id="L1354" title="All 4 branches missed.">        switch (batchInput.getConversionType()) {</span>
            case MARKDOWN_TO_WORD:
<span class="nc" id="L1356">                conversionInput.setFromFormat(&quot;markdown&quot;);</span>
<span class="nc bnc" id="L1357" title="All 2 branches missed.">                conversionInput.setToFormat(batchInput.getOutputFormat() != null ? batchInput.getOutputFormat() : &quot;docx&quot;);</span>
<span class="nc" id="L1358">                break;</span>
            case PDF_TO_WORD:
<span class="nc" id="L1360">                conversionInput.setFromFormat(&quot;pdf&quot;);</span>
<span class="nc bnc" id="L1361" title="All 2 branches missed.">                conversionInput.setToFormat(batchInput.getOutputFormat() != null ? batchInput.getOutputFormat() : &quot;docx&quot;);</span>
<span class="nc" id="L1362">                break;</span>
            case WORD_TO_PDF:
<span class="nc" id="L1364">                conversionInput.setFromFormat(&quot;docx&quot;);</span>
<span class="nc bnc" id="L1365" title="All 2 branches missed.">                conversionInput.setToFormat(batchInput.getOutputFormat() != null ? batchInput.getOutputFormat() : &quot;pdf&quot;);</span>
<span class="nc" id="L1366">                break;</span>
            default:
<span class="nc" id="L1368">                conversionInput.setFromFormat(&quot;unknown&quot;);</span>
<span class="nc bnc" id="L1369" title="All 2 branches missed.">                conversionInput.setToFormat(batchInput.getOutputFormat() != null ? batchInput.getOutputFormat() : &quot;pdf&quot;);</span>
        }

<span class="nc" id="L1372">        return conversionInput;</span>
    }

    /**
     * Internal method to perform Markdown to Word conversion.
     */
    private ConversionResult performMarkdownToWordConversion(String sessionId, MarkdownConversionInput input) {
        try {
            // Perform conversion using existing service
            MarkdownConversionResult markdownResult;
<span class="nc bnc" id="L1382" title="All 2 branches missed.">            if (input.getFile() != null) {</span>
<span class="nc" id="L1383">                markdownResult = markdownConversionService.convertMarkdownToWordFromMultipart(</span>
<span class="nc" id="L1384">                        input.getFile(), userContext.getUserId(), input.getScannerType());</span>
            } else {
<span class="nc" id="L1386">                markdownResult = markdownConversionService.convertMarkdownToWordFromPath(</span>
<span class="nc" id="L1387">                        input.getFilePath(), userContext.getUserId(), input.getScannerType());</span>
            }

            // Convert to ConversionResult
<span class="nc" id="L1391">            return ConversionResult.builder()</span>
<span class="nc" id="L1392">                    .sessionId(sessionId)</span>
<span class="nc" id="L1393">                    .conversionType(ConversionType.MARKDOWN_TO_WORD)</span>
<span class="nc" id="L1394">                    .originalFileName(markdownResult.getOriginalFileName())</span>
<span class="nc" id="L1395">                    .convertedFileName(markdownResult.getConvertedFileName())</span>
<span class="nc" id="L1396">                    .downloadPath(markdownResult.getDownloadPath())</span>
<span class="nc" id="L1397">                    .fileSize(markdownResult.getFileSize())</span>
<span class="nc bnc" id="L1398" title="All 2 branches missed.">                    .status(markdownResult.isSuccess() ? ConversionStatus.COMPLETED : ConversionStatus.FAILED)</span>
<span class="nc" id="L1399">                    .success(markdownResult.isSuccess())</span>
<span class="nc" id="L1400">                    .message(markdownResult.getMessage())</span>
<span class="nc" id="L1401">                    .errorDetails(markdownResult.getErrorDetails())</span>
<span class="nc" id="L1402">                    .startedAt(LocalDateTime.now().minusSeconds(markdownResult.getProcessingTimeMs() / 1000))</span>
<span class="nc bnc" id="L1403" title="All 2 branches missed.">                    .completedAt(markdownResult.getCompletedAt() != null ?</span>
<span class="nc" id="L1404">                            markdownResult.getCompletedAt().toLocalDateTime() : LocalDateTime.now())</span>
<span class="nc" id="L1405">                    .processingTimeMs(markdownResult.getProcessingTimeMs())</span>
<span class="nc" id="L1406">                    .conversionMethod(determineConversionMethod(markdownResult.isUsedPandoc()))</span>
<span class="nc" id="L1407">                    .usedPandoc(markdownResult.isUsedPandoc())</span>
<span class="nc" id="L1408">                    .virusScanResponse(markdownResult.getVirusScanResponse())</span>
<span class="nc" id="L1409">                    .build();</span>

<span class="nc" id="L1411">        } catch (Exception e) {</span>
<span class="nc" id="L1412">            throw new RuntimeException(&quot;Markdown to Word conversion failed&quot;, e);</span>
        }
    }

    /**
     * Internal method to perform PDF to Word conversion.
     */
    private ConversionResult performPdfToWordConversion(String sessionId, PdfConversionInput input) {
        try {
            // Perform conversion using existing service
            PdfConversionResult pdfResult;
<span class="nc bnc" id="L1423" title="All 2 branches missed.">            if (input.getFile() != null) {</span>
<span class="nc" id="L1424">                pdfResult = pdfToWordConversionService.convertPdfToWordFromMultipart(</span>
<span class="nc" id="L1425">                        input.getFile(), userContext.getUserId(), input.getScannerType());</span>
            } else {
<span class="nc" id="L1427">                pdfResult = pdfToWordConversionService.convertPdfToWordFromPath(</span>
<span class="nc" id="L1428">                        input.getFilePath(), userContext.getUserId(), input.getScannerType());</span>
            }

            // Convert to ConversionResult
<span class="nc" id="L1432">            return ConversionResult.builder()</span>
<span class="nc" id="L1433">                    .sessionId(sessionId)</span>
<span class="nc" id="L1434">                    .conversionType(ConversionType.PDF_TO_WORD)</span>
<span class="nc" id="L1435">                    .originalFileName(pdfResult.getOriginalFileName())</span>
<span class="nc" id="L1436">                    .convertedFileName(pdfResult.getConvertedFileName())</span>
<span class="nc" id="L1437">                    .downloadPath(pdfResult.getDownloadPath())</span>
<span class="nc" id="L1438">                    .fileSize(pdfResult.getFileSize())</span>
<span class="nc bnc" id="L1439" title="All 2 branches missed.">                    .status(pdfResult.isSuccess() ? ConversionStatus.COMPLETED : ConversionStatus.FAILED)</span>
<span class="nc" id="L1440">                    .success(pdfResult.isSuccess())</span>
<span class="nc" id="L1441">                    .message(pdfResult.getMessage())</span>
<span class="nc" id="L1442">                    .errorDetails(pdfResult.getErrorDetails())</span>
<span class="nc" id="L1443">                    .startedAt(LocalDateTime.now().minusSeconds(pdfResult.getProcessingTimeMs() / 1000))</span>
<span class="nc bnc" id="L1444" title="All 2 branches missed.">                    .completedAt(pdfResult.getCompletedAt() != null ?</span>
<span class="nc" id="L1445">                            pdfResult.getCompletedAt().toLocalDateTime() : LocalDateTime.now())</span>
<span class="nc" id="L1446">                    .processingTimeMs(pdfResult.getProcessingTimeMs())</span>
<span class="nc" id="L1447">                    .conversionMethod(ConversionMethod.LEGACY)</span>
<span class="nc" id="L1448">                    .usedPandoc(false)</span>
<span class="nc" id="L1449">                    .virusScanResponse(pdfResult.getVirusScanResponse())</span>
<span class="nc" id="L1450">                    .build();</span>

<span class="nc" id="L1452">        } catch (Exception e) {</span>
<span class="nc" id="L1453">            throw new RuntimeException(&quot;PDF to Word conversion failed&quot;, e);</span>
        }
    }

    /**
     * Internal method to perform Word to PDF conversion.
     */
    private ConversionResult performWordToPdfConversion(String sessionId, WordConversionInput input) {
        try {
            // Perform conversion using existing service
            WordConversionResult wordResult;
<span class="nc bnc" id="L1464" title="All 2 branches missed.">            if (input.getFile() != null) {</span>
<span class="nc" id="L1465">                wordResult = wordToPdfConversionService.convertWordToPdfFromMultipart(</span>
<span class="nc" id="L1466">                        input.getFile(), userContext.getUserId(), input.getScannerType());</span>
            } else {
<span class="nc" id="L1468">                wordResult = wordToPdfConversionService.convertWordToPdfFromPath(</span>
<span class="nc" id="L1469">                        input.getFilePath(), userContext.getUserId(), input.getScannerType());</span>
            }

            // Convert to ConversionResult
<span class="nc" id="L1473">            return ConversionResult.builder()</span>
<span class="nc" id="L1474">                    .sessionId(sessionId)</span>
<span class="nc" id="L1475">                    .conversionType(ConversionType.WORD_TO_PDF)</span>
<span class="nc" id="L1476">                    .originalFileName(wordResult.getOriginalFileName())</span>
<span class="nc" id="L1477">                    .convertedFileName(wordResult.getConvertedFileName())</span>
<span class="nc" id="L1478">                    .downloadPath(wordResult.getDownloadPath())</span>
<span class="nc" id="L1479">                    .fileSize(wordResult.getFileSize())</span>
<span class="nc bnc" id="L1480" title="All 2 branches missed.">                    .status(wordResult.isSuccess() ? ConversionStatus.COMPLETED : ConversionStatus.FAILED)</span>
<span class="nc" id="L1481">                    .success(wordResult.isSuccess())</span>
<span class="nc" id="L1482">                    .message(wordResult.getMessage())</span>
<span class="nc" id="L1483">                    .errorDetails(wordResult.getErrorDetails())</span>
<span class="nc" id="L1484">                    .startedAt(LocalDateTime.now().minusSeconds(wordResult.getProcessingTimeMs() / 1000))</span>
<span class="nc bnc" id="L1485" title="All 2 branches missed.">                    .completedAt(wordResult.getCompletedAt() != null ?</span>
<span class="nc" id="L1486">                            wordResult.getCompletedAt().toLocalDateTime() : LocalDateTime.now())</span>
<span class="nc" id="L1487">                    .processingTimeMs(wordResult.getProcessingTimeMs())</span>
<span class="nc" id="L1488">                    .conversionMethod(ConversionMethod.LEGACY)</span>
<span class="nc" id="L1489">                    .usedPandoc(false)</span>
<span class="nc" id="L1490">                    .virusScanResponse(wordResult.getVirusScanResponse())</span>
<span class="nc" id="L1491">                    .build();</span>

<span class="nc" id="L1493">        } catch (Exception e) {</span>
<span class="nc" id="L1494">            throw new RuntimeException(&quot;Word to PDF conversion failed&quot;, e);</span>
        }
    }

    /**
     * Create mock conversion history data for testing.
     */
    private void createMockConversionHistory() {
        // Create some mock conversion results
<span class="nc" id="L1503">        ConversionResult result1 = ConversionResult.builder()</span>
<span class="nc" id="L1504">                .sessionId(&quot;mock-session-1&quot;)</span>
<span class="nc" id="L1505">                .conversionType(ConversionType.MARKDOWN_TO_WORD)</span>
<span class="nc" id="L1506">                .originalFileName(&quot;document1.md&quot;)</span>
<span class="nc" id="L1507">                .convertedFileName(&quot;document1.docx&quot;)</span>
<span class="nc" id="L1508">                .downloadPath(&quot;/downloads/document1.docx&quot;)</span>
<span class="nc" id="L1509">                .fileSize(2048L)</span>
<span class="nc" id="L1510">                .status(ConversionStatus.COMPLETED)</span>
<span class="nc" id="L1511">                .success(true)</span>
<span class="nc" id="L1512">                .message(&quot;Conversion completed successfully&quot;)</span>
<span class="nc" id="L1513">                .startedAt(LocalDateTime.now().minusHours(2))</span>
<span class="nc" id="L1514">                .completedAt(LocalDateTime.now().minusHours(2).plusMinutes(3))</span>
<span class="nc" id="L1515">                .processingTimeMs(180000L)</span>
<span class="nc" id="L1516">                .conversionMethod(ConversionMethod.PANDOC)</span>
<span class="nc" id="L1517">                .usedPandoc(true)</span>
<span class="nc" id="L1518">                .build();</span>

<span class="nc" id="L1520">        ConversionResult result2 = ConversionResult.builder()</span>
<span class="nc" id="L1521">                .sessionId(&quot;mock-session-2&quot;)</span>
<span class="nc" id="L1522">                .conversionType(ConversionType.PDF_TO_WORD)</span>
<span class="nc" id="L1523">                .originalFileName(&quot;document2.pdf&quot;)</span>
<span class="nc" id="L1524">                .convertedFileName(&quot;document2.docx&quot;)</span>
<span class="nc" id="L1525">                .downloadPath(&quot;/downloads/document2.docx&quot;)</span>
<span class="nc" id="L1526">                .fileSize(4096L)</span>
<span class="nc" id="L1527">                .status(ConversionStatus.COMPLETED)</span>
<span class="nc" id="L1528">                .success(true)</span>
<span class="nc" id="L1529">                .message(&quot;Conversion completed successfully&quot;)</span>
<span class="nc" id="L1530">                .startedAt(LocalDateTime.now().minusHours(1))</span>
<span class="nc" id="L1531">                .completedAt(LocalDateTime.now().minusHours(1).plusMinutes(5))</span>
<span class="nc" id="L1532">                .processingTimeMs(300000L)</span>
<span class="nc" id="L1533">                .conversionMethod(ConversionMethod.LEGACY)</span>
<span class="nc" id="L1534">                .usedPandoc(false)</span>
<span class="nc" id="L1535">                .build();</span>

<span class="nc" id="L1537">        ConversionResult result3 = ConversionResult.builder()</span>
<span class="nc" id="L1538">                .sessionId(&quot;mock-session-3&quot;)</span>
<span class="nc" id="L1539">                .conversionType(ConversionType.WORD_TO_PDF)</span>
<span class="nc" id="L1540">                .originalFileName(&quot;document3.docx&quot;)</span>
<span class="nc" id="L1541">                .convertedFileName(&quot;document3.pdf&quot;)</span>
<span class="nc" id="L1542">                .downloadPath(&quot;/downloads/document3.pdf&quot;)</span>
<span class="nc" id="L1543">                .fileSize(1536L)</span>
<span class="nc" id="L1544">                .status(ConversionStatus.FAILED)</span>
<span class="nc" id="L1545">                .success(false)</span>
<span class="nc" id="L1546">                .message(&quot;Conversion failed&quot;)</span>
<span class="nc" id="L1547">                .errorDetails(&quot;File format not supported&quot;)</span>
<span class="nc" id="L1548">                .startedAt(LocalDateTime.now().minusMinutes(30))</span>
<span class="nc" id="L1549">                .completedAt(LocalDateTime.now().minusMinutes(28))</span>
<span class="nc" id="L1550">                .processingTimeMs(120000L)</span>
<span class="nc" id="L1551">                .conversionMethod(ConversionMethod.FALLBACK)</span>
<span class="nc" id="L1552">                .usedPandoc(false)</span>
<span class="nc" id="L1553">                .build();</span>

        // Add to the map
<span class="nc" id="L1556">        conversionResultMap.put(result1.getSessionId(), result1);</span>
<span class="nc" id="L1557">        conversionResultMap.put(result2.getSessionId(), result2);</span>
<span class="nc" id="L1558">        conversionResultMap.put(result3.getSessionId(), result3);</span>
<span class="nc" id="L1559">    }</span>

    // Getter methods for test access
    public java.util.Map&lt;String, ConversionResult&gt; getConversionResultMap() {
<span class="nc" id="L1563">        return conversionResultMap;</span>
    }

    public java.util.Map&lt;String, ConversionProgress&gt; getConversionProgressMap() {
<span class="nc" id="L1567">        return conversionProgressMap;</span>
    }

    public java.util.Map&lt;String, BatchConversionResult&gt; getBatchConversionResultMap() {
<span class="nc" id="L1571">        return batchConversionResultMap;</span>
    }

    // ===== FIELD RESOLVERS FOR DATETIME SERIALIZATION =====

    /**
     * Field resolver for BatchConversionResult.startedAt to handle DateTime serialization.
     * Converts LocalDateTime to OffsetDateTime for GraphQL DateTime scalar compatibility.
     */
    @org.springframework.graphql.data.method.annotation.SchemaMapping(typeName = &quot;BatchConversionResult&quot;, field = &quot;startedAt&quot;)
    public java.time.OffsetDateTime batchConversionResultStartedAt(BatchConversionResult result) {
<span class="nc bnc" id="L1582" title="All 2 branches missed.">        return result.getStartedAt() != null ? result.getStartedAt().atOffset(java.time.ZoneOffset.UTC) : null;</span>
    }

    /**
     * Field resolver for BatchConversionResult.completedAt to handle DateTime serialization.
     * Converts LocalDateTime to OffsetDateTime for GraphQL DateTime scalar compatibility.
     */
    @org.springframework.graphql.data.method.annotation.SchemaMapping(typeName = &quot;BatchConversionResult&quot;, field = &quot;completedAt&quot;)
    public java.time.OffsetDateTime batchConversionResultCompletedAt(BatchConversionResult result) {
<span class="nc bnc" id="L1591" title="All 2 branches missed.">        return result.getCompletedAt() != null ? result.getCompletedAt().atOffset(java.time.ZoneOffset.UTC) : null;</span>
    }

    /**
     * Field resolver for BatchConversionResult.estimatedCompletionTime to handle DateTime serialization.
     * Converts LocalDateTime to OffsetDateTime for GraphQL DateTime scalar compatibility.
     */
    @org.springframework.graphql.data.method.annotation.SchemaMapping(typeName = &quot;BatchConversionResult&quot;, field = &quot;estimatedCompletionTime&quot;)
    public java.time.OffsetDateTime batchConversionResultEstimatedCompletionTime(BatchConversionResult result) {
<span class="nc bnc" id="L1600" title="All 2 branches missed.">        return result.getEstimatedCompletionTime() != null ? result.getEstimatedCompletionTime().atOffset(java.time.ZoneOffset.UTC) : null;</span>
    }

    /**
     * Field resolver for ConversionProgress.startedAt to handle DateTime serialization.
     * Converts LocalDateTime to OffsetDateTime for GraphQL DateTime scalar compatibility.
     */
    @org.springframework.graphql.data.method.annotation.SchemaMapping(typeName = &quot;ConversionProgress&quot;, field = &quot;startedAt&quot;)
    public java.time.OffsetDateTime conversionProgressStartedAt(ConversionProgress progress) {
<span class="nc bnc" id="L1609" title="All 2 branches missed.">        return progress.getStartedAt() != null ? progress.getStartedAt().atOffset(java.time.ZoneOffset.UTC) : null;</span>
    }

    /**
     * Field resolver for ConversionProgress.lastUpdatedAt to handle DateTime serialization.
     * Converts LocalDateTime to OffsetDateTime for GraphQL DateTime scalar compatibility.
     */
    @org.springframework.graphql.data.method.annotation.SchemaMapping(typeName = &quot;ConversionProgress&quot;, field = &quot;lastUpdatedAt&quot;)
    public java.time.OffsetDateTime conversionProgressLastUpdatedAt(ConversionProgress progress) {
<span class="nc bnc" id="L1618" title="All 2 branches missed.">        return progress.getLastUpdatedAt() != null ? progress.getLastUpdatedAt().atOffset(java.time.ZoneOffset.UTC) : null;</span>
    }
}
</pre><div class="footer"><span class="right">Created with <a href="http://www.jacoco.org/jacoco">JaCoCo</a> 0.8.10.202304240956</span></div></body></html>