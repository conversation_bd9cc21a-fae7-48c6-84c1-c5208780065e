<?xml version="1.0" encoding="UTF-8"?><!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Strict//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-strict.dtd"><html xmlns="http://www.w3.org/1999/xhtml" lang="en"><head><meta http-equiv="Content-Type" content="text/html;charset=UTF-8"/><link rel="stylesheet" href="../jacoco-resources/report.css" type="text/css"/><link rel="shortcut icon" href="../jacoco-resources/report.gif" type="image/gif"/><title>com.ascentbusiness.dms_svc.interceptor</title><script type="text/javascript" src="../jacoco-resources/sort.js"></script></head><body onload="initialSort(['breadcrumb', 'coveragetable'])"><div class="breadcrumb" id="breadcrumb"><span class="info"><a href="index.source.html" class="el_source">Source Files</a><a href="../jacoco-sessions.html" class="el_session">Sessions</a></span><a href="../index.html" class="el_report">dms-svc</a> &gt; <span class="el_package">com.ascentbusiness.dms_svc.interceptor</span></div><h1>com.ascentbusiness.dms_svc.interceptor</h1><table class="coverage" cellspacing="0" id="coveragetable"><thead><tr><td class="sortable" id="a" onclick="toggleSort(this)">Element</td><td class="down sortable bar" id="b" onclick="toggleSort(this)">Missed Instructions</td><td class="sortable ctr2" id="c" onclick="toggleSort(this)">Cov.</td><td class="sortable bar" id="d" onclick="toggleSort(this)">Missed Branches</td><td class="sortable ctr2" id="e" onclick="toggleSort(this)">Cov.</td><td class="sortable ctr1" id="f" onclick="toggleSort(this)">Missed</td><td class="sortable ctr2" id="g" onclick="toggleSort(this)">Cxty</td><td class="sortable ctr1" id="h" onclick="toggleSort(this)">Missed</td><td class="sortable ctr2" id="i" onclick="toggleSort(this)">Lines</td><td class="sortable ctr1" id="j" onclick="toggleSort(this)">Missed</td><td class="sortable ctr2" id="k" onclick="toggleSort(this)">Methods</td><td class="sortable ctr1" id="l" onclick="toggleSort(this)">Missed</td><td class="sortable ctr2" id="m" onclick="toggleSort(this)">Classes</td></tr></thead><tfoot><tr><td>Total</td><td class="bar">536 of 543</td><td class="ctr2">1%</td><td class="bar">80 of 80</td><td class="ctr2">0%</td><td class="ctr1">51</td><td class="ctr2">53</td><td class="ctr1">105</td><td class="ctr2">107</td><td class="ctr1">11</td><td class="ctr2">13</td><td class="ctr1">2</td><td class="ctr2">3</td></tr></tfoot><tbody><tr><td id="a0"><a href="CorrelationIdInterceptor.html" class="el_class">CorrelationIdInterceptor</a></td><td class="bar" id="b0"><img src="../jacoco-resources/redbar.gif" width="117" height="10" title="302" alt="302"/><img src="../jacoco-resources/greenbar.gif" width="2" height="10" title="7" alt="7"/></td><td class="ctr2" id="c0">2%</td><td class="bar" id="d0"><img src="../jacoco-resources/redbar.gif" width="120" height="10" title="52" alt="52"/></td><td class="ctr2" id="e0">0%</td><td class="ctr1" id="f0">31</td><td class="ctr2" id="g0">33</td><td class="ctr1" id="h0">76</td><td class="ctr2" id="i0">78</td><td class="ctr1" id="j0">5</td><td class="ctr2" id="k0">7</td><td class="ctr1" id="l2">0</td><td class="ctr2" id="m0">1</td></tr><tr><td id="a1"><a href="CorrelationIdInterceptor$2.html" class="el_class">CorrelationIdInterceptor.new InstrumentationContext() {...}</a></td><td class="bar" id="b1"><img src="../jacoco-resources/redbar.gif" width="49" height="10" title="127" alt="127"/></td><td class="ctr2" id="c1">0%</td><td class="bar" id="d1"><img src="../jacoco-resources/redbar.gif" width="41" height="10" title="18" alt="18"/></td><td class="ctr2" id="e1">0%</td><td class="ctr1" id="f1">12</td><td class="ctr2" id="g1">12</td><td class="ctr1" id="h1">16</td><td class="ctr2" id="i1">16</td><td class="ctr1" id="j1">3</td><td class="ctr2" id="k1">3</td><td class="ctr1" id="l0">1</td><td class="ctr2" id="m1">1</td></tr><tr><td id="a2"><a href="CorrelationIdInterceptor$1.html" class="el_class">CorrelationIdInterceptor.new InstrumentationContext() {...}</a></td><td class="bar" id="b2"><img src="../jacoco-resources/redbar.gif" width="41" height="10" title="107" alt="107"/></td><td class="ctr2" id="c2">0%</td><td class="bar" id="d2"><img src="../jacoco-resources/redbar.gif" width="23" height="10" title="10" alt="10"/></td><td class="ctr2" id="e2">0%</td><td class="ctr1" id="f2">8</td><td class="ctr2" id="g2">8</td><td class="ctr1" id="h2">15</td><td class="ctr2" id="i2">15</td><td class="ctr1" id="j2">3</td><td class="ctr2" id="k2">3</td><td class="ctr1" id="l1">1</td><td class="ctr2" id="m2">1</td></tr></tbody></table><div class="footer"><span class="right">Created with <a href="http://www.jacoco.org/jacoco">JaCoCo</a> 0.8.10.202304240956</span></div></body></html>