<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>DMS Download Test</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 1000px;
            margin: 50px auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        h1 {
            color: #333;
            text-align: center;
        }
        .test-section {
            margin: 20px 0;
            padding: 20px;
            border: 1px solid #ddd;
            border-radius: 5px;
        }
        .download-btn {
            background-color: #007bff;
            color: white;
            padding: 10px 20px;
            border: none;
            border-radius: 5px;
            cursor: pointer;
            margin: 5px;
            text-decoration: none;
            display: inline-block;
        }
        .download-btn:hover {
            background-color: #0056b3;
        }
        .auth-btn {
            background-color: #28a745;
            color: white;
            padding: 10px 20px;
            border: none;
            border-radius: 5px;
            cursor: pointer;
            margin: 5px;
        }
        .auth-btn:hover {
            background-color: #218838;
        }
        .info {
            background-color: #e7f3ff;
            padding: 15px;
            border-radius: 5px;
            margin: 10px 0;
        }
        .warning {
            background-color: #fff3cd;
            padding: 15px;
            border-radius: 5px;
            margin: 10px 0;
            border-left: 4px solid #ffc107;
        }
        .success {
            background-color: #d4edda;
            padding: 15px;
            border-radius: 5px;
            margin: 10px 0;
            border-left: 4px solid #28a745;
        }
        .error {
            background-color: #f8d7da;
            padding: 15px;
            border-radius: 5px;
            margin: 10px 0;
            border-left: 4px solid #dc3545;
        }
        .code {
            background-color: #f8f9fa;
            padding: 10px;
            border-radius: 3px;
            font-family: monospace;
            margin: 10px 0;
        }
        .input-group {
            margin: 10px 0;
        }
        .input-group label {
            display: block;
            margin-bottom: 5px;
            font-weight: bold;
        }
        .input-group input, .input-group select {
            width: 100%;
            padding: 8px;
            border: 1px solid #ddd;
            border-radius: 4px;
            box-sizing: border-box;
        }
        .flex-container {
            display: flex;
            gap: 20px;
            flex-wrap: wrap;
        }
        .flex-item {
            flex: 1;
            min-width: 300px;
        }
        .token-display {
            background-color: #f8f9fa;
            padding: 10px;
            border-radius: 4px;
            border: 1px solid #ddd;
            word-break: break-all;
            font-family: monospace;
            font-size: 12px;
            max-height: 100px;
            overflow-y: auto;
        }
        .method-selector {
            margin: 10px 0;
        }
        .method-selector label {
            margin-right: 15px;
            cursor: pointer;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🗂️ DMS Download Test Page</h1>
        
        <div class="info">
            <strong>📋 Instructions:</strong>
            <ol>
                <li>First, authenticate to get a JWT token (or paste an existing one)</li>
                <li>Configure the document ID you want to download</li>
                <li>Choose your download method and test the functionality</li>
                <li>Files will download to your default Downloads folder</li>
            </ol>
        </div>

        <!-- Authentication Section -->
        <div class="test-section">
            <h3>🔐 Authentication & JWT Token</h3>
            
            <div class="flex-container">
                <div class="flex-item">
                    <h4>Generate New Token</h4>
                    <div class="input-group">
                        <label for="username">Username:</label>
                        <input type="text" id="username" value="testuser" placeholder="Enter username">
                    </div>
                    <div class="input-group">
                        <label for="password">Password:</label>
                        <input type="password" id="password" value="password123" placeholder="Enter password">
                    </div>
                    <button class="auth-btn" onclick="generateToken()">
                        🔑 Generate JWT Token
                    </button>
                </div>
                
                <div class="flex-item">
                    <h4>Current JWT Token</h4>
                    <div class="input-group">
                        <label for="jwtToken">JWT Token:</label>
                        <textarea id="jwtToken" rows="4" placeholder="JWT token will appear here or paste your existing token" style="width: 100%; padding: 8px; border: 1px solid #ddd; border-radius: 4px; font-family: monospace; font-size: 12px;"></textarea>
                    </div>
                    <button class="auth-btn" onclick="validateToken()">
                        ✅ Validate Token
                    </button>
                </div>
            </div>
        </div>

        <!-- Configuration Section -->
        <div class="test-section">
            <h3>⚙️ Download Configuration</h3>
            
            <div class="flex-container">
                <div class="flex-item">
                    <div class="input-group">
                        <label for="documentId">Document ID:</label>
                        <input type="number" id="documentId" value="85" placeholder="Enter document ID">
                    </div>
                </div>
                
                <div class="flex-item">
                    <div class="input-group">
                        <label for="apiVersion">API Version:</label>
                        <select id="apiVersion">
                            <option value="v1">V1 API (Basic)</option>
                            <option value="v2" selected>V2 API (Enhanced)</option>
                        </select>
                    </div>
                </div>
            </div>
            
            <div class="method-selector">
                <strong>Download Method:</strong><br>
                <label><input type="radio" name="downloadMethod" value="javascript" checked> JavaScript Download (Recommended - with Auth)</label>
                <label><input type="radio" name="downloadMethod" value="direct"> Direct Link (No Auth - will show 401)</label>
                <label><input type="radio" name="downloadMethod" value="inline"> Inline View (V2 only)</label>
            </div>
        </div>

        <!-- Download Section -->
        <div class="test-section">
            <h3>📥 Download Test</h3>
            
            <button class="download-btn" onclick="performDownload()">
                🚀 Start Download Test
            </button>
            
            <button class="download-btn" onclick="getDocumentInfo()">
                ℹ️ Get Document Info (V2 only)
            </button>
            
            <div class="code" id="currentConfig">
                <strong>Current Configuration:</strong><br>
                Document ID: <span id="configDocId">85</span><br>
                API Version: <span id="configApiVersion">v2</span><br>
                Method: <span id="configMethod">javascript</span><br>
                URL: <span id="configUrl">http://localhost:9093/api/v2/documents/85/download</span>
            </div>
        </div>

        <!-- Direct Links Section -->
        <div class="test-section">
            <h3>🔗 Quick Direct Links</h3>
            <div class="warning">
                <strong>⚠️ Note:</strong> Direct links cannot send Authorization headers and will show 401 Unauthorized without proper authentication setup.
            </div>
            
            <div id="directLinks">
                <a href="http://localhost:9093/api/v1/documents/85/download" class="download-btn" target="_blank">
                    📥 V1 Direct Link
                </a>
                <a href="http://localhost:9093/api/v2/documents/85/download" class="download-btn" target="_blank">
                    📥 V2 Direct Link
                </a>
                <a href="http://localhost:9093/api/v2/documents/85/download?inline=true" class="download-btn" target="_blank">
                    👁️ V2 Inline View
                </a>
            </div>
        </div>

        <!-- Results Section -->
        <div class="test-section">
            <h3>📊 Test Results</h3>
            <div id="result"></div>
        </div>

        <!-- Expected Results -->
        <div class="test-section">
            <h3>✅ Expected Results</h3>
            <div class="success">
                <strong>When working correctly:</strong>
                <ul>
                    <li><strong>File Location:</strong> Your default Downloads folder</li>
                    <li><strong>File Name:</strong> Original document name (e.g., "Kong Configuration.pdf")</li>
                    <li><strong>File Size:</strong> Actual content size (e.g., 51,590 bytes for document 85)</li>
                    <li><strong>File Type:</strong> Proper file type that can be opened</li>
                    <li><strong>Content-Disposition:</strong> <code>attachment; filename="original_name.ext"</code></li>
                </ul>
            </div>
        </div>

        <!-- API Information -->
        <div class="test-section">
            <h3>🛠️ API Information</h3>
            <div class="code">
                <strong>Available Endpoints:</strong><br>
                • POST /graphql - GraphQL endpoint (for JWT token generation)<br>
                • POST /auth/login - REST JWT token generation (legacy)<br>
                • GET /api/v1/documents/{id}/download - Basic download<br>
                • GET /api/v2/documents/{id}/download - Enhanced download<br>
                • GET /api/v2/documents/{id}/download?inline=true - Inline view<br>
                • GET /api/v2/documents/{id}/download/info - Metadata only<br><br>
                
                <strong>GraphQL Token Generation Mutation:</strong><br>
                mutation GenerateToken($input: JwtTokenRequest!) {<br>
                &nbsp;&nbsp;generateTestToken(input: $input) {<br>
                &nbsp;&nbsp;&nbsp;&nbsp;token<br>
                &nbsp;&nbsp;&nbsp;&nbsp;tokenType<br>
                &nbsp;&nbsp;&nbsp;&nbsp;expiresAt<br>
                &nbsp;&nbsp;}<br>
                }<br><br>
                
                <strong>Required Headers for Downloads:</strong><br>
                Authorization: Bearer YOUR_JWT_TOKEN<br><br>
                
                <strong>Response Headers:</strong><br>
                Content-Type: application/pdf (or appropriate MIME type)<br>
                Content-Disposition: attachment; filename="document_name.ext"<br>
                Content-Length: file_size_in_bytes
            </div>
        </div>
    </div>

    <script>
        // Update configuration display
        function updateConfigDisplay() {
            const docId = document.getElementById('documentId').value;
            const apiVersion = document.getElementById('apiVersion').value;
            const method = document.querySelector('input[name="downloadMethod"]:checked').value;
            
            document.getElementById('configDocId').textContent = docId;
            document.getElementById('configApiVersion').textContent = apiVersion;
            document.getElementById('configMethod').textContent = method;
            
            const baseUrl = `http://localhost:9093/api/${apiVersion}/documents/${docId}/download`;
            const url = method === 'inline' ? `${baseUrl}?inline=true` : baseUrl;
            document.getElementById('configUrl').textContent = url;
            
            // Update direct links
            updateDirectLinks(docId);
        }
        
        // Update direct links with current document ID
        function updateDirectLinks(docId) {
            const linksContainer = document.getElementById('directLinks');
            linksContainer.innerHTML = `
                <a href="http://localhost:9093/api/v1/documents/${docId}/download" class="download-btn" target="_blank">
                    📥 V1 Direct Link
                </a>
                <a href="http://localhost:9093/api/v2/documents/${docId}/download" class="download-btn" target="_blank">
                    📥 V2 Direct Link
                </a>
                <a href="http://localhost:9093/api/v2/documents/${docId}/download?inline=true" class="download-btn" target="_blank">
                    👁️ V2 Inline View
                </a>
            `;
        }
        
        // Generate JWT token using GraphQL mutation
        async function generateToken() {
            const username = document.getElementById('username').value.trim();
            const resultDiv = document.getElementById('result');
            
            if (!username) {
                resultDiv.innerHTML = '<div class="warning">⚠️ Please enter a username!</div>';
                return;
            }
            
            try {
                resultDiv.innerHTML = '<div class="info">🔄 Generating token via GraphQL...</div>';
                
                // Use GraphQL mutation to generate JWT token
                const graphqlQuery = {
                    query: `
                        mutation GenerateToken($input: JwtTokenRequest!) {
                            generateTestToken(input: $input) {
                                token
                                tokenType
                                expiresAt
                            }
                        }
                    `,
                    variables: {
                        input: {
                            username: username,
                            roles: ["ADMIN", "USER"],
                            permissions: ["READ", "WRITE", "DELETE"]
                        }
                    }
                };

                const response = await fetch('http://localhost:9093/graphql', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify(graphqlQuery)
                });
                
                if (response.ok) {
                    const data = await response.json();
                    
                    if (data.errors) {
                        throw new Error(data.errors[0].message || 'GraphQL error occurred');
                    }
                    
                    if (data.data && data.data.generateTestToken) {
                        const tokenData = data.data.generateTestToken;
                        const token = tokenData.token;
                        
                        if (token) {
                            document.getElementById('jwtToken').value = token;
                            resultDiv.innerHTML = `
                                <div class="success">
                                    ✅ <strong>Token Generated Successfully via GraphQL!</strong><br>
                                    👤 User: ${username}<br>
                                    🔑 Token has been set in the JWT Token field<br>
                                    🏷️ Roles: ADMIN, USER<br>
                                    🔐 Permissions: READ, WRITE, DELETE<br>
                                    ⏰ Token expires at: ${tokenData.expiresAt ? new Date(tokenData.expiresAt).toLocaleString() : 'Unknown'}<br>
                                    🔧 Method: GraphQL Mutation
                                </div>
                            `;
                        } else {
                            resultDiv.innerHTML = `
                                <div class="error">
                                    ❌ <strong>Token not found in GraphQL response!</strong><br>
                                    Response: ${JSON.stringify(tokenData)}
                                </div>
                            `;
                        }
                    } else {
                        resultDiv.innerHTML = `
                            <div class="error">
                                ❌ <strong>No token data received from GraphQL mutation!</strong><br>
                                Response: ${JSON.stringify(data)}
                            </div>
                        `;
                    }
                } else {
                    const errorText = await response.text();
                    resultDiv.innerHTML = `
                        <div class="error">
                            ❌ <strong>GraphQL Request Failed!</strong><br>
                            Status: ${response.status} ${response.statusText}<br>
                            Error: ${errorText}
                        </div>
                    `;
                }
            } catch (error) {
                resultDiv.innerHTML = `
                    <div class="error">
                        ❌ <strong>Error:</strong> ${error.message}<br>
                        Make sure the DMS service is running on localhost:9093
                    </div>
                `;
            }
        }
        
        // Validate JWT token
        async function validateToken() {
            const token = document.getElementById('jwtToken').value.trim();
            const resultDiv = document.getElementById('result');
            
            if (!token) {
                resultDiv.innerHTML = '<div class="warning">⚠️ Please enter a JWT token first!</div>';
                return;
            }
            
            try {
                // Decode JWT payload (basic validation)
                const payload = JSON.parse(atob(token.split('.')[1]));
                const now = Math.floor(Date.now() / 1000);
                const isExpired = payload.exp && payload.exp < now;
                
                resultDiv.innerHTML = `
                    <div class="${isExpired ? 'error' : 'success'}">
                        ${isExpired ? '❌' : '✅'} <strong>Token ${isExpired ? 'Expired' : 'Valid'}</strong><br>
                        👤 Subject: ${payload.sub || 'Unknown'}<br>
                        🏷️ Roles: ${payload.roles ? payload.roles.join(', ') : 'Unknown'}<br>
                        🔐 Permissions: ${payload.permissions ? payload.permissions.join(', ') : 'Unknown'}<br>
                        ⏰ Issued: ${payload.iat ? new Date(payload.iat * 1000).toLocaleString() : 'Unknown'}<br>
                        ⏰ Expires: ${payload.exp ? new Date(payload.exp * 1000).toLocaleString() : 'Unknown'}
                    </div>
                `;
            } catch (error) {
                resultDiv.innerHTML = `
                    <div class="error">
                        ❌ <strong>Invalid Token Format!</strong><br>
                        Error: ${error.message}
                    </div>
                `;
            }
        }
        
        // Perform download based on selected method
        async function performDownload() {
            const method = document.querySelector('input[name="downloadMethod"]:checked').value;
            
            if (method === 'direct') {
                performDirectDownload();
            } else if (method === 'inline') {
                performInlineView();
            } else {
                performJavaScriptDownload();
            }
        }
        
        // JavaScript download with authentication
        async function performJavaScriptDownload() {
            const token = document.getElementById('jwtToken').value.trim();
            const docId = document.getElementById('documentId').value;
            const apiVersion = document.getElementById('apiVersion').value;
            const resultDiv = document.getElementById('result');
            
            if (!token) {
                resultDiv.innerHTML = '<div class="warning">⚠️ Please enter a JWT token first!</div>';
                return;
            }
            
            if (!docId) {
                resultDiv.innerHTML = '<div class="warning">⚠️ Please enter a document ID!</div>';
                return;
            }
            
            try {
                const url = `http://localhost:9093/api/${apiVersion}/documents/${docId}/download`;
                
                resultDiv.innerHTML = '<div class="info">🔄 Downloading...</div>';
                
                const response = await fetch(url, {
                    method: 'GET',
                    headers: {
                        'Authorization': `Bearer ${token}`
                    }
                });
                
                if (response.ok) {
                    // Get filename from Content-Disposition header
                    const contentDisposition = response.headers.get('Content-Disposition');
                    let filename = `document_${docId}_${apiVersion}.pdf`;
                    
                    if (contentDisposition) {
                        const filenameMatch = contentDisposition.match(/filename[^;=\n]*=((['"]).*?\2|[^;\n]*)/);
                        if (filenameMatch && filenameMatch[1]) {
                            filename = filenameMatch[1].replace(/['"]/g, '');
                            // Decode URL-encoded filename
                            filename = decodeURIComponent(filename);
                        }
                    }
                    
                    // Create blob and download
                    const blob = await response.blob();
                    const downloadUrl = window.URL.createObjectURL(blob);
                    const a = document.createElement('a');
                    a.href = downloadUrl;
                    a.download = filename;
                    document.body.appendChild(a);
                    a.click();
                    document.body.removeChild(a);
                    window.URL.revokeObjectURL(downloadUrl);
                    
                    resultDiv.innerHTML = `
                        <div class="success">
                            ✅ <strong>Download Successful!</strong><br>
                            📁 File: ${filename}<br>
                            📊 Size: ${blob.size.toLocaleString()} bytes<br>
                            📂 Location: Your Downloads folder<br>
                            🔗 API: ${apiVersion.toUpperCase()}<br>
                            📋 Content-Disposition: ${contentDisposition || 'Not set'}
                        </div>
                    `;
                } else {
                    const errorText = await response.text();
                    resultDiv.innerHTML = `
                        <div class="error">
                            ❌ <strong>Download Failed!</strong><br>
                            Status: ${response.status} ${response.statusText}<br>
                            Error: ${errorText}
                        </div>
                    `;
                }
            } catch (error) {
                resultDiv.innerHTML = `
                    <div class="error">
                        ❌ <strong>Error:</strong> ${error.message}
                    </div>
                `;
            }
        }
        
        // Direct download (will likely fail due to no auth headers)
        function performDirectDownload() {
            const docId = document.getElementById('documentId').value;
            const apiVersion = document.getElementById('apiVersion').value;
            const resultDiv = document.getElementById('result');
            
            if (!docId) {
                resultDiv.innerHTML = '<div class="warning">⚠️ Please enter a document ID!</div>';
                return;
            }
            
            const url = `http://localhost:9093/api/${apiVersion}/documents/${docId}/download`;
            
            resultDiv.innerHTML = `
                <div class="warning">
                    ⚠️ <strong>Opening Direct Link...</strong><br>
                    This will likely show 401 Unauthorized because browsers cannot send Authorization headers with direct links.<br>
                    URL: <a href="${url}" target="_blank">${url}</a>
                </div>
            `;
            
            window.open(url, '_blank');
        }
        
        // Inline view
        function performInlineView() {
            const docId = document.getElementById('documentId').value;
            const resultDiv = document.getElementById('result');
            
            if (!docId) {
                resultDiv.innerHTML = '<div class="warning">⚠️ Please enter a document ID!</div>';
                return;
            }
            
            const url = `http://localhost:9093/api/v2/documents/${docId}/download?inline=true`;
            
            resultDiv.innerHTML = `
                <div class="info">
                    👁️ <strong>Opening Inline View...</strong><br>
                    Note: This will likely show 401 Unauthorized without proper authentication.<br>
                    URL: <a href="${url}" target="_blank">${url}</a>
                </div>
            `;
            
            window.open(url, '_blank');
        }
        
        // Get document info
        async function getDocumentInfo() {
            const token = document.getElementById('jwtToken').value.trim();
            const docId = document.getElementById('documentId').value;
            const resultDiv = document.getElementById('result');
            
            if (!token) {
                resultDiv.innerHTML = '<div class="warning">⚠️ Please enter a JWT token first!</div>';
                return;
            }
            
            if (!docId) {
                resultDiv.innerHTML = '<div class="warning">⚠️ Please enter a document ID!</div>';
                return;
            }
            
            try {
                const url = `http://localhost:9093/api/v2/documents/${docId}/download/info`;
                
                resultDiv.innerHTML = '<div class="info">🔄 Getting document info...</div>';
                
                const response = await fetch(url, {
                    method: 'GET',
                    headers: {
                        'Authorization': `Bearer ${token}`
                    }
                });
                
                if (response.ok) {
                    const info = await response.json();
                    
                    resultDiv.innerHTML = `
                        <div class="success">
                            ✅ <strong>Document Information:</strong><br>
                            📄 Name: ${info.name || 'Unknown'}<br>
                            📁 Original Filename: ${info.originalFileName || 'Unknown'}<br>
                            📊 File Size: ${info.fileSize ? info.fileSize.toLocaleString() + ' bytes' : 'Unknown'}<br>
                            🏷️ MIME Type: ${info.mimeType || 'Unknown'}<br>
                            📋 Version: ${info.version || 'Unknown'}<br>
                            📈 Status: ${info.status || 'Unknown'}<br>
                            💾 Storage: ${info.storageProvider || 'Unknown'}<br>
                            👤 Created By: ${info.createdBy || 'Unknown'}<br>
                            📅 Created: ${info.createdDate ? new Date(info.createdDate).toLocaleString() : 'Unknown'}<br>
                            📅 Modified: ${info.lastModifiedDate ? new Date(info.lastModifiedDate).toLocaleString() : 'Unknown'}<br>
                            🔗 Download URL: ${info.downloadUrl || 'Unknown'}<br>
                            👁️ Inline URL: ${info.inlineUrl || 'Unknown'}
                        </div>
                    `;
                } else {
                    const errorText = await response.text();
                    resultDiv.innerHTML = `
                        <div class="error">
                            ❌ <strong>Failed to get document info!</strong><br>
                            Status: ${response.status} ${response.statusText}<br>
                            Error: ${errorText}
                        </div>
                    `;
                }
            } catch (error) {
                resultDiv.innerHTML = `
                    <div class="error">
                        ❌ <strong>Error:</strong> ${error.message}
                    </div>
                `;
            }
        }
        
        // Event listeners
        document.addEventListener('DOMContentLoaded', function() {
            // Update config display when inputs change
            document.getElementById('documentId').addEventListener('input', updateConfigDisplay);
            document.getElementById('apiVersion').addEventListener('change', updateConfigDisplay);
            document.querySelectorAll('input[name="downloadMethod"]').forEach(radio => {
                radio.addEventListener('change', updateConfigDisplay);
            });
            
            // Initial config display update
            updateConfigDisplay();
            
            // Set default JWT token if available
            const defaultToken = "eyJhbGciOiJIUzI1NiJ9.eyJzdWIiOiJ0ZXN0dXNlciIsInJvbGVzIjpbIkFETUlOIiwiVVNFUiJdLCJwZXJtaXNzaW9ucyI6WyJSRUFEIiwiV1JJVEUiLCJERUxFVEUiXSwiaWF0IjoxNzUyOTk3Mjk3LCJleHAiOjE3NTMwODM2OTd9.BKXkwTqEYQT2PZlZmduc0Ar3Vy0CkP6RaWl2LWdC6bE";
            document.getElementById('jwtToken').value = defaultToken;
        });
    </script>
</body>
</html>