<?xml version="1.0" encoding="UTF-8"?><!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Strict//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-strict.dtd"><html xmlns="http://www.w3.org/1999/xhtml" lang="en"><head><meta http-equiv="Content-Type" content="text/html;charset=UTF-8"/><link rel="stylesheet" href="../jacoco-resources/report.css" type="text/css"/><link rel="shortcut icon" href="../jacoco-resources/report.gif" type="image/gif"/><title>SystemEventService.java</title><link rel="stylesheet" href="../jacoco-resources/prettify.css" type="text/css"/><script type="text/javascript" src="../jacoco-resources/prettify.js"></script></head><body onload="window['PR_TAB_WIDTH']=4;prettyPrint()"><div class="breadcrumb" id="breadcrumb"><span class="info"><a href="../jacoco-sessions.html" class="el_session">Sessions</a></span><a href="../index.html" class="el_report">dms-svc</a> &gt; <a href="index.source.html" class="el_package">com.ascentbusiness.dms_svc.service</a> &gt; <span class="el_source">SystemEventService.java</span></div><h1>SystemEventService.java</h1><pre class="source lang-java linenums">package com.ascentbusiness.dms_svc.service;

import com.ascentbusiness.dms_svc.entity.SystemEvent;
import com.ascentbusiness.dms_svc.enums.EventType;
import com.ascentbusiness.dms_svc.enums.EventCategory;
import com.ascentbusiness.dms_svc.repository.SystemEventRepository;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;
import java.util.UUID;

/**
 * Service for managing system events
 */
@Service
<span class="fc" id="L25">@RequiredArgsConstructor</span>
<span class="fc" id="L26">@Slf4j</span>
@Transactional
public class SystemEventService {

    private final SystemEventRepository systemEventRepository;
    private final ObjectMapper objectMapper;

    /**
     * Create a new system event
     */
    public SystemEvent createSystemEvent(EventType eventType, EventCategory eventCategory, 
                                       String actorUserId, String eventName, 
                                       Map&lt;String, Object&gt; eventData) {
<span class="nc" id="L39">        log.debug(&quot;Creating system event: {} by actor: {}&quot;, eventType, actorUserId);</span>
        
<span class="nc" id="L41">        SystemEvent event = SystemEvent.builder()</span>
<span class="nc" id="L42">                .eventType(eventType)</span>
<span class="nc" id="L43">                .eventCategory(eventCategory)</span>
<span class="nc" id="L44">                .eventName(eventName)</span>
<span class="nc" id="L45">                .actorUserId(actorUserId)</span>
<span class="nc bnc" id="L46" title="All 2 branches missed.">                .actorType(actorUserId != null ? &quot;USER&quot; : &quot;SYSTEM&quot;)</span>
<span class="nc" id="L47">                .eventTimestamp(LocalDateTime.now())</span>
<span class="nc" id="L48">                .eventData(convertToJsonNode(eventData))</span>
<span class="nc" id="L49">                .correlationId(UUID.randomUUID().toString())</span>
<span class="nc" id="L50">                .sessionId(getCurrentSessionId())</span>
<span class="nc" id="L51">                .processingStatus(&quot;PENDING&quot;)</span>
<span class="nc" id="L52">                .build();</span>
        
<span class="nc" id="L54">        SystemEvent saved = systemEventRepository.save(event);</span>
<span class="nc" id="L55">        log.debug(&quot;Created system event with ID: {}&quot;, saved.getId());</span>
<span class="nc" id="L56">        return saved;</span>
    }

    /**
     * Create system event with source entity
     */
    public SystemEvent createSystemEvent(EventType eventType, EventCategory eventCategory,
                                       String actorUserId, String eventName,
                                       String sourceEntityType, Long sourceEntityId,
                                       Map&lt;String, Object&gt; eventData) {
<span class="nc" id="L66">        log.debug(&quot;Creating system event: {} for entity: {}:{} by actor: {}&quot;, </span>
                 eventType, sourceEntityType, sourceEntityId, actorUserId);
        
<span class="nc" id="L69">        SystemEvent event = SystemEvent.builder()</span>
<span class="nc" id="L70">                .eventType(eventType)</span>
<span class="nc" id="L71">                .eventCategory(eventCategory)</span>
<span class="nc" id="L72">                .eventName(eventName)</span>
<span class="nc" id="L73">                .actorUserId(actorUserId)</span>
<span class="nc bnc" id="L74" title="All 2 branches missed.">                .actorType(actorUserId != null ? &quot;USER&quot; : &quot;SYSTEM&quot;)</span>
<span class="nc" id="L75">                .eventTimestamp(LocalDateTime.now())</span>
<span class="nc" id="L76">                .sourceEntityType(sourceEntityType)</span>
<span class="nc" id="L77">                .sourceEntityId(sourceEntityId)</span>
<span class="nc" id="L78">                .eventData(convertToJsonNode(eventData))</span>
<span class="nc" id="L79">                .correlationId(UUID.randomUUID().toString())</span>
<span class="nc" id="L80">                .sessionId(getCurrentSessionId())</span>
<span class="nc" id="L81">                .processingStatus(&quot;PENDING&quot;)</span>
<span class="nc" id="L82">                .build();</span>
        
<span class="nc" id="L84">        SystemEvent saved = systemEventRepository.save(event);</span>
<span class="nc" id="L85">        log.debug(&quot;Created system event with ID: {}&quot;, saved.getId());</span>
<span class="nc" id="L86">        return saved;</span>
    }

    /**
     * Get system event by ID
     */
    @Transactional(readOnly = true)
    public SystemEvent getSystemEventById(Long id) {
<span class="nc" id="L94">        return systemEventRepository.findById(id)</span>
<span class="nc" id="L95">                .orElseThrow(() -&gt; new RuntimeException(&quot;System event not found with ID: &quot; + id));</span>
    }

    /**
     * Get events by type
     */
    @Transactional(readOnly = true)
    public List&lt;SystemEvent&gt; getEventsByType(EventType eventType) {
<span class="nc" id="L103">        return systemEventRepository.findByEventType(eventType);</span>
    }

    /**
     * Get events by category
     */
    @Transactional(readOnly = true)
    public List&lt;SystemEvent&gt; getEventsByCategory(EventCategory eventCategory) {
<span class="nc" id="L111">        return systemEventRepository.findByEventCategory(eventCategory);</span>
    }

    /**
     * Get events by actor
     */
    @Transactional(readOnly = true)
    public Page&lt;SystemEvent&gt; getEventsByActor(String actorUserId, Pageable pageable) {
<span class="nc" id="L119">        return systemEventRepository.findByActorUserId(actorUserId, pageable);</span>
    }

    /**
     * Get events by correlation ID
     */
    @Transactional(readOnly = true)
    public List&lt;SystemEvent&gt; getEventsByCorrelationId(String correlationId) {
<span class="nc" id="L127">        return systemEventRepository.findByCorrelationId(correlationId);</span>
    }

    /**
     * Get events within date range
     */
    @Transactional(readOnly = true)
    public Page&lt;SystemEvent&gt; getEventsInDateRange(LocalDateTime startDate, LocalDateTime endDate, Pageable pageable) {
<span class="nc" id="L135">        return systemEventRepository.findByEventTimestampBetween(startDate, endDate, pageable);</span>
    }

    /**
     * Get events by source entity
     */
    @Transactional(readOnly = true)
    public List&lt;SystemEvent&gt; getEventsBySourceEntity(String sourceEntityType, Long sourceEntityId) {
<span class="nc" id="L143">        return systemEventRepository.findBySourceEntityTypeAndSourceEntityId(sourceEntityType, sourceEntityId);</span>
    }

    /**
     * Get recent events
     */
    @Transactional(readOnly = true)
    public List&lt;SystemEvent&gt; getRecentEvents(int hoursBack) {
<span class="nc" id="L151">        LocalDateTime cutoffDate = LocalDateTime.now().minusHours(hoursBack);</span>
<span class="nc" id="L152">        return systemEventRepository.findByEventTimestampAfterOrderByEventTimestampDesc(cutoffDate);</span>
    }

    /**
     * Get pending events for processing
     */
    @Transactional(readOnly = true)
    public List&lt;SystemEvent&gt; getPendingEvents() {
<span class="nc" id="L160">        return systemEventRepository.findByProcessingStatus(&quot;PENDING&quot;);</span>
    }

    /**
     * Get failed events
     */
    @Transactional(readOnly = true)
    public List&lt;SystemEvent&gt; getFailedEvents() {
<span class="nc" id="L168">        return systemEventRepository.findByProcessingStatus(&quot;FAILED&quot;);</span>
    }

    /**
     * Mark event as processing
     */
    public SystemEvent markEventAsProcessing(Long eventId) {
<span class="nc" id="L175">        SystemEvent event = getSystemEventById(eventId);</span>
<span class="nc" id="L176">        event.markAsProcessing();</span>
<span class="nc" id="L177">        return systemEventRepository.save(event);</span>
    }

    /**
     * Mark event as completed
     */
    public SystemEvent markEventAsCompleted(Long eventId) {
<span class="nc" id="L184">        SystemEvent event = getSystemEventById(eventId);</span>
<span class="nc" id="L185">        event.markAsCompleted();</span>
<span class="nc" id="L186">        return systemEventRepository.save(event);</span>
    }

    /**
     * Mark event as failed
     */
    public SystemEvent markEventAsFailed(Long eventId) {
<span class="nc" id="L193">        SystemEvent event = getSystemEventById(eventId);</span>
<span class="nc" id="L194">        event.markAsFailed();</span>
<span class="nc" id="L195">        return systemEventRepository.save(event);</span>
    }

    /**
     * Increment webhook delivery count
     */
    public SystemEvent incrementWebhookDeliveryCount(Long eventId) {
<span class="nc" id="L202">        SystemEvent event = getSystemEventById(eventId);</span>
<span class="nc" id="L203">        event.incrementWebhookDeliveryCount();</span>
<span class="nc" id="L204">        return systemEventRepository.save(event);</span>
    }

    /**
     * Get event statistics
     */
    @Transactional(readOnly = true)
    public EventStatistics getEventStatistics() {
<span class="nc" id="L212">        List&lt;Object[]&gt; typeStats = systemEventRepository.getEventStatisticsByType();</span>
<span class="nc" id="L213">        List&lt;Object[]&gt; categoryStats = systemEventRepository.getEventStatisticsByCategory();</span>
<span class="nc" id="L214">        List&lt;Object[]&gt; statusStats = systemEventRepository.getEventStatisticsByProcessingStatus();</span>
        
<span class="nc" id="L216">        long totalPending = systemEventRepository.countByProcessingStatus(&quot;PENDING&quot;);</span>
<span class="nc" id="L217">        long totalProcessing = systemEventRepository.countByProcessingStatus(&quot;PROCESSING&quot;);</span>
<span class="nc" id="L218">        long totalCompleted = systemEventRepository.countByProcessingStatus(&quot;COMPLETED&quot;);</span>
<span class="nc" id="L219">        long totalFailed = systemEventRepository.countByProcessingStatus(&quot;FAILED&quot;);</span>
        
<span class="nc" id="L221">        return EventStatistics.builder()</span>
<span class="nc" id="L222">                .typeStatistics(typeStats)</span>
<span class="nc" id="L223">                .categoryStatistics(categoryStats)</span>
<span class="nc" id="L224">                .statusStatistics(statusStats)</span>
<span class="nc" id="L225">                .totalPending(totalPending)</span>
<span class="nc" id="L226">                .totalProcessing(totalProcessing)</span>
<span class="nc" id="L227">                .totalCompleted(totalCompleted)</span>
<span class="nc" id="L228">                .totalFailed(totalFailed)</span>
<span class="nc" id="L229">                .build();</span>
    }

    /**
     * Get hourly event counts for the last 24 hours
     */
    @Transactional(readOnly = true)
    public List&lt;Object[]&gt; getHourlyEventCounts() {
<span class="nc" id="L237">        LocalDateTime cutoffTime = LocalDateTime.now().minusHours(24);</span>
<span class="nc" id="L238">        return systemEventRepository.getHourlyEventCounts(cutoffTime);</span>
    }

    /**
     * Get daily event counts for the last 30 days
     */
    @Transactional(readOnly = true)
    public List&lt;Object[]&gt; getDailyEventCounts() {
<span class="nc" id="L246">        LocalDateTime cutoffTime = LocalDateTime.now().minusDays(30);</span>
<span class="nc" id="L247">        return systemEventRepository.getDailyEventCounts(cutoffTime);</span>
    }

    /**
     * Clean up old events
     */
    public int cleanupOldEvents(int daysToKeep) {
<span class="nc" id="L254">        log.info(&quot;Cleaning up events older than {} days&quot;, daysToKeep);</span>
        
<span class="nc" id="L256">        LocalDateTime cutoffDate = LocalDateTime.now().minusDays(daysToKeep);</span>
<span class="nc" id="L257">        List&lt;SystemEvent&gt; oldEvents = systemEventRepository.findByEventTimestampBefore(cutoffDate);</span>
        
<span class="nc" id="L259">        systemEventRepository.deleteAll(oldEvents);</span>
        
<span class="nc" id="L261">        log.info(&quot;Cleaned up {} old events&quot;, oldEvents.size());</span>
<span class="nc" id="L262">        return oldEvents.size();</span>
    }

    // Private helper methods

    private JsonNode convertToJsonNode(Map&lt;String, Object&gt; data) {
<span class="nc bnc" id="L268" title="All 4 branches missed.">        if (data == null || data.isEmpty()) {</span>
<span class="nc" id="L269">            return objectMapper.createObjectNode();</span>
        }
        
        try {
<span class="nc" id="L273">            return objectMapper.valueToTree(data);</span>
<span class="nc" id="L274">        } catch (Exception e) {</span>
<span class="nc" id="L275">            log.error(&quot;Error converting event data to JSON&quot;, e);</span>
<span class="nc" id="L276">            return objectMapper.createObjectNode();</span>
        }
    }

    private String getCurrentSessionId() {
        // This would get the current session ID from the request context
        // For now, return null as placeholder
<span class="nc" id="L283">        return null;</span>
    }

    // Inner class for event statistics
<span class="nc" id="L287">    public static class EventStatistics {</span>
        private List&lt;Object[]&gt; typeStatistics;
        private List&lt;Object[]&gt; categoryStatistics;
        private List&lt;Object[]&gt; statusStatistics;
        private long totalPending;
        private long totalProcessing;
        private long totalCompleted;
        private long totalFailed;
        
        public static EventStatisticsBuilder builder() {
<span class="nc" id="L297">            return new EventStatisticsBuilder();</span>
        }
        
<span class="nc" id="L300">        public static class EventStatisticsBuilder {</span>
            private List&lt;Object[]&gt; typeStatistics;
            private List&lt;Object[]&gt; categoryStatistics;
            private List&lt;Object[]&gt; statusStatistics;
            private long totalPending;
            private long totalProcessing;
            private long totalCompleted;
            private long totalFailed;
            
            public EventStatisticsBuilder typeStatistics(List&lt;Object[]&gt; typeStatistics) {
<span class="nc" id="L310">                this.typeStatistics = typeStatistics;</span>
<span class="nc" id="L311">                return this;</span>
            }
            
            public EventStatisticsBuilder categoryStatistics(List&lt;Object[]&gt; categoryStatistics) {
<span class="nc" id="L315">                this.categoryStatistics = categoryStatistics;</span>
<span class="nc" id="L316">                return this;</span>
            }
            
            public EventStatisticsBuilder statusStatistics(List&lt;Object[]&gt; statusStatistics) {
<span class="nc" id="L320">                this.statusStatistics = statusStatistics;</span>
<span class="nc" id="L321">                return this;</span>
            }
            
            public EventStatisticsBuilder totalPending(long totalPending) {
<span class="nc" id="L325">                this.totalPending = totalPending;</span>
<span class="nc" id="L326">                return this;</span>
            }
            
            public EventStatisticsBuilder totalProcessing(long totalProcessing) {
<span class="nc" id="L330">                this.totalProcessing = totalProcessing;</span>
<span class="nc" id="L331">                return this;</span>
            }
            
            public EventStatisticsBuilder totalCompleted(long totalCompleted) {
<span class="nc" id="L335">                this.totalCompleted = totalCompleted;</span>
<span class="nc" id="L336">                return this;</span>
            }
            
            public EventStatisticsBuilder totalFailed(long totalFailed) {
<span class="nc" id="L340">                this.totalFailed = totalFailed;</span>
<span class="nc" id="L341">                return this;</span>
            }
            
            public EventStatistics build() {
<span class="nc" id="L345">                EventStatistics stats = new EventStatistics();</span>
<span class="nc" id="L346">                stats.typeStatistics = this.typeStatistics;</span>
<span class="nc" id="L347">                stats.categoryStatistics = this.categoryStatistics;</span>
<span class="nc" id="L348">                stats.statusStatistics = this.statusStatistics;</span>
<span class="nc" id="L349">                stats.totalPending = this.totalPending;</span>
<span class="nc" id="L350">                stats.totalProcessing = this.totalProcessing;</span>
<span class="nc" id="L351">                stats.totalCompleted = this.totalCompleted;</span>
<span class="nc" id="L352">                stats.totalFailed = this.totalFailed;</span>
<span class="nc" id="L353">                return stats;</span>
            }
        }
        
        // Getters
<span class="nc" id="L358">        public List&lt;Object[]&gt; getTypeStatistics() { return typeStatistics; }</span>
<span class="nc" id="L359">        public List&lt;Object[]&gt; getCategoryStatistics() { return categoryStatistics; }</span>
<span class="nc" id="L360">        public List&lt;Object[]&gt; getStatusStatistics() { return statusStatistics; }</span>
<span class="nc" id="L361">        public long getTotalPending() { return totalPending; }</span>
<span class="nc" id="L362">        public long getTotalProcessing() { return totalProcessing; }</span>
<span class="nc" id="L363">        public long getTotalCompleted() { return totalCompleted; }</span>
<span class="nc" id="L364">        public long getTotalFailed() { return totalFailed; }</span>
    }
}
</pre><div class="footer"><span class="right">Created with <a href="http://www.jacoco.org/jacoco">JaCoCo</a> 0.8.10.202304240956</span></div></body></html>