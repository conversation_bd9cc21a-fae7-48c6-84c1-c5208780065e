<?xml version="1.0" encoding="UTF-8"?><!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Strict//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-strict.dtd"><html xmlns="http://www.w3.org/1999/xhtml" lang="en"><head><meta http-equiv="Content-Type" content="text/html;charset=UTF-8"/><link rel="stylesheet" href="../jacoco-resources/report.css" type="text/css"/><link rel="shortcut icon" href="../jacoco-resources/report.gif" type="image/gif"/><title>CorrelationIdFilter</title><script type="text/javascript" src="../jacoco-resources/sort.js"></script></head><body onload="initialSort(['breadcrumb'])"><div class="breadcrumb" id="breadcrumb"><span class="info"><a href="../jacoco-sessions.html" class="el_session">Sessions</a></span><a href="../index.html" class="el_report">dms-svc</a> &gt; <a href="index.html" class="el_package">com.ascentbusiness.dms_svc.filter</a> &gt; <span class="el_class">CorrelationIdFilter</span></div><h1>CorrelationIdFilter</h1><table class="coverage" cellspacing="0" id="coveragetable"><thead><tr><td class="sortable" id="a" onclick="toggleSort(this)">Element</td><td class="down sortable bar" id="b" onclick="toggleSort(this)">Missed Instructions</td><td class="sortable ctr2" id="c" onclick="toggleSort(this)">Cov.</td><td class="sortable bar" id="d" onclick="toggleSort(this)">Missed Branches</td><td class="sortable ctr2" id="e" onclick="toggleSort(this)">Cov.</td><td class="sortable ctr1" id="f" onclick="toggleSort(this)">Missed</td><td class="sortable ctr2" id="g" onclick="toggleSort(this)">Cxty</td><td class="sortable ctr1" id="h" onclick="toggleSort(this)">Missed</td><td class="sortable ctr2" id="i" onclick="toggleSort(this)">Lines</td><td class="sortable ctr1" id="j" onclick="toggleSort(this)">Missed</td><td class="sortable ctr2" id="k" onclick="toggleSort(this)">Methods</td></tr></thead><tfoot><tr><td>Total</td><td class="bar">216 of 223</td><td class="ctr2">3%</td><td class="bar">30 of 30</td><td class="ctr2">0%</td><td class="ctr1">21</td><td class="ctr2">23</td><td class="ctr1">57</td><td class="ctr2">59</td><td class="ctr1">6</td><td class="ctr2">8</td></tr></tfoot><tbody><tr><td id="a3"><a href="CorrelationIdFilter.java.html#L59" class="el_method">doFilterInternal(HttpServletRequest, HttpServletResponse, FilterChain)</a></td><td class="bar" id="b0"><img src="../jacoco-resources/redbar.gif" width="120" height="10" title="97" alt="97"/></td><td class="ctr2" id="c2">0%</td><td class="bar" id="d1"><img src="../jacoco-resources/redbar.gif" width="68" height="10" title="8" alt="8"/></td><td class="ctr2" id="e0">0%</td><td class="ctr1" id="f1">5</td><td class="ctr2" id="g1">5</td><td class="ctr1" id="h0">23</td><td class="ctr2" id="i0">23</td><td class="ctr1" id="j0">1</td><td class="ctr2" id="k0">1</td></tr><tr><td id="a6"><a href="CorrelationIdFilter.java.html#L183" class="el_method">shouldNotFilter(HttpServletRequest)</a></td><td class="bar" id="b1"><img src="../jacoco-resources/redbar.gif" width="45" height="10" title="37" alt="37"/></td><td class="ctr2" id="c3">0%</td><td class="bar" id="d0"><img src="../jacoco-resources/redbar.gif" width="120" height="10" title="14" alt="14"/></td><td class="ctr2" id="e1">0%</td><td class="ctr1" id="f0">8</td><td class="ctr2" id="g0">8</td><td class="ctr1" id="h1">10</td><td class="ctr2" id="i1">10</td><td class="ctr1" id="j1">1</td><td class="ctr2" id="k1">1</td></tr><tr><td id="a5"><a href="CorrelationIdFilter.java.html#L165" class="el_method">getClientIpAddress(HttpServletRequest)</a></td><td class="bar" id="b2"><img src="../jacoco-resources/redbar.gif" width="33" height="10" title="27" alt="27"/></td><td class="ctr2" id="c4">0%</td><td class="bar" id="d2"><img src="../jacoco-resources/redbar.gif" width="34" height="10" title="4" alt="4"/></td><td class="ctr2" id="e2">0%</td><td class="ctr1" id="f2">3</td><td class="ctr2" id="g2">3</td><td class="ctr1" id="h2">7</td><td class="ctr2" id="i2">7</td><td class="ctr1" id="j2">1</td><td class="ctr2" id="k2">1</td></tr><tr><td id="a4"><a href="CorrelationIdFilter.java.html#L133" class="el_method">extractCorrelationId(HttpServletRequest)</a></td><td class="bar" id="b3"><img src="../jacoco-resources/redbar.gif" width="29" height="10" title="24" alt="24"/></td><td class="ctr2" id="c5">0%</td><td class="bar" id="d3"><img src="../jacoco-resources/redbar.gif" width="17" height="10" title="2" alt="2"/></td><td class="ctr2" id="e3">0%</td><td class="ctr1" id="f3">2</td><td class="ctr2" id="g3">2</td><td class="ctr1" id="h3">7</td><td class="ctr2" id="i3">7</td><td class="ctr1" id="j3">1</td><td class="ctr2" id="k3">1</td></tr><tr><td id="a0"><a href="CorrelationIdFilter.java.html#L151" class="el_method">addResponseHeaders(HttpServletResponse, String)</a></td><td class="bar" id="b4"><img src="../jacoco-resources/redbar.gif" width="23" height="10" title="19" alt="19"/></td><td class="ctr2" id="c6">0%</td><td class="bar" id="d5"/><td class="ctr2" id="e5">n/a</td><td class="ctr1" id="f5">1</td><td class="ctr2" id="g5">1</td><td class="ctr1" id="h4">5</td><td class="ctr2" id="i4">5</td><td class="ctr1" id="j4">1</td><td class="ctr2" id="k4">1</td></tr><tr><td id="a1"><a href="CorrelationIdFilter.java.html#L119" class="el_method">cleanupMDC()</a></td><td class="bar" id="b5"><img src="../jacoco-resources/redbar.gif" width="14" height="10" title="12" alt="12"/></td><td class="ctr2" id="c7">0%</td><td class="bar" id="d4"><img src="../jacoco-resources/redbar.gif" width="17" height="10" title="2" alt="2"/></td><td class="ctr2" id="e4">0%</td><td class="ctr1" id="f4">2</td><td class="ctr2" id="g4">2</td><td class="ctr1" id="h5">5</td><td class="ctr2" id="i5">5</td><td class="ctr1" id="j5">1</td><td class="ctr2" id="k5">1</td></tr><tr><td id="a7"><a href="CorrelationIdFilter.java.html#L35" class="el_method">static {...}</a></td><td class="bar" id="b6"><img src="../jacoco-resources/greenbar.gif" width="4" height="10" title="4" alt="4"/></td><td class="ctr2" id="c0">100%</td><td class="bar" id="d6"/><td class="ctr2" id="e6">n/a</td><td class="ctr1" id="f6">0</td><td class="ctr2" id="g6">1</td><td class="ctr1" id="h6">0</td><td class="ctr2" id="i6">1</td><td class="ctr1" id="j6">0</td><td class="ctr2" id="k6">1</td></tr><tr><td id="a2"><a href="CorrelationIdFilter.java.html#L33" class="el_method">CorrelationIdFilter()</a></td><td class="bar" id="b7"><img src="../jacoco-resources/greenbar.gif" width="3" height="10" title="3" alt="3"/></td><td class="ctr2" id="c1">100%</td><td class="bar" id="d7"/><td class="ctr2" id="e7">n/a</td><td class="ctr1" id="f7">0</td><td class="ctr2" id="g7">1</td><td class="ctr1" id="h7">0</td><td class="ctr2" id="i7">1</td><td class="ctr1" id="j7">0</td><td class="ctr2" id="k7">1</td></tr></tbody></table><div class="footer"><span class="right">Created with <a href="http://www.jacoco.org/jacoco">JaCoCo</a> 0.8.10.202304240956</span></div></body></html>