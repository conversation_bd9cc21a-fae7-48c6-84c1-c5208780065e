<?xml version="1.0" encoding="UTF-8"?><!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Strict//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-strict.dtd"><html xmlns="http://www.w3.org/1999/xhtml" lang="en"><head><meta http-equiv="Content-Type" content="text/html;charset=UTF-8"/><link rel="stylesheet" href="../jacoco-resources/report.css" type="text/css"/><link rel="shortcut icon" href="../jacoco-resources/report.gif" type="image/gif"/><title>DocumentDownloadController.java</title><link rel="stylesheet" href="../jacoco-resources/prettify.css" type="text/css"/><script type="text/javascript" src="../jacoco-resources/prettify.js"></script></head><body onload="window['PR_TAB_WIDTH']=4;prettyPrint()"><div class="breadcrumb" id="breadcrumb"><span class="info"><a href="../jacoco-sessions.html" class="el_session">Sessions</a></span><a href="../index.html" class="el_report">dms-svc</a> &gt; <a href="index.source.html" class="el_package">com.ascentbusiness.dms_svc.controller</a> &gt; <span class="el_source">DocumentDownloadController.java</span></div><h1>DocumentDownloadController.java</h1><pre class="source lang-java linenums">/*
 * Copyright (c) 2024 Ascent Business Technology
 * All rights reserved.
 */

package com.ascentbusiness.dms_svc.controller;

import com.ascentbusiness.dms_svc.annotation.RateLimit;
import com.ascentbusiness.dms_svc.entity.Document;
import com.ascentbusiness.dms_svc.service.DocumentService;
import com.ascentbusiness.dms_svc.service.AuditService;
import com.ascentbusiness.dms_svc.enums.AuditAction;
import com.ascentbusiness.dms_svc.exception.DocumentNotFoundException;
import com.ascentbusiness.dms_svc.exception.UnauthorizedException;
import com.ascentbusiness.dms_svc.versioning.ApiVersion;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.core.io.ByteArrayResource;
import org.springframework.core.io.Resource;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.security.core.Authentication;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.web.bind.annotation.*;

import java.io.UnsupportedEncodingException;
import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;
import java.time.Instant;
import java.util.HashMap;
import java.util.Map;

/**
 * REST controller for document download operations.
 * Provides versioned API endpoints for downloading documents with proper file streaming,
 * content headers, and audit logging.
 * 
 * &lt;p&gt;This controller handles binary file downloads which are not suitable for GraphQL.
 * It provides proper HTTP response headers for file downloads including content-disposition,
 * content-type, and content-length headers.
 * 
 * &lt;p&gt;API Versioning:
 * &lt;ul&gt;
 *   &lt;li&gt;v1: Basic download functionality with audit logging&lt;/li&gt;
 *   &lt;li&gt;v2: Enhanced download with metadata and streaming support&lt;/li&gt;
 * &lt;/ul&gt;
 * 
 * <AUTHOR> Development Team
 * @version 2.0.0
 * @since 1.0.0
 */
@RestController
@RequestMapping(&quot;/api&quot;)
<span class="fc" id="L57">@RequiredArgsConstructor</span>
<span class="fc" id="L58">@Slf4j</span>
public class DocumentDownloadController {

    private final DocumentService documentService;
    private final AuditService auditService;

    /**
     * Download document by ID (API v1).
     * 
     * &lt;p&gt;Basic download functionality that returns the document file content
     * with appropriate HTTP headers for browser download.
     * 
     * @param id the document ID to download
     * @return ResponseEntity containing the file content as byte array
     * @throws DocumentNotFoundException if document doesn't exist
     * @throws UnauthorizedException if user lacks READ permission
     */
    @GetMapping(&quot;/v1/documents/{id}/download&quot;)
    @ApiVersion(since = &quot;1.0&quot;, description = &quot;Download document by ID&quot;)
    @RateLimit(value = 30, window = 60, type = RateLimit.RateLimitType.DOCUMENT_DOWNLOAD,
               message = &quot;Document download rate limit exceeded. Please wait before downloading again.&quot;)
    @PreAuthorize(&quot;hasAnyRole('USER', 'ADMIN', 'MANAGER')&quot;)
    public ResponseEntity&lt;byte[]&gt; downloadDocumentV1(@PathVariable Long id) {
<span class="nc" id="L81">        log.info(&quot;API v1: Download request for document ID: {}&quot;, id);</span>
        
        try {
            // Get document metadata for headers
<span class="nc" id="L85">            Document document = documentService.getDocumentById(id);</span>
            
            // Download actual file content
<span class="nc" id="L88">            byte[] fileContent = documentService.downloadDocument(id);</span>
            
            // Build response headers
<span class="nc" id="L91">            HttpHeaders headers = new HttpHeaders();</span>
<span class="nc" id="L92">            headers.setContentType(MediaType.parseMediaType(</span>
<span class="nc bnc" id="L93" title="All 2 branches missed.">                document.getMimeType() != null ? document.getMimeType() : &quot;application/octet-stream&quot;));</span>
<span class="nc" id="L94">            headers.setContentLength(fileContent.length);</span>
            
            // Set content-disposition for download
<span class="nc" id="L97">            String filename = sanitizeFilename(document.getOriginalFileName());</span>
<span class="nc" id="L98">            headers.add(&quot;Content-Disposition&quot;, &quot;attachment; filename=\&quot;&quot; + filename + &quot;\&quot;&quot;);</span>
            
            // Add custom headers
<span class="nc" id="L101">            headers.add(&quot;X-Document-ID&quot;, document.getId().toString());</span>
<span class="nc" id="L102">            headers.add(&quot;X-Document-Version&quot;, document.getVersion().toString());</span>
<span class="nc" id="L103">            headers.add(&quot;X-API-Version&quot;, &quot;1.0&quot;);</span>
            
<span class="nc" id="L105">            log.info(&quot;API v1: Successfully downloaded document ID: {}, filename: {}, size: {} bytes&quot;, </span>
<span class="nc" id="L106">                    id, filename, fileContent.length);</span>
            
<span class="nc" id="L108">            return ResponseEntity.ok()</span>
<span class="nc" id="L109">                    .headers(headers)</span>
<span class="nc" id="L110">                    .body(fileContent);</span>
                    
<span class="nc" id="L112">        } catch (DocumentNotFoundException e) {</span>
<span class="nc" id="L113">            log.warn(&quot;API v1: Document not found for ID: {}&quot;, id);</span>
<span class="nc" id="L114">            throw e;</span>
<span class="nc" id="L115">        } catch (UnauthorizedException e) {</span>
<span class="nc" id="L116">            log.warn(&quot;API v1: Unauthorized download attempt for document ID: {} by user: {}&quot;, </span>
<span class="nc" id="L117">                    id, getCurrentUserId());</span>
<span class="nc" id="L118">            throw e;</span>
<span class="nc" id="L119">        } catch (Exception e) {</span>
<span class="nc" id="L120">            log.error(&quot;API v1: Error downloading document ID: {}&quot;, id, e);</span>
<span class="nc" id="L121">            throw new RuntimeException(&quot;Failed to download document: &quot; + e.getMessage(), e);</span>
        }
    }

    /**
     * Download document by ID (API v2).
     * 
     * &lt;p&gt;Enhanced download functionality with improved streaming support,
     * additional metadata headers, and better error handling.
     * 
     * @param id the document ID to download
     * @param inline optional parameter to display inline instead of download (default: false)
     * @return ResponseEntity containing the file content as Resource for better streaming
     * @throws DocumentNotFoundException if document doesn't exist
     * @throws UnauthorizedException if user lacks READ permission
     */
    @GetMapping(&quot;/v2/documents/{id}/download&quot;)
    @ApiVersion(since = &quot;2.0&quot;, description = &quot;Enhanced download with streaming support&quot;)
    @RateLimit(value = 30, window = 60, type = RateLimit.RateLimitType.DOCUMENT_DOWNLOAD,
               message = &quot;Document download rate limit exceeded. Please wait before downloading again.&quot;)
    @PreAuthorize(&quot;hasAnyRole('USER', 'ADMIN', 'MANAGER')&quot;)
    public ResponseEntity&lt;Resource&gt; downloadDocumentV2(
            @PathVariable Long id,
            @RequestParam(defaultValue = &quot;false&quot;) boolean inline) {
        
<span class="nc" id="L146">        log.info(&quot;API v2: Download request for document ID: {}, inline: {}&quot;, id, inline);</span>
        
        try {
            // Get document metadata for headers
<span class="nc" id="L150">            Document document = documentService.getDocumentById(id);</span>
            
            // Download actual file content
<span class="nc" id="L153">            byte[] fileContent = documentService.downloadDocument(id);</span>
            
            // Create resource for streaming
<span class="nc" id="L156">            ByteArrayResource resource = new ByteArrayResource(fileContent);</span>
            
            // Build enhanced response headers
<span class="nc" id="L159">            HttpHeaders headers = new HttpHeaders();</span>
<span class="nc" id="L160">            headers.setContentType(MediaType.parseMediaType(</span>
<span class="nc bnc" id="L161" title="All 2 branches missed.">                document.getMimeType() != null ? document.getMimeType() : &quot;application/octet-stream&quot;));</span>
<span class="nc" id="L162">            headers.setContentLength(fileContent.length);</span>
            
            // Set content-disposition based on inline parameter
<span class="nc" id="L165">            String filename = sanitizeFilename(document.getOriginalFileName());</span>
<span class="nc bnc" id="L166" title="All 2 branches missed.">            if (inline) {</span>
<span class="nc" id="L167">                headers.add(&quot;Content-Disposition&quot;, &quot;inline; filename=\&quot;&quot; + filename + &quot;\&quot;&quot;);</span>
            } else {
<span class="nc" id="L169">                headers.add(&quot;Content-Disposition&quot;, &quot;attachment; filename=\&quot;&quot; + filename + &quot;\&quot;&quot;);</span>
            }
            
            // Add comprehensive metadata headers
<span class="nc" id="L173">            headers.add(&quot;X-Document-ID&quot;, document.getId().toString());</span>
<span class="nc" id="L174">            headers.add(&quot;X-Document-Name&quot;, document.getName());</span>
<span class="nc" id="L175">            headers.add(&quot;X-Document-Version&quot;, document.getVersion().toString());</span>
<span class="nc" id="L176">            headers.add(&quot;X-Document-Status&quot;, document.getStatus().toString());</span>
<span class="nc" id="L177">            headers.add(&quot;X-Document-Creator&quot;, document.getCreatorUserId());</span>
<span class="nc" id="L178">            headers.add(&quot;X-Document-Created&quot;, document.getCreatedDate().toString());</span>
<span class="nc" id="L179">            headers.add(&quot;X-Document-Modified&quot;, document.getLastModifiedDate().toString());</span>
<span class="nc" id="L180">            headers.add(&quot;X-Storage-Provider&quot;, document.getStorageProvider().toString());</span>
<span class="nc" id="L181">            headers.add(&quot;X-API-Version&quot;, &quot;2.0&quot;);</span>
            
            // Add cache control headers
<span class="nc" id="L184">            headers.setCacheControl(&quot;private, max-age=3600&quot;); // Cache for 1 hour</span>
<span class="nc" id="L185">            headers.add(&quot;ETag&quot;, &quot;\&quot;&quot; + document.getId() + &quot;-&quot; + document.getVersion() + &quot;\&quot;&quot;);</span>
            
<span class="nc" id="L187">            log.info(&quot;API v2: Successfully downloaded document ID: {}, filename: {}, size: {} bytes, inline: {}&quot;, </span>
<span class="nc" id="L188">                    id, filename, fileContent.length, inline);</span>
            
<span class="nc" id="L190">            return ResponseEntity.ok()</span>
<span class="nc" id="L191">                    .headers(headers)</span>
<span class="nc" id="L192">                    .body(resource);</span>
                    
<span class="nc" id="L194">        } catch (DocumentNotFoundException e) {</span>
<span class="nc" id="L195">            log.warn(&quot;API v2: Document not found for ID: {}&quot;, id);</span>
<span class="nc" id="L196">            throw e;</span>
<span class="nc" id="L197">        } catch (UnauthorizedException e) {</span>
<span class="nc" id="L198">            log.warn(&quot;API v2: Unauthorized download attempt for document ID: {} by user: {}&quot;, </span>
<span class="nc" id="L199">                    id, getCurrentUserId());</span>
<span class="nc" id="L200">            throw e;</span>
<span class="nc" id="L201">        } catch (Exception e) {</span>
<span class="nc" id="L202">            log.error(&quot;API v2: Error downloading document ID: {}&quot;, id, e);</span>
<span class="nc" id="L203">            throw new RuntimeException(&quot;Failed to download document: &quot; + e.getMessage(), e);</span>
        }
    }

    /**
     * Get download metadata without downloading the file (API v2).
     * 
     * &lt;p&gt;Returns document metadata and download information without transferring
     * the actual file content. Useful for checking file properties before download.
     * 
     * @param id the document ID
     * @return ResponseEntity containing document download metadata
     */
    @GetMapping(&quot;/v2/documents/{id}/download/info&quot;)
    @ApiVersion(since = &quot;2.0&quot;, description = &quot;Get download metadata without file content&quot;)
    @RateLimit(value = 60, window = 60, type = RateLimit.RateLimitType.SEARCH_OPERATION,
               message = &quot;Download info rate limit exceeded.&quot;)
    @PreAuthorize(&quot;hasAnyRole('USER', 'ADMIN', 'MANAGER')&quot;)
    public ResponseEntity&lt;Map&lt;String, Object&gt;&gt; getDownloadInfo(@PathVariable Long id) {
<span class="nc" id="L222">        log.info(&quot;API v2: Download info request for document ID: {}&quot;, id);</span>
        
        try {
<span class="nc" id="L225">            Document document = documentService.getDocumentById(id);</span>
            
<span class="nc" id="L227">            Map&lt;String, Object&gt; downloadInfo = new HashMap&lt;&gt;();</span>
<span class="nc" id="L228">            downloadInfo.put(&quot;documentId&quot;, document.getId());</span>
<span class="nc" id="L229">            downloadInfo.put(&quot;name&quot;, document.getName());</span>
<span class="nc" id="L230">            downloadInfo.put(&quot;originalFileName&quot;, document.getOriginalFileName());</span>
<span class="nc" id="L231">            downloadInfo.put(&quot;fileSize&quot;, document.getFileSize());</span>
<span class="nc" id="L232">            downloadInfo.put(&quot;mimeType&quot;, document.getMimeType());</span>
<span class="nc" id="L233">            downloadInfo.put(&quot;version&quot;, document.getVersion());</span>
<span class="nc" id="L234">            downloadInfo.put(&quot;status&quot;, document.getStatus());</span>
<span class="nc" id="L235">            downloadInfo.put(&quot;storageProvider&quot;, document.getStorageProvider());</span>
<span class="nc" id="L236">            downloadInfo.put(&quot;createdBy&quot;, document.getCreatorUserId());</span>
<span class="nc" id="L237">            downloadInfo.put(&quot;createdDate&quot;, document.getCreatedDate());</span>
<span class="nc" id="L238">            downloadInfo.put(&quot;lastModifiedDate&quot;, document.getLastModifiedDate());</span>
<span class="nc" id="L239">            downloadInfo.put(&quot;downloadUrl&quot;, &quot;/api/v2/documents/&quot; + id + &quot;/download&quot;);</span>
<span class="nc" id="L240">            downloadInfo.put(&quot;inlineUrl&quot;, &quot;/api/v2/documents/&quot; + id + &quot;/download?inline=true&quot;);</span>
<span class="nc" id="L241">            downloadInfo.put(&quot;apiVersion&quot;, &quot;2.0&quot;);</span>
<span class="nc" id="L242">            downloadInfo.put(&quot;timestamp&quot;, Instant.now());</span>
            
<span class="nc" id="L244">            log.info(&quot;API v2: Successfully retrieved download info for document ID: {}&quot;, id);</span>
            
<span class="nc" id="L246">            return ResponseEntity.ok(downloadInfo);</span>
            
<span class="nc" id="L248">        } catch (DocumentNotFoundException e) {</span>
<span class="nc" id="L249">            log.warn(&quot;API v2: Document not found for download info request, ID: {}&quot;, id);</span>
<span class="nc" id="L250">            throw e;</span>
<span class="nc" id="L251">        } catch (UnauthorizedException e) {</span>
<span class="nc" id="L252">            log.warn(&quot;API v2: Unauthorized download info request for document ID: {} by user: {}&quot;, </span>
<span class="nc" id="L253">                    id, getCurrentUserId());</span>
<span class="nc" id="L254">            throw e;</span>
<span class="nc" id="L255">        } catch (Exception e) {</span>
<span class="nc" id="L256">            log.error(&quot;API v2: Error retrieving download info for document ID: {}&quot;, id, e);</span>
<span class="nc" id="L257">            throw new RuntimeException(&quot;Failed to retrieve download info: &quot; + e.getMessage(), e);</span>
        }
    }

    /**
     * Bulk download multiple documents as a ZIP file (API v2).
     * 
     * &lt;p&gt;Downloads multiple documents packaged in a ZIP archive.
     * Useful for downloading related documents or document collections.
     * 
     * @param documentIds comma-separated list of document IDs
     * @return ResponseEntity containing ZIP file with requested documents
     */
    @GetMapping(&quot;/v2/documents/download/bulk&quot;)
    @ApiVersion(since = &quot;2.0&quot;, description = &quot;Bulk download multiple documents as ZIP&quot;)
    @RateLimit(value = 5, window = 300, type = RateLimit.RateLimitType.DOCUMENT_DOWNLOAD,
               message = &quot;Bulk download rate limit exceeded. Please wait before downloading again.&quot;)
    @PreAuthorize(&quot;hasAnyRole('USER', 'ADMIN', 'MANAGER')&quot;)
    public ResponseEntity&lt;Resource&gt; bulkDownload(@RequestParam String documentIds) {
<span class="nc" id="L276">        log.info(&quot;API v2: Bulk download request for document IDs: {}&quot;, documentIds);</span>
        
        // TODO: Implement bulk download functionality
        // This would involve:
        // 1. Parse document IDs
        // 2. Validate permissions for each document
        // 3. Create ZIP archive with all documents
        // 4. Return ZIP as response
        
<span class="nc" id="L285">        throw new UnsupportedOperationException(&quot;Bulk download functionality not yet implemented&quot;);</span>
    }

    // Helper methods

    /**
     * Sanitize filename for safe HTTP header usage.
     * 
     * @param filename the original filename
     * @return sanitized filename safe for HTTP headers
     */
    private String sanitizeFilename(String filename) {
<span class="nc bnc" id="L297" title="All 4 branches missed.">        if (filename == null || filename.trim().isEmpty()) {</span>
<span class="nc" id="L298">            return &quot;document&quot;;</span>
        }
        
        try {
            // URL encode the filename to handle special characters
<span class="nc" id="L303">            return URLEncoder.encode(filename, StandardCharsets.UTF_8.toString())</span>
<span class="nc" id="L304">                    .replace(&quot;+&quot;, &quot;%20&quot;); // Replace + with %20 for spaces</span>
<span class="nc" id="L305">        } catch (UnsupportedEncodingException e) {</span>
<span class="nc" id="L306">            log.warn(&quot;Failed to encode filename: {}, using fallback&quot;, filename);</span>
            // Fallback: remove problematic characters
<span class="nc" id="L308">            return filename.replaceAll(&quot;[^a-zA-Z0-9._-]&quot;, &quot;_&quot;);</span>
        }
    }

    /**
     * Get current user ID from security context.
     * 
     * @return current user ID or &quot;anonymous&quot; if not authenticated
     */
    private String getCurrentUserId() {
        try {
<span class="nc" id="L319">            Authentication authentication = SecurityContextHolder.getContext().getAuthentication();</span>
<span class="nc bnc" id="L320" title="All 4 branches missed.">            if (authentication != null &amp;&amp; authentication.isAuthenticated()) {</span>
<span class="nc" id="L321">                return authentication.getName();</span>
            }
<span class="nc" id="L323">        } catch (Exception e) {</span>
<span class="nc" id="L324">            log.debug(&quot;Failed to get current user ID&quot;, e);</span>
<span class="nc" id="L325">        }</span>
<span class="nc" id="L326">        return &quot;anonymous&quot;;</span>
    }

    // Exception handlers

    /**
     * Handle DocumentNotFoundException.
     */
    @ExceptionHandler(DocumentNotFoundException.class)
    public ResponseEntity&lt;Map&lt;String, Object&gt;&gt; handleDocumentNotFound(DocumentNotFoundException e) {
<span class="nc" id="L336">        log.warn(&quot;Document not found: {}&quot;, e.getMessage());</span>
        
<span class="nc" id="L338">        Map&lt;String, Object&gt; error = new HashMap&lt;&gt;();</span>
<span class="nc" id="L339">        error.put(&quot;error&quot;, &quot;DOCUMENT_NOT_FOUND&quot;);</span>
<span class="nc" id="L340">        error.put(&quot;message&quot;, e.getMessage());</span>
<span class="nc" id="L341">        error.put(&quot;timestamp&quot;, Instant.now());</span>
<span class="nc" id="L342">        error.put(&quot;status&quot;, HttpStatus.NOT_FOUND.value());</span>
        
<span class="nc" id="L344">        return ResponseEntity.status(HttpStatus.NOT_FOUND).body(error);</span>
    }

    /**
     * Handle UnauthorizedException.
     */
    @ExceptionHandler(UnauthorizedException.class)
    public ResponseEntity&lt;Map&lt;String, Object&gt;&gt; handleUnauthorized(UnauthorizedException e) {
<span class="nc" id="L352">        log.warn(&quot;Unauthorized access: {}&quot;, e.getMessage());</span>
        
<span class="nc" id="L354">        Map&lt;String, Object&gt; error = new HashMap&lt;&gt;();</span>
<span class="nc" id="L355">        error.put(&quot;error&quot;, &quot;UNAUTHORIZED&quot;);</span>
<span class="nc" id="L356">        error.put(&quot;message&quot;, e.getMessage());</span>
<span class="nc" id="L357">        error.put(&quot;timestamp&quot;, Instant.now());</span>
<span class="nc" id="L358">        error.put(&quot;status&quot;, HttpStatus.FORBIDDEN.value());</span>
        
<span class="nc" id="L360">        return ResponseEntity.status(HttpStatus.FORBIDDEN).body(error);</span>
    }

    /**
     * Handle general exceptions.
     */
    @ExceptionHandler(Exception.class)
    public ResponseEntity&lt;Map&lt;String, Object&gt;&gt; handleGeneralException(Exception e) {
<span class="nc" id="L368">        log.error(&quot;Unexpected error in document download&quot;, e);</span>
        
<span class="nc" id="L370">        Map&lt;String, Object&gt; error = new HashMap&lt;&gt;();</span>
<span class="nc" id="L371">        error.put(&quot;error&quot;, &quot;INTERNAL_SERVER_ERROR&quot;);</span>
<span class="nc" id="L372">        error.put(&quot;message&quot;, &quot;An unexpected error occurred during document download&quot;);</span>
<span class="nc" id="L373">        error.put(&quot;timestamp&quot;, Instant.now());</span>
<span class="nc" id="L374">        error.put(&quot;status&quot;, HttpStatus.INTERNAL_SERVER_ERROR.value());</span>
        
<span class="nc" id="L376">        return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body(error);</span>
    }
}
</pre><div class="footer"><span class="right">Created with <a href="http://www.jacoco.org/jacoco">JaCoCo</a> 0.8.10.202304240956</span></div></body></html>