<?xml version="1.0" encoding="UTF-8"?><!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Strict//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-strict.dtd"><html xmlns="http://www.w3.org/1999/xhtml" lang="en"><head><meta http-equiv="Content-Type" content="text/html;charset=UTF-8"/><link rel="stylesheet" href="../jacoco-resources/report.css" type="text/css"/><link rel="shortcut icon" href="../jacoco-resources/report.gif" type="image/gif"/><title>StorageConfigurationService.java</title><link rel="stylesheet" href="../jacoco-resources/prettify.css" type="text/css"/><script type="text/javascript" src="../jacoco-resources/prettify.js"></script></head><body onload="window['PR_TAB_WIDTH']=4;prettyPrint()"><div class="breadcrumb" id="breadcrumb"><span class="info"><a href="../jacoco-sessions.html" class="el_session">Sessions</a></span><a href="../index.html" class="el_report">dms-svc</a> &gt; <a href="index.source.html" class="el_package">com.ascentbusiness.dms_svc.service</a> &gt; <span class="el_source">StorageConfigurationService.java</span></div><h1>StorageConfigurationService.java</h1><pre class="source lang-java linenums">package com.ascentbusiness.dms_svc.service;

import com.ascentbusiness.dms_svc.entity.StorageConfiguration;
import com.ascentbusiness.dms_svc.enums.StorageProvider;
import com.ascentbusiness.dms_svc.repository.StorageConfigurationRepository;
import com.ascentbusiness.dms_svc.util.CorrelationIdUtil;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cache.annotation.CacheEvict;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Optional;

/**
 * Service for managing storage configurations with caching and health monitoring.
 */
@Service
<span class="fc" id="L26">public class StorageConfigurationService {</span>

<span class="fc" id="L28">    private static final Logger logger = LoggerFactory.getLogger(StorageConfigurationService.class);</span>
    private static final String CACHE_NAME = &quot;storageConfigurations&quot;;

    @Autowired
    private StorageConfigurationRepository configurationRepository;

    @Autowired
    private ObjectMapper objectMapper;

    /**
     * Get configuration by provider type with caching.
     */
    @Cacheable(value = CACHE_NAME, key = &quot;#providerType&quot;)
    public Optional&lt;StorageConfiguration&gt; getConfigurationByProvider(StorageProvider providerType) {
<span class="nc" id="L42">        String correlationId = CorrelationIdUtil.getCurrentCorrelationId();</span>
<span class="nc" id="L43">        logger.info(&quot;Getting configuration for provider: {} [{}]&quot;, providerType, correlationId);</span>
        
<span class="nc" id="L45">        return configurationRepository.findActiveByProviderType(providerType);</span>
    }

    /**
     * Get the default storage configuration.
     */
    @Cacheable(value = CACHE_NAME, key = &quot;'default'&quot;)
    public Optional&lt;StorageConfiguration&gt; getDefaultConfiguration() {
<span class="nc" id="L53">        String correlationId = CorrelationIdUtil.getCurrentCorrelationId();</span>
<span class="nc" id="L54">        logger.info(&quot;Getting default storage configuration [{}]&quot;, correlationId);</span>
        
<span class="nc" id="L56">        return configurationRepository.findDefaultConfiguration();</span>
    }

    /**
     * Get all active configurations ordered by priority.
     */
    @Cacheable(value = CACHE_NAME, key = &quot;'active'&quot;)
    public List&lt;StorageConfiguration&gt; getActiveConfigurations() {
<span class="nc" id="L64">        String correlationId = CorrelationIdUtil.getCurrentCorrelationId();</span>
<span class="nc" id="L65">        logger.info(&quot;Getting all active storage configurations [{}]&quot;, correlationId);</span>
        
<span class="nc" id="L67">        return configurationRepository.findAllActiveOrderByPriority();</span>
    }

    /**
     * Create or update storage configuration.
     */
    @Transactional
    @CacheEvict(value = CACHE_NAME, allEntries = true)
    public StorageConfiguration saveConfiguration(StorageConfiguration configuration, String userId) {
<span class="nc" id="L76">        String correlationId = CorrelationIdUtil.getCurrentCorrelationId();</span>
<span class="nc" id="L77">        logger.info(&quot;Saving storage configuration for provider: {} by user: {} [{}]&quot;, </span>
<span class="nc" id="L78">                   configuration.getProviderType(), userId, correlationId);</span>

        // Validate configuration JSON
<span class="nc bnc" id="L81" title="All 2 branches missed.">        if (!isValidConfigurationJson(configuration.getConfigurationJson())) {</span>
<span class="nc" id="L82">            throw new IllegalArgumentException(&quot;Invalid configuration JSON format&quot;);</span>
        }

        // Set audit fields
<span class="nc bnc" id="L86" title="All 2 branches missed.">        if (configuration.getId() == null) {</span>
<span class="nc" id="L87">            configuration.setCreatedBy(userId);</span>
        } else {
<span class="nc" id="L89">            configuration.setLastModifiedBy(userId);</span>
        }

        // If setting as default, clear other defaults first
<span class="nc bnc" id="L93" title="All 2 branches missed.">        if (Boolean.TRUE.equals(configuration.getIsDefault())) {</span>
<span class="nc" id="L94">            configurationRepository.clearAllDefaultFlags();</span>
        }

<span class="nc" id="L97">        StorageConfiguration saved = configurationRepository.save(configuration);</span>
        
<span class="nc" id="L99">        logger.info(&quot;Storage configuration saved with ID: {} [{}]&quot;, saved.getId(), correlationId);</span>
<span class="nc" id="L100">        return saved;</span>
    }

    /**
     * Set configuration as default.
     */
    @Transactional
    @CacheEvict(value = CACHE_NAME, allEntries = true)
    public void setAsDefault(Long configurationId, String userId) {
<span class="nc" id="L109">        String correlationId = CorrelationIdUtil.getCurrentCorrelationId();</span>
<span class="nc" id="L110">        logger.info(&quot;Setting configuration {} as default by user: {} [{}]&quot;, configurationId, userId, correlationId);</span>

<span class="nc" id="L112">        Optional&lt;StorageConfiguration&gt; configOpt = configurationRepository.findById(configurationId);</span>
<span class="nc bnc" id="L113" title="All 2 branches missed.">        if (configOpt.isEmpty()) {</span>
<span class="nc" id="L114">            throw new IllegalArgumentException(&quot;Configuration not found with ID: &quot; + configurationId);</span>
        }

<span class="nc" id="L117">        StorageConfiguration config = configOpt.get();</span>
<span class="nc bnc" id="L118" title="All 2 branches missed.">        if (!config.getIsActive()) {</span>
<span class="nc" id="L119">            throw new IllegalArgumentException(&quot;Cannot set inactive configuration as default&quot;);</span>
        }

<span class="nc bnc" id="L122" title="All 2 branches missed.">        if (!config.isHealthy()) {</span>
<span class="nc" id="L123">            throw new IllegalArgumentException(&quot;Cannot set unhealthy configuration as default&quot;);</span>
        }

        // Clear all default flags and set this one as default
<span class="nc" id="L127">        configurationRepository.clearAllDefaultFlags();</span>
<span class="nc" id="L128">        configurationRepository.setAsDefault(configurationId);</span>

<span class="nc" id="L130">        logger.info(&quot;Configuration {} set as default [{}]&quot;, configurationId, correlationId);</span>
<span class="nc" id="L131">    }</span>

    /**
     * Activate or deactivate configuration.
     */
    @Transactional
    @CacheEvict(value = CACHE_NAME, allEntries = true)
    public void toggleConfiguration(Long configurationId, boolean active, String userId) {
<span class="nc" id="L139">        String correlationId = CorrelationIdUtil.getCurrentCorrelationId();</span>
<span class="nc" id="L140">        logger.info(&quot;Toggling configuration {} to active: {} by user: {} [{}]&quot;, </span>
<span class="nc" id="L141">                   configurationId, active, userId, correlationId);</span>

<span class="nc" id="L143">        Optional&lt;StorageConfiguration&gt; configOpt = configurationRepository.findById(configurationId);</span>
<span class="nc bnc" id="L144" title="All 2 branches missed.">        if (configOpt.isEmpty()) {</span>
<span class="nc" id="L145">            throw new IllegalArgumentException(&quot;Configuration not found with ID: &quot; + configurationId);</span>
        }

<span class="nc" id="L148">        StorageConfiguration config = configOpt.get();</span>
<span class="nc" id="L149">        config.setIsActive(active);</span>
<span class="nc" id="L150">        config.setLastModifiedBy(userId);</span>

        // If deactivating the default configuration, clear default flag
<span class="nc bnc" id="L153" title="All 4 branches missed.">        if (!active &amp;&amp; Boolean.TRUE.equals(config.getIsDefault())) {</span>
<span class="nc" id="L154">            config.setIsDefault(false);</span>
        }

<span class="nc" id="L157">        configurationRepository.save(config);</span>
<span class="nc" id="L158">        logger.info(&quot;Configuration {} toggled to active: {} [{}]&quot;, configurationId, active, correlationId);</span>
<span class="nc" id="L159">    }</span>

    /**
     * Delete configuration.
     */
    @Transactional
    @CacheEvict(value = CACHE_NAME, allEntries = true)
    public void deleteConfiguration(Long configurationId, String userId) {
<span class="nc" id="L167">        String correlationId = CorrelationIdUtil.getCurrentCorrelationId();</span>
<span class="nc" id="L168">        logger.info(&quot;Deleting configuration {} by user: {} [{}]&quot;, configurationId, userId, correlationId);</span>

<span class="nc" id="L170">        Optional&lt;StorageConfiguration&gt; configOpt = configurationRepository.findById(configurationId);</span>
<span class="nc bnc" id="L171" title="All 2 branches missed.">        if (configOpt.isEmpty()) {</span>
<span class="nc" id="L172">            throw new IllegalArgumentException(&quot;Configuration not found with ID: &quot; + configurationId);</span>
        }

<span class="nc" id="L175">        StorageConfiguration config = configOpt.get();</span>
<span class="nc bnc" id="L176" title="All 2 branches missed.">        if (Boolean.TRUE.equals(config.getIsDefault())) {</span>
<span class="nc" id="L177">            throw new IllegalArgumentException(&quot;Cannot delete default configuration. Set another as default first.&quot;);</span>
        }

<span class="nc" id="L180">        configurationRepository.deleteById(configurationId);</span>
<span class="nc" id="L181">        logger.info(&quot;Configuration {} deleted [{}]&quot;, configurationId, correlationId);</span>
<span class="nc" id="L182">    }</span>

    /**
     * Perform health check on all configurations.
     */
    @Scheduled(fixedRate = 300000) // Every 5 minutes
    @CacheEvict(value = CACHE_NAME, allEntries = true)
    public void performHealthChecks() {
<span class="fc" id="L190">        String correlationId = CorrelationIdUtil.getCurrentCorrelationId();</span>
<span class="fc" id="L191">        logger.info(&quot;Starting scheduled health checks [{}]&quot;, correlationId);</span>

<span class="fc" id="L193">        LocalDateTime checkTime = LocalDateTime.now().minusMinutes(5);</span>
<span class="fc" id="L194">        List&lt;StorageConfiguration&gt; configurationsToCheck = </span>
<span class="fc" id="L195">            configurationRepository.findConfigurationsNeedingHealthCheck(checkTime);</span>

<span class="pc bpc" id="L197" title="1 of 2 branches missed.">        for (StorageConfiguration config : configurationsToCheck) {</span>
            try {
<span class="nc" id="L199">                boolean isHealthy = performHealthCheck(config);</span>
<span class="nc bnc" id="L200" title="All 2 branches missed.">                if (isHealthy) {</span>
<span class="nc" id="L201">                    config.markHealthy();</span>
                } else {
<span class="nc" id="L203">                    config.markUnhealthy(&quot;Health check failed&quot;);</span>
                }
<span class="nc" id="L205">                configurationRepository.save(config);</span>
                
<span class="nc" id="L207">                logger.info(&quot;Health check completed for provider: {} - Status: {} [{}]&quot;, </span>
<span class="nc" id="L208">                           config.getProviderType(), config.getHealthStatus(), correlationId);</span>
<span class="nc" id="L209">            } catch (Exception e) {</span>
<span class="nc" id="L210">                config.markUnhealthy(&quot;Health check error: &quot; + e.getMessage());</span>
<span class="nc" id="L211">                configurationRepository.save(config);</span>
<span class="nc" id="L212">                logger.error(&quot;Health check failed for provider: {} [{}]&quot;, config.getProviderType(), correlationId, e);</span>
<span class="nc" id="L213">            }</span>
<span class="nc" id="L214">        }</span>

<span class="fc" id="L216">        logger.info(&quot;Completed health checks for {} configurations [{}]&quot;, </span>
<span class="fc" id="L217">                   configurationsToCheck.size(), correlationId);</span>
<span class="fc" id="L218">    }</span>

    /**
     * Get all configurations for admin interface.
     */
    public List&lt;StorageConfiguration&gt; getAllConfigurations() {
<span class="fc" id="L224">        String correlationId = CorrelationIdUtil.getCurrentCorrelationId();</span>
<span class="fc" id="L225">        logger.info(&quot;Getting all storage configurations for admin [{}]&quot;, correlationId);</span>
        
<span class="fc" id="L227">        return configurationRepository.findAllOrderByPriorityAndDate();</span>
    }

    /**
     * Test configuration connectivity.
     */
    public boolean testConfiguration(StorageConfiguration configuration) {
<span class="nc" id="L234">        String correlationId = CorrelationIdUtil.getCurrentCorrelationId();</span>
<span class="nc" id="L235">        logger.info(&quot;Testing configuration for provider: {} [{}]&quot;, configuration.getProviderType(), correlationId);</span>

        try {
<span class="nc" id="L238">            return performHealthCheck(configuration);</span>
<span class="nc" id="L239">        } catch (Exception e) {</span>
<span class="nc" id="L240">            logger.error(&quot;Configuration test failed for provider: {} [{}]&quot;, </span>
<span class="nc" id="L241">                        configuration.getProviderType(), correlationId, e);</span>
<span class="nc" id="L242">            return false;</span>
        }
    }

    /**
     * Validate configuration JSON format.
     */
    private boolean isValidConfigurationJson(String configJson) {
        try {
<span class="nc" id="L251">            JsonNode jsonNode = objectMapper.readTree(configJson);</span>
<span class="nc bnc" id="L252" title="All 4 branches missed.">            return jsonNode != null &amp;&amp; jsonNode.isObject();</span>
<span class="nc" id="L253">        } catch (Exception e) {</span>
<span class="nc" id="L254">            logger.error(&quot;Invalid configuration JSON: {}&quot;, e.getMessage());</span>
<span class="nc" id="L255">            return false;</span>
        }
    }

    /**
     * Perform actual health check based on provider type.
     */
    private boolean performHealthCheck(StorageConfiguration configuration) {
<span class="nc bnc" id="L263" title="All 4 branches missed.">        switch (configuration.getProviderType()) {</span>
            case LOCAL:
<span class="nc" id="L265">                return performLocalHealthCheck(configuration);</span>
            case S3:
<span class="nc" id="L267">                return performS3HealthCheck(configuration);</span>
            case SHAREPOINT:
<span class="nc" id="L269">                return performSharePointHealthCheck(configuration);</span>
            default:
<span class="nc" id="L271">                return false;</span>
        }
    }

    private boolean performLocalHealthCheck(StorageConfiguration configuration) {
        // Implementation for local storage health check
        // Check if directory exists and is writable
<span class="nc" id="L278">        return true; // Simplified for now</span>
    }

    private boolean performS3HealthCheck(StorageConfiguration configuration) {
        // Implementation for S3 health check
        // Test S3 connectivity and permissions
<span class="nc" id="L284">        return true; // Simplified for now</span>
    }

    private boolean performSharePointHealthCheck(StorageConfiguration configuration) {
        // Implementation for SharePoint health check
        // Test SharePoint connectivity and authentication
<span class="nc" id="L290">        return true; // Simplified for now</span>
    }
}
</pre><div class="footer"><span class="right">Created with <a href="http://www.jacoco.org/jacoco">JaCoCo</a> 0.8.10.202304240956</span></div></body></html>