--liquibase formatted sql

--changeset system:063-create-event-templates-table
CREATE TABLE event_templates (
    id BIGINT AUTO_INCREMENT PRIMARY KEY,
    name VARCHAR(255) NOT NULL UNIQUE,
    description TEXT,
    event_type VARCHAR(100) NOT NULL,
    template_format VARCHAR(50) NOT NULL DEFAULT 'JSON',
    template_content TEXT NOT NULL,
    available_variables JSON,
    required_variables JSON,
    is_system_template BOOLEAN NOT NULL DEFAULT FALSE,
    is_active BOOLEAN NOT NULL DEFAULT TRUE,
    version VARCHAR(50) NOT NULL DEFAULT '1.0',
    usage_count INT DEFAULT 0,
    last_used_date DATETIME,
    created_by <PERSON><PERSON><PERSON><PERSON>(100) NOT NULL,
    last_modified_by VA<PERSON><PERSON><PERSON>(100),
    created_date DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
    last_modified_date DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
);

--rollback DROP TABLE event_templates;