<?xml version="1.0" encoding="UTF-8"?><!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Strict//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-strict.dtd"><html xmlns="http://www.w3.org/1999/xhtml" lang="en"><head><meta http-equiv="Content-Type" content="text/html;charset=UTF-8"/><link rel="stylesheet" href="../jacoco-resources/report.css" type="text/css"/><link rel="shortcut icon" href="../jacoco-resources/report.gif" type="image/gif"/><title>RetentionPolicyService.java</title><link rel="stylesheet" href="../jacoco-resources/prettify.css" type="text/css"/><script type="text/javascript" src="../jacoco-resources/prettify.js"></script></head><body onload="window['PR_TAB_WIDTH']=4;prettyPrint()"><div class="breadcrumb" id="breadcrumb"><span class="info"><a href="../jacoco-sessions.html" class="el_session">Sessions</a></span><a href="../index.html" class="el_report">dms-svc</a> &gt; <a href="index.source.html" class="el_package">com.ascentbusiness.dms_svc.service</a> &gt; <span class="el_source">RetentionPolicyService.java</span></div><h1>RetentionPolicyService.java</h1><pre class="source lang-java linenums">package com.ascentbusiness.dms_svc.service;

import com.ascentbusiness.dms_svc.entity.Document;
import com.ascentbusiness.dms_svc.entity.RetentionPolicy;
import com.ascentbusiness.dms_svc.entity.RetentionPolicyAssignment;
import com.ascentbusiness.dms_svc.enums.AuditAction;
import com.ascentbusiness.dms_svc.exception.DuplicateResourceException;
import com.ascentbusiness.dms_svc.exception.ResourceNotFoundException;
import com.ascentbusiness.dms_svc.repository.RetentionPolicyAssignmentRepository;
import com.ascentbusiness.dms_svc.repository.RetentionPolicyRepository;
import com.ascentbusiness.dms_svc.security.UserContext;
import com.ascentbusiness.dms_svc.util.CorrelationIdUtil;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;
import java.util.Optional;

/**
 * Service for managing retention policies
 */
@Service
@Transactional
<span class="fc" id="L29">public class RetentionPolicyService {</span>
    
<span class="fc" id="L31">    private static final Logger logger = LoggerFactory.getLogger(RetentionPolicyService.class);</span>
    
    @Autowired
    private RetentionPolicyRepository retentionPolicyRepository;
    
    @Autowired
    private RetentionPolicyAssignmentRepository assignmentRepository;
    
    @Autowired
    private AuditService auditService;

    @Autowired
    private UserContext userContext;
    
    /**
     * Create a new retention policy
     */
    public RetentionPolicy createRetentionPolicy(RetentionPolicy policy) {
<span class="nc" id="L49">        String correlationId = CorrelationIdUtil.getCurrentCorrelationId();</span>
<span class="nc" id="L50">        String currentUserId = userContext.getCurrentUserId();</span>
        
<span class="nc" id="L52">        logger.info(&quot;Creating retention policy: {} [{}]&quot;, policy.getName(), correlationId);</span>
        
        // Check for duplicate name
<span class="nc bnc" id="L55" title="All 2 branches missed.">        if (retentionPolicyRepository.existsByNameAndIdNot(policy.getName(), null)) {</span>
<span class="nc" id="L56">            throw new DuplicateResourceException(&quot;Retention policy with name '&quot; + policy.getName() + &quot;' already exists&quot;);</span>
        }
        
        // Validate policy
<span class="nc" id="L60">        validateRetentionPolicy(policy);</span>
        
        // Save policy
<span class="nc" id="L63">        RetentionPolicy savedPolicy = retentionPolicyRepository.save(policy);</span>
        
        // Audit log
<span class="nc" id="L66">        auditService.logAudit(AuditAction.RETENTION_POLICY_CREATED, null, currentUserId,</span>
<span class="nc" id="L67">                String.format(&quot;Created retention policy: %s (ID: %d)&quot;, savedPolicy.getName(), savedPolicy.getId()));</span>
        
<span class="nc" id="L69">        logger.info(&quot;Retention policy created successfully: {} (ID: {}) [{}]&quot;, </span>
<span class="nc" id="L70">                savedPolicy.getName(), savedPolicy.getId(), correlationId);</span>
        
<span class="nc" id="L72">        return savedPolicy;</span>
    }
    
    /**
     * Update an existing retention policy
     */
    public RetentionPolicy updateRetentionPolicy(Long policyId, RetentionPolicy updatedPolicy) {
<span class="nc" id="L79">        String correlationId = CorrelationIdUtil.getCurrentCorrelationId();</span>
<span class="nc" id="L80">        String currentUserId = userContext.getCurrentUserId();</span>
        
<span class="nc" id="L82">        logger.info(&quot;Updating retention policy ID: {} [{}]&quot;, policyId, correlationId);</span>
        
<span class="nc" id="L84">        RetentionPolicy existingPolicy = retentionPolicyRepository.findById(policyId)</span>
<span class="nc" id="L85">                .orElseThrow(() -&gt; new ResourceNotFoundException(&quot;Retention policy not found with ID: &quot; + policyId));</span>
        
        // Check for duplicate name (excluding current policy)
<span class="nc bnc" id="L88" title="All 2 branches missed.">        if (retentionPolicyRepository.existsByNameAndIdNot(updatedPolicy.getName(), policyId)) {</span>
<span class="nc" id="L89">            throw new DuplicateResourceException(&quot;Retention policy with name '&quot; + updatedPolicy.getName() + &quot;' already exists&quot;);</span>
        }
        
        // Validate updated policy
<span class="nc" id="L93">        validateRetentionPolicy(updatedPolicy);</span>
        
        // Update fields
<span class="nc" id="L96">        existingPolicy.setName(updatedPolicy.getName());</span>
<span class="nc" id="L97">        existingPolicy.setDescription(updatedPolicy.getDescription());</span>
<span class="nc" id="L98">        existingPolicy.setScope(updatedPolicy.getScope());</span>
<span class="nc" id="L99">        existingPolicy.setRetentionPeriod(updatedPolicy.getRetentionPeriod());</span>
<span class="nc" id="L100">        existingPolicy.setRetentionPeriodUnit(updatedPolicy.getRetentionPeriodUnit());</span>
<span class="nc" id="L101">        existingPolicy.setDispositionAction(updatedPolicy.getDispositionAction());</span>
<span class="nc" id="L102">        existingPolicy.setIsActive(updatedPolicy.getIsActive());</span>
<span class="nc" id="L103">        existingPolicy.setAllowLegalHold(updatedPolicy.getAllowLegalHold());</span>
<span class="nc" id="L104">        existingPolicy.setAutoApply(updatedPolicy.getAutoApply());</span>
<span class="nc" id="L105">        existingPolicy.setPriority(updatedPolicy.getPriority());</span>
<span class="nc" id="L106">        existingPolicy.setTriggerEvent(updatedPolicy.getTriggerEvent());</span>
<span class="nc" id="L107">        existingPolicy.setBusinessJustification(updatedPolicy.getBusinessJustification());</span>
<span class="nc" id="L108">        existingPolicy.setLegalBasis(updatedPolicy.getLegalBasis());</span>
<span class="nc" id="L109">        existingPolicy.setReviewFrequencyMonths(updatedPolicy.getReviewFrequencyMonths());</span>
<span class="nc" id="L110">        existingPolicy.setNotificationBeforeDays(updatedPolicy.getNotificationBeforeDays());</span>
        
<span class="nc" id="L112">        RetentionPolicy savedPolicy = retentionPolicyRepository.save(existingPolicy);</span>
        
        // Audit log
<span class="nc" id="L115">        auditService.logAudit(AuditAction.RETENTION_POLICY_UPDATED, null, currentUserId,</span>
<span class="nc" id="L116">                String.format(&quot;Updated retention policy: %s (ID: %d)&quot;, savedPolicy.getName(), savedPolicy.getId()));</span>
        
<span class="nc" id="L118">        logger.info(&quot;Retention policy updated successfully: {} (ID: {}) [{}]&quot;, </span>
<span class="nc" id="L119">                savedPolicy.getName(), savedPolicy.getId(), correlationId);</span>
        
<span class="nc" id="L121">        return savedPolicy;</span>
    }
    
    /**
     * Delete a retention policy
     */
    public void deleteRetentionPolicy(Long policyId) {
<span class="nc" id="L128">        String correlationId = CorrelationIdUtil.getCurrentCorrelationId();</span>
<span class="nc" id="L129">        String currentUserId = userContext.getCurrentUserId();</span>
        
<span class="nc" id="L131">        logger.info(&quot;Deleting retention policy ID: {} [{}]&quot;, policyId, correlationId);</span>
        
<span class="nc" id="L133">        RetentionPolicy policy = retentionPolicyRepository.findById(policyId)</span>
<span class="nc" id="L134">                .orElseThrow(() -&gt; new ResourceNotFoundException(&quot;Retention policy not found with ID: &quot; + policyId));</span>
        
        // Deactivate assignments first
<span class="nc" id="L137">        assignmentRepository.deactivateByRetentionPolicyId(policyId);</span>
        
        // Soft delete by marking as inactive
<span class="nc" id="L140">        policy.setIsActive(false);</span>
<span class="nc" id="L141">        retentionPolicyRepository.save(policy);</span>
        
        // Audit log
<span class="nc" id="L144">        auditService.logAudit(AuditAction.RETENTION_POLICY_DELETED, null, currentUserId,</span>
<span class="nc" id="L145">                String.format(&quot;Deleted retention policy: %s (ID: %d)&quot;, policy.getName(), policy.getId()));</span>
        
<span class="nc" id="L147">        logger.info(&quot;Retention policy deleted successfully: {} (ID: {}) [{}]&quot;, </span>
<span class="nc" id="L148">                policy.getName(), policy.getId(), correlationId);</span>
<span class="nc" id="L149">    }</span>
    
    /**
     * Get retention policy by ID
     */
    @Transactional(readOnly = true)
    public RetentionPolicy getRetentionPolicy(Long policyId) {
<span class="nc" id="L156">        return retentionPolicyRepository.findById(policyId)</span>
<span class="nc" id="L157">                .orElseThrow(() -&gt; new ResourceNotFoundException(&quot;Retention policy not found with ID: &quot; + policyId));</span>
    }
    
    /**
     * Get all active retention policies
     */
    @Transactional(readOnly = true)
    public List&lt;RetentionPolicy&gt; getAllActiveRetentionPolicies() {
<span class="nc" id="L165">        return retentionPolicyRepository.findAllActive();</span>
    }
    
    /**
     * Get retention policies with pagination
     */
    @Transactional(readOnly = true)
    public Page&lt;RetentionPolicy&gt; getRetentionPolicies(Pageable pageable) {
<span class="nc" id="L173">        return retentionPolicyRepository.findAllActive(pageable);</span>
    }
    
    /**
     * Find the best matching retention policy for a document
     */
    @Transactional(readOnly = true)
    public Optional&lt;RetentionPolicy&gt; findBestMatchingPolicy(Document document) {
<span class="nc" id="L181">        String correlationId = CorrelationIdUtil.getCurrentCorrelationId();</span>
        
<span class="nc" id="L183">        logger.debug(&quot;Finding best matching retention policy for document ID: {} [{}]&quot;, document.getId(), correlationId);</span>
        
<span class="nc" id="L185">        List&lt;RetentionPolicyAssignment&gt; allAssignments = assignmentRepository.findAllActiveOrderedByPriority();</span>
        
<span class="nc bnc" id="L187" title="All 2 branches missed.">        for (RetentionPolicyAssignment assignment : allAssignments) {</span>
<span class="nc bnc" id="L188" title="All 2 branches missed.">            if (assignment.matches(document)) {</span>
<span class="nc" id="L189">                logger.debug(&quot;Found matching retention policy: {} for document ID: {} [{}]&quot;, </span>
<span class="nc" id="L190">                        assignment.getRetentionPolicy().getName(), document.getId(), correlationId);</span>
<span class="nc" id="L191">                return Optional.of(assignment.getRetentionPolicy());</span>
            }
<span class="nc" id="L193">        }</span>
        
<span class="nc" id="L195">        logger.debug(&quot;No matching retention policy found for document ID: {} [{}]&quot;, document.getId(), correlationId);</span>
<span class="nc" id="L196">        return Optional.empty();</span>
    }
    
    /**
     * Validate retention policy
     */
    private void validateRetentionPolicy(RetentionPolicy policy) {
<span class="nc bnc" id="L203" title="All 4 branches missed.">        if (policy.getName() == null || policy.getName().trim().isEmpty()) {</span>
<span class="nc" id="L204">            throw new IllegalArgumentException(&quot;Retention policy name is required&quot;);</span>
        }
        
<span class="nc bnc" id="L207" title="All 4 branches missed.">        if (policy.getRetentionPeriod() == null || policy.getRetentionPeriod() &lt;= 0) {</span>
<span class="nc" id="L208">            throw new IllegalArgumentException(&quot;Retention period must be greater than 0&quot;);</span>
        }
        
<span class="nc bnc" id="L211" title="All 2 branches missed.">        if (policy.getRetentionPeriodUnit() == null) {</span>
<span class="nc" id="L212">            throw new IllegalArgumentException(&quot;Retention period unit is required&quot;);</span>
        }
        
<span class="nc bnc" id="L215" title="All 2 branches missed.">        if (policy.getDispositionAction() == null) {</span>
<span class="nc" id="L216">            throw new IllegalArgumentException(&quot;Disposition action is required&quot;);</span>
        }
        
<span class="nc bnc" id="L219" title="All 2 branches missed.">        if (policy.getPriority() == null) {</span>
<span class="nc" id="L220">            policy.setPriority(0);</span>
        }
        
<span class="nc bnc" id="L223" title="All 2 branches missed.">        if (policy.getIsActive() == null) {</span>
<span class="nc" id="L224">            policy.setIsActive(true);</span>
        }
        
<span class="nc bnc" id="L227" title="All 2 branches missed.">        if (policy.getAllowLegalHold() == null) {</span>
<span class="nc" id="L228">            policy.setAllowLegalHold(true);</span>
        }
        
<span class="nc bnc" id="L231" title="All 2 branches missed.">        if (policy.getAutoApply() == null) {</span>
<span class="nc" id="L232">            policy.setAutoApply(false);</span>
        }
<span class="nc" id="L234">    }</span>
}
</pre><div class="footer"><span class="right">Created with <a href="http://www.jacoco.org/jacoco">JaCoCo</a> 0.8.10.202304240956</span></div></body></html>