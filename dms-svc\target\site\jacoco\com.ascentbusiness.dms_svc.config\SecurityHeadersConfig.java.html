<?xml version="1.0" encoding="UTF-8"?><!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Strict//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-strict.dtd"><html xmlns="http://www.w3.org/1999/xhtml" lang="en"><head><meta http-equiv="Content-Type" content="text/html;charset=UTF-8"/><link rel="stylesheet" href="../jacoco-resources/report.css" type="text/css"/><link rel="shortcut icon" href="../jacoco-resources/report.gif" type="image/gif"/><title>SecurityHeadersConfig.java</title><link rel="stylesheet" href="../jacoco-resources/prettify.css" type="text/css"/><script type="text/javascript" src="../jacoco-resources/prettify.js"></script></head><body onload="window['PR_TAB_WIDTH']=4;prettyPrint()"><div class="breadcrumb" id="breadcrumb"><span class="info"><a href="../jacoco-sessions.html" class="el_session">Sessions</a></span><a href="../index.html" class="el_report">dms-svc</a> &gt; <a href="index.source.html" class="el_package">com.ascentbusiness.dms_svc.config</a> &gt; <span class="el_source">SecurityHeadersConfig.java</span></div><h1>SecurityHeadersConfig.java</h1><pre class="source lang-java linenums">package com.ascentbusiness.dms_svc.config;

import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.security.web.header.writers.ReferrerPolicyHeaderWriter;
import org.springframework.security.web.header.writers.StaticHeadersWriter;

import java.util.HashMap;
import java.util.Map;

/**
 * Comprehensive security headers configuration for the DMS system
 * Implements industry-standard security headers for protection against various attacks
 */
@Configuration
<span class="fc" id="L17">public class SecurityHeadersConfig {</span>
    
    @Value(&quot;${dms.security.headers.csp.enabled:true}&quot;)
    private boolean cspEnabled;
    
    @Value(&quot;${dms.security.headers.csp.policy:default-src 'self'; script-src 'self' 'unsafe-inline' 'unsafe-eval'; style-src 'self' 'unsafe-inline'; img-src 'self' data: https:; font-src 'self' data:; connect-src 'self'; frame-ancestors 'none'; base-uri 'self'; form-action 'self'}&quot;)
    private String cspPolicy;
    
    @Value(&quot;${dms.security.headers.csp.report-only:false}&quot;)
    private boolean cspReportOnly;
    
    @Value(&quot;${dms.security.headers.hsts.enabled:true}&quot;)
    private boolean hstsEnabled;
    
    @Value(&quot;${dms.security.headers.hsts.max-age:31536000}&quot;)
    private long hstsMaxAge;
    
    @Value(&quot;${dms.security.headers.hsts.include-subdomains:true}&quot;)
    private boolean hstsIncludeSubdomains;
    
    @Value(&quot;${dms.security.headers.hsts.preload:true}&quot;)
    private boolean hstsPreload;
    
    @Value(&quot;${dms.security.headers.frame-options:DENY}&quot;)
    private String frameOptions;
    
    @Value(&quot;${dms.security.headers.content-type-options:nosniff}&quot;)
    private String contentTypeOptions;
    
    @Value(&quot;${dms.security.headers.referrer-policy:strict-origin-when-cross-origin}&quot;)
    private String referrerPolicy;
    
    @Value(&quot;${dms.security.headers.permissions-policy:geolocation=(), microphone=(), camera=(), payment=(), usb=(), magnetometer=(), gyroscope=(), speaker=(), vibrate=(), fullscreen=(self), sync-xhr=()}&quot;)
    private String permissionsPolicy;
    
    @Value(&quot;${dms.security.headers.cross-origin-embedder-policy:require-corp}&quot;)
    private String crossOriginEmbedderPolicy;
    
    @Value(&quot;${dms.security.headers.cross-origin-opener-policy:same-origin}&quot;)
    private String crossOriginOpenerPolicy;
    
    @Value(&quot;${dms.security.headers.cross-origin-resource-policy:same-origin}&quot;)
    private String crossOriginResourcePolicy;
    
    @Value(&quot;${dms.security.headers.expect-ct.enabled:false}&quot;)
    private boolean expectCtEnabled;
    
    @Value(&quot;${dms.security.headers.expect-ct.max-age:86400}&quot;)
    private long expectCtMaxAge;
    
    @Value(&quot;${dms.security.headers.expect-ct.enforce:false}&quot;)
    private boolean expectCtEnforce;
    
    @Value(&quot;${dms.security.headers.expect-ct.report-uri:}&quot;)
    private String expectCtReportUri;
    
    /**
     * Get Content Security Policy header configuration
     */
    public Map&lt;String, String&gt; getCSPHeaders() {
<span class="nc" id="L77">        Map&lt;String, String&gt; headers = new HashMap&lt;&gt;();</span>
        
<span class="nc bnc" id="L79" title="All 2 branches missed.">        if (cspEnabled) {</span>
<span class="nc bnc" id="L80" title="All 2 branches missed.">            String headerName = cspReportOnly ? &quot;Content-Security-Policy-Report-Only&quot; : &quot;Content-Security-Policy&quot;;</span>
<span class="nc" id="L81">            headers.put(headerName, cspPolicy);</span>
        }
        
<span class="nc" id="L84">        return headers;</span>
    }
    
    /**
     * Get HSTS header configuration
     */
    public Map&lt;String, String&gt; getHSTSHeaders() {
<span class="nc" id="L91">        Map&lt;String, String&gt; headers = new HashMap&lt;&gt;();</span>
        
<span class="nc bnc" id="L93" title="All 2 branches missed.">        if (hstsEnabled) {</span>
<span class="nc" id="L94">            StringBuilder hstsValue = new StringBuilder();</span>
<span class="nc" id="L95">            hstsValue.append(&quot;max-age=&quot;).append(hstsMaxAge);</span>
            
<span class="nc bnc" id="L97" title="All 2 branches missed.">            if (hstsIncludeSubdomains) {</span>
<span class="nc" id="L98">                hstsValue.append(&quot;; includeSubDomains&quot;);</span>
            }
            
<span class="nc bnc" id="L101" title="All 2 branches missed.">            if (hstsPreload) {</span>
<span class="nc" id="L102">                hstsValue.append(&quot;; preload&quot;);</span>
            }
            
<span class="nc" id="L105">            headers.put(&quot;Strict-Transport-Security&quot;, hstsValue.toString());</span>
        }
        
<span class="nc" id="L108">        return headers;</span>
    }
    
    /**
     * Get all security headers
     */
    public Map&lt;String, String&gt; getAllSecurityHeaders() {
<span class="nc" id="L115">        Map&lt;String, String&gt; headers = new HashMap&lt;&gt;();</span>
        
        // CSP headers
<span class="nc" id="L118">        headers.putAll(getCSPHeaders());</span>
        
        // HSTS headers
<span class="nc" id="L121">        headers.putAll(getHSTSHeaders());</span>
        
        // Frame options
<span class="nc" id="L124">        headers.put(&quot;X-Frame-Options&quot;, frameOptions);</span>
        
        // Content type options
<span class="nc" id="L127">        headers.put(&quot;X-Content-Type-Options&quot;, contentTypeOptions);</span>
        
        // Referrer policy
<span class="nc" id="L130">        headers.put(&quot;Referrer-Policy&quot;, referrerPolicy);</span>
        
        // Permissions policy
<span class="nc" id="L133">        headers.put(&quot;Permissions-Policy&quot;, permissionsPolicy);</span>
        
        // Cross-origin policies
<span class="nc" id="L136">        headers.put(&quot;Cross-Origin-Embedder-Policy&quot;, crossOriginEmbedderPolicy);</span>
<span class="nc" id="L137">        headers.put(&quot;Cross-Origin-Opener-Policy&quot;, crossOriginOpenerPolicy);</span>
<span class="nc" id="L138">        headers.put(&quot;Cross-Origin-Resource-Policy&quot;, crossOriginResourcePolicy);</span>
        
        // Expect-CT header
<span class="nc bnc" id="L141" title="All 2 branches missed.">        if (expectCtEnabled) {</span>
<span class="nc" id="L142">            StringBuilder expectCtValue = new StringBuilder();</span>
<span class="nc" id="L143">            expectCtValue.append(&quot;max-age=&quot;).append(expectCtMaxAge);</span>
            
<span class="nc bnc" id="L145" title="All 2 branches missed.">            if (expectCtEnforce) {</span>
<span class="nc" id="L146">                expectCtValue.append(&quot;, enforce&quot;);</span>
            }
            
<span class="nc bnc" id="L149" title="All 4 branches missed.">            if (expectCtReportUri != null &amp;&amp; !expectCtReportUri.trim().isEmpty()) {</span>
<span class="nc" id="L150">                expectCtValue.append(&quot;, report-uri=\&quot;&quot;).append(expectCtReportUri).append(&quot;\&quot;&quot;);</span>
            }
            
<span class="nc" id="L153">            headers.put(&quot;Expect-CT&quot;, expectCtValue.toString());</span>
        }
        
        // Additional security headers
<span class="nc" id="L157">        headers.put(&quot;X-XSS-Protection&quot;, &quot;1; mode=block&quot;);</span>
<span class="nc" id="L158">        headers.put(&quot;X-Download-Options&quot;, &quot;noopen&quot;);</span>
<span class="nc" id="L159">        headers.put(&quot;X-Permitted-Cross-Domain-Policies&quot;, &quot;none&quot;);</span>
<span class="nc" id="L160">        headers.put(&quot;Cache-Control&quot;, &quot;no-cache, no-store, must-revalidate&quot;);</span>
<span class="nc" id="L161">        headers.put(&quot;Pragma&quot;, &quot;no-cache&quot;);</span>
<span class="nc" id="L162">        headers.put(&quot;Expires&quot;, &quot;0&quot;);</span>
        
<span class="nc" id="L164">        return headers;</span>
    }
    
    /**
     * Get ReferrerPolicy enum value for Spring Security configuration
     */
    public ReferrerPolicyHeaderWriter.ReferrerPolicy getReferrerPolicyEnum() {
<span class="nc bnc" id="L171" title="All 9 branches missed.">        switch (referrerPolicy.toLowerCase()) {</span>
            case &quot;no-referrer&quot;:
<span class="nc" id="L173">                return ReferrerPolicyHeaderWriter.ReferrerPolicy.NO_REFERRER;</span>
            case &quot;no-referrer-when-downgrade&quot;:
<span class="nc" id="L175">                return ReferrerPolicyHeaderWriter.ReferrerPolicy.NO_REFERRER_WHEN_DOWNGRADE;</span>
            case &quot;origin&quot;:
<span class="nc" id="L177">                return ReferrerPolicyHeaderWriter.ReferrerPolicy.ORIGIN;</span>
            case &quot;origin-when-cross-origin&quot;:
<span class="nc" id="L179">                return ReferrerPolicyHeaderWriter.ReferrerPolicy.ORIGIN_WHEN_CROSS_ORIGIN;</span>
            case &quot;same-origin&quot;:
<span class="nc" id="L181">                return ReferrerPolicyHeaderWriter.ReferrerPolicy.SAME_ORIGIN;</span>
            case &quot;strict-origin&quot;:
<span class="nc" id="L183">                return ReferrerPolicyHeaderWriter.ReferrerPolicy.STRICT_ORIGIN;</span>
            case &quot;strict-origin-when-cross-origin&quot;:
<span class="nc" id="L185">                return ReferrerPolicyHeaderWriter.ReferrerPolicy.STRICT_ORIGIN_WHEN_CROSS_ORIGIN;</span>
            case &quot;unsafe-url&quot;:
<span class="nc" id="L187">                return ReferrerPolicyHeaderWriter.ReferrerPolicy.UNSAFE_URL;</span>
            default:
<span class="nc" id="L189">                return ReferrerPolicyHeaderWriter.ReferrerPolicy.STRICT_ORIGIN_WHEN_CROSS_ORIGIN;</span>
        }
    }
    
    /**
     * Create static headers writers for additional security headers
     */
    @Bean
    public StaticHeadersWriter additionalSecurityHeadersWriter() {
        // Permissions Policy - use default if null
<span class="pc bpc" id="L199" title="1 of 2 branches missed.">        String permissionsPolicyValue = permissionsPolicy != null ? permissionsPolicy : &quot;geolocation=(), microphone=(), camera=()&quot;;</span>
        
        // Cross-Origin policies - use defaults if null
<span class="pc bpc" id="L202" title="1 of 2 branches missed.">        String crossOriginEmbedderPolicyValue = crossOriginEmbedderPolicy != null ? crossOriginEmbedderPolicy : &quot;require-corp&quot;;</span>
<span class="pc bpc" id="L203" title="1 of 2 branches missed.">        String crossOriginOpenerPolicyValue = crossOriginOpenerPolicy != null ? crossOriginOpenerPolicy : &quot;same-origin&quot;;</span>
<span class="pc bpc" id="L204" title="1 of 2 branches missed.">        String crossOriginResourcePolicyValue = crossOriginResourcePolicy != null ? crossOriginResourcePolicy : &quot;same-origin&quot;;</span>
        
        // Build Expect-CT header if enabled
<span class="fc" id="L207">        String expectCtValue = null;</span>
<span class="pc bpc" id="L208" title="1 of 2 branches missed.">        if (expectCtEnabled) {</span>
<span class="nc" id="L209">            StringBuilder expectCtBuilder = new StringBuilder();</span>
<span class="nc" id="L210">            expectCtBuilder.append(&quot;max-age=&quot;).append(expectCtMaxAge);</span>
            
<span class="nc bnc" id="L212" title="All 2 branches missed.">            if (expectCtEnforce) {</span>
<span class="nc" id="L213">                expectCtBuilder.append(&quot;, enforce&quot;);</span>
            }
            
<span class="nc bnc" id="L216" title="All 4 branches missed.">            if (expectCtReportUri != null &amp;&amp; !expectCtReportUri.trim().isEmpty()) {</span>
<span class="nc" id="L217">                expectCtBuilder.append(&quot;, report-uri=\&quot;&quot;).append(expectCtReportUri).append(&quot;\&quot;&quot;);</span>
            }
            
<span class="nc" id="L220">            expectCtValue = expectCtBuilder.toString();</span>
        }
        
        // Create StaticHeadersWriter with conditional Expect-CT header
<span class="pc bpc" id="L224" title="3 of 4 branches missed.">        if (expectCtEnabled &amp;&amp; expectCtValue != null) {</span>
<span class="nc" id="L225">            return new StaticHeadersWriter(</span>
                &quot;Permissions-Policy&quot;, permissionsPolicyValue,
                &quot;Cross-Origin-Embedder-Policy&quot;, crossOriginEmbedderPolicyValue,
                &quot;Cross-Origin-Opener-Policy&quot;, crossOriginOpenerPolicyValue,
                &quot;Cross-Origin-Resource-Policy&quot;, crossOriginResourcePolicyValue,
                &quot;X-XSS-Protection&quot;, &quot;1; mode=block&quot;,
                &quot;X-Download-Options&quot;, &quot;noopen&quot;,
                &quot;X-Permitted-Cross-Domain-Policies&quot;, &quot;none&quot;,
                &quot;Expect-CT&quot;, expectCtValue
            );
        } else {
<span class="fc" id="L236">            return new StaticHeadersWriter(</span>
                &quot;Permissions-Policy&quot;, permissionsPolicyValue,
                &quot;Cross-Origin-Embedder-Policy&quot;, crossOriginEmbedderPolicyValue,
                &quot;Cross-Origin-Opener-Policy&quot;, crossOriginOpenerPolicyValue,
                &quot;Cross-Origin-Resource-Policy&quot;, crossOriginResourcePolicyValue,
                &quot;X-XSS-Protection&quot;, &quot;1; mode=block&quot;,
                &quot;X-Download-Options&quot;, &quot;noopen&quot;,
                &quot;X-Permitted-Cross-Domain-Policies&quot;, &quot;none&quot;
            );
        }
    }
    
    // Getters for configuration values
<span class="fc" id="L249">    public boolean isCspEnabled() { return cspEnabled; }</span>
<span class="nc" id="L250">    public String getCspPolicy() { return cspPolicy; }</span>
<span class="nc" id="L251">    public boolean isCspReportOnly() { return cspReportOnly; }</span>
<span class="fc" id="L252">    public boolean isHstsEnabled() { return hstsEnabled; }</span>
<span class="nc" id="L253">    public long getHstsMaxAge() { return hstsMaxAge; }</span>
<span class="nc" id="L254">    public boolean isHstsIncludeSubdomains() { return hstsIncludeSubdomains; }</span>
<span class="nc" id="L255">    public boolean isHstsPreload() { return hstsPreload; }</span>
<span class="fc" id="L256">    public String getFrameOptions() { return frameOptions; }</span>
<span class="nc" id="L257">    public String getContentTypeOptions() { return contentTypeOptions; }</span>
<span class="nc" id="L258">    public String getReferrerPolicy() { return referrerPolicy; }</span>
<span class="nc" id="L259">    public String getPermissionsPolicy() { return permissionsPolicy; }</span>
}
</pre><div class="footer"><span class="right">Created with <a href="http://www.jacoco.org/jacoco">JaCoCo</a> 0.8.10.202304240956</span></div></body></html>