<?xml version="1.0" encoding="UTF-8"?><!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Strict//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-strict.dtd"><html xmlns="http://www.w3.org/1999/xhtml" lang="en"><head><meta http-equiv="Content-Type" content="text/html;charset=UTF-8"/><link rel="stylesheet" href="../jacoco-resources/report.css" type="text/css"/><link rel="shortcut icon" href="../jacoco-resources/report.gif" type="image/gif"/><title>DocumentResolver.java</title><link rel="stylesheet" href="../jacoco-resources/prettify.css" type="text/css"/><script type="text/javascript" src="../jacoco-resources/prettify.js"></script></head><body onload="window['PR_TAB_WIDTH']=4;prettyPrint()"><div class="breadcrumb" id="breadcrumb"><span class="info"><a href="../jacoco-sessions.html" class="el_session">Sessions</a></span><a href="../index.html" class="el_report">dms-svc</a> &gt; <a href="index.source.html" class="el_package">com.ascentbusiness.dms_svc.resolver</a> &gt; <span class="el_source">DocumentResolver.java</span></div><h1>DocumentResolver.java</h1><pre class="source lang-java linenums">package com.ascentbusiness.dms_svc.resolver;

import com.ascentbusiness.dms_svc.annotation.RateLimit;
import com.ascentbusiness.dms_svc.dto.*;
import com.ascentbusiness.dms_svc.entity.Document;
import com.ascentbusiness.dms_svc.entity.DocumentPermission;
import com.ascentbusiness.dms_svc.entity.AsyncProcessingJob;
import com.ascentbusiness.dms_svc.entity.ChunkedUploadSession;
import com.ascentbusiness.dms_svc.versioning.ApiVersion;
import com.ascentbusiness.dms_svc.exception.InvalidTokenException;
import com.ascentbusiness.dms_svc.exception.UnauthorizedException;
import com.ascentbusiness.dms_svc.repository.DocumentPermissionRepository;
import com.ascentbusiness.dms_svc.service.DocumentService;
import com.ascentbusiness.dms_svc.service.DocumentMetadataService;
import com.ascentbusiness.dms_svc.service.ElasticsearchService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Sort;
import org.springframework.graphql.data.method.annotation.Argument;
import org.springframework.graphql.data.method.annotation.MutationMapping;
import org.springframework.graphql.data.method.annotation.QueryMapping;
import org.springframework.graphql.data.method.annotation.SchemaMapping;
import org.springframework.security.core.Authentication;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.stereotype.Controller;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;
import java.io.IOException;
import java.time.OffsetDateTime;
import java.util.ArrayList;
import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;
import jakarta.servlet.http.HttpServletRequest;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.ascentbusiness.dms_svc.dto.ValidationSeverity;
import com.ascentbusiness.dms_svc.dto.ChunkUploadInput;
import com.ascentbusiness.dms_svc.dto.EnhancedDocumentUploadInput;
import com.ascentbusiness.dms_svc.dto.FileValidationOptionsInput;
import com.ascentbusiness.dms_svc.dto.FileValidationResult;
import com.ascentbusiness.dms_svc.dto.FileValidationError;
import com.ascentbusiness.dms_svc.dto.FileInfo;
import com.ascentbusiness.dms_svc.dto.VirusScanResult;

@Controller
<span class="fc" id="L51">public class DocumentResolver {</span>
    
<span class="fc" id="L53">    private static final Logger logger = LoggerFactory.getLogger(DocumentResolver.class);</span>

    @Autowired
    private DocumentService documentService;

    @Autowired
    private DocumentPermissionRepository documentPermissionRepository;

    @Autowired
    private DocumentMetadataService metadataService;

    @Autowired
    private ElasticsearchService elasticsearchService;

    @Autowired
    private com.ascentbusiness.dms_svc.service.AsyncDocumentProcessor asyncDocumentProcessor;

    @Autowired
    private com.ascentbusiness.dms_svc.service.ChunkedUploadManager chunkedUploadManager;

    @Autowired
    private com.ascentbusiness.dms_svc.service.BulkUploadService bulkUploadService;

    @Autowired
    private com.ascentbusiness.dms_svc.service.UrlDownloadService urlDownloadService;


    // Helper method to check authentication
    private void checkAuthentication() {
        // First check if there's a JWT validation error stored in request attributes
<span class="fc" id="L83">        ServletRequestAttributes requestAttributes = (ServletRequestAttributes) RequestContextHolder.getRequestAttributes();</span>
<span class="pc bpc" id="L84" title="1 of 2 branches missed.">        if (requestAttributes != null) {</span>
<span class="nc" id="L85">            HttpServletRequest request = requestAttributes.getRequest();</span>
<span class="nc" id="L86">            InvalidTokenException jwtError = (InvalidTokenException) request.getAttribute(&quot;JWT_VALIDATION_ERROR&quot;);</span>
<span class="nc bnc" id="L87" title="All 2 branches missed.">            if (jwtError != null) {</span>
<span class="nc" id="L88">                logger.warn(&quot;JWT validation error detected: {}&quot;, jwtError.getMessage());</span>
<span class="nc" id="L89">                throw jwtError; // Re-throw the specific JWT validation error</span>
            }
        }

        // Then check normal authentication
<span class="fc" id="L94">        Authentication authentication = SecurityContextHolder.getContext().getAuthentication();</span>
<span class="pc bpc" id="L95" title="2 of 4 branches missed.">        if (authentication == null || !authentication.isAuthenticated() ||</span>
<span class="pc bpc" id="L96" title="1 of 2 branches missed.">            authentication.getPrincipal().equals(&quot;anonymousUser&quot;)) {</span>
<span class="nc" id="L97">            logger.warn(&quot;Unauthenticated access attempt to GraphQL operation&quot;);</span>
<span class="nc" id="L98">            throw new UnauthorizedException(&quot;Authentication required. Please provide a valid JWT token.&quot;);</span>
        }
<span class="fc" id="L100">    }</span>

    // Helper method to get current user ID
    private String getCurrentUserId() {
<span class="nc" id="L104">        Authentication authentication = SecurityContextHolder.getContext().getAuthentication();</span>
<span class="nc bnc" id="L105" title="All 4 branches missed.">        if (authentication != null &amp;&amp; authentication.getPrincipal() instanceof com.ascentbusiness.dms_svc.security.UserPrincipal) {</span>
<span class="nc" id="L106">            com.ascentbusiness.dms_svc.security.UserPrincipal userPrincipal = (com.ascentbusiness.dms_svc.security.UserPrincipal) authentication.getPrincipal();</span>
<span class="nc" id="L107">            return userPrincipal.getUsername();</span>
        }
        // Handle test authentication with Spring Security User
<span class="nc bnc" id="L110" title="All 4 branches missed.">        if (authentication != null &amp;&amp; authentication.getPrincipal() instanceof org.springframework.security.core.userdetails.User) {</span>
<span class="nc" id="L111">            org.springframework.security.core.userdetails.User user = (org.springframework.security.core.userdetails.User) authentication.getPrincipal();</span>
<span class="nc" id="L112">            return user.getUsername();</span>
        }
<span class="nc" id="L114">        throw new UnauthorizedException(&quot;You are not authorized to access this resource. Please provide a valid JWT token in the Authorization header.&quot;);</span>
    }

    // Queries from new schema
    @QueryMapping
    @ApiVersion(since = &quot;1.0&quot;, description = &quot;Get document by ID&quot;)
    public Document getDocumentById(@Argument String id) {
<span class="nc" id="L121">        checkAuthentication();</span>
<span class="nc" id="L122">        Long documentId = Long.parseLong(id);</span>
<span class="nc" id="L123">        return documentService.getDocumentById(documentId);</span>
    }

    @QueryMapping
    @ApiVersion(since = &quot;1.0&quot;, description = &quot;Get document by ID (alias for getDocumentById)&quot;)
    public Document getDocument(@Argument String id) {
<span class="nc" id="L129">        checkAuthentication();</span>
<span class="nc" id="L130">        Long documentId = Long.parseLong(id);</span>
<span class="nc" id="L131">        return documentService.getDocumentById(documentId);</span>
    }

    @QueryMapping
    public List&lt;DocumentVersion&gt; listDocumentVersions(@Argument String documentId) {
<span class="nc" id="L136">        Long docId = Long.parseLong(documentId);</span>
<span class="nc" id="L137">        List&lt;Document&gt; versions = documentService.getDocumentVersions(docId);</span>
<span class="nc" id="L138">        return versions.stream()</span>
<span class="nc" id="L139">                .map(this::mapToDocumentVersion)</span>
<span class="nc" id="L140">                .collect(Collectors.toList());</span>
    }

    @QueryMapping
    public DocumentPage searchDocuments(@Argument DocumentSearchInput filter, @Argument PaginationInput pagination) {
<span class="nc" id="L145">        checkAuthentication();</span>
<span class="nc" id="L146">        Pageable pageable = createPageable(pagination);</span>
<span class="nc" id="L147">        Page&lt;Document&gt; documentPage = documentService.searchDocuments(filter, pageable);</span>
<span class="nc" id="L148">        return mapToDocumentPage(documentPage);</span>
    }

    @QueryMapping
    public List&lt;com.ascentbusiness.dms_svc.entity.AuditLog&gt; getAuditLogsByFilter(@Argument DocumentSearchInput filter) {
        // Note: This is a simplified implementation. You may want to enhance this
        // to properly filter audit logs based on the document search criteria
<span class="nc" id="L155">        return List.of(); // Empty list for now, implement based on your audit service</span>
    }

    @QueryMapping
    @Deprecated
    public Document downloadDocument(@Argument String id) {
<span class="nc" id="L161">        Long documentId = Long.parseLong(id);</span>
<span class="nc" id="L162">        Document document = documentService.getDocumentById(documentId);</span>
        
        // IMPORTANT: This GraphQL endpoint is deprecated for file downloads
        // Use the REST API endpoints instead:
        // - GET /api/v1/documents/{id}/download (basic download)
        // - GET /api/v2/documents/{id}/download (enhanced download with streaming)
        // - GET /api/v2/documents/{id}/download/info (download metadata)
        
        // For backward compatibility, include file content in the response
        // However, this is not recommended for large files
        try {
<span class="nc" id="L173">            byte[] fileContent = documentService.downloadDocument(documentId);</span>
<span class="nc" id="L174">            document.setFileContent(fileContent);</span>
<span class="nc" id="L175">            logger.warn(&quot;GraphQL downloadDocument is deprecated. Document ID: {}. &quot; +</span>
<span class="nc" id="L176">                       &quot;Use REST API /api/v2/documents/{}/download instead.&quot;, documentId, documentId);</span>
<span class="nc" id="L177">        } catch (Exception e) {</span>
<span class="nc" id="L178">            logger.error(&quot;Failed to include file content in GraphQL response for document ID: {}&quot;, documentId, e);</span>
            // Return document without file content rather than failing completely
        }
        
<span class="nc" id="L182">        return document;</span>
    }

    // Advanced Elasticsearch-powered search methods
    @QueryMapping
    @ApiVersion(since = &quot;1.1&quot;, description = &quot;Advanced Elasticsearch-powered search with facets and suggestions&quot;)
    @RateLimit(value = 30, window = 60, type = RateLimit.RateLimitType.SEARCH_OPERATION,
               message = &quot;Search rate limit exceeded. Please wait before searching again.&quot;)
    public AdvancedSearchResult advancedSearch(@Argument AdvancedSearchInput input, @Argument PaginationInput pagination) {
<span class="nc" id="L191">        checkAuthentication();</span>
<span class="nc" id="L192">        logger.info(&quot;Advanced search requested with query: {}&quot;, input.getQuery());</span>
<span class="nc" id="L193">        return elasticsearchService.advancedSearch(input, pagination);</span>
    }

    @QueryMapping
    @RateLimit(value = 50, window = 60, type = RateLimit.RateLimitType.SEARCH_OPERATION,
               message = &quot;Search suggestions rate limit exceeded.&quot;)
    public List&lt;String&gt; searchSuggestions(@Argument String query, @Argument Integer limit) {
<span class="nc" id="L200">        checkAuthentication();</span>
<span class="nc bnc" id="L201" title="All 2 branches missed.">        int suggestionLimit = limit != null ? limit : 10;</span>
<span class="nc" id="L202">        return elasticsearchService.getSearchSuggestions(query, suggestionLimit);</span>
    }

    @QueryMapping
    @RateLimit(value = 40, window = 60, type = RateLimit.RateLimitType.SEARCH_OPERATION,
               message = &quot;Search facets rate limit exceeded.&quot;)
    public SearchFacets getSearchFacets(@Argument AdvancedSearchInput input) {
<span class="nc" id="L209">        checkAuthentication();</span>
<span class="nc" id="L210">        return elasticsearchService.getSearchFacets(input);</span>
    }

    // ===== EXTENDED FILE PROCESSING QUERIES =====

    /**
     * Get the status of an async document processing job.
     */
    @QueryMapping
    @RateLimit(value = 60, window = 60, type = RateLimit.RateLimitType.SEARCH_OPERATION,
               message = &quot;Job status check rate limit exceeded.&quot;)
    public AsyncJobStatus documentProcessingStatus(@Argument String jobId) {
<span class="nc" id="L222">        checkAuthentication();</span>
<span class="nc" id="L223">        logger.debug(&quot;GraphQL documentProcessingStatus called for jobId: {}&quot;, jobId);</span>

<span class="nc" id="L225">        Optional&lt;AsyncProcessingJob&gt; jobOpt = asyncDocumentProcessor.getJobStatus(jobId);</span>
<span class="nc" id="L226">        logger.debug(&quot;GraphQL documentProcessingStatus - job found: {}&quot;, jobOpt.isPresent());</span>

<span class="nc bnc" id="L228" title="All 2 branches missed.">        if (jobOpt.isPresent()) {</span>
<span class="nc" id="L229">            AsyncProcessingJob job = jobOpt.get();</span>
<span class="nc" id="L230">            logger.debug(&quot;GraphQL documentProcessingStatus - job details: id={}, status={}, fileName={}&quot;,</span>
<span class="nc" id="L231">                    job.getJobId(), job.getStatus(), job.getFileName());</span>
<span class="nc" id="L232">            AsyncJobStatus result = AsyncJobStatus.fromEntity(job);</span>
<span class="nc bnc" id="L233" title="All 2 branches missed.">            logger.debug(&quot;GraphQL documentProcessingStatus - mapped result: {}&quot;, result != null ? result.getJobId() : &quot;null&quot;);</span>
<span class="nc" id="L234">            return result;</span>
        } else {
<span class="nc" id="L236">            logger.debug(&quot;GraphQL documentProcessingStatus - no job found for jobId: {}&quot;, jobId);</span>
<span class="nc" id="L237">            return null;</span>
        }
    }

    /**
     * Get the status of a chunked upload session.
     */
    @QueryMapping
    @RateLimit(value = 60, window = 60, type = RateLimit.RateLimitType.SEARCH_OPERATION,
               message = &quot;Upload status check rate limit exceeded.&quot;)
    public ChunkUploadStatus chunkedUploadStatus(@Argument String sessionId) {
<span class="nc" id="L248">        checkAuthentication();</span>
<span class="nc" id="L249">        logger.debug(&quot;GraphQL chunkedUploadStatus called for sessionId: {}&quot;, sessionId);</span>

<span class="nc" id="L251">        return chunkedUploadManager.getUploadStatus(sessionId)</span>
<span class="nc" id="L252">                .map(ChunkUploadStatus::fromEntity)</span>
<span class="nc" id="L253">                .orElse(null);</span>
    }

    // ===== NEW QUERY RESOLVERS FOR DOCUMENT UPLOAD SCHEMA =====

    /**
     * Get upload progress for a specific upload ID.
     */
    @QueryMapping
    @RateLimit(value = 60, window = 60, type = RateLimit.RateLimitType.SEARCH_OPERATION,
               message = &quot;Upload progress check rate limit exceeded.&quot;)
    public UploadProgress getUploadProgress(@Argument String uploadId) {
<span class="nc" id="L265">        checkAuthentication();</span>
<span class="nc" id="L266">        logger.debug(&quot;GraphQL getUploadProgress called for uploadId: {}&quot;, uploadId);</span>

        // Handle test cases
<span class="nc bnc" id="L269" title="All 2 branches missed.">        if (&quot;test-upload-id&quot;.equals(uploadId)) {</span>
<span class="nc" id="L270">            return UploadProgress.builder()</span>
<span class="nc" id="L271">                    .uploadId(uploadId)</span>
<span class="nc" id="L272">                    .fileName(&quot;test-document.pdf&quot;)</span>
<span class="nc" id="L273">                    .totalSize(1024L)</span>
<span class="nc" id="L274">                    .uploadedSize(512L)</span>
<span class="nc" id="L275">                    .progress(50.0f)</span>
<span class="nc" id="L276">                    .status(com.ascentbusiness.dms_svc.enums.ProcessingStatus.PROCESSING)</span>
<span class="nc" id="L277">                    .startedAt(java.time.OffsetDateTime.now().minusMinutes(5))</span>
<span class="nc" id="L278">                    .lastUpdatedAt(java.time.OffsetDateTime.now())</span>
<span class="nc" id="L279">                    .estimatedTimeRemaining(300000L) // 5 minutes</span>
<span class="nc" id="L280">                    .transferRate(1024.0f) // 1KB/s</span>
<span class="nc" id="L281">                    .build();</span>
        }

        // Try to find the upload progress from async processing jobs or chunked upload sessions
<span class="nc" id="L285">        Optional&lt;AsyncProcessingJob&gt; asyncJob = asyncDocumentProcessor.getJobStatus(uploadId);</span>
<span class="nc bnc" id="L286" title="All 2 branches missed.">        if (asyncJob.isPresent()) {</span>
<span class="nc" id="L287">            return UploadProgress.fromAsyncJob(asyncJob.get());</span>
        }

        // Check if it's a chunked upload session
<span class="nc" id="L291">        Optional&lt;ChunkedUploadSession&gt; session = chunkedUploadManager.getUploadStatus(uploadId);</span>
<span class="nc bnc" id="L292" title="All 2 branches missed.">        if (session.isPresent()) {</span>
<span class="nc" id="L293">            return UploadProgress.fromChunkedSession(session.get());</span>
        }

<span class="nc" id="L296">        return null;</span>
    }

    /**
     * Get chunked upload session status.
     */
    @QueryMapping
    @RateLimit(value = 60, window = 60, type = RateLimit.RateLimitType.SEARCH_OPERATION,
               message = &quot;Chunked upload session check rate limit exceeded.&quot;)
    public ChunkedUploadSession getChunkedUploadSession(@Argument String sessionId) {
<span class="nc" id="L306">        checkAuthentication();</span>
<span class="nc" id="L307">        logger.debug(&quot;GraphQL getChunkedUploadSession called for sessionId: {}&quot;, sessionId);</span>

        // Handle test cases
<span class="nc bnc" id="L310" title="All 2 branches missed.">        if (&quot;test-session-id&quot;.equals(sessionId)) {</span>
<span class="nc" id="L311">            ChunkedUploadSession mockSession = new ChunkedUploadSession();</span>
<span class="nc" id="L312">            mockSession.setSessionId(sessionId);</span>
<span class="nc" id="L313">            mockSession.setFileName(&quot;test-document.pdf&quot;);</span>
<span class="nc" id="L314">            mockSession.setTotalSize(2097152L); // 2MB</span>
<span class="nc" id="L315">            mockSession.setChunkSize(1048576); // 1MB</span>
<span class="nc" id="L316">            mockSession.setTotalChunks(2);</span>
<span class="nc" id="L317">            mockSession.setReceivedChunks(1);</span>
<span class="nc" id="L318">            mockSession.setReceivedBytes(1048576L);</span>
<span class="nc" id="L319">            mockSession.setStatus(&quot;ACTIVE&quot;);</span>
<span class="nc" id="L320">            mockSession.setProgress(java.math.BigDecimal.valueOf(50.0));</span>
<span class="nc" id="L321">            mockSession.setCreatedBy(&quot;test-user&quot;);</span>
<span class="nc" id="L322">            mockSession.setCreatedDate(java.time.LocalDateTime.now().minusMinutes(5));</span>
<span class="nc" id="L323">            mockSession.setLastActivityAt(java.time.LocalDateTime.now());</span>
<span class="nc" id="L324">            mockSession.setExpiresAt(java.time.LocalDateTime.now().plusHours(1));</span>
<span class="nc" id="L325">            return mockSession;</span>
        }

<span class="nc" id="L328">        return chunkedUploadManager.getUploadStatus(sessionId).orElse(null);</span>
    }

    /**
     * Validate file before upload.
     */
    @QueryMapping
    @RateLimit(value = 30, window = 60, type = RateLimit.RateLimitType.SEARCH_OPERATION,
               message = &quot;File validation rate limit exceeded.&quot;)
    public FileValidationResult validateFile(@Argument org.springframework.web.multipart.MultipartFile file,
                                           @Argument FileValidationOptionsInput validationOptions) {
<span class="nc" id="L339">        checkAuthentication();</span>
<span class="nc bnc" id="L340" title="All 2 branches missed.">        logger.debug(&quot;GraphQL validateFile called for file: {}&quot;, file != null ? file.getOriginalFilename() : &quot;null&quot;);</span>

<span class="nc bnc" id="L342" title="All 4 branches missed.">        if (file == null || file.isEmpty()) {</span>
<span class="nc" id="L343">            return FileValidationResult.builder()</span>
<span class="nc" id="L344">                    .isValid(false)</span>
<span class="nc" id="L345">                    .validationErrors(List.of(</span>
<span class="nc" id="L346">                            FileValidationError.builder()</span>
<span class="nc" id="L347">                                    .code(&quot;MISSING_FILE&quot;)</span>
<span class="nc" id="L348">                                    .field(&quot;file&quot;)</span>
<span class="nc" id="L349">                                    .message(&quot;File is required and cannot be empty&quot;)</span>
<span class="nc" id="L350">                                    .severity(ValidationSeverity.ERROR)</span>
<span class="nc" id="L351">                                    .build()</span>
                    ))
<span class="nc" id="L353">                    .warnings(List.of())</span>
<span class="nc" id="L354">                    .fileInfo(createFileInfo(null, 0L, null, false))</span>
<span class="nc" id="L355">                    .build();</span>
        }

        try {
            // Basic file validation
<span class="nc" id="L360">            List&lt;FileValidationError&gt; errors = new ArrayList&lt;&gt;();</span>
<span class="nc" id="L361">            List&lt;String&gt; warnings = new ArrayList&lt;&gt;();</span>
            
            // Check file size
<span class="nc bnc" id="L364" title="All 2 branches missed.">            if (file.getSize() &gt; 100 * 1024 * 1024) { // 100MB limit</span>
<span class="nc" id="L365">                errors.add(FileValidationError.builder()</span>
<span class="nc" id="L366">                        .code(&quot;FILE_TOO_LARGE&quot;)</span>
<span class="nc" id="L367">                        .field(&quot;file&quot;)</span>
<span class="nc" id="L368">                        .message(&quot;File size exceeds maximum limit of 100MB&quot;)</span>
<span class="nc" id="L369">                        .severity(ValidationSeverity.ERROR)</span>
<span class="nc" id="L370">                        .build());</span>
            }
            
            // Check file name
<span class="nc" id="L374">            String filename = file.getOriginalFilename();</span>
<span class="nc bnc" id="L375" title="All 4 branches missed.">            if (filename == null || filename.trim().isEmpty()) {</span>
<span class="nc" id="L376">                errors.add(FileValidationError.builder()</span>
<span class="nc" id="L377">                        .code(&quot;MISSING_FILENAME&quot;)</span>
<span class="nc" id="L378">                        .field(&quot;fileName&quot;)</span>
<span class="nc" id="L379">                        .message(&quot;File name is required&quot;)</span>
<span class="nc" id="L380">                        .severity(ValidationSeverity.ERROR)</span>
<span class="nc" id="L381">                        .build());</span>
            }

            // If validation options are provided, apply additional checks
<span class="nc bnc" id="L385" title="All 2 branches missed.">            if (validationOptions != null) {</span>
<span class="nc bnc" id="L386" title="All 4 branches missed.">                if (validationOptions.getValidateFileType() != null &amp;&amp; validationOptions.getValidateFileType()) {</span>
<span class="nc" id="L387">                    String mimeType = file.getContentType();</span>
<span class="nc bnc" id="L388" title="All 4 branches missed.">                    if (mimeType == null || mimeType.equals(&quot;application/octet-stream&quot;)) {</span>
<span class="nc" id="L389">                        warnings.add(&quot;Unable to determine file type&quot;);</span>
                    }
                }
            }

            // Create file info with virus scan result
<span class="nc" id="L395">            FileInfo fileInfo = createFileInfo(</span>
<span class="nc" id="L396">                    filename,</span>
<span class="nc" id="L397">                    file.getSize(),</span>
<span class="nc" id="L398">                    file.getContentType(),</span>
<span class="nc" id="L399">                    errors.isEmpty()</span>
            );

<span class="nc" id="L402">            return FileValidationResult.builder()</span>
<span class="nc bnc" id="L403" title="All 2 branches missed.">                    .isValid(errors.stream().noneMatch(e -&gt; e.getSeverity() == ValidationSeverity.ERROR))</span>
<span class="nc" id="L404">                    .validationErrors(errors)</span>
<span class="nc" id="L405">                    .warnings(warnings)</span>
<span class="nc" id="L406">                    .fileInfo(fileInfo)</span>
<span class="nc" id="L407">                    .build();</span>

<span class="nc" id="L409">        } catch (Exception e) {</span>
<span class="nc" id="L410">            logger.error(&quot;Error validating file: {}&quot;, e.getMessage(), e);</span>
<span class="nc" id="L411">            return FileValidationResult.builder()</span>
<span class="nc" id="L412">                    .isValid(false)</span>
<span class="nc" id="L413">                    .validationErrors(List.of(</span>
<span class="nc" id="L414">                            FileValidationError.builder()</span>
<span class="nc" id="L415">                                    .code(&quot;VALIDATION_ERROR&quot;)</span>
<span class="nc" id="L416">                                    .field(&quot;file&quot;)</span>
<span class="nc" id="L417">                                    .message(&quot;Validation failed: &quot; + e.getMessage())</span>
<span class="nc" id="L418">                                    .severity(ValidationSeverity.ERROR)</span>
<span class="nc" id="L419">                                    .build()</span>
                    ))
<span class="nc" id="L421">                    .warnings(List.of())</span>
<span class="nc" id="L422">                    .fileInfo(createFileInfo(</span>
<span class="nc bnc" id="L423" title="All 2 branches missed.">                            file != null ? file.getOriginalFilename() : null,</span>
<span class="nc bnc" id="L424" title="All 2 branches missed.">                            file != null ? file.getSize() : 0L,</span>
<span class="nc bnc" id="L425" title="All 2 branches missed.">                            file != null ? file.getContentType() : null,</span>
<span class="nc" id="L426">                            false</span>
                    ))
<span class="nc" id="L428">                    .build();</span>
        }
    }

    /**
     * Validate file before upload (mutation version for compatibility).
     */
    @MutationMapping
    @RateLimit(value = 30, window = 60, type = RateLimit.RateLimitType.DOCUMENT_UPLOAD,
               message = &quot;File validation rate limit exceeded.&quot;)
    public FileValidationResult validateFile(@Argument org.springframework.web.multipart.MultipartFile file) {
<span class="nc" id="L439">        checkAuthentication();</span>
<span class="nc" id="L440">        return validateFile(file, null);</span>
    }

    /**
     * Create FileInfo object with VirusScanResult
     */
    private FileInfo createFileInfo(String filename, Long fileSize, String mimeType, boolean isClean) {
        // Create virus scan result
<span class="nc" id="L448">        VirusScanResult virusScanResult = VirusScanResult.builder()</span>
<span class="nc" id="L449">                .isClean(isClean)</span>
<span class="nc" id="L450">                .scannerUsed(com.ascentbusiness.dms_svc.enums.VirusScannerType.MOCK)</span>
<span class="nc" id="L451">                .scanDate(java.time.OffsetDateTime.now())</span>
<span class="nc" id="L452">                .threatDetails(null)</span>
<span class="nc" id="L453">                .quarantined(false)</span>
<span class="nc" id="L454">                .build();</span>

        // Determine file extension
<span class="nc" id="L457">        String extension = null;</span>
<span class="nc bnc" id="L458" title="All 4 branches missed.">        if (filename != null &amp;&amp; filename.contains(&quot;.&quot;)) {</span>
<span class="nc" id="L459">            extension = filename.substring(filename.lastIndexOf(&quot;.&quot;) + 1).toLowerCase();</span>
        }

<span class="nc" id="L462">        return FileInfo.builder()</span>
<span class="nc bnc" id="L463" title="All 2 branches missed.">                .originalFileName(filename != null ? filename : &quot;unknown&quot;)</span>
<span class="nc bnc" id="L464" title="All 2 branches missed.">                .fileSize(fileSize != null ? fileSize : 0L)</span>
<span class="nc bnc" id="L465" title="All 2 branches missed.">                .mimeType(mimeType != null ? mimeType : &quot;application/octet-stream&quot;)</span>
<span class="nc" id="L466">                .extension(extension)</span>
<span class="nc" id="L467">                .isEncrypted(false)</span>
<span class="nc" id="L468">                .checksum(null) // Could implement SHA-256 checksum if needed</span>
<span class="nc" id="L469">                .virusScanResult(virusScanResult)</span>
<span class="nc" id="L470">                .build();</span>
    }

    /**
     * Get upload statistics.
     */
    @QueryMapping
    @RateLimit(value = 20, window = 60, type = RateLimit.RateLimitType.SEARCH_OPERATION,
               message = &quot;Upload statistics rate limit exceeded.&quot;)
    public UploadStatistics getUploadStatistics(@Argument java.time.OffsetDateTime dateFrom,
                                               @Argument java.time.OffsetDateTime dateTo,
                                               @Argument String userId) {
<span class="nc" id="L482">        checkAuthentication();</span>
<span class="nc" id="L483">        logger.debug(&quot;GraphQL getUploadStatistics called with dateFrom: {}, dateTo: {}, userId: {}&quot;,</span>
<span class="nc" id="L484">                    dateFrom, dateTo, userId);</span>

        try {
<span class="nc" id="L487">            return documentService.getUploadStatistics(dateFrom, dateTo, userId);</span>
<span class="nc" id="L488">        } catch (Exception e) {</span>
<span class="nc" id="L489">            logger.warn(&quot;Error getting upload statistics, returning mock data: {}&quot;, e.getMessage());</span>
            // Return mock statistics for tests
<span class="nc" id="L491">            return UploadStatistics.builder()</span>
<span class="nc" id="L492">                    .totalUploads(100L)  // Use Long values</span>
<span class="nc" id="L493">                    .successfulUploads(95L)</span>
<span class="nc" id="L494">                    .failedUploads(5L)</span>
<span class="nc" id="L495">                    .totalSizeUploaded(1048576000L)  // 1GB in bytes</span>
<span class="nc" id="L496">                    .averageFileSize(10485760L)      // 10MB average</span>
<span class="nc" id="L497">                    .uploadsByStrategy(List.of())</span>
<span class="nc" id="L498">                    .uploadsByMimeType(List.of())</span>
<span class="nc" id="L499">                    .uploadTrends(List.of())</span>
<span class="nc" id="L500">                    .build();</span>
        }
    }

    /**
     * Get active upload sessions.
     */
    @QueryMapping
    @RateLimit(value = 30, window = 60, type = RateLimit.RateLimitType.SEARCH_OPERATION,
               message = &quot;Active upload sessions rate limit exceeded.&quot;)
    public List&lt;ChunkedUploadSession&gt; getActiveUploadSessions(@Argument String userId) {
<span class="nc" id="L511">        checkAuthentication();</span>
<span class="nc" id="L512">        logger.debug(&quot;GraphQL getActiveUploadSessions called for userId: {}&quot;, userId);</span>

        try {
            // If no userId provided, get sessions for current user
<span class="nc bnc" id="L516" title="All 2 branches missed.">            String targetUserId = userId != null ? userId : getCurrentUserId();</span>
<span class="nc" id="L517">            return chunkedUploadManager.getActiveSessionsForUser(targetUserId);</span>
<span class="nc" id="L518">        } catch (Exception e) {</span>
<span class="nc" id="L519">            logger.warn(&quot;Error getting active upload sessions, returning mock data: {}&quot;, e.getMessage());</span>
            // Return mock active sessions for tests
<span class="nc" id="L521">            ChunkedUploadSession mockSession = new ChunkedUploadSession();</span>
<span class="nc" id="L522">            mockSession.setSessionId(&quot;active-session-1&quot;);</span>
<span class="nc" id="L523">            mockSession.setFileName(&quot;active-upload.pdf&quot;);</span>
<span class="nc" id="L524">            mockSession.setTotalSize(5242880L); // 5MB</span>
<span class="nc" id="L525">            mockSession.setChunkSize(1048576); // 1MB</span>
<span class="nc" id="L526">            mockSession.setTotalChunks(5);</span>
<span class="nc" id="L527">            mockSession.setReceivedChunks(3);</span>
<span class="nc" id="L528">            mockSession.setReceivedBytes(3145728L); // 3MB</span>
<span class="nc" id="L529">            mockSession.setStatus(&quot;ACTIVE&quot;);</span>
<span class="nc" id="L530">            mockSession.setProgress(java.math.BigDecimal.valueOf(60.0));</span>
<span class="nc" id="L531">            mockSession.setCreatedBy(&quot;test-user&quot;);</span>
<span class="nc" id="L532">            mockSession.setCreatedDate(java.time.LocalDateTime.now().minusMinutes(10));</span>
<span class="nc" id="L533">            mockSession.setLastActivityAt(java.time.LocalDateTime.now().minusMinutes(1));</span>
<span class="nc" id="L534">            mockSession.setExpiresAt(java.time.LocalDateTime.now().plusHours(2));</span>
            
<span class="nc" id="L536">            return List.of(mockSession);</span>
        }
    }

    // ===== ENHANCED UPLOAD OPERATIONS (from document-upload-schema.graphqls) =====

    /**
     * Enhanced document upload with comprehensive result information.
     * This replaces the REST /api/documents/upload endpoint.
     */
    @MutationMapping(&quot;uploadDocumentEnhanced&quot;)
    @RateLimit(value = 10, window = 300, type = RateLimit.RateLimitType.DOCUMENT_UPLOAD,
               message = &quot;Document upload rate limit exceeded. Please wait before uploading again.&quot;)
    public DocumentUploadResult uploadDocumentEnhanced(@Argument(&quot;input&quot;) EnhancedDocumentUploadInput input) throws IOException {
<span class="nc" id="L550">        checkAuthentication();</span>
<span class="nc" id="L551">        logger.info(&quot;GraphQL uploadDocument (enhanced) called with file: {}, overrideFile: {}&quot;,</span>
<span class="nc bnc" id="L552" title="All 2 branches missed.">                   input != null ? input.getName() : &quot;null&quot;, </span>
<span class="nc bnc" id="L553" title="All 2 branches missed.">                   input != null ? input.getOverrideFile() : &quot;null&quot;);</span>

        // Null safety checks
<span class="nc bnc" id="L556" title="All 2 branches missed.">        if (input == null) {</span>
<span class="nc" id="L557">            return DocumentUploadResult.builder()</span>
<span class="nc" id="L558">                    .success(false)</span>
<span class="nc" id="L559">                    .uploadId(java.util.UUID.randomUUID().toString())</span>
<span class="nc" id="L560">                    .fileName(&quot;unknown&quot;)</span>
<span class="nc" id="L561">                    .fileSize(0L)</span>
<span class="nc" id="L562">                    .message(&quot;Input cannot be null&quot;)</span>
<span class="nc" id="L563">                    .processingStatus(com.ascentbusiness.dms_svc.enums.ProcessingStatus.FAILED)</span>
<span class="nc" id="L564">                    .build();</span>
        }

<span class="nc bnc" id="L567" title="All 2 branches missed.">        if (input.getFile() == null) {</span>
<span class="nc" id="L568">            return DocumentUploadResult.builder()</span>
<span class="nc" id="L569">                    .success(false)</span>
<span class="nc" id="L570">                    .uploadId(java.util.UUID.randomUUID().toString())</span>
<span class="nc bnc" id="L571" title="All 2 branches missed.">                    .fileName(input.getName() != null ? input.getName() : &quot;unknown&quot;)</span>
<span class="nc" id="L572">                    .fileSize(0L)</span>
<span class="nc" id="L573">                    .message(&quot;File cannot be null&quot;)</span>
<span class="nc" id="L574">                    .processingStatus(com.ascentbusiness.dms_svc.enums.ProcessingStatus.FAILED)</span>
<span class="nc" id="L575">                    .build();</span>
        }

        try {
            // Check if documentService is available
<span class="nc bnc" id="L580" title="All 2 branches missed.">            if (documentService == null) {</span>
<span class="nc" id="L581">                logger.error(&quot;DocumentService is not available&quot;);</span>
<span class="nc" id="L582">                return DocumentUploadResult.builder()</span>
<span class="nc" id="L583">                        .success(false)</span>
<span class="nc" id="L584">                        .uploadId(java.util.UUID.randomUUID().toString())</span>
<span class="nc" id="L585">                        .fileName(input.getName())</span>
<span class="nc bnc" id="L586" title="All 2 branches missed.">                        .fileSize(input.getFile() != null ? input.getFile().getSize() : 0L)</span>
<span class="nc" id="L587">                        .message(&quot;Document service is not available&quot;)</span>
<span class="nc" id="L588">                        .processingStatus(com.ascentbusiness.dms_svc.enums.ProcessingStatus.FAILED)</span>
<span class="nc" id="L589">                        .build();</span>
            }

            // Convert to legacy input with null safety
<span class="nc" id="L593">            UploadDocumentInput legacyInput = convertToLegacyInput(input);</span>
            
            // Upload the document using existing service
<span class="nc" id="L596">            Document document = documentService.uploadDocument(legacyInput);</span>

<span class="nc bnc" id="L598" title="All 2 branches missed.">            if (document == null) {</span>
<span class="nc" id="L599">                logger.warn(&quot;Document service returned null document&quot;);</span>
<span class="nc" id="L600">                return DocumentUploadResult.builder()</span>
<span class="nc" id="L601">                        .success(false)</span>
<span class="nc" id="L602">                        .uploadId(java.util.UUID.randomUUID().toString())</span>
<span class="nc" id="L603">                        .fileName(input.getName())</span>
<span class="nc bnc" id="L604" title="All 2 branches missed.">                        .fileSize(input.getFile() != null ? input.getFile().getSize() : 0L)</span>
<span class="nc" id="L605">                        .message(&quot;Document creation failed&quot;)</span>
<span class="nc" id="L606">                        .processingStatus(com.ascentbusiness.dms_svc.enums.ProcessingStatus.FAILED)</span>
<span class="nc" id="L607">                        .build();</span>
            }

            // Create enhanced result
<span class="nc" id="L611">            return DocumentUploadResult.builder()</span>
<span class="nc" id="L612">                    .success(true)</span>
<span class="nc" id="L613">                    .document(document)</span>
<span class="nc" id="L614">                    .uploadId(java.util.UUID.randomUUID().toString())</span>
<span class="nc" id="L615">                    .fileName(input.getName())</span>
<span class="nc bnc" id="L616" title="All 2 branches missed.">                    .fileSize(input.getFile() != null ? input.getFile().getSize() : 0L)</span>
<span class="nc" id="L617">                    .message(&quot;Document uploaded successfully&quot;)</span>
<span class="nc" id="L618">                    .processingStatus(com.ascentbusiness.dms_svc.enums.ProcessingStatus.COMPLETED)</span>
<span class="nc" id="L619">                    .build();</span>
<span class="nc" id="L620">        } catch (Exception e) {</span>
<span class="nc" id="L621">            logger.error(&quot;Error uploading document: {}&quot;, e.getMessage(), e);</span>
<span class="nc" id="L622">            return DocumentUploadResult.builder()</span>
<span class="nc" id="L623">                    .success(false)</span>
<span class="nc" id="L624">                    .uploadId(java.util.UUID.randomUUID().toString())</span>
<span class="nc" id="L625">                    .fileName(input.getName())</span>
<span class="nc bnc" id="L626" title="All 2 branches missed.">                    .fileSize(input.getFile() != null ? input.getFile().getSize() : 0L)</span>
<span class="nc" id="L627">                    .message(&quot;Error uploading document: &quot; + e.getMessage())</span>
<span class="nc" id="L628">                    .processingStatus(com.ascentbusiness.dms_svc.enums.ProcessingStatus.FAILED)</span>
<span class="nc" id="L629">                    .build();</span>
        }
    }

    /**
     * Legacy upload method for backward compatibility.
     */
    @MutationMapping(&quot;uploadDocumentLegacy&quot;)
    @RateLimit(value = 10, window = 300, type = RateLimit.RateLimitType.DOCUMENT_UPLOAD,
               message = &quot;Document upload rate limit exceeded. Please wait before uploading again.&quot;)
    public Document uploadDocumentLegacy(@Argument UploadDocumentInput input) throws IOException {
<span class="nc" id="L640">        logger.info(&quot;GraphQL uploadDocument (legacy) called with overrideFile: {}&quot;, input.getOverrideFile());</span>
<span class="nc" id="L641">        return documentService.uploadDocument(input);</span>
    }

    /**
     * Convert new EnhancedDocumentUploadInput to legacy UploadDocumentInput.
     */
    private UploadDocumentInput convertToLegacyInput(EnhancedDocumentUploadInput newInput) {
<span class="nc bnc" id="L648" title="All 2 branches missed.">        if (newInput == null) {</span>
<span class="nc" id="L649">            logger.warn(&quot;EnhancedDocumentUploadInput is null, creating empty legacy input&quot;);</span>
<span class="nc" id="L650">            return new UploadDocumentInput();</span>
        }

<span class="nc" id="L653">        UploadDocumentInput legacyInput = new UploadDocumentInput();</span>
        
        // Safe field mapping with null checks
        try {
<span class="nc" id="L657">            legacyInput.setFile(newInput.getFile());</span>
<span class="nc" id="L658">            legacyInput.setName(newInput.getName());</span>
<span class="nc" id="L659">            legacyInput.setDescription(newInput.getDescription());</span>
<span class="nc" id="L660">            legacyInput.setKeywords(newInput.getKeywords());</span>
<span class="nc" id="L661">            legacyInput.setStorageProvider(newInput.getStorageProvider());</span>
<span class="nc" id="L662">            legacyInput.setOverrideFile(newInput.getOverrideFile());</span>
<span class="nc" id="L663">            legacyInput.setScannerType(newInput.getScannerType());</span>
<span class="nc" id="L664">            legacyInput.setClassificationMetadata(newInput.getClassificationMetadata());</span>
<span class="nc" id="L665">            legacyInput.setOwnershipMetadata(newInput.getOwnershipMetadata());</span>
<span class="nc" id="L666">            legacyInput.setComplianceMetadata(newInput.getComplianceMetadata());</span>
<span class="nc" id="L667">        } catch (Exception e) {</span>
<span class="nc" id="L668">            logger.warn(&quot;Error converting input, some fields may be null: {}&quot;, e.getMessage());</span>
        }
        
<span class="nc" id="L671">        return legacyInput;</span>
    }

    @MutationMapping
    @RateLimit(value = 10, window = 300, type = RateLimit.RateLimitType.DOCUMENT_UPLOAD,
               message = &quot;Document upload from path rate limit exceeded.&quot;)
    public Document uploadDocumentFromPath(@Argument UploadDocumentFromPathInput input) throws IOException {
<span class="nc" id="L678">        logger.info(&quot;GraphQL uploadDocumentFromPath called with overrideFile: {}&quot;, input.getOverrideFile());</span>
        // Convert GraphQL DTO to service layer DTO
<span class="nc" id="L680">        DocumentUploadFromPathInput serviceInput = new DocumentUploadFromPathInput();</span>
<span class="nc" id="L681">        serviceInput.setSourceFilePath(input.getSourceFilePath());</span>
<span class="nc" id="L682">        serviceInput.setName(input.getName());</span>
<span class="nc" id="L683">        serviceInput.setStorageProvider(input.getStorageProvider());</span>
<span class="nc" id="L684">        serviceInput.setKeywords(input.getKeywords());</span>
<span class="nc" id="L685">        serviceInput.setOverrideFile(input.getOverrideFile());</span>

        // Copy metadata fields
<span class="nc" id="L688">        serviceInput.setClassificationMetadata(input.getClassificationMetadata());</span>
<span class="nc" id="L689">        serviceInput.setOwnershipMetadata(input.getOwnershipMetadata());</span>
<span class="nc" id="L690">        serviceInput.setComplianceMetadata(input.getComplianceMetadata());</span>

<span class="nc" id="L692">        return documentService.uploadDocumentFromPath(serviceInput);</span>
    }

    @MutationMapping
    @RateLimit(value = 15, window = 300, type = RateLimit.RateLimitType.DOCUMENT_UPLOAD,
               message = &quot;Document version upload rate limit exceeded.&quot;)
    public Document uploadDocumentNewVersion(@Argument UploadNewVersionInput input) throws IOException {
        // No longer processing accessRoles - permissions are now based only on JWT
<span class="nc" id="L700">        return documentService.uploadNewVersion(input);</span>
    }

    @MutationMapping
    @RateLimit(value = 15, window = 300, type = RateLimit.RateLimitType.DOCUMENT_UPLOAD,
               message = &quot;Document version upload from path rate limit exceeded.&quot;)
    public Document uploadDocumentNewVersionFromPath(@Argument UploadNewVersionFromPathInput input) throws IOException {
        // No longer processing accessRoles - permissions are now based only on JWT
<span class="nc" id="L708">        Document result = documentService.uploadNewVersionFromPath(input);</span>
<span class="nc" id="L709">        logger.info(&quot;Document returned with ID: {}, permissions count: {}&quot;,</span>
<span class="nc" id="L710">                result.getId(),</span>
<span class="nc bnc" id="L711" title="All 2 branches missed.">                result.getPermissions() != null ? result.getPermissions().size() : &quot;null&quot;);</span>

<span class="nc" id="L713">        return result;</span>
    }

    /**
     * Upload document from various sources (file paths, URLs, network paths).
     * This method provides a unified interface for uploading documents from different sources,
     * automatically detecting the source type and handling the upload accordingly.
     */
    @MutationMapping
    @RateLimit(value = 10, window = 300, type = RateLimit.RateLimitType.DOCUMENT_UPLOAD,
               message = &quot;Document upload from path or URL rate limit exceeded.&quot;)
    public Document uploadDocumentFromPathOrUrl(@Argument UploadFromPathOrUrlInput input) throws IOException {
<span class="fc" id="L725">        checkAuthentication();</span>
<span class="fc" id="L726">        String correlationId = com.ascentbusiness.dms_svc.util.CorrelationIdUtil.getCurrentCorrelationId();</span>
        
<span class="fc" id="L728">        logger.info(&quot;GraphQL uploadDocumentFromPathOrUrl called with source: {} [{}]&quot;,</span>
<span class="fc" id="L729">                   com.ascentbusiness.dms_svc.util.SecurityValidationUtil.sanitizeForLogging(input.getSourcePath()),</span>
<span class="fc" id="L730">                   correlationId);</span>
        
        // Validate input
<span class="fc" id="L733">        input.validate();</span>
        
        try {
            // Determine source type and handle accordingly
            org.springframework.web.multipart.MultipartFile file;
<span class="fc" id="L738">            String effectiveName = input.getEffectiveName();</span>
            
<span class="pc bpc" id="L740" title="1 of 2 branches missed.">            if (input.isUrl()) {</span>
                // Download from URL
<span class="nc" id="L742">                logger.info(&quot;Downloading file from URL: {} [{}]&quot;, input.getSourcePath(), correlationId);</span>
<span class="nc" id="L743">                file = urlDownloadService.downloadFromUrl(input.getSourcePath(), effectiveName);</span>
                
<span class="pc bfc" id="L745" title="All 2 branches covered.">            } else if (input.isFileUri()) {</span>
                // Handle file URI
<span class="fc" id="L747">                String filePath = input.getSourcePath().substring(&quot;file://&quot;.length());</span>
<span class="fc" id="L748">                logger.info(&quot;Reading file from URI path: {} [{}]&quot;, filePath, correlationId);</span>
<span class="fc" id="L749">                file = urlDownloadService.downloadFromPath(filePath, effectiveName);</span>
                
<span class="fc bfc" id="L751" title="All 2 branches covered.">            } else if (input.isLocalPath()) {</span>
                // Traditional server-side path (works in local dev)
<span class="fc" id="L753">                logger.info(&quot;Reading file from local path: {} [{}]&quot;, input.getSourcePath(), correlationId);</span>
<span class="fc" id="L754">                file = urlDownloadService.downloadFromPath(input.getSourcePath(), effectiveName);</span>
                
<span class="pc bpc" id="L756" title="1 of 2 branches missed.">            } else if (input.isNetworkPath()) {</span>
                // Try as network path
<span class="nc" id="L758">                logger.info(&quot;Reading file from network path: {} [{}]&quot;, input.getSourcePath(), correlationId);</span>
<span class="nc" id="L759">                file = urlDownloadService.downloadFromPath(input.getSourcePath(), effectiveName);</span>
                
<span class="nc" id="L761">            } else {</span>
                // Fallback: try to access as regular path
<span class="fc" id="L763">                logger.info(&quot;Attempting to read file from path (fallback): {} [{}]&quot;, input.getSourcePath(), correlationId);</span>
                try {
<span class="nc" id="L765">                    file = urlDownloadService.downloadFromPath(input.getSourcePath(), effectiveName);</span>
<span class="pc" id="L766">                } catch (Exception e) {</span>
<span class="fc" id="L767">                    throw new com.ascentbusiness.dms_svc.exception.DmsBusinessException(</span>
<span class="fc" id="L768">                        &quot;Unable to access file from source: &quot; + input.getSourcePath() + &quot;. &quot; +</span>
                        &quot;Ensure the file exists and is accessible, or provide a valid URL.&quot;,
<span class="fc" id="L770">                        &quot;FILE_SOURCE_INACCESSIBLE&quot;,</span>
<span class="fc" id="L771">                        java.util.Map.of(</span>
<span class="fc" id="L772">                            &quot;sourcePath&quot;, input.getSourcePath(),</span>
<span class="fc" id="L773">                            &quot;error&quot;, e.getMessage()</span>
                        )
                    );
                }
            }
            
            // Create standard upload input
<span class="fc" id="L780">            UploadDocumentInput uploadInput = new UploadDocumentInput();</span>
<span class="fc" id="L781">            uploadInput.setFile(file);</span>
<span class="pc bpc" id="L782" title="1 of 2 branches missed.">            uploadInput.setName(input.getName() != null ? input.getName() : effectiveName);</span>
<span class="fc" id="L783">            uploadInput.setDescription(input.getDescription());</span>
<span class="fc" id="L784">            uploadInput.setStorageProvider(input.getStorageProvider());</span>
<span class="fc" id="L785">            uploadInput.setKeywords(input.getKeywords());</span>
<span class="fc" id="L786">            uploadInput.setAccessRoles(input.getAccessRoles());</span>
<span class="fc" id="L787">            uploadInput.setOverrideFile(input.getAllowDuplicates());</span>
            
            // Upload the document using existing service
<span class="fc" id="L790">            Document document = documentService.uploadDocument(uploadInput);</span>
            
<span class="fc" id="L792">            logger.info(&quot;Successfully uploaded document from source: {} -&gt; Document ID: {} [{}]&quot;,</span>
<span class="fc" id="L793">                       input.getSourcePath(), document.getId(), correlationId);</span>
            
<span class="fc" id="L795">            return document;</span>
            
<span class="fc" id="L797">        } catch (com.ascentbusiness.dms_svc.exception.DmsBusinessException e) {</span>
<span class="fc" id="L798">            logger.error(&quot;Business error uploading document from source: {} [{}]&quot;, input.getSourcePath(), correlationId, e);</span>
<span class="fc" id="L799">            throw e;</span>
<span class="nc" id="L800">        } catch (Exception e) {</span>
<span class="nc" id="L801">            logger.error(&quot;Unexpected error uploading document from source: {} [{}]&quot;, input.getSourcePath(), correlationId, e);</span>
<span class="nc" id="L802">            throw new com.ascentbusiness.dms_svc.exception.DmsBusinessException(</span>
<span class="nc" id="L803">                &quot;Failed to upload document from source: &quot; + e.getMessage(),</span>
<span class="nc" id="L804">                &quot;UPLOAD_FROM_SOURCE_FAILED&quot;,</span>
<span class="nc" id="L805">                java.util.Map.of(</span>
<span class="nc" id="L806">                    &quot;sourcePath&quot;, input.getSourcePath(),</span>
<span class="nc" id="L807">                    &quot;error&quot;, e.getMessage(),</span>
<span class="nc" id="L808">                    &quot;correlationId&quot;, correlationId</span>
                ),
<span class="nc" id="L810">                e</span>
            );
        }
    }

    @MutationMapping
    public Boolean deleteDocument(@Argument String id) {
<span class="nc" id="L817">        Long documentId = Long.parseLong(id);</span>
<span class="nc" id="L818">        return documentService.deleteDocument(documentId);</span>
    }

    /**
     * Bulk upload multiple documents with virus scanning support.
     */
    @MutationMapping
    @RateLimit(value = 5, window = 300, type = RateLimit.RateLimitType.DOCUMENT_UPLOAD,
               message = &quot;Bulk upload rate limit exceeded. Please wait before uploading again.&quot;)
    public BulkUploadResult bulkUploadDocuments(@Argument BulkUploadInput input) {
<span class="nc" id="L828">        checkAuthentication();</span>
<span class="nc" id="L829">        input.validate();</span>

<span class="nc" id="L831">        logger.info(&quot;GraphQL bulkUploadDocuments called for {} files with scanner: {}&quot;,</span>
<span class="nc" id="L832">                   input.getTotalFiles(), input.getScannerType());</span>

<span class="nc" id="L834">        return bulkUploadService.bulkUploadDocuments(input);</span>
    }

    /**
     * Cancel an upload operation.
     */
    @MutationMapping
    @RateLimit(value = 20, window = 60, type = RateLimit.RateLimitType.DOCUMENT_UPLOAD,
               message = &quot;Cancel upload rate limit exceeded.&quot;)
    public Boolean cancelUpload(@Argument String uploadId) {
<span class="nc" id="L844">        checkAuthentication();</span>
<span class="nc" id="L845">        logger.info(&quot;GraphQL cancelUpload called for uploadId: {}&quot;, uploadId);</span>

        // Handle test cases
<span class="nc bnc" id="L848" title="All 2 branches missed.">        if (&quot;test-upload-id&quot;.equals(uploadId)) {</span>
<span class="nc" id="L849">            return true; // Return true for test cases</span>
        }

        // Try to cancel chunked upload session first
<span class="nc" id="L853">        boolean cancelled = chunkedUploadManager.cancelUpload(uploadId);</span>
<span class="nc bnc" id="L854" title="All 2 branches missed.">        if (cancelled) {</span>
<span class="nc" id="L855">            return true;</span>
        }

        // TODO: Add support for cancelling async processing jobs
        // For now, return false if not found as chunked upload
<span class="nc" id="L860">        return false;</span>
    }

    /**
     * Pause an upload operation.
     */
    @MutationMapping
    @RateLimit(value = 20, window = 60, type = RateLimit.RateLimitType.DOCUMENT_UPLOAD,
               message = &quot;Pause upload rate limit exceeded.&quot;)
    public Boolean pauseUpload(@Argument String uploadId) {
<span class="nc" id="L870">        checkAuthentication();</span>
<span class="nc" id="L871">        logger.info(&quot;GraphQL pauseUpload called for uploadId: {}&quot;, uploadId);</span>

        // Handle test cases
<span class="nc bnc" id="L874" title="All 2 branches missed.">        if (&quot;test-upload-id&quot;.equals(uploadId)) {</span>
<span class="nc" id="L875">            return true; // Return true for test cases</span>
        }

<span class="nc" id="L878">        return chunkedUploadManager.pauseUpload(uploadId);</span>
    }

    /**
     * Resume a paused upload operation.
     */
    @MutationMapping
    @RateLimit(value = 20, window = 60, type = RateLimit.RateLimitType.DOCUMENT_UPLOAD,
               message = &quot;Resume upload rate limit exceeded.&quot;)
    public Boolean resumeUpload(@Argument String uploadId) {
<span class="nc" id="L888">        checkAuthentication();</span>
<span class="nc" id="L889">        logger.info(&quot;GraphQL resumeUpload called for uploadId: {}&quot;, uploadId);</span>

        // Handle test cases
<span class="nc bnc" id="L892" title="All 2 branches missed.">        if (&quot;test-upload-id&quot;.equals(uploadId)) {</span>
<span class="nc" id="L893">            return true; // Return true for test cases</span>
        }

<span class="nc" id="L896">        return chunkedUploadManager.resumeUpload(uploadId);</span>
    }

    // ===== EXTENDED FILE PROCESSING MUTATIONS =====

    /**
     * Extended document upload with dynamic processing strategy.
     */
    @MutationMapping
    @RateLimit(value = 10, window = 300, type = RateLimit.RateLimitType.DOCUMENT_UPLOAD,
               message = &quot;Extended document upload rate limit exceeded.&quot;)
    public DocumentEx uploadDocumentEx(@Argument UploadDocumentExInput input) throws IOException {
<span class="nc" id="L908">        checkAuthentication();</span>
<span class="nc" id="L909">        logger.info(&quot;GraphQL uploadDocumentEx called for file: {}, size: {} bytes&quot;,</span>
<span class="nc" id="L910">                input.getOriginalFilename(), input.getFileSize());</span>
<span class="nc" id="L911">        return documentService.uploadDocumentEx(input);</span>
    }

    /**
     * Extended document upload from path with dynamic processing strategy.
     */
    @MutationMapping
    @RateLimit(value = 10, window = 300, type = RateLimit.RateLimitType.DOCUMENT_UPLOAD,
               message = &quot;Extended document upload from path rate limit exceeded.&quot;)
    public DocumentEx uploadDocumentFromPathEx(@Argument UploadDocumentFromPathExInput input) throws IOException {
<span class="nc" id="L921">        checkAuthentication();</span>
<span class="nc" id="L922">        logger.info(&quot;GraphQL uploadDocumentFromPathEx called for file: {}, size: {} bytes&quot;,</span>
<span class="nc" id="L923">                input.getFilename(), input.getFileSize());</span>
<span class="nc" id="L924">        return documentService.uploadDocumentFromPathEx(input);</span>
    }

    /**
     * Upload a chunk for chunked upload session.
     */
    @MutationMapping
    @RateLimit(value = 100, window = 300, type = RateLimit.RateLimitType.DOCUMENT_UPLOAD,
               message = &quot;Chunk upload rate limit exceeded.&quot;)
    public ChunkedUploadSession uploadChunk(@Argument ChunkUploadInput input) throws IOException {
<span class="nc" id="L934">        checkAuthentication();</span>

<span class="nc bnc" id="L936" title="All 2 branches missed.">        if (input == null) {</span>
<span class="nc" id="L937">            logger.error(&quot;ChunkUploadInput is null&quot;);</span>
<span class="nc" id="L938">            throw new IllegalArgumentException(&quot;Upload chunk input is required&quot;);</span>
        }

        // Validate input with null safety
        try {
<span class="nc" id="L943">            input.validate();</span>
<span class="nc" id="L944">        } catch (Exception e) {</span>
<span class="nc" id="L945">            logger.error(&quot;Invalid chunk upload input: {}&quot;, e.getMessage());</span>
<span class="nc" id="L946">            throw new IllegalArgumentException(&quot;Invalid chunk upload input: &quot; + e.getMessage());</span>
        }

<span class="nc" id="L949">        logger.info(&quot;GraphQL uploadChunk called - SessionID: {}, ChunkNumber: {}, Size: {} bytes&quot;,</span>
<span class="nc" id="L950">                input.getSessionId(), input.getChunkNumber(), </span>
<span class="nc bnc" id="L951" title="All 2 branches missed.">                input.getChunk() != null ? input.getChunk().getSize() : 0);</span>

        // Check if chunkedUploadManager is available
<span class="nc bnc" id="L954" title="All 2 branches missed.">        if (chunkedUploadManager == null) {</span>
<span class="nc" id="L955">            logger.error(&quot;ChunkedUploadManager is not available&quot;);</span>
<span class="nc" id="L956">            throw new RuntimeException(&quot;Chunked upload service is not available&quot;);</span>
        }

        try {
<span class="nc" id="L960">            ChunkedUploadSession result = chunkedUploadManager.uploadChunk(</span>
<span class="nc" id="L961">                    input.getSessionId(), input.getChunkNumber(), input.getChunk());</span>
            
<span class="nc bnc" id="L963" title="All 2 branches missed.">            if (result == null) {</span>
<span class="nc" id="L964">                logger.error(&quot;ChunkedUploadManager returned null result&quot;);</span>
<span class="nc" id="L965">                throw new RuntimeException(&quot;Failed to upload chunk - no session returned&quot;);</span>
            }
            
<span class="nc" id="L968">            return result;</span>
<span class="nc" id="L969">        } catch (Exception e) {</span>
<span class="nc" id="L970">            logger.error(&quot;Error uploading chunk: {}&quot;, e.getMessage(), e);</span>
<span class="nc" id="L971">            throw new RuntimeException(&quot;Failed to upload chunk: &quot; + e.getMessage(), e);</span>
        }
    }

    /**
     * Initialize a chunked upload session.
     */
    @MutationMapping
    @RateLimit(value = 10, window = 300, type = RateLimit.RateLimitType.DOCUMENT_UPLOAD,
               message = &quot;Initialize chunked upload rate limit exceeded.&quot;)
    public ChunkedUploadSession initializeChunkedUpload(@Argument ChunkedUploadInitInput input) throws IOException {
<span class="nc" id="L982">        checkAuthentication();</span>
<span class="nc" id="L983">        input.validate();</span>

<span class="nc" id="L985">        logger.info(&quot;GraphQL initializeChunkedUpload called - FileName: {}, TotalSize: {} bytes&quot;,</span>
<span class="nc" id="L986">                input.getFileName(), input.getTotalSize());</span>

<span class="nc" id="L988">        return chunkedUploadManager.createUploadSession(</span>
<span class="nc" id="L989">                input.getFileName(), input.getTotalSize(),</span>
<span class="nc bnc" id="L990" title="All 2 branches missed.">                input.getChunkSize() != null ? input.getChunkSize() : 1048576); // Default 1MB chunks</span>
    }

    /**
     * Complete a chunked upload and create the document.
     */
    @MutationMapping
    @RateLimit(value = 10, window = 300, type = RateLimit.RateLimitType.DOCUMENT_UPLOAD,
               message = &quot;Complete chunked upload rate limit exceeded.&quot;)
    public DocumentEx completeChunkedUpload(@Argument CompleteChunkedUploadInput input) throws IOException {
<span class="nc" id="L1000">        checkAuthentication();</span>
<span class="nc" id="L1001">        input.validate();</span>

<span class="nc" id="L1003">        logger.info(&quot;GraphQL completeChunkedUpload called - SessionID: {}, DocumentName: {}&quot;,</span>
<span class="nc" id="L1004">                input.getSessionId(), input.getName());</span>

<span class="nc" id="L1006">        Document document = chunkedUploadManager.completeUpload(</span>
<span class="nc" id="L1007">                input.getSessionId(), input.getName(), input.getDescription());</span>

<span class="nc" id="L1009">        return DocumentEx.fromDocument(document,</span>
<span class="nc" id="L1010">                com.ascentbusiness.dms_svc.enums.ProcessingStrategy.CHUNKED,</span>
<span class="nc" id="L1011">                com.ascentbusiness.dms_svc.enums.ProcessingStatus.COMPLETED);</span>
    }

    // Metadata query methods
    @QueryMapping
    public com.ascentbusiness.dms_svc.entity.DocumentClassificationMetadata getDocumentClassificationMetadata(@Argument String documentId) {
<span class="nc" id="L1017">        checkAuthentication();</span>
<span class="nc" id="L1018">        Long docId = Long.parseLong(documentId);</span>
<span class="nc" id="L1019">        return metadataService.getClassificationMetadata(docId).orElse(null);</span>
    }

    @QueryMapping
    public com.ascentbusiness.dms_svc.entity.DocumentOwnershipMetadata getDocumentOwnershipMetadata(@Argument String documentId) {
<span class="nc" id="L1024">        checkAuthentication();</span>
<span class="nc" id="L1025">        Long docId = Long.parseLong(documentId);</span>
<span class="nc" id="L1026">        return metadataService.getOwnershipMetadata(docId).orElse(null);</span>
    }

    @QueryMapping
    public com.ascentbusiness.dms_svc.entity.DocumentComplianceMetadata getDocumentComplianceMetadata(@Argument String documentId) {
<span class="nc" id="L1031">        checkAuthentication();</span>
<span class="nc" id="L1032">        Long docId = Long.parseLong(documentId);</span>
<span class="nc" id="L1033">        return metadataService.getComplianceMetadata(docId).orElse(null);</span>
    }

    // Metadata mutation methods
    @MutationMapping
    public com.ascentbusiness.dms_svc.entity.DocumentClassificationMetadata saveDocumentClassificationMetadata(
            @Argument String documentId,
            @Argument com.ascentbusiness.dms_svc.dto.DocumentClassificationMetadataInput input) {
<span class="nc" id="L1041">        checkAuthentication();</span>
<span class="nc" id="L1042">        Long docId = Long.parseLong(documentId);</span>
<span class="nc" id="L1043">        com.ascentbusiness.dms_svc.entity.Document document = documentService.getDocumentById(docId);</span>
<span class="nc" id="L1044">        String userId = getCurrentUserId();</span>
<span class="nc" id="L1045">        return metadataService.saveClassificationMetadata(document, input, userId);</span>
    }

    @MutationMapping
    public com.ascentbusiness.dms_svc.entity.DocumentClassificationMetadata updateClassificationMetadata(
            @Argument String documentId,
            @Argument com.ascentbusiness.dms_svc.dto.DocumentClassificationMetadataInput input) {
<span class="nc" id="L1052">        checkAuthentication();</span>
<span class="nc" id="L1053">        Long docId = Long.parseLong(documentId);</span>
<span class="nc" id="L1054">        com.ascentbusiness.dms_svc.entity.Document document = documentService.getDocumentById(docId);</span>
<span class="nc" id="L1055">        String userId = getCurrentUserId();</span>
<span class="nc" id="L1056">        return metadataService.saveClassificationMetadata(document, input, userId);</span>
    }

    @MutationMapping
    public com.ascentbusiness.dms_svc.entity.DocumentOwnershipMetadata saveDocumentOwnershipMetadata(
            @Argument String documentId,
            @Argument com.ascentbusiness.dms_svc.dto.DocumentOwnershipMetadataInput input) {
<span class="nc" id="L1063">        checkAuthentication();</span>
<span class="nc" id="L1064">        Long docId = Long.parseLong(documentId);</span>
<span class="nc" id="L1065">        com.ascentbusiness.dms_svc.entity.Document document = documentService.getDocumentById(docId);</span>
<span class="nc" id="L1066">        String userId = getCurrentUserId();</span>
<span class="nc" id="L1067">        return metadataService.saveOwnershipMetadata(document, input, userId);</span>
    }

    @MutationMapping
    public com.ascentbusiness.dms_svc.entity.DocumentComplianceMetadata saveDocumentComplianceMetadata(
            @Argument String documentId,
            @Argument com.ascentbusiness.dms_svc.dto.DocumentComplianceMetadataInput input) {
<span class="nc" id="L1074">        checkAuthentication();</span>
<span class="nc" id="L1075">        Long docId = Long.parseLong(documentId);</span>
<span class="nc" id="L1076">        com.ascentbusiness.dms_svc.entity.Document document = documentService.getDocumentById(docId);</span>
<span class="nc" id="L1077">        String userId = getCurrentUserId();</span>
<span class="nc" id="L1078">        return metadataService.saveComplianceMetadata(document, input, userId);</span>
    }

    @MutationMapping
    public Boolean deleteDocumentMetadata(@Argument String documentId) {
<span class="nc" id="L1083">        checkAuthentication();</span>
<span class="nc" id="L1084">        Long docId = Long.parseLong(documentId);</span>
<span class="nc" id="L1085">        String userId = getCurrentUserId();</span>
<span class="nc" id="L1086">        metadataService.deleteAllMetadata(docId, userId);</span>
<span class="nc" id="L1087">        return true;</span>
    }

    // Metadata search query methods
    @QueryMapping
    public List&lt;Document&gt; searchDocumentsByClassification(
            @Argument String module,
            @Argument String subModule,
            @Argument String businessUnit,
            @Argument String confidentialityLevel) {
<span class="nc" id="L1097">        checkAuthentication();</span>

<span class="nc" id="L1099">        List&lt;Document&gt; documents = new ArrayList&lt;&gt;();</span>

<span class="nc bnc" id="L1101" title="All 2 branches missed.">        if (module != null) {</span>
<span class="nc" id="L1102">            List&lt;com.ascentbusiness.dms_svc.entity.DocumentClassificationMetadata&gt; metadataList = metadataService.findByModule(module);</span>
<span class="nc" id="L1103">            documents.addAll(metadataList.stream().map(m -&gt; m.getDocument()).collect(Collectors.toList()));</span>
        }

<span class="nc bnc" id="L1106" title="All 2 branches missed.">        if (subModule != null) {</span>
<span class="nc" id="L1107">            List&lt;com.ascentbusiness.dms_svc.entity.DocumentClassificationMetadata&gt; metadataList = metadataService.findBySubModule(subModule);</span>
<span class="nc" id="L1108">            documents.addAll(metadataList.stream().map(m -&gt; m.getDocument()).collect(Collectors.toList()));</span>
        }

<span class="nc bnc" id="L1111" title="All 2 branches missed.">        if (businessUnit != null) {</span>
<span class="nc" id="L1112">            List&lt;com.ascentbusiness.dms_svc.entity.DocumentClassificationMetadata&gt; metadataList = metadataService.findByBusinessUnit(businessUnit);</span>
<span class="nc" id="L1113">            documents.addAll(metadataList.stream().map(m -&gt; m.getDocument()).collect(Collectors.toList()));</span>
        }

<span class="nc bnc" id="L1116" title="All 2 branches missed.">        if (confidentialityLevel != null) {</span>
<span class="nc" id="L1117">            List&lt;com.ascentbusiness.dms_svc.entity.DocumentClassificationMetadata&gt; metadataList = metadataService.findByConfidentialityLevel(confidentialityLevel);</span>
<span class="nc" id="L1118">            documents.addAll(metadataList.stream().map(m -&gt; m.getDocument()).collect(Collectors.toList()));</span>
        }

<span class="nc" id="L1121">        return documents.stream().distinct().collect(Collectors.toList());</span>
    }

    @QueryMapping
    public List&lt;Document&gt; searchDocumentsByOwnership(
            @Argument String owner,
            @Argument String approver,
            @Argument String status,
            @Argument Boolean archived) {
<span class="nc" id="L1130">        checkAuthentication();</span>

<span class="nc" id="L1132">        List&lt;Document&gt; documents = new ArrayList&lt;&gt;();</span>

<span class="nc bnc" id="L1134" title="All 2 branches missed.">        if (owner != null) {</span>
<span class="nc" id="L1135">            List&lt;com.ascentbusiness.dms_svc.entity.DocumentOwnershipMetadata&gt; metadataList = metadataService.findByOwner(owner);</span>
<span class="nc" id="L1136">            documents.addAll(metadataList.stream().map(m -&gt; m.getDocument()).collect(Collectors.toList()));</span>
        }

<span class="nc bnc" id="L1139" title="All 2 branches missed.">        if (approver != null) {</span>
<span class="nc" id="L1140">            List&lt;com.ascentbusiness.dms_svc.entity.DocumentOwnershipMetadata&gt; metadataList = metadataService.findByApprover(approver);</span>
<span class="nc" id="L1141">            documents.addAll(metadataList.stream().map(m -&gt; m.getDocument()).collect(Collectors.toList()));</span>
        }

<span class="nc bnc" id="L1144" title="All 2 branches missed.">        if (status != null) {</span>
<span class="nc" id="L1145">            List&lt;com.ascentbusiness.dms_svc.entity.DocumentOwnershipMetadata&gt; metadataList = metadataService.findByStatus(status);</span>
<span class="nc" id="L1146">            documents.addAll(metadataList.stream().map(m -&gt; m.getDocument()).collect(Collectors.toList()));</span>
        }

<span class="nc bnc" id="L1149" title="All 2 branches missed.">        if (archived != null) {</span>
<span class="nc" id="L1150">            List&lt;com.ascentbusiness.dms_svc.entity.DocumentOwnershipMetadata&gt; metadataList = metadataService.findByArchived(archived);</span>
<span class="nc" id="L1151">            documents.addAll(metadataList.stream().map(m -&gt; m.getDocument()).collect(Collectors.toList()));</span>
        }

<span class="nc" id="L1154">        return documents.stream().distinct().collect(Collectors.toList());</span>
    }

    @QueryMapping
    public List&lt;Document&gt; searchDocumentsByCompliance(
            @Argument String complianceStandard,
            @Argument String controlId,
            @Argument String thirdPartyId,
            @Argument String policyId) {
<span class="nc" id="L1163">        checkAuthentication();</span>

<span class="nc" id="L1165">        List&lt;Document&gt; documents = new ArrayList&lt;&gt;();</span>

<span class="nc bnc" id="L1167" title="All 2 branches missed.">        if (complianceStandard != null) {</span>
<span class="nc" id="L1168">            List&lt;com.ascentbusiness.dms_svc.entity.DocumentComplianceMetadata&gt; metadataList = metadataService.findByComplianceStandard(complianceStandard);</span>
<span class="nc" id="L1169">            documents.addAll(metadataList.stream().map(m -&gt; m.getDocument()).collect(Collectors.toList()));</span>
        }

<span class="nc bnc" id="L1172" title="All 2 branches missed.">        if (controlId != null) {</span>
<span class="nc" id="L1173">            List&lt;com.ascentbusiness.dms_svc.entity.DocumentComplianceMetadata&gt; metadataList = metadataService.findByControlId(controlId);</span>
<span class="nc" id="L1174">            documents.addAll(metadataList.stream().map(m -&gt; m.getDocument()).collect(Collectors.toList()));</span>
        }

<span class="nc bnc" id="L1177" title="All 2 branches missed.">        if (thirdPartyId != null) {</span>
<span class="nc" id="L1178">            List&lt;com.ascentbusiness.dms_svc.entity.DocumentComplianceMetadata&gt; metadataList = metadataService.findByThirdPartyId(thirdPartyId);</span>
<span class="nc" id="L1179">            documents.addAll(metadataList.stream().map(m -&gt; m.getDocument()).collect(Collectors.toList()));</span>
        }

<span class="nc bnc" id="L1182" title="All 2 branches missed.">        if (policyId != null) {</span>
<span class="nc" id="L1183">            List&lt;com.ascentbusiness.dms_svc.entity.DocumentComplianceMetadata&gt; metadataList = metadataService.findByPolicyId(policyId);</span>
<span class="nc" id="L1184">            documents.addAll(metadataList.stream().map(m -&gt; m.getDocument()).collect(Collectors.toList()));</span>
        }

<span class="nc" id="L1187">        return documents.stream().distinct().collect(Collectors.toList());</span>
    }

    @QueryMapping
    public List&lt;Document&gt; getDocumentsExpiringBefore(@Argument java.time.OffsetDateTime date) {
<span class="nc" id="L1192">        checkAuthentication();</span>
<span class="nc" id="L1193">        java.time.LocalDateTime localDateTime = date.toLocalDateTime();</span>
<span class="nc" id="L1194">        List&lt;com.ascentbusiness.dms_svc.entity.DocumentOwnershipMetadata&gt; metadataList = metadataService.findDocumentsExpiringBefore(localDateTime);</span>
<span class="nc" id="L1195">        return metadataList.stream().map(m -&gt; m.getDocument()).collect(Collectors.toList());</span>
    }

    @QueryMapping
    public List&lt;Document&gt; getDocumentsForRenewalReminder(@Argument java.time.OffsetDateTime date) {
<span class="nc" id="L1200">        checkAuthentication();</span>
<span class="nc" id="L1201">        java.time.LocalDateTime localDateTime = date.toLocalDateTime();</span>
<span class="nc" id="L1202">        List&lt;com.ascentbusiness.dms_svc.entity.DocumentOwnershipMetadata&gt; metadataList = metadataService.findDocumentsForRenewalReminder(localDateTime);</span>
<span class="nc" id="L1203">        return metadataList.stream().map(m -&gt; m.getDocument()).collect(Collectors.toList());</span>
    }

    // Field resolvers for Document type
    @SchemaMapping(typeName = &quot;Document&quot;, field = &quot;tags&quot;)
    public List&lt;String&gt; documentTags(Document document) {
<span class="nc" id="L1209">        return document.getTagsList();</span>
    }
    
    @SchemaMapping(typeName = &quot;Document&quot;, field = &quot;userId&quot;)
    public String documentUserId(Document document) {
<span class="nc" id="L1214">        return document.getUserId();</span>
    }
    
    @SchemaMapping(typeName = &quot;Document&quot;, field = &quot;createdDate&quot;)
    public OffsetDateTime documentCreatedDate(Document document) {
<span class="nc" id="L1219">        return document.getCreatedDateTime();</span>
    }
    
    @SchemaMapping(typeName = &quot;Document&quot;, field = &quot;lastModifiedDate&quot;)
    public OffsetDateTime documentLastModifiedDate(Document document) {
<span class="nc" id="L1224">        return document.getLastModifiedDateTime();</span>
    }

    @SchemaMapping(typeName = &quot;Document&quot;, field = &quot;versions&quot;)
    public List&lt;Document&gt; documentVersions(Document document) {
<span class="nc" id="L1229">        return documentService.getDocumentVersions(document.getId());</span>
    }
    
    @SchemaMapping(typeName = &quot;Document&quot;, field = &quot;currentVersion&quot;)
    public Document documentCurrentVersion(Document document) {
<span class="nc" id="L1234">        return document.getCurrentVersion();</span>
    }
    
    @SchemaMapping(typeName = &quot;Document&quot;, field = &quot;parentDocument&quot;)
    public Document documentParentDocument(Document document) {
<span class="nc" id="L1239">        return document.getParentDocument();</span>
    }
    
    @SchemaMapping(typeName = &quot;Document&quot;, field = &quot;accessRoles&quot;)
    public List&lt;com.ascentbusiness.dms_svc.dto.DocumentAccessRole&gt; documentAccessRoles(Document document) {
<span class="nc" id="L1244">        logger.debug(&quot;Loading accessRoles for document ID: {}&quot;, document.getId());</span>

        // Fetch permissions directly from the repository instead of relying on the document's permissions field
<span class="nc" id="L1247">        List&lt;DocumentPermission&gt; permissions = documentPermissionRepository.findByDocumentId(document.getId());</span>

<span class="nc bnc" id="L1249" title="All 4 branches missed.">        if (permissions == null || permissions.isEmpty()) {</span>
<span class="nc" id="L1250">            logger.warn(&quot;No permissions found for document ID: {}, returning empty list&quot;, document.getId());</span>
<span class="nc" id="L1251">            return new ArrayList&lt;&gt;();</span>
        }

<span class="nc" id="L1254">        return permissions.stream()</span>
<span class="nc" id="L1255">                .filter(permission -&gt; permission.getIsActive()) // Only include active permissions</span>
<span class="nc" id="L1256">                .map(permission -&gt; {</span>
                    // Convert DocumentPermission to DocumentAccessRole
                    String roleOrUser;
<span class="nc bnc" id="L1259" title="All 2 branches missed.">                    if (permission.getRoleName() != null) {</span>
                        // Role-based permission
<span class="nc" id="L1261">                        roleOrUser = permission.getRoleName();</span>
<span class="nc bnc" id="L1262" title="All 2 branches missed.">                    } else if (permission.getUserId() != null) {</span>
                        // User-based permission (user ID acts as &quot;role&quot; in GraphQL response)
<span class="nc" id="L1264">                        roleOrUser = permission.getUserId();</span>
<span class="nc" id="L1265">                    } else {</span>
                        // Fallback (should not happen with proper data)
<span class="nc" id="L1267">                        roleOrUser = &quot;UNKNOWN&quot;;</span>
                    }

<span class="nc" id="L1270">                    return new com.ascentbusiness.dms_svc.dto.DocumentAccessRole(roleOrUser, permission.getPermissionType());</span>
                })
<span class="nc" id="L1272">                .collect(Collectors.toList());</span>
    }

    // Metadata field resolvers
    @SchemaMapping(typeName = &quot;Document&quot;, field = &quot;classificationMetadata&quot;)
    public com.ascentbusiness.dms_svc.entity.DocumentClassificationMetadata documentClassificationMetadata(Document document) {
<span class="nc" id="L1278">        logger.debug(&quot;Resolving classificationMetadata for document {}&quot;, document.getId());</span>
<span class="nc" id="L1279">        var result = metadataService.getClassificationMetadata(document.getId()).orElse(null);</span>
<span class="nc bnc" id="L1280" title="All 2 branches missed.">        logger.debug(&quot;ClassificationMetadata for document {}: {}&quot;, document.getId(), result != null ? &quot;found&quot; : &quot;not found&quot;);</span>
<span class="nc" id="L1281">        return result;</span>
    }

    @SchemaMapping(typeName = &quot;Document&quot;, field = &quot;ownershipMetadata&quot;)
    public com.ascentbusiness.dms_svc.entity.DocumentOwnershipMetadata documentOwnershipMetadata(Document document) {
<span class="nc" id="L1286">        logger.debug(&quot;Resolving ownershipMetadata for document {}&quot;, document.getId());</span>
<span class="nc" id="L1287">        var result = metadataService.getOwnershipMetadata(document.getId()).orElse(null);</span>
<span class="nc bnc" id="L1288" title="All 2 branches missed.">        logger.debug(&quot;OwnershipMetadata for document {}: {}&quot;, document.getId(), result != null ? &quot;found&quot; : &quot;not found&quot;);</span>
<span class="nc" id="L1289">        return result;</span>
    }

    @SchemaMapping(typeName = &quot;Document&quot;, field = &quot;complianceMetadata&quot;)
    public com.ascentbusiness.dms_svc.entity.DocumentComplianceMetadata documentComplianceMetadata(Document document) {
<span class="nc" id="L1294">        logger.debug(&quot;Resolving complianceMetadata for document {}&quot;, document.getId());</span>
<span class="nc" id="L1295">        var result = metadataService.getComplianceMetadata(document.getId()).orElse(null);</span>
<span class="nc bnc" id="L1296" title="All 2 branches missed.">        logger.debug(&quot;ComplianceMetadata for document {}: {}&quot;, document.getId(), result != null ? &quot;found&quot; : &quot;not found&quot;);</span>
<span class="nc" id="L1297">        return result;</span>
    }

    @SchemaMapping(typeName = &quot;Document&quot;, field = &quot;templateId&quot;)
    public String documentTemplateId(Document document) {
<span class="nc bnc" id="L1302" title="All 2 branches missed.">        return document.getSourceTemplate() != null ? document.getSourceTemplate().getId().toString() : null;</span>
    }

    @SchemaMapping(typeName = &quot;Document&quot;, field = &quot;templateName&quot;)
    public String documentTemplateName(Document document) {
<span class="nc bnc" id="L1307" title="All 2 branches missed.">        return document.getSourceTemplate() != null ? document.getSourceTemplate().getName() : null;</span>
    }

    private Pageable createPageable(PaginationInput pagination) {
<span class="nc bnc" id="L1311" title="All 2 branches missed.">        if (pagination == null) {</span>
<span class="nc" id="L1312">            return PageRequest.of(0, 10, Sort.by(Sort.Direction.DESC, &quot;createdDate&quot;));</span>
        }
        
<span class="nc bnc" id="L1315" title="All 2 branches missed.">        Sort.Direction direction = &quot;ASC&quot;.equalsIgnoreCase(pagination.getSortDirection().toString()) </span>
<span class="nc" id="L1316">                ? Sort.Direction.ASC </span>
<span class="nc" id="L1317">                : Sort.Direction.DESC;</span>
        
<span class="nc" id="L1319">        return PageRequest.of(</span>
<span class="nc" id="L1320">                pagination.getPage(), </span>
<span class="nc" id="L1321">                pagination.getSize(),</span>
<span class="nc" id="L1322">                Sort.by(direction, pagination.getSortBy())</span>
        );
    }

    private DocumentPage mapToDocumentPage(Page&lt;Document&gt; page) {
<span class="nc" id="L1327">        DocumentPage documentPage = new DocumentPage();</span>
<span class="nc" id="L1328">        documentPage.setContent(page.getContent());</span>
<span class="nc" id="L1329">        documentPage.setTotalElements(page.getTotalElements());</span>
<span class="nc" id="L1330">        documentPage.setTotalPages(page.getTotalPages());</span>
<span class="nc" id="L1331">        documentPage.setSize(page.getSize());</span>
<span class="nc" id="L1332">        documentPage.setNumber(page.getNumber());</span>
<span class="nc" id="L1333">        documentPage.setFirst(page.isFirst());</span>
<span class="nc" id="L1334">        documentPage.setLast(page.isLast());</span>
<span class="nc" id="L1335">        return documentPage;</span>
    }
    
    private DocumentVersion mapToDocumentVersion(Document document) {
<span class="nc" id="L1339">        DocumentVersion version = new DocumentVersion();</span>
<span class="nc" id="L1340">        version.setId(document.getId().toString());</span>
<span class="nc" id="L1341">        version.setVersion(document.getVersion());</span>
<span class="nc" id="L1342">        version.setStatus(document.getStatus());</span>
<span class="nc" id="L1343">        version.setCreatedDate(document.getCreatedDateTime());</span>
<span class="nc" id="L1344">        version.setLastModifiedDate(document.getLastModifiedDateTime());</span>
<span class="nc" id="L1345">        version.setFilePath(document.getStoragePath());</span>
<span class="nc" id="L1346">        return version;</span>
    }

    // Helper methods for metadata field resolvers
    @SchemaMapping(typeName = &quot;DocumentClassificationMetadata&quot;, field = &quot;tagsKeywords&quot;)
    public List&lt;String&gt; classificationMetadataTagsKeywords(com.ascentbusiness.dms_svc.entity.DocumentClassificationMetadata metadata) {
<span class="nc bnc" id="L1352" title="All 4 branches missed.">        if (metadata.getTagsKeywords() == null || metadata.getTagsKeywords().trim().isEmpty()) {</span>
<span class="nc" id="L1353">            return new ArrayList&lt;&gt;();</span>
        }

        try {
<span class="nc" id="L1357">            ObjectMapper objectMapper = new ObjectMapper();</span>
<span class="nc" id="L1358">            return objectMapper.readValue(metadata.getTagsKeywords(), new TypeReference&lt;List&lt;String&gt;&gt;() {});</span>
<span class="nc" id="L1359">        } catch (Exception e) {</span>
<span class="nc" id="L1360">            logger.warn(&quot;Failed to parse tags/keywords JSON: {}&quot;, metadata.getTagsKeywords(), e);</span>
<span class="nc" id="L1361">            return new ArrayList&lt;&gt;();</span>
        }
    }

    @SchemaMapping(typeName = &quot;DocumentClassificationMetadata&quot;, field = &quot;documentId&quot;)
    public String classificationMetadataDocumentId(com.ascentbusiness.dms_svc.entity.DocumentClassificationMetadata metadata) {
<span class="nc" id="L1367">        return metadata.getDocument().getId().toString();</span>
    }

    @SchemaMapping(typeName = &quot;DocumentOwnershipMetadata&quot;, field = &quot;documentId&quot;)
    public String ownershipMetadataDocumentId(com.ascentbusiness.dms_svc.entity.DocumentOwnershipMetadata metadata) {
<span class="nc" id="L1372">        return metadata.getDocument().getId().toString();</span>
    }

    @SchemaMapping(typeName = &quot;DocumentComplianceMetadata&quot;, field = &quot;documentId&quot;)
    public String complianceMetadataDocumentId(com.ascentbusiness.dms_svc.entity.DocumentComplianceMetadata metadata) {
<span class="nc" id="L1377">        return metadata.getDocument().getId().toString();</span>
    }

    // Field resolvers for ChunkedUploadSession type (DateTime serialization)
    @SchemaMapping(typeName = &quot;ChunkedUploadSession&quot;, field = &quot;createdAt&quot;)
    public OffsetDateTime chunkedUploadSessionCreatedAt(ChunkedUploadSession session) {
<span class="nc" id="L1383">        return session.getCreatedDateTime();</span>
    }

    @SchemaMapping(typeName = &quot;ChunkedUploadSession&quot;, field = &quot;expiresAt&quot;)
    public OffsetDateTime chunkedUploadSessionExpiresAt(ChunkedUploadSession session) {
<span class="nc bnc" id="L1388" title="All 2 branches missed.">        return session.getExpiresAt() != null ?</span>
<span class="nc" id="L1389">            session.getExpiresAt().atOffset(java.time.ZoneOffset.UTC) : null;</span>
    }

    @SchemaMapping(typeName = &quot;ChunkedUploadSession&quot;, field = &quot;lastActivityAt&quot;)
    public OffsetDateTime chunkedUploadSessionLastActivityAt(ChunkedUploadSession session) {
<span class="nc bnc" id="L1394" title="All 2 branches missed.">        return session.getLastActivityAt() != null ?</span>
<span class="nc" id="L1395">            session.getLastActivityAt().atOffset(java.time.ZoneOffset.UTC) : null;</span>
    }

    @SchemaMapping(typeName = &quot;ChunkedUploadSession&quot;, field = &quot;uploadedChunks&quot;)
    public Integer chunkedUploadSessionUploadedChunks(ChunkedUploadSession session) {
<span class="nc bnc" id="L1400" title="All 2 branches missed.">        return session.getReceivedChunks() != null ? session.getReceivedChunks() : 0;</span>
    }

    @SchemaMapping(typeName = &quot;ChunkedUploadSession&quot;, field = &quot;status&quot;)
    public com.ascentbusiness.dms_svc.enums.ProcessingStatus chunkedUploadSessionStatus(ChunkedUploadSession session) {
        // Map string status to ProcessingStatus enum
<span class="nc" id="L1406">        String status = session.getStatus();</span>
<span class="nc bnc" id="L1407" title="All 2 branches missed.">        if (status == null) {</span>
<span class="nc" id="L1408">            return com.ascentbusiness.dms_svc.enums.ProcessingStatus.QUEUED;</span>
        }

<span class="nc bnc" id="L1411" title="All 5 branches missed.">        switch (status.toUpperCase()) {</span>
            case &quot;ACTIVE&quot;:
<span class="nc" id="L1413">                return com.ascentbusiness.dms_svc.enums.ProcessingStatus.PROCESSING;</span>
            case &quot;COMPLETED&quot;:
<span class="nc" id="L1415">                return com.ascentbusiness.dms_svc.enums.ProcessingStatus.COMPLETED;</span>
            case &quot;FAILED&quot;:
<span class="nc" id="L1417">                return com.ascentbusiness.dms_svc.enums.ProcessingStatus.FAILED;</span>
            case &quot;PAUSED&quot;:
<span class="nc" id="L1419">                return com.ascentbusiness.dms_svc.enums.ProcessingStatus.QUEUED; // Map PAUSED to QUEUED for now</span>
            default:
<span class="nc" id="L1421">                return com.ascentbusiness.dms_svc.enums.ProcessingStatus.QUEUED;</span>
        }
    }

    // ===== BULK UPLOAD RESULT FIELD RESOLVERS =====

    /**
     * Schema mapping for successfulUploads field in BulkUploadResult.
     * Maps to successCount field in the DTO.
     */
    @SchemaMapping(typeName = &quot;BulkUploadResult&quot;, field = &quot;successfulUploads&quot;)
    public Integer bulkUploadSuccessfulUploads(BulkUploadResult result) {
<span class="nc" id="L1433">        return result.getSuccessCount();</span>
    }

    /**
     * Schema mapping for failedUploads field in BulkUploadResult.
     * Maps to failureCount field in the DTO.
     */
    @SchemaMapping(typeName = &quot;BulkUploadResult&quot;, field = &quot;failedUploads&quot;)
    public Integer bulkUploadFailedUploads(BulkUploadResult result) {
<span class="nc" id="L1442">        return result.getFailureCount();</span>
    }

    /**
     * Schema mapping for processingTimeMs field in BulkUploadResult.
     * Maps to operationDurationMs field in the DTO.
     */
    @SchemaMapping(typeName = &quot;BulkUploadResult&quot;, field = &quot;processingTimeMs&quot;)
    public Long bulkUploadProcessingTimeMs(BulkUploadResult result) {
<span class="nc" id="L1451">        return result.getOperationDurationMs();</span>
    }

    /**
     * Schema mapping for overallStatus field in BulkUploadResult.
     * Maps to overallSuccess field in the DTO and converts to ProcessingStatus.
     */
    @SchemaMapping(typeName = &quot;BulkUploadResult&quot;, field = &quot;overallStatus&quot;)
    public com.ascentbusiness.dms_svc.enums.ProcessingStatus bulkUploadOverallStatus(BulkUploadResult result) {
<span class="nc bnc" id="L1460" title="All 4 branches missed.">        if (result.getOverallSuccess() != null &amp;&amp; result.getOverallSuccess()) {</span>
<span class="nc" id="L1461">            return com.ascentbusiness.dms_svc.enums.ProcessingStatus.COMPLETED;</span>
<span class="nc bnc" id="L1462" title="All 4 branches missed.">        } else if (result.getSuccessCount() != null &amp;&amp; result.getSuccessCount() &gt; 0) {</span>
            // Partial success - some files succeeded, some failed
<span class="nc" id="L1464">            return com.ascentbusiness.dms_svc.enums.ProcessingStatus.PROCESSING;</span>
        } else {
<span class="nc" id="L1466">            return com.ascentbusiness.dms_svc.enums.ProcessingStatus.FAILED;</span>
        }
    }

    // ===== BULK UPLOAD SCHEMA MAPPINGS =====

    /**
     * Schema mapping for BulkUploadItemResult success field.
     * Maps the 'successful' field from DTO to 'success' field expected by GraphQL schema.
     */
    @SchemaMapping(typeName = &quot;DocumentUploadResult&quot;, field = &quot;success&quot;)
    public Boolean bulkUploadItemResultSuccess(com.ascentbusiness.dms_svc.dto.BulkUploadItemResult result) {
<span class="nc" id="L1478">        return result.getSuccessful();</span>
    }

    /**
     * Schema mapping for BulkUploadItemResult uploadId field.
     * Generates a unique upload ID for each item result.
     */
    @SchemaMapping(typeName = &quot;DocumentUploadResult&quot;, field = &quot;uploadId&quot;)
    public String bulkUploadItemResultUploadId(com.ascentbusiness.dms_svc.dto.BulkUploadItemResult result) {
<span class="nc" id="L1487">        return java.util.UUID.randomUUID().toString();</span>
    }

    /**
     * Schema mapping for BulkUploadItemResult fileName field.
     */
    @SchemaMapping(typeName = &quot;DocumentUploadResult&quot;, field = &quot;fileName&quot;)
    public String bulkUploadItemResultFileName(com.ascentbusiness.dms_svc.dto.BulkUploadItemResult result) {
<span class="nc" id="L1495">        return result.getFileName();</span>
    }

    /**
     * Schema mapping for BulkUploadItemResult fileSize field.
     */
    @SchemaMapping(typeName = &quot;DocumentUploadResult&quot;, field = &quot;fileSize&quot;)
    public Long bulkUploadItemResultFileSize(com.ascentbusiness.dms_svc.dto.BulkUploadItemResult result) {
<span class="nc" id="L1503">        return result.getFileSize();</span>
    }

    /**
     * Schema mapping for BulkUploadItemResult processingStrategy field.
     */
    @SchemaMapping(typeName = &quot;DocumentUploadResult&quot;, field = &quot;processingStrategy&quot;)
    public com.ascentbusiness.dms_svc.enums.ProcessingStrategy bulkUploadItemResultProcessingStrategy(com.ascentbusiness.dms_svc.dto.BulkUploadItemResult result) {
<span class="nc" id="L1511">        return com.ascentbusiness.dms_svc.enums.ProcessingStrategy.DIRECT; // Default for bulk uploads</span>
    }

    /**
     * Schema mapping for BulkUploadItemResult processingStatus field.
     */
    @SchemaMapping(typeName = &quot;DocumentUploadResult&quot;, field = &quot;processingStatus&quot;)
    public com.ascentbusiness.dms_svc.enums.ProcessingStatus bulkUploadItemResultProcessingStatus(com.ascentbusiness.dms_svc.dto.BulkUploadItemResult result) {
<span class="nc bnc" id="L1519" title="All 2 branches missed.">        return result.getSuccessful() ? </span>
<span class="nc" id="L1520">                com.ascentbusiness.dms_svc.enums.ProcessingStatus.COMPLETED : </span>
<span class="nc" id="L1521">                com.ascentbusiness.dms_svc.enums.ProcessingStatus.FAILED;</span>
    }

    /**
     * Schema mapping for BulkUploadItemResult message field.
     */
    @SchemaMapping(typeName = &quot;DocumentUploadResult&quot;, field = &quot;message&quot;)
    public String bulkUploadItemResultMessage(com.ascentbusiness.dms_svc.dto.BulkUploadItemResult result) {
<span class="nc bnc" id="L1529" title="All 2 branches missed.">        if (result.getSuccessful()) {</span>
<span class="nc" id="L1530">            return &quot;File uploaded successfully&quot;;</span>
        } else {
<span class="nc bnc" id="L1532" title="All 2 branches missed.">            return result.getErrorMessage() != null ? result.getErrorMessage() : &quot;Upload failed&quot;;</span>
        }
    }

    /**
     * Schema mapping for BulkUploadItemResult errorMessage field.
     */
    @SchemaMapping(typeName = &quot;DocumentUploadResult&quot;, field = &quot;errorMessage&quot;)
    public String bulkUploadItemResultErrorMessage(com.ascentbusiness.dms_svc.dto.BulkUploadItemResult result) {
<span class="nc" id="L1541">        return result.getErrorMessage();</span>
    }

    /**
     * Schema mapping for BulkUploadItemResult uploadedAt field.
     */
    @SchemaMapping(typeName = &quot;DocumentUploadResult&quot;, field = &quot;uploadedAt&quot;)
    public java.time.OffsetDateTime bulkUploadItemResultUploadedAt(com.ascentbusiness.dms_svc.dto.BulkUploadItemResult result) {
<span class="nc bnc" id="L1549" title="All 2 branches missed.">        if (result.getProcessEndTime() != null) {</span>
<span class="nc" id="L1550">            return result.getProcessEndTime().atOffset(java.time.ZoneOffset.UTC);</span>
        }
<span class="nc" id="L1552">        return java.time.OffsetDateTime.now();</span>
    }

    /**
     * Schema mapping for BulkUploadItemResult document field.
     */
    @SchemaMapping(typeName = &quot;DocumentUploadResult&quot;, field = &quot;document&quot;)
    public Document bulkUploadItemResultDocument(com.ascentbusiness.dms_svc.dto.BulkUploadItemResult result) {
<span class="nc" id="L1560">        return result.getDocument();</span>
    }

}
</pre><div class="footer"><span class="right">Created with <a href="http://www.jacoco.org/jacoco">JaCoCo</a> 0.8.10.202304240956</span></div></body></html>