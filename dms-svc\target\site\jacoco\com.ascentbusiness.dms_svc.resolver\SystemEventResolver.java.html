<?xml version="1.0" encoding="UTF-8"?><!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Strict//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-strict.dtd"><html xmlns="http://www.w3.org/1999/xhtml" lang="en"><head><meta http-equiv="Content-Type" content="text/html;charset=UTF-8"/><link rel="stylesheet" href="../jacoco-resources/report.css" type="text/css"/><link rel="shortcut icon" href="../jacoco-resources/report.gif" type="image/gif"/><title>SystemEventResolver.java</title><link rel="stylesheet" href="../jacoco-resources/prettify.css" type="text/css"/><script type="text/javascript" src="../jacoco-resources/prettify.js"></script></head><body onload="window['PR_TAB_WIDTH']=4;prettyPrint()"><div class="breadcrumb" id="breadcrumb"><span class="info"><a href="../jacoco-sessions.html" class="el_session">Sessions</a></span><a href="../index.html" class="el_report">dms-svc</a> &gt; <a href="index.source.html" class="el_package">com.ascentbusiness.dms_svc.resolver</a> &gt; <span class="el_source">SystemEventResolver.java</span></div><h1>SystemEventResolver.java</h1><pre class="source lang-java linenums">package com.ascentbusiness.dms_svc.resolver;

import com.ascentbusiness.dms_svc.dto.CleanupResult;
import com.ascentbusiness.dms_svc.entity.SystemEvent;
import com.ascentbusiness.dms_svc.entity.WebhookDelivery;
import com.ascentbusiness.dms_svc.enums.EventType;
import com.ascentbusiness.dms_svc.enums.EventCategory;
import com.ascentbusiness.dms_svc.service.SystemEventService;
import com.ascentbusiness.dms_svc.security.UserContext;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Sort;
import org.springframework.graphql.data.method.annotation.Argument;
import org.springframework.graphql.data.method.annotation.MutationMapping;
import org.springframework.graphql.data.method.annotation.QueryMapping;
import org.springframework.graphql.data.method.annotation.SchemaMapping;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.stereotype.Controller;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;

/**
 * GraphQL resolver for SystemEvent operations
 */
@Controller
<span class="fc" id="L31">@RequiredArgsConstructor</span>
<span class="fc" id="L32">@Slf4j</span>
public class SystemEventResolver {

    private final SystemEventService systemEventService;
    private final UserContext userContext;

    // ===== QUERIES =====

    @QueryMapping
    @PreAuthorize(&quot;hasAnyRole('ADMIN', 'MANAGER')&quot;)
    public SystemEvent getSystemEvent(@Argument Long id) {
<span class="nc" id="L43">        log.info(&quot;Getting system event with ID: {}&quot;, id);</span>
<span class="nc" id="L44">        return systemEventService.getSystemEventById(id);</span>
    }

    @QueryMapping
    @PreAuthorize(&quot;hasAnyRole('ADMIN', 'MANAGER')&quot;)
    public SystemEventPage getSystemEvents(@Argument EventPaginationInput pagination, @Argument EventFilterInput filter) {
<span class="nc" id="L50">        log.info(&quot;Getting system events with pagination: {} and filter: {}&quot;, pagination, filter);</span>
        
<span class="nc" id="L52">        Pageable pageable = createPageable(pagination);</span>
        Page&lt;SystemEvent&gt; page;
        
<span class="nc bnc" id="L55" title="All 6 branches missed.">        if (filter != null &amp;&amp; filter.getDateFrom() != null &amp;&amp; filter.getDateTo() != null) {</span>
<span class="nc" id="L56">            page = systemEventService.getEventsInDateRange(filter.getDateFrom(), filter.getDateTo(), pageable);</span>
        } else {
            // Get recent events if no date filter
<span class="nc" id="L59">            List&lt;SystemEvent&gt; events = systemEventService.getRecentEvents(24);</span>
<span class="nc" id="L60">            page = new org.springframework.data.domain.PageImpl&lt;&gt;(events, pageable, events.size());</span>
        }
        
<span class="nc" id="L63">        return SystemEventPage.builder()</span>
<span class="nc" id="L64">                .content(page.getContent())</span>
<span class="nc" id="L65">                .totalElements((int) page.getTotalElements())</span>
<span class="nc" id="L66">                .totalPages(page.getTotalPages())</span>
<span class="nc" id="L67">                .size(page.getSize())</span>
<span class="nc" id="L68">                .number(page.getNumber())</span>
<span class="nc" id="L69">                .first(page.isFirst())</span>
<span class="nc" id="L70">                .last(page.isLast())</span>
<span class="nc" id="L71">                .build();</span>
    }

    @QueryMapping
    @PreAuthorize(&quot;hasAnyRole('ADMIN', 'MANAGER')&quot;)
    public List&lt;SystemEvent&gt; getEventsByType(@Argument EventType eventType) {
<span class="nc" id="L77">        log.info(&quot;Getting events by type: {}&quot;, eventType);</span>
<span class="nc" id="L78">        return systemEventService.getEventsByType(eventType);</span>
    }

    @QueryMapping
    @PreAuthorize(&quot;hasAnyRole('ADMIN', 'MANAGER')&quot;)
    public List&lt;SystemEvent&gt; getEventsByCategory(@Argument EventCategory eventCategory) {
<span class="nc" id="L84">        log.info(&quot;Getting events by category: {}&quot;, eventCategory);</span>
<span class="nc" id="L85">        return systemEventService.getEventsByCategory(eventCategory);</span>
    }

    @QueryMapping
    @PreAuthorize(&quot;hasAnyRole('ADMIN', 'MANAGER', 'USER')&quot;)
    public SystemEventPage getEventsByActor(@Argument String actorUserId, @Argument EventPaginationInput pagination) {
<span class="nc" id="L91">        log.info(&quot;Getting events by actor: {}&quot;, actorUserId);</span>
<span class="nc bnc" id="L92" title="All 2 branches missed.">        String actualActor = actorUserId != null ? actorUserId : userContext.getUserId();</span>
<span class="nc" id="L93">        Pageable pageable = createPageable(pagination);</span>
<span class="nc" id="L94">        Page&lt;SystemEvent&gt; page = systemEventService.getEventsByActor(actualActor, pageable);</span>
        
<span class="nc" id="L96">        return SystemEventPage.builder()</span>
<span class="nc" id="L97">                .content(page.getContent())</span>
<span class="nc" id="L98">                .totalElements((int) page.getTotalElements())</span>
<span class="nc" id="L99">                .totalPages(page.getTotalPages())</span>
<span class="nc" id="L100">                .size(page.getSize())</span>
<span class="nc" id="L101">                .number(page.getNumber())</span>
<span class="nc" id="L102">                .first(page.isFirst())</span>
<span class="nc" id="L103">                .last(page.isLast())</span>
<span class="nc" id="L104">                .build();</span>
    }

    @QueryMapping
    @PreAuthorize(&quot;hasAnyRole('ADMIN', 'MANAGER')&quot;)
    public List&lt;SystemEvent&gt; getEventsByCorrelationId(@Argument String correlationId) {
<span class="nc" id="L110">        log.info(&quot;Getting events by correlation ID: {}&quot;, correlationId);</span>
<span class="nc" id="L111">        return systemEventService.getEventsByCorrelationId(correlationId);</span>
    }

    @QueryMapping
    @PreAuthorize(&quot;hasAnyRole('ADMIN', 'MANAGER')&quot;)
    public List&lt;SystemEvent&gt; getEventsBySourceEntity(@Argument String sourceEntityType, @Argument Long sourceEntityId) {
<span class="nc" id="L117">        log.info(&quot;Getting events by source entity: {}:{}&quot;, sourceEntityType, sourceEntityId);</span>
<span class="nc" id="L118">        return systemEventService.getEventsBySourceEntity(sourceEntityType, sourceEntityId);</span>
    }

    @QueryMapping
    @PreAuthorize(&quot;hasAnyRole('ADMIN', 'MANAGER')&quot;)
    public List&lt;SystemEvent&gt; getRecentEvents(@Argument Integer hoursBack) {
<span class="nc" id="L124">        log.info(&quot;Getting recent events for {} hours back&quot;, hoursBack);</span>
<span class="nc bnc" id="L125" title="All 2 branches missed.">        int actualHoursBack = hoursBack != null ? hoursBack : 24;</span>
<span class="nc" id="L126">        return systemEventService.getRecentEvents(actualHoursBack);</span>
    }

    @QueryMapping
    @PreAuthorize(&quot;hasAnyRole('ADMIN', 'MANAGER')&quot;)
    public List&lt;SystemEvent&gt; getPendingEvents() {
<span class="nc" id="L132">        log.info(&quot;Getting pending events&quot;);</span>
<span class="nc" id="L133">        return systemEventService.getPendingEvents();</span>
    }

    @QueryMapping
    @PreAuthorize(&quot;hasAnyRole('ADMIN', 'MANAGER')&quot;)
    public List&lt;SystemEvent&gt; getFailedEvents() {
<span class="nc" id="L139">        log.info(&quot;Getting failed events&quot;);</span>
<span class="nc" id="L140">        return systemEventService.getFailedEvents();</span>
    }

    @QueryMapping
    @PreAuthorize(&quot;hasAnyRole('ADMIN', 'MANAGER')&quot;)
    public SystemEventService.EventStatistics getEventStatistics() {
<span class="nc" id="L146">        log.info(&quot;Getting event statistics&quot;);</span>
<span class="nc" id="L147">        return systemEventService.getEventStatistics();</span>
    }

    @QueryMapping
    @PreAuthorize(&quot;hasAnyRole('ADMIN', 'MANAGER')&quot;)
    public List&lt;Object[]&gt; getHourlyEventCounts() {
<span class="nc" id="L153">        log.info(&quot;Getting hourly event counts&quot;);</span>
<span class="nc" id="L154">        return systemEventService.getHourlyEventCounts();</span>
    }

    @QueryMapping
    @PreAuthorize(&quot;hasAnyRole('ADMIN', 'MANAGER')&quot;)
    public List&lt;Object[]&gt; getDailyEventCounts() {
<span class="nc" id="L160">        log.info(&quot;Getting daily event counts&quot;);</span>
<span class="nc" id="L161">        return systemEventService.getDailyEventCounts();</span>
    }

    // ===== MUTATIONS =====

    @MutationMapping
    @PreAuthorize(&quot;hasAnyRole('ADMIN', 'MANAGER')&quot;)
    public SystemEvent createSystemEvent(@Argument SystemEventInput input) {
<span class="nc" id="L169">        log.info(&quot;Creating system event: {}&quot;, input.getEventName());</span>
        
<span class="nc" id="L171">        return systemEventService.createSystemEvent(</span>
<span class="nc" id="L172">                input.getEventType(),</span>
<span class="nc" id="L173">                input.getEventCategory(),</span>
<span class="nc bnc" id="L174" title="All 2 branches missed.">                input.getActorUserId() != null ? input.getActorUserId() : userContext.getUserId(),</span>
<span class="nc" id="L175">                input.getEventName(),</span>
<span class="nc" id="L176">                input.getSourceEntityType(),</span>
<span class="nc" id="L177">                input.getSourceEntityId(),</span>
<span class="nc" id="L178">                input.getEventData()</span>
        );
    }

    @MutationMapping
    @PreAuthorize(&quot;hasAnyRole('ADMIN', 'MANAGER')&quot;)
    public SystemEvent markEventAsProcessing(@Argument Long eventId) {
<span class="nc" id="L185">        log.info(&quot;Marking event as processing: {}&quot;, eventId);</span>
<span class="nc" id="L186">        return systemEventService.markEventAsProcessing(eventId);</span>
    }

    @MutationMapping
    @PreAuthorize(&quot;hasAnyRole('ADMIN', 'MANAGER')&quot;)
    public SystemEvent markEventAsCompleted(@Argument Long eventId) {
<span class="nc" id="L192">        log.info(&quot;Marking event as completed: {}&quot;, eventId);</span>
<span class="nc" id="L193">        return systemEventService.markEventAsCompleted(eventId);</span>
    }

    @MutationMapping
    @PreAuthorize(&quot;hasAnyRole('ADMIN', 'MANAGER')&quot;)
    public SystemEvent markEventAsFailed(@Argument Long eventId) {
<span class="nc" id="L199">        log.info(&quot;Marking event as failed: {}&quot;, eventId);</span>
<span class="nc" id="L200">        return systemEventService.markEventAsFailed(eventId);</span>
    }

    @MutationMapping
    @PreAuthorize(&quot;hasRole('ADMIN')&quot;)
    public CleanupResult cleanupOldEvents(@Argument Integer olderThanDays) {
<span class="nc" id="L206">        log.info(&quot;Cleaning up events older than {} days&quot;, olderThanDays);</span>
<span class="nc bnc" id="L207" title="All 2 branches missed.">        int actualDaysToKeep = olderThanDays != null ? olderThanDays : 90;</span>
<span class="nc" id="L208">        int deletedCount = systemEventService.cleanupOldEvents(actualDaysToKeep);</span>

<span class="nc" id="L210">        return CleanupResult.builder()</span>
<span class="nc" id="L211">                .success(true)</span>
<span class="nc" id="L212">                .deletedCount(deletedCount)</span>
<span class="nc" id="L213">                .message(String.format(&quot;Successfully cleaned up %d events older than %d days&quot;, deletedCount, actualDaysToKeep))</span>
<span class="nc" id="L214">                .build();</span>
    }

    // ===== FIELD RESOLVERS =====

    @SchemaMapping(typeName = &quot;SystemEvent&quot;, field = &quot;webhookDeliveries&quot;)
    public List&lt;WebhookDelivery&gt; getWebhookDeliveries(SystemEvent systemEvent) {
<span class="nc bnc" id="L221" title="All 2 branches missed.">        return systemEvent.getWebhookDeliveries() != null ? </span>
<span class="nc" id="L222">               systemEvent.getWebhookDeliveries().stream().toList() : List.of();</span>
    }

    @SchemaMapping(typeName = &quot;SystemEvent&quot;, field = &quot;isPending&quot;)
    public Boolean getIsPending(SystemEvent systemEvent) {
<span class="nc" id="L227">        return systemEvent.isPending();</span>
    }

    @SchemaMapping(typeName = &quot;SystemEvent&quot;, field = &quot;isCompleted&quot;)
    public Boolean getIsCompleted(SystemEvent systemEvent) {
<span class="nc" id="L232">        return systemEvent.isCompleted();</span>
    }

    @SchemaMapping(typeName = &quot;SystemEvent&quot;, field = &quot;isFailed&quot;)
    public Boolean getIsFailed(SystemEvent systemEvent) {
<span class="nc" id="L237">        return systemEvent.isFailed();</span>
    }

    @SchemaMapping(typeName = &quot;SystemEvent&quot;, field = &quot;isRecent&quot;)
    public Boolean getIsRecent(SystemEvent systemEvent) {
<span class="nc" id="L242">        return systemEvent.isRecent();</span>
    }

    @SchemaMapping(typeName = &quot;SystemEvent&quot;, field = &quot;eventAgeHours&quot;)
    public Long getEventAgeHours(SystemEvent systemEvent) {
<span class="nc" id="L247">        return systemEvent.getEventAgeHours();</span>
    }

    @SchemaMapping(typeName = &quot;SystemEvent&quot;, field = &quot;eventDisplayName&quot;)
    public String getEventDisplayName(SystemEvent systemEvent) {
<span class="nc" id="L252">        return systemEvent.getEventDisplayName();</span>
    }

    // ===== HELPER METHODS =====

    private Pageable createPageable(EventPaginationInput pagination) {
<span class="nc bnc" id="L258" title="All 2 branches missed.">        if (pagination == null) {</span>
<span class="nc" id="L259">            return PageRequest.of(0, 10, Sort.by(Sort.Direction.DESC, &quot;eventTimestamp&quot;));</span>
        }
        
<span class="nc bnc" id="L262" title="All 2 branches missed.">        Sort.Direction direction = &quot;ASC&quot;.equalsIgnoreCase(pagination.getSortDirection()) ? </span>
<span class="nc" id="L263">                                  Sort.Direction.ASC : Sort.Direction.DESC;</span>
<span class="nc bnc" id="L264" title="All 2 branches missed.">        Sort sort = Sort.by(direction, pagination.getSortBy() != null ? pagination.getSortBy() : &quot;eventTimestamp&quot;);</span>
        
<span class="nc" id="L266">        return PageRequest.of(</span>
<span class="nc bnc" id="L267" title="All 2 branches missed.">                pagination.getPage() != null ? pagination.getPage() : 0,</span>
<span class="nc bnc" id="L268" title="All 2 branches missed.">                pagination.getSize() != null ? pagination.getSize() : 10,</span>
                sort
        );
    }

    // ===== INPUT/OUTPUT CLASSES =====

<span class="nc" id="L275">    public static class SystemEventInput {</span>
        private EventType eventType;
        private EventCategory eventCategory;
        private String eventName;
        private String sourceEntityType;
        private Long sourceEntityId;
        private String actorUserId;
        private Map&lt;String, Object&gt; eventData;
        
        // Getters and setters
<span class="nc" id="L285">        public EventType getEventType() { return eventType; }</span>
<span class="nc" id="L286">        public void setEventType(EventType eventType) { this.eventType = eventType; }</span>
<span class="nc" id="L287">        public EventCategory getEventCategory() { return eventCategory; }</span>
<span class="nc" id="L288">        public void setEventCategory(EventCategory eventCategory) { this.eventCategory = eventCategory; }</span>
<span class="nc" id="L289">        public String getEventName() { return eventName; }</span>
<span class="nc" id="L290">        public void setEventName(String eventName) { this.eventName = eventName; }</span>
<span class="nc" id="L291">        public String getSourceEntityType() { return sourceEntityType; }</span>
<span class="nc" id="L292">        public void setSourceEntityType(String sourceEntityType) { this.sourceEntityType = sourceEntityType; }</span>
<span class="nc" id="L293">        public Long getSourceEntityId() { return sourceEntityId; }</span>
<span class="nc" id="L294">        public void setSourceEntityId(Long sourceEntityId) { this.sourceEntityId = sourceEntityId; }</span>
<span class="nc" id="L295">        public String getActorUserId() { return actorUserId; }</span>
<span class="nc" id="L296">        public void setActorUserId(String actorUserId) { this.actorUserId = actorUserId; }</span>
<span class="nc" id="L297">        public Map&lt;String, Object&gt; getEventData() { return eventData; }</span>
<span class="nc" id="L298">        public void setEventData(Map&lt;String, Object&gt; eventData) { this.eventData = eventData; }</span>
    }

<span class="nc" id="L301">    public static class EventFilterInput {</span>
        private List&lt;EventType&gt; eventTypes;
        private List&lt;EventCategory&gt; eventCategories;
        private String actorUserId;
        private String sourceEntityType;
        private LocalDateTime dateFrom;
        private LocalDateTime dateTo;
        
        // Getters and setters
<span class="nc" id="L310">        public List&lt;EventType&gt; getEventTypes() { return eventTypes; }</span>
<span class="nc" id="L311">        public void setEventTypes(List&lt;EventType&gt; eventTypes) { this.eventTypes = eventTypes; }</span>
<span class="nc" id="L312">        public List&lt;EventCategory&gt; getEventCategories() { return eventCategories; }</span>
<span class="nc" id="L313">        public void setEventCategories(List&lt;EventCategory&gt; eventCategories) { this.eventCategories = eventCategories; }</span>
<span class="nc" id="L314">        public String getActorUserId() { return actorUserId; }</span>
<span class="nc" id="L315">        public void setActorUserId(String actorUserId) { this.actorUserId = actorUserId; }</span>
<span class="nc" id="L316">        public String getSourceEntityType() { return sourceEntityType; }</span>
<span class="nc" id="L317">        public void setSourceEntityType(String sourceEntityType) { this.sourceEntityType = sourceEntityType; }</span>
<span class="nc" id="L318">        public LocalDateTime getDateFrom() { return dateFrom; }</span>
<span class="nc" id="L319">        public void setDateFrom(LocalDateTime dateFrom) { this.dateFrom = dateFrom; }</span>
<span class="nc" id="L320">        public LocalDateTime getDateTo() { return dateTo; }</span>
<span class="nc" id="L321">        public void setDateTo(LocalDateTime dateTo) { this.dateTo = dateTo; }</span>
    }

<span class="nc" id="L324">    public static class EventPaginationInput {</span>
        private Integer page;
        private Integer size;
        private String sortBy;
        private String sortDirection;
        
        // Getters and setters
<span class="nc" id="L331">        public Integer getPage() { return page; }</span>
<span class="nc" id="L332">        public void setPage(Integer page) { this.page = page; }</span>
<span class="nc" id="L333">        public Integer getSize() { return size; }</span>
<span class="nc" id="L334">        public void setSize(Integer size) { this.size = size; }</span>
<span class="nc" id="L335">        public String getSortBy() { return sortBy; }</span>
<span class="nc" id="L336">        public void setSortBy(String sortBy) { this.sortBy = sortBy; }</span>
<span class="nc" id="L337">        public String getSortDirection() { return sortDirection; }</span>
<span class="nc" id="L338">        public void setSortDirection(String sortDirection) { this.sortDirection = sortDirection; }</span>
    }

<span class="nc" id="L341">    public static class SystemEventPage {</span>
        private List&lt;SystemEvent&gt; content;
        private Integer totalElements;
        private Integer totalPages;
        private Integer size;
        private Integer number;
        private Boolean first;
        private Boolean last;

        public static SystemEventPageBuilder builder() {
<span class="nc" id="L351">            return new SystemEventPageBuilder();</span>
        }

<span class="nc" id="L354">        public static class SystemEventPageBuilder {</span>
            private List&lt;SystemEvent&gt; content;
            private Integer totalElements;
            private Integer totalPages;
            private Integer size;
            private Integer number;
            private Boolean first;
            private Boolean last;

            public SystemEventPageBuilder content(List&lt;SystemEvent&gt; content) {
<span class="nc" id="L364">                this.content = content;</span>
<span class="nc" id="L365">                return this;</span>
            }

            public SystemEventPageBuilder totalElements(Integer totalElements) {
<span class="nc" id="L369">                this.totalElements = totalElements;</span>
<span class="nc" id="L370">                return this;</span>
            }

            public SystemEventPageBuilder totalPages(Integer totalPages) {
<span class="nc" id="L374">                this.totalPages = totalPages;</span>
<span class="nc" id="L375">                return this;</span>
            }

            public SystemEventPageBuilder size(Integer size) {
<span class="nc" id="L379">                this.size = size;</span>
<span class="nc" id="L380">                return this;</span>
            }

            public SystemEventPageBuilder number(Integer number) {
<span class="nc" id="L384">                this.number = number;</span>
<span class="nc" id="L385">                return this;</span>
            }

            public SystemEventPageBuilder first(Boolean first) {
<span class="nc" id="L389">                this.first = first;</span>
<span class="nc" id="L390">                return this;</span>
            }

            public SystemEventPageBuilder last(Boolean last) {
<span class="nc" id="L394">                this.last = last;</span>
<span class="nc" id="L395">                return this;</span>
            }

            public SystemEventPage build() {
<span class="nc" id="L399">                SystemEventPage page = new SystemEventPage();</span>
<span class="nc" id="L400">                page.content = this.content;</span>
<span class="nc" id="L401">                page.totalElements = this.totalElements;</span>
<span class="nc" id="L402">                page.totalPages = this.totalPages;</span>
<span class="nc" id="L403">                page.size = this.size;</span>
<span class="nc" id="L404">                page.number = this.number;</span>
<span class="nc" id="L405">                page.first = this.first;</span>
<span class="nc" id="L406">                page.last = this.last;</span>
<span class="nc" id="L407">                return page;</span>
            }
        }

        // Getters
<span class="nc" id="L412">        public List&lt;SystemEvent&gt; getContent() { return content; }</span>
<span class="nc" id="L413">        public Integer getTotalElements() { return totalElements; }</span>
<span class="nc" id="L414">        public Integer getTotalPages() { return totalPages; }</span>
<span class="nc" id="L415">        public Integer getSize() { return size; }</span>
<span class="nc" id="L416">        public Integer getNumber() { return number; }</span>
<span class="nc" id="L417">        public Boolean getFirst() { return first; }</span>
<span class="nc" id="L418">        public Boolean getLast() { return last; }</span>
    }
}
</pre><div class="footer"><span class="right">Created with <a href="http://www.jacoco.org/jacoco">JaCoCo</a> 0.8.10.202304240956</span></div></body></html>