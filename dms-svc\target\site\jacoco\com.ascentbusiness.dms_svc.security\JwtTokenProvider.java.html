<?xml version="1.0" encoding="UTF-8"?><!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Strict//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-strict.dtd"><html xmlns="http://www.w3.org/1999/xhtml" lang="en"><head><meta http-equiv="Content-Type" content="text/html;charset=UTF-8"/><link rel="stylesheet" href="../jacoco-resources/report.css" type="text/css"/><link rel="shortcut icon" href="../jacoco-resources/report.gif" type="image/gif"/><title>JwtTokenProvider.java</title><link rel="stylesheet" href="../jacoco-resources/prettify.css" type="text/css"/><script type="text/javascript" src="../jacoco-resources/prettify.js"></script></head><body onload="window['PR_TAB_WIDTH']=4;prettyPrint()"><div class="breadcrumb" id="breadcrumb"><span class="info"><a href="../jacoco-sessions.html" class="el_session">Sessions</a></span><a href="../index.html" class="el_report">dms-svc</a> &gt; <a href="index.source.html" class="el_package">com.ascentbusiness.dms_svc.security</a> &gt; <span class="el_source">JwtTokenProvider.java</span></div><h1>JwtTokenProvider.java</h1><pre class="source lang-java linenums">package com.ascentbusiness.dms_svc.security;

import com.ascentbusiness.dms_svc.exception.InvalidTokenException;
import io.jsonwebtoken.*;
import io.jsonwebtoken.security.Keys;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.security.core.Authentication;
import org.springframework.stereotype.Component;

import javax.crypto.SecretKey;
import java.util.Date;
import java.util.List;

@Component
<span class="fc" id="L17">public class JwtTokenProvider {</span>

<span class="fc" id="L19">    private static final Logger logger = LoggerFactory.getLogger(JwtTokenProvider.class);</span>

    @Value(&quot;${dms.jwt.secret}&quot;)
    private String jwtSecret;

    @Value(&quot;${dms.jwt.expiration}&quot;)
    private int jwtExpirationInMs;

    private SecretKey getSigningKey() {
<span class="nc" id="L28">        return Keys.hmacShaKeyFor(jwtSecret.getBytes());</span>
    }

    public String generateToken(Authentication authentication) {
<span class="nc" id="L32">        UserPrincipal userPrincipal = (UserPrincipal) authentication.getPrincipal();</span>
<span class="nc" id="L33">        Date expiryDate = new Date(System.currentTimeMillis() + jwtExpirationInMs);</span>

<span class="nc" id="L35">        return Jwts.builder()</span>
<span class="nc" id="L36">                .claim(&quot;sub&quot;, userPrincipal.getUsername())</span>
<span class="nc" id="L37">                .issuedAt(new Date())</span>
<span class="nc" id="L38">                .expiration(expiryDate)</span>
<span class="nc" id="L39">                .signWith(getSigningKey())</span>
<span class="nc" id="L40">                .compact();</span>
    }

    public String generateTokenFromUsername(String username) {
<span class="nc" id="L44">        Date expiryDate = new Date(System.currentTimeMillis() + jwtExpirationInMs);</span>

<span class="nc" id="L46">        return Jwts.builder()</span>
<span class="nc" id="L47">                .claim(&quot;sub&quot;, username)</span>
<span class="nc" id="L48">                .issuedAt(new Date())</span>
<span class="nc" id="L49">                .expiration(expiryDate)</span>
<span class="nc" id="L50">                .signWith(getSigningKey())</span>
<span class="nc" id="L51">                .compact();</span>
    }

    public String generateTokenWithRolesAndPermissions(String username, List&lt;String&gt; roles, List&lt;String&gt; permissions) {
<span class="nc" id="L55">        Date expiryDate = new Date(System.currentTimeMillis() + jwtExpirationInMs);</span>

<span class="nc" id="L57">        return Jwts.builder()</span>
<span class="nc" id="L58">                .claim(&quot;sub&quot;, username)</span>
<span class="nc" id="L59">                .claim(&quot;roles&quot;, roles)</span>
<span class="nc" id="L60">                .claim(&quot;permissions&quot;, permissions)</span>
<span class="nc" id="L61">                .issuedAt(new Date())</span>
<span class="nc" id="L62">                .expiration(expiryDate)</span>
<span class="nc" id="L63">                .signWith(getSigningKey())</span>
<span class="nc" id="L64">                .compact();</span>
    }

    public String getUsernameFromToken(String token) {
<span class="nc" id="L68">        Claims claims = Jwts.parser()</span>
<span class="nc" id="L69">                .verifyWith(getSigningKey())</span>
<span class="nc" id="L70">                .build()</span>
<span class="nc" id="L71">                .parseSignedClaims(token)</span>
<span class="nc" id="L72">                .getPayload();</span>

<span class="nc" id="L74">        return claims.getSubject();</span>
    }

    @SuppressWarnings(&quot;unchecked&quot;)
    public List&lt;String&gt; getRolesFromToken(String token) {
        try {
<span class="nc" id="L80">            Claims claims = Jwts.parser()</span>
<span class="nc" id="L81">                    .verifyWith(getSigningKey())</span>
<span class="nc" id="L82">                    .build()</span>
<span class="nc" id="L83">                    .parseSignedClaims(token)</span>
<span class="nc" id="L84">                    .getPayload();</span>

<span class="nc" id="L86">            List&lt;String&gt; roles = (List&lt;String&gt;) claims.get(&quot;roles&quot;, List.class);</span>
<span class="nc bnc" id="L87" title="All 2 branches missed.">            return roles != null ? roles : List.of();</span>
<span class="nc" id="L88">        } catch (Exception e) {</span>
<span class="nc" id="L89">            logger.warn(&quot;Could not extract roles from token&quot;, e);</span>
<span class="nc" id="L90">            return List.of();</span>
        }
    }

    @SuppressWarnings(&quot;unchecked&quot;)
    public List&lt;String&gt; getPermissionsFromToken(String token) {
        try {
<span class="nc" id="L97">            Claims claims = Jwts.parser()</span>
<span class="nc" id="L98">                    .verifyWith(getSigningKey())</span>
<span class="nc" id="L99">                    .build()</span>
<span class="nc" id="L100">                    .parseSignedClaims(token)</span>
<span class="nc" id="L101">                    .getPayload();</span>

<span class="nc" id="L103">            List&lt;String&gt; permissions = (List&lt;String&gt;) claims.get(&quot;permissions&quot;, List.class);</span>
<span class="nc bnc" id="L104" title="All 2 branches missed.">            return permissions != null ? permissions : List.of();</span>
<span class="nc" id="L105">        } catch (Exception e) {</span>
<span class="nc" id="L106">            logger.warn(&quot;Could not extract permissions from token&quot;, e);</span>
<span class="nc" id="L107">            return List.of();</span>
        }
    }

    public Date getExpirationDateFromToken(String token) {
<span class="nc" id="L112">        Claims claims = Jwts.parser()</span>
<span class="nc" id="L113">                .verifyWith(getSigningKey())</span>
<span class="nc" id="L114">                .build()</span>
<span class="nc" id="L115">                .parseSignedClaims(token)</span>
<span class="nc" id="L116">                .getPayload();</span>

<span class="nc" id="L118">        return claims.getExpiration();</span>
    }

    public boolean validateToken(String authToken) {
        try {
<span class="nc" id="L123">            Jwts.parser()</span>
<span class="nc" id="L124">                .verifyWith(getSigningKey())</span>
<span class="nc" id="L125">                .build()</span>
<span class="nc" id="L126">                .parseSignedClaims(authToken);</span>
<span class="nc" id="L127">            return true;</span>
<span class="nc" id="L128">        } catch (MalformedJwtException ex) {</span>
            // Don't log here - let the calling component decide how to handle/log
<span class="nc" id="L130">            throw new InvalidTokenException(&quot;Invalid or malformed JWT token&quot;, ex);</span>
<span class="nc" id="L131">        } catch (ExpiredJwtException ex) {</span>
            // Don't log here - let the calling component decide how to handle/log
<span class="nc" id="L133">            throw new InvalidTokenException(&quot;JWT token has expired&quot;, ex);</span>
<span class="nc" id="L134">        } catch (UnsupportedJwtException ex) {</span>
            // Don't log here - let the calling component decide how to handle/log
<span class="nc" id="L136">            throw new InvalidTokenException(&quot;Unsupported JWT token&quot;, ex);</span>
<span class="nc" id="L137">        } catch (IllegalArgumentException ex) {</span>
            // Don't log here - let the calling component decide how to handle/log
<span class="nc" id="L139">            throw new InvalidTokenException(&quot;JWT token is empty or null&quot;, ex);</span>
<span class="nc" id="L140">        } catch (Exception ex) {</span>
            // Don't log here - let the calling component decide how to handle/log
<span class="nc" id="L142">            throw new InvalidTokenException(&quot;JWT token validation failed&quot;, ex);</span>
        }
    }
}
</pre><div class="footer"><span class="right">Created with <a href="http://www.jacoco.org/jacoco">JaCoCo</a> 0.8.10.202304240956</span></div></body></html>