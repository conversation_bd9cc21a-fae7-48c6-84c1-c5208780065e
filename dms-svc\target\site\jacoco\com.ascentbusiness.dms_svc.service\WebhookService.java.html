<?xml version="1.0" encoding="UTF-8"?><!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Strict//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-strict.dtd"><html xmlns="http://www.w3.org/1999/xhtml" lang="en"><head><meta http-equiv="Content-Type" content="text/html;charset=UTF-8"/><link rel="stylesheet" href="../jacoco-resources/report.css" type="text/css"/><link rel="shortcut icon" href="../jacoco-resources/report.gif" type="image/gif"/><title>WebhookService.java</title><link rel="stylesheet" href="../jacoco-resources/prettify.css" type="text/css"/><script type="text/javascript" src="../jacoco-resources/prettify.js"></script></head><body onload="window['PR_TAB_WIDTH']=4;prettyPrint()"><div class="breadcrumb" id="breadcrumb"><span class="info"><a href="../jacoco-sessions.html" class="el_session">Sessions</a></span><a href="../index.html" class="el_report">dms-svc</a> &gt; <a href="index.source.html" class="el_package">com.ascentbusiness.dms_svc.service</a> &gt; <span class="el_source">WebhookService.java</span></div><h1>WebhookService.java</h1><pre class="source lang-java linenums">package com.ascentbusiness.dms_svc.service;

import com.ascentbusiness.dms_svc.entity.WebhookEndpoint;
import com.ascentbusiness.dms_svc.entity.WebhookDelivery;
import com.ascentbusiness.dms_svc.entity.SystemEvent;
import com.ascentbusiness.dms_svc.enums.DeliveryStatus;
import com.ascentbusiness.dms_svc.enums.WebhookAuthType;
import com.ascentbusiness.dms_svc.repository.WebhookEndpointRepository;
import com.ascentbusiness.dms_svc.repository.WebhookDeliveryRepository;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.http.*;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.client.RestTemplate;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;
import java.util.UUID;

/**
 * Service for managing webhooks and webhook deliveries
 */
@Service
<span class="fc" id="L30">@RequiredArgsConstructor</span>
<span class="fc" id="L31">@Slf4j</span>
@Transactional
public class WebhookService {

    private final WebhookEndpointRepository webhookEndpointRepository;
    private final WebhookDeliveryRepository webhookDeliveryRepository;
    private final AuditService auditService;
    private final RestTemplate restTemplate;
    private final ObjectMapper objectMapper;

    /**
     * Create a new webhook endpoint
     */
    public WebhookEndpoint createWebhookEndpoint(WebhookEndpoint webhook, String createdBy) {
<span class="nc" id="L45">        log.info(&quot;Creating webhook endpoint: {} by user: {}&quot;, webhook.getName(), createdBy);</span>
        
        // Validate webhook
<span class="nc" id="L48">        validateWebhookEndpoint(webhook);</span>
        
        // Set audit fields
<span class="nc" id="L51">        webhook.setCreatedBy(createdBy);</span>
<span class="nc" id="L52">        webhook.setCreatedDate(LocalDateTime.now());</span>
<span class="nc" id="L53">        webhook.setLastModifiedDate(LocalDateTime.now());</span>
<span class="nc" id="L54">        webhook.setLastModifiedBy(createdBy);</span>
        
        // Generate verification token and secret key
<span class="nc" id="L57">        webhook.generateVerificationToken();</span>
<span class="nc" id="L58">        webhook.generateSecretKey();</span>
        
<span class="nc" id="L60">        WebhookEndpoint saved = webhookEndpointRepository.save(webhook);</span>
        
        // Create audit log
<span class="nc" id="L63">        auditService.logWebhookEndpointCreated(saved, createdBy);</span>
        
<span class="nc" id="L65">        log.info(&quot;Created webhook endpoint with ID: {}&quot;, saved.getId());</span>
<span class="nc" id="L66">        return saved;</span>
    }

    /**
     * Update webhook endpoint
     */
    public WebhookEndpoint updateWebhookEndpoint(Long id, WebhookEndpoint updatedWebhook, String modifiedBy) {
<span class="nc" id="L73">        log.info(&quot;Updating webhook endpoint ID: {} by user: {}&quot;, id, modifiedBy);</span>
        
<span class="nc" id="L75">        WebhookEndpoint existing = getWebhookEndpointById(id);</span>
        
        // Validate updated webhook
<span class="nc" id="L78">        validateWebhookEndpoint(updatedWebhook);</span>
        
        // Update fields
<span class="nc" id="L81">        existing.setName(updatedWebhook.getName());</span>
<span class="nc" id="L82">        existing.setDescription(updatedWebhook.getDescription());</span>
<span class="nc" id="L83">        existing.setUrl(updatedWebhook.getUrl());</span>
<span class="nc" id="L84">        existing.setHttpMethod(updatedWebhook.getHttpMethod());</span>
<span class="nc" id="L85">        existing.setContentType(updatedWebhook.getContentType());</span>
<span class="nc" id="L86">        existing.setTimeoutSeconds(updatedWebhook.getTimeoutSeconds());</span>
<span class="nc" id="L87">        existing.setAuthType(updatedWebhook.getAuthType());</span>
<span class="nc" id="L88">        existing.setAuthConfig(updatedWebhook.getAuthConfig());</span>
<span class="nc" id="L89">        existing.setCustomHeaders(updatedWebhook.getCustomHeaders());</span>
<span class="nc" id="L90">        existing.setPayloadTemplate(updatedWebhook.getPayloadTemplate());</span>
<span class="nc" id="L91">        existing.setEventTypes(updatedWebhook.getEventTypes());</span>
<span class="nc" id="L92">        existing.setEventFilters(updatedWebhook.getEventFilters());</span>
<span class="nc" id="L93">        existing.setMaxRetries(updatedWebhook.getMaxRetries());</span>
<span class="nc" id="L94">        existing.setRetryDelaySeconds(updatedWebhook.getRetryDelaySeconds());</span>
<span class="nc" id="L95">        existing.setExponentialBackoff(updatedWebhook.getExponentialBackoff());</span>
<span class="nc" id="L96">        existing.setRateLimitPerMinute(updatedWebhook.getRateLimitPerMinute());</span>
<span class="nc" id="L97">        existing.setRateLimitPerHour(updatedWebhook.getRateLimitPerHour());</span>
<span class="nc" id="L98">        existing.setLastModifiedBy(modifiedBy);</span>
<span class="nc" id="L99">        existing.setLastModifiedDate(LocalDateTime.now());</span>
        
        // If URL changed, mark as unverified
<span class="nc bnc" id="L102" title="All 2 branches missed.">        if (!existing.getUrl().equals(updatedWebhook.getUrl())) {</span>
<span class="nc" id="L103">            existing.setIsVerified(false);</span>
<span class="nc" id="L104">            existing.generateVerificationToken();</span>
        }
        
<span class="nc" id="L107">        WebhookEndpoint saved = webhookEndpointRepository.save(existing);</span>
        
        // Create audit log
<span class="nc" id="L110">        auditService.logWebhookEndpointUpdated(saved, modifiedBy);</span>
        
<span class="nc" id="L112">        log.info(&quot;Updated webhook endpoint ID: {}&quot;, saved.getId());</span>
<span class="nc" id="L113">        return saved;</span>
    }

    /**
     * Get webhook endpoint by ID
     */
    @Transactional(readOnly = true)
    public WebhookEndpoint getWebhookEndpointById(Long id) {
<span class="nc" id="L121">        return webhookEndpointRepository.findById(id)</span>
<span class="nc" id="L122">                .orElseThrow(() -&gt; new RuntimeException(&quot;Webhook endpoint not found with ID: &quot; + id));</span>
    }

    /**
     * Get all active webhook endpoints
     */
    @Transactional(readOnly = true)
    public List&lt;WebhookEndpoint&gt; getActiveWebhookEndpoints() {
<span class="nc" id="L130">        return webhookEndpointRepository.findByIsActiveTrueAndIsVerifiedTrue();</span>
    }

    /**
     * Get webhook endpoints by creator
     */
    @Transactional(readOnly = true)
    public Page&lt;WebhookEndpoint&gt; getWebhookEndpointsByCreator(String createdBy, Pageable pageable) {
<span class="nc" id="L138">        return webhookEndpointRepository.findByCreatedBy(createdBy, pageable);</span>
    }

    /**
     * Get webhook endpoints that listen for specific event type
     */
    @Transactional(readOnly = true)
    public List&lt;WebhookEndpoint&gt; getWebhookEndpointsForEvent(String eventType) {
<span class="nc" id="L146">        return webhookEndpointRepository.findByEventType(&quot;\&quot;&quot; + eventType + &quot;\&quot;&quot;);</span>
    }

    /**
     * Verify webhook endpoint
     */
    public WebhookEndpoint verifyWebhookEndpoint(String verificationToken) {
<span class="nc" id="L153">        log.info(&quot;Verifying webhook endpoint with token: {}&quot;, verificationToken);</span>
        
<span class="nc" id="L155">        WebhookEndpoint webhook = webhookEndpointRepository.findByVerificationToken(verificationToken)</span>
<span class="nc" id="L156">                .orElseThrow(() -&gt; new RuntimeException(&quot;Invalid verification token&quot;));</span>
        
<span class="nc" id="L158">        webhook.setIsVerified(true);</span>
<span class="nc" id="L159">        webhook.setVerificationToken(null); // Clear token after verification</span>
<span class="nc" id="L160">        webhook.setLastModifiedDate(LocalDateTime.now());</span>
        
<span class="nc" id="L162">        WebhookEndpoint saved = webhookEndpointRepository.save(webhook);</span>
        
        // Create audit log
<span class="nc" id="L165">        auditService.logWebhookEndpointVerified(saved);</span>
        
<span class="nc" id="L167">        log.info(&quot;Verified webhook endpoint ID: {}&quot;, saved.getId());</span>
<span class="nc" id="L168">        return saved;</span>
    }

    /**
     * Activate webhook endpoint
     */
    public WebhookEndpoint activateWebhookEndpoint(Long id, String activatedBy) {
<span class="nc" id="L175">        log.info(&quot;Activating webhook endpoint ID: {} by user: {}&quot;, id, activatedBy);</span>
        
<span class="nc" id="L177">        WebhookEndpoint webhook = getWebhookEndpointById(id);</span>
<span class="nc" id="L178">        webhook.setIsActive(true);</span>
<span class="nc" id="L179">        webhook.setLastModifiedBy(activatedBy);</span>
<span class="nc" id="L180">        webhook.setLastModifiedDate(LocalDateTime.now());</span>
        
<span class="nc" id="L182">        WebhookEndpoint saved = webhookEndpointRepository.save(webhook);</span>
        
        // Create audit log
<span class="nc" id="L185">        auditService.logWebhookEndpointActivated(saved, activatedBy);</span>
        
<span class="nc" id="L187">        return saved;</span>
    }

    /**
     * Deactivate webhook endpoint
     */
    public WebhookEndpoint deactivateWebhookEndpoint(Long id, String deactivatedBy) {
<span class="nc" id="L194">        log.info(&quot;Deactivating webhook endpoint ID: {} by user: {}&quot;, id, deactivatedBy);</span>
        
<span class="nc" id="L196">        WebhookEndpoint webhook = getWebhookEndpointById(id);</span>
<span class="nc" id="L197">        webhook.setIsActive(false);</span>
<span class="nc" id="L198">        webhook.setLastModifiedBy(deactivatedBy);</span>
<span class="nc" id="L199">        webhook.setLastModifiedDate(LocalDateTime.now());</span>
        
<span class="nc" id="L201">        WebhookEndpoint saved = webhookEndpointRepository.save(webhook);</span>
        
        // Create audit log
<span class="nc" id="L204">        auditService.logWebhookEndpointDeactivated(saved, deactivatedBy);</span>
        
<span class="nc" id="L206">        return saved;</span>
    }

    /**
     * Deliver webhook for system event
     */
    public void deliverWebhook(SystemEvent systemEvent) {
<span class="nc" id="L213">        log.info(&quot;Delivering webhooks for event: {} (ID: {})&quot;, systemEvent.getEventType(), systemEvent.getId());</span>
        
<span class="nc" id="L215">        List&lt;WebhookEndpoint&gt; endpoints = getWebhookEndpointsForEvent(systemEvent.getEventType().name());</span>
        
<span class="nc bnc" id="L217" title="All 2 branches missed.">        for (WebhookEndpoint endpoint : endpoints) {</span>
<span class="nc bnc" id="L218" title="All 2 branches missed.">            if (shouldDeliverToEndpoint(endpoint, systemEvent)) {</span>
<span class="nc" id="L219">                scheduleWebhookDelivery(endpoint, systemEvent);</span>
            }
<span class="nc" id="L221">        }</span>
<span class="nc" id="L222">    }</span>

    /**
     * Schedule webhook delivery
     */
    public WebhookDelivery scheduleWebhookDelivery(WebhookEndpoint endpoint, SystemEvent systemEvent) {
<span class="nc" id="L228">        log.info(&quot;Scheduling webhook delivery to endpoint: {} for event: {}&quot;, endpoint.getName(), systemEvent.getId());</span>
        
<span class="nc" id="L230">        WebhookDelivery delivery = WebhookDelivery.builder()</span>
<span class="nc" id="L231">                .webhookEndpoint(endpoint)</span>
<span class="nc" id="L232">                .systemEvent(systemEvent)</span>
<span class="nc" id="L233">                .deliveryAttempt(1)</span>
<span class="nc" id="L234">                .deliveryStatus(DeliveryStatus.PENDING)</span>
<span class="nc" id="L235">                .scheduledDate(LocalDateTime.now())</span>
<span class="nc" id="L236">                .correlationId(UUID.randomUUID().toString())</span>
<span class="nc" id="L237">                .build();</span>
        
<span class="nc" id="L239">        WebhookDelivery saved = webhookDeliveryRepository.save(delivery);</span>
        
        // Attempt immediate delivery
<span class="nc" id="L242">        attemptWebhookDelivery(saved);</span>
        
<span class="nc" id="L244">        return saved;</span>
    }

    /**
     * Attempt webhook delivery
     */
    public void attemptWebhookDelivery(WebhookDelivery delivery) {
<span class="nc" id="L251">        log.info(&quot;Attempting webhook delivery ID: {} (attempt {})&quot;, delivery.getId(), delivery.getDeliveryAttempt());</span>
        
        try {
<span class="nc" id="L254">            delivery.markAsStarted();</span>
<span class="nc" id="L255">            webhookDeliveryRepository.save(delivery);</span>
            
            // Prepare request
<span class="nc" id="L258">            HttpHeaders headers = prepareHeaders(delivery.getWebhookEndpoint());</span>
<span class="nc" id="L259">            String payload = preparePayload(delivery.getWebhookEndpoint(), delivery.getSystemEvent());</span>
            
<span class="nc" id="L261">            delivery.setRequestPayload(payload);</span>
<span class="nc" id="L262">            delivery.setRequestHeaders(objectMapper.valueToTree(headers.toSingleValueMap()));</span>
            
<span class="nc" id="L264">            HttpEntity&lt;String&gt; request = new HttpEntity&lt;&gt;(payload, headers);</span>
            
            // Make HTTP request
<span class="nc" id="L267">            ResponseEntity&lt;String&gt; response = restTemplate.exchange(</span>
<span class="nc" id="L268">                    delivery.getWebhookEndpoint().getUrl(),</span>
<span class="nc" id="L269">                    HttpMethod.valueOf(delivery.getWebhookEndpoint().getHttpMethod()),</span>
                    request,
                    String.class
            );
            
            // Handle successful response
<span class="nc" id="L275">            delivery.markAsSuccessful(response.getStatusCode().value(), response.getBody());</span>
<span class="nc" id="L276">            delivery.setResponseHeaders(objectMapper.valueToTree(response.getHeaders().toSingleValueMap()));</span>
            
            // Update endpoint success count
<span class="nc" id="L279">            delivery.getWebhookEndpoint().recordSuccess();</span>
<span class="nc" id="L280">            webhookEndpointRepository.save(delivery.getWebhookEndpoint());</span>
            
<span class="nc" id="L282">            log.info(&quot;Successfully delivered webhook ID: {}&quot;, delivery.getId());</span>
            
<span class="nc" id="L284">        } catch (Exception e) {</span>
<span class="nc" id="L285">            log.error(&quot;Failed to deliver webhook ID: {}&quot;, delivery.getId(), e);</span>
            
            // Handle failed delivery
<span class="nc" id="L288">            delivery.markAsFailed(e.getMessage(), &quot;HTTP_ERROR&quot;);</span>
            
            // Update endpoint failure count
<span class="nc" id="L291">            delivery.getWebhookEndpoint().recordFailure(e.getMessage());</span>
<span class="nc" id="L292">            webhookEndpointRepository.save(delivery.getWebhookEndpoint());</span>
            
            // Schedule retry if applicable
<span class="nc bnc" id="L295" title="All 2 branches missed.">            if (shouldRetryDelivery(delivery)) {</span>
<span class="nc" id="L296">                scheduleRetryDelivery(delivery);</span>
            }
        } finally {
<span class="nc" id="L299">            webhookDeliveryRepository.save(delivery);</span>
        }
<span class="nc" id="L301">    }</span>

    /**
     * Get webhook delivery statistics
     */
    @Transactional(readOnly = true)
    public WebhookStatistics getWebhookStatistics() {
<span class="nc" id="L308">        Object[] stats = webhookEndpointRepository.getWebhookStatistics();</span>
<span class="nc" id="L309">        long totalEndpoints = webhookEndpointRepository.countByIsActiveTrue();</span>
<span class="nc" id="L310">        long verifiedEndpoints = webhookEndpointRepository.countByIsVerifiedTrue();</span>
        
<span class="nc" id="L312">        return WebhookStatistics.builder()</span>
<span class="nc" id="L313">                .totalEndpoints(totalEndpoints)</span>
<span class="nc" id="L314">                .verifiedEndpoints(verifiedEndpoints)</span>
<span class="nc bnc" id="L315" title="All 4 branches missed.">                .totalSuccesses(stats != null &amp;&amp; stats[0] != null ? ((Number) stats[0]).longValue() : 0)</span>
<span class="nc bnc" id="L316" title="All 4 branches missed.">                .totalFailures(stats != null &amp;&amp; stats[1] != null ? ((Number) stats[1]).longValue() : 0)</span>
<span class="nc" id="L317">                .build();</span>
    }

    // Private helper methods

    private void validateWebhookEndpoint(WebhookEndpoint webhook) {
<span class="nc bnc" id="L323" title="All 4 branches missed.">        if (webhook.getName() == null || webhook.getName().trim().isEmpty()) {</span>
<span class="nc" id="L324">            throw new RuntimeException(&quot;Webhook name is required&quot;);</span>
        }
        
<span class="nc bnc" id="L327" title="All 4 branches missed.">        if (webhook.getUrl() == null || webhook.getUrl().trim().isEmpty()) {</span>
<span class="nc" id="L328">            throw new RuntimeException(&quot;Webhook URL is required&quot;);</span>
        }
        
<span class="nc bnc" id="L331" title="All 4 branches missed.">        if (!webhook.getUrl().startsWith(&quot;http://&quot;) &amp;&amp; !webhook.getUrl().startsWith(&quot;https://&quot;)) {</span>
<span class="nc" id="L332">            throw new RuntimeException(&quot;Webhook URL must start with http:// or https://&quot;);</span>
        }
        
        // Check for duplicate URL
<span class="nc bnc" id="L336" title="All 2 branches missed.">        if (webhookEndpointRepository.existsByUrl(webhook.getUrl())) {</span>
<span class="nc" id="L337">            throw new RuntimeException(&quot;Webhook URL already exists&quot;);</span>
        }
<span class="nc" id="L339">    }</span>

    private boolean shouldDeliverToEndpoint(WebhookEndpoint endpoint, SystemEvent systemEvent) {
        // Check if endpoint is active and verified
<span class="nc bnc" id="L343" title="All 4 branches missed.">        if (!endpoint.isCurrentlyActive() || !endpoint.isEndpointVerified()) {</span>
<span class="nc" id="L344">            return false;</span>
        }
        
        // Check event type filter
<span class="nc bnc" id="L348" title="All 4 branches missed.">        if (endpoint.getEventTypes() != null &amp;&amp; !endpoint.getEventTypes().isNull()) {</span>
<span class="nc" id="L349">            boolean eventTypeMatches = false;</span>
<span class="nc bnc" id="L350" title="All 2 branches missed.">            for (JsonNode eventType : endpoint.getEventTypes()) {</span>
<span class="nc bnc" id="L351" title="All 2 branches missed.">                if (eventType.asText().equals(systemEvent.getEventType().name())) {</span>
<span class="nc" id="L352">                    eventTypeMatches = true;</span>
<span class="nc" id="L353">                    break;</span>
                }
<span class="nc" id="L355">            }</span>
<span class="nc bnc" id="L356" title="All 2 branches missed.">            if (!eventTypeMatches) {</span>
<span class="nc" id="L357">                return false;</span>
            }
        }
        
        // Check additional event filters
<span class="nc bnc" id="L362" title="All 2 branches missed.">        if (endpoint.hasEventFilters()) {</span>
<span class="nc" id="L363">            return evaluateEventFilters(endpoint.getEventFilters(), systemEvent);</span>
        }
        
<span class="nc" id="L366">        return true;</span>
    }

    private boolean evaluateEventFilters(JsonNode filters, SystemEvent systemEvent) {
        // Implementation would evaluate complex filter conditions
        // For now, return true (no filtering)
<span class="nc" id="L372">        return true;</span>
    }

    private HttpHeaders prepareHeaders(WebhookEndpoint endpoint) {
<span class="nc" id="L376">        HttpHeaders headers = new HttpHeaders();</span>
<span class="nc" id="L377">        headers.setContentType(MediaType.parseMediaType(endpoint.getContentType()));</span>
        
        // Add authentication headers
<span class="nc bnc" id="L380" title="All 2 branches missed.">        if (endpoint.hasAuthentication()) {</span>
<span class="nc" id="L381">            addAuthenticationHeaders(headers, endpoint);</span>
        }
        
        // Add custom headers
<span class="nc bnc" id="L385" title="All 2 branches missed.">        if (endpoint.hasCustomHeaders()) {</span>
<span class="nc" id="L386">            endpoint.getCustomHeaders().fields().forEachRemaining(entry -&gt; {</span>
<span class="nc" id="L387">                headers.add(entry.getKey(), entry.getValue().asText());</span>
<span class="nc" id="L388">            });</span>
        }
        
<span class="nc" id="L391">        return headers;</span>
    }

    private void addAuthenticationHeaders(HttpHeaders headers, WebhookEndpoint endpoint) {
<span class="nc bnc" id="L395" title="All 4 branches missed.">        if (endpoint.getAuthType() == WebhookAuthType.BEARER &amp;&amp; endpoint.getAuthConfig() != null) {</span>
<span class="nc" id="L396">            String token = endpoint.getAuthConfig().get(&quot;token&quot;).asText();</span>
<span class="nc" id="L397">            headers.setBearerAuth(token);</span>
<span class="nc bnc" id="L398" title="All 4 branches missed.">        } else if (endpoint.getAuthType() == WebhookAuthType.API_KEY &amp;&amp; endpoint.getAuthConfig() != null) {</span>
<span class="nc" id="L399">            String keyName = endpoint.getAuthConfig().get(&quot;keyName&quot;).asText();</span>
<span class="nc" id="L400">            String keyValue = endpoint.getAuthConfig().get(&quot;keyValue&quot;).asText();</span>
<span class="nc" id="L401">            headers.add(keyName, keyValue);</span>
        }
        // Add other authentication types as needed
<span class="nc" id="L404">    }</span>

    private String preparePayload(WebhookEndpoint endpoint, SystemEvent systemEvent) {
<span class="nc bnc" id="L407" title="All 4 branches missed.">        if (endpoint.getPayloadTemplate() != null &amp;&amp; !endpoint.getPayloadTemplate().trim().isEmpty()) {</span>
<span class="nc" id="L408">            return processPayloadTemplate(endpoint.getPayloadTemplate(), systemEvent);</span>
        } else {
<span class="nc" id="L410">            return createDefaultPayload(systemEvent);</span>
        }
    }

    private String processPayloadTemplate(String template, SystemEvent systemEvent) {
        // Implementation would process template with event data
        // For now, return a simple JSON payload
<span class="nc" id="L417">        return createDefaultPayload(systemEvent);</span>
    }

    private String createDefaultPayload(SystemEvent systemEvent) {
        try {
<span class="nc" id="L422">            Map&lt;String, Object&gt; payload = Map.of(</span>
<span class="nc" id="L423">                    &quot;eventId&quot;, systemEvent.getId(),</span>
<span class="nc" id="L424">                    &quot;eventType&quot;, systemEvent.getEventType().name(),</span>
<span class="nc" id="L425">                    &quot;eventCategory&quot;, systemEvent.getEventCategory().name(),</span>
<span class="nc" id="L426">                    &quot;eventName&quot;, systemEvent.getEventName(),</span>
<span class="nc" id="L427">                    &quot;timestamp&quot;, systemEvent.getEventTimestamp().toString(),</span>
<span class="nc bnc" id="L428" title="All 2 branches missed.">                    &quot;actor&quot;, systemEvent.getActorUserId() != null ? systemEvent.getActorUserId() : &quot;system&quot;,</span>
<span class="nc bnc" id="L429" title="All 2 branches missed.">                    &quot;data&quot;, systemEvent.getEventData() != null ? systemEvent.getEventData() : objectMapper.createObjectNode()</span>
            );
<span class="nc" id="L431">            return objectMapper.writeValueAsString(payload);</span>
<span class="nc" id="L432">        } catch (Exception e) {</span>
<span class="nc" id="L433">            log.error(&quot;Error creating default payload&quot;, e);</span>
<span class="nc" id="L434">            return &quot;{}&quot;;</span>
        }
    }

    private boolean shouldRetryDelivery(WebhookDelivery delivery) {
<span class="nc bnc" id="L439" title="All 2 branches missed.">        return delivery.getDeliveryAttempt() &lt; delivery.getWebhookEndpoint().getMaxRetries() &amp;&amp;</span>
<span class="nc bnc" id="L440" title="All 2 branches missed.">               delivery.shouldRetry();</span>
    }

    private void scheduleRetryDelivery(WebhookDelivery delivery) {
<span class="nc" id="L444">        int delaySeconds = calculateRetryDelay(delivery);</span>
<span class="nc" id="L445">        LocalDateTime retryTime = LocalDateTime.now().plusSeconds(delaySeconds);</span>
        
<span class="nc" id="L447">        WebhookDelivery retryDelivery = WebhookDelivery.builder()</span>
<span class="nc" id="L448">                .webhookEndpoint(delivery.getWebhookEndpoint())</span>
<span class="nc" id="L449">                .systemEvent(delivery.getSystemEvent())</span>
<span class="nc" id="L450">                .deliveryAttempt(delivery.getDeliveryAttempt() + 1)</span>
<span class="nc" id="L451">                .deliveryStatus(DeliveryStatus.PENDING)</span>
<span class="nc" id="L452">                .scheduledDate(retryTime)</span>
<span class="nc" id="L453">                .correlationId(delivery.getCorrelationId())</span>
<span class="nc" id="L454">                .build();</span>
        
<span class="nc" id="L456">        webhookDeliveryRepository.save(retryDelivery);</span>
        
<span class="nc" id="L458">        log.info(&quot;Scheduled retry delivery for webhook ID: {} at {}&quot;, delivery.getId(), retryTime);</span>
<span class="nc" id="L459">    }</span>

    private int calculateRetryDelay(WebhookDelivery delivery) {
<span class="nc" id="L462">        int baseDelay = delivery.getWebhookEndpoint().getRetryDelaySeconds();</span>
        
<span class="nc bnc" id="L464" title="All 2 branches missed.">        if (delivery.getWebhookEndpoint().getExponentialBackoff()) {</span>
<span class="nc" id="L465">            return baseDelay * (int) Math.pow(2, delivery.getDeliveryAttempt() - 1);</span>
        } else {
<span class="nc" id="L467">            return baseDelay;</span>
        }
    }

    // Inner class for webhook statistics
<span class="nc" id="L472">    public static class WebhookStatistics {</span>
        private long totalEndpoints;
        private long verifiedEndpoints;
        private long totalSuccesses;
        private long totalFailures;
        
        public static WebhookStatisticsBuilder builder() {
<span class="nc" id="L479">            return new WebhookStatisticsBuilder();</span>
        }
        
<span class="nc" id="L482">        public static class WebhookStatisticsBuilder {</span>
            private long totalEndpoints;
            private long verifiedEndpoints;
            private long totalSuccesses;
            private long totalFailures;
            
            public WebhookStatisticsBuilder totalEndpoints(long totalEndpoints) {
<span class="nc" id="L489">                this.totalEndpoints = totalEndpoints;</span>
<span class="nc" id="L490">                return this;</span>
            }
            
            public WebhookStatisticsBuilder verifiedEndpoints(long verifiedEndpoints) {
<span class="nc" id="L494">                this.verifiedEndpoints = verifiedEndpoints;</span>
<span class="nc" id="L495">                return this;</span>
            }
            
            public WebhookStatisticsBuilder totalSuccesses(long totalSuccesses) {
<span class="nc" id="L499">                this.totalSuccesses = totalSuccesses;</span>
<span class="nc" id="L500">                return this;</span>
            }
            
            public WebhookStatisticsBuilder totalFailures(long totalFailures) {
<span class="nc" id="L504">                this.totalFailures = totalFailures;</span>
<span class="nc" id="L505">                return this;</span>
            }
            
            public WebhookStatistics build() {
<span class="nc" id="L509">                WebhookStatistics stats = new WebhookStatistics();</span>
<span class="nc" id="L510">                stats.totalEndpoints = this.totalEndpoints;</span>
<span class="nc" id="L511">                stats.verifiedEndpoints = this.verifiedEndpoints;</span>
<span class="nc" id="L512">                stats.totalSuccesses = this.totalSuccesses;</span>
<span class="nc" id="L513">                stats.totalFailures = this.totalFailures;</span>
<span class="nc" id="L514">                return stats;</span>
            }
        }
        
        // Getters
<span class="nc" id="L519">        public long getTotalEndpoints() { return totalEndpoints; }</span>
<span class="nc" id="L520">        public long getVerifiedEndpoints() { return verifiedEndpoints; }</span>
<span class="nc" id="L521">        public long getTotalSuccesses() { return totalSuccesses; }</span>
<span class="nc" id="L522">        public long getTotalFailures() { return totalFailures; }</span>
        public double getSuccessRate() {
<span class="nc" id="L524">            long total = totalSuccesses + totalFailures;</span>
<span class="nc bnc" id="L525" title="All 2 branches missed.">            return total &gt; 0 ? (double) totalSuccesses / total * 100.0 : 0.0;</span>
        }
    }
}
</pre><div class="footer"><span class="right">Created with <a href="http://www.jacoco.org/jacoco">JaCoCo</a> 0.8.10.202304240956</span></div></body></html>