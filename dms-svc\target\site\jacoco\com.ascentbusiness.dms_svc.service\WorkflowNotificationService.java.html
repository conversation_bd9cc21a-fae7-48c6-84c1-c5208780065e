<?xml version="1.0" encoding="UTF-8"?><!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Strict//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-strict.dtd"><html xmlns="http://www.w3.org/1999/xhtml" lang="en"><head><meta http-equiv="Content-Type" content="text/html;charset=UTF-8"/><link rel="stylesheet" href="../jacoco-resources/report.css" type="text/css"/><link rel="shortcut icon" href="../jacoco-resources/report.gif" type="image/gif"/><title>WorkflowNotificationService.java</title><link rel="stylesheet" href="../jacoco-resources/prettify.css" type="text/css"/><script type="text/javascript" src="../jacoco-resources/prettify.js"></script></head><body onload="window['PR_TAB_WIDTH']=4;prettyPrint()"><div class="breadcrumb" id="breadcrumb"><span class="info"><a href="../jacoco-sessions.html" class="el_session">Sessions</a></span><a href="../index.html" class="el_report">dms-svc</a> &gt; <a href="index.source.html" class="el_package">com.ascentbusiness.dms_svc.service</a> &gt; <span class="el_source">WorkflowNotificationService.java</span></div><h1>WorkflowNotificationService.java</h1><pre class="source lang-java linenums">package com.ascentbusiness.dms_svc.service;

import com.ascentbusiness.dms_svc.entity.WorkflowInstance;
import com.ascentbusiness.dms_svc.entity.WorkflowTask;
import com.ascentbusiness.dms_svc.entity.WorkflowNotification;
import com.ascentbusiness.dms_svc.enums.NotificationType;
import com.ascentbusiness.dms_svc.repository.WorkflowNotificationRepository;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.util.List;

/**
 * Service for managing workflow notifications
 */
@Service
<span class="fc" id="L20">@RequiredArgsConstructor</span>
<span class="fc" id="L21">@Slf4j</span>
@Transactional
public class WorkflowNotificationService {

    private final WorkflowNotificationRepository workflowNotificationRepository;
    private final SystemEventService systemEventService;

    /**
     * Send workflow started notification
     */
    public void sendWorkflowStartedNotification(WorkflowInstance workflowInstance) {
<span class="nc" id="L32">        log.info(&quot;Sending workflow started notification for instance: {}&quot;, workflowInstance.getId());</span>
        
<span class="nc" id="L34">        WorkflowNotification notification = WorkflowNotification.builder()</span>
<span class="nc" id="L35">                .workflowInstance(workflowInstance)</span>
<span class="nc" id="L36">                .notificationType(NotificationType.WORKFLOW_STARTED.name())</span>
<span class="nc" id="L37">                .recipientUserId(workflowInstance.getInitiatorUserId())</span>
<span class="nc" id="L38">                .subject(&quot;Workflow Started: &quot; + workflowInstance.getInstanceName())</span>
<span class="nc" id="L39">                .message(&quot;Your workflow has been started for document: &quot; + workflowInstance.getDocument().getName())</span>
<span class="nc" id="L40">                .status(&quot;PENDING&quot;)</span>
<span class="nc" id="L41">                .build();</span>
        
<span class="nc" id="L43">        workflowNotificationRepository.save(notification);</span>
        
        // Create system event
<span class="nc" id="L46">        systemEventService.createSystemEvent(</span>
                com.ascentbusiness.dms_svc.enums.EventType.WORKFLOW_STARTED,
                com.ascentbusiness.dms_svc.enums.EventCategory.WORKFLOW,
                &quot;system&quot;,
                &quot;Workflow Started Notification&quot;,
                &quot;WorkflowInstance&quot;,
<span class="nc" id="L52">                workflowInstance.getId(),</span>
<span class="nc" id="L53">                java.util.Map.of(</span>
<span class="nc" id="L54">                        &quot;workflowInstanceId&quot;, workflowInstance.getId(),</span>
<span class="nc" id="L55">                        &quot;documentId&quot;, workflowInstance.getDocument().getId(),</span>
<span class="nc" id="L56">                        &quot;initiatorUserId&quot;, workflowInstance.getInitiatorUserId()</span>
                )
        );
<span class="nc" id="L59">    }</span>

    /**
     * Send workflow completion notification
     */
    public void sendWorkflowCompletionNotification(WorkflowInstance workflowInstance) {
<span class="nc" id="L65">        log.info(&quot;Sending workflow completion notification for instance: {}&quot;, workflowInstance.getId());</span>
        
<span class="nc" id="L67">        WorkflowNotification notification = WorkflowNotification.builder()</span>
<span class="nc" id="L68">                .workflowInstance(workflowInstance)</span>
<span class="nc" id="L69">                .notificationType(NotificationType.WORKFLOW_COMPLETED.name())</span>
<span class="nc" id="L70">                .recipientUserId(workflowInstance.getInitiatorUserId())</span>
<span class="nc" id="L71">                .subject(&quot;Workflow Completed: &quot; + workflowInstance.getInstanceName())</span>
<span class="nc" id="L72">                .message(&quot;Your workflow has been completed for document: &quot; + workflowInstance.getDocument().getName())</span>
<span class="nc" id="L73">                .status(&quot;PENDING&quot;)</span>
<span class="nc" id="L74">                .build();</span>
        
<span class="nc" id="L76">        workflowNotificationRepository.save(notification);</span>
        
        // Create system event
<span class="nc" id="L79">        systemEventService.createSystemEvent(</span>
                com.ascentbusiness.dms_svc.enums.EventType.WORKFLOW_COMPLETED,
                com.ascentbusiness.dms_svc.enums.EventCategory.WORKFLOW,
                &quot;system&quot;,
                &quot;Workflow Completed Notification&quot;,
                &quot;WorkflowInstance&quot;,
<span class="nc" id="L85">                workflowInstance.getId(),</span>
<span class="nc" id="L86">                java.util.Map.of(</span>
<span class="nc" id="L87">                        &quot;workflowInstanceId&quot;, workflowInstance.getId(),</span>
<span class="nc" id="L88">                        &quot;documentId&quot;, workflowInstance.getDocument().getId(),</span>
<span class="nc bnc" id="L89" title="All 2 branches missed.">                        &quot;completionReason&quot;, workflowInstance.getCompletionReason() != null ? workflowInstance.getCompletionReason() : &quot;Completed&quot;</span>
                )
        );
<span class="nc" id="L92">    }</span>

    /**
     * Send task assignment notification
     */
    public void sendTaskAssignmentNotification(WorkflowTask task) {
<span class="nc" id="L98">        log.info(&quot;Sending task assignment notification for task: {} to user: {}&quot;, task.getId(), task.getAssignedToUserId());</span>
        
<span class="nc bnc" id="L100" title="All 2 branches missed.">        if (task.getAssignedToUserId() == null) {</span>
<span class="nc" id="L101">            log.warn(&quot;Cannot send task assignment notification - no assigned user for task: {}&quot;, task.getId());</span>
<span class="nc" id="L102">            return;</span>
        }
        
<span class="nc" id="L105">        WorkflowNotification notification = WorkflowNotification.builder()</span>
<span class="nc" id="L106">                .workflowInstance(task.getWorkflowInstance())</span>
<span class="nc" id="L107">                .workflowTask(task)</span>
<span class="nc" id="L108">                .notificationType(NotificationType.TASK_ASSIGNED.name())</span>
<span class="nc" id="L109">                .recipientUserId(task.getAssignedToUserId())</span>
<span class="nc" id="L110">                .subject(&quot;Task Assigned: &quot; + task.getTaskName())</span>
<span class="nc" id="L111">                .message(&quot;You have been assigned a new task: &quot; + task.getTaskName() +</span>
<span class="nc" id="L112">                        &quot; for document: &quot; + task.getWorkflowInstance().getDocument().getName())</span>
<span class="nc" id="L113">                .status(&quot;PENDING&quot;)</span>
<span class="nc" id="L114">                .build();</span>
        
<span class="nc" id="L116">        workflowNotificationRepository.save(notification);</span>
<span class="nc" id="L117">    }</span>

    /**
     * Send task due soon notification
     */
    public void sendTaskDueSoonNotification(WorkflowTask task) {
<span class="nc" id="L123">        log.info(&quot;Sending task due soon notification for task: {} to user: {}&quot;, task.getId(), task.getAssignedToUserId());</span>
        
<span class="nc bnc" id="L125" title="All 2 branches missed.">        if (task.getAssignedToUserId() == null) {</span>
<span class="nc" id="L126">            log.warn(&quot;Cannot send task due soon notification - no assigned user for task: {}&quot;, task.getId());</span>
<span class="nc" id="L127">            return;</span>
        }
        
<span class="nc" id="L130">        WorkflowNotification notification = WorkflowNotification.builder()</span>
<span class="nc" id="L131">                .workflowInstance(task.getWorkflowInstance())</span>
<span class="nc" id="L132">                .workflowTask(task)</span>
<span class="nc" id="L133">                .notificationType(NotificationType.TASK_DUE_SOON.name())</span>
<span class="nc" id="L134">                .recipientUserId(task.getAssignedToUserId())</span>
<span class="nc" id="L135">                .subject(&quot;Task Due Soon: &quot; + task.getTaskName())</span>
<span class="nc" id="L136">                .message(&quot;Your task '&quot; + task.getTaskName() + &quot;' is due soon. Please complete it by: &quot; +</span>
<span class="nc bnc" id="L137" title="All 2 branches missed.">                        (task.getDueDate() != null ? task.getDueDate().toString() : &quot;N/A&quot;))</span>
<span class="nc" id="L138">                .status(&quot;PENDING&quot;)</span>
<span class="nc" id="L139">                .build();</span>
        
<span class="nc" id="L141">        workflowNotificationRepository.save(notification);</span>
<span class="nc" id="L142">    }</span>

    /**
     * Send task overdue notification
     */
    public void sendTaskOverdueNotification(WorkflowTask task) {
<span class="nc" id="L148">        log.info(&quot;Sending task overdue notification for task: {} to user: {}&quot;, task.getId(), task.getAssignedToUserId());</span>
        
<span class="nc bnc" id="L150" title="All 2 branches missed.">        if (task.getAssignedToUserId() == null) {</span>
<span class="nc" id="L151">            log.warn(&quot;Cannot send task overdue notification - no assigned user for task: {}&quot;, task.getId());</span>
<span class="nc" id="L152">            return;</span>
        }
        
<span class="nc" id="L155">        WorkflowNotification notification = WorkflowNotification.builder()</span>
<span class="nc" id="L156">                .workflowInstance(task.getWorkflowInstance())</span>
<span class="nc" id="L157">                .workflowTask(task)</span>
<span class="nc" id="L158">                .notificationType(NotificationType.TASK_OVERDUE.name())</span>
<span class="nc" id="L159">                .recipientUserId(task.getAssignedToUserId())</span>
<span class="nc" id="L160">                .subject(&quot;Task Overdue: &quot; + task.getTaskName())</span>
<span class="nc" id="L161">                .message(&quot;Your task '&quot; + task.getTaskName() + &quot;' is overdue. Please complete it immediately.&quot;)</span>
<span class="nc" id="L162">                .status(&quot;PENDING&quot;)</span>
<span class="nc" id="L163">                .build();</span>
        
<span class="nc" id="L165">        workflowNotificationRepository.save(notification);</span>
<span class="nc" id="L166">    }</span>

    /**
     * Send task escalation notification
     */
    public void sendTaskEscalationNotification(WorkflowTask task, String escalatedTo) {
<span class="nc" id="L172">        log.info(&quot;Sending task escalation notification for task: {} to user: {}&quot;, task.getId(), escalatedTo);</span>
        
<span class="nc" id="L174">        WorkflowNotification notification = WorkflowNotification.builder()</span>
<span class="nc" id="L175">                .workflowInstance(task.getWorkflowInstance())</span>
<span class="nc" id="L176">                .workflowTask(task)</span>
<span class="nc" id="L177">                .notificationType(NotificationType.TASK_ESCALATED.name())</span>
<span class="nc" id="L178">                .recipientUserId(escalatedTo)</span>
<span class="nc" id="L179">                .subject(&quot;Task Escalated: &quot; + task.getTaskName())</span>
<span class="nc" id="L180">                .message(&quot;A task has been escalated to you: &quot; + task.getTaskName() +</span>
<span class="nc" id="L181">                        &quot; for document: &quot; + task.getWorkflowInstance().getDocument().getName())</span>
<span class="nc" id="L182">                .status(&quot;PENDING&quot;)</span>
<span class="nc" id="L183">                .build();</span>
        
<span class="nc" id="L185">        workflowNotificationRepository.save(notification);</span>
<span class="nc" id="L186">    }</span>

    /**
     * Get notifications for a user
     */
    @Transactional(readOnly = true)
    public List&lt;WorkflowNotification&gt; getNotificationsForUser(String userId) {
<span class="nc" id="L193">        return workflowNotificationRepository.findByRecipientUserIdOrderByCreatedDateDesc(userId);</span>
    }

    /**
     * Get unread notifications for a user
     */
    @Transactional(readOnly = true)
    public List&lt;WorkflowNotification&gt; getUnreadNotificationsForUser(String userId) {
<span class="nc" id="L201">        return workflowNotificationRepository.findByRecipientUserIdOrderByCreatedDateDesc(userId)</span>
<span class="nc" id="L202">                .stream()</span>
<span class="nc" id="L203">                .filter(notification -&gt; &quot;PENDING&quot;.equals(notification.getStatus()))</span>
<span class="nc" id="L204">                .collect(java.util.stream.Collectors.toList());</span>
    }

    /**
     * Mark notification as read
     */
    public void markNotificationAsRead(Long notificationId) {
<span class="nc" id="L211">        WorkflowNotification notification = workflowNotificationRepository.findById(notificationId)</span>
<span class="nc" id="L212">                .orElseThrow(() -&gt; new RuntimeException(&quot;Notification not found with ID: &quot; + notificationId));</span>

<span class="nc" id="L214">        notification.markAsDelivered(); // Use existing method to mark as delivered/read</span>
<span class="nc" id="L215">        workflowNotificationRepository.save(notification);</span>
<span class="nc" id="L216">    }</span>

    /**
     * Mark all notifications as read for a user
     */
    public void markAllNotificationsAsRead(String userId) {
<span class="nc" id="L222">        List&lt;WorkflowNotification&gt; unreadNotifications = getUnreadNotificationsForUser(userId);</span>

<span class="nc bnc" id="L224" title="All 2 branches missed.">        for (WorkflowNotification notification : unreadNotifications) {</span>
<span class="nc" id="L225">            notification.markAsDelivered(); // Use existing method to mark as delivered/read</span>
<span class="nc" id="L226">            workflowNotificationRepository.save(notification);</span>
<span class="nc" id="L227">        }</span>

<span class="nc" id="L229">        log.info(&quot;Marked {} notifications as read for user: {}&quot;, unreadNotifications.size(), userId);</span>
<span class="nc" id="L230">    }</span>

    /**
     * Delete old notifications
     */
    public int deleteOldNotifications(int daysToKeep) {
<span class="nc" id="L236">        LocalDateTime cutoffDate = LocalDateTime.now().minusDays(daysToKeep);</span>
<span class="nc" id="L237">        List&lt;WorkflowNotification&gt; oldNotifications = workflowNotificationRepository.findByCreatedDateBefore(cutoffDate);</span>
        
<span class="nc" id="L239">        workflowNotificationRepository.deleteAll(oldNotifications);</span>
        
<span class="nc" id="L241">        log.info(&quot;Deleted {} old notifications older than {} days&quot;, oldNotifications.size(), daysToKeep);</span>
<span class="nc" id="L242">        return oldNotifications.size();</span>
    }

    /**
     * Get notification statistics for a user
     */
    @Transactional(readOnly = true)
    public NotificationStatistics getNotificationStatistics(String userId) {
<span class="nc" id="L250">        long totalNotifications = workflowNotificationRepository.countByRecipientUserId(userId);</span>
<span class="nc" id="L251">        long unreadNotifications = workflowNotificationRepository.findByRecipientUserId(userId)</span>
<span class="nc" id="L252">                .stream()</span>
<span class="nc" id="L253">                .filter(notification -&gt; &quot;PENDING&quot;.equals(notification.getStatus()))</span>
<span class="nc" id="L254">                .count();</span>

<span class="nc" id="L256">        return NotificationStatistics.builder()</span>
<span class="nc" id="L257">                .totalNotifications(totalNotifications)</span>
<span class="nc" id="L258">                .unreadNotifications(unreadNotifications)</span>
<span class="nc" id="L259">                .readNotifications(totalNotifications - unreadNotifications)</span>
<span class="nc" id="L260">                .build();</span>
    }

    // Inner class for notification statistics
<span class="nc" id="L264">    public static class NotificationStatistics {</span>
        private long totalNotifications;
        private long unreadNotifications;
        private long readNotifications;
        
        public static NotificationStatisticsBuilder builder() {
<span class="nc" id="L270">            return new NotificationStatisticsBuilder();</span>
        }
        
<span class="nc" id="L273">        public static class NotificationStatisticsBuilder {</span>
            private long totalNotifications;
            private long unreadNotifications;
            private long readNotifications;
            
            public NotificationStatisticsBuilder totalNotifications(long totalNotifications) {
<span class="nc" id="L279">                this.totalNotifications = totalNotifications;</span>
<span class="nc" id="L280">                return this;</span>
            }
            
            public NotificationStatisticsBuilder unreadNotifications(long unreadNotifications) {
<span class="nc" id="L284">                this.unreadNotifications = unreadNotifications;</span>
<span class="nc" id="L285">                return this;</span>
            }
            
            public NotificationStatisticsBuilder readNotifications(long readNotifications) {
<span class="nc" id="L289">                this.readNotifications = readNotifications;</span>
<span class="nc" id="L290">                return this;</span>
            }
            
            public NotificationStatistics build() {
<span class="nc" id="L294">                NotificationStatistics stats = new NotificationStatistics();</span>
<span class="nc" id="L295">                stats.totalNotifications = this.totalNotifications;</span>
<span class="nc" id="L296">                stats.unreadNotifications = this.unreadNotifications;</span>
<span class="nc" id="L297">                stats.readNotifications = this.readNotifications;</span>
<span class="nc" id="L298">                return stats;</span>
            }
        }
        
        // Getters
<span class="nc" id="L303">        public long getTotalNotifications() { return totalNotifications; }</span>
<span class="nc" id="L304">        public long getUnreadNotifications() { return unreadNotifications; }</span>
<span class="nc" id="L305">        public long getReadNotifications() { return readNotifications; }</span>
    }
}
</pre><div class="footer"><span class="right">Created with <a href="http://www.jacoco.org/jacoco">JaCoCo</a> 0.8.10.202304240956</span></div></body></html>