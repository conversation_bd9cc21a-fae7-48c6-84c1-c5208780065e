package com.ascentbusiness.dms_svc.controller;

import com.ascentbusiness.dms_svc.service.CacheService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.util.HashMap;
import java.util.Map;

/**
 * Admin controller for system management operations
 */
@RestController
@RequestMapping("/api/admin")
public class AdminController {

    private static final Logger logger = LoggerFactory.getLogger(AdminController.class);

    @Autowired
    private CacheService cacheService;

    /**
     * Clear all caches
     */
    @PostMapping("/cache/clear-all")
    public ResponseEntity<Map<String, Object>> clearAllCaches() {
        logger.info("Admin request to clear all caches");
        
        Map<String, Object> response = new HashMap<>();
        
        try {
            cacheService.invalidateAllCaches();
            response.put("success", true);
            response.put("message", "All caches cleared successfully");
            logger.info("All caches cleared successfully via admin endpoint");
        } catch (Exception e) {
            logger.error("Error clearing all caches via admin endpoint", e);
            response.put("success", false);
            response.put("message", "Error clearing caches: " + e.getMessage());
            return ResponseEntity.internalServerError().body(response);
        }
        
        return ResponseEntity.ok(response);
    }

    /**
     * Clear storage configuration caches specifically
     */
    @PostMapping("/cache/clear-storage-config")
    public ResponseEntity<Map<String, Object>> clearStorageConfigCaches() {
        logger.info("Admin request to clear storage configuration caches");
        
        Map<String, Object> response = new HashMap<>();
        
        try {
            cacheService.invalidateStorageConfigurationCaches();
            response.put("success", true);
            response.put("message", "Storage configuration caches cleared successfully");
            logger.info("Storage configuration caches cleared successfully via admin endpoint");
        } catch (Exception e) {
            logger.error("Error clearing storage configuration caches via admin endpoint", e);
            response.put("success", false);
            response.put("message", "Error clearing storage configuration caches: " + e.getMessage());
            return ResponseEntity.internalServerError().body(response);
        }
        
        return ResponseEntity.ok(response);
    }

    /**
     * Get cache statistics
     */
    @GetMapping("/cache/stats")
    public ResponseEntity<CacheService.CacheStats> getCacheStats() {
        logger.debug("Admin request for cache statistics");
        
        try {
            CacheService.CacheStats stats = cacheService.getCacheStats();
            return ResponseEntity.ok(stats);
        } catch (Exception e) {
            logger.error("Error getting cache statistics via admin endpoint", e);
            return ResponseEntity.internalServerError().build();
        }
    }
}