<?xml version="1.0" encoding="UTF-8"?><!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Strict//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-strict.dtd"><html xmlns="http://www.w3.org/1999/xhtml" lang="en"><head><meta http-equiv="Content-Type" content="text/html;charset=UTF-8"/><link rel="stylesheet" href="../jacoco-resources/report.css" type="text/css"/><link rel="shortcut icon" href="../jacoco-resources/report.gif" type="image/gif"/><title>StorageConfigurationResolver.java</title><link rel="stylesheet" href="../jacoco-resources/prettify.css" type="text/css"/><script type="text/javascript" src="../jacoco-resources/prettify.js"></script></head><body onload="window['PR_TAB_WIDTH']=4;prettyPrint()"><div class="breadcrumb" id="breadcrumb"><span class="info"><a href="../jacoco-sessions.html" class="el_session">Sessions</a></span><a href="../index.html" class="el_report">dms-svc</a> &gt; <a href="index.source.html" class="el_package">com.ascentbusiness.dms_svc.resolver</a> &gt; <span class="el_source">StorageConfigurationResolver.java</span></div><h1>StorageConfigurationResolver.java</h1><pre class="source lang-java linenums">package com.ascentbusiness.dms_svc.resolver;

import com.ascentbusiness.dms_svc.entity.StorageConfiguration;
import com.ascentbusiness.dms_svc.enums.StorageProvider;
import com.ascentbusiness.dms_svc.service.DynamicStorageProviderFactory;
import com.ascentbusiness.dms_svc.service.StorageConfigurationService;
import com.ascentbusiness.dms_svc.util.CorrelationIdUtil;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.graphql.data.method.annotation.Argument;
import org.springframework.graphql.data.method.annotation.MutationMapping;
import org.springframework.graphql.data.method.annotation.QueryMapping;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.security.core.Authentication;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.stereotype.Controller;

import java.util.List;
import java.util.Optional;

/**
 * GraphQL resolver for storage configuration management.
 * Provides admin interface for managing storage providers dynamically.
 */
@Controller
<span class="fc" id="L27">public class StorageConfigurationResolver {</span>

<span class="fc" id="L29">    private static final Logger logger = LoggerFactory.getLogger(StorageConfigurationResolver.class);</span>

    @Autowired
    private StorageConfigurationService configurationService;

    @Autowired
    private DynamicStorageProviderFactory storageProviderFactory;

    /**
     * Get all storage configurations (Admin only).
     */
    @QueryMapping
    @PreAuthorize(&quot;hasRole('ADMIN')&quot;)
    public List&lt;StorageConfiguration&gt; getAllStorageConfigurations() {
<span class="nc" id="L43">        String correlationId = CorrelationIdUtil.getCurrentCorrelationId();</span>
<span class="nc" id="L44">        String userId = getCurrentUserId();</span>
<span class="nc" id="L45">        logger.info(&quot;Admin {} requesting all storage configurations [{}]&quot;, userId, correlationId);</span>
        
<span class="nc" id="L47">        return configurationService.getAllConfigurations();</span>
    }

    /**
     * Get active storage configurations.
     */
    @QueryMapping
    @PreAuthorize(&quot;hasRole('ADMIN')&quot;)
    public List&lt;StorageConfiguration&gt; getActiveStorageConfigurations() {
<span class="nc" id="L56">        String correlationId = CorrelationIdUtil.getCurrentCorrelationId();</span>
<span class="nc" id="L57">        String userId = getCurrentUserId();</span>
<span class="nc" id="L58">        logger.info(&quot;Admin {} requesting active storage configurations [{}]&quot;, userId, correlationId);</span>
        
<span class="nc" id="L60">        return configurationService.getActiveConfigurations();</span>
    }

    /**
     * Get storage configuration by provider type.
     */
    @QueryMapping
    @PreAuthorize(&quot;hasRole('ADMIN')&quot;)
    public StorageConfiguration getStorageConfigurationByProvider(@Argument StorageProvider providerType) {
<span class="nc" id="L69">        String correlationId = CorrelationIdUtil.getCurrentCorrelationId();</span>
<span class="nc" id="L70">        String userId = getCurrentUserId();</span>
<span class="nc" id="L71">        logger.info(&quot;Admin {} requesting configuration for provider: {} [{}]&quot;, userId, providerType, correlationId);</span>
        
<span class="nc" id="L73">        Optional&lt;StorageConfiguration&gt; config = configurationService.getConfigurationByProvider(providerType);</span>
<span class="nc" id="L74">        return config.orElse(null);</span>
    }

    /**
     * Get the default storage configuration.
     */
    @QueryMapping
    @PreAuthorize(&quot;hasRole('ADMIN')&quot;)
    public StorageConfiguration getDefaultStorageConfiguration() {
<span class="nc" id="L83">        String correlationId = CorrelationIdUtil.getCurrentCorrelationId();</span>
<span class="nc" id="L84">        String userId = getCurrentUserId();</span>
<span class="nc" id="L85">        logger.info(&quot;Admin {} requesting default storage configuration [{}]&quot;, userId, correlationId);</span>
        
<span class="nc" id="L87">        Optional&lt;StorageConfiguration&gt; config = configurationService.getDefaultConfiguration();</span>
<span class="nc" id="L88">        return config.orElse(null);</span>
    }

    /**
     * Create or update storage configuration.
     */
    @MutationMapping
    @PreAuthorize(&quot;hasRole('ADMIN')&quot;)
    public StorageConfiguration saveStorageConfiguration(@Argument StorageConfigurationInput input) {
<span class="nc" id="L97">        String correlationId = CorrelationIdUtil.getCurrentCorrelationId();</span>
<span class="nc" id="L98">        String userId = getCurrentUserId();</span>
<span class="nc" id="L99">        logger.info(&quot;Admin {} saving storage configuration for provider: {} [{}]&quot;, </span>
<span class="nc" id="L100">                   userId, input.getProviderType(), correlationId);</span>

        // Convert input to entity
<span class="nc" id="L103">        StorageConfiguration configuration = StorageConfiguration.builder()</span>
<span class="nc" id="L104">                .id(input.getId())</span>
<span class="nc" id="L105">                .providerType(input.getProviderType())</span>
<span class="nc" id="L106">                .isActive(input.getIsActive())</span>
<span class="nc" id="L107">                .isDefault(input.getIsDefault())</span>
<span class="nc" id="L108">                .configurationJson(input.getConfigurationJson())</span>
<span class="nc" id="L109">                .description(input.getDescription())</span>
<span class="nc" id="L110">                .priority(input.getPriority())</span>
<span class="nc" id="L111">                .healthCheckEnabled(input.getHealthCheckEnabled())</span>
<span class="nc" id="L112">                .build();</span>

<span class="nc" id="L114">        StorageConfiguration saved = configurationService.saveConfiguration(configuration, userId);</span>
        
        // Clear cache to force reload of storage services
<span class="nc" id="L117">        storageProviderFactory.clearCache(saved.getProviderType());</span>
        
<span class="nc" id="L119">        logger.info(&quot;Storage configuration saved for provider: {} with ID: {} [{}]&quot;, </span>
<span class="nc" id="L120">                   saved.getProviderType(), saved.getId(), correlationId);</span>
        
<span class="nc" id="L122">        return saved;</span>
    }

    /**
     * Set configuration as default.
     */
    @MutationMapping
    @PreAuthorize(&quot;hasRole('ADMIN')&quot;)
    public Boolean setStorageConfigurationAsDefault(@Argument Long configurationId) {
<span class="nc" id="L131">        String correlationId = CorrelationIdUtil.getCurrentCorrelationId();</span>
<span class="nc" id="L132">        String userId = getCurrentUserId();</span>
<span class="nc" id="L133">        logger.info(&quot;Admin {} setting configuration {} as default [{}]&quot;, userId, configurationId, correlationId);</span>

        try {
<span class="nc" id="L136">            configurationService.setAsDefault(configurationId, userId);</span>
            
            // Clear all cache to force reload
<span class="nc" id="L139">            storageProviderFactory.clearAllCache();</span>
            
<span class="nc" id="L141">            logger.info(&quot;Configuration {} set as default successfully [{}]&quot;, configurationId, correlationId);</span>
<span class="nc" id="L142">            return true;</span>
<span class="nc" id="L143">        } catch (Exception e) {</span>
<span class="nc" id="L144">            logger.error(&quot;Failed to set configuration {} as default [{}]&quot;, configurationId, correlationId, e);</span>
<span class="nc" id="L145">            return false;</span>
        }
    }

    /**
     * Toggle configuration active status.
     */
    @MutationMapping
    @PreAuthorize(&quot;hasRole('ADMIN')&quot;)
    public Boolean toggleStorageConfiguration(@Argument Long configurationId, @Argument Boolean active) {
<span class="nc" id="L155">        String correlationId = CorrelationIdUtil.getCurrentCorrelationId();</span>
<span class="nc" id="L156">        String userId = getCurrentUserId();</span>
<span class="nc" id="L157">        logger.info(&quot;Admin {} toggling configuration {} to active: {} [{}]&quot;, </span>
                   userId, configurationId, active, correlationId);

        try {
<span class="nc" id="L161">            configurationService.toggleConfiguration(configurationId, active, userId);</span>
            
            // Clear cache for affected provider
<span class="nc" id="L164">            Optional&lt;StorageConfiguration&gt; config = configurationService.getAllConfigurations()</span>
<span class="nc" id="L165">                    .stream()</span>
<span class="nc" id="L166">                    .filter(c -&gt; c.getId().equals(configurationId))</span>
<span class="nc" id="L167">                    .findFirst();</span>
            
<span class="nc bnc" id="L169" title="All 2 branches missed.">            if (config.isPresent()) {</span>
<span class="nc" id="L170">                storageProviderFactory.clearCache(config.get().getProviderType());</span>
            }
            
<span class="nc" id="L173">            logger.info(&quot;Configuration {} toggled to active: {} successfully [{}]&quot;, </span>
                       configurationId, active, correlationId);
<span class="nc" id="L175">            return true;</span>
<span class="nc" id="L176">        } catch (Exception e) {</span>
<span class="nc" id="L177">            logger.error(&quot;Failed to toggle configuration {} [{}]&quot;, configurationId, correlationId, e);</span>
<span class="nc" id="L178">            return false;</span>
        }
    }

    /**
     * Delete storage configuration.
     */
    @MutationMapping
    @PreAuthorize(&quot;hasRole('ADMIN')&quot;)
    public Boolean deleteStorageConfiguration(@Argument Long configurationId) {
<span class="nc" id="L188">        String correlationId = CorrelationIdUtil.getCurrentCorrelationId();</span>
<span class="nc" id="L189">        String userId = getCurrentUserId();</span>
<span class="nc" id="L190">        logger.info(&quot;Admin {} deleting configuration {} [{}]&quot;, userId, configurationId, correlationId);</span>

        try {
            // Get provider type before deletion for cache clearing
<span class="nc" id="L194">            Optional&lt;StorageConfiguration&gt; config = configurationService.getAllConfigurations()</span>
<span class="nc" id="L195">                    .stream()</span>
<span class="nc" id="L196">                    .filter(c -&gt; c.getId().equals(configurationId))</span>
<span class="nc" id="L197">                    .findFirst();</span>
            
<span class="nc" id="L199">            configurationService.deleteConfiguration(configurationId, userId);</span>
            
            // Clear cache for deleted provider
<span class="nc bnc" id="L202" title="All 2 branches missed.">            if (config.isPresent()) {</span>
<span class="nc" id="L203">                storageProviderFactory.clearCache(config.get().getProviderType());</span>
            }
            
<span class="nc" id="L206">            logger.info(&quot;Configuration {} deleted successfully [{}]&quot;, configurationId, correlationId);</span>
<span class="nc" id="L207">            return true;</span>
<span class="nc" id="L208">        } catch (Exception e) {</span>
<span class="nc" id="L209">            logger.error(&quot;Failed to delete configuration {} [{}]&quot;, configurationId, correlationId, e);</span>
<span class="nc" id="L210">            return false;</span>
        }
    }

    /**
     * Test storage configuration connectivity.
     */
    @MutationMapping
    @PreAuthorize(&quot;hasRole('ADMIN')&quot;)
    public Boolean testStorageConfiguration(@Argument Long configurationId) {
<span class="nc" id="L220">        String correlationId = CorrelationIdUtil.getCurrentCorrelationId();</span>
<span class="nc" id="L221">        String userId = getCurrentUserId();</span>
<span class="nc" id="L222">        logger.info(&quot;Admin {} testing configuration {} [{}]&quot;, userId, configurationId, correlationId);</span>

        try {
<span class="nc" id="L225">            Optional&lt;StorageConfiguration&gt; configOpt = configurationService.getAllConfigurations()</span>
<span class="nc" id="L226">                    .stream()</span>
<span class="nc" id="L227">                    .filter(c -&gt; c.getId().equals(configurationId))</span>
<span class="nc" id="L228">                    .findFirst();</span>
            
<span class="nc bnc" id="L230" title="All 2 branches missed.">            if (configOpt.isEmpty()) {</span>
<span class="nc" id="L231">                logger.warn(&quot;Configuration {} not found for testing [{}]&quot;, configurationId, correlationId);</span>
<span class="nc" id="L232">                return false;</span>
            }
            
<span class="nc" id="L235">            boolean testResult = configurationService.testConfiguration(configOpt.get());</span>
            
<span class="nc" id="L237">            logger.info(&quot;Configuration {} test result: {} [{}]&quot;, configurationId, testResult, correlationId);</span>
<span class="nc" id="L238">            return testResult;</span>
<span class="nc" id="L239">        } catch (Exception e) {</span>
<span class="nc" id="L240">            logger.error(&quot;Failed to test configuration {} [{}]&quot;, configurationId, correlationId, e);</span>
<span class="nc" id="L241">            return false;</span>
        }
    }

    /**
     * Get available storage providers.
     */
    @QueryMapping
    @PreAuthorize(&quot;hasRole('ADMIN')&quot;)
    public List&lt;String&gt; getAvailableStorageProviders() {
<span class="nc" id="L251">        String correlationId = CorrelationIdUtil.getCurrentCorrelationId();</span>
<span class="nc" id="L252">        String userId = getCurrentUserId();</span>
<span class="nc" id="L253">        logger.info(&quot;Admin {} requesting available storage providers [{}]&quot;, userId, correlationId);</span>
        
<span class="nc" id="L255">        return storageProviderFactory.getAvailableProviders()</span>
<span class="nc" id="L256">                .stream()</span>
<span class="nc" id="L257">                .map(StorageProvider::name)</span>
<span class="nc" id="L258">                .toList();</span>
    }

    /**
     * Check if storage provider is available and healthy.
     */
    @QueryMapping
    @PreAuthorize(&quot;hasRole('ADMIN')&quot;)
    public Boolean isStorageProviderAvailable(@Argument StorageProvider providerType) {
<span class="nc" id="L267">        String correlationId = CorrelationIdUtil.getCurrentCorrelationId();</span>
<span class="nc" id="L268">        String userId = getCurrentUserId();</span>
<span class="nc" id="L269">        logger.info(&quot;Admin {} checking availability for provider: {} [{}]&quot;, userId, providerType, correlationId);</span>
        
<span class="nc" id="L271">        return storageProviderFactory.isProviderAvailable(providerType);</span>
    }

    /**
     * Get current user ID from security context.
     */
    private String getCurrentUserId() {
<span class="nc" id="L278">        Authentication authentication = SecurityContextHolder.getContext().getAuthentication();</span>
<span class="nc bnc" id="L279" title="All 2 branches missed.">        return authentication != null ? authentication.getName() : &quot;system&quot;;</span>
    }

    /**
     * Input type for storage configuration mutations.
     */
<span class="nc" id="L285">    public static class StorageConfigurationInput {</span>
        private Long id;
        private StorageProvider providerType;
        private Boolean isActive;
        private Boolean isDefault;
        private String configurationJson;
        private String description;
        private Integer priority;
        private Boolean healthCheckEnabled;

        // Getters and setters
<span class="nc" id="L296">        public Long getId() { return id; }</span>
<span class="nc" id="L297">        public void setId(Long id) { this.id = id; }</span>
        
<span class="nc" id="L299">        public StorageProvider getProviderType() { return providerType; }</span>
<span class="nc" id="L300">        public void setProviderType(StorageProvider providerType) { this.providerType = providerType; }</span>
        
<span class="nc" id="L302">        public Boolean getIsActive() { return isActive; }</span>
<span class="nc" id="L303">        public void setIsActive(Boolean isActive) { this.isActive = isActive; }</span>
        
<span class="nc" id="L305">        public Boolean getIsDefault() { return isDefault; }</span>
<span class="nc" id="L306">        public void setIsDefault(Boolean isDefault) { this.isDefault = isDefault; }</span>
        
<span class="nc" id="L308">        public String getConfigurationJson() { return configurationJson; }</span>
<span class="nc" id="L309">        public void setConfigurationJson(String configurationJson) { this.configurationJson = configurationJson; }</span>
        
<span class="nc" id="L311">        public String getDescription() { return description; }</span>
<span class="nc" id="L312">        public void setDescription(String description) { this.description = description; }</span>
        
<span class="nc" id="L314">        public Integer getPriority() { return priority; }</span>
<span class="nc" id="L315">        public void setPriority(Integer priority) { this.priority = priority; }</span>
        
<span class="nc" id="L317">        public Boolean getHealthCheckEnabled() { return healthCheckEnabled; }</span>
<span class="nc" id="L318">        public void setHealthCheckEnabled(Boolean healthCheckEnabled) { this.healthCheckEnabled = healthCheckEnabled; }</span>
    }
}
</pre><div class="footer"><span class="right">Created with <a href="http://www.jacoco.org/jacoco">JaCoCo</a> 0.8.10.202304240956</span></div></body></html>