--liquibase formatted sql

--changeset system:072-fix-remaining-workflow-tables-audit-columns
--comment: Add missing created_by and last_modified_by columns to remaining workflow tables (with existence checks)

-- Add missing audit columns to workflow_transitions table (only if they don't exist)
SET @sql = (SELECT IF(
    (SELECT COUNT(*) FROM INFORMATION_SCHEMA.COLUMNS 
     WHERE TABLE_SCHEMA = DATABASE() 
     AND TABLE_NAME = 'workflow_transitions' 
     AND COLUMN_NAME = 'last_modified_date') = 0,
    'ALTER TABLE workflow_transitions ADD COLUMN last_modified_date TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP AFTER created_date',
    'SELECT "Column last_modified_date already exists in workflow_transitions" AS message'
));
PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

SET @sql = (SELECT IF(
    (SELECT COUNT(*) FROM INFORMATION_SCHEMA.COLUMNS 
     WHERE TABLE_SCHEMA = DATABASE() 
     AND TABLE_NAME = 'workflow_transitions' 
     AND COLUMN_NAME = 'created_by') = 0,
    'ALTER TABLE workflow_transitions ADD COLUMN created_by VARCHAR(255) NOT NULL DEFAULT "system" AFTER last_modified_date',
    'SELECT "Column created_by already exists in workflow_transitions" AS message'
));
PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

SET @sql = (SELECT IF(
    (SELECT COUNT(*) FROM INFORMATION_SCHEMA.COLUMNS 
     WHERE TABLE_SCHEMA = DATABASE() 
     AND TABLE_NAME = 'workflow_transitions' 
     AND COLUMN_NAME = 'last_modified_by') = 0,
    'ALTER TABLE workflow_transitions ADD COLUMN last_modified_by VARCHAR(255) AFTER created_by',
    'SELECT "Column last_modified_by already exists in workflow_transitions" AS message'
));
PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

-- Add missing audit columns to workflow_instances table (only if they don't exist)
SET @sql = (SELECT IF(
    (SELECT COUNT(*) FROM INFORMATION_SCHEMA.COLUMNS 
     WHERE TABLE_SCHEMA = DATABASE() 
     AND TABLE_NAME = 'workflow_instances' 
     AND COLUMN_NAME = 'created_by') = 0,
    'ALTER TABLE workflow_instances ADD COLUMN created_by VARCHAR(255) NOT NULL DEFAULT "system" AFTER last_modified_date',
    'SELECT "Column created_by already exists in workflow_instances" AS message'
));
PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

SET @sql = (SELECT IF(
    (SELECT COUNT(*) FROM INFORMATION_SCHEMA.COLUMNS 
     WHERE TABLE_SCHEMA = DATABASE() 
     AND TABLE_NAME = 'workflow_instances' 
     AND COLUMN_NAME = 'last_modified_by') = 0,
    'ALTER TABLE workflow_instances ADD COLUMN last_modified_by VARCHAR(255) AFTER created_by',
    'SELECT "Column last_modified_by already exists in workflow_instances" AS message'
));
PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

-- Add missing audit columns to workflow_history table (only if they don't exist)
SET @sql = (SELECT IF(
    (SELECT COUNT(*) FROM INFORMATION_SCHEMA.COLUMNS 
     WHERE TABLE_SCHEMA = DATABASE() 
     AND TABLE_NAME = 'workflow_history' 
     AND COLUMN_NAME = 'created_date') = 0,
    'ALTER TABLE workflow_history ADD COLUMN created_date TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP AFTER event_timestamp',
    'SELECT "Column created_date already exists in workflow_history" AS message'
));
PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

SET @sql = (SELECT IF(
    (SELECT COUNT(*) FROM INFORMATION_SCHEMA.COLUMNS 
     WHERE TABLE_SCHEMA = DATABASE() 
     AND TABLE_NAME = 'workflow_history' 
     AND COLUMN_NAME = 'last_modified_date') = 0,
    'ALTER TABLE workflow_history ADD COLUMN last_modified_date TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP AFTER created_date',
    'SELECT "Column last_modified_date already exists in workflow_history" AS message'
));
PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

SET @sql = (SELECT IF(
    (SELECT COUNT(*) FROM INFORMATION_SCHEMA.COLUMNS 
     WHERE TABLE_SCHEMA = DATABASE() 
     AND TABLE_NAME = 'workflow_history' 
     AND COLUMN_NAME = 'created_by') = 0,
    'ALTER TABLE workflow_history ADD COLUMN created_by VARCHAR(255) NOT NULL DEFAULT "system" AFTER last_modified_date',
    'SELECT "Column created_by already exists in workflow_history" AS message'
));
PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

SET @sql = (SELECT IF(
    (SELECT COUNT(*) FROM INFORMATION_SCHEMA.COLUMNS 
     WHERE TABLE_SCHEMA = DATABASE() 
     AND TABLE_NAME = 'workflow_history' 
     AND COLUMN_NAME = 'last_modified_by') = 0,
    'ALTER TABLE workflow_history ADD COLUMN last_modified_by VARCHAR(255) AFTER created_by',
    'SELECT "Column last_modified_by already exists in workflow_history" AS message'
));
PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

-- Update existing records with default values
UPDATE workflow_transitions SET created_by = 'system' WHERE created_by IS NULL OR created_by = '';
UPDATE workflow_transitions SET last_modified_by = 'system' WHERE last_modified_by IS NULL OR last_modified_by = '';

UPDATE workflow_instances SET created_by = 'system' WHERE created_by IS NULL OR created_by = '';
UPDATE workflow_instances SET last_modified_by = 'system' WHERE last_modified_by IS NULL OR last_modified_by = '';

UPDATE workflow_history SET created_by = 'system' WHERE created_by IS NULL OR created_by = '';
UPDATE workflow_history SET last_modified_by = 'system' WHERE last_modified_by IS NULL OR last_modified_by = '';

-- Remove default constraints after updating existing records
SET @sql = (SELECT IF(
    (SELECT COUNT(*) FROM INFORMATION_SCHEMA.COLUMNS 
     WHERE TABLE_SCHEMA = DATABASE() 
     AND TABLE_NAME = 'workflow_transitions' 
     AND COLUMN_NAME = 'created_by' 
     AND COLUMN_DEFAULT = 'system') > 0,
    'ALTER TABLE workflow_transitions ALTER COLUMN created_by DROP DEFAULT',
    'SELECT "No default to drop for workflow_transitions.created_by" AS message'
));
PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

SET @sql = (SELECT IF(
    (SELECT COUNT(*) FROM INFORMATION_SCHEMA.COLUMNS 
     WHERE TABLE_SCHEMA = DATABASE() 
     AND TABLE_NAME = 'workflow_instances' 
     AND COLUMN_NAME = 'created_by' 
     AND COLUMN_DEFAULT = 'system') > 0,
    'ALTER TABLE workflow_instances ALTER COLUMN created_by DROP DEFAULT',
    'SELECT "No default to drop for workflow_instances.created_by" AS message'
));
PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

SET @sql = (SELECT IF(
    (SELECT COUNT(*) FROM INFORMATION_SCHEMA.COLUMNS 
     WHERE TABLE_SCHEMA = DATABASE() 
     AND TABLE_NAME = 'workflow_history' 
     AND COLUMN_NAME = 'created_by' 
     AND COLUMN_DEFAULT = 'system') > 0,
    'ALTER TABLE workflow_history ALTER COLUMN created_by DROP DEFAULT',
    'SELECT "No default to drop for workflow_history.created_by" AS message'
));
PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;