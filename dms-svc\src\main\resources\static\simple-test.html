<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Simple GraphQL Test</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 600px;
            margin: 50px auto;
            padding: 20px;
        }
        .result {
            background: #f5f5f5;
            padding: 15px;
            margin: 10px 0;
            border-radius: 5px;
            white-space: pre-wrap;
        }
        .error {
            background: #ffebee;
            color: #c62828;
        }
        .success {
            background: #e8f5e8;
            color: #2e7d32;
        }
        button {
            background: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover {
            background: #0056b3;
        }
    </style>
</head>
<body>
    <h1>Simple GraphQL Test</h1>
    
    <p>This page tests basic GraphQL connectivity without complex JavaScript.</p>
    
    <button onclick="testBasicGraphQL()">Test Basic GraphQL</button>
    <button onclick="testGraphiQLAccess()">Test GraphiQL Access</button>
    <button onclick="checkConsoleErrors()">Check Console</button>
    
    <div id="result" class="result">Click a button to test...</div>

    <script>
        function log(message, isError = false) {
            const resultDiv = document.getElementById('result');
            resultDiv.className = 'result ' + (isError ? 'error' : 'success');
            resultDiv.textContent = message;
            console.log(message);
        }

        async function testBasicGraphQL() {
            log('Testing basic GraphQL endpoint...');
            
            try {
                const response = await fetch('/graphql', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({
                        query: '{ __typename }'
                    })
                });

                const text = await response.text();
                log(`GraphQL Response (${response.status}):\n${text}`, !response.ok);
                
            } catch (error) {
                log(`GraphQL Test Failed:\n${error.message}`, true);
            }
        }

        async function testGraphiQLAccess() {
            log('Testing GraphiQL access...');
            
            try {
                const response = await fetch('/graphiql');
                const text = await response.text();
                
                if (response.ok) {
                    log(`GraphiQL accessible (${response.status})\nContent length: ${text.length} chars\nContent type: ${response.headers.get('content-type')}`);
                } else {
                    log(`GraphiQL access failed (${response.status}):\n${text}`, true);
                }
                
            } catch (error) {
                log(`GraphiQL Test Failed:\n${error.message}`, true);
            }
        }

        function checkConsoleErrors() {
            log('Check browser console (F12) for JavaScript errors.\n\nCommon issues:\n- CORS errors\n- Network connectivity\n- JavaScript syntax errors\n- Security policy violations');
        }

        // Auto-run basic test on page load
        window.addEventListener('load', function() {
            setTimeout(testBasicGraphQL, 1000);
        });

        // Log any JavaScript errors
        window.addEventListener('error', function(e) {
            console.error('JavaScript Error:', e.error);
            log(`JavaScript Error: ${e.message}\nFile: ${e.filename}\nLine: ${e.lineno}`, true);
        });
    </script>
</body>
</html>
