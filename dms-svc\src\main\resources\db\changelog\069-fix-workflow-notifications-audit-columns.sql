--liquibase formatted sql

--changeset system:069-fix-workflow-notifications-audit-columns
--comment: Add missing BaseEntity audit columns to workflow_notifications table

-- Add only the missing BaseEntity audit columns to workflow_notifications table
-- (created_date and last_modified_date already exist)
ALTER TABLE workflow_notifications 
ADD COLUMN created_by VARCHAR(100),
ADD COLUMN last_modified_by VARCHAR(100);

-- Update existing records with default audit values for the new columns
UPDATE workflow_notifications 
SET created_by = COALESCE(created_by, 'system'),
    last_modified_by = COALESCE(last_modified_by, 'system')
WHERE created_by IS NULL OR last_modified_by IS NULL;