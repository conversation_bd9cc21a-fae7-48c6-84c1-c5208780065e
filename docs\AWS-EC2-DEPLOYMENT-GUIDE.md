# AWS EC2 Deployment Guide with CORS Configuration

## 🚨 **CORS Issue - Critical for Frontend Integration**

**YES, you need to worry about CORS when deploying to AWS EC2!** Your current configuration uses wildcards (`*`) which **will not work** with credentials enabled in production.

## ⚠️ **Current Problem**

```properties
# This WILL FAIL in production browsers:
spring.graphql.cors.allowed-origins=*
spring.graphql.cors.allow-credentials=true
```

**Browser Error:** `"Cannot use wildcard in Access-Control-Allow-Origin when credentials flag is true"`

## ✅ **Solution: Proper CORS Configuration**

### **1. AWS EC2 Deployment Configuration**

I've created `docker-compose.aws-ec2.yml` and AWS-specific application properties that properly handle CORS.

### **2. CORS Configuration Options**

#### **Option A: Specific Origins (Recommended)**
```bash
# In .env.aws-ec2 file
CORS_ALLOWED_ORIGINS=https://your-frontend-domain.com,https://www.your-frontend-domain.com
```

#### **Option B: Origin Patterns (for subdomains)**
```bash
# If you need subdomain support
CORS_ALLOWED_ORIGIN_PATTERNS=https://*.your-domain.com
```

## 🚀 **AWS EC2 Deployment Steps**

### **Step 1: Prepare AWS EC2 Instance**

```bash
# Launch EC2 instance (Ubuntu 22.04 LTS recommended)
# Instance type: t3.medium or larger (4GB+ RAM)
# Security Group: Open ports 22, 80, 443, 9091, 9093

# Connect to EC2 instance
ssh -i your-key.pem ubuntu@your-ec2-public-ip
```

### **Step 2: Install Docker and Docker Compose**

```bash
# Update system
sudo apt update && sudo apt upgrade -y

# Install Docker
curl -fsSL https://get.docker.com -o get-docker.sh
sudo sh get-docker.sh
sudo usermod -aG docker ubuntu

# Install Docker Compose
sudo curl -L "https://github.com/docker/compose/releases/latest/download/docker-compose-$(uname -s)-$(uname -m)" -o /usr/local/bin/docker-compose
sudo chmod +x /usr/local/bin/docker-compose

# Logout and login again for group changes
exit
ssh -i your-key.pem ubuntu@your-ec2-public-ip
```

### **Step 3: Deploy Your Application**

```bash
# Clone your repository
git clone https://github.com/your-username/grc-platform-v4.git
cd grc-platform-v4

# Configure environment for AWS EC2
cp .env.aws-ec2.template .env.aws-ec2

# Edit the environment file
nano .env.aws-ec2
```

### **Step 4: Configure CORS for Your Frontend**

```bash
# In .env.aws-ec2, set your frontend URLs:

# Example 1: Angular app on Netlify
CORS_ALLOWED_ORIGINS=https://your-app.netlify.app,https://your-app.vercel.app

# Example 2: React app on custom domain
CORS_ALLOWED_ORIGINS=https://app.yourcompany.com,https://admin.yourcompany.com

# Example 3: Multiple environments
CORS_ALLOWED_ORIGINS=https://dev.yourapp.com,https://staging.yourapp.com,https://yourapp.com

# Your EC2 public IP (get from AWS console)
AWS_EC2_PUBLIC_IP=************

# If you have a domain name
AWS_EC2_DOMAIN=api.yourcompany.com
```

### **Step 5: Start Services**

```bash
# Start with AWS EC2 configuration
docker-compose -f docker-compose.aws-ec2.yml --env-file .env.aws-ec2 up -d

# Check status
docker-compose -f docker-compose.aws-ec2.yml ps

# View logs
docker-compose -f docker-compose.aws-ec2.yml logs -f
```

## 🌐 **Frontend Integration**

### **Service URLs on AWS EC2**

```javascript
// Your frontend will call these URLs:
const API_CONFIG = {
  DMS_SERVICE: 'http://your-ec2-public-ip:9093/dms/graphql',
  NOTIFICATION_SERVICE: 'http://your-ec2-public-ip:9091/graphql'
};

// Or with domain name:
const API_CONFIG = {
  DMS_SERVICE: 'https://api.yourcompany.com/dms/graphql',
  NOTIFICATION_SERVICE: 'https://api.yourcompany.com/notifications/graphql'
};
```

### **Frontend CORS Headers**

Your frontend should include these headers:

```javascript
// Example for Angular HttpClient
const headers = {
  'Content-Type': 'application/json',
  'Authorization': 'Bearer ' + token,
  'X-Correlation-ID': generateCorrelationId()
};

// Example for Axios
axios.defaults.withCredentials = true;
axios.defaults.headers.common['Content-Type'] = 'application/json';
```

## 🔒 **Security Configuration**

### **AWS EC2 Security Group**

```bash
# Inbound Rules:
Port 22   (SSH)     - Your IP only
Port 80   (HTTP)    - 0.0.0.0/0
Port 443  (HTTPS)   - 0.0.0.0/0
Port 9091 (Notification) - 0.0.0.0/0
Port 9093 (DMS)     - 0.0.0.0/0

# Optional (for monitoring):
Port 3000 (Grafana) - Your IP only
Port 9090 (Prometheus) - Your IP only
```

### **Production Security Checklist**

- ✅ Change all default passwords
- ✅ Use strong JWT secrets
- ✅ Configure specific CORS origins
- ✅ Disable GraphiQL in production
- ✅ Use HTTPS with SSL certificates
- ✅ Set up proper backup strategy
- ✅ Configure monitoring and alerting

## 🔧 **Domain Name Setup (Optional)**

### **Using Route 53 + ALB**

```bash
# 1. Create Application Load Balancer
# 2. Configure target groups for ports 9091, 9093
# 3. Set up Route 53 DNS records
# 4. Configure SSL certificate with ACM

# Update CORS configuration:
CORS_ALLOWED_ORIGINS=https://your-frontend.com,https://www.your-frontend.com
```

## 🚨 **Common CORS Issues and Solutions**

### **Issue 1: Wildcard with Credentials**
```bash
# ❌ This fails:
CORS_ALLOWED_ORIGINS=*

# ✅ Use specific origins:
CORS_ALLOWED_ORIGINS=https://yourapp.com,https://www.yourapp.com
```

### **Issue 2: HTTP vs HTTPS Mismatch**
```bash
# ❌ Frontend on HTTPS, API on HTTP:
Frontend: https://yourapp.com
API: http://ec2-ip:9093

# ✅ Both should use same protocol:
Frontend: https://yourapp.com
API: https://api.yourapp.com
```

### **Issue 3: Missing Preflight Headers**
```bash
# Ensure these headers are allowed:
CORS_ALLOWED_HEADERS=Content-Type,Authorization,X-Correlation-ID,Accept
```

## 📊 **Testing CORS Configuration**

### **Test from Browser Console**

```javascript
// Test CORS from your frontend domain
fetch('http://your-ec2-ip:9093/dms/graphql', {
  method: 'POST',
  headers: {
    'Content-Type': 'application/json',
  },
  credentials: 'include',
  body: JSON.stringify({
    query: '{ __schema { types { name } } }'
  })
})
.then(response => response.json())
.then(data => console.log('CORS working:', data))
.catch(error => console.error('CORS error:', error));
```

### **Test with cURL**

```bash
# Test preflight request
curl -X OPTIONS \
  -H "Origin: https://your-frontend-domain.com" \
  -H "Access-Control-Request-Method: POST" \
  -H "Access-Control-Request-Headers: Content-Type,Authorization" \
  http://your-ec2-ip:9093/dms/graphql

# Should return CORS headers
```

## 🎯 **Quick Start Commands**

```bash
# 1. Deploy to AWS EC2
cp .env.aws-ec2.template .env.aws-ec2
# Edit .env.aws-ec2 with your settings

# 2. Start services
docker-compose -f docker-compose.aws-ec2.yml --env-file .env.aws-ec2 up -d

# 3. Test services
curl http://your-ec2-ip:9093/actuator/health
curl http://your-ec2-ip:9091/actuator/health

# 4. Test GraphQL endpoints
curl -X POST \
  -H "Content-Type: application/json" \
  -d '{"query":"{ __schema { types { name } } }"}' \
  http://your-ec2-ip:9093/dms/graphql
```

## ✅ **Summary**

**CORS Configuration is Critical:**
1. ✅ Use specific origins, not wildcards
2. ✅ Match protocol (HTTP/HTTPS) between frontend and API
3. ✅ Include all necessary headers
4. ✅ Test thoroughly before production deployment

**Your services will be accessible at:**
- **DMS Service**: `http://your-ec2-ip:9093/dms/graphql`
- **Notification Service**: `http://your-ec2-ip:9091/graphql`

**Frontend can call these APIs** as long as CORS is properly configured with your frontend domain! 🚀
