<?xml version="1.0" encoding="UTF-8"?><!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Strict//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-strict.dtd"><html xmlns="http://www.w3.org/1999/xhtml" lang="en"><head><meta http-equiv="Content-Type" content="text/html;charset=UTF-8"/><link rel="stylesheet" href="../jacoco-resources/report.css" type="text/css"/><link rel="shortcut icon" href="../jacoco-resources/report.gif" type="image/gif"/><title>TracingConfiguration.java</title><link rel="stylesheet" href="../jacoco-resources/prettify.css" type="text/css"/><script type="text/javascript" src="../jacoco-resources/prettify.js"></script></head><body onload="window['PR_TAB_WIDTH']=4;prettyPrint()"><div class="breadcrumb" id="breadcrumb"><span class="info"><a href="../jacoco-sessions.html" class="el_session">Sessions</a></span><a href="../index.html" class="el_report">dms-svc</a> &gt; <a href="index.source.html" class="el_package">com.ascentbusiness.dms_svc.config</a> &gt; <span class="el_source">TracingConfiguration.java</span></div><h1>TracingConfiguration.java</h1><pre class="source lang-java linenums">package com.ascentbusiness.dms_svc.config;

import io.opentelemetry.api.OpenTelemetry;
import io.opentelemetry.api.trace.Tracer;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

/**
 * Configuration for OpenTelemetry tracing
 */
@Configuration
<span class="fc" id="L13">public class TracingConfiguration {</span>

    @Value(&quot;${spring.application.name:dms-svc}&quot;)
    private String applicationName;

    /**
     * Provides a Tracer bean for manual instrumentation
     */
    @Bean
    public Tracer tracer(OpenTelemetry openTelemetry) {
<span class="fc" id="L23">        return openTelemetry.getTracer(applicationName);</span>
    }
}
</pre><div class="footer"><span class="right">Created with <a href="http://www.jacoco.org/jacoco">JaCoCo</a> 0.8.10.202304240956</span></div></body></html>