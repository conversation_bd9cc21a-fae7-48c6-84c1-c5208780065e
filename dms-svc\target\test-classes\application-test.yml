# Test configuration for enhanced audit system
spring:
  
  # Test database configuration
  datasource:
    url: jdbc:h2:mem:testdb;DB_CLOSE_DELAY=-1;DB_CLOSE_ON_EXIT=FALSE
    driver-class-name: org.h2.Driver
    username: sa
    password: 
    
  # JPA configuration for tests
  jpa:
    hibernate:
      ddl-auto: validate
    show-sql: false
    properties:
      hibernate:
        dialect: org.hibernate.dialect.H2Dialect
        format_sql: false

  # Liquibase configuration for tests - disabled for performance tests
  liquibase:
    enabled: false
    
  # Async configuration for tests
  task:
    execution:
      pool:
        core-size: 10
        max-size: 20
        queue-capacity: 500
        
# DMS specific test configuration
dms:
  audit:
    # Disable audit logging for integration tests to avoid transaction issues
    enabled: false
    async: false
    include-request-body: false
    include-response-body: false
    encryption:
      enabled: false

    # Export configuration for tests
    export:
      base-path: ${java.io.tmpdir}/audit-exports-test
      max-file-size: 10MB
      retention-days: 30

    # Verification configuration for tests
    verification:
      batch-size: 50
      scheduled-interval: 60000 # 1 minute for tests

    # Chain configuration for tests
    chain:
      hash-algorithm: SHA-256
      signature-algorithm: RSA-2048

  # JWT configuration for tests
  jwt:
    secret: test-secret-key-for-audit-system-testing-only-that-is-long-enough-for-HS256-algorithm
    expiration: 3600000 # 1 hour
    header: Authorization
    prefix: Bearer

  # URL upload configuration for tests
  upload:
    url:
      enabled: true
      timeout-ms: 30000
      max-file-size: 10485760 # 10MB
      max-redirects: 5
      user-agent: DMS-Service/1.0
      allowed-domains: ""
      validate-ssl: false
      allow-private-ips: true
      blocked-ports: "22,23,25"
      allowed-content-types: ""
      blocked-content-types: "text/html,application/javascript"
    network-path:
      enabled: true
      timeout-ms: 60000
      
# Security configuration for tests
security:
  jwt:
    secret: test-secret-key-for-audit-system-testing-only
    expiration: 3600000 # 1 hour
    
# Logging configuration for tests
logging:
  level:
    com.ascentbusiness.dms_svc: DEBUG
    org.springframework.security: DEBUG
    org.hibernate.SQL: ERROR
    org.hibernate.type.descriptor.sql.BasicBinder: ERROR
    
  pattern:
    console: "%d{yyyy-MM-dd HH:mm:ss} - %msg%n"
    
# Test-specific properties
test:
  audit:
    # Performance test thresholds
    performance:
      min-logs-per-second: 50
      min-verifications-per-second: 20
      min-export-records-per-second: 50
      max-memory-increase-mb: 100
      max-concurrent-threads: 20
      
    # Security test configuration
    security:
      rate-limit:
        requests-per-minute: 5
        
    # Integration test configuration
    integration:
      async-wait-timeout: 5000 # 5 seconds
      chain-verification-timeout: 10000 # 10 seconds
      
# H2 Console for debugging (test only)
h2:
  console:
    enabled: true
    path: /h2-console
    
# Management endpoints for test monitoring
management:
  endpoints:
    web:
      exposure:
        include: health,info,metrics,prometheus
  endpoint:
    health:
      show-details: always
      
# Test data configuration
testdata:
  audit:
    # Sample users for testing
    users:
      - username: test-user
        roles: [USER]
      - username: compliance-officer
        roles: [USER, COMPLIANCE_OFFICER]
      - username: auditor
        roles: [USER, AUDITOR]
      - username: security-officer
        roles: [USER, SECURITY_OFFICER]
      - username: system-admin
        roles: [USER, SYSTEM_ADMIN]
        
    # Sample compliance frameworks for testing
    frameworks:
      - id: 1
        name: GDPR
        type: PRIVACY
        region: EU
      - id: 2
        name: HIPAA
        type: HEALTHCARE
        region: US
      - id: 3
        name: SOX
        type: FINANCIAL
        region: US
        
    # Sample documents for testing
    documents:
      - id: 100
        name: test-document-1.pdf
        classification: CONFIDENTIAL
      - id: 101
        name: test-document-2.docx
        classification: INTERNAL
      - id: 102
        name: test-document-3.xlsx
        classification: RESTRICTED
