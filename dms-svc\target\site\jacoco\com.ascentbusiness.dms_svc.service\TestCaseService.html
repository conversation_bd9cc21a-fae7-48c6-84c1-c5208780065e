<?xml version="1.0" encoding="UTF-8"?><!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Strict//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-strict.dtd"><html xmlns="http://www.w3.org/1999/xhtml" lang="en"><head><meta http-equiv="Content-Type" content="text/html;charset=UTF-8"/><link rel="stylesheet" href="../jacoco-resources/report.css" type="text/css"/><link rel="shortcut icon" href="../jacoco-resources/report.gif" type="image/gif"/><title>TestCaseService</title><script type="text/javascript" src="../jacoco-resources/sort.js"></script></head><body onload="initialSort(['breadcrumb'])"><div class="breadcrumb" id="breadcrumb"><span class="info"><a href="../jacoco-sessions.html" class="el_session">Sessions</a></span><a href="../index.html" class="el_report">dms-svc</a> &gt; <a href="index.html" class="el_package">com.ascentbusiness.dms_svc.service</a> &gt; <span class="el_class">TestCaseService</span></div><h1>TestCaseService</h1><table class="coverage" cellspacing="0" id="coveragetable"><thead><tr><td class="sortable" id="a" onclick="toggleSort(this)">Element</td><td class="down sortable bar" id="b" onclick="toggleSort(this)">Missed Instructions</td><td class="sortable ctr2" id="c" onclick="toggleSort(this)">Cov.</td><td class="sortable bar" id="d" onclick="toggleSort(this)">Missed Branches</td><td class="sortable ctr2" id="e" onclick="toggleSort(this)">Cov.</td><td class="sortable ctr1" id="f" onclick="toggleSort(this)">Missed</td><td class="sortable ctr2" id="g" onclick="toggleSort(this)">Cxty</td><td class="sortable ctr1" id="h" onclick="toggleSort(this)">Missed</td><td class="sortable ctr2" id="i" onclick="toggleSort(this)">Lines</td><td class="sortable ctr1" id="j" onclick="toggleSort(this)">Missed</td><td class="sortable ctr2" id="k" onclick="toggleSort(this)">Methods</td></tr></thead><tfoot><tr><td>Total</td><td class="bar">806 of 917</td><td class="ctr2">12%</td><td class="bar">76 of 76</td><td class="ctr2">0%</td><td class="ctr1">59</td><td class="ctr2">62</td><td class="ctr1">180</td><td class="ctr2">202</td><td class="ctr1">21</td><td class="ctr2">24</td></tr></tfoot><tbody><tr><td id="a19"><a href="TestCaseService.java.html#L156" class="el_method">parseSummaryFile(String)</a></td><td class="bar" id="b0"><img src="../jacoco-resources/redbar.gif" width="120" height="10" title="107" alt="107"/></td><td class="ctr2" id="c3">0%</td><td class="bar" id="d1"><img src="../jacoco-resources/redbar.gif" width="50" height="10" title="10" alt="10"/></td><td class="ctr2" id="e0">0%</td><td class="ctr1" id="f1">6</td><td class="ctr2" id="g1">6</td><td class="ctr1" id="h0">27</td><td class="ctr2" id="i0">27</td><td class="ctr1" id="j0">1</td><td class="ctr2" id="k0">1</td></tr><tr><td id="a20"><a href="TestCaseService.java.html#L199" class="el_method">parseTestCaseFile(String, String)</a></td><td class="bar" id="b1"><img src="../jacoco-resources/redbar.gif" width="118" height="10" title="106" alt="106"/></td><td class="ctr2" id="c4">0%</td><td class="bar" id="d3"><img src="../jacoco-resources/redbar.gif" width="40" height="10" title="8" alt="8"/></td><td class="ctr2" id="e1">0%</td><td class="ctr1" id="f3">5</td><td class="ctr2" id="g3">5</td><td class="ctr1" id="h1">27</td><td class="ctr2" id="i1">27</td><td class="ctr1" id="j1">1</td><td class="ctr2" id="k1">1</td></tr><tr><td id="a5"><a href="TestCaseService.java.html#L297" class="el_method">getCoverageAreas(String)</a></td><td class="bar" id="b2"><img src="../jacoco-resources/redbar.gif" width="95" height="10" title="85" alt="85"/></td><td class="ctr2" id="c5">0%</td><td class="bar" id="d11"/><td class="ctr2" id="e11">n/a</td><td class="ctr1" id="f11">1</td><td class="ctr2" id="g11">1</td><td class="ctr1" id="h2">17</td><td class="ctr2" id="i2">17</td><td class="ctr1" id="j2">1</td><td class="ctr2" id="k2">1</td></tr><tr><td id="a7"><a href="TestCaseService.java.html#L318" class="el_method">getDescription(String)</a></td><td class="bar" id="b3"><img src="../jacoco-resources/redbar.gif" width="95" height="10" title="85" alt="85"/></td><td class="ctr2" id="c6">0%</td><td class="bar" id="d12"/><td class="ctr2" id="e12">n/a</td><td class="ctr1" id="f12">1</td><td class="ctr2" id="g12">1</td><td class="ctr1" id="h3">17</td><td class="ctr2" id="i3">17</td><td class="ctr1" id="j3">1</td><td class="ctr2" id="k3">1</td></tr><tr><td id="a18"><a href="TestCaseService.java.html#L239" class="el_method">parseCSVLine(String)</a></td><td class="bar" id="b4"><img src="../jacoco-resources/redbar.gif" width="75" height="10" title="67" alt="67"/></td><td class="ctr2" id="c7">0%</td><td class="bar" id="d2"><img src="../jacoco-resources/redbar.gif" width="50" height="10" title="10" alt="10"/></td><td class="ctr2" id="e2">0%</td><td class="ctr1" id="f2">6</td><td class="ctr2" id="g2">6</td><td class="ctr1" id="h5">12</td><td class="ctr2" id="i6">12</td><td class="ctr1" id="j4">1</td><td class="ctr2" id="k4">1</td></tr><tr><td id="a9"><a href="TestCaseService.java.html#L81" class="el_method">getTestCasesByCategory(String)</a></td><td class="bar" id="b5"><img src="../jacoco-resources/redbar.gif" width="72" height="10" title="65" alt="65"/></td><td class="ctr2" id="c8">0%</td><td class="bar" id="d8"><img src="../jacoco-resources/redbar.gif" width="10" height="10" title="2" alt="2"/></td><td class="ctr2" id="e3">0%</td><td class="ctr1" id="f8">2</td><td class="ctr2" id="g8">2</td><td class="ctr1" id="h4">15</td><td class="ctr2" id="i5">15</td><td class="ctr1" id="j5">1</td><td class="ctr2" id="k5">1</td></tr><tr><td id="a0"><a href="TestCaseService.java.html#L339" class="el_method">containsQuery(TestCaseResponse, String)</a></td><td class="bar" id="b6"><img src="../jacoco-resources/redbar.gif" width="67" height="10" title="60" alt="60"/></td><td class="ctr2" id="c9">0%</td><td class="bar" id="d0"><img src="../jacoco-resources/redbar.gif" width="120" height="10" title="24" alt="24"/></td><td class="ctr2" id="e4">0%</td><td class="ctr1" id="f0">13</td><td class="ctr2" id="g0">13</td><td class="ctr1" id="h10">8</td><td class="ctr2" id="i11">8</td><td class="ctr1" id="j6">1</td><td class="ctr2" id="k6">1</td></tr><tr><td id="a2"><a href="TestCaseService.java.html#L56" class="el_method">getAllTestCategoriesSummary()</a></td><td class="bar" id="b7"><img src="../jacoco-resources/redbar.gif" width="49" height="10" title="44" alt="44"/></td><td class="ctr2" id="c10">0%</td><td class="bar" id="d5"><img src="../jacoco-resources/redbar.gif" width="20" height="10" title="4" alt="4"/></td><td class="ctr2" id="e5">0%</td><td class="ctr1" id="f5">3</td><td class="ctr2" id="g5">3</td><td class="ctr1" id="h6">12</td><td class="ctr2" id="i7">12</td><td class="ctr1" id="j7">1</td><td class="ctr2" id="k7">1</td></tr><tr><td id="a21"><a href="TestCaseService.java.html#L141" class="el_method">searchTestCases(String)</a></td><td class="bar" id="b8"><img src="../jacoco-resources/redbar.gif" width="44" height="10" title="40" alt="40"/></td><td class="ctr2" id="c11">0%</td><td class="bar" id="d4"><img src="../jacoco-resources/redbar.gif" width="30" height="10" title="6" alt="6"/></td><td class="ctr2" id="e6">0%</td><td class="ctr1" id="f4">4</td><td class="ctr2" id="g4">4</td><td class="ctr1" id="h9">9</td><td class="ctr2" id="i10">9</td><td class="ctr1" id="j8">1</td><td class="ctr2" id="k8">1</td></tr><tr><td id="a10"><a href="TestCaseService.java.html#L271" class="el_method">getTestRange(List)</a></td><td class="bar" id="b9"><img src="../jacoco-resources/redbar.gif" width="42" height="10" title="38" alt="38"/></td><td class="ctr2" id="c12">0%</td><td class="bar" id="d6"><img src="../jacoco-resources/redbar.gif" width="20" height="10" title="4" alt="4"/></td><td class="ctr2" id="e7">0%</td><td class="ctr1" id="f6">3</td><td class="ctr2" id="g6">3</td><td class="ctr1" id="h7">12</td><td class="ctr2" id="i8">12</td><td class="ctr1" id="j9">1</td><td class="ctr2" id="k9">1</td></tr><tr><td id="a1"><a href="TestCaseService.java.html#L123" class="el_method">getAllCategories()</a></td><td class="bar" id="b10"><img src="../jacoco-resources/redbar.gif" width="37" height="10" title="33" alt="33"/></td><td class="ctr2" id="c13">0%</td><td class="bar" id="d9"><img src="../jacoco-resources/redbar.gif" width="10" height="10" title="2" alt="2"/></td><td class="ctr2" id="e8">0%</td><td class="ctr1" id="f9">2</td><td class="ctr2" id="g9">2</td><td class="ctr1" id="h8">11</td><td class="ctr2" id="i9">11</td><td class="ctr1" id="j10">1</td><td class="ctr2" id="k10">1</td></tr><tr><td id="a8"><a href="TestCaseService.java.html#L110" class="el_method">getTestCaseById(String, String)</a></td><td class="bar" id="b11"><img src="../jacoco-resources/redbar.gif" width="24" height="10" title="22" alt="22"/></td><td class="ctr2" id="c14">0%</td><td class="bar" id="d7"><img src="../jacoco-resources/redbar.gif" width="20" height="10" title="4" alt="4"/></td><td class="ctr2" id="e9">0%</td><td class="ctr1" id="f7">3</td><td class="ctr2" id="g7">3</td><td class="ctr1" id="h11">7</td><td class="ctr2" id="i12">7</td><td class="ctr1" id="j11">1</td><td class="ctr2" id="k11">1</td></tr><tr><td id="a13"><a href="TestCaseService.java.html#L130" class="el_method">lambda$getAllCategories$2(Path)</a></td><td class="bar" id="b12"><img src="../jacoco-resources/redbar.gif" width="11" height="10" title="10" alt="10"/></td><td class="ctr2" id="c15">0%</td><td class="bar" id="d10"><img src="../jacoco-resources/redbar.gif" width="10" height="10" title="2" alt="2"/></td><td class="ctr2" id="e10">0%</td><td class="ctr1" id="f10">2</td><td class="ctr2" id="g10">2</td><td class="ctr1" id="h13">1</td><td class="ctr2" id="i15">1</td><td class="ctr1" id="j12">1</td><td class="ctr2" id="k12">1</td></tr><tr><td id="a4"><a href="TestCaseService.java.html#L263" class="el_method">getCategoryFromFileName(String)</a></td><td class="bar" id="b13"><img src="../jacoco-resources/redbar.gif" width="8" height="10" title="8" alt="8"/></td><td class="ctr2" id="c16">0%</td><td class="bar" id="d13"/><td class="ctr2" id="e13">n/a</td><td class="ctr1" id="f13">1</td><td class="ctr2" id="g13">1</td><td class="ctr1" id="h14">1</td><td class="ctr2" id="i16">1</td><td class="ctr1" id="j13">1</td><td class="ctr2" id="k13">1</td></tr><tr><td id="a16"><a href="TestCaseService.java.html#L280" class="el_method">lambda$getTestRange$5(String)</a></td><td class="bar" id="b14"><img src="../jacoco-resources/redbar.gif" width="7" height="10" title="7" alt="7"/></td><td class="ctr2" id="c17">0%</td><td class="bar" id="d14"/><td class="ctr2" id="e14">n/a</td><td class="ctr1" id="f14">1</td><td class="ctr2" id="g14">1</td><td class="ctr1" id="h12">3</td><td class="ctr2" id="i14">3</td><td class="ctr1" id="j14">1</td><td class="ctr2" id="k14">1</td></tr><tr><td id="a14"><a href="TestCaseService.java.html#L131" class="el_method">lambda$getAllCategories$3(Path)</a></td><td class="bar" id="b15"><img src="../jacoco-resources/redbar.gif" width="6" height="10" title="6" alt="6"/></td><td class="ctr2" id="c18">0%</td><td class="bar" id="d15"/><td class="ctr2" id="e15">n/a</td><td class="ctr1" id="f15">1</td><td class="ctr2" id="g15">1</td><td class="ctr1" id="h15">1</td><td class="ctr2" id="i17">1</td><td class="ctr1" id="j15">1</td><td class="ctr2" id="k15">1</td></tr><tr><td id="a3"><a href="TestCaseService.java.html#L267" class="el_method">getCategoryFromDisplayName(String)</a></td><td class="bar" id="b16"><img src="../jacoco-resources/redbar.gif" width="5" height="10" title="5" alt="5"/></td><td class="ctr2" id="c19">0%</td><td class="bar" id="d16"/><td class="ctr2" id="e16">n/a</td><td class="ctr1" id="f16">1</td><td class="ctr2" id="g16">1</td><td class="ctr1" id="h16">1</td><td class="ctr2" id="i18">1</td><td class="ctr1" id="j16">1</td><td class="ctr2" id="k16">1</td></tr><tr><td id="a17"><a href="TestCaseService.java.html#L151" class="el_method">lambda$searchTestCases$4(String, TestCaseResponse)</a></td><td class="bar" id="b17"><img src="../jacoco-resources/redbar.gif" width="5" height="10" title="5" alt="5"/></td><td class="ctr2" id="c20">0%</td><td class="bar" id="d17"/><td class="ctr2" id="e17">n/a</td><td class="ctr1" id="f17">1</td><td class="ctr2" id="g17">1</td><td class="ctr1" id="h17">1</td><td class="ctr2" id="i19">1</td><td class="ctr1" id="j17">1</td><td class="ctr2" id="k17">1</td></tr><tr><td id="a12"><a href="TestCaseService.java.html#L129" class="el_method">lambda$getAllCategories$1(Path)</a></td><td class="bar" id="b18"><img src="../jacoco-resources/redbar.gif" width="5" height="10" title="5" alt="5"/></td><td class="ctr2" id="c21">0%</td><td class="bar" id="d18"/><td class="ctr2" id="e18">n/a</td><td class="ctr1" id="f18">1</td><td class="ctr2" id="g18">1</td><td class="ctr1" id="h18">1</td><td class="ctr2" id="i20">1</td><td class="ctr1" id="j18">1</td><td class="ctr2" id="k18">1</td></tr><tr><td id="a15"><a href="TestCaseService.java.html#L116" class="el_method">lambda$getTestCaseById$0(String, TestCaseResponse)</a></td><td class="bar" id="b19"><img src="../jacoco-resources/redbar.gif" width="5" height="10" title="5" alt="5"/></td><td class="ctr2" id="c22">0%</td><td class="bar" id="d19"/><td class="ctr2" id="e19">n/a</td><td class="ctr1" id="f19">1</td><td class="ctr2" id="g19">1</td><td class="ctr1" id="h19">1</td><td class="ctr2" id="i21">1</td><td class="ctr1" id="j19">1</td><td class="ctr2" id="k19">1</td></tr><tr><td id="a6"><a href="TestCaseService.java.html#L259" class="el_method">getCsvFileNameForCategory(String)</a></td><td class="bar" id="b20"><img src="../jacoco-resources/redbar.gif" width="3" height="10" title="3" alt="3"/></td><td class="ctr2" id="c23">0%</td><td class="bar" id="d20"/><td class="ctr2" id="e20">n/a</td><td class="ctr1" id="f20">1</td><td class="ctr2" id="g20">1</td><td class="ctr1" id="h20">1</td><td class="ctr2" id="i22">1</td><td class="ctr1" id="j20">1</td><td class="ctr2" id="k20">1</td></tr><tr><td id="a11"><a href="TestCaseService.java.html#L36" class="el_method">initializeCategoryDisplayNames()</a></td><td class="bar" id="b21"><img src="../jacoco-resources/greenbar.gif" width="108" height="10" title="97" alt="97"/></td><td class="ctr2" id="c0">100%</td><td class="bar" id="d21"/><td class="ctr2" id="e21">n/a</td><td class="ctr1" id="f21">0</td><td class="ctr2" id="g21">1</td><td class="ctr1" id="h21">0</td><td class="ctr2" id="i4">17</td><td class="ctr1" id="j21">0</td><td class="ctr2" id="k21">1</td></tr><tr><td id="a23"><a href="TestCaseService.java.html#L29" class="el_method">TestCaseService()</a></td><td class="bar" id="b22"><img src="../jacoco-resources/greenbar.gif" width="11" height="10" title="10" alt="10"/></td><td class="ctr2" id="c1">100%</td><td class="bar" id="d22"/><td class="ctr2" id="e22">n/a</td><td class="ctr1" id="f22">0</td><td class="ctr2" id="g22">1</td><td class="ctr1" id="h22">0</td><td class="ctr2" id="i13">4</td><td class="ctr1" id="j22">0</td><td class="ctr2" id="k22">1</td></tr><tr><td id="a22"><a href="TestCaseService.java.html#L24" class="el_method">static {...}</a></td><td class="bar" id="b23"><img src="../jacoco-resources/greenbar.gif" width="4" height="10" title="4" alt="4"/></td><td class="ctr2" id="c2">100%</td><td class="bar" id="d23"/><td class="ctr2" id="e23">n/a</td><td class="ctr1" id="f23">0</td><td class="ctr2" id="g23">1</td><td class="ctr1" id="h23">0</td><td class="ctr2" id="i23">1</td><td class="ctr1" id="j23">0</td><td class="ctr2" id="k23">1</td></tr></tbody></table><div class="footer"><span class="right">Created with <a href="http://www.jacoco.org/jacoco">JaCoCo</a> 0.8.10.202304240956</span></div></body></html>