/*
 * Copyright (c) 2024 Ascent Business Technology
 * All rights reserved.
 */

package com.ascentbusiness.dms_svc.service;

import com.ascentbusiness.dms_svc.config.UrlUploadConfig;
import com.ascentbusiness.dms_svc.exception.DmsBusinessException;
import com.ascentbusiness.dms_svc.util.ByteArrayMultipartFile;
import com.ascentbusiness.dms_svc.util.CorrelationIdUtil;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.web.multipart.MultipartFile;

import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.io.InputStream;
import java.net.*;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.time.Duration;
import java.util.Map;

/**
 * Service for downloading files from URLs and converting them to MultipartFile instances.
 * 
 * <p>This service handles secure downloading of files from HTTP/HTTPS URLs with
 * comprehensive validation, security checks, and error handling. It supports
 * configurable timeouts, size limits, and domain restrictions.
 * 
 * <AUTHOR> Development Team
 * @version 1.0.0
 * @since 1.0.0
 */
@Service
@RequiredArgsConstructor
@Slf4j
public class UrlDownloadService {
    
    private final UrlUploadConfig urlUploadConfig;
    
    /**
     * Download a file from URL and return as MultipartFile.
     * 
     * @param url the URL to download from
     * @param suggestedFilename suggested filename (optional)
     * @return MultipartFile containing the downloaded content
     * @throws IOException if download fails
     * @throws DmsBusinessException if validation fails
     */
    public MultipartFile downloadFromUrl(String url, String suggestedFilename) throws IOException {
        String correlationId = CorrelationIdUtil.getCurrentCorrelationId();
        
        log.info("Starting URL download: {} [{}]", url, correlationId);
        
        // Validate URL
        validateUrl(url);
        
        URL urlObj = new URL(url);
        String filename = suggestedFilename != null ? suggestedFilename : extractFilenameFromUrl(urlObj);
        
        // Create connection with security settings
        HttpURLConnection connection = createSecureConnection(urlObj);
        
        try {
            // Check response
            int responseCode = connection.getResponseCode();
            if (responseCode != HttpURLConnection.HTTP_OK) {
                throw new DmsBusinessException(
                    "Failed to download file from URL: HTTP " + responseCode,
                    "URL_DOWNLOAD_FAILED",
                    Map.of("url", url, "responseCode", responseCode)
                );
            }
            
            // Validate content type
            String contentType = connection.getContentType();
            validateContentType(contentType);
            
            // Check content length
            long contentLength = connection.getContentLengthLong();
            if (contentLength > urlUploadConfig.getMaxFileSize()) {
                throw new DmsBusinessException(
                    "File too large: " + contentLength + " bytes (max: " + urlUploadConfig.getMaxFileSize() + ")",
                    "FILE_TOO_LARGE",
                    Map.of("size", contentLength, "maxSize", urlUploadConfig.getMaxFileSize())
                );
            }
            
            // Download content
            byte[] content = downloadContent(connection.getInputStream(), contentLength);
            
            log.info("Successfully downloaded file from URL: {} -> {} bytes [{}]", 
                    url, content.length, correlationId);
            
            return new ByteArrayMultipartFile(content, filename, contentType);
            
        } finally {
            connection.disconnect();
        }
    }
    
    /**
     * Download a file from local file path and return as MultipartFile.
     * 
     * @param filePath the local file path
     * @param suggestedFilename suggested filename (optional)
     * @return MultipartFile containing the file content
     * @throws IOException if file reading fails
     */
    public MultipartFile downloadFromPath(String filePath, String suggestedFilename) throws IOException {
        String correlationId = CorrelationIdUtil.getCurrentCorrelationId();
        
        log.info("Reading file from path: {} [{}]", filePath, correlationId);
        
        Path path = Paths.get(filePath);
        
        // Security validation
        validateFilePath(path);
        
        if (!Files.exists(path)) {
            throw new DmsBusinessException(
                "File not found: " + filePath,
                "FILE_NOT_FOUND",
                Map.of("path", filePath)
            );
        }
        
        if (!Files.isReadable(path)) {
            throw new DmsBusinessException(
                "File not readable: " + filePath,
                "FILE_NOT_READABLE",
                Map.of("path", filePath)
            );
        }
        
        // Check file size
        long fileSize = Files.size(path);
        if (fileSize > urlUploadConfig.getMaxFileSize()) {
            throw new DmsBusinessException(
                "File too large: " + fileSize + " bytes (max: " + urlUploadConfig.getMaxFileSize() + ")",
                "FILE_TOO_LARGE",
                Map.of("size", fileSize, "maxSize", urlUploadConfig.getMaxFileSize())
            );
        }
        
        // Read file content
        byte[] content = Files.readAllBytes(path);
        String filename = suggestedFilename != null ? suggestedFilename : path.getFileName().toString();
        String contentType = Files.probeContentType(path);
        
        log.info("Successfully read file from path: {} -> {} bytes [{}]", 
                filePath, content.length, correlationId);
        
        return new ByteArrayMultipartFile(content, filename, contentType);
    }
    
    /**
     * Validate URL for security and configuration compliance.
     */
    private void validateUrl(String url) {
        if (!urlUploadConfig.isEnabled()) {
            throw new DmsBusinessException(
                "URL uploads are disabled",
                "URL_UPLOAD_DISABLED"
            );
        }
        
        try {
            URL urlObj = new URL(url);
            
            // Check protocol
            String protocol = urlObj.getProtocol().toLowerCase();
            if (!"http".equals(protocol) && !"https".equals(protocol)) {
                throw new DmsBusinessException(
                    "Unsupported protocol: " + protocol,
                    "UNSUPPORTED_PROTOCOL",
                    Map.of("protocol", protocol)
                );
            }
            
            // Check domain
            String host = urlObj.getHost().toLowerCase();
            if (!urlUploadConfig.isDomainAllowed(host)) {
                throw new DmsBusinessException(
                    "Domain not allowed: " + host,
                    "DOMAIN_NOT_ALLOWED",
                    Map.of("domain", host)
                );
            }
            
            // Check port
            int port = urlObj.getPort();
            if (port == -1) {
                port = urlObj.getDefaultPort();
            }
            if (urlUploadConfig.isPortBlocked(port)) {
                throw new DmsBusinessException(
                    "Port blocked: " + port,
                    "PORT_BLOCKED",
                    Map.of("port", port)
                );
            }
            
            // Check for private IPs if not allowed
            if (!urlUploadConfig.isAllowPrivateIps()) {
                InetAddress address = InetAddress.getByName(host);
                if (address.isSiteLocalAddress() || address.isLoopbackAddress() || address.isLinkLocalAddress()) {
                    throw new DmsBusinessException(
                        "Private IP addresses not allowed: " + address.getHostAddress(),
                        "PRIVATE_IP_NOT_ALLOWED",
                        Map.of("ip", address.getHostAddress())
                    );
                }
            }
            
        } catch (MalformedURLException | UnknownHostException e) {
            throw new DmsBusinessException(
                "Invalid URL: " + e.getMessage(),
                "INVALID_URL",
                Map.of("url", url, "error", e.getMessage())
            );
        }
    }
    
    /**
     * Validate file path for security.
     */
    private void validateFilePath(Path path) {
        try {
            // Normalize the path to resolve any .. or . components
            Path normalizedPath = path.normalize().toAbsolutePath();
            String pathStr = normalizedPath.toString();
            
            // Check for path traversal by comparing normalized path components
            // This is more robust than simple string matching
            Path currentPath = normalizedPath;
            while (currentPath != null) {
                String fileName = currentPath.getFileName() != null ?
                    currentPath.getFileName().toString() : "";
                
                // Check for explicit traversal attempts
                if ("..".equals(fileName)) {
                    throw new DmsBusinessException(
                        "Path traversal not allowed: " + path.toString(),
                        "PATH_TRAVERSAL_DETECTED",
                        Map.of("path", path.toString())
                    );
                }
                
                currentPath = currentPath.getParent();
            }
            
            // Additional security checks can be added here
            
        } catch (Exception e) {
            if (e instanceof DmsBusinessException) {
                throw e;
            }
            // If path normalization fails, it might be malicious
            throw new DmsBusinessException(
                "Invalid file path: " + path.toString(),
                "INVALID_FILE_PATH",
                Map.of("path", path.toString(), "error", e.getMessage())
            );
        }
    }
    
    /**
     * Validate content type.
     */
    private void validateContentType(String contentType) {
        if (contentType != null && !urlUploadConfig.isContentTypeAllowed(contentType)) {
            throw new DmsBusinessException(
                "Content type not allowed: " + contentType,
                "CONTENT_TYPE_NOT_ALLOWED",
                Map.of("contentType", contentType)
            );
        }
    }
    
    /**
     * Create a secure HTTP connection.
     */
    private HttpURLConnection createSecureConnection(URL url) throws IOException {
        HttpURLConnection connection = (HttpURLConnection) url.openConnection();
        
        // Set timeouts
        connection.setConnectTimeout(urlUploadConfig.getTimeoutMs());
        connection.setReadTimeout(urlUploadConfig.getTimeoutMs());
        
        // Set user agent
        connection.setRequestProperty("User-Agent", urlUploadConfig.getUserAgent());
        
        // Set other headers
        connection.setRequestProperty("Accept", "*/*");
        connection.setRequestProperty("Connection", "close");
        
        // Configure redirects
        connection.setInstanceFollowRedirects(true);
        
        // SSL validation
        if (url.getProtocol().equals("https") && !urlUploadConfig.isValidateSsl()) {
            // Note: In production, SSL validation should always be enabled
            log.warn("SSL validation is disabled - this is not recommended for production");
        }
        
        return connection;
    }
    
    /**
     * Download content from input stream with size limits.
     */
    private byte[] downloadContent(InputStream inputStream, long expectedSize) throws IOException {
        ByteArrayOutputStream buffer = new ByteArrayOutputStream();
        byte[] data = new byte[8192];
        long totalRead = 0;
        int bytesRead;
        
        while ((bytesRead = inputStream.read(data)) != -1) {
            totalRead += bytesRead;
            
            // Check size limit
            if (totalRead > urlUploadConfig.getMaxFileSize()) {
                throw new DmsBusinessException(
                    "File too large during download: " + totalRead + " bytes",
                    "FILE_TOO_LARGE_DURING_DOWNLOAD",
                    Map.of("size", totalRead, "maxSize", urlUploadConfig.getMaxFileSize())
                );
            }
            
            buffer.write(data, 0, bytesRead);
        }
        
        return buffer.toByteArray();
    }
    
    /**
     * Extract filename from URL.
     */
    private String extractFilenameFromUrl(URL url) {
        String path = url.getPath();
        if (path == null || path.isEmpty() || path.equals("/")) {
            return "downloaded-file";
        }
        
        String filename = path.substring(path.lastIndexOf('/') + 1);
        if (filename.isEmpty()) {
            return "downloaded-file";
        }
        
        // Remove query parameters if present
        int queryIndex = filename.indexOf('?');
        if (queryIndex > 0) {
            filename = filename.substring(0, queryIndex);
        }
        
        return filename;
    }
}