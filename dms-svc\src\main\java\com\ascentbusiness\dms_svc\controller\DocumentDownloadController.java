/*
 * Copyright (c) 2024 Ascent Business Technology
 * All rights reserved.
 */

package com.ascentbusiness.dms_svc.controller;

import com.ascentbusiness.dms_svc.annotation.RateLimit;
import com.ascentbusiness.dms_svc.entity.Document;
import com.ascentbusiness.dms_svc.service.DocumentService;
import com.ascentbusiness.dms_svc.service.AuditService;
import com.ascentbusiness.dms_svc.enums.AuditAction;
import com.ascentbusiness.dms_svc.exception.DocumentNotFoundException;
import com.ascentbusiness.dms_svc.exception.UnauthorizedException;
import com.ascentbusiness.dms_svc.versioning.ApiVersion;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.core.io.ByteArrayResource;
import org.springframework.core.io.Resource;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.security.core.Authentication;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.web.bind.annotation.*;

import java.io.UnsupportedEncodingException;
import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;
import java.time.Instant;
import java.util.HashMap;
import java.util.Map;

/**
 * REST controller for document download operations.
 * Provides versioned API endpoints for downloading documents with proper file streaming,
 * content headers, and audit logging.
 * 
 * <p>This controller handles binary file downloads which are not suitable for GraphQL.
 * It provides proper HTTP response headers for file downloads including content-disposition,
 * content-type, and content-length headers.
 * 
 * <p>API Versioning:
 * <ul>
 *   <li>v1: Basic download functionality with audit logging</li>
 *   <li>v2: Enhanced download with metadata and streaming support</li>
 * </ul>
 * 
 * <AUTHOR> Development Team
 * @version 2.0.0
 * @since 1.0.0
 */
@RestController
@RequestMapping("/api")
@RequiredArgsConstructor
@Slf4j
public class DocumentDownloadController {

    private final DocumentService documentService;
    private final AuditService auditService;

    /**
     * Download document by ID (API v1).
     * 
     * <p>Basic download functionality that returns the document file content
     * with appropriate HTTP headers for browser download.
     * 
     * @param id the document ID to download
     * @return ResponseEntity containing the file content as byte array
     * @throws DocumentNotFoundException if document doesn't exist
     * @throws UnauthorizedException if user lacks READ permission
     */
    @GetMapping("/v1/documents/{id}/download")
    @ApiVersion(since = "1.0", description = "Download document by ID")
    @RateLimit(value = 30, window = 60, type = RateLimit.RateLimitType.DOCUMENT_DOWNLOAD,
               message = "Document download rate limit exceeded. Please wait before downloading again.")
    @PreAuthorize("hasAnyRole('USER', 'ADMIN', 'MANAGER')")
    public ResponseEntity<byte[]> downloadDocumentV1(@PathVariable Long id) {
        log.info("API v1: Download request for document ID: {}", id);
        
        try {
            // Get document metadata for headers
            Document document = documentService.getDocumentById(id);
            
            // Download actual file content
            byte[] fileContent = documentService.downloadDocument(id);
            
            // Build response headers
            HttpHeaders headers = new HttpHeaders();
            headers.setContentType(MediaType.parseMediaType(
                document.getMimeType() != null ? document.getMimeType() : "application/octet-stream"));
            headers.setContentLength(fileContent.length);
            
            // Set content-disposition for download
            String filename = sanitizeFilename(document.getOriginalFileName());
            headers.add("Content-Disposition", "attachment; filename=\"" + filename + "\"");
            
            // Add custom headers
            headers.add("X-Document-ID", document.getId().toString());
            headers.add("X-Document-Version", document.getVersion().toString());
            headers.add("X-API-Version", "1.0");
            
            log.info("API v1: Successfully downloaded document ID: {}, filename: {}, size: {} bytes", 
                    id, filename, fileContent.length);
            
            return ResponseEntity.ok()
                    .headers(headers)
                    .body(fileContent);
                    
        } catch (DocumentNotFoundException e) {
            log.warn("API v1: Document not found for ID: {}", id);
            throw e;
        } catch (UnauthorizedException e) {
            log.warn("API v1: Unauthorized download attempt for document ID: {} by user: {}", 
                    id, getCurrentUserId());
            throw e;
        } catch (Exception e) {
            log.error("API v1: Error downloading document ID: {}", id, e);
            throw new RuntimeException("Failed to download document: " + e.getMessage(), e);
        }
    }

    /**
     * Download document by ID (API v2).
     * 
     * <p>Enhanced download functionality with improved streaming support,
     * additional metadata headers, and better error handling.
     * 
     * @param id the document ID to download
     * @param inline optional parameter to display inline instead of download (default: false)
     * @return ResponseEntity containing the file content as Resource for better streaming
     * @throws DocumentNotFoundException if document doesn't exist
     * @throws UnauthorizedException if user lacks READ permission
     */
    @GetMapping("/v2/documents/{id}/download")
    @ApiVersion(since = "2.0", description = "Enhanced download with streaming support")
    @RateLimit(value = 30, window = 60, type = RateLimit.RateLimitType.DOCUMENT_DOWNLOAD,
               message = "Document download rate limit exceeded. Please wait before downloading again.")
    @PreAuthorize("hasAnyRole('USER', 'ADMIN', 'MANAGER')")
    public ResponseEntity<Resource> downloadDocumentV2(
            @PathVariable Long id,
            @RequestParam(defaultValue = "false") boolean inline) {
        
        log.info("API v2: Download request for document ID: {}, inline: {}", id, inline);
        
        try {
            // Get document metadata for headers
            Document document = documentService.getDocumentById(id);
            
            // Download actual file content
            byte[] fileContent = documentService.downloadDocument(id);
            
            // Create resource for streaming
            ByteArrayResource resource = new ByteArrayResource(fileContent);
            
            // Build enhanced response headers
            HttpHeaders headers = new HttpHeaders();
            headers.setContentType(MediaType.parseMediaType(
                document.getMimeType() != null ? document.getMimeType() : "application/octet-stream"));
            headers.setContentLength(fileContent.length);
            
            // Set content-disposition based on inline parameter
            String filename = sanitizeFilename(document.getOriginalFileName());
            if (inline) {
                headers.add("Content-Disposition", "inline; filename=\"" + filename + "\"");
            } else {
                headers.add("Content-Disposition", "attachment; filename=\"" + filename + "\"");
            }
            
            // Add comprehensive metadata headers
            headers.add("X-Document-ID", document.getId().toString());
            headers.add("X-Document-Name", document.getName());
            headers.add("X-Document-Version", document.getVersion().toString());
            headers.add("X-Document-Status", document.getStatus().toString());
            headers.add("X-Document-Creator", document.getCreatorUserId());
            headers.add("X-Document-Created", document.getCreatedDate().toString());
            headers.add("X-Document-Modified", document.getLastModifiedDate().toString());
            headers.add("X-Storage-Provider", document.getStorageProvider().toString());
            headers.add("X-API-Version", "2.0");
            
            // Add cache control headers
            headers.setCacheControl("private, max-age=3600"); // Cache for 1 hour
            headers.add("ETag", "\"" + document.getId() + "-" + document.getVersion() + "\"");
            
            log.info("API v2: Successfully downloaded document ID: {}, filename: {}, size: {} bytes, inline: {}", 
                    id, filename, fileContent.length, inline);
            
            return ResponseEntity.ok()
                    .headers(headers)
                    .body(resource);
                    
        } catch (DocumentNotFoundException e) {
            log.warn("API v2: Document not found for ID: {}", id);
            throw e;
        } catch (UnauthorizedException e) {
            log.warn("API v2: Unauthorized download attempt for document ID: {} by user: {}", 
                    id, getCurrentUserId());
            throw e;
        } catch (Exception e) {
            log.error("API v2: Error downloading document ID: {}", id, e);
            throw new RuntimeException("Failed to download document: " + e.getMessage(), e);
        }
    }

    /**
     * Get download metadata without downloading the file (API v2).
     * 
     * <p>Returns document metadata and download information without transferring
     * the actual file content. Useful for checking file properties before download.
     * 
     * @param id the document ID
     * @return ResponseEntity containing document download metadata
     */
    @GetMapping("/v2/documents/{id}/download/info")
    @ApiVersion(since = "2.0", description = "Get download metadata without file content")
    @RateLimit(value = 60, window = 60, type = RateLimit.RateLimitType.SEARCH_OPERATION,
               message = "Download info rate limit exceeded.")
    @PreAuthorize("hasAnyRole('USER', 'ADMIN', 'MANAGER')")
    public ResponseEntity<Map<String, Object>> getDownloadInfo(@PathVariable Long id) {
        log.info("API v2: Download info request for document ID: {}", id);
        
        try {
            Document document = documentService.getDocumentById(id);
            
            Map<String, Object> downloadInfo = new HashMap<>();
            downloadInfo.put("documentId", document.getId());
            downloadInfo.put("name", document.getName());
            downloadInfo.put("originalFileName", document.getOriginalFileName());
            downloadInfo.put("fileSize", document.getFileSize());
            downloadInfo.put("mimeType", document.getMimeType());
            downloadInfo.put("version", document.getVersion());
            downloadInfo.put("status", document.getStatus());
            downloadInfo.put("storageProvider", document.getStorageProvider());
            downloadInfo.put("createdBy", document.getCreatorUserId());
            downloadInfo.put("createdDate", document.getCreatedDate());
            downloadInfo.put("lastModifiedDate", document.getLastModifiedDate());
            downloadInfo.put("downloadUrl", "/api/v2/documents/" + id + "/download");
            downloadInfo.put("inlineUrl", "/api/v2/documents/" + id + "/download?inline=true");
            downloadInfo.put("apiVersion", "2.0");
            downloadInfo.put("timestamp", Instant.now());
            
            log.info("API v2: Successfully retrieved download info for document ID: {}", id);
            
            return ResponseEntity.ok(downloadInfo);
            
        } catch (DocumentNotFoundException e) {
            log.warn("API v2: Document not found for download info request, ID: {}", id);
            throw e;
        } catch (UnauthorizedException e) {
            log.warn("API v2: Unauthorized download info request for document ID: {} by user: {}", 
                    id, getCurrentUserId());
            throw e;
        } catch (Exception e) {
            log.error("API v2: Error retrieving download info for document ID: {}", id, e);
            throw new RuntimeException("Failed to retrieve download info: " + e.getMessage(), e);
        }
    }

    /**
     * Bulk download multiple documents as a ZIP file (API v2).
     * 
     * <p>Downloads multiple documents packaged in a ZIP archive.
     * Useful for downloading related documents or document collections.
     * 
     * @param documentIds comma-separated list of document IDs
     * @return ResponseEntity containing ZIP file with requested documents
     */
    @GetMapping("/v2/documents/download/bulk")
    @ApiVersion(since = "2.0", description = "Bulk download multiple documents as ZIP")
    @RateLimit(value = 5, window = 300, type = RateLimit.RateLimitType.DOCUMENT_DOWNLOAD,
               message = "Bulk download rate limit exceeded. Please wait before downloading again.")
    @PreAuthorize("hasAnyRole('USER', 'ADMIN', 'MANAGER')")
    public ResponseEntity<Resource> bulkDownload(@RequestParam String documentIds) {
        log.info("API v2: Bulk download request for document IDs: {}", documentIds);
        
        // TODO: Implement bulk download functionality
        // This would involve:
        // 1. Parse document IDs
        // 2. Validate permissions for each document
        // 3. Create ZIP archive with all documents
        // 4. Return ZIP as response
        
        throw new UnsupportedOperationException("Bulk download functionality not yet implemented");
    }

    // Helper methods

    /**
     * Sanitize filename for safe HTTP header usage.
     * 
     * @param filename the original filename
     * @return sanitized filename safe for HTTP headers
     */
    private String sanitizeFilename(String filename) {
        if (filename == null || filename.trim().isEmpty()) {
            return "document";
        }
        
        try {
            // URL encode the filename to handle special characters
            return URLEncoder.encode(filename, StandardCharsets.UTF_8.toString())
                    .replace("+", "%20"); // Replace + with %20 for spaces
        } catch (UnsupportedEncodingException e) {
            log.warn("Failed to encode filename: {}, using fallback", filename);
            // Fallback: remove problematic characters
            return filename.replaceAll("[^a-zA-Z0-9._-]", "_");
        }
    }

    /**
     * Get current user ID from security context.
     * 
     * @return current user ID or "anonymous" if not authenticated
     */
    private String getCurrentUserId() {
        try {
            Authentication authentication = SecurityContextHolder.getContext().getAuthentication();
            if (authentication != null && authentication.isAuthenticated()) {
                return authentication.getName();
            }
        } catch (Exception e) {
            log.debug("Failed to get current user ID", e);
        }
        return "anonymous";
    }

    // Exception handlers

    /**
     * Handle DocumentNotFoundException.
     */
    @ExceptionHandler(DocumentNotFoundException.class)
    public ResponseEntity<Map<String, Object>> handleDocumentNotFound(DocumentNotFoundException e) {
        log.warn("Document not found: {}", e.getMessage());
        
        Map<String, Object> error = new HashMap<>();
        error.put("error", "DOCUMENT_NOT_FOUND");
        error.put("message", e.getMessage());
        error.put("timestamp", Instant.now());
        error.put("status", HttpStatus.NOT_FOUND.value());
        
        return ResponseEntity.status(HttpStatus.NOT_FOUND).body(error);
    }

    /**
     * Handle UnauthorizedException.
     */
    @ExceptionHandler(UnauthorizedException.class)
    public ResponseEntity<Map<String, Object>> handleUnauthorized(UnauthorizedException e) {
        log.warn("Unauthorized access: {}", e.getMessage());
        
        Map<String, Object> error = new HashMap<>();
        error.put("error", "UNAUTHORIZED");
        error.put("message", e.getMessage());
        error.put("timestamp", Instant.now());
        error.put("status", HttpStatus.FORBIDDEN.value());
        
        return ResponseEntity.status(HttpStatus.FORBIDDEN).body(error);
    }

    /**
     * Handle general exceptions.
     */
    @ExceptionHandler(Exception.class)
    public ResponseEntity<Map<String, Object>> handleGeneralException(Exception e) {
        log.error("Unexpected error in document download", e);
        
        Map<String, Object> error = new HashMap<>();
        error.put("error", "INTERNAL_SERVER_ERROR");
        error.put("message", "An unexpected error occurred during document download");
        error.put("timestamp", Instant.now());
        error.put("status", HttpStatus.INTERNAL_SERVER_ERROR.value());
        
        return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body(error);
    }
}