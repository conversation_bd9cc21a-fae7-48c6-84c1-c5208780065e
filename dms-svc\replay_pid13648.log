version 2
JvmtiExport can_access_local_variables 0
JvmtiExport can_hotswap_or_post_breakpoint 0
JvmtiExport can_post_on_exceptions 0
# 420 ciObject found
instanceKlass org/apache/maven/model/inheritance/DefaultInheritanceAssembler$InheritanceModelMerger
ciInstanceKlass java/lang/Cloneable 1 0 7 100 1 100 1 1 1
# instanceKlass java/lang/invoke/LambdaForm$MH+0x000002da81218000
# instanceKlass java/lang/invoke/LambdaForm$MH+0x000002da81211400
instanceKlass java/util/ComparableTimSort
instanceKlass org/apache/maven/artifact/repository/metadata/io/xpp3/MetadataXpp3Reader$1
instanceKlass org/apache/maven/artifact/repository/metadata/io/xpp3/MetadataXpp3Reader$ContentTransformer
instanceKlass org/apache/maven/artifact/repository/metadata/io/xpp3/MetadataXpp3Reader
instanceKlass  @bci org/apache/maven/ReactorReader findVersions (Lorg/eclipse/aether/artifact/Artifact;)Ljava/util/List; 71 <appendix> argL0 ; # org/apache/maven/ReactorReader$$Lambda+0x000002da812139b0
instanceKlass  @bci org/apache/maven/ReactorReader findVersions (Lorg/eclipse/aether/artifact/Artifact;)Ljava/util/List; 58 <appendix> argL0 ; # org/apache/maven/ReactorReader$$Lambda+0x000002da81213780
instanceKlass  @bci org/apache/maven/ReactorReader findVersions (Lorg/eclipse/aether/artifact/Artifact;)Ljava/util/List; 48 <appendix> member <vmtarget> ; # org/apache/maven/ReactorReader$$Lambda+0x000002da81213538
instanceKlass org/eclipse/aether/resolution/ResolutionErrorPolicyRequest
instanceKlass org/eclipse/aether/repository/AuthenticationDigest
instanceKlass org/eclipse/aether/internal/impl/Utils
instanceKlass org/eclipse/aether/repository/LocalMetadataResult
instanceKlass org/eclipse/aether/repository/LocalMetadataRequest
instanceKlass org/eclipse/aether/resolution/MetadataResult
instanceKlass org/eclipse/aether/resolution/MetadataRequest
instanceKlass org/eclipse/aether/metadata/AbstractMetadata
instanceKlass org/eclipse/aether/util/version/UnionVersionRange
instanceKlass org/eclipse/aether/version/VersionRange$Bound
instanceKlass org/eclipse/aether/util/version/GenericVersionRange
# instanceKlass java/lang/invoke/LambdaForm$MH+0x000002da81211000
# instanceKlass java/lang/invoke/LambdaForm$MH+0x000002da81210c00
instanceKlass java/util/Collections$UnmodifiableList$1
instanceKlass org/apache/maven/model/merge/ModelMerger$1
instanceKlass  @bci org/apache/maven/utils/Os getOsFamily ()Ljava/lang/String; 74 <appendix> argL0 ; # org/apache/maven/utils/Os$$Lambda+0x000002da81216e30
instanceKlass org/apache/maven/utils/Os
instanceKlass  @bci org/apache/maven/model/building/DefaultModelBuilder lambda$getInterpolatedProfiles$2 (Lorg/codehaus/plexus/interpolation/RegexBasedInterpolator;Lorg/apache/maven/model/building/DefaultModelProblemCollector;Lorg/apache/maven/model/ActivationOS;)V 107 <appendix> member <vmtarget> ; # org/apache/maven/model/building/DefaultModelBuilder$$Lambda+0x000002da81216a10
instanceKlass  @bci org/apache/maven/model/building/DefaultModelBuilder lambda$getInterpolatedProfiles$2 (Lorg/codehaus/plexus/interpolation/RegexBasedInterpolator;Lorg/apache/maven/model/building/DefaultModelProblemCollector;Lorg/apache/maven/model/ActivationOS;)V 84 <appendix> member <vmtarget> ; # org/apache/maven/model/building/DefaultModelBuilder$$Lambda+0x000002da812167e8
instanceKlass  @bci org/apache/maven/model/building/DefaultModelBuilder lambda$getInterpolatedProfiles$2 (Lorg/codehaus/plexus/interpolation/RegexBasedInterpolator;Lorg/apache/maven/model/building/DefaultModelProblemCollector;Lorg/apache/maven/model/ActivationOS;)V 61 <appendix> member <vmtarget> ; # org/apache/maven/model/building/DefaultModelBuilder$$Lambda+0x000002da812165c0
instanceKlass  @bci org/apache/maven/model/building/DefaultModelBuilder lambda$getInterpolatedProfiles$2 (Lorg/codehaus/plexus/interpolation/RegexBasedInterpolator;Lorg/apache/maven/model/building/DefaultModelProblemCollector;Lorg/apache/maven/model/ActivationOS;)V 38 <appendix> member <vmtarget> ; # org/apache/maven/model/building/DefaultModelBuilder$$Lambda+0x000002da81216398
instanceKlass  @bci org/apache/maven/model/building/DefaultModelBuilder lambda$getInterpolatedProfiles$2 (Lorg/codehaus/plexus/interpolation/RegexBasedInterpolator;Lorg/apache/maven/model/building/DefaultModelProblemCollector;Lorg/apache/maven/model/ActivationOS;)V 12 <appendix> member <vmtarget> ; # org/apache/maven/model/building/DefaultModelBuilder$$Lambda+0x000002da81216180
# instanceKlass java/lang/invoke/LambdaForm$MH+0x000002da81210800
instanceKlass org/eclipse/aether/util/graph/selector/ExclusionDependencySelector$ExclusionComparator
# instanceKlass java/lang/invoke/LambdaForm$MH+0x000002da81210400
instanceKlass org/apache/maven/artifact/handler/DefaultArtifactHandler$__sisu12$$FastClassByGuice$$205381969
instanceKlass org/eclipse/aether/collection/DependencyManagement
instanceKlass org/eclipse/aether/internal/impl/collect/DataPool$GraphKey
instanceKlass org/eclipse/aether/internal/impl/collect/DefaultDependencyCycle
instanceKlass org/eclipse/aether/internal/impl/collect/DataPool$Descriptor
instanceKlass  @bci org/eclipse/aether/internal/impl/collect/DataPool$HardInternPool intern (Ljava/lang/Object;Ljava/lang/Object;)Ljava/lang/Object; 6 <appendix> member <vmtarget> ; # org/eclipse/aether/internal/impl/collect/DataPool$HardInternPool$$Lambda+0x000002da81215288
instanceKlass org/eclipse/aether/internal/impl/collect/DataPool$DescriptorKey
instanceKlass org/eclipse/aether/internal/impl/collect/DataPool$Constraint$VersionRepo
instanceKlass org/eclipse/aether/internal/impl/collect/DataPool$Constraint
instanceKlass org/eclipse/aether/internal/impl/collect/DataPool$ConstraintKey
instanceKlass org/eclipse/aether/internal/impl/collect/CollectStepDataImpl
instanceKlass org/eclipse/aether/collection/CollectStepData
instanceKlass org/eclipse/aether/graph/Dependency$Exclusions$1
instanceKlass  @bci org/eclipse/aether/util/graph/manager/ClassicDependencyManager deriveChildManager (Lorg/eclipse/aether/collection/DependencyCollectionContext;)Lorg/eclipse/aether/collection/DependencyManager; 420 <appendix> argL0 ; # org/eclipse/aether/util/graph/manager/ClassicDependencyManager$$Lambda+0x000002da81214000
instanceKlass org/eclipse/aether/util/graph/manager/ClassicDependencyManager$Key
instanceKlass org/eclipse/aether/internal/impl/collect/df/NodeStack
instanceKlass org/eclipse/aether/graph/DependencyCycle
instanceKlass org/eclipse/aether/internal/impl/collect/DataPool$HardInternPool
instanceKlass org/eclipse/aether/internal/impl/collect/DataPool$WeakInternPool
instanceKlass org/eclipse/aether/internal/impl/collect/DataPool$InternPool
instanceKlass org/eclipse/aether/internal/impl/collect/CachingArtifactTypeRegistry
instanceKlass org/apache/maven/artifact/handler/DefaultArtifactHandler$__sisu16$$FastClassByGuice$$203742166
instanceKlass  @bci org/apache/maven/RepositoryUtils toDependency (Lorg/apache/maven/model/Dependency;Lorg/eclipse/aether/artifact/ArtifactTypeRegistry;)Lorg/eclipse/aether/graph/Dependency; 106 <appendix> argL0 ; # org/apache/maven/RepositoryUtils$$Lambda+0x000002da8120ef60
instanceKlass  @bci org/apache/maven/RepositoryUtils toDependency (Lorg/apache/maven/artifact/Artifact;Ljava/util/Collection;)Lorg/eclipse/aether/graph/Dependency; 29 <appendix> argL0 ; # org/apache/maven/RepositoryUtils$$Lambda+0x000002da8120ed30
instanceKlass org/eclipse/aether/util/artifact/ArtifactIdUtils
instanceKlass org/apache/maven/project/DefaultDependencyResolutionRequest
instanceKlass org/apache/maven/lifecycle/internal/LifecycleDependencyResolver$ReactorDependencyFilter
instanceKlass org/eclipse/aether/util/filter/AndDependencyFilter
instanceKlass org/eclipse/aether/util/filter/ScopeDependencyFilter
instanceKlass org/apache/maven/project/artifact/DefaultProjectArtifactsCache$CacheKey
instanceKlass  @bci java/util/function/Predicate and (Ljava/util/function/Predicate;)Ljava/util/function/Predicate; 7 <appendix> member <vmtarget> ; # java/util/function/Predicate$$Lambda+0x000002da811ca3e8
instanceKlass  @cpi java/util/function/Predicate 72 form vmentry <vmtarget> ; # java/lang/invoke/LambdaForm$DMH+0x000002da81210000
instanceKlass  @bci org/apache/maven/artifact/resolver/filter/ExclusionArtifactFilter <init> (Ljava/util/List;)V 14 <appendix> argL0 ; # org/apache/maven/artifact/resolver/filter/ExclusionArtifactFilter$$Lambda+0x000002da8120dc50
instanceKlass  @bci org/apache/maven/artifact/resolver/filter/ExclusionArtifactFilter <init> (Ljava/util/List;)V 5 <appendix> argL0 ; # org/apache/maven/artifact/resolver/filter/ExclusionArtifactFilter$$Lambda+0x000002da8120da10
instanceKlass org/apache/maven/artifact/resolver/filter/ExclusionArtifactFilter
instanceKlass org/apache/maven/lifecycle/internal/MojoExecutor$1
instanceKlass org/apache/maven/lifecycle/internal/ExecutionPlanItem
instanceKlass  @bci org/apache/maven/lifecycle/internal/DefaultMojoExecutionConfigurator checkUnknownMojoConfigurationParameters (Lorg/apache/maven/plugin/MojoExecution;)V 200 <appendix> member <vmtarget> ; # org/apache/maven/lifecycle/internal/DefaultMojoExecutionConfigurator$$Lambda+0x000002da8120d190
instanceKlass  @bci org/apache/maven/lifecycle/internal/DefaultMojoExecutionConfigurator checkUnknownMojoConfigurationParameters (Lorg/apache/maven/plugin/MojoExecution;)V 188 <appendix> member <vmtarget> ; # org/apache/maven/lifecycle/internal/DefaultMojoExecutionConfigurator$$Lambda+0x000002da8120cf48
instanceKlass  @bci org/apache/maven/lifecycle/internal/DefaultMojoExecutionConfigurator checkUnknownMojoConfigurationParameters (Lorg/apache/maven/plugin/MojoExecution;)V 147 <appendix> member <vmtarget> ; # org/apache/maven/lifecycle/internal/DefaultMojoExecutionConfigurator$$Lambda+0x000002da8120cd10
instanceKlass  @bci org/apache/maven/lifecycle/internal/DefaultMojoExecutionConfigurator checkUnknownMojoConfigurationParameters (Lorg/apache/maven/plugin/MojoExecution;)V 136 <appendix> argL0 ; # org/apache/maven/lifecycle/internal/DefaultMojoExecutionConfigurator$$Lambda+0x000002da8120cae0
instanceKlass  @bci org/apache/maven/lifecycle/internal/DefaultMojoExecutionConfigurator checkUnknownMojoConfigurationParameters (Lorg/apache/maven/plugin/MojoExecution;)V 126 <appendix> argL0 ; # org/apache/maven/lifecycle/internal/DefaultMojoExecutionConfigurator$$Lambda+0x000002da8120c8a0
instanceKlass  @bci org/apache/maven/lifecycle/internal/DefaultMojoExecutionConfigurator checkUnknownMojoConfigurationParameters (Lorg/apache/maven/plugin/MojoExecution;)V 110 <appendix> argL0 ; # org/apache/maven/lifecycle/internal/DefaultMojoExecutionConfigurator$$Lambda+0x000002da8120c690
instanceKlass  @bci org/apache/maven/lifecycle/internal/DefaultMojoExecutionConfigurator checkUnknownMojoConfigurationParameters (Lorg/apache/maven/plugin/MojoExecution;)V 102 <appendix> argL0 ; # org/apache/maven/lifecycle/internal/DefaultMojoExecutionConfigurator$$Lambda+0x000002da8120c460
instanceKlass org/apache/maven/model/merge/ModelMerger$NotifierKeyComputer
instanceKlass  @bci org/apache/maven/lifecycle/internal/DefaultMojoExecutionConfigurator getUnknownParameters (Lorg/apache/maven/plugin/MojoExecution;Ljava/util/Set;)Ljava/util/Set; 21 <appendix> member <vmtarget> ; # org/apache/maven/lifecycle/internal/DefaultMojoExecutionConfigurator$$Lambda+0x000002da8120bff0
instanceKlass  @bci org/apache/maven/lifecycle/internal/DefaultMojoExecutionConfigurator getUnknownParameters (Lorg/apache/maven/plugin/MojoExecution;Ljava/util/Set;)Ljava/util/Set; 10 <appendix> argL0 ; # org/apache/maven/lifecycle/internal/DefaultMojoExecutionConfigurator$$Lambda+0x000002da8120bdc0
instanceKlass  @bci org/apache/maven/lifecycle/internal/DefaultMojoExecutionConfigurator checkUnknownMojoConfigurationParameters (Lorg/apache/maven/plugin/MojoExecution;)V 54 <appendix> member <vmtarget> ; # org/apache/maven/lifecycle/internal/DefaultMojoExecutionConfigurator$$Lambda+0x000002da8120bb88
instanceKlass  @bci org/apache/maven/lifecycle/internal/DefaultMojoExecutionConfigurator checkUnknownMojoConfigurationParameters (Lorg/apache/maven/plugin/MojoExecution;)V 37 <appendix> argL0 ; # org/apache/maven/lifecycle/internal/DefaultMojoExecutionConfigurator$$Lambda+0x000002da8120b978
instanceKlass org/apache/maven/lifecycle/internal/DefaultMojoExecutionConfigurator$$FastClassByGuice$$203380813
instanceKlass org/apache/maven/execution/ProjectExecutionEvent
instanceKlass org/apache/maven/lifecycle/internal/CompoundProjectExecutionListener
instanceKlass org/apache/maven/lifecycle/internal/DefaultLifecycleMappingDelegate$$FastClassByGuice$$201920062
instanceKlass org/apache/maven/lifecycle/internal/DefaultLifecycleExecutionPlanCalculator$$FastClassByGuice$$201011513
instanceKlass org/apache/maven/project/artifact/DefaultProjectArtifactsCache$$FastClassByGuice$$200037352
instanceKlass org/apache/maven/lifecycle/internal/builder/singlethreaded/SingleThreadedBuilder$$FastClassByGuice$$198798843
instanceKlass org/apache/maven/graph/DefaultProjectDependencyGraph$MavenProjectComparator
instanceKlass org/apache/maven/lifecycle/internal/GoalTask
instanceKlass org/apache/maven/plugin/prefix/internal/DefaultPluginPrefixResult
instanceKlass org/apache/maven/plugin/MavenPluginValidator
instanceKlass java/io/RandomAccessFile$1
instanceKlass org/codehaus/plexus/component/repository/ComponentDependency
instanceKlass org/codehaus/plexus/component/repository/ComponentRequirement
instanceKlass org/codehaus/plexus/configuration/DefaultPlexusConfiguration
instanceKlass  @bci java/util/stream/MatchOps makeRef (Ljava/util/function/Predicate;Ljava/util/stream/MatchOps$MatchKind;)Ljava/util/stream/TerminalOp; 20 <appendix> member <vmtarget> ; # java/util/stream/MatchOps$$Lambda+0x000002da811c9f20
instanceKlass java/util/stream/MatchOps$BooleanTerminalSink
instanceKlass java/util/stream/MatchOps$MatchOp
instanceKlass java/util/stream/MatchOps
instanceKlass  @bci org/apache/maven/plugin/internal/PlexusContainerDefaultDependenciesValidator doValidate (Lorg/eclipse/aether/RepositorySystemSession;Lorg/eclipse/aether/artifact/Artifact;Lorg/eclipse/aether/resolution/ArtifactDescriptorResult;)V 19 <appendix> argL0 ; # org/apache/maven/plugin/internal/PlexusContainerDefaultDependenciesValidator$$Lambda+0x000002da812088f0
instanceKlass  @bci org/apache/maven/plugin/internal/PlexusContainerDefaultDependenciesValidator doValidate (Lorg/eclipse/aether/RepositorySystemSession;Lorg/eclipse/aether/artifact/Artifact;Lorg/eclipse/aether/resolution/ArtifactDescriptorResult;)V 9 <appendix> argL0 ; # org/apache/maven/plugin/internal/PlexusContainerDefaultDependenciesValidator$$Lambda+0x000002da812086b0
instanceKlass  @bci org/apache/maven/plugin/internal/MavenScopeDependenciesValidator doValidate (Lorg/eclipse/aether/RepositorySystemSession;Lorg/eclipse/aether/artifact/Artifact;Lorg/eclipse/aether/resolution/ArtifactDescriptorResult;)V 59 <appendix> argL0 ; # org/apache/maven/plugin/internal/MavenScopeDependenciesValidator$$Lambda+0x000002da81208480
instanceKlass  @bci org/apache/maven/plugin/internal/MavenScopeDependenciesValidator doValidate (Lorg/eclipse/aether/RepositorySystemSession;Lorg/eclipse/aether/artifact/Artifact;Lorg/eclipse/aether/resolution/ArtifactDescriptorResult;)V 49 <appendix> argL0 ; # org/apache/maven/plugin/internal/MavenScopeDependenciesValidator$$Lambda+0x000002da81208240
instanceKlass  @bci org/apache/maven/plugin/internal/MavenScopeDependenciesValidator doValidate (Lorg/eclipse/aether/RepositorySystemSession;Lorg/eclipse/aether/artifact/Artifact;Lorg/eclipse/aether/resolution/ArtifactDescriptorResult;)V 39 <appendix> argL0 ; # org/apache/maven/plugin/internal/MavenScopeDependenciesValidator$$Lambda+0x000002da81208000
instanceKlass  @bci org/apache/maven/plugin/internal/MavenScopeDependenciesValidator doValidate (Lorg/eclipse/aether/RepositorySystemSession;Lorg/eclipse/aether/artifact/Artifact;Lorg/eclipse/aether/resolution/ArtifactDescriptorResult;)V 29 <appendix> argL0 ; # org/apache/maven/plugin/internal/MavenScopeDependenciesValidator$$Lambda+0x000002da81201400
instanceKlass  @bci org/apache/maven/plugin/internal/MavenScopeDependenciesValidator doValidate (Lorg/eclipse/aether/RepositorySystemSession;Lorg/eclipse/aether/artifact/Artifact;Lorg/eclipse/aether/resolution/ArtifactDescriptorResult;)V 19 <appendix> argL0 ; # org/apache/maven/plugin/internal/MavenScopeDependenciesValidator$$Lambda+0x000002da81201c70
instanceKlass  @bci org/apache/maven/plugin/internal/MavenScopeDependenciesValidator doValidate (Lorg/eclipse/aether/RepositorySystemSession;Lorg/eclipse/aether/artifact/Artifact;Lorg/eclipse/aether/resolution/ArtifactDescriptorResult;)V 9 <appendix> argL0 ; # org/apache/maven/plugin/internal/MavenScopeDependenciesValidator$$Lambda+0x000002da81201a30
instanceKlass  @bci org/apache/maven/plugin/internal/MavenMixedDependenciesValidator doValidate (Lorg/eclipse/aether/RepositorySystemSession;Lorg/eclipse/aether/artifact/Artifact;Lorg/eclipse/aether/resolution/ArtifactDescriptorResult;)V 39 <appendix> argL0 ; # org/apache/maven/plugin/internal/MavenMixedDependenciesValidator$$Lambda+0x000002da81201800
instanceKlass  @bci org/apache/maven/plugin/internal/MavenMixedDependenciesValidator doValidate (Lorg/eclipse/aether/RepositorySystemSession;Lorg/eclipse/aether/artifact/Artifact;Lorg/eclipse/aether/resolution/ArtifactDescriptorResult;)V 29 <appendix> argL0 ; # org/apache/maven/plugin/internal/MavenMixedDependenciesValidator$$Lambda+0x000002da81203d08
instanceKlass  @bci org/apache/maven/plugin/internal/MavenMixedDependenciesValidator doValidate (Lorg/eclipse/aether/RepositorySystemSession;Lorg/eclipse/aether/artifact/Artifact;Lorg/eclipse/aether/resolution/ArtifactDescriptorResult;)V 19 <appendix> argL0 ; # org/apache/maven/plugin/internal/MavenMixedDependenciesValidator$$Lambda+0x000002da81203ac8
instanceKlass  @bci org/apache/maven/plugin/internal/MavenMixedDependenciesValidator doValidate (Lorg/eclipse/aether/RepositorySystemSession;Lorg/eclipse/aether/artifact/Artifact;Lorg/eclipse/aether/resolution/ArtifactDescriptorResult;)V 9 <appendix> argL0 ; # org/apache/maven/plugin/internal/MavenMixedDependenciesValidator$$Lambda+0x000002da81203898
instanceKlass  @bci java/util/stream/Collectors toSet ()Ljava/util/stream/Collector; 14 <appendix> argL0 ; # java/util/stream/Collectors$$Lambda+0x800000041
instanceKlass  @bci java/util/stream/Collectors toSet ()Ljava/util/stream/Collector; 9 <appendix> argL0 ; # java/util/stream/Collectors$$Lambda+0x80000003a
instanceKlass  @bci java/util/stream/Collectors toSet ()Ljava/util/stream/Collector; 4 <appendix> argL0 ; # java/util/stream/Collectors$$Lambda+0x800000046
instanceKlass  @bci org/apache/maven/plugin/internal/Maven2DependenciesValidator doValidate (Lorg/eclipse/aether/RepositorySystemSession;Lorg/eclipse/aether/artifact/Artifact;Lorg/eclipse/aether/resolution/ArtifactDescriptorResult;)V 49 <appendix> argL0 ; # org/apache/maven/plugin/internal/Maven2DependenciesValidator$$Lambda+0x000002da81203658
instanceKlass  @bci org/apache/maven/plugin/internal/Maven2DependenciesValidator doValidate (Lorg/eclipse/aether/RepositorySystemSession;Lorg/eclipse/aether/artifact/Artifact;Lorg/eclipse/aether/resolution/ArtifactDescriptorResult;)V 39 <appendix> argL0 ; # org/apache/maven/plugin/internal/Maven2DependenciesValidator$$Lambda+0x000002da81203428
instanceKlass  @bci org/apache/maven/plugin/internal/Maven2DependenciesValidator doValidate (Lorg/eclipse/aether/RepositorySystemSession;Lorg/eclipse/aether/artifact/Artifact;Lorg/eclipse/aether/resolution/ArtifactDescriptorResult;)V 29 <appendix> argL0 ; # org/apache/maven/plugin/internal/Maven2DependenciesValidator$$Lambda+0x000002da812031e8
instanceKlass  @bci org/apache/maven/plugin/internal/Maven2DependenciesValidator doValidate (Lorg/eclipse/aether/RepositorySystemSession;Lorg/eclipse/aether/artifact/Artifact;Lorg/eclipse/aether/resolution/ArtifactDescriptorResult;)V 19 <appendix> argL0 ; # org/apache/maven/plugin/internal/Maven2DependenciesValidator$$Lambda+0x000002da81202fa8
instanceKlass  @bci org/apache/maven/plugin/internal/Maven2DependenciesValidator doValidate (Lorg/eclipse/aether/RepositorySystemSession;Lorg/eclipse/aether/artifact/Artifact;Lorg/eclipse/aether/resolution/ArtifactDescriptorResult;)V 9 <appendix> argL0 ; # org/apache/maven/plugin/internal/Maven2DependenciesValidator$$Lambda+0x000002da81202d78
instanceKlass org/apache/maven/model/Notifier
instanceKlass org/apache/maven/model/io/xpp3/MavenXpp3Reader$1
instanceKlass org/apache/maven/model/io/xpp3/MavenXpp3Reader$ContentTransformer
instanceKlass org/apache/maven/model/io/xpp3/MavenXpp3Reader
instanceKlass org/apache/maven/repository/internal/DefaultModelResolver
instanceKlass  @bci org/apache/maven/plugin/DefaultPluginDescriptorCache get (Lorg/apache/maven/plugin/PluginDescriptorCache$Key;Lorg/apache/maven/plugin/PluginDescriptorCache$PluginDescriptorSupplier;)Lorg/apache/maven/plugin/descriptor/PluginDescriptor; 6 <appendix> member <vmtarget> ; # org/apache/maven/plugin/DefaultPluginDescriptorCache$$Lambda+0x000002da81207ba0
instanceKlass  @bci org/apache/maven/plugin/internal/DefaultMavenPluginManager getPluginDescriptor (Lorg/apache/maven/model/Plugin;Ljava/util/List;Lorg/eclipse/aether/RepositorySystemSession;)Lorg/apache/maven/plugin/descriptor/PluginDescriptor; 24 <appendix> member <vmtarget> ; # org/apache/maven/plugin/internal/DefaultMavenPluginManager$$Lambda+0x000002da81207988
instanceKlass org/apache/maven/plugin/DefaultPluginDescriptorCache$CacheKey
instanceKlass org/apache/maven/plugin/prefix/DefaultPluginPrefixRequest
instanceKlass  @bci org/apache/maven/plugin/internal/DefaultPluginValidationManager validationPluginExcludes (Lorg/eclipse/aether/RepositorySystemSession;)Ljava/util/List; 11 <appendix> member <vmtarget> ; # org/apache/maven/plugin/internal/DefaultPluginValidationManager$$Lambda+0x000002da81207298
instanceKlass  @bci org/apache/maven/plugin/internal/DefaultPluginValidationManager validationReportLevel (Lorg/eclipse/aether/RepositorySystemSession;)Lorg/apache/maven/plugin/internal/DefaultPluginValidationManager$ValidationReportLevel; 10 <appendix> member <vmtarget> ; # org/apache/maven/plugin/internal/DefaultPluginValidationManager$$Lambda+0x000002da81207080
instanceKlass org/apache/maven/internal/aether/MavenChainedWorkspaceReader
instanceKlass  @bci java/util/stream/Collectors lambda$groupingBy$53 (Ljava/util/function/Function;Ljava/util/function/Supplier;Ljava/util/function/BiConsumer;Ljava/util/Map;Ljava/lang/Object;)V 20 <appendix> member <vmtarget> ; # java/util/stream/Collectors$$Lambda+0x000002da811c91f8
instanceKlass  @bci java/util/stream/Collectors groupingBy (Ljava/util/function/Function;Ljava/util/function/Supplier;Ljava/util/stream/Collector;)Ljava/util/stream/Collector; 19 <appendix> member <vmtarget> ; # java/util/stream/Collectors$$Lambda+0x000002da811c8fd0
instanceKlass  @bci java/util/stream/Collectors groupingBy (Ljava/util/function/Function;Ljava/util/stream/Collector;)Ljava/util/stream/Collector; 1 <appendix> argL0 ; # java/util/stream/Collectors$$Lambda+0x000002da811c8dc0
instanceKlass  @bci org/apache/maven/ReactorReader <init> (Lorg/apache/maven/execution/MavenSession;)V 14 <appendix> argL0 ; # org/apache/maven/ReactorReader$$Lambda+0x000002da81206a28
instanceKlass  @bci org/apache/maven/ReactorReader <init> (Lorg/apache/maven/execution/MavenSession;)V 5 <appendix> argL0 ; # org/apache/maven/ReactorReader$$Lambda+0x000002da812067f8
instanceKlass  @bci org/apache/maven/session/scope/internal/SessionScope$ScopeState scope (Lcom/google/inject/Key;Lcom/google/inject/Provider;)Lcom/google/inject/Provider; 6 <appendix> member <vmtarget> ; # org/apache/maven/session/scope/internal/SessionScope$ScopeState$$Lambda+0x000002da812065c0
instanceKlass  @bci org/apache/maven/execution/MavenSession setProjects (Ljava/util/List;)V 40 <appendix> argL0 ; # org/apache/maven/execution/MavenSession$$Lambda+0x000002da81206380
instanceKlass  @bci org/apache/maven/execution/MavenSession setProjects (Ljava/util/List;)V 22 <appendix> member <vmtarget> ; # org/apache/maven/execution/MavenSession$$Lambda+0x000002da81206168
instanceKlass org/codehaus/plexus/util/dag/TopologicalSorter
instanceKlass org/codehaus/plexus/util/dag/Vertex
instanceKlass org/codehaus/plexus/util/dag/DAG
instanceKlass org/apache/maven/project/ProjectSorter
instanceKlass org/apache/maven/graph/DefaultProjectDependencyGraph
instanceKlass org/apache/maven/project/DefaultProjectBuildingResult
instanceKlass org/apache/maven/lifecycle/mapping/DefaultLifecycleMapping$$FastClassByGuice$$198125900
# instanceKlass java/lang/invoke/LambdaForm$MH+0x000002da81201000
# instanceKlass java/lang/invoke/LambdaForm$MH+0x000002da81200c00
instanceKlass  @bci org/apache/maven/model/building/DefaultModelBuilder lambda$getInterpolatedProfiles$1 (Lorg/apache/maven/model/profile/DefaultProfileActivationContext;Lorg/apache/maven/model/building/DefaultModelProblemCollector;Lorg/apache/maven/model/ActivationFile;)V 57 <appendix> member <vmtarget> ; # org/apache/maven/model/building/DefaultModelBuilder$$Lambda+0x000002da81204f70
instanceKlass  @bci org/apache/maven/model/building/DefaultModelBuilder lambda$getInterpolatedProfiles$1 (Lorg/apache/maven/model/profile/DefaultProfileActivationContext;Lorg/apache/maven/model/building/DefaultModelProblemCollector;Lorg/apache/maven/model/ActivationFile;)V 34 <appendix> member <vmtarget> ; # org/apache/maven/model/building/DefaultModelBuilder$$Lambda+0x000002da81204d48
instanceKlass  @bci org/apache/maven/model/building/DefaultModelBuilder lambda$getInterpolatedProfiles$1 (Lorg/apache/maven/model/profile/DefaultProfileActivationContext;Lorg/apache/maven/model/building/DefaultModelProblemCollector;Lorg/apache/maven/model/ActivationFile;)V 8 <appendix> member <vmtarget> ; # org/apache/maven/model/building/DefaultModelBuilder$$Lambda+0x000002da81204b30
instanceKlass org/apache/maven/model/Site
instanceKlass java/util/stream/Streams$RangeIntSpliterator
instanceKlass  @bci org/apache/maven/model/building/DefaultModelBuilder lambda$getInterpolatedProfiles$4 (Lorg/apache/maven/model/Activation;Lorg/codehaus/plexus/interpolation/RegexBasedInterpolator;Lorg/apache/maven/model/building/DefaultModelProblemCollector;Ljava/lang/String;)V 32 <appendix> member <vmtarget> ; # org/apache/maven/model/building/DefaultModelBuilder$$Lambda+0x000002da81204678
instanceKlass  @bci org/apache/maven/model/building/DefaultModelBuilder lambda$getInterpolatedProfiles$4 (Lorg/apache/maven/model/Activation;Lorg/codehaus/plexus/interpolation/RegexBasedInterpolator;Lorg/apache/maven/model/building/DefaultModelProblemCollector;Ljava/lang/String;)V 12 <appendix> member <vmtarget> ; # org/apache/maven/model/building/DefaultModelBuilder$$Lambda+0x000002da81204460
instanceKlass org/apache/maven/lifecycle/Lifecycle$__sisu7$$FastClassByGuice$$196490158
instanceKlass org/apache/maven/lifecycle/Lifecycle$__sisu8$$FastClassByGuice$$195076717
instanceKlass org/apache/maven/lifecycle/mapping/LifecycleMojo
instanceKlass org/apache/maven/lifecycle/mapping/DefaultLifecycleMapping$__sisu1$$FastClassByGuice$$194252951
instanceKlass org/apache/maven/lifecycle/mapping/Lifecycle
instanceKlass org/apache/maven/model/building/DefaultModelBuildingEvent
instanceKlass org/apache/maven/model/building/ModelBuildingEventCatapult$1
instanceKlass org/apache/maven/project/DefaultProjectBuilder$InterimResult
instanceKlass org/apache/maven/artifact/handler/DefaultArtifactHandler$__sisu13$$FastClassByGuice$$193906993
instanceKlass org/apache/maven/artifact/versioning/Restriction
# instanceKlass java/lang/invoke/LambdaForm$MH+0x000002da81200800
# instanceKlass java/lang/invoke/LambdaForm$MH+0x000002da81200400
# instanceKlass java/lang/invoke/LambdaForm$MH+0x000002da81200000
# instanceKlass java/lang/invoke/LambdaForm$BMH+0x000002da811bdc00
# instanceKlass java/lang/invoke/LambdaForm$DMH+0x000002da811bd800
# instanceKlass java/lang/invoke/LambdaForm$DMH+0x000002da811bd400
# instanceKlass java/lang/invoke/LambdaForm$MH+0x000002da811bd000
# instanceKlass java/lang/invoke/LambdaForm$MH+0x000002da811bcc00
# instanceKlass java/lang/invoke/LambdaForm$MH+0x000002da811bc800
# instanceKlass java/lang/invoke/LambdaForm$MH+0x000002da811bc400
instanceKlass org/apache/maven/artifact/handler/DefaultArtifactHandler$__sisu9$$FastClassByGuice$$192547602
instanceKlass org/apache/maven/artifact/versioning/ComparableVersion$StringItem
instanceKlass org/apache/maven/artifact/ArtifactUtils
instanceKlass org/apache/maven/artifact/DefaultArtifact
instanceKlass org/apache/maven/artifact/handler/DefaultArtifactHandler$$FastClassByGuice$$191879944
instanceKlass java/util/StringTokenizer
instanceKlass org/apache/maven/artifact/versioning/ComparableVersion$IntItem
instanceKlass org/apache/maven/artifact/versioning/ComparableVersion$Item
instanceKlass org/apache/maven/artifact/versioning/ComparableVersion
instanceKlass org/apache/maven/artifact/versioning/DefaultArtifactVersion
instanceKlass org/apache/maven/repository/internal/ArtifactDescriptorUtils
instanceKlass  @bci org/apache/maven/model/building/DefaultModelBuilder interpolateModel (Lorg/apache/maven/model/Model;Lorg/apache/maven/model/building/ModelBuildingRequest;Lorg/apache/maven/model/building/ModelProblemCollector;)Lorg/apache/maven/model/Model; 219 <appendix> member <vmtarget> ; # org/apache/maven/model/building/DefaultModelBuilder$$Lambda+0x000002da811b9300
instanceKlass  @cpi org/apache/maven/model/building/DefaultModelBuilder 1548 form vmentry <vmtarget> ; # java/lang/invoke/LambdaForm$DMH+0x000002da811bc000
instanceKlass org/apache/maven/model/Extension
instanceKlass org/codehaus/plexus/interpolation/util/StringUtils
instanceKlass org/apache/maven/model/DistributionManagement
instanceKlass org/apache/maven/model/Prerequisites
instanceKlass org/apache/maven/model/CiManagement
instanceKlass org/apache/maven/model/Organization
instanceKlass org/apache/maven/model/MailingList
instanceKlass org/codehaus/plexus/interpolation/reflection/MethodMap
instanceKlass org/codehaus/plexus/interpolation/reflection/ClassMap$CacheMiss
instanceKlass org/codehaus/plexus/interpolation/reflection/ClassMap
instanceKlass org/codehaus/plexus/interpolation/reflection/ReflectionValueExtractor$Tokenizer
instanceKlass org/codehaus/plexus/interpolation/reflection/ReflectionValueExtractor
instanceKlass org/codehaus/plexus/interpolation/util/ValueSourceUtils
instanceKlass org/apache/maven/model/interpolation/StringVisitorModelInterpolator$ModelVisitor
instanceKlass org/apache/maven/model/interpolation/StringVisitorModelInterpolator$1
instanceKlass org/codehaus/plexus/interpolation/PrefixAwareRecursionInterceptor
instanceKlass org/apache/maven/model/interpolation/UrlNormalizingPostProcessor
instanceKlass org/apache/maven/model/interpolation/PathTranslatingPostProcessor
instanceKlass java/text/DontCareFieldPosition$1
instanceKlass java/text/Format$FieldDelegate
instanceKlass sun/util/resources/Bundles$2
instanceKlass sun/util/resources/LocaleData$LocaleDataResourceBundleProvider
instanceKlass java/util/spi/ResourceBundleProvider
instanceKlass org/apache/maven/model/interpolation/MavenBuildTimestamp
instanceKlass org/apache/maven/model/interpolation/ProblemDetectingValueSource
instanceKlass org/codehaus/plexus/interpolation/PrefixedValueSourceWrapper
instanceKlass org/codehaus/plexus/interpolation/FeedbackEnabledValueSource
instanceKlass org/codehaus/plexus/interpolation/AbstractDelegatingValueSource
instanceKlass org/codehaus/plexus/interpolation/QueryEnabledValueSource
instanceKlass org/apache/maven/model/merge/ModelMerger$ExtensionKeyComputer
instanceKlass org/apache/maven/model/merge/ModelMerger$ResourceKeyComputer
instanceKlass org/apache/maven/model/merge/ModelMerger$SourceDominant
instanceKlass org/apache/maven/model/merge/ModelMerger$DependencyKeyComputer
instanceKlass  @bci org/apache/maven/model/building/DefaultModelBuilder lambda$getInterpolatedProfiles$3 (Lorg/codehaus/plexus/interpolation/RegexBasedInterpolator;Lorg/apache/maven/model/building/DefaultModelProblemCollector;Lorg/apache/maven/model/ActivationProperty;)V 61 <appendix> member <vmtarget> ; # org/apache/maven/model/building/DefaultModelBuilder$$Lambda+0x000002da811b2aa0
instanceKlass  @bci org/apache/maven/model/building/DefaultModelBuilder lambda$getInterpolatedProfiles$3 (Lorg/codehaus/plexus/interpolation/RegexBasedInterpolator;Lorg/apache/maven/model/building/DefaultModelProblemCollector;Lorg/apache/maven/model/ActivationProperty;)V 38 <appendix> member <vmtarget> ; # org/apache/maven/model/building/DefaultModelBuilder$$Lambda+0x000002da811b2878
instanceKlass  @bci org/apache/maven/model/building/DefaultModelBuilder lambda$getInterpolatedProfiles$3 (Lorg/codehaus/plexus/interpolation/RegexBasedInterpolator;Lorg/apache/maven/model/building/DefaultModelProblemCollector;Lorg/apache/maven/model/ActivationProperty;)V 12 <appendix> member <vmtarget> ; # org/apache/maven/model/building/DefaultModelBuilder$$Lambda+0x000002da811b2660
instanceKlass  @cpi org/apache/maven/model/building/DefaultModelBuilder 1552 form vmentry <vmtarget> ; # java/lang/invoke/LambdaForm$DMH+0x000002da811b4800
instanceKlass org/apache/maven/model/building/DefaultModelBuilder$InterpolateString
instanceKlass org/apache/maven/model/building/DefaultModelBuilder$1Interpolation
instanceKlass org/apache/maven/model/DependencyManagement
instanceKlass  @bci org/apache/maven/model/building/DefaultModelBuilder getInterpolatedProfiles (Lorg/apache/maven/model/Model;Lorg/apache/maven/model/profile/DefaultProfileActivationContext;Lorg/apache/maven/model/building/DefaultModelProblemCollector;)Ljava/util/List; 205 <appendix> member <vmtarget> ; # org/apache/maven/model/building/DefaultModelBuilder$$Lambda+0x000002da811b1de0
instanceKlass  @cpi org/apache/maven/model/building/DefaultModelBuilder 1540 form vmentry <vmtarget> ; # java/lang/invoke/LambdaForm$DMH+0x000002da811b4400
instanceKlass  @bci org/apache/maven/model/building/DefaultModelBuilder getInterpolatedProfiles (Lorg/apache/maven/model/Model;Lorg/apache/maven/model/profile/DefaultProfileActivationContext;Lorg/apache/maven/model/building/DefaultModelProblemCollector;)Ljava/util/List; 191 <appendix> argL0 ; # org/apache/maven/model/building/DefaultModelBuilder$$Lambda+0x000002da811b1bb0
instanceKlass  @bci org/apache/maven/model/building/DefaultModelBuilder getInterpolatedProfiles (Lorg/apache/maven/model/Model;Lorg/apache/maven/model/profile/DefaultProfileActivationContext;Lorg/apache/maven/model/building/DefaultModelProblemCollector;)Ljava/util/List; 181 <appendix> member <vmtarget> ; # org/apache/maven/model/building/DefaultModelBuilder$$Lambda+0x000002da811b1988
instanceKlass  @bci org/apache/maven/model/building/DefaultModelBuilder getInterpolatedProfiles (Lorg/apache/maven/model/Model;Lorg/apache/maven/model/profile/DefaultProfileActivationContext;Lorg/apache/maven/model/building/DefaultModelProblemCollector;)Ljava/util/List; 169 <appendix> argL0 ; # org/apache/maven/model/building/DefaultModelBuilder$$Lambda+0x000002da811b1758
instanceKlass  @bci org/apache/maven/model/building/DefaultModelBuilder getInterpolatedProfiles (Lorg/apache/maven/model/Model;Lorg/apache/maven/model/profile/DefaultProfileActivationContext;Lorg/apache/maven/model/building/DefaultModelProblemCollector;)Ljava/util/List; 159 <appendix> member <vmtarget> ; # org/apache/maven/model/building/DefaultModelBuilder$$Lambda+0x000002da811b1530
instanceKlass  @bci org/apache/maven/model/building/DefaultModelBuilder getInterpolatedProfiles (Lorg/apache/maven/model/Model;Lorg/apache/maven/model/profile/DefaultProfileActivationContext;Lorg/apache/maven/model/building/DefaultModelProblemCollector;)Ljava/util/List; 147 <appendix> argL0 ; # org/apache/maven/model/building/DefaultModelBuilder$$Lambda+0x000002da811b1300
instanceKlass  @bci org/apache/maven/model/building/DefaultModelBuilder getInterpolatedProfiles (Lorg/apache/maven/model/Model;Lorg/apache/maven/model/profile/DefaultProfileActivationContext;Lorg/apache/maven/model/building/DefaultModelProblemCollector;)Ljava/util/List; 137 <appendix> member <vmtarget> ; # org/apache/maven/model/building/DefaultModelBuilder$$Lambda+0x000002da811b10d8
instanceKlass  @cpi org/apache/maven/model/building/DefaultModelBuilder 1505 form vmentry <vmtarget> ; # java/lang/invoke/LambdaForm$DMH+0x000002da811b4000
instanceKlass  @bci org/apache/maven/model/building/DefaultModelBuilder getInterpolatedProfiles (Lorg/apache/maven/model/Model;Lorg/apache/maven/model/profile/DefaultProfileActivationContext;Lorg/apache/maven/model/building/DefaultModelProblemCollector;)Ljava/util/List; 126 <appendix> argL0 ; # org/apache/maven/model/building/DefaultModelBuilder$$Lambda+0x000002da811b0ea8
instanceKlass org/apache/maven/model/IssueManagement
instanceKlass org/apache/maven/model/Scm
instanceKlass org/apache/maven/model/License
instanceKlass org/apache/maven/model/building/FilterModelBuildingRequest
instanceKlass  @bci org/eclipse/aether/named/support/NamedLockFactorySupport closeLock (Ljava/lang/String;)V 6 <appendix> member <vmtarget> ; # org/eclipse/aether/named/support/NamedLockFactorySupport$$Lambda+0x000002da811af920
instanceKlass java/util/AbstractMap$2$1
instanceKlass java/nio/channels/spi/AbstractInterruptibleChannel$1
instanceKlass sun/nio/ch/Interruptible
instanceKlass sun/nio/ch/FileKey
instanceKlass sun/nio/ch/FileLockTable
instanceKlass sun/nio/fs/WindowsFileSystemProvider$1
instanceKlass org/eclipse/aether/repository/LocalArtifactRequest
instanceKlass org/apache/maven/repository/internal/DefaultVersionResolver$Key
instanceKlass org/eclipse/aether/internal/impl/DefaultRepositoryEventDispatcher$1
instanceKlass org/eclipse/aether/RepositoryEvent$Builder
instanceKlass org/eclipse/aether/internal/impl/filter/RemoteRepositoryFilterSourceSupport$SimpleResult
instanceKlass  @bci org/eclipse/aether/internal/impl/filter/DefaultRemoteRepositoryFilterManager getRemoteRepositoryFilter (Lorg/eclipse/aether/RepositorySystemSession;)Lorg/eclipse/aether/spi/connector/filter/RemoteRepositoryFilter; 11 <appendix> member <vmtarget> ; # org/eclipse/aether/internal/impl/filter/DefaultRemoteRepositoryFilterManager$$Lambda+0x000002da811ae8c0
instanceKlass  @bci org/apache/maven/utils/Os getOsFamily ()Ljava/lang/String; 74 <bsm> form vmentry <vmtarget> ; # java/lang/invoke/LambdaForm$DMH+0x000002da811a4400
instanceKlass  @bci org/eclipse/aether/named/support/ReadWriteLockNamedLock <init> (Ljava/lang/String;Lorg/eclipse/aether/named/support/NamedLockFactorySupport;Ljava/util/concurrent/locks/ReadWriteLock;)V 7 <appendix> argL0 ; # org/eclipse/aether/named/support/ReadWriteLockNamedLock$$Lambda+0x000002da811ae290
instanceKlass org/eclipse/aether/named/support/Retry$DoNotRetry
instanceKlass  @bci org/eclipse/aether/named/support/NamedLockFactorySupport getLock (Ljava/lang/String;)Lorg/eclipse/aether/named/support/NamedLockSupport; 6 <appendix> member <vmtarget> ; # org/eclipse/aether/named/support/NamedLockFactorySupport$$Lambda+0x000002da811adc28
instanceKlass java/time/temporal/TemporalUnit
instanceKlass java/time/Duration
instanceKlass java/time/temporal/TemporalAmount
instanceKlass org/eclipse/aether/internal/impl/synccontext/named/NamedLockFactoryAdapter$AdaptedLockSyncContext
instanceKlass org/eclipse/aether/internal/impl/synccontext/named/GAVNameMapper
instanceKlass org/eclipse/aether/internal/impl/synccontext/named/NameMappers
instanceKlass org/eclipse/sisu/wire/NamedIterableAdapter$NamedEntry
instanceKlass org/eclipse/sisu/wire/NamedIterableAdapter$NamedIterator
instanceKlass  @bci org/eclipse/aether/DefaultSessionData computeIfAbsent (Ljava/lang/Object;Ljava/util/function/Supplier;)Ljava/lang/Object; 6 <appendix> member <vmtarget> ; # org/eclipse/aether/DefaultSessionData$$Lambda+0x000002da811acef8
instanceKlass  @bci org/eclipse/aether/internal/impl/synccontext/DefaultSyncContextFactory newInstance (Lorg/eclipse/aether/RepositorySystemSession;Z)Lorg/eclipse/aether/SyncContext; 18 <appendix> member <vmtarget> ; # org/eclipse/aether/internal/impl/synccontext/DefaultSyncContextFactory$$Lambda+0x000002da811acce0
instanceKlass org/apache/maven/project/ReactorModelPool$CacheKey
instanceKlass org/eclipse/aether/util/version/GenericVersion$Item
instanceKlass org/eclipse/aether/util/version/GenericVersion$Tokenizer
instanceKlass org/eclipse/aether/util/version/GenericVersion
instanceKlass org/eclipse/aether/util/version/GenericVersionConstraint
instanceKlass org/eclipse/aether/version/VersionConstraint
instanceKlass org/eclipse/aether/version/VersionRange
instanceKlass org/eclipse/aether/util/version/GenericVersionScheme
instanceKlass org/eclipse/aether/artifact/AbstractArtifact
instanceKlass java/util/Formatter$Flags
instanceKlass java/util/Formattable
instanceKlass java/util/Formatter$FormatSpecifier
instanceKlass java/util/Formatter$Conversion
instanceKlass java/util/Formatter$FixedString
instanceKlass java/util/Formatter$FormatString
instanceKlass java/util/Formatter
instanceKlass org/apache/maven/repository/internal/DefaultModelCache$Key
instanceKlass org/apache/maven/model/building/ModelCacheTag$2
instanceKlass org/apache/maven/model/building/ModelCacheTag$1
instanceKlass  @bci org/apache/maven/model/profile/DefaultProfileActivationContext setProjectProperties (Ljava/util/Properties;)Lorg/apache/maven/model/profile/DefaultProfileActivationContext; 27 <appendix> argL0 ; # org/apache/maven/model/profile/DefaultProfileActivationContext$$Lambda+0x000002da811aadf0
instanceKlass  @bci org/apache/maven/model/profile/DefaultProfileActivationContext setProjectProperties (Ljava/util/Properties;)Lorg/apache/maven/model/profile/DefaultProfileActivationContext; 19 <appendix> argL0 ; # org/apache/maven/model/profile/DefaultProfileActivationContext$$Lambda+0x000002da811aabc0
instanceKlass  @bci org/apache/maven/model/profile/DefaultProfileActivationContext setProjectProperties (Ljava/util/Properties;)Lorg/apache/maven/model/profile/DefaultProfileActivationContext; 14 <appendix> argL0 ; # org/apache/maven/model/profile/DefaultProfileActivationContext$$Lambda+0x000002da811aa990
instanceKlass  @bci org/apache/maven/model/building/DefaultModelBuilder getProfileActivationContext (Lorg/apache/maven/model/building/ModelBuildingRequest;Lorg/apache/maven/model/Model;)Lorg/apache/maven/model/profile/DefaultProfileActivationContext; 55 <appendix> member <vmtarget> ; # org/apache/maven/model/building/DefaultModelBuilder$$Lambda+0x000002da811aa758
instanceKlass org/apache/maven/model/building/ModelProblemUtils
instanceKlass org/apache/maven/model/io/xpp3/MavenXpp3ReaderEx$Xpp3DomBuilderInputLocationBuilder
instanceKlass org/apache/maven/model/Exclusion
instanceKlass org/apache/maven/model/Parent
instanceKlass org/apache/maven/model/io/xpp3/MavenXpp3ReaderEx$1
instanceKlass org/codehaus/plexus/util/xml/Xpp3DomBuilder$InputLocationBuilder
instanceKlass org/apache/maven/model/io/xpp3/MavenXpp3ReaderEx$ContentTransformer
instanceKlass org/apache/maven/model/io/xpp3/MavenXpp3ReaderEx
instanceKlass org/apache/maven/model/building/ModelSource2
instanceKlass org/apache/maven/model/building/DefaultModelBuildingResult
instanceKlass org/apache/maven/model/building/AbstractModelBuildingListener
instanceKlass org/apache/maven/project/ProjectModelResolver
instanceKlass org/apache/maven/model/building/DefaultModelBuildingRequest
instanceKlass org/apache/maven/artifact/repository/LegacyLocalRepositoryManager
instanceKlass org/apache/maven/repository/internal/DefaultModelCache
instanceKlass org/apache/maven/project/DefaultProjectBuildingRequest
instanceKlass org/apache/maven/shared/utils/logging/AnsiMessageBuilder
instanceKlass org/apache/maven/lifecycle/internal/DefaultExecutionEventCatapult$1
instanceKlass org/apache/maven/lifecycle/internal/DefaultExecutionEvent
instanceKlass org/apache/maven/AbstractMavenLifecycleParticipant
instanceKlass java/util/concurrent/atomic/AtomicReference
instanceKlass org/apache/maven/session/scope/internal/SessionScope$CachingProvider
instanceKlass  @bci org/apache/maven/session/scope/internal/SessionScope seed (Ljava/lang/Class;Ljava/lang/Object;)V 3 <appendix> member <vmtarget> ; # org/apache/maven/session/scope/internal/SessionScope$$Lambda+0x000002da811a6a70
instanceKlass org/apache/maven/settings/RuntimeInfo
instanceKlass org/eclipse/aether/internal/impl/LocalPathPrefixComposerFactorySupport$LocalPathPrefixComposerSupport
instanceKlass org/eclipse/aether/internal/impl/SimpleLocalRepositoryManager
instanceKlass java/util/ArrayList$SubList$1
instanceKlass org/eclipse/aether/internal/impl/PrioritizedComponent
instanceKlass org/eclipse/sisu/wire/EntrySetAdapter$ValueIterator
instanceKlass org/eclipse/aether/internal/impl/PrioritizedComponents
instanceKlass  @bci org/apache/maven/RepositoryUtils toRepos (Ljava/util/List;)Ljava/util/List; 18 <appendix> argL0 ; # org/apache/maven/RepositoryUtils$$Lambda+0x000002da811a3268
instanceKlass org/eclipse/aether/repository/RemoteRepository$Builder
instanceKlass java/net/spi/URLStreamHandlerProvider
instanceKlass java/net/URL$1
instanceKlass java/net/URL$2
instanceKlass jdk/internal/misc/ThreadTracker
instanceKlass java/net/URL$ThreadTrackHolder
instanceKlass org/eclipse/aether/util/ConfigUtils
instanceKlass org/eclipse/aether/AbstractRepositoryListener
instanceKlass org/eclipse/aether/util/repository/DefaultAuthenticationSelector
instanceKlass org/eclipse/aether/util/repository/DefaultProxySelector
instanceKlass org/eclipse/aether/util/repository/DefaultMirrorSelector$MirrorDef
instanceKlass org/eclipse/aether/util/repository/DefaultMirrorSelector
instanceKlass org/apache/maven/settings/crypto/DefaultSettingsDecryptionResult
instanceKlass org/apache/maven/settings/crypto/DefaultSettingsDecryptionRequest
instanceKlass org/apache/maven/RepositoryUtils$MavenArtifactTypeRegistry
instanceKlass org/apache/maven/RepositoryUtils
instanceKlass org/eclipse/aether/util/repository/SimpleResolutionErrorPolicy
instanceKlass  @bci java/util/stream/Collectors mapMerger (Ljava/util/function/BinaryOperator;)Ljava/util/function/BinaryOperator; 1 <appendix> member <vmtarget> ; # java/util/stream/Collectors$$Lambda+0x000002da811c4830
instanceKlass  @bci java/util/stream/Collectors toMap (Ljava/util/function/Function;Ljava/util/function/Function;Ljava/util/function/BinaryOperator;Ljava/util/function/Supplier;)Ljava/util/stream/Collector; 3 <appendix> member <vmtarget> ; # java/util/stream/Collectors$$Lambda+0x000002da811c4608
instanceKlass  @cpi java/util/stream/Collectors 1299 form vmentry <vmtarget> ; # java/lang/invoke/LambdaForm$DMH+0x000002da811a4000
instanceKlass  @bci java/util/stream/Collectors toMap (Ljava/util/function/Function;Ljava/util/function/Function;Ljava/util/function/BinaryOperator;)Ljava/util/stream/Collector; 3 <appendix> argL0 ; # java/util/stream/Collectors$$Lambda+0x000002da811c43f8
instanceKlass  @bci org/apache/maven/internal/aether/DefaultRepositorySystemSessionFactory getPropertiesFromRequestedProfiles (Lorg/apache/maven/execution/MavenExecutionRequest;)Ljava/util/Map; 59 <appendix> argL0 ; # org/apache/maven/internal/aether/DefaultRepositorySystemSessionFactory$$Lambda+0x000002da811a0f48
instanceKlass  @bci org/apache/maven/internal/aether/DefaultRepositorySystemSessionFactory getPropertiesFromRequestedProfiles (Lorg/apache/maven/execution/MavenExecutionRequest;)Ljava/util/Map; 54 <appendix> argL0 ; # org/apache/maven/internal/aether/DefaultRepositorySystemSessionFactory$$Lambda+0x000002da811a0d18
instanceKlass  @bci org/apache/maven/internal/aether/DefaultRepositorySystemSessionFactory getPropertiesFromRequestedProfiles (Lorg/apache/maven/execution/MavenExecutionRequest;)Ljava/util/Map; 49 <appendix> argL0 ; # org/apache/maven/internal/aether/DefaultRepositorySystemSessionFactory$$Lambda+0x000002da811a0ae8
instanceKlass  @bci org/apache/maven/internal/aether/DefaultRepositorySystemSessionFactory getPropertiesFromRequestedProfiles (Lorg/apache/maven/execution/MavenExecutionRequest;)Ljava/util/Map; 39 <appendix> argL0 ; # org/apache/maven/internal/aether/DefaultRepositorySystemSessionFactory$$Lambda+0x000002da811a08b8
instanceKlass  @bci org/apache/maven/internal/aether/DefaultRepositorySystemSessionFactory getPropertiesFromRequestedProfiles (Lorg/apache/maven/execution/MavenExecutionRequest;)Ljava/util/Map; 29 <appendix> argL0 ; # org/apache/maven/internal/aether/DefaultRepositorySystemSessionFactory$$Lambda+0x000002da811a0688
instanceKlass  @bci org/apache/maven/internal/aether/DefaultRepositorySystemSessionFactory getPropertiesFromRequestedProfiles (Lorg/apache/maven/execution/MavenExecutionRequest;)Ljava/util/Map; 19 <appendix> member <vmtarget> ; # org/apache/maven/internal/aether/DefaultRepositorySystemSessionFactory$$Lambda+0x000002da811a0440
instanceKlass org/eclipse/aether/util/repository/SimpleArtifactDescriptorPolicy
instanceKlass org/eclipse/aether/artifact/DefaultArtifactType
instanceKlass org/eclipse/aether/util/artifact/SimpleArtifactTypeRegistry
instanceKlass org/eclipse/aether/util/graph/transformer/JavaDependencyContextRefiner
instanceKlass org/eclipse/aether/util/graph/transformer/ChainedDependencyGraphTransformer
instanceKlass org/eclipse/aether/util/graph/transformer/ConflictResolver
instanceKlass org/eclipse/aether/graph/Exclusion
instanceKlass org/eclipse/aether/util/graph/selector/ExclusionDependencySelector
instanceKlass org/eclipse/aether/util/graph/selector/OptionalDependencySelector
instanceKlass org/eclipse/aether/util/graph/selector/ScopeDependencySelector
instanceKlass org/eclipse/aether/util/graph/selector/AndDependencySelector
instanceKlass org/eclipse/aether/util/graph/manager/ClassicDependencyManager
instanceKlass org/eclipse/aether/util/graph/traverser/FatArtifactTraverser
instanceKlass org/eclipse/aether/DefaultSessionData
instanceKlass org/eclipse/aether/DefaultRepositorySystemSession$NullFileTransformerManager
instanceKlass org/eclipse/aether/transform/FileTransformerManager
instanceKlass org/eclipse/aether/DefaultRepositorySystemSession$NullArtifactTypeRegistry
instanceKlass org/eclipse/aether/DefaultRepositorySystemSession$NullAuthenticationSelector
instanceKlass org/eclipse/aether/DefaultRepositorySystemSession$NullProxySelector
instanceKlass org/eclipse/aether/DefaultRepositorySystemSession$NullMirrorSelector
instanceKlass org/eclipse/aether/SessionData
instanceKlass org/eclipse/aether/artifact/ArtifactTypeRegistry
instanceKlass org/eclipse/aether/collection/DependencyGraphTransformer
instanceKlass org/eclipse/aether/util/graph/transformer/ConflictResolver$VersionSelector
instanceKlass org/eclipse/aether/util/graph/transformer/ConflictResolver$ScopeSelector
instanceKlass org/eclipse/aether/util/graph/transformer/ConflictResolver$OptionalitySelector
instanceKlass org/eclipse/aether/util/graph/transformer/ConflictResolver$ScopeDeriver
instanceKlass org/apache/maven/repository/internal/MavenRepositorySystemUtils
instanceKlass org/apache/maven/execution/DefaultMavenExecutionResult
instanceKlass org/apache/maven/artifact/repository/MavenArtifactRepository
instanceKlass org/apache/maven/artifact/repository/layout/ArtifactRepositoryLayout2
instanceKlass org/apache/maven/artifact/repository/layout/DefaultRepositoryLayout$$FastClassByGuice$$190153364
instanceKlass org/apache/maven/execution/AbstractExecutionListener
instanceKlass java/util/concurrent/ForkJoinPool$2
instanceKlass jdk/internal/access/JavaUtilConcurrentFJPAccess
instanceKlass java/util/concurrent/ForkJoinPool$DefaultForkJoinWorkerThreadFactory
instanceKlass java/util/concurrent/ForkJoinPool$WorkQueue
instanceKlass java/util/concurrent/ForkJoinPool$ForkJoinWorkerThreadFactory
instanceKlass  @bci org/apache/maven/cli/transfer/SimplexTransferListener <init> (Lorg/eclipse/aether/transfer/TransferListener;IIZ)V 74 <appendix> member <vmtarget> ; # org/apache/maven/cli/transfer/SimplexTransferListener$$Lambda+0x000002da8119a110
instanceKlass org/apache/maven/cli/transfer/SimplexTransferListener$Exchange
instanceKlass org/eclipse/aether/transfer/AbstractTransferListener
instanceKlass org/apache/maven/toolchain/building/DefaultToolchainsBuildingResult
instanceKlass org/apache/maven/toolchain/building/DefaultToolchainsBuilder$1
instanceKlass org/apache/maven/toolchain/model/io/xpp3/MavenToolchainsXpp3Writer
instanceKlass org/apache/maven/toolchain/model/io/xpp3/MavenToolchainsXpp3Reader$1
instanceKlass org/apache/maven/toolchain/model/io/xpp3/MavenToolchainsXpp3Reader$ContentTransformer
instanceKlass org/apache/maven/toolchain/model/io/xpp3/MavenToolchainsXpp3Reader
instanceKlass org/apache/maven/building/DefaultProblemCollector
instanceKlass org/apache/maven/building/ProblemCollectorFactory
instanceKlass org/apache/maven/toolchain/building/DefaultToolchainsBuildingRequest
instanceKlass org/apache/maven/settings/building/DefaultSettingsBuildingResult
instanceKlass org/apache/maven/settings/building/DefaultSettingsBuilder$1
instanceKlass org/codehaus/plexus/interpolation/os/OperatingSystemUtils$DefaultEnvVarSource
instanceKlass org/codehaus/plexus/interpolation/os/OperatingSystemUtils$EnvVarSource
instanceKlass org/codehaus/plexus/interpolation/os/OperatingSystemUtils
instanceKlass org/codehaus/plexus/util/xml/pull/MXSerializer
instanceKlass org/codehaus/plexus/util/xml/pull/XmlSerializer
instanceKlass org/apache/maven/settings/io/xpp3/SettingsXpp3Writer
instanceKlass org/codehaus/plexus/util/xml/pull/EntityReplacementMap
instanceKlass org/apache/maven/settings/io/xpp3/SettingsXpp3Reader$1
instanceKlass org/apache/maven/settings/io/xpp3/SettingsXpp3Reader$ContentTransformer
instanceKlass org/apache/maven/settings/io/xpp3/SettingsXpp3Reader
instanceKlass org/apache/maven/building/FileSource
instanceKlass org/apache/maven/settings/building/DefaultSettingsBuildingRequest
instanceKlass  @bci java/util/regex/CharPredicates ASCII_WORD ()Ljava/util/regex/Pattern$BmpCharPredicate; 0 <appendix> argL0 ; # java/util/regex/CharPredicates$$Lambda+0x000002da811c3638
instanceKlass org/apache/maven/graph/DefaultGraphBuilder$$FastClassByGuice$$188981648
instanceKlass jdk/internal/math/MathUtils
instanceKlass jdk/internal/math/FloatToDecimal
instanceKlass org/apache/maven/plugin/prefix/internal/DefaultPluginPrefixResolver$$FastClassByGuice$$187731897
instanceKlass org/apache/maven/plugin/CompoundMojoExecutionListener
instanceKlass org/apache/maven/plugin/DefaultBuildPluginManager$$FastClassByGuice$$187038583
instanceKlass org/apache/maven/lifecycle/internal/DefaultLifecycleTaskSegmentCalculator$$FastClassByGuice$$185849225
instanceKlass org/apache/maven/lifecycle/internal/DefaultExecutionEventCatapult$$FastClassByGuice$$184691093
instanceKlass org/apache/maven/project/DefaultProjectDependenciesResolver$$FastClassByGuice$$184524890
instanceKlass org/apache/maven/project/RepositorySessionDecorator
instanceKlass org/apache/maven/plugin/DefaultPluginArtifactsCache$$FastClassByGuice$$182768168
instanceKlass com/google/inject/internal/DelegatingInvocationHandler
instanceKlass org/apache/maven/artifact/repository/metadata/io/DefaultMetadataReader$$FastClassByGuice$$182289145
instanceKlass org/apache/maven/plugin/version/internal/DefaultPluginVersionResolver$$FastClassByGuice$$180712997
instanceKlass org/apache/maven/plugin/DefaultExtensionRealmCache$$FastClassByGuice$$179684563
instanceKlass org/apache/maven/rtinfo/internal/DefaultRuntimeInformation$$FastClassByGuice$$178412832
instanceKlass org/apache/maven/plugin/DefaultPluginRealmCache$$FastClassByGuice$$177468393
instanceKlass org/apache/maven/plugin/DefaultPluginDescriptorCache$$FastClassByGuice$$176192906
instanceKlass org/apache/maven/plugin/internal/DefaultMavenPluginManager$$FastClassByGuice$$175324130
instanceKlass  @bci sun/security/provider/AbstractDrbg <clinit> ()V 12 <appendix> argL0 ; # sun/security/provider/AbstractDrbg$$Lambda+0x000002da811c3428
instanceKlass  @cpi sun/security/provider/AbstractDrbg 383 form vmentry <vmtarget> ; # java/lang/invoke/LambdaForm$DMH+0x000002da81194800
# instanceKlass java/lang/invoke/LambdaForm$DMH+0x000002da81194400
instanceKlass sun/security/provider/EntropySource
instanceKlass sun/security/provider/AbstractDrbg
instanceKlass java/security/DrbgParameters$Instantiation
instanceKlass java/security/DrbgParameters
instanceKlass sun/security/provider/MoreDrbgParameters
instanceKlass  @bci sun/security/provider/DRBG <init> (Ljava/security/SecureRandomParameters;)V 26 <appendix> argL0 ; # sun/security/provider/DRBG$$Lambda+0x000002da811c1f68
instanceKlass java/security/SecureRandomSpi
instanceKlass java/security/SecureRandomParameters
instanceKlass jdk/internal/event/Event
instanceKlass sun/security/util/SecurityProviderConstants
instanceKlass java/security/Provider$UString
instanceKlass java/security/Provider$Service
instanceKlass sun/security/provider/NativePRNG$NonBlocking
instanceKlass sun/security/provider/NativePRNG$Blocking
instanceKlass sun/security/provider/NativePRNG
instanceKlass sun/security/provider/SunEntries$1
instanceKlass sun/security/provider/SunEntries
instanceKlass sun/security/util/SecurityConstants
instanceKlass sun/security/jca/ProviderList$2
instanceKlass jdk/internal/math/FloatingDecimal$ASCIIToBinaryBuffer
instanceKlass jdk/internal/math/FloatingDecimal$PreparedASCIIToBinaryBuffer
instanceKlass jdk/internal/math/FloatingDecimal$ASCIIToBinaryConverter
instanceKlass jdk/internal/math/FloatingDecimal$BinaryToASCIIBuffer
instanceKlass jdk/internal/math/FloatingDecimal$ExceptionalBinaryToASCIIBuffer
instanceKlass jdk/internal/math/FloatingDecimal$BinaryToASCIIConverter
instanceKlass jdk/internal/math/FloatingDecimal
instanceKlass java/security/Provider$EngineDescription
instanceKlass java/security/Provider$ServiceKey
instanceKlass sun/security/jca/ProviderConfig
instanceKlass sun/security/jca/ProviderList
instanceKlass sun/security/jca/Providers
instanceKlass java/security/Key
instanceKlass java/security/spec/AlgorithmParameterSpec
instanceKlass org/apache/maven/repository/DefaultMirrorSelector$$FastClassByGuice$$174586291
instanceKlass org/apache/maven/repository/legacy/repository/DefaultArtifactRepositoryFactory$$FastClassByGuice$$173692087
instanceKlass org/eclipse/aether/artifact/ArtifactType
instanceKlass  @bci org/eclipse/aether/internal/impl/synccontext/named/NamedLockFactoryAdapterFactoryImpl <init> (Ljava/util/Map;Ljava/lang/String;Ljava/util/Map;Ljava/lang/String;Lorg/eclipse/aether/impl/RepositorySystemLifecycle;)V 63 <appendix> member <vmtarget> ; # org/eclipse/aether/internal/impl/synccontext/named/NamedLockFactoryAdapterFactoryImpl$$Lambda+0x000002da811918c0
instanceKlass  @cpi org/eclipse/aether/internal/impl/synccontext/named/NamedLockFactoryAdapterFactoryImpl 335 form vmentry <vmtarget> ; # java/lang/invoke/LambdaForm$DMH+0x000002da81194000
instanceKlass org/eclipse/sisu/wire/NamedIterableAdapter
instanceKlass org/eclipse/aether/internal/impl/DefaultUpdateCheckManager$1
instanceKlass org/apache/maven/project/artifact/DefaultMavenMetadataCache$$FastClassByGuice$$172803369
instanceKlass org/apache/maven/repository/legacy/DefaultUpdateCheckManager$$FastClassByGuice$$171468301
instanceKlass org/apache/maven/repository/legacy/DefaultWagonManager$$FastClassByGuice$$170554433
instanceKlass org/apache/maven/artifact/repository/metadata/DefaultRepositoryMetadataManager$$FastClassByGuice$$169502913
instanceKlass org/apache/maven/project/artifact/DefaultMetadataSource$$FastClassByGuice$$168250951
instanceKlass org/apache/maven/artifact/resolver/DefaultResolutionErrorHandler$$FastClassByGuice$$166796673
instanceKlass org/apache/maven/plugin/internal/DefaultLegacySupport$$FastClassByGuice$$165823460
instanceKlass org/apache/maven/repository/legacy/resolver/conflict/NearestConflictResolver$$FastClassByGuice$$165245655
instanceKlass org/apache/maven/artifact/resolver/DefaultArtifactCollector$$FastClassByGuice$$164298338
instanceKlass jdk/internal/vm/ThreadContainers
instanceKlass jdk/internal/vm/StackableScope
instanceKlass org/apache/maven/artifact/resolver/DefaultArtifactResolver$DaemonThreadCreator
instanceKlass java/util/concurrent/ThreadPoolExecutor$AbortPolicy
instanceKlass java/util/concurrent/RejectedExecutionHandler
instanceKlass java/util/concurrent/AbstractExecutorService
instanceKlass java/util/concurrent/ExecutorService
instanceKlass org/apache/maven/artifact/resolver/DefaultArtifactResolver$$FastClassByGuice$$163179838
instanceKlass org/apache/maven/artifact/handler/manager/DefaultArtifactHandlerManager$$FastClassByGuice$$162419777
instanceKlass org/apache/maven/artifact/factory/DefaultArtifactFactory$$FastClassByGuice$$160765827
instanceKlass org/apache/maven/repository/legacy/LegacyRepositorySystem$$FastClassByGuice$$160268688
instanceKlass org/apache/maven/project/DefaultProjectRealmCache$$FastClassByGuice$$158979477
instanceKlass org/codehaus/plexus/classworlds/realm/Entry
instanceKlass java/util/Random
instanceKlass java/util/random/RandomGenerator
instanceKlass org/eclipse/sisu/inject/Guice4$2
instanceKlass org/apache/maven/project/DefaultProjectBuildingHelper$$FastClassByGuice$$157895137
instanceKlass org/apache/maven/lifecycle/internal/DefaultLifecyclePluginAnalyzer$$FastClassByGuice$$156671703
instanceKlass org/apache/maven/model/plugin/DefaultLifecycleBindingsInjector$$FastClassByGuice$$155277563
instanceKlass org/apache/maven/model/Contributor
instanceKlass org/apache/maven/model/PatternSet
instanceKlass org/apache/maven/model/merge/ModelMerger$KeyComputer
instanceKlass org/apache/maven/model/merge/ModelMerger$Remapping
# instanceKlass java/lang/invoke/LambdaForm$MH+0x000002da8118c800
# instanceKlass java/lang/invoke/LambdaForm$MH+0x000002da8118c400
instanceKlass org/apache/maven/project/DefaultProjectBuilder$$FastClassByGuice$$154535915
instanceKlass org/apache/maven/DefaultMaven$$FastClassByGuice$$153981845
instanceKlass org/apache/maven/cli/event/DefaultEventSpyContext
instanceKlass org/eclipse/sisu/wire/EntryListAdapter$ValueIterator
# instanceKlass java/lang/invoke/LambdaForm$MH+0x000002da8118c000
instanceKlass org/apache/maven/cli/logging/Slf4jLogger
instanceKlass org/eclipse/sisu/inject/LazyBeanEntry$JsrNamed
instanceKlass org/eclipse/sisu/inject/LazyBeanEntry
instanceKlass javax/annotation/Priority
instanceKlass org/eclipse/sisu/inject/Implementations
instanceKlass org/eclipse/sisu/plexus/LazyPlexusBean
instanceKlass org/eclipse/sisu/inject/RankedSequence$Itr
instanceKlass org/eclipse/sisu/inject/RankedBindings$Itr
instanceKlass org/eclipse/sisu/inject/LocatedBeans$Itr
instanceKlass org/eclipse/sisu/plexus/RealmFilteredBeans$FilteredItr
instanceKlass org/eclipse/sisu/plexus/DefaultPlexusBeans$Itr
instanceKlass org/eclipse/sisu/plexus/DefaultPlexusBeans
instanceKlass org/eclipse/sisu/plexus/RealmFilteredBeans
instanceKlass org/eclipse/sisu/inject/BeanCache
instanceKlass org/eclipse/sisu/inject/LocatedBeans
instanceKlass org/eclipse/sisu/inject/MildElements$Indexable
instanceKlass com/google/inject/internal/ProviderInternalFactory$1
# instanceKlass java/lang/invoke/LambdaForm$MH+0x000002da81185000
# instanceKlass java/lang/invoke/LambdaForm$MH+0x000002da81184c00
instanceKlass com/google/inject/internal/ConstructorInjector$1
instanceKlass  @bci jdk/internal/reflect/MethodHandleObjectFieldAccessorImpl set (Ljava/lang/Object;Ljava/lang/Object;)V 41 <adapter> ; # java/lang/invoke/LambdaForm$MH+0x000002da81184800
# instanceKlass java/lang/invoke/LambdaForm$MH+0x000002da81184400
# instanceKlass java/lang/invoke/LambdaForm$MH+0x000002da81184000
instanceKlass org/eclipse/sisu/inject/WatchedBeans
instanceKlass org/eclipse/sisu/inject/MildValues$ValueItr
instanceKlass org/eclipse/sisu/inject/InjectorBindings
instanceKlass com/google/inject/spi/ProvisionListener$ProvisionInvocation
instanceKlass com/google/inject/internal/MembersInjectorImpl$1
instanceKlass com/google/inject/internal/InternalContext
instanceKlass com/google/inject/internal/Initializer$1
instanceKlass com/google/common/collect/AbstractMapBasedMultimap$AsMap$AsMapIterator
instanceKlass org/codehaus/plexus/DefaultPlexusContainer$$FastClassByGuice$$152971684
instanceKlass org/eclipse/sisu/wire/TypeConverterCache$$FastClassByGuice$$151320077
instanceKlass com/google/inject/internal/SingleMethodInjector$1
instanceKlass org/eclipse/sisu/inject/DefaultBeanLocator$$FastClassByGuice$$150930935
instanceKlass com/google/inject/internal/InjectorImpl$MethodInvoker
instanceKlass com/google/inject/internal/SingleMethodInjector
instanceKlass org/eclipse/sisu/plexus/PlexusXmlBeanConverter$$FastClassByGuice$$149148210
instanceKlass org/sonatype/plexus/components/sec/dispatcher/DefaultSecDispatcher$$FastClassByGuice$$148509251
instanceKlass org/sonatype/plexus/components/cipher/DefaultPlexusCipher$$FastClassByGuice$$147297218
instanceKlass org/codehaus/plexus/component/configurator/MapOrientedComponentConfigurator$$FastClassByGuice$$146581652
instanceKlass org/codehaus/plexus/component/configurator/BasicComponentConfigurator$$FastClassByGuice$$145607985
instanceKlass org/apache/maven/settings/validation/DefaultSettingsValidator$$FastClassByGuice$$144199521
instanceKlass org/apache/maven/settings/io/DefaultSettingsWriter$$FastClassByGuice$$143553318
instanceKlass org/apache/maven/settings/io/DefaultSettingsReader$$FastClassByGuice$$141588040
instanceKlass org/apache/maven/settings/crypto/DefaultSettingsDecrypter$$FastClassByGuice$$140645492
instanceKlass org/apache/maven/settings/building/DefaultSettingsBuilder$$FastClassByGuice$$139995483
instanceKlass org/eclipse/aether/transport/wagon/WagonTransporterFactory$$FastClassByGuice$$139233891
instanceKlass org/eclipse/aether/internal/transport/wagon/PlexusWagonProvider$$FastClassByGuice$$137716192
instanceKlass org/eclipse/aether/internal/transport/wagon/PlexusWagonConfigurator$$FastClassByGuice$$137215589
instanceKlass org/eclipse/aether/transport/http/XChecksumChecksumExtractor$$FastClassByGuice$$135644029
instanceKlass org/eclipse/aether/transport/http/Nexus2ChecksumExtractor$$FastClassByGuice$$135035302
instanceKlass org/eclipse/aether/transport/http/HttpTransporterFactory$$FastClassByGuice$$133406262
instanceKlass org/eclipse/aether/transport/file/FileTransporterFactory$$FastClassByGuice$$132524603
instanceKlass org/apache/maven/repository/internal/VersionsMetadataGeneratorFactory$$FastClassByGuice$$131817905
instanceKlass org/apache/maven/repository/internal/SnapshotMetadataGeneratorFactory$$FastClassByGuice$$130357859
instanceKlass org/apache/maven/repository/internal/PluginsMetadataGeneratorFactory$$FastClassByGuice$$129682251
instanceKlass org/apache/maven/repository/internal/DefaultVersionResolver$$FastClassByGuice$$127948380
instanceKlass org/apache/maven/repository/internal/DefaultVersionRangeResolver$$FastClassByGuice$$127847874
instanceKlass org/apache/maven/repository/internal/DefaultModelCacheFactory$$FastClassByGuice$$126578537
instanceKlass org/apache/maven/repository/internal/DefaultArtifactDescriptorReader$$FastClassByGuice$$125711913
instanceKlass org/eclipse/aether/named/providers/NoopNamedLockFactory$$FastClassByGuice$$124026809
instanceKlass org/eclipse/aether/named/providers/LocalSemaphoreNamedLockFactory$$FastClassByGuice$$122841597
instanceKlass org/eclipse/aether/named/providers/LocalReadWriteLockNamedLockFactory$$FastClassByGuice$$121881489
instanceKlass org/eclipse/aether/named/providers/FileLockNamedLockFactory$$FastClassByGuice$$121137755
instanceKlass org/eclipse/aether/internal/impl/synccontext/named/providers/StaticNameMapperProvider$$FastClassByGuice$$119666177
instanceKlass org/eclipse/aether/internal/impl/synccontext/named/providers/GAVNameMapperProvider$$FastClassByGuice$$119044652
instanceKlass org/eclipse/aether/internal/impl/synccontext/named/providers/FileStaticNameMapperProvider$$FastClassByGuice$$117671648
instanceKlass org/eclipse/aether/internal/impl/synccontext/named/providers/FileHashingGAVNameMapperProvider$$FastClassByGuice$$116611037
instanceKlass org/eclipse/aether/internal/impl/synccontext/named/providers/FileGAVNameMapperProvider$$FastClassByGuice$$115877666
instanceKlass org/eclipse/aether/internal/impl/synccontext/named/providers/DiscriminatingNameMapperProvider$$FastClassByGuice$$115122071
instanceKlass org/eclipse/aether/internal/impl/synccontext/named/NamedLockFactoryAdapterFactoryImpl$$FastClassByGuice$$113348798
instanceKlass org/eclipse/aether/internal/impl/synccontext/legacy/DefaultSyncContextFactory$$FastClassByGuice$$112414568
instanceKlass org/eclipse/aether/internal/impl/synccontext/DefaultSyncContextFactory$$FastClassByGuice$$112049730
instanceKlass org/eclipse/aether/internal/impl/slf4j/Slf4jLoggerFactory$$FastClassByGuice$$111041047
instanceKlass org/eclipse/aether/internal/impl/resolution/TrustedChecksumsArtifactResolverPostProcessor$$FastClassByGuice$$109587779
instanceKlass org/eclipse/aether/internal/impl/filter/PrefixesRemoteRepositoryFilterSource$$FastClassByGuice$$109028982
instanceKlass org/eclipse/aether/internal/impl/filter/GroupIdRemoteRepositoryFilterSource$$FastClassByGuice$$107422745
instanceKlass org/eclipse/aether/internal/impl/filter/DefaultRemoteRepositoryFilterManager$$FastClassByGuice$$105950413
instanceKlass org/eclipse/aether/internal/impl/collect/df/DfDependencyCollector$$FastClassByGuice$$104971349
instanceKlass org/eclipse/aether/internal/impl/collect/bf/BfDependencyCollector$$FastClassByGuice$$104086179
instanceKlass org/eclipse/aether/internal/impl/collect/DefaultDependencyCollector$$FastClassByGuice$$103315744
instanceKlass org/eclipse/aether/internal/impl/checksum/TrustedToProvidedChecksumsSourceAdapter$$FastClassByGuice$$102752558
instanceKlass org/eclipse/aether/internal/impl/checksum/SummaryFileTrustedChecksumsSource$$FastClassByGuice$$101038247
instanceKlass org/eclipse/aether/internal/impl/checksum/SparseDirectoryTrustedChecksumsSource$$FastClassByGuice$$99801055
instanceKlass org/eclipse/aether/internal/impl/checksum/Sha512ChecksumAlgorithmFactory$$FastClassByGuice$$98976594
instanceKlass org/eclipse/aether/internal/impl/checksum/Sha256ChecksumAlgorithmFactory$$FastClassByGuice$$97519936
instanceKlass org/eclipse/aether/internal/impl/checksum/Sha1ChecksumAlgorithmFactory$$FastClassByGuice$$97454558
instanceKlass  @bci com/google/inject/internal/aop/ImmutableStringTrie buildTrie (Ljava/util/Collection;)Ljava/util/function/ToIntFunction; 38 <appendix> argL0 ; # com/google/inject/internal/aop/ImmutableStringTrie$$Lambda+0x000002da81173fc0
instanceKlass org/eclipse/aether/internal/impl/checksum/Md5ChecksumAlgorithmFactory$$FastClassByGuice$$96152789
instanceKlass org/eclipse/aether/internal/impl/checksum/DefaultChecksumAlgorithmFactorySelector$$FastClassByGuice$$95048687
instanceKlass org/eclipse/aether/internal/impl/SimpleLocalRepositoryManagerFactory$$FastClassByGuice$$94238823
instanceKlass org/eclipse/aether/internal/impl/Maven2RepositoryLayoutFactory$$FastClassByGuice$$92478935
instanceKlass org/eclipse/aether/internal/impl/LoggerFactoryProvider$$FastClassByGuice$$92247349
instanceKlass org/eclipse/aether/internal/impl/EnhancedLocalRepositoryManagerFactory$$FastClassByGuice$$90673606
instanceKlass org/eclipse/aether/internal/impl/DefaultUpdatePolicyAnalyzer$$FastClassByGuice$$89608218
instanceKlass org/eclipse/aether/internal/impl/DefaultUpdateCheckManager$$FastClassByGuice$$88810135
instanceKlass org/eclipse/aether/internal/impl/DefaultTransporterProvider$$FastClassByGuice$$87262957
instanceKlass org/eclipse/aether/internal/impl/DefaultTrackingFileManager$$FastClassByGuice$$86405703
instanceKlass org/eclipse/aether/internal/impl/DefaultRepositorySystemLifecycle$$FastClassByGuice$$85783640
instanceKlass org/eclipse/aether/internal/impl/DefaultRepositorySystem$$FastClassByGuice$$84620735
instanceKlass org/eclipse/aether/internal/impl/DefaultRepositoryLayoutProvider$$FastClassByGuice$$83470486
instanceKlass org/eclipse/aether/internal/impl/DefaultRepositoryEventDispatcher$$FastClassByGuice$$82823028
instanceKlass org/eclipse/aether/internal/impl/DefaultRepositoryConnectorProvider$$FastClassByGuice$$81020375
instanceKlass org/eclipse/aether/internal/impl/DefaultRemoteRepositoryManager$$FastClassByGuice$$80324158
instanceKlass org/eclipse/aether/internal/impl/DefaultOfflineController$$FastClassByGuice$$79690698
instanceKlass org/eclipse/aether/internal/impl/DefaultMetadataResolver$$FastClassByGuice$$78361515
instanceKlass org/eclipse/aether/internal/impl/DefaultLocalRepositoryProvider$$FastClassByGuice$$76968733
instanceKlass org/eclipse/aether/internal/impl/DefaultLocalPathPrefixComposerFactory$$FastClassByGuice$$76255522
instanceKlass org/eclipse/aether/internal/impl/DefaultLocalPathComposer$$FastClassByGuice$$75373406
instanceKlass org/eclipse/aether/internal/impl/DefaultInstaller$$FastClassByGuice$$73589771
instanceKlass org/eclipse/aether/internal/impl/DefaultFileProcessor$$FastClassByGuice$$73158817
instanceKlass org/eclipse/aether/internal/impl/DefaultDeployer$$FastClassByGuice$$72232348
instanceKlass org/eclipse/aether/internal/impl/DefaultChecksumPolicyProvider$$FastClassByGuice$$71066829
instanceKlass org/eclipse/aether/internal/impl/DefaultArtifactResolver$$FastClassByGuice$$69444027
instanceKlass org/eclipse/aether/connector/basic/BasicRepositoryConnectorFactory$$FastClassByGuice$$68367506
instanceKlass org/apache/maven/model/validation/DefaultModelValidator$$FastClassByGuice$$67181407
instanceKlass org/apache/maven/model/superpom/DefaultSuperPomProvider$$FastClassByGuice$$66072666
instanceKlass org/apache/maven/model/profile/activation/PropertyProfileActivator$$FastClassByGuice$$65999298
instanceKlass org/apache/maven/model/profile/activation/OperatingSystemProfileActivator$$FastClassByGuice$$64192833
instanceKlass org/apache/maven/model/profile/activation/JdkVersionProfileActivator$$FastClassByGuice$$63422923
instanceKlass org/apache/maven/model/profile/activation/FileProfileActivator$$FastClassByGuice$$62282980
instanceKlass org/apache/maven/model/profile/DefaultProfileSelector$$FastClassByGuice$$60996665
instanceKlass org/apache/maven/model/profile/DefaultProfileInjector$$FastClassByGuice$$60652016
instanceKlass org/apache/maven/model/plugin/DefaultReportingConverter$$FastClassByGuice$$59276356
instanceKlass org/apache/maven/model/plugin/DefaultReportConfigurationExpander$$FastClassByGuice$$58312567
instanceKlass org/apache/maven/model/plugin/DefaultPluginConfigurationExpander$$FastClassByGuice$$57644209
instanceKlass org/apache/maven/model/path/ProfileActivationFilePathInterpolator$$FastClassByGuice$$56034619
instanceKlass org/apache/maven/model/path/DefaultUrlNormalizer$$FastClassByGuice$$54918550
instanceKlass org/apache/maven/model/path/DefaultPathTranslator$$FastClassByGuice$$54085013
instanceKlass org/apache/maven/model/path/DefaultModelUrlNormalizer$$FastClassByGuice$$53445435
instanceKlass org/apache/maven/model/path/DefaultModelPathTranslator$$FastClassByGuice$$52193988
instanceKlass org/apache/maven/model/normalization/DefaultModelNormalizer$$FastClassByGuice$$50936493
instanceKlass org/apache/maven/model/management/DefaultPluginManagementInjector$$FastClassByGuice$$49710966
instanceKlass org/apache/maven/model/management/DefaultDependencyManagementInjector$$FastClassByGuice$$48526449
instanceKlass org/apache/maven/model/locator/DefaultModelLocator$$FastClassByGuice$$47251440
instanceKlass org/apache/maven/model/io/DefaultModelWriter$$FastClassByGuice$$47037833
instanceKlass org/apache/maven/model/io/DefaultModelReader$$FastClassByGuice$$45252553
instanceKlass org/apache/maven/model/interpolation/StringVisitorModelInterpolator$$FastClassByGuice$$44810584
instanceKlass org/apache/maven/model/interpolation/DefaultModelVersionProcessor$$FastClassByGuice$$43172616
instanceKlass org/apache/maven/model/inheritance/DefaultInheritanceAssembler$$FastClassByGuice$$41970072
instanceKlass org/apache/maven/model/composition/DefaultDependencyManagementImporter$$FastClassByGuice$$41858163
instanceKlass org/apache/maven/model/building/DefaultModelProcessor$$FastClassByGuice$$39915574
instanceKlass org/apache/maven/model/building/DefaultModelBuilder$$FastClassByGuice$$38985117
instanceKlass org/apache/maven/cli/internal/BootstrapCoreExtensionManager$$FastClassByGuice$$38353925
instanceKlass org/apache/maven/cli/configuration/SettingsXmlConfigurationProcessor$$FastClassByGuice$$36876964
instanceKlass org/apache/maven/toolchain/io/DefaultToolchainsWriter$$FastClassByGuice$$36557334
instanceKlass org/apache/maven/toolchain/io/DefaultToolchainsReader$$FastClassByGuice$$34682502
instanceKlass org/apache/maven/toolchain/building/DefaultToolchainsBuilder$$FastClassByGuice$$33786050
instanceKlass org/apache/maven/plugin/internal/ReadOnlyPluginParametersValidator$$FastClassByGuice$$33382430
instanceKlass org/apache/maven/plugin/internal/PlexusContainerDefaultDependenciesValidator$$FastClassByGuice$$31513588
instanceKlass org/apache/maven/plugin/internal/MavenScopeDependenciesValidator$$FastClassByGuice$$30603102
instanceKlass org/apache/maven/plugin/internal/MavenMixedDependenciesValidator$$FastClassByGuice$$29416246
instanceKlass org/apache/maven/plugin/internal/Maven3CompatDependenciesValidator$$FastClassByGuice$$28524989
instanceKlass org/apache/maven/plugin/internal/Maven2DependenciesValidator$$FastClassByGuice$$27791780
instanceKlass org/apache/maven/plugin/internal/DeprecatedPluginValidator$$FastClassByGuice$$26816298
instanceKlass org/apache/maven/plugin/internal/DeprecatedCoreExpressionValidator$$FastClassByGuice$$25661013
instanceKlass org/apache/maven/plugin/internal/DefaultPluginValidationManager$$FastClassByGuice$$24706974
instanceKlass org/apache/maven/plugin/DefaultMojosExecutionStrategy$$FastClassByGuice$$23284592
instanceKlass org/apache/maven/lifecycle/internal/LifecycleDependencyResolver$$FastClassByGuice$$22536196
instanceKlass org/apache/maven/lifecycle/internal/DefaultProjectArtifactFactory$$FastClassByGuice$$21671227
instanceKlass org/apache/maven/internal/aether/ResolverLifecycle$$FastClassByGuice$$20334092
instanceKlass com/google/inject/internal/InjectorImpl$SyntheticProviderBindingImpl$1
instanceKlass com/google/inject/internal/InjectorImpl$1
instanceKlass org/apache/maven/internal/aether/DefaultRepositorySystemSessionFactory$$FastClassByGuice$$19233600
instanceKlass com/google/inject/internal/SingleFieldInjector
instanceKlass org/apache/maven/extension/internal/CoreExportsProvider$$FastClassByGuice$$18414182
instanceKlass org/apache/maven/execution/DefaultMavenExecutionRequestPopulator$$FastClassByGuice$$17723589
instanceKlass org/apache/maven/classrealm/DefaultClassRealmManager$$FastClassByGuice$$16310046
instanceKlass org/apache/maven/ReactorReader$$FastClassByGuice$$15311276
instanceKlass org/apache/maven/DefaultArtifactFilterManager$$FastClassByGuice$$14361382
instanceKlass com/google/inject/internal/SingleParameterInjector
instanceKlass org/apache/maven/lifecycle/internal/builder/BuilderCommon$$FastClassByGuice$$13542371
instanceKlass org/apache/maven/eventspy/internal/EventSpyDispatcher$$FastClassByGuice$$11706131
instanceKlass org/eclipse/sisu/PreDestroy
instanceKlass org/eclipse/sisu/PostConstruct
instanceKlass org/apache/maven/lifecycle/internal/MojoExecutor$$FastClassByGuice$$10581234
instanceKlass org/apache/maven/bridge/MavenRepositorySystem$$FastClassByGuice$$9885711
instanceKlass org/apache/maven/lifecycle/internal/BuildListCalculator$$FastClassByGuice$$8950485
instanceKlass org/apache/maven/lifecycle/DefaultLifecycles$$FastClassByGuice$$7506026
instanceKlass org/apache/maven/lifecycle/internal/LifecyclePluginResolver$$FastClassByGuice$$6900243
instanceKlass org/apache/maven/lifecycle/internal/LifecycleModuleBuilder$$FastClassByGuice$$5621234
instanceKlass org/eclipse/sisu/bean/BeanPropertySetter
instanceKlass org/apache/maven/lifecycle/internal/LifecycleStarter$$FastClassByGuice$$4859617
instanceKlass org/apache/maven/lifecycle/Lifecycle$$FastClassByGuice$$4124120
instanceKlass org/eclipse/sisu/plexus/PlexusConfigurations$ConfigurationProvider
instanceKlass org/apache/maven/lifecycle/internal/MojoDescriptorCreator$$FastClassByGuice$$2604447
instanceKlass org/apache/maven/lifecycle/internal/LifecycleDebugLogger$$FastClassByGuice$$1060380
instanceKlass com/google/inject/internal/ProxyFactory
instanceKlass com/google/common/collect/TransformedIterator
instanceKlass  @bci com/google/inject/internal/ConstructorInjectorStore createConstructor (Lcom/google/inject/spi/InjectionPoint;Lcom/google/inject/internal/Errors;)Lcom/google/inject/internal/ConstructorInjector; 62 <appendix> argL0 ; # com/google/inject/internal/ConstructorInjectorStore$$Lambda+0x000002da81165168
instanceKlass com/google/inject/spi/InterceptorBinding
instanceKlass com/google/inject/internal/MethodAspect
instanceKlass com/google/inject/internal/MembersInjectorImpl
instanceKlass org/eclipse/sisu/bean/BeanInjector
instanceKlass org/eclipse/sisu/plexus/PlexusLifecycleManager$2
instanceKlass org/eclipse/sisu/bean/PropertyBinder$1
instanceKlass org/eclipse/sisu/plexus/ProvidedPropertyBinding
instanceKlass org/eclipse/sisu/plexus/PlexusRequirements$AbstractRequirementProvider
instanceKlass org/eclipse/sisu/bean/BeanPropertyField
instanceKlass org/eclipse/sisu/bean/DeclaredMembers$MemberIterator
instanceKlass org/eclipse/sisu/bean/BeanPropertyIterator
instanceKlass org/eclipse/sisu/bean/DeclaredMembers
instanceKlass org/eclipse/sisu/bean/IgnoreSetters
instanceKlass org/eclipse/sisu/bean/BeanProperties
instanceKlass org/eclipse/sisu/plexus/PlexusRequirements
instanceKlass org/eclipse/sisu/plexus/PlexusConfigurations
instanceKlass org/eclipse/sisu/plexus/PlexusPropertyBinder
instanceKlass org/eclipse/sisu/bean/BeanLifecycle
instanceKlass com/google/inject/internal/EncounterImpl
instanceKlass  @bci com/google/inject/internal/AbstractBindingProcessor$Processor scheduleInitialization (Lcom/google/inject/internal/BindingImpl;)V 9 <appendix> member <vmtarget> ; # com/google/inject/internal/AbstractBindingProcessor$Processor$$Lambda+0x000002da81160b10
instanceKlass  @bci org/apache/maven/session/scope/internal/SessionScope scope (Lcom/google/inject/Key;Lcom/google/inject/Provider;)Lcom/google/inject/Provider; 3 <appendix> member <vmtarget> ; # org/apache/maven/session/scope/internal/SessionScope$$Lambda+0x000002da811608e0
instanceKlass org/apache/maven/execution/scope/internal/MojoExecutionScope$2
instanceKlass com/google/inject/internal/ProviderInternalFactory
instanceKlass com/google/inject/internal/InternalProviderInstanceBindingImpl$Factory
instanceKlass com/google/inject/internal/FactoryProxy
instanceKlass com/google/inject/internal/InternalFactoryToProviderAdapter
instanceKlass com/google/inject/internal/ConstructionContext
instanceKlass com/google/inject/internal/SingletonScope$1
instanceKlass com/google/inject/internal/ProviderToInternalFactoryAdapter
instanceKlass com/google/inject/internal/CycleDetectingLock$CycleDetectingLockFactory$ReentrantCycleDetectingLock
instanceKlass com/google/inject/internal/Initializer$InjectableReference
instanceKlass com/google/inject/internal/ProvisionListenerStackCallback
instanceKlass com/google/common/cache/LocalCache$AbstractReferenceEntry
instanceKlass com/google/inject/internal/ProvisionListenerCallbackStore$KeyBinding
instanceKlass com/google/inject/internal/util/Classes
instanceKlass com/google/inject/spi/ExposedBinding
instanceKlass com/google/inject/internal/CreationListener
instanceKlass com/google/inject/internal/InjectorShell$LoggerFactory
instanceKlass com/google/inject/internal/InjectorShell$InjectorFactory
instanceKlass com/google/inject/internal/Initializables$1
instanceKlass com/google/inject/internal/Initializables
instanceKlass com/google/inject/internal/ConstantFactory
instanceKlass com/google/inject/internal/InjectorShell
instanceKlass com/google/inject/internal/ProvisionListenerCallbackStore
instanceKlass com/google/inject/spi/TypeEncounter
instanceKlass com/google/inject/internal/SingleMemberInjector
instanceKlass com/google/inject/internal/MembersInjectorStore
instanceKlass com/google/inject/internal/TypeConverterBindingProcessor$4
instanceKlass com/google/inject/internal/TypeConverterBindingProcessor$2
instanceKlass com/google/inject/internal/TypeConverterBindingProcessor$1
instanceKlass com/google/inject/internal/TypeConverterBindingProcessor$5
instanceKlass com/google/inject/internal/FailableCache
instanceKlass com/google/inject/internal/ConstructorInjectorStore
instanceKlass com/google/inject/internal/DeferredLookups
instanceKlass com/google/inject/spi/ProviderBinding
instanceKlass com/google/inject/spi/ConvertedConstantBinding
instanceKlass com/google/inject/internal/InjectorImpl
instanceKlass com/google/inject/internal/Lookups
instanceKlass com/google/inject/internal/InjectorImpl$InjectorOptions
instanceKlass  @bci com/google/inject/internal/AbstractProcessor process (Lcom/google/inject/internal/InjectorImpl;Ljava/util/List;)V 13 <appendix> member <vmtarget> ; # com/google/inject/internal/AbstractProcessor$$Lambda+0x000002da811527b8
instanceKlass  @bci com/google/inject/spi/BindingSourceRestriction check (Lcom/google/inject/internal/GuiceInternal;Ljava/util/List;)Lcom/google/common/collect/ImmutableList; 11 <appendix> argL0 ; # com/google/inject/spi/BindingSourceRestriction$$Lambda+0x000002da81151f00
instanceKlass com/google/inject/internal/ProvisionListenerStackCallback$ProvisionCallback
instanceKlass com/google/inject/internal/ConstructorInjector
instanceKlass com/google/inject/internal/DefaultConstructionProxyFactory$FastClassProxy
instanceKlass  @bci com/google/inject/internal/aop/AbstractGlueGenerator lambda$bindSignaturesToInvokers$0 (Ljava/lang/invoke/MethodHandle;Ljava/util/function/ToIntFunction;Ljava/lang/String;)Ljava/util/function/BiFunction; 8 <adapter> ; # java/lang/invoke/LambdaForm$MH+0x000002da81157c00
instanceKlass  @bci com/google/inject/internal/aop/AbstractGlueGenerator bindSignaturesToInvokers (Ljava/util/function/ToIntFunction;Ljava/lang/invoke/MethodHandle;)Ljava/util/function/Function; 13 <appendix> member <vmtarget> ; # com/google/inject/internal/aop/AbstractGlueGenerator$$Lambda+0x000002da81150c58
instanceKlass com/google/inject/internal/aop/ImmutableStringTrie
instanceKlass java/util/function/ToIntFunction
instanceKlass org/apache/maven/plugin/internal/DefaultPluginDependenciesResolver$$FastClassByGuice$$1014050
instanceKlass com/google/inject/internal/aop/GeneratedClassDefiner
# instanceKlass java/lang/ClassLoader$$DefineAccessByGuice$$+0x000002da81157800
instanceKlass jdk/internal/org/objectweb/asm/ClassReader
# instanceKlass java/lang/invoke/LambdaForm$MH+0x000002da81157400
# instanceKlass java/lang/invoke/LambdaForm$MH+0x000002da81157000
# instanceKlass java/lang/invoke/LambdaForm$MH+0x000002da81156c00
# instanceKlass java/lang/invoke/LambdaForm$MH+0x000002da81156800
# instanceKlass java/lang/invoke/LambdaForm$MH+0x000002da81156400
# instanceKlass java/lang/invoke/LambdaForm$DMH+0x000002da81156000
# instanceKlass java/lang/invoke/LambdaForm$DMH+0x000002da81155c00
# instanceKlass java/lang/invoke/LambdaForm$DMH+0x000002da81155800
# instanceKlass java/lang/invoke/LambdaForm$MH+0x000002da81155400
# instanceKlass java/lang/invoke/LambdaForm$MH+0x000002da81155000
# instanceKlass java/lang/invoke/LambdaForm$DMH+0x000002da81154c00
# instanceKlass java/lang/invoke/LambdaForm$MH+0x000002da81154800
# instanceKlass java/lang/invoke/LambdaForm$DMH+0x000002da81154400
instanceKlass java/lang/invoke/ClassSpecializer$Factory$1Var
instanceKlass java/lang/invoke/MethodHandles$1
# instanceKlass java/lang/invoke/LambdaForm$DMH+0x000002da81154000
instanceKlass sun/invoke/util/ValueConversions$1
instanceKlass  @bci com/google/inject/internal/aop/UnsafeClassDefiner$ClassLoaderDefineClassHolder <clinit> ()V 0 <appendix> argL0 ; # com/google/inject/internal/aop/UnsafeClassDefiner$ClassLoaderDefineClassHolder$$Lambda+0x000002da811503f8
instanceKlass com/google/inject/internal/aop/UnsafeClassDefiner$ClassLoaderDefineClassHolder
instanceKlass com/google/inject/internal/asm/$Handler
instanceKlass com/google/inject/internal/asm/$Attribute
instanceKlass com/google/inject/internal/aop/BytecodeTasks
instanceKlass  @bci com/google/inject/internal/aop/AbstractGlueGenerator generateTrampoline (Lcom/google/inject/internal/asm/$ClassWriter;Ljava/util/Collection;)V 30 <appendix> argL0 ; # com/google/inject/internal/aop/AbstractGlueGenerator$$Lambda+0x000002da8114d800
instanceKlass com/google/inject/internal/asm/$Handle
instanceKlass com/google/inject/internal/asm/$Label
instanceKlass com/google/inject/internal/asm/$Frame
instanceKlass com/google/inject/internal/asm/$ByteVector
instanceKlass com/google/inject/internal/asm/$Symbol
instanceKlass com/google/inject/internal/asm/$SymbolTable
instanceKlass com/google/inject/internal/asm/$FieldVisitor
instanceKlass com/google/inject/internal/asm/$MethodVisitor
instanceKlass com/google/inject/internal/asm/$AnnotationVisitor
instanceKlass com/google/inject/internal/asm/$ModuleVisitor
instanceKlass com/google/inject/internal/asm/$RecordComponentVisitor
instanceKlass com/google/inject/internal/asm/$ClassVisitor
instanceKlass com/google/inject/internal/aop/AbstractGlueGenerator
instanceKlass  @bci com/google/inject/internal/aop/ClassBuilding buildFastClass (Ljava/lang/Class;)Ljava/util/function/Function; 20 <appendix> member <vmtarget> ; # com/google/inject/internal/aop/ClassBuilding$$Lambda+0x000002da81149cb8
instanceKlass  @bci com/google/inject/internal/aop/ClassBuilding buildFastClass (Ljava/lang/Class;)Ljava/util/function/Function; 10 <appendix> member <vmtarget> ; # com/google/inject/internal/aop/ClassBuilding$$Lambda+0x000002da81149a90
instanceKlass com/google/inject/internal/asm/$Type
# instanceKlass java/lang/invoke/LambdaForm$MH+0x000002da8114d400
# instanceKlass java/lang/invoke/LambdaForm$MH+0x000002da8114d000
# instanceKlass java/lang/invoke/LambdaForm$MH+0x000002da8114cc00
# instanceKlass java/lang/invoke/LambdaForm$MH+0x000002da8114c800
# instanceKlass java/lang/invoke/LambdaForm$MH+0x000002da8114c400
instanceKlass jdk/internal/vm/annotation/ForceInline
instanceKlass  @bci com/google/inject/internal/aop/UnsafeClassDefiner <clinit> ()V 30 <appendix> argL0 ; # com/google/inject/internal/aop/UnsafeClassDefiner$$Lambda+0x000002da81149680
instanceKlass com/google/inject/internal/aop/HiddenClassDefiner
instanceKlass  @bci java/util/stream/Collectors joining (Ljava/lang/CharSequence;Ljava/lang/CharSequence;Ljava/lang/CharSequence;)Ljava/util/stream/Collector; 22 <appendix> argL0 ; # java/util/stream/Collectors$$Lambda+0x800000044
instanceKlass  @bci java/util/stream/Collectors joining (Ljava/lang/CharSequence;Ljava/lang/CharSequence;Ljava/lang/CharSequence;)Ljava/util/stream/Collector; 17 <appendix> argL0 ; # java/util/stream/Collectors$$Lambda+0x800000042
instanceKlass  @bci java/util/stream/Collectors joining (Ljava/lang/CharSequence;Ljava/lang/CharSequence;Ljava/lang/CharSequence;)Ljava/util/stream/Collector; 12 <appendix> argL0 ; # java/util/stream/Collectors$$Lambda+0x80000003f
instanceKlass  @bci java/util/stream/Collectors joining (Ljava/lang/CharSequence;Ljava/lang/CharSequence;Ljava/lang/CharSequence;)Ljava/util/stream/Collector; 7 <appendix> member <vmtarget> ; # java/util/stream/Collectors$$Lambda+0x800000047
instanceKlass  @bci java/lang/Class methodToString (Ljava/lang/String;[Ljava/lang/Class;)Ljava/lang/String; 42 <appendix> argL0 ; # java/lang/Class$$Lambda+0x000002da81077770
instanceKlass  @bci com/google/inject/internal/aop/UnsafeClassDefiner <clinit> ()V 11 <appendix> argL0 ; # com/google/inject/internal/aop/UnsafeClassDefiner$$Lambda+0x000002da81149260
instanceKlass com/google/inject/internal/aop/AnonymousClassDefiner
instanceKlass com/google/inject/internal/aop/UnsafeClassDefiner
instanceKlass com/google/inject/internal/aop/ClassDefining$ClassDefinerHolder
instanceKlass com/google/inject/internal/aop/ClassDefiner
instanceKlass com/google/inject/internal/aop/ClassDefining
instanceKlass  @bci com/google/inject/internal/aop/ClassBuilding getOverridableObjectMethods ()[Ljava/lang/reflect/Method; 15 <appendix> member <vmtarget> ; # com/google/inject/internal/aop/ClassBuilding$$Lambda+0x000002da81148638
instanceKlass  @cpi com/google/inject/internal/aop/ClassBuilding 332 form vmentry <vmtarget> ; # java/lang/invoke/LambdaForm$DMH+0x000002da8114c000
instanceKlass  @bci com/google/inject/internal/BytecodeGen <clinit> ()V 25 <appendix> argL0 ; # com/google/inject/internal/BytecodeGen$$Lambda+0x000002da811483e8
instanceKlass com/google/inject/internal/BytecodeGen$EnhancerBuilder
instanceKlass com/google/inject/internal/aop/ClassBuilding
instanceKlass com/google/common/collect/MapMakerInternalMap$StrongValueEntry
instanceKlass com/google/common/collect/MapMakerInternalMap$WeakKeyStrongValueEntry$Helper
instanceKlass com/google/common/collect/MapMakerInternalMap$InternalEntry
instanceKlass com/google/common/collect/MapMakerInternalMap$1
instanceKlass com/google/common/collect/MapMakerInternalMap$InternalEntryHelper
instanceKlass com/google/common/collect/MapMakerInternalMap$WeakValueReference
instanceKlass com/google/common/collect/MapMaker
instanceKlass com/google/inject/internal/BytecodeGen
instanceKlass com/google/inject/internal/ConstructionProxy
instanceKlass com/google/inject/internal/DefaultConstructionProxyFactory
instanceKlass com/google/inject/internal/ConstructionProxyFactory
instanceKlass com/google/inject/internal/ConstructorBindingImpl$Factory
instanceKlass org/eclipse/sisu/inject/TypeArguments$Implicit
instanceKlass org/eclipse/sisu/wire/BeanProviders$3
instanceKlass org/sonatype/inject/BeanEntry
instanceKlass org/eclipse/sisu/BeanEntry
instanceKlass org/eclipse/sisu/wire/BeanProviders$4
instanceKlass org/eclipse/sisu/wire/BeanProviders$6
instanceKlass org/eclipse/sisu/wire/PlaceholderBeanProvider
instanceKlass org/eclipse/sisu/wire/BeanProviders$7
instanceKlass org/eclipse/sisu/wire/BeanProviders$1
instanceKlass com/google/inject/spi/ProviderLookup$1
instanceKlass com/google/inject/spi/ProviderWithDependencies
instanceKlass com/google/inject/spi/ProviderLookup
instanceKlass org/eclipse/sisu/wire/BeanProviders
instanceKlass org/eclipse/sisu/inject/HiddenSource
instanceKlass org/eclipse/sisu/wire/LocatorWiring
instanceKlass com/google/inject/ProvidedBy
instanceKlass com/google/inject/ImplementedBy
instanceKlass org/sonatype/plexus/components/cipher/PBECipher
instanceKlass org/codehaus/classworlds/ClassRealm
instanceKlass org/apache/maven/settings/crypto/SettingsDecryptionResult
instanceKlass org/apache/maven/settings/building/DefaultSettingsProblemCollector
instanceKlass org/apache/maven/settings/merge/MavenSettingsMerger
instanceKlass org/apache/maven/settings/building/SettingsBuildingResult
instanceKlass org/apache/maven/settings/building/SettingsProblemCollector
instanceKlass org/eclipse/aether/impl/MetadataGenerator
instanceKlass org/apache/maven/model/Relocation
instanceKlass org/apache/maven/repository/internal/ArtifactDescriptorReaderDelegate
instanceKlass org/eclipse/aether/named/support/AdaptedSemaphoreNamedLock$AdaptedSemaphore
instanceKlass org/eclipse/aether/named/support/NamedLockFactorySupport$NamedLockHolder
instanceKlass org/eclipse/aether/named/support/NamedLockSupport
instanceKlass org/eclipse/aether/named/NamedLock
instanceKlass org/eclipse/aether/internal/impl/synccontext/named/NamedLockFactoryAdapter
instanceKlass org/eclipse/aether/spi/log/Logger
instanceKlass org/eclipse/aether/internal/impl/filter/PrefixesRemoteRepositoryFilterSource$Node
instanceKlass org/eclipse/aether/spi/connector/filter/RemoteRepositoryFilter$Result
instanceKlass org/eclipse/aether/spi/connector/filter/RemoteRepositoryFilter
instanceKlass org/eclipse/aether/collection/DependencyTraverser
instanceKlass org/eclipse/aether/collection/DependencyManager
instanceKlass org/eclipse/aether/internal/impl/collect/df/DfDependencyCollector$Args
instanceKlass org/eclipse/aether/internal/impl/collect/bf/BfDependencyCollector$DescriptorResolutionResult
instanceKlass org/eclipse/aether/internal/impl/collect/bf/BfDependencyCollector$Args
instanceKlass org/eclipse/aether/internal/impl/collect/bf/DependencyProcessingContext
instanceKlass org/eclipse/aether/internal/impl/collect/bf/DependencyResolutionSkipper
instanceKlass org/eclipse/aether/internal/impl/collect/DependencyCollectorDelegate$Results
instanceKlass org/eclipse/aether/internal/impl/collect/DefaultDependencyCollectionContext
instanceKlass org/eclipse/aether/collection/DependencyCollectionContext
instanceKlass org/eclipse/aether/internal/impl/collect/DataPool
instanceKlass org/eclipse/aether/graph/DefaultDependencyNode
instanceKlass org/eclipse/aether/version/Version
instanceKlass org/eclipse/aether/internal/impl/collect/PremanagedDependency
instanceKlass org/eclipse/aether/internal/impl/collect/DefaultVersionFilterContext
instanceKlass org/eclipse/aether/collection/VersionFilter
instanceKlass org/eclipse/aether/collection/DependencyGraphTransformationContext
instanceKlass org/eclipse/aether/collection/VersionFilter$VersionFilterContext
instanceKlass org/eclipse/aether/spi/connector/Transfer
instanceKlass org/eclipse/aether/internal/impl/checksum/SummaryFileTrustedChecksumsSource$SummaryFileWriter
instanceKlass org/eclipse/aether/internal/impl/checksum/SparseDirectoryTrustedChecksumsSource$SparseDirectoryWriter
instanceKlass org/eclipse/aether/spi/checksums/TrustedChecksumsSource$Writer
instanceKlass org/eclipse/aether/spi/connector/checksum/ChecksumAlgorithm
instanceKlass org/eclipse/aether/impl/UpdateCheck
instanceKlass org/eclipse/aether/spi/connector/transport/Transporter
instanceKlass java/nio/channels/FileLock
instanceKlass org/eclipse/aether/resolution/DependencyResult
instanceKlass org/eclipse/aether/resolution/DependencyRequest
instanceKlass org/eclipse/aether/collection/CollectResult
instanceKlass org/eclipse/aether/collection/CollectRequest
instanceKlass org/eclipse/aether/resolution/ArtifactDescriptorRequest
instanceKlass org/eclipse/aether/resolution/VersionRangeResult
instanceKlass org/eclipse/aether/resolution/VersionRangeRequest
instanceKlass org/eclipse/aether/resolution/VersionRequest
instanceKlass java/util/concurrent/atomic/AtomicBoolean
instanceKlass org/eclipse/aether/spi/connector/layout/RepositoryLayout
instanceKlass org/eclipse/aether/RepositoryEvent
instanceKlass org/eclipse/aether/repository/LocalRepository
instanceKlass org/eclipse/aether/internal/impl/LocalPathPrefixComposer
instanceKlass org/eclipse/aether/transform/FileTransformer
instanceKlass org/eclipse/aether/installation/InstallResult
instanceKlass org/eclipse/aether/installation/InstallRequest
instanceKlass org/eclipse/aether/spi/io/FileProcessor$ProgressListener
instanceKlass org/eclipse/aether/repository/RepositoryPolicy
instanceKlass org/eclipse/aether/internal/impl/DefaultDeployer$EventCatapult
instanceKlass org/eclipse/aether/deployment/DeployResult
instanceKlass org/eclipse/aether/deployment/DeployRequest
instanceKlass org/eclipse/aether/transfer/TransferResource
instanceKlass org/eclipse/aether/spi/connector/checksum/ChecksumPolicy
instanceKlass org/eclipse/aether/resolution/ArtifactRequest
instanceKlass org/eclipse/aether/resolution/VersionResult
instanceKlass org/eclipse/aether/repository/LocalArtifactResult
instanceKlass org/eclipse/aether/internal/impl/DefaultArtifactResolver$ResolutionGroup
instanceKlass org/eclipse/aether/SyncContext
instanceKlass org/eclipse/aether/spi/locator/ServiceLocator
instanceKlass org/eclipse/aether/repository/RemoteRepository
instanceKlass org/eclipse/aether/spi/connector/RepositoryConnector
instanceKlass org/apache/maven/model/validation/DefaultModelValidator$1ActivationFrame
instanceKlass org/apache/maven/model/profile/activation/JdkVersionProfileActivator$RangeValue
instanceKlass org/apache/maven/model/InputLocation
instanceKlass org/apache/maven/model/InputSource
instanceKlass org/apache/maven/model/interpolation/StringVisitorModelInterpolator$InnerInterpolator
instanceKlass org/apache/maven/model/ActivationProperty
instanceKlass org/apache/maven/model/ActivationFile
instanceKlass org/apache/maven/model/ActivationOS
instanceKlass org/codehaus/plexus/interpolation/RegexBasedInterpolator
instanceKlass org/apache/maven/model/Activation
instanceKlass org/apache/maven/model/profile/DefaultProfileActivationContext
instanceKlass org/apache/maven/model/building/ModelBuildingEventCatapult
instanceKlass org/apache/maven/model/building/ModelData
instanceKlass org/apache/maven/model/building/DefaultModelProblemCollector
instanceKlass org/apache/maven/model/building/ModelCacheTag
instanceKlass org/apache/maven/model/building/ModelBuildingEvent
instanceKlass org/apache/maven/model/building/ModelProblemCollectorExt
instanceKlass org/apache/maven/model/profile/ProfileActivationContext
instanceKlass org/apache/maven/cli/internal/extension/model/CoreExtension
instanceKlass org/apache/maven/building/ProblemCollector
instanceKlass org/apache/maven/toolchain/merge/MavenToolchainMerger
instanceKlass org/codehaus/plexus/interpolation/InterpolationPostProcessor
instanceKlass org/apache/maven/toolchain/building/ToolchainsBuildingResult
instanceKlass org/eclipse/aether/graph/Dependency
instanceKlass org/eclipse/aether/resolution/ArtifactDescriptorResult
instanceKlass org/apache/maven/plugin/internal/DefaultPluginValidationManager$PluginValidationIssues
instanceKlass com/google/inject/util/Types
instanceKlass org/eclipse/sisu/Nullable
instanceKlass org/eclipse/aether/repository/AuthenticationSelector
instanceKlass org/eclipse/aether/repository/ProxySelector
instanceKlass org/eclipse/aether/repository/MirrorSelector
instanceKlass org/eclipse/aether/resolution/ResolutionErrorPolicy
instanceKlass org/eclipse/aether/repository/LocalRepositoryManager
instanceKlass org/apache/maven/classrealm/ClassRealmManagerDelegate
instanceKlass org/apache/maven/classrealm/ClassRealmConstituent
instanceKlass org/apache/maven/classrealm/ClassRealmRequest
instanceKlass org/eclipse/aether/repository/WorkspaceRepository
instanceKlass org/apache/maven/ArtifactFilterManagerDelegate
instanceKlass org/apache/maven/exception/ExceptionSummary
instanceKlass org/apache/maven/exception/DefaultExceptionHandler
instanceKlass org/eclipse/aether/RepositoryListener
instanceKlass org/apache/maven/repository/legacy/resolver/conflict/NewestConflictResolver
instanceKlass org/apache/maven/repository/metadata/ClasspathContainer
instanceKlass org/apache/maven/repository/metadata/DefaultClasspathTransformation
instanceKlass org/apache/maven/repository/legacy/repository/DefaultArtifactRepositoryFactory
instanceKlass org/apache/maven/artifact/resolver/DefaultArtifactResolver
instanceKlass org/apache/maven/lifecycle/internal/PhaseRecorder
instanceKlass org/apache/maven/lifecycle/internal/DependencyContext
instanceKlass org/apache/maven/lifecycle/internal/ProjectIndex
instanceKlass org/apache/maven/plugin/MojoExecutionRunner
instanceKlass org/apache/maven/plugin/PluginRealmCache$PluginRealmSupplier
instanceKlass org/apache/maven/plugin/PluginRealmCache$Key
instanceKlass org/apache/maven/plugin/DefaultPluginRealmCache
instanceKlass org/apache/maven/plugin/DefaultBuildPluginManager
instanceKlass org/apache/maven/settings/building/SettingsBuildingRequest
instanceKlass org/apache/maven/settings/RepositoryBase
instanceKlass org/apache/maven/settings/RepositoryPolicy
instanceKlass org/apache/maven/lifecycle/DefaultLifecycleExecutor
instanceKlass org/apache/maven/plugin/descriptor/Parameter
instanceKlass org/apache/maven/lifecycle/internal/DefaultMojoExecutionConfigurator
instanceKlass org/apache/maven/artifact/repository/Authentication
instanceKlass org/apache/maven/repository/Proxy
instanceKlass org/apache/maven/model/RepositoryPolicy
instanceKlass org/apache/maven/repository/ArtifactTransferListener
instanceKlass org/apache/maven/repository/legacy/LegacyRepositorySystem
instanceKlass org/apache/maven/lifecycle/internal/builder/singlethreaded/SingleThreadedBuilder
instanceKlass org/apache/maven/profiles/ProfilesRoot
instanceKlass org/apache/maven/plugin/internal/DefaultPluginManager
instanceKlass org/apache/maven/artifact/handler/manager/DefaultArtifactHandlerManager
instanceKlass org/apache/maven/project/path/DefaultPathTranslator
instanceKlass org/apache/maven/repository/metadata/MetadataGraph
instanceKlass org/apache/maven/repository/metadata/MetadataGraphVertex
instanceKlass org/apache/maven/repository/metadata/DefaultGraphConflictResolver
instanceKlass org/apache/maven/plugin/internal/DefaultLegacySupport
instanceKlass org/apache/maven/settings/crypto/SettingsDecryptionRequest
instanceKlass org/eclipse/aether/resolution/ArtifactResult
instanceKlass org/eclipse/aether/collection/DependencySelector
instanceKlass org/eclipse/aether/resolution/ArtifactDescriptorPolicy
instanceKlass org/apache/maven/plugin/internal/DefaultPluginDependenciesResolver
# instanceKlass java/lang/invoke/LambdaForm$MH+0x000002da81120000
instanceKlass org/apache/maven/lifecycle/internal/DefaultLifecycleTaskSegmentCalculator
instanceKlass org/apache/maven/configuration/BeanConfigurationRequest
instanceKlass org/codehaus/plexus/component/configurator/converters/lookup/ConverterLookup
instanceKlass org/apache/maven/configuration/internal/DefaultBeanConfigurator
instanceKlass org/apache/maven/plugin/version/internal/DefaultPluginVersionResolver$Versions
instanceKlass org/apache/maven/plugin/version/internal/DefaultPluginVersionResult
instanceKlass org/apache/maven/plugin/version/internal/DefaultPluginVersionResolver$Key
instanceKlass org/apache/maven/plugin/version/PluginVersionResult
instanceKlass org/apache/maven/plugin/version/internal/DefaultPluginVersionResolver
instanceKlass org/apache/http/config/Registry
instanceKlass org/apache/http/impl/conn/PoolingHttpClientConnectionManager
instanceKlass org/apache/http/pool/ConnPoolControl
instanceKlass org/apache/http/client/methods/CloseableHttpResponse
instanceKlass org/apache/http/HttpResponse
instanceKlass org/apache/maven/wagon/shared/http/BasicAuthScope
instanceKlass org/apache/maven/wagon/shared/http/HttpConfiguration
instanceKlass org/apache/http/impl/client/CloseableHttpClient
instanceKlass org/apache/http/client/HttpClient
instanceKlass org/apache/http/client/HttpRequestRetryHandler
instanceKlass org/apache/http/client/RedirectStrategy
instanceKlass org/apache/http/config/Lookup
instanceKlass org/apache/http/conn/ssl/TrustStrategy
instanceKlass org/apache/http/ssl/TrustStrategy
instanceKlass org/apache/http/auth/Credentials
instanceKlass org/apache/http/client/AuthCache
instanceKlass org/apache/http/client/CredentialsProvider
instanceKlass org/apache/http/Header
instanceKlass org/apache/http/NameValuePair
instanceKlass org/apache/http/client/ServiceUnavailableRetryStrategy
instanceKlass org/apache/http/protocol/HttpContext
instanceKlass org/apache/http/HttpEntity
instanceKlass org/apache/http/client/methods/HttpUriRequest
instanceKlass org/apache/http/HttpRequest
instanceKlass org/apache/http/HttpMessage
instanceKlass org/apache/http/auth/AuthScheme
instanceKlass org/apache/http/conn/HttpClientConnectionManager
instanceKlass org/apache/maven/model/Reporting
instanceKlass org/apache/maven/model/PluginContainer
instanceKlass org/apache/maven/project/inheritance/DefaultModelInheritanceAssembler
instanceKlass org/apache/maven/settings/TrackableBase
instanceKlass org/apache/maven/repository/DefaultMirrorSelector
instanceKlass org/apache/maven/repository/legacy/resolver/conflict/FarthestConflictResolver
instanceKlass org/eclipse/aether/DefaultRepositorySystemSession
instanceKlass org/apache/maven/execution/MavenExecutionResult
instanceKlass org/apache/maven/DefaultMaven
instanceKlass org/apache/maven/lifecycle/internal/DefaultLifecycleExecutionPlanCalculator
instanceKlass org/apache/maven/wagon/observers/ChecksumObserver
instanceKlass org/apache/maven/repository/legacy/DefaultWagonManager
instanceKlass org/apache/maven/toolchain/DefaultToolchainsBuilder
instanceKlass org/apache/maven/repository/legacy/resolver/transform/DefaultArtifactTransformationManager
instanceKlass org/apache/maven/toolchain/model/TrackableBase
instanceKlass org/apache/maven/toolchain/DefaultToolchain
instanceKlass org/apache/maven/toolchain/ToolchainPrivate
instanceKlass org/apache/maven/toolchain/java/JavaToolchain
instanceKlass org/apache/maven/toolchain/java/JavaToolchainFactory
instanceKlass org/apache/maven/profiles/ProfileManager
instanceKlass org/apache/maven/project/ProjectBuilderConfiguration
instanceKlass org/apache/maven/project/DefaultMavenProjectBuilder
instanceKlass org/apache/maven/lifecycle/internal/DefaultLifecycleMappingDelegate
instanceKlass org/apache/maven/project/DefaultDependencyResolutionResult
instanceKlass org/apache/maven/project/DefaultProjectDependenciesResolver
instanceKlass org/apache/maven/artifact/resolver/DefaultResolutionErrorHandler
instanceKlass org/apache/maven/model/building/Result
instanceKlass org/apache/maven/execution/ProjectDependencyGraph
instanceKlass org/apache/maven/graph/DefaultGraphBuilder
instanceKlass org/apache/maven/toolchain/Toolchain
instanceKlass org/apache/maven/toolchain/DefaultToolchainManager
instanceKlass org/apache/maven/model/merge/ModelMerger
instanceKlass org/apache/maven/model/plugin/DefaultLifecycleBindingsInjector
instanceKlass org/apache/maven/execution/ExecutionEvent
instanceKlass org/apache/maven/lifecycle/internal/DefaultExecutionEventCatapult
instanceKlass org/apache/maven/project/ReactorModelPool
instanceKlass org/apache/maven/project/DependencyResolutionResult
instanceKlass org/apache/maven/model/building/ModelBuildingResult
instanceKlass org/apache/maven/project/DefaultProjectBuilder$InternalConfig
instanceKlass org/apache/maven/model/resolution/ModelResolver
instanceKlass org/apache/maven/project/DependencyResolutionRequest
instanceKlass org/apache/maven/model/RepositoryBase
instanceKlass org/apache/maven/project/ProjectBuildingResult
instanceKlass org/apache/maven/model/building/ModelBuildingListener
instanceKlass org/apache/maven/model/building/ModelSource
instanceKlass org/apache/maven/model/building/ModelCache
instanceKlass org/apache/maven/project/DefaultProjectBuilder
instanceKlass org/apache/maven/project/artifact/DefaultMavenMetadataCache$CacheKey
instanceKlass org/apache/maven/project/artifact/DefaultMavenMetadataCache
instanceKlass org/apache/maven/plugin/PluginArtifactsCache$CacheRecord
instanceKlass org/apache/maven/plugin/PluginArtifactsCache$Key
instanceKlass org/apache/maven/plugin/DefaultPluginArtifactsCache
instanceKlass org/apache/maven/repository/legacy/resolver/conflict/NearestConflictResolver
instanceKlass org/apache/maven/artifact/repository/metadata/io/DefaultMetadataReader
instanceKlass org/apache/maven/project/validation/ModelValidationResult
instanceKlass org/apache/maven/model/building/ModelBuildingRequest
instanceKlass org/apache/maven/model/building/ModelProblemCollector
instanceKlass org/apache/maven/project/validation/DefaultModelValidator
instanceKlass org/apache/maven/plugin/PluginRealmCache$CacheRecord
instanceKlass org/eclipse/aether/util/graph/visitor/AbstractDepthFirstNodeListGenerator
instanceKlass org/eclipse/aether/graph/DependencyNode
instanceKlass org/apache/maven/plugin/descriptor/PluginDescriptorBuilder
instanceKlass org/codehaus/plexus/component/configurator/ConfigurationListener
instanceKlass org/eclipse/aether/graph/DependencyVisitor
instanceKlass org/codehaus/plexus/component/configurator/expression/ExpressionEvaluator
instanceKlass org/codehaus/plexus/configuration/PlexusConfiguration
instanceKlass org/apache/maven/plugin/logging/Log
instanceKlass org/apache/maven/plugin/internal/DefaultMavenPluginManager
instanceKlass org/apache/maven/execution/ProjectExecutionListener
instanceKlass org/apache/maven/execution/BuildSummary
instanceKlass org/sonatype/plexus/components/sec/dispatcher/PasswordDecryptor
instanceKlass sun/reflect/generics/tree/MethodTypeSignature
instanceKlass sun/reflect/generics/tree/VoidDescriptor
instanceKlass org/sonatype/plexus/components/sec/dispatcher/model/SettingsSecurity
instanceKlass org/apache/maven/project/DefaultProjectBuildingHelper
instanceKlass org/apache/maven/project/artifact/ProjectArtifactsCache$CacheRecord
instanceKlass org/apache/maven/project/artifact/ProjectArtifactsCache$Key
instanceKlass org/apache/maven/project/artifact/DefaultProjectArtifactsCache
instanceKlass org/eclipse/aether/version/VersionScheme
instanceKlass org/apache/maven/rtinfo/internal/DefaultRuntimeInformation
instanceKlass org/apache/maven/lifecycle/mapping/DefaultLifecycleMapping
instanceKlass org/apache/maven/artifact/repository/layout/DefaultRepositoryLayout
instanceKlass org/codehaus/plexus/component/repository/ComponentSetDescriptor
instanceKlass org/apache/maven/plugin/PluginDescriptorCache$PluginDescriptorSupplier
instanceKlass org/apache/maven/plugin/PluginDescriptorCache$Key
instanceKlass org/apache/maven/plugin/DefaultPluginDescriptorCache
instanceKlass org/apache/maven/lifecycle/mapping/LifecyclePhase
instanceKlass org/apache/maven/lifecycle/internal/DefaultLifecyclePluginAnalyzer$GoalSpec
instanceKlass org/apache/maven/lifecycle/internal/DefaultLifecyclePluginAnalyzer
instanceKlass org/apache/maven/repository/legacy/resolver/conflict/OldestConflictResolver
instanceKlass org/eclipse/aether/artifact/Artifact
instanceKlass org/eclipse/aether/RequestTrace
instanceKlass org/eclipse/aether/metadata/Metadata
instanceKlass org/eclipse/aether/repository/ArtifactRepository
instanceKlass org/apache/maven/plugin/prefix/PluginPrefixResult
instanceKlass org/apache/maven/plugin/prefix/internal/DefaultPluginPrefixResolver
instanceKlass org/apache/maven/artifact/repository/ArtifactRepositoryPolicy
instanceKlass org/apache/maven/artifact/repository/DefaultArtifactRepositoryFactory
instanceKlass org/apache/maven/artifact/factory/DefaultArtifactFactory
instanceKlass org/apache/maven/artifact/repository/layout/FlatRepositoryLayout
instanceKlass org/apache/maven/artifact/repository/metadata/Versioning
instanceKlass org/apache/maven/repository/metadata/MetadataGraphEdge
instanceKlass org/apache/maven/repository/metadata/DefaultGraphConflictResolutionPolicy
instanceKlass org/eclipse/aether/graph/DependencyFilter
instanceKlass org/apache/maven/project/ProjectRealmCache$CacheRecord
instanceKlass org/apache/maven/project/ProjectRealmCache$Key
instanceKlass org/apache/maven/project/DefaultProjectRealmCache
instanceKlass org/codehaus/plexus/logging/AbstractLogEnabled
instanceKlass org/apache/maven/artifact/versioning/ArtifactVersion
instanceKlass org/apache/maven/execution/DefaultRuntimeInformation
instanceKlass org/apache/maven/lifecycle/internal/ProjectSegment
instanceKlass org/apache/maven/lifecycle/internal/builder/multithreaded/ThreadOutputMuxer
instanceKlass org/apache/maven/lifecycle/internal/TaskSegment
instanceKlass org/apache/maven/lifecycle/internal/builder/multithreaded/ConcurrencyDependencyGraph
instanceKlass org/apache/maven/lifecycle/internal/ReactorBuildStatus
instanceKlass org/apache/maven/lifecycle/internal/ReactorContext
instanceKlass java/util/concurrent/CompletionService
instanceKlass org/apache/maven/lifecycle/internal/builder/multithreaded/MultiThreadedBuilder
instanceKlass org/apache/maven/DefaultProjectDependenciesResolver
instanceKlass org/apache/maven/wagon/InputData
instanceKlass org/apache/maven/wagon/OutputData
instanceKlass java/util/EventObject
instanceKlass org/apache/maven/wagon/events/TransferListener
instanceKlass org/apache/maven/wagon/events/SessionListener
instanceKlass org/apache/maven/wagon/resource/Resource
instanceKlass org/apache/maven/wagon/repository/RepositoryPermissions
instanceKlass org/apache/maven/wagon/proxy/ProxyInfo
instanceKlass org/apache/maven/wagon/authentication/AuthenticationInfo
instanceKlass org/apache/maven/wagon/events/TransferEventSupport
instanceKlass org/apache/maven/wagon/events/SessionEventSupport
instanceKlass org/apache/maven/wagon/repository/Repository
instanceKlass org/apache/maven/wagon/proxy/ProxyInfoProvider
instanceKlass org/apache/maven/wagon/AbstractWagon
instanceKlass org/apache/maven/wagon/StreamingWagon
instanceKlass org/apache/maven/artifact/versioning/VersionRange
instanceKlass org/apache/maven/artifact/resolver/ResolutionNode
instanceKlass org/apache/maven/artifact/resolver/ArtifactResolutionResult
instanceKlass org/apache/maven/artifact/resolver/ArtifactResolutionRequest
instanceKlass org/apache/maven/repository/legacy/resolver/DefaultLegacyArtifactCollector
instanceKlass org/apache/maven/project/artifact/MavenMetadataSource$ProjectRelocation
instanceKlass org/apache/maven/model/building/ModelProblem
instanceKlass org/apache/maven/model/ModelBase
instanceKlass org/eclipse/aether/RepositorySystemSession
instanceKlass org/apache/maven/artifact/repository/metadata/Metadata
instanceKlass org/apache/maven/model/Dependency
instanceKlass org/apache/maven/artifact/repository/ArtifactRepository
instanceKlass org/apache/maven/artifact/Artifact
instanceKlass org/apache/maven/artifact/resolver/filter/ArtifactFilter
instanceKlass org/apache/maven/artifact/repository/metadata/RepositoryMetadata
instanceKlass org/apache/maven/artifact/metadata/ArtifactMetadata
instanceKlass org/apache/maven/repository/legacy/metadata/ArtifactMetadata
instanceKlass org/apache/maven/repository/legacy/metadata/MetadataResolutionRequest
instanceKlass org/apache/maven/artifact/repository/RepositoryRequest
instanceKlass org/apache/maven/repository/legacy/metadata/ResolutionGroup
instanceKlass org/apache/maven/project/artifact/MavenMetadataSource
instanceKlass org/apache/maven/model/ConfigurationContainer
instanceKlass org/apache/maven/model/InputLocationTracker
instanceKlass org/apache/maven/plugin/version/PluginVersionRequest
instanceKlass org/apache/maven/plugin/prefix/PluginPrefixRequest
instanceKlass org/apache/maven/plugin/ExtensionRealmCache$CacheRecord
instanceKlass org/apache/maven/plugin/ExtensionRealmCache$Key
instanceKlass org/apache/maven/plugin/DefaultExtensionRealmCache
instanceKlass org/apache/maven/lifecycle/MavenExecutionPlan
instanceKlass org/apache/maven/lifecycle/internal/ProjectBuildList
instanceKlass org/apache/maven/artifact/handler/DefaultArtifactHandler
instanceKlass org/eclipse/sisu/space/asm/Handler
instanceKlass org/eclipse/sisu/space/asm/Frame
instanceKlass org/eclipse/sisu/space/asm/ByteVector
instanceKlass org/eclipse/sisu/space/asm/Symbol
instanceKlass org/eclipse/sisu/space/asm/SymbolTable
instanceKlass org/eclipse/sisu/space/asm/RecordComponentVisitor
instanceKlass org/eclipse/sisu/space/asm/ModuleVisitor
instanceKlass org/eclipse/sisu/space/asm/MethodVisitor
instanceKlass org/eclipse/sisu/space/asm/FieldVisitor
instanceKlass org/apache/maven/repository/legacy/resolver/conflict/DefaultConflictResolverFactory
instanceKlass com/google/inject/spi/ProviderWithExtensionVisitor
instanceKlass com/google/common/collect/Iterables
instanceKlass java/util/stream/ForEachOps$ForEachOp
instanceKlass java/util/stream/ForEachOps
instanceKlass  @bci com/google/inject/spi/InjectionPoint forConstructorOf (Lcom/google/inject/TypeLiteral;Z)Lcom/google/inject/spi/InjectionPoint; 83 <appendix> member <vmtarget> ; # com/google/inject/spi/InjectionPoint$$Lambda+0x000002da810e7998
instanceKlass  @bci com/google/inject/spi/InjectionPoint forConstructorOf (Lcom/google/inject/TypeLiteral;Z)Lcom/google/inject/spi/InjectionPoint; 67 <appendix> argL0 ; # com/google/inject/spi/InjectionPoint$$Lambda+0x000002da810e7758
instanceKlass  @bci com/google/inject/spi/InjectionPoint forConstructorOf (Lcom/google/inject/TypeLiteral;Z)Lcom/google/inject/spi/InjectionPoint; 57 <appendix> argL0 ; # com/google/inject/spi/InjectionPoint$$Lambda+0x000002da810e7518
instanceKlass  @bci com/google/inject/spi/InjectionPoint forConstructorOf (Lcom/google/inject/TypeLiteral;Z)Lcom/google/inject/spi/InjectionPoint; 24 <appendix> argL0 ; # com/google/inject/spi/InjectionPoint$$Lambda+0x000002da810e72d8
instanceKlass org/eclipse/sisu/plexus/PlexusBean
instanceKlass org/codehaus/plexus/component/repository/ComponentDescriptor
instanceKlass com/google/inject/spi/ProvidesMethodBinding
instanceKlass org/eclipse/sisu/inject/Guice4
instanceKlass com/google/inject/internal/GuiceInternal
instanceKlass org/sonatype/inject/Parameters
instanceKlass org/eclipse/sisu/plexus/PlexusXmlBeanConverter
instanceKlass org/eclipse/sisu/plexus/PlexusBeanConverter
instanceKlass com/google/inject/spi/TypeConverterBinding
instanceKlass java/lang/reflect/AnnotatedParameterizedType
instanceKlass sun/reflect/generics/tree/Wildcard
instanceKlass sun/reflect/generics/tree/BottomSignature
instanceKlass org/eclipse/sisu/inject/DefaultRankingFunction
instanceKlass com/google/inject/spi/ProvisionListenerBinding
instanceKlass com/google/inject/spi/TypeListenerBinding
instanceKlass org/eclipse/sisu/bean/BeanListener
instanceKlass com/google/inject/matcher/Matchers
instanceKlass org/eclipse/sisu/bean/PropertyBinder
instanceKlass org/eclipse/sisu/plexus/PlexusBeanBinder
instanceKlass com/google/inject/spi/InjectionListener
instanceKlass org/sonatype/plexus/components/sec/dispatcher/DefaultSecDispatcher
instanceKlass org/sonatype/plexus/components/cipher/DefaultPlexusCipher
instanceKlass org/sonatype/plexus/components/cipher/PlexusCipher
# instanceKlass java/lang/invoke/LambdaForm$MH+0x000002da810e8000
instanceKlass java/lang/invoke/MethodHandle$1
instanceKlass org/codehaus/plexus/component/configurator/AbstractComponentConfigurator
instanceKlass org/codehaus/plexus/component/configurator/ComponentConfigurator
instanceKlass org/apache/maven/settings/validation/DefaultSettingsValidator
instanceKlass org/apache/maven/settings/validation/SettingsValidator
instanceKlass org/apache/maven/settings/io/DefaultSettingsWriter
instanceKlass org/apache/maven/settings/io/SettingsWriter
instanceKlass org/apache/maven/settings/io/DefaultSettingsReader
instanceKlass org/apache/maven/settings/io/SettingsReader
instanceKlass org/apache/maven/settings/crypto/DefaultSettingsDecrypter
instanceKlass org/apache/maven/settings/crypto/SettingsDecrypter
instanceKlass org/apache/maven/settings/building/DefaultSettingsBuilder
instanceKlass org/apache/maven/settings/building/SettingsBuilder
instanceKlass org/eclipse/aether/transport/wagon/WagonTransporterFactory
instanceKlass org/eclipse/aether/internal/transport/wagon/PlexusWagonProvider
instanceKlass org/eclipse/aether/transport/wagon/WagonProvider
instanceKlass org/eclipse/aether/internal/transport/wagon/PlexusWagonConfigurator
instanceKlass org/eclipse/aether/transport/wagon/WagonConfigurator
instanceKlass org/eclipse/aether/transport/http/ChecksumExtractor
instanceKlass org/eclipse/aether/transport/http/HttpTransporterFactory
instanceKlass org/eclipse/aether/transport/file/FileTransporterFactory
instanceKlass org/eclipse/aether/spi/connector/transport/TransporterFactory
instanceKlass org/apache/maven/repository/internal/VersionsMetadataGeneratorFactory
instanceKlass org/apache/maven/repository/internal/SnapshotMetadataGeneratorFactory
instanceKlass org/apache/maven/repository/internal/PluginsMetadataGeneratorFactory
instanceKlass org/eclipse/aether/impl/MetadataGeneratorFactory
instanceKlass org/apache/maven/repository/internal/DefaultVersionResolver
instanceKlass org/eclipse/aether/impl/VersionResolver
instanceKlass org/apache/maven/repository/internal/DefaultVersionRangeResolver
instanceKlass org/eclipse/aether/impl/VersionRangeResolver
instanceKlass org/apache/maven/repository/internal/DefaultModelCacheFactory
instanceKlass org/apache/maven/repository/internal/ModelCacheFactory
instanceKlass org/apache/maven/repository/internal/DefaultArtifactDescriptorReader
instanceKlass org/eclipse/aether/impl/ArtifactDescriptorReader
instanceKlass org/eclipse/aether/named/support/NamedLockFactorySupport
instanceKlass org/eclipse/aether/named/NamedLockFactory
instanceKlass org/eclipse/aether/internal/impl/synccontext/named/providers/StaticNameMapperProvider
instanceKlass org/eclipse/aether/internal/impl/synccontext/named/providers/GAVNameMapperProvider
instanceKlass org/eclipse/aether/internal/impl/synccontext/named/providers/FileStaticNameMapperProvider
instanceKlass org/eclipse/aether/internal/impl/synccontext/named/providers/FileHashingGAVNameMapperProvider
instanceKlass org/eclipse/aether/internal/impl/synccontext/named/providers/FileGAVNameMapperProvider
instanceKlass org/eclipse/aether/internal/impl/synccontext/named/NameMapper
instanceKlass org/eclipse/aether/internal/impl/synccontext/named/providers/DiscriminatingNameMapperProvider
instanceKlass org/eclipse/aether/internal/impl/synccontext/named/NamedLockFactoryAdapterFactoryImpl
instanceKlass org/eclipse/aether/internal/impl/synccontext/named/NamedLockFactoryAdapterFactory
instanceKlass org/eclipse/aether/internal/impl/synccontext/legacy/DefaultSyncContextFactory
instanceKlass org/eclipse/aether/impl/SyncContextFactory
instanceKlass org/eclipse/aether/internal/impl/synccontext/DefaultSyncContextFactory
instanceKlass org/eclipse/aether/spi/synccontext/SyncContextFactory
instanceKlass java/lang/Deprecated
instanceKlass org/eclipse/aether/internal/impl/slf4j/Slf4jLoggerFactory
instanceKlass org/eclipse/aether/internal/impl/resolution/ArtifactResolverPostProcessorSupport
instanceKlass org/eclipse/aether/internal/impl/filter/RemoteRepositoryFilterSourceSupport
instanceKlass org/eclipse/aether/spi/connector/filter/RemoteRepositoryFilterSource
instanceKlass org/eclipse/aether/spi/resolution/ArtifactResolverPostProcessor
instanceKlass org/eclipse/aether/internal/impl/filter/DefaultRemoteRepositoryFilterManager
instanceKlass org/eclipse/aether/impl/RemoteRepositoryFilterManager
instanceKlass org/eclipse/aether/internal/impl/collect/DependencyCollectorDelegate
instanceKlass org/eclipse/aether/internal/impl/collect/DefaultDependencyCollector
instanceKlass org/eclipse/aether/impl/DependencyCollector
instanceKlass org/eclipse/aether/internal/impl/checksum/TrustedToProvidedChecksumsSourceAdapter
instanceKlass org/eclipse/aether/spi/checksums/ProvidedChecksumsSource
instanceKlass org/eclipse/aether/internal/impl/checksum/FileTrustedChecksumsSourceSupport
instanceKlass org/eclipse/aether/spi/checksums/TrustedChecksumsSource
instanceKlass org/eclipse/aether/spi/connector/checksum/ChecksumAlgorithmFactorySupport
instanceKlass org/eclipse/aether/spi/connector/checksum/ChecksumAlgorithmFactory
instanceKlass org/eclipse/aether/internal/impl/checksum/DefaultChecksumAlgorithmFactorySelector
instanceKlass org/eclipse/aether/spi/connector/checksum/ChecksumAlgorithmFactorySelector
instanceKlass org/eclipse/aether/internal/impl/SimpleLocalRepositoryManagerFactory
instanceKlass org/eclipse/aether/internal/impl/Maven2RepositoryLayoutFactory
instanceKlass org/eclipse/aether/spi/connector/layout/RepositoryLayoutFactory
instanceKlass org/eclipse/aether/spi/log/LoggerFactory
instanceKlass org/eclipse/aether/internal/impl/LoggerFactoryProvider
instanceKlass org/eclipse/aether/internal/impl/EnhancedLocalRepositoryManagerFactory
instanceKlass org/eclipse/aether/spi/localrepo/LocalRepositoryManagerFactory
instanceKlass org/eclipse/aether/internal/impl/DefaultUpdatePolicyAnalyzer
instanceKlass org/eclipse/aether/impl/UpdatePolicyAnalyzer
instanceKlass org/eclipse/aether/internal/impl/DefaultUpdateCheckManager
instanceKlass org/eclipse/aether/impl/UpdateCheckManager
instanceKlass java/lang/Long$LongCache
instanceKlass org/eclipse/aether/internal/impl/DefaultTransporterProvider
instanceKlass org/eclipse/aether/spi/connector/transport/TransporterProvider
instanceKlass org/eclipse/aether/internal/impl/DefaultTrackingFileManager
instanceKlass org/eclipse/aether/internal/impl/TrackingFileManager
instanceKlass org/eclipse/aether/internal/impl/DefaultRepositorySystemLifecycle
instanceKlass org/eclipse/aether/impl/RepositorySystemLifecycle
instanceKlass org/eclipse/aether/internal/impl/DefaultRepositorySystem
instanceKlass org/eclipse/aether/RepositorySystem
instanceKlass org/eclipse/aether/internal/impl/DefaultRepositoryLayoutProvider
instanceKlass org/eclipse/aether/spi/connector/layout/RepositoryLayoutProvider
instanceKlass org/eclipse/aether/internal/impl/DefaultRepositoryEventDispatcher
instanceKlass org/eclipse/aether/impl/RepositoryEventDispatcher
instanceKlass org/eclipse/aether/internal/impl/DefaultRepositoryConnectorProvider
instanceKlass org/eclipse/aether/impl/RepositoryConnectorProvider
instanceKlass org/eclipse/aether/internal/impl/DefaultRemoteRepositoryManager
instanceKlass org/eclipse/aether/impl/RemoteRepositoryManager
instanceKlass org/eclipse/aether/internal/impl/DefaultOfflineController
instanceKlass org/eclipse/aether/impl/OfflineController
instanceKlass org/eclipse/aether/internal/impl/DefaultMetadataResolver
instanceKlass org/eclipse/aether/impl/MetadataResolver
instanceKlass org/eclipse/aether/internal/impl/DefaultLocalRepositoryProvider
instanceKlass org/eclipse/aether/impl/LocalRepositoryProvider
instanceKlass org/eclipse/aether/internal/impl/LocalPathPrefixComposerFactorySupport
instanceKlass org/eclipse/aether/internal/impl/LocalPathPrefixComposerFactory
instanceKlass org/eclipse/aether/internal/impl/DefaultLocalPathComposer
instanceKlass org/eclipse/aether/internal/impl/LocalPathComposer
instanceKlass org/eclipse/aether/internal/impl/DefaultInstaller
instanceKlass org/eclipse/aether/impl/Installer
instanceKlass org/eclipse/aether/internal/impl/DefaultFileProcessor
instanceKlass org/eclipse/aether/spi/io/FileProcessor
instanceKlass org/eclipse/aether/internal/impl/DefaultDeployer
instanceKlass org/eclipse/aether/impl/Deployer
instanceKlass org/eclipse/aether/internal/impl/DefaultChecksumPolicyProvider
instanceKlass org/eclipse/aether/spi/connector/checksum/ChecksumPolicyProvider
instanceKlass org/eclipse/aether/internal/impl/DefaultArtifactResolver
instanceKlass org/eclipse/aether/impl/ArtifactResolver
instanceKlass org/eclipse/aether/connector/basic/BasicRepositoryConnectorFactory
instanceKlass org/eclipse/aether/spi/locator/Service
instanceKlass org/eclipse/aether/spi/connector/RepositoryConnectorFactory
instanceKlass org/apache/maven/model/validation/DefaultModelValidator
instanceKlass org/apache/maven/model/validation/ModelValidator
instanceKlass org/apache/maven/model/superpom/DefaultSuperPomProvider
instanceKlass org/apache/maven/model/superpom/SuperPomProvider
instanceKlass org/apache/maven/model/profile/activation/PropertyProfileActivator
instanceKlass org/apache/maven/model/profile/activation/OperatingSystemProfileActivator
instanceKlass org/apache/maven/model/profile/activation/JdkVersionProfileActivator
instanceKlass org/apache/maven/model/profile/activation/FileProfileActivator
instanceKlass org/apache/maven/model/profile/activation/ProfileActivator
instanceKlass org/apache/maven/model/profile/DefaultProfileSelector
instanceKlass org/apache/maven/model/profile/ProfileSelector
instanceKlass org/apache/maven/model/profile/DefaultProfileInjector
instanceKlass org/apache/maven/model/profile/ProfileInjector
instanceKlass org/apache/maven/model/plugin/DefaultReportingConverter
instanceKlass org/apache/maven/model/plugin/ReportingConverter
instanceKlass org/apache/maven/model/plugin/DefaultReportConfigurationExpander
instanceKlass org/apache/maven/model/plugin/ReportConfigurationExpander
instanceKlass org/apache/maven/model/plugin/DefaultPluginConfigurationExpander
instanceKlass org/apache/maven/model/plugin/PluginConfigurationExpander
instanceKlass org/apache/maven/model/path/ProfileActivationFilePathInterpolator
instanceKlass org/apache/maven/model/path/DefaultUrlNormalizer
instanceKlass org/apache/maven/model/path/UrlNormalizer
instanceKlass org/apache/maven/model/path/DefaultPathTranslator
instanceKlass org/apache/maven/model/path/PathTranslator
instanceKlass org/apache/maven/model/path/DefaultModelUrlNormalizer
instanceKlass org/apache/maven/model/path/ModelUrlNormalizer
instanceKlass org/apache/maven/model/path/DefaultModelPathTranslator
instanceKlass org/apache/maven/model/path/ModelPathTranslator
instanceKlass org/apache/maven/model/normalization/DefaultModelNormalizer
instanceKlass org/apache/maven/model/normalization/ModelNormalizer
instanceKlass org/apache/maven/model/management/DefaultPluginManagementInjector
instanceKlass org/apache/maven/model/management/PluginManagementInjector
instanceKlass org/apache/maven/model/management/DefaultDependencyManagementInjector
instanceKlass org/apache/maven/model/management/DependencyManagementInjector
instanceKlass org/apache/maven/model/locator/DefaultModelLocator
instanceKlass org/apache/maven/model/io/DefaultModelWriter
instanceKlass org/apache/maven/model/io/ModelWriter
instanceKlass org/apache/maven/model/io/DefaultModelReader
instanceKlass org/apache/maven/model/interpolation/AbstractStringBasedModelInterpolator
instanceKlass org/apache/maven/model/interpolation/ModelInterpolator
instanceKlass org/apache/maven/model/interpolation/DefaultModelVersionProcessor
instanceKlass org/apache/maven/model/interpolation/ModelVersionProcessor
instanceKlass org/apache/maven/model/inheritance/DefaultInheritanceAssembler
instanceKlass org/apache/maven/model/inheritance/InheritanceAssembler
instanceKlass org/apache/maven/model/composition/DefaultDependencyManagementImporter
instanceKlass org/apache/maven/model/composition/DependencyManagementImporter
instanceKlass  @bci sun/reflect/annotation/AnnotationParser parseClassArray (ILjava/nio/ByteBuffer;Ljdk/internal/reflect/ConstantPool;Ljava/lang/Class;)Ljava/lang/Object; 10 <appendix> member <vmtarget> ; # sun/reflect/annotation/AnnotationParser$$Lambda+0x000002da81073928
instanceKlass org/apache/maven/model/building/DefaultModelProcessor
instanceKlass org/apache/maven/model/building/ModelProcessor
instanceKlass org/apache/maven/model/io/ModelReader
instanceKlass org/apache/maven/model/locator/ModelLocator
instanceKlass org/apache/maven/model/building/DefaultModelBuilder
instanceKlass org/apache/maven/model/building/ModelBuilder
instanceKlass org/apache/maven/cli/internal/BootstrapCoreExtensionManager
instanceKlass org/apache/maven/cli/configuration/SettingsXmlConfigurationProcessor
instanceKlass org/apache/maven/cli/configuration/ConfigurationProcessor
instanceKlass org/apache/maven/toolchain/io/DefaultToolchainsWriter
instanceKlass org/apache/maven/toolchain/io/ToolchainsWriter
instanceKlass org/apache/maven/toolchain/io/DefaultToolchainsReader
instanceKlass org/apache/maven/toolchain/io/ToolchainsReader
instanceKlass org/apache/maven/toolchain/building/DefaultToolchainsBuilder
instanceKlass org/apache/maven/toolchain/building/ToolchainsBuilder
instanceKlass org/apache/maven/execution/MavenSession
instanceKlass org/apache/maven/session/scope/internal/SessionScope$ScopeState
instanceKlass  @bci org/apache/maven/session/scope/internal/SessionScope <clinit> ()V 0 <appendix> argL0 ; # org/apache/maven/session/scope/internal/SessionScope$$Lambda+0x000002da810c9030
instanceKlass org/apache/maven/session/scope/internal/SessionScope
instanceKlass org/apache/maven/plugin/internal/AbstractMavenPluginDependenciesValidator
instanceKlass org/apache/maven/plugin/internal/MavenPluginDependenciesValidator
instanceKlass org/apache/maven/plugin/internal/AbstractMavenPluginParametersValidator
instanceKlass org/apache/maven/plugin/internal/MavenPluginConfigurationValidator
instanceKlass org/apache/maven/eventspy/AbstractEventSpy
instanceKlass org/apache/maven/eventspy/EventSpy
instanceKlass org/apache/maven/plugin/PluginValidationManager
instanceKlass org/apache/maven/plugin/DefaultMojosExecutionStrategy
instanceKlass org/apache/maven/plugin/MojosExecutionStrategy
instanceKlass org/apache/maven/lifecycle/internal/LifecycleDependencyResolver
instanceKlass org/apache/maven/lifecycle/internal/DefaultProjectArtifactFactory
instanceKlass org/apache/maven/lifecycle/internal/ProjectArtifactFactory
instanceKlass org/apache/maven/internal/aether/ResolverLifecycle
instanceKlass org/apache/maven/internal/aether/DefaultRepositorySystemSessionFactory
instanceKlass org/apache/maven/extension/internal/CoreExportsProvider
instanceKlass org/apache/maven/plugin/MojoExecution
instanceKlass org/apache/maven/project/MavenProject
instanceKlass org/apache/maven/execution/scope/internal/MojoExecutionScope$ScopeState
instanceKlass org/apache/maven/execution/MojoExecutionEvent
instanceKlass org/apache/maven/execution/scope/MojoExecutionScoped
instanceKlass com/google/inject/RestrictedBindingSource$Permit
instanceKlass org/apache/maven/execution/scope/internal/MojoExecutionScope$1
instanceKlass org/apache/maven/execution/scope/internal/MojoExecutionScope
instanceKlass org/apache/maven/execution/MojoExecutionListener
instanceKlass org/eclipse/sisu/space/QualifiedTypeBinder$1
instanceKlass org/apache/maven/execution/DefaultMavenExecutionRequestPopulator
instanceKlass org/apache/maven/execution/MavenExecutionRequestPopulator
instanceKlass org/apache/maven/classrealm/DefaultClassRealmManager
instanceKlass org/apache/maven/classrealm/ClassRealmManager
instanceKlass org/apache/maven/SessionScoped
instanceKlass org/apache/maven/ReactorReader
instanceKlass org/apache/maven/repository/internal/MavenWorkspaceReader
instanceKlass org/eclipse/aether/repository/WorkspaceReader
instanceKlass org/eclipse/sisu/space/WildcardKey$QualifiedImpl
instanceKlass org/eclipse/sisu/space/WildcardKey$Qualified
instanceKlass org/eclipse/sisu/space/WildcardKey
instanceKlass org/eclipse/sisu/Typed
instanceKlass org/sonatype/inject/EagerSingleton
instanceKlass org/eclipse/sisu/EagerSingleton
instanceKlass org/sonatype/inject/Mediator
instanceKlass org/eclipse/sisu/inject/TypeArguments
instanceKlass org/apache/maven/DefaultArtifactFilterManager
instanceKlass org/apache/maven/ArtifactFilterManager
instanceKlass org/eclipse/sisu/space/asm/Context
instanceKlass org/eclipse/sisu/space/asm/Attribute
instanceKlass org/eclipse/sisu/space/asm/AnnotationVisitor
instanceKlass org/eclipse/sisu/space/asm/ClassReader
instanceKlass org/eclipse/sisu/space/IndexedClassFinder$1
instanceKlass org/eclipse/sisu/inject/Logs$SLF4JSink
instanceKlass org/eclipse/sisu/inject/Logs$Sink
instanceKlass org/eclipse/sisu/inject/Logs
instanceKlass org/eclipse/sisu/space/QualifierCache
instanceKlass org/eclipse/sisu/space/QualifiedTypeVisitor
instanceKlass org/eclipse/sisu/plexus/PlexusTypeVisitor$ComponentAnnotationVisitor
instanceKlass org/eclipse/sisu/space/AnnotationVisitor
instanceKlass org/eclipse/sisu/plexus/PlexusTypeVisitor
instanceKlass org/eclipse/sisu/space/ClassVisitor
instanceKlass org/eclipse/sisu/plexus/PlexusXmlBeanModule$PlexusXmlBeanSource
instanceKlass org/eclipse/sisu/inject/DescriptionSource
instanceKlass org/eclipse/sisu/inject/AnnotatedSource
instanceKlass org/eclipse/sisu/Hidden
instanceKlass org/eclipse/sisu/Priority
instanceKlass org/eclipse/sisu/Description
instanceKlass org/eclipse/sisu/inject/Sources
instanceKlass com/google/inject/Key$AnnotationInstanceStrategy
instanceKlass com/google/inject/name/NamedImpl
instanceKlass com/google/inject/name/Named
instanceKlass com/google/inject/name/Names
instanceKlass com/google/inject/internal/MoreTypes$ParameterizedTypeImpl
instanceKlass sun/reflect/generics/reflectiveObjects/ParameterizedTypeImpl
instanceKlass sun/reflect/generics/reflectiveObjects/LazyReflectiveObjectGenerator
instanceKlass org/apache/maven/wagon/Wagon
instanceKlass org/apache/maven/toolchain/ToolchainsBuilder
instanceKlass org/apache/maven/toolchain/ToolchainManagerPrivate
instanceKlass org/apache/maven/toolchain/ToolchainManager
instanceKlass org/apache/maven/toolchain/ToolchainFactory
instanceKlass org/apache/maven/settings/MavenSettingsBuilder
instanceKlass org/apache/maven/rtinfo/RuntimeInformation
instanceKlass org/apache/maven/project/artifact/ProjectArtifactsCache
instanceKlass org/apache/maven/project/artifact/MavenMetadataCache
instanceKlass org/apache/maven/project/ProjectRealmCache
instanceKlass org/apache/maven/project/ProjectDependenciesResolver
instanceKlass org/apache/maven/project/ProjectBuildingHelper
instanceKlass org/apache/maven/project/ProjectBuilder
instanceKlass org/apache/maven/project/MavenProjectHelper
instanceKlass org/apache/maven/plugin/version/PluginVersionResolver
instanceKlass org/apache/maven/plugin/prefix/PluginPrefixResolver
instanceKlass org/apache/maven/plugin/internal/PluginDependenciesResolver
instanceKlass org/apache/maven/plugin/PluginRealmCache
instanceKlass org/apache/maven/plugin/PluginManager
instanceKlass org/apache/maven/plugin/PluginDescriptorCache
instanceKlass org/apache/maven/plugin/PluginArtifactsCache
instanceKlass org/apache/maven/plugin/MavenPluginManager
instanceKlass org/apache/maven/plugin/LegacySupport
instanceKlass org/apache/maven/plugin/ExtensionRealmCache
instanceKlass org/apache/maven/plugin/BuildPluginManager
instanceKlass org/apache/maven/model/plugin/LifecycleBindingsInjector
instanceKlass org/apache/maven/lifecycle/internal/builder/BuilderCommon
instanceKlass org/apache/maven/lifecycle/internal/builder/Builder
instanceKlass org/apache/maven/lifecycle/internal/MojoExecutor
instanceKlass org/apache/maven/lifecycle/internal/MojoDescriptorCreator
instanceKlass org/apache/maven/lifecycle/internal/LifecycleTaskSegmentCalculator
instanceKlass org/apache/maven/lifecycle/internal/LifecycleStarter
instanceKlass org/apache/maven/lifecycle/internal/LifecyclePluginResolver
instanceKlass org/apache/maven/lifecycle/internal/LifecycleModuleBuilder
instanceKlass org/apache/maven/lifecycle/internal/LifecycleExecutionPlanCalculator
instanceKlass org/apache/maven/lifecycle/internal/LifecycleDebugLogger
instanceKlass org/apache/maven/lifecycle/internal/ExecutionEventCatapult
instanceKlass org/apache/maven/lifecycle/internal/BuildListCalculator
instanceKlass org/apache/maven/lifecycle/MojoExecutionConfigurator
instanceKlass org/apache/maven/lifecycle/LifecycleMappingDelegate
instanceKlass org/apache/maven/lifecycle/LifecycleExecutor
instanceKlass org/apache/maven/lifecycle/LifeCyclePluginAnalyzer
instanceKlass org/apache/maven/lifecycle/DefaultLifecycles
instanceKlass org/apache/maven/graph/GraphBuilder
instanceKlass org/apache/maven/eventspy/internal/EventSpyDispatcher
instanceKlass org/apache/maven/configuration/BeanConfigurator
instanceKlass org/apache/maven/bridge/MavenRepositorySystem
instanceKlass org/apache/maven/artifact/resolver/ResolutionErrorHandler
instanceKlass org/apache/maven/artifact/repository/metadata/io/MetadataReader
instanceKlass org/apache/maven/artifact/metadata/ArtifactMetadataSource
instanceKlass org/apache/maven/repository/legacy/metadata/ArtifactMetadataSource
instanceKlass org/apache/maven/artifact/handler/manager/ArtifactHandlerManager
instanceKlass org/apache/maven/artifact/factory/ArtifactFactory
instanceKlass org/apache/maven/ProjectDependenciesResolver
instanceKlass org/apache/maven/Maven
instanceKlass org/apache/maven/artifact/handler/ArtifactHandler
instanceKlass org/sonatype/plexus/components/sec/dispatcher/SecDispatcher
instanceKlass org/apache/maven/lifecycle/Lifecycle
instanceKlass org/eclipse/sisu/space/CloningClassSpace$1
instanceKlass org/apache/maven/lifecycle/mapping/LifecycleMapping
instanceKlass org/apache/maven/repository/metadata/GraphConflictResolver
instanceKlass org/apache/maven/repository/metadata/GraphConflictResolutionPolicy
instanceKlass org/eclipse/sisu/plexus/ConfigurationImpl
instanceKlass org/apache/maven/repository/metadata/ClasspathTransformation
instanceKlass org/apache/maven/repository/legacy/resolver/transform/ArtifactTransformationManager
instanceKlass org/apache/maven/repository/legacy/resolver/transform/ArtifactTransformation
instanceKlass org/apache/maven/repository/legacy/resolver/conflict/ConflictResolverFactory
instanceKlass org/apache/maven/repository/legacy/resolver/conflict/ConflictResolver
instanceKlass java/lang/foreign/MemorySegment
instanceKlass org/apache/maven/repository/legacy/repository/ArtifactRepositoryFactory
instanceKlass org/apache/maven/repository/legacy/UpdateCheckManager
instanceKlass org/apache/maven/repository/RepositorySystem
instanceKlass org/apache/maven/repository/MirrorSelector
instanceKlass org/apache/maven/project/validation/ModelValidator
instanceKlass org/apache/maven/project/path/PathTranslator
instanceKlass org/apache/maven/project/interpolation/ModelInterpolator
instanceKlass org/apache/maven/project/inheritance/ModelInheritanceAssembler
instanceKlass org/apache/maven/project/MavenProjectBuilder
instanceKlass org/apache/maven/profiles/MavenProfilesBuilder
instanceKlass org/apache/maven/execution/RuntimeInformation
instanceKlass org/apache/maven/artifact/resolver/ArtifactResolver
instanceKlass org/apache/maven/artifact/resolver/ArtifactCollector
instanceKlass org/apache/maven/repository/legacy/resolver/LegacyArtifactCollector
instanceKlass org/apache/maven/artifact/repository/metadata/RepositoryMetadataManager
instanceKlass org/apache/maven/artifact/repository/layout/ArtifactRepositoryLayout
instanceKlass org/apache/maven/artifact/repository/ArtifactRepositoryFactory
instanceKlass org/apache/maven/artifact/manager/WagonManager
instanceKlass org/apache/maven/repository/legacy/WagonManager
instanceKlass org/apache/maven/artifact/installer/ArtifactInstaller
instanceKlass org/eclipse/sisu/plexus/PlexusXmlMetadata
instanceKlass org/eclipse/sisu/plexus/Roles
instanceKlass org/apache/maven/artifact/deployer/ArtifactDeployer
instanceKlass org/eclipse/sisu/plexus/Hints
instanceKlass org/eclipse/sisu/space/AbstractDeferredClass
instanceKlass org/eclipse/sisu/plexus/RequirementImpl
instanceKlass org/codehaus/plexus/component/annotations/Requirement
instanceKlass org/eclipse/sisu/space/Streams
instanceKlass org/eclipse/sisu/plexus/ComponentImpl
instanceKlass org/codehaus/plexus/component/annotations/Component
instanceKlass org/eclipse/sisu/plexus/PlexusTypeRegistry
instanceKlass org/eclipse/sisu/plexus/PlexusXmlScanner
instanceKlass org/eclipse/sisu/space/QualifiedTypeBinder
instanceKlass org/eclipse/sisu/plexus/PlexusTypeBinder
instanceKlass com/google/inject/spi/InjectionRequest
instanceKlass org/eclipse/sisu/bean/BeanProperty
instanceKlass com/google/common/collect/ObjectArrays
instanceKlass com/google/inject/internal/Nullability
instanceKlass  @bci com/google/inject/internal/KotlinSupport$KotlinUnsupported <clinit> ()V 7 <appendix> argL0 ; # com/google/inject/internal/KotlinSupport$KotlinUnsupported$$Lambda+0x000002da810aae78
instanceKlass  @cpi org/apache/maven/utils/Os 183 form vmentry <vmtarget> ; # java/lang/invoke/LambdaForm$DMH+0x000002da810a8c00
instanceKlass com/google/inject/internal/KotlinSupport$KotlinUnsupported
instanceKlass com/google/inject/internal/KotlinSupport$KotlinSupportHolder
instanceKlass com/google/inject/internal/KotlinSupportInterface
instanceKlass com/google/inject/internal/KotlinSupport
instanceKlass com/google/inject/spi/InjectionPoint$OverrideIndex
instanceKlass org/eclipse/sisu/inject/RankedBindings
instanceKlass org/eclipse/sisu/Mediator
instanceKlass sun/reflect/generics/tree/TypeVariableSignature
instanceKlass com/google/inject/Inject
instanceKlass javax/inject/Inject
instanceKlass  @bci com/google/inject/internal/DeclaredMembers getDeclaredFields (Ljava/lang/Class;)[Ljava/lang/reflect/Field; 38 <appendix> argL0 ; # com/google/inject/internal/DeclaredMembers$$Lambda+0x000002da810af200
instanceKlass  @bci com/google/inject/internal/DeclaredMembers getDeclaredFields (Ljava/lang/Class;)[Ljava/lang/reflect/Field; 20 <appendix> argL0 ; # com/google/inject/internal/DeclaredMembers$$Lambda+0x000002da810aefd0
instanceKlass  @bci com/google/inject/internal/DeclaredMembers getDeclaredFields (Ljava/lang/Class;)[Ljava/lang/reflect/Field; 15 <appendix> argL0 ; # com/google/inject/internal/DeclaredMembers$$Lambda+0x000002da810aeda0
instanceKlass  @bci com/google/inject/internal/DeclaredMembers getDeclaredFields (Ljava/lang/Class;)[Ljava/lang/reflect/Field; 7 <appendix> argL0 ; # com/google/inject/internal/DeclaredMembers$$Lambda+0x000002da810aeb70
instanceKlass java/lang/reflect/WildcardType
instanceKlass com/google/inject/spi/InjectionPoint$InjectableMembers
instanceKlass com/google/inject/spi/InjectionPoint$InjectableMember
instanceKlass com/google/inject/spi/InjectionPoint
instanceKlass java/lang/reflect/ParameterizedType
instanceKlass com/google/inject/internal/MoreTypes$GenericArrayTypeImpl
instanceKlass com/google/inject/internal/MoreTypes$CompositeType
instanceKlass com/google/inject/Key$AnnotationTypeStrategy
instanceKlass com/google/common/cache/LocalCache$StrongValueReference
instanceKlass com/google/common/util/concurrent/AbstractFuture$Failure
instanceKlass com/google/common/util/concurrent/AbstractFuture$Cancellation
instanceKlass com/google/common/util/concurrent/AbstractFuture$SetFuture
instanceKlass com/google/common/util/concurrent/Uninterruptibles
instanceKlass com/google/common/util/concurrent/AbstractFuture$Waiter
instanceKlass com/google/common/util/concurrent/AbstractFuture$Listener
# instanceKlass java/lang/invoke/LambdaForm$MH+0x000002da810a8800
instanceKlass com/google/common/util/concurrent/AbstractFuture$UnsafeAtomicHelper$1
instanceKlass sun/misc/Unsafe
instanceKlass com/google/common/util/concurrent/LazyLogger
instanceKlass java/util/concurrent/Executor
instanceKlass com/google/common/util/concurrent/AbstractFuture$AtomicHelper
instanceKlass com/google/common/util/concurrent/internal/InternalFutureFailureAccess
instanceKlass com/google/common/util/concurrent/AbstractFuture$Trusted
instanceKlass com/google/common/util/concurrent/ListenableFuture
instanceKlass  @bci org/apache/maven/plugin/internal/DefaultMavenPluginManager getPluginDescriptor (Lorg/apache/maven/model/Plugin;Ljava/util/List;Lorg/eclipse/aether/RepositorySystemSession;)Lorg/apache/maven/plugin/descriptor/PluginDescriptor; 24 <appendix> form vmentry <vmtarget> ; # java/lang/invoke/LambdaForm$DMH+0x000002da810a8400
instanceKlass  @bci sun/reflect/annotation/AnnotationParser parseEnumArray (ILjava/lang/Class;Ljava/nio/ByteBuffer;Ljdk/internal/reflect/ConstantPool;Ljava/lang/Class;)Ljava/lang/Object; 16 <appendix> member <vmtarget> ; # sun/reflect/annotation/AnnotationParser$$Lambda+0x000002da81070050
instanceKlass java/lang/annotation/Documented
instanceKlass java/lang/annotation/Target
instanceKlass javax/inject/Named
instanceKlass javax/inject/Qualifier
instanceKlass com/google/inject/BindingAnnotation
instanceKlass javax/inject/Scope
instanceKlass com/google/inject/ScopeAnnotation
instanceKlass com/google/inject/internal/Annotations$AnnotationChecker
# instanceKlass java/lang/invoke/LambdaForm$MH+0x000002da810a8000
instanceKlass java/lang/reflect/Proxy$ProxyBuilder$1
instanceKlass jdk/internal/org/objectweb/asm/Edge
instanceKlass  @bci java/lang/reflect/ProxyGenerator addProxyMethod (Ljava/lang/reflect/Method;Ljava/lang/Class;)V 23 <appendix> argL0 ; # java/lang/reflect/ProxyGenerator$$Lambda+0x000002da8106e930
instanceKlass  @bci java/lang/reflect/ProxyGenerator addProxyMethod (Ljava/lang/reflect/ProxyGenerator$ProxyMethod;)V 10 <appendix> argL0 ; # java/lang/reflect/ProxyGenerator$$Lambda+0x000002da8106e700
instanceKlass java/util/StringJoiner
instanceKlass java/lang/reflect/ProxyGenerator$ProxyMethod
instanceKlass  @bci java/lang/reflect/Proxy getLoader (Ljava/lang/Module;)Ljava/lang/ClassLoader; 6 <appendix> member <vmtarget> ; # java/lang/reflect/Proxy$$Lambda+0x000002da8106dfd8
instanceKlass  @bci java/lang/WeakPairMap computeIfAbsent (Ljava/lang/Object;Ljava/lang/Object;Ljava/util/function/BiFunction;)Ljava/lang/Object; 18 <appendix> member <vmtarget> ; # java/lang/WeakPairMap$$Lambda+0x000002da8106dba0
instanceKlass  @bci java/lang/Module implAddExportsOrOpens (Ljava/lang/String;Ljava/lang/Module;ZZ)V 145 <appendix> argL0 ; # java/lang/Module$$Lambda+0x000002da8106d980
instanceKlass  @bci java/lang/module/ModuleDescriptor$Builder packages (Ljava/util/Set;)Ljava/lang/module/ModuleDescriptor$Builder; 17 <appendix> argL0 ; # java/lang/module/ModuleDescriptor$Builder$$Lambda+0x800000002
instanceKlass java/lang/WeakPairMap$Pair$Lookup
instanceKlass java/lang/WeakPairMap$Pair
instanceKlass java/lang/WeakPairMap
instanceKlass java/lang/Module$ReflectionData
instanceKlass jdk/internal/module/Checks
instanceKlass java/lang/module/ModuleDescriptor$Builder
instanceKlass  @bci java/lang/reflect/Proxy$ProxyBuilder getDynamicModule (Ljava/lang/ClassLoader;)Ljava/lang/Module; 4 <appendix> argL0 ; # java/lang/reflect/Proxy$ProxyBuilder$$Lambda+0x000002da8106d068
instanceKlass java/lang/PublicMethods
instanceKlass java/util/Collections$1
instanceKlass java/lang/reflect/Proxy$ProxyBuilder
instanceKlass  @bci java/lang/reflect/Proxy getProxyConstructor (Ljava/lang/Class;Ljava/lang/ClassLoader;[Ljava/lang/Class;)Ljava/lang/reflect/Constructor; 35 <appendix> argL0 ; # java/lang/reflect/Proxy$$Lambda+0x000002da8106c450
instanceKlass java/lang/reflect/Proxy
instanceKlass sun/reflect/annotation/AnnotationInvocationHandler
instanceKlass sun/reflect/annotation/AnnotationParser$1
instanceKlass sun/reflect/annotation/ExceptionProxy
instanceKlass java/lang/annotation/Inherited
instanceKlass java/lang/annotation/Retention
instanceKlass sun/reflect/annotation/AnnotationType$1
instanceKlass sun/reflect/annotation/AnnotationType
instanceKlass java/lang/reflect/GenericArrayType
instanceKlass sun/reflect/generics/visitor/Reifier
instanceKlass sun/reflect/generics/visitor/TypeTreeVisitor
instanceKlass sun/reflect/generics/factory/CoreReflectionFactory
instanceKlass sun/reflect/generics/factory/GenericsFactory
instanceKlass sun/reflect/generics/scope/AbstractScope
instanceKlass sun/reflect/generics/scope/Scope
instanceKlass com/google/inject/internal/Annotations$TestAnnotation
instanceKlass com/google/inject/internal/Annotations$AnnotationToStringConfig
instanceKlass com/google/common/base/Joiner$MapJoiner
instanceKlass com/google/common/base/Joiner
instanceKlass java/lang/reflect/InvocationHandler
instanceKlass com/google/inject/internal/Annotations
instanceKlass org/eclipse/sisu/Parameters
instanceKlass org/eclipse/sisu/wire/ParameterKeys
instanceKlass com/google/inject/internal/util/StackTraceElements$InMemoryStackTraceElement
instanceKlass com/google/inject/internal/util/StackTraceElements
instanceKlass org/eclipse/sisu/wire/TypeConverterCache
instanceKlass com/google/inject/internal/Scoping
instanceKlass com/google/inject/internal/InternalFactory
instanceKlass java/lang/StackTraceElement$HashedModules
instanceKlass com/google/inject/internal/InternalFlags$1
instanceKlass com/google/inject/internal/InternalFlags
instanceKlass com/google/inject/spi/ConstructorBinding
instanceKlass com/google/inject/spi/InstanceBinding
instanceKlass com/google/inject/internal/DelayedInitialize
instanceKlass com/google/inject/spi/ProviderKeyBinding
instanceKlass com/google/inject/spi/ProviderInstanceBinding
instanceKlass com/google/inject/spi/HasDependencies
instanceKlass com/google/inject/spi/LinkedKeyBinding
instanceKlass com/google/inject/spi/UntargettedBinding
instanceKlass com/google/inject/internal/BindingImpl
instanceKlass com/google/inject/Key$AnnotationStrategy
instanceKlass org/eclipse/sisu/wire/ElementAnalyzer$1
instanceKlass com/google/inject/util/Modules$EmptyModule
instanceKlass com/google/inject/util/Modules$OverriddenModuleBuilder
instanceKlass com/google/inject/util/Modules
instanceKlass  @bci com/google/inject/internal/DeclaredMembers getDeclaredMethods (Ljava/lang/Class;)[Ljava/lang/reflect/Method; 62 <appendix> argL0 ; # com/google/inject/internal/DeclaredMembers$$Lambda+0x000002da8109d400
instanceKlass java/util/stream/SortedOps
instanceKlass com/google/common/collect/Ordering
instanceKlass  @bci com/google/inject/internal/DeclaredMembers getDeclaredMethods (Ljava/lang/Class;)[Ljava/lang/reflect/Method; 38 <appendix> argL0 ; # com/google/inject/internal/DeclaredMembers$$Lambda+0x000002da81096920
instanceKlass  @bci com/google/inject/internal/DeclaredMembers getDeclaredMethods (Ljava/lang/Class;)[Ljava/lang/reflect/Method; 33 <appendix> argL0 ; # com/google/inject/internal/DeclaredMembers$$Lambda+0x000002da810966f0
instanceKlass  @bci java/util/Comparator thenComparing (Ljava/util/Comparator;)Ljava/util/Comparator; 7 <appendix> member <vmtarget> ; # java/util/Comparator$$Lambda+0x000002da81067f18
instanceKlass  @cpi java/util/Comparator 251 form vmentry <vmtarget> ; # java/lang/invoke/LambdaForm$DMH+0x000002da81098800
instanceKlass  @bci java/util/Comparator comparing (Ljava/util/function/Function;Ljava/util/Comparator;)Ljava/util/Comparator; 12 <appendix> member <vmtarget> ; # java/util/Comparator$$Lambda+0x000002da81067c88
instanceKlass  @bci com/google/inject/internal/DeclaredMembers getDeclaredMethods (Ljava/lang/Class;)[Ljava/lang/reflect/Method; 20 <appendix> argL0 ; # com/google/inject/internal/DeclaredMembers$$Lambda+0x000002da810964c0
instanceKlass  @bci com/google/inject/internal/DeclaredMembers getDeclaredMethods (Ljava/lang/Class;)[Ljava/lang/reflect/Method; 15 <appendix> argL0 ; # com/google/inject/internal/DeclaredMembers$$Lambda+0x000002da81096290
instanceKlass  @bci java/util/Comparator comparing (Ljava/util/function/Function;)Ljava/util/Comparator; 6 <appendix> member <vmtarget> ; # java/util/Comparator$$Lambda+0x000002da810679f8
instanceKlass  @bci java/lang/invoke/BootstrapMethodInvoker invoke (Ljava/lang/Class;Ljava/lang/invoke/MethodHandle;Ljava/lang/String;Ljava/lang/Object;Ljava/lang/Object;Ljava/lang/Class;)Ljava/lang/Object; 462 <adapter> ; # java/lang/invoke/LambdaForm$MH+0x000002da81098400
instanceKlass  @cpi java/util/Comparator 259 form vmentry <vmtarget> ; # java/lang/invoke/LambdaForm$DMH+0x000002da81098000
instanceKlass  @bci com/google/inject/internal/DeclaredMembers getDeclaredMethods (Ljava/lang/Class;)[Ljava/lang/reflect/Method; 7 <appendix> argL0 ; # com/google/inject/internal/DeclaredMembers$$Lambda+0x000002da81096060
instanceKlass com/google/inject/internal/DeclaredMembers
instanceKlass com/google/common/base/ExtraObjectsMethodsForWeb
instanceKlass com/google/common/collect/ImmutableMap$Builder
instanceKlass com/google/inject/internal/MoreTypes
instanceKlass com/google/inject/multibindings/ProvidesIntoOptional
instanceKlass com/google/inject/multibindings/ProvidesIntoMap
instanceKlass com/google/inject/multibindings/ProvidesIntoSet
instanceKlass com/google/inject/Provides
instanceKlass javax/inject/Singleton
instanceKlass com/google/inject/spi/ElementSource
instanceKlass com/google/inject/spi/ScopeBinding
instanceKlass com/google/inject/Scopes$2
instanceKlass com/google/inject/Scopes$1
instanceKlass com/google/inject/internal/SingletonScope
instanceKlass com/google/inject/Scopes
instanceKlass com/google/inject/Singleton
instanceKlass com/google/inject/spi/Elements$ModuleInfo
instanceKlass com/google/inject/PrivateModule
instanceKlass  @bci java/util/stream/Collectors toList ()Ljava/util/stream/Collector; 14 <appendix> argL0 ; # java/util/stream/Collectors$$Lambda+0x000002da810677c0
instanceKlass  @bci java/util/stream/Collectors toList ()Ljava/util/stream/Collector; 9 <appendix> argL0 ; # java/util/stream/Collectors$$Lambda+0x000002da810675a0
instanceKlass  @bci java/util/stream/Collectors toList ()Ljava/util/stream/Collector; 4 <appendix> argL0 ; # java/util/stream/Collectors$$Lambda+0x000002da81067390
instanceKlass  @bci com/google/inject/spi/BindingSourceRestriction$PermitMapConstruction pushModule (Ljava/lang/Class;Lcom/google/inject/spi/ModuleSource;)V 5 <appendix> member <vmtarget> ; # com/google/inject/spi/BindingSourceRestriction$PermitMapConstruction$$Lambda+0x000002da81090470
instanceKlass  @bci com/google/inject/spi/BindingSourceRestriction getPermits (Ljava/lang/Class;)Ljava/util/stream/Stream; 43 <appendix> argL0 ; # com/google/inject/spi/BindingSourceRestriction$$Lambda+0x000002da81090230
instanceKlass  @bci com/google/inject/spi/BindingSourceRestriction getPermits (Ljava/lang/Class;)Ljava/util/stream/Stream; 33 <appendix> argL0 ; # com/google/inject/spi/BindingSourceRestriction$$Lambda+0x000002da81090000
instanceKlass  @cpi org/apache/maven/plugin/internal/MavenMixedDependenciesValidator 168 form vmentry <vmtarget> ; # java/lang/invoke/LambdaForm$DMH+0x000002da81088400
instanceKlass java/util/stream/Streams$2
instanceKlass java/util/stream/Streams$ConcatSpliterator
instanceKlass sun/reflect/annotation/AnnotatedTypeFactory$AnnotatedTypeBaseImpl
instanceKlass java/lang/reflect/AnnotatedType
instanceKlass sun/reflect/annotation/AnnotatedTypeFactory
instanceKlass sun/reflect/annotation/TypeAnnotation$LocationInfo$Location
instanceKlass sun/reflect/annotation/TypeAnnotation$LocationInfo
instanceKlass sun/reflect/generics/tree/ClassSignature
instanceKlass sun/reflect/generics/tree/Signature
instanceKlass sun/reflect/generics/tree/ClassTypeSignature
instanceKlass sun/reflect/generics/tree/SimpleClassTypeSignature
instanceKlass sun/reflect/generics/tree/FieldTypeSignature
instanceKlass sun/reflect/generics/tree/BaseType
instanceKlass sun/reflect/generics/tree/TypeSignature
instanceKlass sun/reflect/generics/tree/ReturnType
instanceKlass sun/reflect/generics/tree/TypeArgument
instanceKlass sun/reflect/generics/tree/FormalTypeParameter
instanceKlass sun/reflect/generics/tree/TypeTree
instanceKlass sun/reflect/generics/tree/Tree
instanceKlass sun/reflect/generics/parser/SignatureParser
instanceKlass java/lang/reflect/TypeVariable
instanceKlass sun/reflect/generics/repository/AbstractRepository
instanceKlass sun/reflect/annotation/TypeAnnotation
instanceKlass sun/reflect/annotation/TypeAnnotationParser
instanceKlass java/lang/Class$AnnotationData
instanceKlass com/google/inject/RestrictedBindingSource
instanceKlass com/google/inject/spi/BindingSourceRestriction
instanceKlass com/google/inject/spi/ModuleSource
instanceKlass com/google/inject/internal/ProviderMethodsModule
instanceKlass com/google/inject/spi/BindingSourceRestriction$PermitMapConstruction$PermitMapImpl
instanceKlass com/google/inject/spi/BindingSourceRestriction$PermitMap
instanceKlass com/google/inject/spi/BindingSourceRestriction$PermitMapConstruction
instanceKlass com/google/common/collect/Hashing
instanceKlass com/google/common/math/IntMath$1
instanceKlass com/google/common/math/MathPreconditions
instanceKlass com/google/common/math/IntMath
instanceKlass com/google/inject/internal/AbstractBindingBuilder
instanceKlass com/google/inject/binder/ConstantBindingBuilder
instanceKlass com/google/inject/binder/AnnotatedElementBuilder
instanceKlass com/google/inject/spi/Elements$RecordingBinder
instanceKlass com/google/inject/Binding
instanceKlass com/google/inject/spi/DefaultBindingTargetVisitor
instanceKlass com/google/inject/spi/BindingTargetVisitor
instanceKlass com/google/inject/spi/Elements
instanceKlass com/google/inject/internal/InjectorShell$RootModule
instanceKlass com/google/common/collect/ListMultimap
instanceKlass com/google/inject/internal/InjectorBindingData
instanceKlass java/util/concurrent/atomic/AtomicReferenceArray
instanceKlass java/util/concurrent/Future
instanceKlass com/google/common/cache/LocalCache$LoadingValueReference
instanceKlass java/util/concurrent/ConcurrentLinkedQueue$Node
instanceKlass com/google/common/cache/Weigher
instanceKlass com/google/common/base/Predicate
instanceKlass com/google/common/base/Equivalence
instanceKlass java/util/function/BiPredicate
instanceKlass com/google/common/base/MoreObjects
instanceKlass com/google/common/cache/LocalCache$1
instanceKlass com/google/common/cache/ReferenceEntry
instanceKlass com/google/common/cache/CacheLoader
instanceKlass com/google/common/cache/LocalCache$LocalManualCache
instanceKlass  @bci com/google/inject/internal/WeakKeySet <init> (Ljava/lang/Object;)V 12 <appendix> member <vmtarget> ; # com/google/inject/internal/WeakKeySet$$Lambda+0x000002da810848a8
instanceKlass  @cpi com/google/inject/internal/AbstractBindingProcessor$Processor 110 form vmentry <vmtarget> ; # java/lang/invoke/LambdaForm$DMH+0x000002da81088000
instanceKlass java/util/AbstractMap$SimpleImmutableEntry
instanceKlass com/google/common/cache/RemovalListener
instanceKlass com/google/common/cache/LocalCache$ValueReference
instanceKlass com/google/common/cache/CacheBuilder$2
instanceKlass com/google/common/cache/CacheStats
instanceKlass com/google/common/base/Suppliers$SupplierOfInstance
instanceKlass com/google/common/base/Suppliers
instanceKlass com/google/common/cache/CacheBuilder$1
instanceKlass com/google/common/cache/AbstractCache$StatsCounter
instanceKlass com/google/common/cache/LoadingCache
instanceKlass com/google/common/cache/Cache
instanceKlass com/google/common/base/Supplier
instanceKlass com/google/common/cache/CacheBuilder
instanceKlass com/google/inject/internal/WeakKeySet
instanceKlass com/google/common/collect/Sets
instanceKlass com/google/inject/internal/InjectorJitBindingData
instanceKlass java/util/Arrays$ArrayItr
instanceKlass com/google/inject/internal/ProcessedBindingData
instanceKlass com/google/inject/spi/DefaultElementVisitor
instanceKlass com/google/inject/internal/InjectorShell$Builder
instanceKlass com/google/common/collect/Lists
instanceKlass com/google/common/collect/AbstractMapEntry
instanceKlass com/google/common/collect/LinkedHashMultimap$ValueSetLink
instanceKlass com/google/common/collect/CollectPreconditions
instanceKlass com/google/common/collect/Platform
instanceKlass com/google/common/collect/Multiset
instanceKlass com/google/common/collect/AbstractMultimap
instanceKlass com/google/common/collect/SetMultimap
instanceKlass com/google/common/collect/Maps$EntryTransformer
instanceKlass com/google/common/collect/BiMap
instanceKlass com/google/common/collect/ImmutableMap
instanceKlass com/google/common/base/Converter
instanceKlass com/google/common/base/Function
instanceKlass com/google/common/collect/SortedMapDifference
instanceKlass com/google/common/collect/MapDifference
instanceKlass com/google/common/collect/Maps
instanceKlass com/google/inject/internal/CycleDetectingLock
instanceKlass com/google/common/collect/Multimap
instanceKlass com/google/inject/internal/CycleDetectingLock$CycleDetectingLockFactory
instanceKlass com/google/inject/internal/Initializable
instanceKlass com/google/inject/internal/Initializer
instanceKlass com/google/common/collect/PeekingIterator
instanceKlass com/google/common/collect/UnmodifiableIterator
instanceKlass com/google/common/collect/Iterators
instanceKlass com/google/common/collect/ImmutableCollection$Builder
instanceKlass com/google/common/collect/ImmutableSet$SetBuilderImpl
instanceKlass com/google/inject/internal/util/SourceProvider
instanceKlass com/google/inject/spi/ErrorDetail
instanceKlass com/google/inject/internal/Errors
instanceKlass com/google/common/base/Preconditions
instanceKlass jdk/internal/logger/DefaultLoggerFinder$1
instanceKlass java/util/logging/Logger$SystemLoggerHelper$1
instanceKlass java/util/logging/Logger$SystemLoggerHelper
instanceKlass java/util/logging/LogManager$4
instanceKlass jdk/internal/logger/BootstrapLogger$BootstrapExecutors
instanceKlass jdk/internal/logger/BootstrapLogger$RedirectedLoggers
instanceKlass java/util/Spliterators$1Adapter
instanceKlass java/util/Spliterators$ArraySpliterator
instanceKlass jdk/internal/logger/BootstrapLogger$DetectBackend$1
instanceKlass jdk/internal/logger/BootstrapLogger$DetectBackend
instanceKlass jdk/internal/logger/BootstrapLogger
instanceKlass sun/util/logging/PlatformLogger$ConfigurableBridge
instanceKlass sun/util/logging/PlatformLogger$Bridge
instanceKlass java/lang/System$Logger
instanceKlass java/util/stream/Streams
instanceKlass java/util/stream/Stream$Builder
instanceKlass java/util/stream/Streams$AbstractStreamBuilderImpl
instanceKlass  @bci java/util/stream/FindOps$FindSink$OfRef <clinit> ()V 43 <appendix> argL0 ; # java/util/stream/FindOps$FindSink$OfRef$$Lambda+0x80000004d
instanceKlass  @bci java/util/stream/FindOps$FindSink$OfRef <clinit> ()V 38 <appendix> argL0 ; # java/util/stream/FindOps$FindSink$OfRef$$Lambda+0x80000004f
instanceKlass  @bci java/util/stream/FindOps$FindSink$OfRef <clinit> ()V 16 <appendix> argL0 ; # java/util/stream/FindOps$FindSink$OfRef$$Lambda+0x80000004e
instanceKlass  @bci java/util/stream/FindOps$FindSink$OfRef <clinit> ()V 11 <appendix> argL0 ; # java/util/stream/FindOps$FindSink$OfRef$$Lambda+0x800000050
instanceKlass  @bci java/util/logging/Level$KnownLevel findByName (Ljava/lang/String;Ljava/util/function/Function;)Ljava/util/Optional; 29 <appendix> argL0 ; # java/util/logging/Level$KnownLevel$$Lambda+0x800000025
instanceKlass java/util/ArrayList$ArrayListSpliterator
instanceKlass  @bci java/util/logging/Level findLevel (Ljava/lang/String;)Ljava/util/logging/Level; 13 <appendix> argL0 ; # java/util/logging/Level$$Lambda+0x800000013
instanceKlass java/util/Hashtable$Enumerator
instanceKlass java/util/logging/LogManager$LoggerContext$1
instanceKlass java/util/logging/LogManager$VisitedLoggers
instanceKlass  @bci java/util/logging/LogManager$LoggerContext$1 run ()Ljava/lang/Void; 22 <appendix> form vmentry <vmtarget> ; # java/lang/invoke/LambdaForm$MH+0x000002da81034000
instanceKlass java/util/logging/LogManager$2
instanceKlass java/lang/System$LoggerFinder
instanceKlass java/util/logging/LogManager$LoggingProviderAccess
instanceKlass sun/util/logging/internal/LoggingProviderImpl$LogManagerAccess
instanceKlass java/util/Collections$SynchronizedMap
instanceKlass java/util/logging/LogManager$LogNode
instanceKlass java/util/logging/LogManager$LoggerContext
instanceKlass java/util/logging/LogManager$1
instanceKlass java/util/logging/LogManager
instanceKlass java/util/logging/Logger$ConfigurationData
instanceKlass java/util/logging/Logger$LoggerBundle
instanceKlass  @bci java/util/logging/Level$KnownLevel add (Ljava/util/logging/Level;)V 49 <appendix> argL0 ; # java/util/logging/Level$KnownLevel$$Lambda+0x800000024
instanceKlass  @bci java/util/logging/Level$KnownLevel add (Ljava/util/logging/Level;)V 19 <appendix> argL0 ; # java/util/logging/Level$KnownLevel$$Lambda+0x800000023
instanceKlass java/util/logging/Level
instanceKlass java/util/logging/Handler
instanceKlass java/util/logging/Logger
instanceKlass com/google/common/base/Ticker
instanceKlass com/google/common/base/Stopwatch
instanceKlass com/google/inject/internal/util/ContinuousStopwatch
instanceKlass com/google/inject/Injector
instanceKlass com/google/inject/internal/InternalInjectorCreator
instanceKlass com/google/inject/Guice
instanceKlass org/eclipse/sisu/wire/Wiring
instanceKlass org/eclipse/sisu/wire/WireModule$Strategy$1
instanceKlass org/eclipse/sisu/wire/WireModule$Strategy
instanceKlass org/eclipse/sisu/wire/AbstractTypeConverter
instanceKlass com/google/inject/spi/ElementVisitor
instanceKlass org/eclipse/sisu/wire/WireModule
instanceKlass org/eclipse/sisu/bean/BeanBinder
instanceKlass org/eclipse/sisu/plexus/PlexusBindingModule
instanceKlass org/codehaus/plexus/DefaultPlexusContainer$BootModule
instanceKlass org/codehaus/plexus/component/annotations/Configuration
instanceKlass org/eclipse/sisu/plexus/PlexusAnnotatedMetadata
instanceKlass org/eclipse/sisu/plexus/PlexusBeanMetadata
instanceKlass org/eclipse/sisu/plexus/PlexusAnnotatedBeanModule$PlexusAnnotatedBeanSource
instanceKlass org/eclipse/sisu/space/SpaceModule$2
instanceKlass org/eclipse/sisu/space/SpaceModule$Strategy$2
instanceKlass org/eclipse/sisu/space/SpaceModule$Strategy$1
instanceKlass org/eclipse/sisu/space/DefaultClassFinder
instanceKlass org/eclipse/sisu/space/asm/ClassVisitor
instanceKlass org/eclipse/sisu/space/SpaceScanner
instanceKlass org/eclipse/sisu/space/IndexedClassFinder
instanceKlass org/eclipse/sisu/space/ClassFinder
instanceKlass org/eclipse/sisu/space/SpaceModule
instanceKlass org/eclipse/sisu/space/SpaceVisitor
instanceKlass org/eclipse/sisu/plexus/PlexusTypeListener
instanceKlass org/eclipse/sisu/space/QualifiedTypeListener
instanceKlass org/eclipse/sisu/plexus/PlexusAnnotatedBeanModule$1
instanceKlass org/eclipse/sisu/space/SpaceModule$Strategy
instanceKlass org/eclipse/sisu/plexus/PlexusAnnotatedBeanModule
instanceKlass org/eclipse/sisu/plexus/PlexusBeanSource
instanceKlass org/eclipse/sisu/plexus/PlexusXmlBeanModule
instanceKlass org/eclipse/sisu/plexus/PlexusBeanModule
instanceKlass org/eclipse/sisu/space/URLClassSpace
instanceKlass org/codehaus/plexus/DefaultPlexusContainer$SLF4JLoggerFactoryProvider
instanceKlass com/google/inject/util/Providers$ConstantProvider
instanceKlass com/google/inject/util/Providers
instanceKlass org/codehaus/plexus/personality/plexus/lifecycle/phase/Disposable
instanceKlass org/codehaus/plexus/personality/plexus/lifecycle/phase/Startable
instanceKlass org/codehaus/plexus/personality/plexus/lifecycle/phase/Initializable
instanceKlass org/codehaus/plexus/personality/plexus/lifecycle/phase/Contextualizable
instanceKlass org/codehaus/plexus/logging/LogEnabled
instanceKlass org/eclipse/sisu/bean/PropertyBinding
instanceKlass javax/annotation/PreDestroy
instanceKlass javax/annotation/PostConstruct
instanceKlass org/eclipse/sisu/bean/LifecycleBuilder
instanceKlass org/eclipse/sisu/bean/BeanScheduler$1
instanceKlass com/google/inject/spi/DefaultBindingScopingVisitor
instanceKlass com/google/inject/spi/BindingScopingVisitor
instanceKlass org/eclipse/sisu/bean/BeanScheduler$CycleActivator
instanceKlass com/google/inject/PrivateBinder
instanceKlass com/google/inject/spi/ModuleAnnotatedMethodScanner
instanceKlass com/google/inject/binder/AnnotatedConstantBindingBuilder
instanceKlass com/google/inject/spi/TypeListener
instanceKlass org/aopalliance/intercept/MethodInterceptor
instanceKlass org/aopalliance/intercept/Interceptor
instanceKlass org/aopalliance/aop/Advice
instanceKlass com/google/inject/MembersInjector
instanceKlass com/google/inject/Scope
instanceKlass com/google/inject/spi/Message
instanceKlass com/google/inject/spi/Element
instanceKlass com/google/inject/spi/Dependency
instanceKlass com/google/inject/Key
instanceKlass com/google/inject/binder/AnnotatedBindingBuilder
instanceKlass com/google/inject/binder/LinkedBindingBuilder
instanceKlass com/google/inject/binder/ScopedBindingBuilder
instanceKlass com/google/inject/TypeLiteral
instanceKlass com/google/inject/spi/ProvisionListener
instanceKlass com/google/inject/Binder
instanceKlass org/eclipse/sisu/bean/BeanScheduler
instanceKlass org/eclipse/sisu/plexus/DefaultPlexusBeanLocator
instanceKlass org/eclipse/sisu/plexus/RealmManager
instanceKlass org/codehaus/plexus/context/ContextMapAdapter
instanceKlass org/codehaus/plexus/context/DefaultContext
instanceKlass org/codehaus/plexus/logging/AbstractLogger
instanceKlass org/codehaus/plexus/logging/AbstractLoggerManager
instanceKlass java/util/Date
instanceKlass java/text/DigitList
instanceKlass java/text/FieldPosition
instanceKlass java/lang/StringUTF16$CharsSpliterator
instanceKlass java/util/stream/Sink$ChainedInt
instanceKlass  @bci java/util/stream/FindOps$FindSink$OfInt <clinit> ()V 43 <appendix> argL0 ; # java/util/stream/FindOps$FindSink$OfInt$$Lambda+0x800000049
instanceKlass  @bci java/util/stream/FindOps$FindSink$OfInt <clinit> ()V 38 <appendix> argL0 ; # java/util/stream/FindOps$FindSink$OfInt$$Lambda+0x80000004b
instanceKlass  @bci java/util/stream/FindOps$FindSink$OfInt <clinit> ()V 16 <appendix> argL0 ; # java/util/stream/FindOps$FindSink$OfInt$$Lambda+0x80000004a
instanceKlass  @bci java/util/stream/FindOps$FindSink$OfInt <clinit> ()V 11 <appendix> argL0 ; # java/util/stream/FindOps$FindSink$OfInt$$Lambda+0x80000004c
instanceKlass java/util/function/Predicate
instanceKlass java/util/OptionalInt
instanceKlass java/util/stream/FindOps$FindOp
instanceKlass java/util/stream/Sink$OfInt
instanceKlass java/util/function/IntConsumer
instanceKlass java/util/stream/FindOps$FindSink
instanceKlass java/util/stream/FindOps
instanceKlass  @bci java/text/DecimalFormatSymbols findNonFormatChar (Ljava/lang/String;C)C 4 <appendix> argL0 ; # java/text/DecimalFormatSymbols$$Lambda+0x80000000d
instanceKlass java/util/function/IntPredicate
instanceKlass java/util/stream/IntStream
instanceKlass java/lang/StringLatin1$CharsSpliterator
instanceKlass  @bci sun/util/locale/provider/JRELocaleProviderAdapter getDecimalFormatSymbolsProvider ()Ljava/text/spi/DecimalFormatSymbolsProvider; 8 <appendix> member <vmtarget> ; # sun/util/locale/provider/JRELocaleProviderAdapter$$Lambda+0x80000006b
instanceKlass java/text/DecimalFormatSymbols
instanceKlass  @bci sun/util/locale/provider/JRELocaleProviderAdapter getNumberFormatProvider ()Ljava/text/spi/NumberFormatProvider; 8 <appendix> member <vmtarget> ; # sun/util/locale/provider/JRELocaleProviderAdapter$$Lambda+0x80000006c
instanceKlass  @bci sun/util/locale/provider/JRELocaleProviderAdapter getDateFormatSymbolsProvider ()Ljava/text/spi/DateFormatSymbolsProvider; 8 <appendix> member <vmtarget> ; # sun/util/locale/provider/JRELocaleProviderAdapter$$Lambda+0x80000006a
instanceKlass java/text/DateFormatSymbols
instanceKlass sun/util/calendar/CalendarUtils
instanceKlass sun/util/calendar/CalendarDate
instanceKlass sun/util/resources/Bundles$CacheKeyReference
instanceKlass  @bci java/util/ResourceBundle$ResourceBundleProviderHelper newResourceBundle (Ljava/lang/Class;)Ljava/util/ResourceBundle; 22 <appendix> member <vmtarget> ; # java/util/ResourceBundle$ResourceBundleProviderHelper$$Lambda+0x800000012
instanceKlass java/util/ResourceBundle$ResourceBundleProviderHelper
instanceKlass  @bci sun/util/cldr/CLDRLocaleProviderAdapter applyAliases (Ljava/util/Locale;)Ljava/util/Locale; 4 <appendix> argL0 ; # sun/util/cldr/CLDRLocaleProviderAdapter$$Lambda+0x800000064
instanceKlass sun/util/resources/Bundles$CacheKey
instanceKlass java/util/ResourceBundle$1
instanceKlass jdk/internal/access/JavaUtilResourceBundleAccess
instanceKlass sun/util/resources/Bundles
instanceKlass sun/util/resources/LocaleData$LocaleDataStrategy
instanceKlass sun/util/resources/Bundles$Strategy
instanceKlass sun/util/resources/LocaleData$1
instanceKlass sun/util/resources/LocaleData
instanceKlass sun/util/locale/provider/LocaleResources
instanceKlass java/util/stream/Sink$ChainedReference
instanceKlass java/util/stream/Node$Builder
instanceKlass java/util/stream/Nodes$ArrayNode
instanceKlass java/util/stream/Node$OfDouble
instanceKlass java/util/stream/Node$OfLong
instanceKlass java/util/stream/Node$OfInt
instanceKlass java/util/stream/Node$OfPrimitive
instanceKlass java/util/stream/Nodes$EmptyNode
instanceKlass java/util/stream/Node
instanceKlass java/util/stream/Nodes
instanceKlass  @bci sun/util/locale/provider/LocaleProviderAdapter toLocaleArray (Ljava/util/Set;)[Ljava/util/Locale; 16 <appendix> argL0 ; # sun/util/locale/provider/LocaleProviderAdapter$$Lambda+0x80000006e
instanceKlass java/util/function/IntFunction
instanceKlass  @bci sun/util/locale/provider/LocaleProviderAdapter toLocaleArray (Ljava/util/Set;)[Ljava/util/Locale; 6 <appendix> argL0 ; # sun/util/locale/provider/LocaleProviderAdapter$$Lambda+0x80000006d
instanceKlass java/util/Spliterators$IteratorSpliterator
instanceKlass java/util/Spliterator$OfDouble
instanceKlass java/util/Spliterator$OfLong
instanceKlass java/util/Spliterator$OfInt
instanceKlass java/util/Spliterator$OfPrimitive
instanceKlass java/util/Spliterators$EmptySpliterator
instanceKlass java/util/Spliterators
instanceKlass  @bci sun/util/cldr/CLDRLocaleProviderAdapter getCalendarDataProvider ()Ljava/util/spi/CalendarDataProvider; 8 <appendix> member <vmtarget> ; # sun/util/cldr/CLDRLocaleProviderAdapter$$Lambda+0x800000067
instanceKlass java/util/ResourceBundle
instanceKlass java/util/ResourceBundle$Control
instanceKlass sun/util/locale/provider/CalendarDataUtility$CalendarWeekParameterGetter
instanceKlass sun/util/locale/provider/LocaleServiceProviderPool$LocalizedObjectGetter
instanceKlass sun/util/locale/provider/LocaleServiceProviderPool
instanceKlass java/util/Locale$Builder
instanceKlass sun/util/locale/provider/CalendarDataUtility
instanceKlass sun/util/calendar/CalendarSystem$GregorianHolder
instanceKlass sun/util/calendar/CalendarSystem
instanceKlass java/util/Calendar$Builder
instanceKlass sun/util/locale/provider/AvailableLanguageTags
instanceKlass  @bci sun/util/locale/provider/JRELocaleProviderAdapter getCalendarProvider ()Lsun/util/spi/CalendarProvider; 8 <appendix> member <vmtarget> ; # sun/util/locale/provider/JRELocaleProviderAdapter$$Lambda+0x800000068
instanceKlass java/util/ServiceLoader$ProviderImpl
instanceKlass java/util/ServiceLoader$Provider
instanceKlass java/util/ServiceLoader$1
instanceKlass sun/util/resources/cldr/provider/CLDRLocaleDataMetaInfo
instanceKlass jdk/internal/module/ModulePatcher$PatchedModuleReader
instanceKlass java/util/ServiceLoader$3
instanceKlass java/util/ServiceLoader$2
instanceKlass java/util/ServiceLoader$LazyClassPathLookupIterator
instanceKlass java/util/concurrent/CopyOnWriteArrayList$COWIterator
instanceKlass java/util/ServiceLoader$ModuleServicesLookupIterator
instanceKlass java/util/ServiceLoader
instanceKlass  @bci sun/util/cldr/CLDRLocaleProviderAdapter <init> ()V 4 <appendix> argL0 ; # sun/util/cldr/CLDRLocaleProviderAdapter$$Lambda+0x800000066
instanceKlass sun/util/locale/InternalLocaleBuilder$CaseInsensitiveChar
instanceKlass sun/util/locale/InternalLocaleBuilder
instanceKlass sun/util/locale/StringTokenIterator
instanceKlass sun/util/locale/ParseStatus
instanceKlass sun/util/locale/LanguageTag
instanceKlass sun/util/cldr/CLDRBaseLocaleDataMetaInfo
instanceKlass sun/util/locale/provider/LocaleDataMetaInfo
instanceKlass sun/util/locale/provider/ResourceBundleBasedAdapter
instanceKlass sun/util/locale/provider/LocaleProviderAdapter
instanceKlass java/util/spi/LocaleServiceProvider
instanceKlass sun/util/calendar/ZoneInfoFile$ZoneOffsetTransitionRule
instanceKlass java/lang/invoke/VarHandle$AccessDescriptor
instanceKlass java/lang/invoke/VarForm
instanceKlass java/lang/invoke/VarHandleGuards
instanceKlass java/lang/ClassValue$Version
instanceKlass java/lang/ClassValue$Identity
instanceKlass java/lang/ClassValue
instanceKlass java/lang/invoke/VarHandles
instanceKlass jdk/internal/util/ByteArray
instanceKlass sun/util/calendar/ZoneInfoFile$1
instanceKlass sun/util/calendar/ZoneInfoFile
instanceKlass java/util/TimeZone
instanceKlass java/util/Calendar
instanceKlass java/text/AttributedCharacterIterator$Attribute
instanceKlass com/google/inject/matcher/AbstractMatcher
instanceKlass com/google/inject/matcher/Matcher
instanceKlass jdk/internal/foreign/MemorySessionImpl
instanceKlass java/lang/foreign/MemorySegment$Scope
instanceKlass com/google/inject/spi/TypeConverter
instanceKlass org/codehaus/plexus/DefaultPlexusContainer$LoggerProvider
instanceKlass org/codehaus/plexus/DefaultPlexusContainer$DefaultsModule
instanceKlass org/codehaus/plexus/DefaultPlexusContainer$ContainerModule
instanceKlass java/util/concurrent/locks/ReentrantReadWriteLock$WriteLock
instanceKlass java/util/concurrent/locks/ReentrantReadWriteLock$ReadLock
instanceKlass java/util/concurrent/locks/ReentrantReadWriteLock
instanceKlass java/util/concurrent/locks/ReadWriteLock
instanceKlass org/eclipse/sisu/inject/ImplicitBindings
instanceKlass org/eclipse/sisu/inject/MildValues$InverseMapping
instanceKlass org/eclipse/sisu/inject/MildValues
instanceKlass org/eclipse/sisu/inject/Weak
instanceKlass sun/reflect/misc/ReflectUtil
instanceKlass java/util/concurrent/atomic/AtomicReferenceFieldUpdater$AtomicReferenceFieldUpdaterImpl$1
instanceKlass java/util/concurrent/atomic/AtomicReferenceFieldUpdater
instanceKlass org/eclipse/sisu/inject/RankedSequence$Content
instanceKlass org/eclipse/sisu/inject/RankedSequence
instanceKlass org/eclipse/sisu/inject/BindingSubscriber
instanceKlass org/eclipse/sisu/inject/DefaultBeanLocator
instanceKlass org/eclipse/sisu/inject/DeferredClass
instanceKlass org/codehaus/plexus/DefaultPlexusContainer$LoggerManagerProvider
instanceKlass org/eclipse/sisu/inject/DeferredProvider
instanceKlass com/google/inject/Provider
instanceKlass com/google/inject/AbstractModule
instanceKlass org/codehaus/plexus/context/Context
instanceKlass org/eclipse/sisu/inject/BindingPublisher
instanceKlass org/eclipse/sisu/inject/RankingFunction
instanceKlass org/eclipse/sisu/space/ClassSpace
instanceKlass javax/inject/Provider
instanceKlass org/eclipse/sisu/bean/BeanManager
instanceKlass org/eclipse/sisu/plexus/PlexusBeanLocator
instanceKlass org/codehaus/plexus/classworlds/ClassWorldListener
instanceKlass com/google/inject/Module
instanceKlass org/eclipse/sisu/inject/MutableBeanLocator
instanceKlass org/eclipse/sisu/inject/BeanLocator
instanceKlass org/codehaus/plexus/DefaultPlexusContainer
instanceKlass org/codehaus/plexus/MutablePlexusContainer
instanceKlass java/util/stream/ReduceOps$AccumulatingSink
instanceKlass java/util/stream/TerminalSink
instanceKlass java/util/stream/Sink
instanceKlass java/util/stream/ReduceOps$Box
instanceKlass java/util/stream/ReduceOps$ReduceOp
instanceKlass java/util/stream/TerminalOp
instanceKlass java/util/stream/ReduceOps
instanceKlass  @bci java/util/function/Function andThen (Ljava/util/function/Function;)Ljava/util/function/Function; 7 <appendix> member <vmtarget> ; # java/util/function/Function$$Lambda+0x000002da8105a4b0
instanceKlass  @cpi java/util/function/Function 58 form vmentry <vmtarget> ; # java/lang/invoke/LambdaForm$DMH+0x000002da81020400
instanceKlass  @bci org/apache/maven/extension/internal/CoreExports <init> (Lorg/codehaus/plexus/classworlds/realm/ClassRealm;Ljava/util/Set;Ljava/util/Set;)V 38 <appendix> argL0 ; # org/apache/maven/extension/internal/CoreExports$$Lambda+0x000002da8101d830
instanceKlass  @bci java/util/stream/Collectors castingIdentity ()Ljava/util/function/Function; 0 <appendix> argL0 ; # java/util/stream/Collectors$$Lambda+0x800000043
instanceKlass  @bci java/util/stream/Collectors uniqKeysMapMerger ()Ljava/util/function/BinaryOperator; 0 <appendix> argL0 ; # java/util/stream/Collectors$$Lambda+0x000002da8105a278
instanceKlass java/util/function/BinaryOperator
instanceKlass  @bci java/util/stream/Collectors uniqKeysMapAccumulator (Ljava/util/function/Function;Ljava/util/function/Function;)Ljava/util/function/BiConsumer; 2 <appendix> member <vmtarget> ; # java/util/stream/Collectors$$Lambda+0x000002da8105a050
instanceKlass  @bci java/util/stream/Collectors toMap (Ljava/util/function/Function;Ljava/util/function/Function;)Ljava/util/stream/Collector; 4 <appendix> argL0 ; # java/util/stream/Collectors$$Lambda+0x000002da81059e40
instanceKlass java/util/stream/Collector
instanceKlass java/util/stream/Collectors
instanceKlass  @bci org/apache/maven/extension/internal/CoreExports <init> (Lorg/codehaus/plexus/classworlds/realm/ClassRealm;Ljava/util/Set;Ljava/util/Set;)V 30 <appendix> member <vmtarget> ; # org/apache/maven/extension/internal/CoreExports$$Lambda+0x000002da8101d5f8
instanceKlass  @bci java/util/function/Function identity ()Ljava/util/function/Function; 0 <appendix> argL0 ; # java/util/function/Function$$Lambda+0x000002da81059a28
# instanceKlass java/lang/invoke/LambdaForm$MH+0x000002da81020000
instanceKlass sun/reflect/annotation/AnnotationParser
instanceKlass java/lang/Class$3
instanceKlass java/util/EnumMap$1
instanceKlass java/util/stream/StreamOpFlag$MaskBuilder
instanceKlass java/util/stream/Stream
instanceKlass java/util/stream/BaseStream
instanceKlass java/util/stream/PipelineHelper
instanceKlass java/util/stream/StreamSupport
instanceKlass java/util/Spliterator
instanceKlass java/util/HashMap$HashMapSpliterator
instanceKlass org/apache/maven/extension/internal/CoreExports
instanceKlass java/util/Collections$UnmodifiableCollection$1
instanceKlass org/codehaus/plexus/DefaultContainerConfiguration
instanceKlass org/codehaus/plexus/ContainerConfiguration
instanceKlass org/codehaus/plexus/util/BaseIOUtil
instanceKlass org/codehaus/plexus/util/xml/XMLWriter
instanceKlass org/codehaus/plexus/util/xml/Xpp3Dom
instanceKlass org/codehaus/plexus/util/xml/pull/MXParser
instanceKlass org/codehaus/plexus/util/xml/pull/XmlPullParser
instanceKlass org/codehaus/plexus/util/xml/Xpp3DomBuilder
instanceKlass java/util/regex/ASCII
instanceKlass  @bci java/util/regex/CharPredicates ASCII_SPACE ()Ljava/util/regex/Pattern$BmpCharPredicate; 0 <appendix> argL0 ; # java/util/regex/CharPredicates$$Lambda+0x800000027
instanceKlass  @bci java/util/regex/Pattern DOT ()Ljava/util/regex/Pattern$CharPredicate; 0 <appendix> argL0 ; # java/util/regex/Pattern$$Lambda+0x000002da81057f00
instanceKlass  @bci java/util/regex/Pattern union (Ljava/util/regex/Pattern$CharPredicate;Ljava/util/regex/Pattern$CharPredicate;Z)Ljava/util/regex/Pattern$CharPredicate; 14 <appendix> member <vmtarget> ; # java/util/regex/Pattern$$Lambda+0x000002da81057868
instanceKlass org/codehaus/plexus/util/ReaderFactory
instanceKlass org/apache/maven/project/ExtensionDescriptor
instanceKlass org/apache/maven/project/ExtensionDescriptorBuilder
instanceKlass org/apache/maven/extension/internal/CoreExtensionEntry
instanceKlass org/codehaus/plexus/logging/Logger
instanceKlass org/apache/maven/cli/logging/Slf4jLoggerManager
instanceKlass org/slf4j/impl/MavenSlf4jSimpleFriend
instanceKlass org/slf4j/MavenSlf4jFriend
instanceKlass  @bci org/apache/commons/cli/CommandLine getOptionValue (Ljava/lang/String;Ljava/lang/String;)Ljava/lang/String; 7 <appendix> member <vmtarget> ; # org/apache/commons/cli/CommandLine$$Lambda+0x000002da81019cd0
instanceKlass org/apache/maven/cli/logging/BaseSlf4jConfiguration
instanceKlass org/codehaus/plexus/util/PropertyUtils
instanceKlass org/apache/maven/cli/logging/Slf4jConfiguration
instanceKlass org/apache/maven/cli/logging/Slf4jConfigurationFactory
instanceKlass org/slf4j/impl/OutputChoice
instanceKlass org/slf4j/impl/SimpleLoggerConfiguration$1
instanceKlass java/text/Format
instanceKlass org/slf4j/impl/SimpleLoggerConfiguration
instanceKlass org/slf4j/helpers/NamedLoggerBase
instanceKlass org/slf4j/impl/SimpleLoggerFactory
instanceKlass org/slf4j/impl/StaticLoggerBinder
instanceKlass org/slf4j/spi/LoggerFactoryBinder
instanceKlass java/util/Collections$3
instanceKlass java/net/URLClassLoader$3$1
instanceKlass java/net/URLClassLoader$3
instanceKlass jdk/internal/loader/URLClassPath$1
instanceKlass java/lang/CompoundEnumeration
instanceKlass jdk/internal/loader/BuiltinClassLoader$1
instanceKlass java/util/Collections$EmptyEnumeration
instanceKlass org/slf4j/helpers/Util
instanceKlass org/slf4j/helpers/NOPLoggerFactory
instanceKlass java/util/concurrent/LinkedBlockingQueue$Node
instanceKlass java/util/concurrent/BlockingQueue
instanceKlass org/slf4j/helpers/SubstituteLoggerFactory
instanceKlass org/slf4j/ILoggerFactory
instanceKlass org/slf4j/event/LoggingEvent
instanceKlass org/slf4j/LoggerFactory
instanceKlass org/codehaus/plexus/interpolation/SimpleRecursionInterceptor
instanceKlass java/util/LinkedList$ListItr
instanceKlass java/util/IdentityHashMap$IdentityHashMapIterator
instanceKlass org/codehaus/plexus/util/StringUtils
instanceKlass org/apache/maven/cli/CLIReportingUtils
instanceKlass  @bci org/apache/maven/cli/MavenCli populateProperties (Lorg/apache/maven/cli/CliRequest;Ljava/util/Properties;Ljava/util/Properties;)V 236 <appendix> argL0 ; # org/apache/maven/cli/MavenCli$$Lambda+0x000002da810178a8
instanceKlass  @cpi com/google/inject/internal/aop/ClassBuilding 362 form vmentry <vmtarget> ; # java/lang/invoke/LambdaForm$DMH+0x000002da81011400
instanceKlass java/util/function/BiConsumer
instanceKlass org/codehaus/plexus/interpolation/AbstractValueSource
instanceKlass org/codehaus/plexus/interpolation/RecursionInterceptor
instanceKlass org/codehaus/plexus/interpolation/StringSearchInterpolator
instanceKlass org/codehaus/plexus/interpolation/Interpolator
instanceKlass org/codehaus/plexus/interpolation/BasicInterpolator
instanceKlass org/apache/maven/properties/internal/SystemProperties
instanceKlass java/util/Collections$SynchronizedCollection
instanceKlass java/util/Properties$EntrySet
instanceKlass java/util/Collections$UnmodifiableMap$UnmodifiableEntrySet$UnmodifiableEntry
instanceKlass java/util/Collections$UnmodifiableMap$UnmodifiableEntrySet$1
instanceKlass org/codehaus/plexus/util/Os
instanceKlass org/apache/maven/properties/internal/EnvironmentUtils
instanceKlass java/util/LinkedList$Node
instanceKlass java/util/AbstractList$Itr
instanceKlass org/apache/commons/cli/Util
instanceKlass  @bci org/apache/commons/cli/CommandLine$Builder <clinit> ()V 0 <appendix> argL0 ; # org/apache/commons/cli/CommandLine$Builder$$Lambda+0x000002da81015c60
instanceKlass  @cpi org/apache/commons/cli/CommandLine$Builder 102 form vmentry <vmtarget> ; # java/lang/invoke/LambdaForm$DMH+0x000002da81011000
instanceKlass  @cpi com/google/inject/spi/BindingSourceRestriction 105 form vmentry <vmtarget> ; # java/lang/invoke/LambdaForm$DMH+0x000002da81010c00
instanceKlass java/util/function/Consumer
instanceKlass org/apache/commons/cli/CommandLine$Builder
instanceKlass org/apache/commons/cli/CommandLine
instanceKlass java/util/Collections$UnmodifiableCollection
instanceKlass java/util/LinkedHashMap$LinkedHashIterator
instanceKlass org/apache/commons/cli/Parser
instanceKlass org/apache/maven/cli/CleanArgument
instanceKlass org/apache/commons/cli/OptionValidator
instanceKlass org/apache/commons/cli/Option$Builder
instanceKlass org/apache/commons/cli/Option
instanceKlass org/apache/commons/cli/Options
instanceKlass org/apache/commons/cli/CommandLineParser
instanceKlass org/apache/maven/cli/CLIManager
instanceKlass org/apache/maven/cli/logging/Slf4jStdoutLogger
instanceKlass sun/nio/fs/WindowsPath$1
instanceKlass sun/nio/fs/WindowsLinkSupport
instanceKlass org/eclipse/aether/DefaultRepositoryCache
instanceKlass org/apache/maven/project/ProjectBuildingRequest
instanceKlass org/eclipse/aether/RepositoryCache
instanceKlass org/apache/maven/execution/DefaultMavenExecutionRequest
instanceKlass org/apache/maven/execution/MavenExecutionRequest
instanceKlass java/lang/Shutdown$Lock
instanceKlass java/lang/Shutdown
instanceKlass java/lang/ApplicationShutdownHooks$1
instanceKlass java/lang/ApplicationShutdownHooks
instanceKlass java/lang/Thread$ThreadNumbering
instanceKlass  @bci org/fusesource/jansi/AnsiConsole ansiStream (Z)Lorg/fusesource/jansi/AnsiPrintStream; 310 <appendix> member <vmtarget> ; # org/fusesource/jansi/AnsiConsole$$Lambda+0x000002da8100d458
instanceKlass  @bci org/fusesource/jansi/AnsiConsole ansiStream (Z)Lorg/fusesource/jansi/AnsiPrintStream; 299 <adapter> ; # java/lang/invoke/LambdaForm$MH+0x000002da81010800
# instanceKlass java/lang/invoke/LambdaForm$DMH+0x000002da81010400
instanceKlass  @bci org/fusesource/jansi/AnsiConsole ansiStream (Z)Lorg/fusesource/jansi/AnsiPrintStream; 299 <appendix> form vmentry <vmtarget> ; # java/lang/invoke/LambdaForm$DMH+0x000002da81010000
instanceKlass  @bci org/fusesource/jansi/AnsiConsole ansiStream (Z)Lorg/fusesource/jansi/AnsiPrintStream; 299 <appendix> member <vmtarget> ; # org/fusesource/jansi/AnsiConsole$$Lambda+0x000002da8100d240
instanceKlass java/util/regex/IntHashSet
instanceKlass java/util/regex/Matcher
instanceKlass  @cpi org/fusesource/jansi/AnsiConsole 319 form vmentry <vmtarget> ; # java/lang/invoke/LambdaForm$DMH+0x000002da81009c00
instanceKlass java/util/regex/MatchResult
instanceKlass org/fusesource/jansi/io/AnsiOutputStream$IoRunnable
instanceKlass sun/net/www/protocol/jar/JarFileFactory
instanceKlass sun/net/www/protocol/jar/URLJarFile$URLJarFileCloseController
instanceKlass org/fusesource/jansi/AnsiConsole$1
instanceKlass java/net/URLClassLoader$2
instanceKlass org/fusesource/jansi/internal/Kernel32
instanceKlass jdk/internal/loader/NativeLibraries$Unloader
instanceKlass jdk/internal/jimage/ImageLocation
instanceKlass jdk/internal/jimage/decompressor/Decompressor
instanceKlass jdk/internal/jimage/ImageStringsReader
instanceKlass jdk/internal/jimage/ImageStrings
instanceKlass jdk/internal/jimage/ImageHeader
instanceKlass jdk/internal/jimage/NativeImageBuffer$1
instanceKlass jdk/internal/jimage/NativeImageBuffer
instanceKlass jdk/internal/jimage/BasicImageReader$1
instanceKlass jdk/internal/jimage/BasicImageReader
instanceKlass jdk/internal/jimage/ImageReader
instanceKlass jdk/internal/jimage/ImageReaderFactory$1
instanceKlass org/fusesource/jansi/internal/OSInfo
instanceKlass jdk/internal/jimage/ImageReaderFactory
instanceKlass jdk/internal/module/SystemModuleFinders$SystemImage
instanceKlass jdk/internal/module/SystemModuleFinders$SystemModuleReader
instanceKlass java/lang/module/ModuleReader
instanceKlass jdk/internal/loader/BuiltinClassLoader$5
instanceKlass jdk/internal/loader/BuiltinClassLoader$2
instanceKlass jdk/internal/module/Resources
instanceKlass org/fusesource/jansi/internal/JansiLoader$1
instanceKlass  @bci org/fusesource/jansi/internal/JansiLoader initialize ()Z 10 <appendix> argL0 ; # org/fusesource/jansi/internal/JansiLoader$$Lambda+0x000002da8100c1f8
instanceKlass org/fusesource/jansi/internal/JansiLoader
instanceKlass org/fusesource/jansi/internal/CLibrary
instanceKlass java/lang/ProcessEnvironment$CheckedEntry
instanceKlass java/lang/ProcessEnvironment$CheckedEntrySet$1
instanceKlass java/lang/ProcessEnvironment$EntryComparator
instanceKlass java/lang/ProcessEnvironment$NameComparator
instanceKlass org/fusesource/jansi/io/AnsiProcessor
instanceKlass org/fusesource/jansi/io/AnsiOutputStream$WidthSupplier
instanceKlass org/fusesource/jansi/AnsiConsole
instanceKlass  @bci org/fusesource/jansi/Ansi <clinit> ()V 26 <appendix> argL0 ; # org/fusesource/jansi/Ansi$$Lambda+0x000002da81007dd8
instanceKlass java/util/concurrent/Callable
instanceKlass org/fusesource/jansi/Ansi
instanceKlass org/apache/maven/shared/utils/logging/LoggerLevelRenderer
instanceKlass org/apache/maven/shared/utils/logging/MessageBuilder
instanceKlass org/apache/maven/shared/utils/logging/MessageUtils
instanceKlass  @bci jdk/internal/reflect/DirectMethodHandleAccessor invokeImpl (Ljava/lang/Object;[Ljava/lang/Object;)Ljava/lang/Object; 72 <adapter> ; # java/lang/invoke/LambdaForm$MH+0x000002da81009800
# instanceKlass java/lang/invoke/LambdaForm$MH+0x000002da81009400
# instanceKlass java/lang/invoke/LambdaForm$MH+0x000002da81009000
# instanceKlass java/lang/invoke/LambdaForm$MH+0x000002da81008c00
# instanceKlass java/lang/invoke/LambdaForm$MH+0x000002da81008800
instanceKlass sun/invoke/util/ValueConversions$WrapperCache
# instanceKlass java/lang/invoke/LambdaForm$MH+0x000002da81008400
instanceKlass  @cpi org/apache/maven/lifecycle/internal/DefaultMojoExecutionConfigurator 403 form vmentry <vmtarget> ; # java/lang/invoke/LambdaForm$DMH+0x000002da81008000
instanceKlass sun/invoke/util/VerifyAccess$1
instanceKlass jdk/internal/reflect/MethodHandleAccessorFactory$LazyStaticHolder
instanceKlass  @bci java/util/regex/Pattern negate (Ljava/util/regex/Pattern$CharPredicate;)Ljava/util/regex/Pattern$CharPredicate; 1 <appendix> member <vmtarget> ; # java/util/regex/Pattern$$Lambda+0x800000032
instanceKlass  @bci java/util/regex/Pattern Range (II)Ljava/util/regex/Pattern$CharPredicate; 23 <appendix> member <vmtarget> ; # java/util/regex/Pattern$$Lambda+0x80000002b
instanceKlass  @bci java/util/regex/Pattern union (Ljava/util/regex/Pattern$CharPredicate;Ljava/util/regex/Pattern$CharPredicate;Z)Ljava/util/regex/Pattern$CharPredicate; 6 <appendix> member <vmtarget> ; # java/util/regex/Pattern$$Lambda+0x800000033
instanceKlass  @bci java/util/regex/CharPredicates ASCII_DIGIT ()Ljava/util/regex/Pattern$BmpCharPredicate; 0 <appendix> argL0 ; # java/util/regex/CharPredicates$$Lambda+0x800000026
instanceKlass java/util/regex/CharPredicates
instanceKlass java/util/regex/Pattern$BitClass
instanceKlass java/util/regex/Pattern$TreeInfo
instanceKlass  @bci java/util/regex/Pattern Single (I)Ljava/util/regex/Pattern$BmpCharPredicate; 1 <appendix> member <vmtarget> ; # java/util/regex/Pattern$$Lambda+0x80000002a
instanceKlass java/util/regex/Pattern$BmpCharPredicate
instanceKlass java/util/regex/Pattern$CharPredicate
instanceKlass java/util/regex/Pattern$Node
instanceKlass java/util/regex/Pattern
instanceKlass org/apache/maven/cli/CliRequest
instanceKlass org/slf4j/Logger
instanceKlass org/apache/maven/toolchain/building/ToolchainsBuildingRequest
instanceKlass org/apache/maven/building/Source
instanceKlass org/codehaus/plexus/logging/LoggerManager
instanceKlass org/codehaus/plexus/interpolation/ValueSource
instanceKlass org/apache/maven/execution/ExecutionListener
instanceKlass org/eclipse/aether/transfer/TransferListener
instanceKlass org/apache/maven/eventspy/EventSpy$Context
instanceKlass org/codehaus/plexus/PlexusContainer
instanceKlass org/apache/maven/exception/ExceptionHandler
instanceKlass org/apache/maven/cli/MavenCli
instanceKlass  @bci java/io/FilePermissionCollection add (Ljava/security/Permission;)V 68 <appendix> argL0 ; # java/io/FilePermissionCollection$$Lambda+0x000002da8104b9e8
instanceKlass java/security/Security$1
instanceKlass jdk/internal/access/JavaSecurityPropertiesAccess
instanceKlass java/util/concurrent/ConcurrentHashMap$MapEntry
instanceKlass java/io/FileInputStream$1
instanceKlass java/util/Properties$LineReader
instanceKlass  @bci java/security/Security <clinit> ()V 9 <appendix> argL0 ; # java/security/Security$$Lambda+0x80000000b
instanceKlass java/security/Security
instanceKlass sun/security/util/SecurityProperties
instanceKlass sun/security/util/FilePermCompat
instanceKlass java/io/FilePermission$1
instanceKlass jdk/internal/access/JavaIOFilePermissionAccess
instanceKlass sun/net/www/MessageHeader
instanceKlass java/net/URLConnection
instanceKlass java/util/TreeMap$Entry
instanceKlass java/net/URLClassLoader$1
instanceKlass java/util/TreeMap$PrivateEntryIterator
instanceKlass java/util/TimSort
instanceKlass java/util/Arrays$LegacyMergeSort
instanceKlass  @bci org/apache/maven/utils/Os getOsFamily ()Ljava/lang/String; 74 <appendix> form vmentry <vmtarget> ; # java/lang/invoke/LambdaForm$MH+0x000002da81004800
instanceKlass java/lang/invoke/LambdaFormBuffer
instanceKlass java/lang/invoke/LambdaFormEditor$TransformKey
instanceKlass java/lang/invoke/LambdaFormEditor
instanceKlass java/lang/invoke/DelegatingMethodHandle$Holder
instanceKlass java/lang/invoke/DirectMethodHandle$2
instanceKlass java/lang/invoke/ClassSpecializer$Factory
instanceKlass java/lang/invoke/ClassSpecializer$SpeciesData
instanceKlass java/lang/invoke/ClassSpecializer$1
instanceKlass java/lang/invoke/ClassSpecializer
instanceKlass  @bci org/codehaus/plexus/classworlds/launcher/Configurator associateRealms ()V 18 <appendix> argL0 ; # org/codehaus/plexus/classworlds/launcher/Configurator$$Lambda+0x000002da81002ef0
instanceKlass  @cpi com/google/inject/spi/BindingSourceRestriction$PermitMapConstruction 96 form vmentry <vmtarget> ; # java/lang/invoke/LambdaForm$DMH+0x000002da81004400
instanceKlass  @bci org/codehaus/plexus/classworlds/launcher/ConfigurationParser loadGlob (Ljava/lang/String;Z)V 83 <appendix> member <vmtarget> ; # org/codehaus/plexus/classworlds/launcher/ConfigurationParser$$Lambda+0x000002da81002cd8
instanceKlass java/lang/invoke/LambdaProxyClassArchive
instanceKlass java/lang/invoke/InfoFromMemberName
instanceKlass java/lang/invoke/MethodHandleInfo
instanceKlass jdk/internal/org/objectweb/asm/ConstantDynamic
instanceKlass jdk/internal/org/objectweb/asm/Handle
instanceKlass sun/security/action/GetBooleanAction
instanceKlass java/lang/invoke/AbstractValidatingLambdaMetafactory
instanceKlass java/lang/invoke/MethodHandleImpl$1
instanceKlass jdk/internal/access/JavaLangInvokeAccess
instanceKlass java/lang/invoke/Invokers$Holder
instanceKlass java/lang/invoke/BootstrapMethodInvoker
instanceKlass  @cpi org/codehaus/plexus/classworlds/launcher/ConfigurationParser 322 form vmentry <vmtarget> ; # java/lang/invoke/LambdaForm$DMH+0x000002da81004000
instanceKlass java/lang/invoke/MethodHandles$Lookup$ClassDefiner
instanceKlass java/lang/invoke/MethodHandles$Lookup$ClassFile
instanceKlass jdk/internal/org/objectweb/asm/Handler
instanceKlass jdk/internal/org/objectweb/asm/Attribute
instanceKlass jdk/internal/org/objectweb/asm/FieldVisitor
instanceKlass sun/invoke/empty/Empty
instanceKlass sun/invoke/util/VerifyType
instanceKlass java/lang/invoke/InvokerBytecodeGenerator$ClassData
instanceKlass jdk/internal/org/objectweb/asm/AnnotationVisitor
instanceKlass jdk/internal/org/objectweb/asm/Frame
instanceKlass jdk/internal/org/objectweb/asm/Label
instanceKlass jdk/internal/org/objectweb/asm/Type
instanceKlass jdk/internal/org/objectweb/asm/MethodVisitor
instanceKlass sun/invoke/util/BytecodeDescriptor
instanceKlass jdk/internal/org/objectweb/asm/ByteVector
instanceKlass jdk/internal/org/objectweb/asm/Symbol
instanceKlass jdk/internal/org/objectweb/asm/SymbolTable
instanceKlass jdk/internal/org/objectweb/asm/ClassVisitor
instanceKlass java/io/FilenameFilter
instanceKlass java/lang/invoke/InvokerBytecodeGenerator$1
instanceKlass java/lang/invoke/InvokerBytecodeGenerator
instanceKlass java/lang/invoke/LambdaForm$Holder
instanceKlass java/lang/invoke/LambdaForm$Name
instanceKlass java/lang/invoke/Invokers
instanceKlass java/lang/invoke/MethodHandleImpl
instanceKlass sun/invoke/util/ValueConversions
instanceKlass java/lang/invoke/DirectMethodHandle$Holder
instanceKlass java/lang/invoke/LambdaForm$NamedFunction
instanceKlass sun/invoke/util/Wrapper$Format
instanceKlass java/lang/invoke/MethodTypeForm
instanceKlass java/lang/invoke/MethodType$ConcurrentWeakInternSet
instanceKlass java/lang/invoke/LambdaMetafactory
instanceKlass java/util/ArrayList$Itr
instanceKlass org/codehaus/plexus/classworlds/strategy/AbstractStrategy
instanceKlass org/codehaus/plexus/classworlds/strategy/Strategy
instanceKlass org/codehaus/plexus/classworlds/strategy/StrategyFactory
instanceKlass java/util/NavigableMap
instanceKlass java/util/SortedMap
instanceKlass java/util/NavigableSet
instanceKlass java/util/SortedSet
instanceKlass java/util/SequencedSet
instanceKlass sun/nio/ch/IOStatus
instanceKlass sun/nio/ch/Util$BufferCache
instanceKlass sun/nio/ch/Util
instanceKlass sun/nio/ch/NativeThread
instanceKlass java/io/Reader
instanceKlass org/codehaus/plexus/classworlds/launcher/ConfigurationParser
instanceKlass org/codehaus/plexus/classworlds/launcher/Configurator
instanceKlass org/codehaus/plexus/classworlds/launcher/ConfigurationHandler
instanceKlass java/nio/channels/NetworkChannel
instanceKlass sun/nio/ch/SelChImpl
instanceKlass sun/nio/ch/Streams
instanceKlass java/nio/channels/Channels
instanceKlass sun/nio/ch/FileChannelImpl$Closer
instanceKlass sun/nio/ch/NativeThreadSet
instanceKlass sun/nio/ch/IOUtil
instanceKlass sun/nio/ch/NativeDispatcher
instanceKlass java/nio/channels/spi/AbstractInterruptibleChannel
instanceKlass java/nio/channels/InterruptibleChannel
instanceKlass java/nio/channels/ScatteringByteChannel
instanceKlass java/nio/channels/GatheringByteChannel
instanceKlass java/nio/channels/SeekableByteChannel
instanceKlass java/nio/channels/ByteChannel
instanceKlass java/nio/channels/WritableByteChannel
instanceKlass java/nio/channels/ReadableByteChannel
instanceKlass java/nio/channels/Channel
instanceKlass java/util/Collections$EmptyIterator
instanceKlass sun/nio/fs/WindowsChannelFactory$Flags
instanceKlass sun/nio/fs/WindowsChannelFactory$1
instanceKlass sun/nio/fs/WindowsChannelFactory
instanceKlass sun/nio/fs/WindowsSecurityDescriptor
instanceKlass java/nio/file/attribute/FileAttribute
instanceKlass java/net/URI$Parser
instanceKlass java/nio/file/FileSystems$DefaultFileSystemHolder$1
instanceKlass java/nio/file/FileSystems$DefaultFileSystemHolder
instanceKlass java/nio/file/FileSystems
instanceKlass java/nio/file/Paths
instanceKlass java/lang/Void
instanceKlass java/lang/PublicMethods$Key
instanceKlass java/lang/PublicMethods$MethodList
instanceKlass org/codehaus/plexus/classworlds/ClassWorld
instanceKlass jdk/internal/misc/PreviewFeatures
instanceKlass jdk/internal/misc/MainMethodFinder
instanceKlass org/codehaus/plexus/classworlds/launcher/Launcher
instanceKlass java/security/SecureClassLoader$DebugHolder
instanceKlass java/security/Permission
instanceKlass java/security/Guard
instanceKlass java/security/PermissionCollection
instanceKlass java/security/SecureClassLoader$1
instanceKlass java/util/zip/Checksum$1
instanceKlass java/util/zip/CRC32
instanceKlass java/util/zip/Checksum
instanceKlass sun/nio/ByteBuffered
instanceKlass java/lang/Package$VersionInfo
instanceKlass java/lang/NamedPackage
instanceKlass java/lang/reflect/Array
instanceKlass java/util/jar/Attributes$Name
instanceKlass java/util/SequencedMap
instanceKlass java/util/jar/Attributes
instanceKlass jdk/internal/loader/Resource
instanceKlass sun/security/action/GetIntegerAction
instanceKlass sun/security/util/Debug
instanceKlass sun/security/util/SignatureFileVerifier
instanceKlass java/util/zip/ZipFile$InflaterCleanupAction
instanceKlass java/util/zip/Inflater$InflaterZStreamRef
instanceKlass java/util/zip/Inflater
instanceKlass java/util/zip/ZipEntry
instanceKlass java/util/zip/ZipFile$2
instanceKlass java/nio/Bits$1
instanceKlass jdk/internal/misc/VM$BufferPool
instanceKlass java/nio/Bits
instanceKlass sun/nio/ch/DirectBuffer
instanceKlass jdk/internal/perf/PerfCounter$CoreCounters
instanceKlass jdk/internal/perf/Perf
instanceKlass jdk/internal/perf/Perf$GetPerfAction
instanceKlass jdk/internal/perf/PerfCounter
instanceKlass sun/util/locale/LocaleObjectCache
instanceKlass sun/util/locale/BaseLocale$Key
instanceKlass sun/util/locale/LocaleUtils
instanceKlass sun/util/locale/BaseLocale
instanceKlass java/util/Locale
instanceKlass java/nio/file/attribute/FileTime
instanceKlass java/util/zip/ZipUtils
instanceKlass java/util/zip/ZipFile$Source$End
instanceKlass java/io/RandomAccessFile$2
instanceKlass jdk/internal/access/JavaIORandomAccessFileAccess
instanceKlass java/io/RandomAccessFile
instanceKlass java/io/DataInput
instanceKlass java/io/DataOutput
instanceKlass sun/nio/fs/WindowsNativeDispatcher$CompletionStatus
instanceKlass sun/nio/fs/WindowsNativeDispatcher$AclInformation
instanceKlass sun/nio/fs/WindowsNativeDispatcher$Account
instanceKlass sun/nio/fs/WindowsNativeDispatcher$DiskFreeSpace
instanceKlass sun/nio/fs/WindowsNativeDispatcher$VolumeInformation
instanceKlass sun/nio/fs/WindowsNativeDispatcher$FirstStream
instanceKlass sun/nio/fs/WindowsNativeDispatcher$FirstFile
instanceKlass java/util/Enumeration
instanceKlass java/util/concurrent/ConcurrentHashMap$Traverser
instanceKlass jdk/internal/loader/NativeLibraries$3
instanceKlass jdk/internal/loader/NativeLibrary
instanceKlass java/util/ArrayDeque$DeqIterator
instanceKlass jdk/internal/loader/NativeLibraries$NativeLibraryContext$1
instanceKlass jdk/internal/loader/NativeLibraries$NativeLibraryContext
instanceKlass jdk/internal/loader/NativeLibraries$2
instanceKlass jdk/internal/loader/NativeLibraries$1
instanceKlass jdk/internal/loader/NativeLibraries$LibraryPaths
instanceKlass sun/nio/fs/WindowsNativeDispatcher
instanceKlass sun/nio/fs/NativeBuffer$Deallocator
instanceKlass sun/nio/fs/NativeBuffer
instanceKlass java/lang/ThreadLocal$ThreadLocalMap
instanceKlass java/lang/ThreadLocal
instanceKlass sun/nio/fs/NativeBuffers
instanceKlass sun/nio/fs/WindowsFileAttributes
instanceKlass java/nio/file/attribute/DosFileAttributes
instanceKlass sun/nio/fs/AbstractBasicFileAttributeView
instanceKlass sun/nio/fs/DynamicFileAttributeView
instanceKlass sun/nio/fs/WindowsFileAttributeViews
instanceKlass sun/nio/fs/Util
instanceKlass java/nio/file/attribute/BasicFileAttributeView
instanceKlass java/nio/file/attribute/FileAttributeView
instanceKlass java/nio/file/attribute/AttributeView
instanceKlass java/nio/file/Files
instanceKlass java/nio/file/CopyOption
instanceKlass java/nio/file/attribute/BasicFileAttributes
instanceKlass java/util/concurrent/ForkJoinPool$ManagedBlocker
instanceKlass java/util/concurrent/locks/AbstractQueuedSynchronizer$Node
instanceKlass sun/nio/fs/WindowsPath
instanceKlass java/nio/file/Path
instanceKlass java/nio/file/Watchable
instanceKlass java/util/zip/ZipFile$Source$Key
instanceKlass sun/nio/fs/WindowsPathParser$Result
instanceKlass sun/nio/fs/WindowsPathParser
instanceKlass java/nio/file/FileSystem
instanceKlass java/nio/file/OpenOption
instanceKlass java/nio/file/spi/FileSystemProvider
instanceKlass sun/nio/fs/DefaultFileSystemProvider
instanceKlass java/util/zip/ZipFile$Source
instanceKlass java/lang/ref/Cleaner$Cleanable
instanceKlass jdk/internal/ref/CleanerImpl
instanceKlass java/lang/ref/Cleaner$1
instanceKlass java/lang/ref/Cleaner
instanceKlass jdk/internal/ref/CleanerFactory$1
instanceKlass java/util/concurrent/ThreadFactory
instanceKlass jdk/internal/ref/CleanerFactory
instanceKlass java/util/zip/ZipCoder
instanceKlass java/util/zip/ZipFile$CleanableResource
instanceKlass java/lang/Runtime$Version
instanceKlass java/util/jar/JavaUtilJarAccessImpl
instanceKlass jdk/internal/access/JavaUtilJarAccess
instanceKlass java/nio/charset/CoderResult
instanceKlass java/lang/Readable
instanceKlass jdk/internal/loader/FileURLMapper
instanceKlass jdk/internal/loader/URLClassPath$JarLoader$1
instanceKlass java/util/zip/ZipFile$1
instanceKlass jdk/internal/access/JavaUtilZipFileAccess
instanceKlass java/util/zip/ZipFile
instanceKlass java/util/zip/ZipConstants
instanceKlass jdk/internal/loader/URLClassPath$Loader
instanceKlass jdk/internal/loader/URLClassPath$3
instanceKlass java/security/PrivilegedExceptionAction
instanceKlass sun/net/util/URLUtil
instanceKlass java/lang/StringCoding
instanceKlass sun/nio/cs/MS1252$Holder
instanceKlass sun/nio/cs/ArrayDecoder
instanceKlass java/nio/charset/CharsetDecoder
instanceKlass sun/launcher/LauncherHelper
instanceKlass jdk/internal/vm/PostVMInitHook$1
instanceKlass jdk/internal/util/EnvUtils
instanceKlass jdk/internal/vm/PostVMInitHook$2
instanceKlass jdk/internal/vm/PostVMInitHook
instanceKlass java/lang/invoke/StringConcatFactory
instanceKlass java/lang/ModuleLayer$Controller
instanceKlass java/util/concurrent/CopyOnWriteArrayList
instanceKlass jdk/internal/module/ServicesCatalog$ServiceProvider
instanceKlass jdk/internal/loader/AbstractClassLoaderValue$Memoizer
instanceKlass java/util/ImmutableCollections$ListItr
instanceKlass java/util/ListIterator
instanceKlass java/lang/ModuleLayer
instanceKlass jdk/internal/module/ModuleLoaderMap$Modules
instanceKlass jdk/internal/module/ModuleLoaderMap$Mapper
instanceKlass jdk/internal/module/ModuleLoaderMap
instanceKlass java/util/AbstractMap$1$1
instanceKlass java/lang/module/ResolvedModule
instanceKlass java/lang/module/Configuration
instanceKlass jdk/internal/loader/BuiltinClassLoader$LoadedModule
instanceKlass jdk/internal/loader/AbstractClassLoaderValue
instanceKlass jdk/internal/module/ServicesCatalog
instanceKlass java/util/Deque
instanceKlass java/util/Queue
instanceKlass sun/net/util/IPAddressUtil$MASKS
instanceKlass sun/net/util/IPAddressUtil
instanceKlass java/net/URLStreamHandler
instanceKlass sun/net/www/ParseUtil
instanceKlass java/net/URL$3
instanceKlass jdk/internal/access/JavaNetURLAccess
instanceKlass java/net/URL$DefaultFactory
instanceKlass java/net/URLStreamHandlerFactory
instanceKlass jdk/internal/loader/URLClassPath
instanceKlass java/security/Principal
instanceKlass java/security/ProtectionDomain$Key
instanceKlass java/security/ProtectionDomain$JavaSecurityAccessImpl
instanceKlass jdk/internal/access/JavaSecurityAccess
instanceKlass java/lang/ClassLoader$ParallelLoaders
instanceKlass java/security/cert/Certificate
instanceKlass jdk/internal/loader/ArchivedClassLoaders
instanceKlass java/util/concurrent/ConcurrentHashMap$CollectionView
instanceKlass jdk/internal/loader/ClassLoaderHelper
instanceKlass jdk/internal/loader/NativeLibraries
instanceKlass java/lang/Module$EnableNativeAccess
instanceKlass jdk/internal/loader/BootLoader
instanceKlass java/util/Optional
instanceKlass jdk/internal/module/SystemModuleFinders$SystemModuleFinder
instanceKlass java/lang/module/ModuleFinder
instanceKlass jdk/internal/module/SystemModuleFinders$3
instanceKlass jdk/internal/module/ModuleHashes$HashSupplier
instanceKlass jdk/internal/module/SystemModuleFinders$2
instanceKlass java/util/function/Supplier
instanceKlass java/lang/module/ModuleReference
instanceKlass jdk/internal/module/ModuleResolution
instanceKlass java/util/Collections$UnmodifiableMap
instanceKlass jdk/internal/module/ModuleHashes$Builder
instanceKlass jdk/internal/module/ModuleHashes
instanceKlass jdk/internal/module/ModuleTarget
instanceKlass java/util/ImmutableCollections$Set12$1
instanceKlass java/lang/reflect/AccessFlag$18
instanceKlass java/lang/reflect/AccessFlag$17
instanceKlass java/lang/reflect/AccessFlag$16
instanceKlass java/lang/reflect/AccessFlag$15
instanceKlass java/lang/reflect/AccessFlag$14
instanceKlass java/lang/reflect/AccessFlag$13
instanceKlass java/lang/reflect/AccessFlag$12
instanceKlass java/lang/reflect/AccessFlag$11
instanceKlass java/lang/reflect/AccessFlag$10
instanceKlass java/lang/reflect/AccessFlag$9
instanceKlass java/lang/reflect/AccessFlag$8
instanceKlass java/lang/reflect/AccessFlag$7
instanceKlass java/lang/reflect/AccessFlag$6
instanceKlass java/lang/reflect/AccessFlag$5
instanceKlass java/lang/reflect/AccessFlag$4
instanceKlass java/lang/reflect/AccessFlag$3
instanceKlass java/lang/reflect/AccessFlag$2
instanceKlass java/lang/reflect/AccessFlag$1
instanceKlass java/lang/module/ModuleDescriptor$Version
instanceKlass java/lang/module/ModuleDescriptor$Provides
instanceKlass java/lang/module/ModuleDescriptor$Opens
instanceKlass java/util/ImmutableCollections$SetN$SetNIterator
instanceKlass java/lang/module/ModuleDescriptor$Exports
instanceKlass java/lang/module/ModuleDescriptor$Requires
instanceKlass jdk/internal/module/Builder
instanceKlass jdk/internal/module/SystemModules$default
instanceKlass jdk/internal/module/SystemModules
instanceKlass jdk/internal/module/SystemModulesMap
instanceKlass java/net/URI$1
instanceKlass jdk/internal/access/JavaNetUriAccess
instanceKlass java/net/URI
instanceKlass jdk/internal/module/SystemModuleFinders
instanceKlass jdk/internal/module/ArchivedModuleGraph
instanceKlass jdk/internal/module/ArchivedBootLayer
instanceKlass jdk/internal/module/ModuleBootstrap$Counters
instanceKlass jdk/internal/module/ModulePatcher
instanceKlass java/io/FileSystem
instanceKlass java/io/DefaultFileSystem
instanceKlass java/io/File
instanceKlass java/lang/module/ModuleDescriptor$1
instanceKlass jdk/internal/access/JavaLangModuleAccess
instanceKlass sun/invoke/util/VerifyAccess
instanceKlass java/util/KeyValueHolder
instanceKlass java/util/ImmutableCollections$MapN$MapNIterator
instanceKlass java/lang/StrictMath
instanceKlass java/lang/invoke/MethodHandles$Lookup
instanceKlass java/lang/invoke/MemberName$Factory
instanceKlass java/lang/invoke/MethodHandles
instanceKlass java/lang/module/ModuleDescriptor
instanceKlass jdk/internal/module/ModuleBootstrap
instanceKlass java/lang/Character$CharacterCache
instanceKlass java/util/HexFormat
instanceKlass jdk/internal/util/ClassFileDumper
instanceKlass sun/security/action/GetPropertyAction
instanceKlass java/lang/invoke/MethodHandleStatics
instanceKlass jdk/internal/misc/Blocker
instanceKlass java/util/concurrent/locks/AbstractQueuedSynchronizer$ConditionObject
instanceKlass java/util/concurrent/locks/Condition
instanceKlass java/util/Collections
instanceKlass java/lang/Thread$ThreadIdentifiers
instanceKlass sun/io/Win32ErrorMode
instanceKlass jdk/internal/misc/OSEnvironment
instanceKlass java/lang/Integer$IntegerCache
instanceKlass jdk/internal/misc/Signal$NativeHandler
instanceKlass java/util/Hashtable$Entry
instanceKlass jdk/internal/misc/Signal
instanceKlass java/lang/Terminator$1
instanceKlass jdk/internal/misc/Signal$Handler
instanceKlass java/lang/Terminator
instanceKlass java/nio/ByteOrder
instanceKlass java/nio/Buffer$2
instanceKlass jdk/internal/access/JavaNioAccess
instanceKlass java/nio/Buffer$1
instanceKlass jdk/internal/misc/ScopedMemoryAccess
instanceKlass java/nio/charset/CodingErrorAction
instanceKlass sun/nio/cs/SingleByte
instanceKlass java/lang/StringUTF16
instanceKlass sun/nio/cs/IBM437$Holder
instanceKlass sun/nio/cs/ArrayEncoder
instanceKlass java/nio/charset/CharsetEncoder
instanceKlass java/io/Writer
instanceKlass java/io/PrintStream$1
instanceKlass jdk/internal/access/JavaIOPrintStreamAccess
instanceKlass jdk/internal/misc/InternalLock
instanceKlass java/io/OutputStream
instanceKlass java/io/Flushable
instanceKlass java/io/FileDescriptor$1
instanceKlass jdk/internal/access/JavaIOFileDescriptorAccess
instanceKlass java/io/FileDescriptor
instanceKlass jdk/internal/util/StaticProperty
instanceKlass jdk/internal/reflect/MethodHandleAccessorFactory
instanceKlass java/lang/reflect/Modifier
instanceKlass java/lang/Class$1
instanceKlass java/lang/Class$Atomic
instanceKlass java/lang/Class$ReflectionData
instanceKlass java/nio/charset/StandardCharsets
instanceKlass sun/nio/cs/HistoricallyNamedCharset
instanceKlass java/nio/charset/spi/CharsetProvider
instanceKlass java/nio/charset/Charset
instanceKlass java/util/HashMap$HashIterator
instanceKlass java/util/concurrent/locks/LockSupport
instanceKlass java/util/concurrent/ConcurrentHashMap$Node
instanceKlass java/util/concurrent/ConcurrentHashMap$CounterCell
instanceKlass java/util/concurrent/locks/ReentrantLock
instanceKlass java/util/concurrent/locks/Lock
instanceKlass java/lang/CharacterData
instanceKlass java/util/Arrays
instanceKlass jdk/internal/util/Preconditions$3
instanceKlass jdk/internal/util/Preconditions$2
instanceKlass jdk/internal/util/Preconditions$4
instanceKlass java/util/function/BiFunction
instanceKlass jdk/internal/util/Preconditions$1
instanceKlass java/util/function/Function
instanceKlass jdk/internal/util/Preconditions
instanceKlass java/lang/Runtime
instanceKlass java/lang/VersionProps
instanceKlass java/lang/StringConcatHelper
instanceKlass java/util/HashMap$Node
instanceKlass java/util/Map$Entry
instanceKlass jdk/internal/util/ArraysSupport
instanceKlass jdk/internal/util/SystemProps$Raw
instanceKlass jdk/internal/util/SystemProps
instanceKlass java/lang/System$2
instanceKlass jdk/internal/access/JavaLangAccess
instanceKlass java/lang/ref/NativeReferenceQueue$Lock
instanceKlass java/lang/ref/ReferenceQueue
instanceKlass java/lang/ref/Reference$1
instanceKlass jdk/internal/access/JavaLangRefAccess
instanceKlass jdk/internal/reflect/ReflectionFactory
instanceKlass java/lang/Math
instanceKlass java/lang/StringLatin1
instanceKlass jdk/internal/reflect/Reflection
instanceKlass jdk/internal/reflect/ReflectionFactory$GetReflectionFactoryAction
instanceKlass java/security/PrivilegedAction
instanceKlass jdk/internal/access/SharedSecrets
instanceKlass java/lang/reflect/ReflectAccess
instanceKlass jdk/internal/access/JavaLangReflectAccess
instanceKlass java/util/ImmutableCollections
instanceKlass java/util/Objects
instanceKlass java/util/Set
instanceKlass jdk/internal/misc/CDS
instanceKlass java/lang/Module$ArchivedData
instanceKlass jdk/internal/misc/VM
instanceKlass java/lang/String$CaseInsensitiveComparator
instanceKlass java/util/Comparator
instanceKlass java/io/ObjectStreamField
instanceKlass jdk/internal/vm/FillerObject
instanceKlass jdk/internal/vm/vector/VectorSupport$VectorPayload
instanceKlass jdk/internal/vm/vector/VectorSupport
instanceKlass java/lang/reflect/RecordComponent
instanceKlass java/util/Iterator
instanceKlass java/lang/Number
instanceKlass java/lang/Character
instanceKlass java/lang/Boolean
instanceKlass java/util/concurrent/locks/AbstractOwnableSynchronizer
instanceKlass java/lang/LiveStackFrame
instanceKlass java/lang/StackFrameInfo
instanceKlass java/lang/StackWalker$StackFrame
instanceKlass java/lang/StackStreamFactory$AbstractStackWalker
instanceKlass java/lang/StackWalker
instanceKlass java/nio/Buffer
instanceKlass java/lang/StackTraceElement
instanceKlass java/util/RandomAccess
instanceKlass java/util/List
instanceKlass java/util/SequencedCollection
instanceKlass java/util/AbstractCollection
instanceKlass java/util/Collection
instanceKlass java/lang/Iterable
instanceKlass java/util/concurrent/ConcurrentMap
instanceKlass java/util/AbstractMap
instanceKlass java/security/CodeSource
instanceKlass jdk/internal/loader/ClassLoaders
instanceKlass java/util/jar/Manifest
instanceKlass java/lang/Enum
instanceKlass java/net/URL
instanceKlass java/io/InputStream
instanceKlass java/io/Closeable
instanceKlass java/lang/AutoCloseable
instanceKlass jdk/internal/module/Modules
instanceKlass jdk/internal/misc/Unsafe
instanceKlass jdk/internal/misc/UnsafeConstants
instanceKlass java/lang/AbstractStringBuilder
instanceKlass java/lang/Appendable
instanceKlass java/lang/AssertionStatusDirectives
instanceKlass java/lang/invoke/MethodHandleNatives$CallSiteContext
instanceKlass jdk/internal/foreign/abi/ABIDescriptor
instanceKlass jdk/internal/foreign/abi/NativeEntryPoint
instanceKlass java/lang/invoke/CallSite
instanceKlass java/lang/invoke/MethodType
instanceKlass java/lang/invoke/TypeDescriptor$OfMethod
instanceKlass java/lang/invoke/LambdaForm
instanceKlass java/lang/invoke/MethodHandleNatives
instanceKlass java/lang/invoke/ResolvedMethodName
instanceKlass java/lang/invoke/MemberName
instanceKlass java/lang/invoke/VarHandle
instanceKlass java/lang/invoke/MethodHandle
instanceKlass jdk/internal/reflect/CallerSensitive
instanceKlass java/lang/annotation/Annotation
instanceKlass jdk/internal/reflect/FieldAccessor
instanceKlass jdk/internal/reflect/ConstantPool
instanceKlass jdk/internal/reflect/ConstructorAccessor
instanceKlass jdk/internal/reflect/MethodAccessor
instanceKlass jdk/internal/reflect/MagicAccessorImpl
instanceKlass jdk/internal/vm/StackChunk
instanceKlass jdk/internal/vm/Continuation
instanceKlass jdk/internal/vm/ContinuationScope
instanceKlass java/lang/reflect/Parameter
instanceKlass java/lang/reflect/Member
instanceKlass java/lang/reflect/AccessibleObject
instanceKlass java/lang/Module
instanceKlass java/util/Map
instanceKlass java/util/Dictionary
instanceKlass java/lang/ThreadGroup
instanceKlass java/lang/Thread$UncaughtExceptionHandler
instanceKlass java/lang/Thread$Constants
instanceKlass java/lang/Thread$FieldHolder
instanceKlass java/lang/Thread
instanceKlass java/lang/Runnable
instanceKlass java/lang/ref/Reference
instanceKlass java/lang/Record
instanceKlass java/security/AccessController
instanceKlass java/security/AccessControlContext
instanceKlass java/security/ProtectionDomain
instanceKlass java/lang/SecurityManager
instanceKlass java/lang/Throwable
instanceKlass java/lang/System
instanceKlass java/lang/ClassLoader
instanceKlass java/lang/Cloneable
instanceKlass java/lang/Class
instanceKlass java/lang/invoke/TypeDescriptor$OfField
instanceKlass java/lang/invoke/TypeDescriptor
instanceKlass java/lang/reflect/Type
instanceKlass java/lang/reflect/GenericDeclaration
instanceKlass java/lang/reflect/AnnotatedElement
instanceKlass java/lang/String
instanceKlass java/lang/constant/ConstantDesc
instanceKlass java/lang/constant/Constable
instanceKlass java/lang/CharSequence
instanceKlass java/lang/Comparable
instanceKlass java/io/Serializable
ciInstanceKlass java/lang/Object 1 1 124 7 1 10 12 1 1 10 7 12 1 1 1 10 7 12 1 1 1 10 12 1 1 8 1 10 12 1 1 10 7 12 1 1 1 10 12 1 10 12 1 1 10 7 12 1 1 1 10 12 1 10 12 1 7 1 10 7 12 1 1 1 10 12 1 1 10 12 1 100 1 8 1 10 12 1 3 8 1 7 1 5 0 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 7 1 1 1 1 1 1 1 1 1 1 7 1 1 1 1 1 1 1 1 1 1 3 1 1
ciMethod java/lang/Object equals (Ljava/lang/Object;)Z 516 0 6233 0 -1
ciMethod java/lang/Object hashCode ()I 256 0 128 0 -1
ciInstanceKlass java/io/Serializable 1 0 7 100 1 100 1 1 1
ciInstanceKlass java/lang/System 1 1 834 10 7 12 1 1 1 9 7 12 1 1 1 10 12 1 10 12 1 1 10 12 1 1 10 12 1 9 12 1 1 10 7 12 1 1 1 11 100 12 1 1 1 10 100 12 1 1 1 10 12 1 1 10 12 1 1 100 1 8 1 10 12 1 10 7 12 1 1 1 10 100 12 1 1 1 18 12 1 1 10 7 12 1 1 1 7 1 10 12 1 1 10 7 12 1 1 1 10 12 1 10 7 12 1 1 1 9 100 12 1 1 1 10 7 12 1 1 1 11 7 12 1 1 1 10 12 1 1 10 7 12 1 1 1 7 1 10 10 12 1 1 8 1 10 12 1 8 1 10 12 1 9 12 1 1 8 1 10 7 12 1 1 1 10 12 1 1 100 1 8 1 10 9 12 1 1 8 1 10 12 1 1 10 100 12 1 1 1 8 1 10 12 1 7 1 10 12 1 8 1 10 12 1 10 12 1 1 100 1 10 12 10 12 1 9 12 1 1 9 12 1 1 10 7 12 1 1 1 10 7 12 1 1 1 10 12 1 1 10 12 1 10 12 1 10 7 12 1 1 1 10 12 1 100 1 100 1 8 1 10 12 1 10 12 1 1 7 1 10 12 1 100 1 8 1 10 10 12 1 100 1 8 1 10 8 1 10 7 12 1 1 8 1 10 12 100 1 8 1 10 10 12 1 1 10 100 12 1 1 1 100 1 18 12 1 100 1 9 100 12 1 1 1 10 12 1 100 1 10 12 1 1 10 12 1 1 10 7 12 1 1 1 10 12 1 1 10 12 1 10 12 1 10 12 1 1 10 12 1 7 1 10 12 1 9 7 12 1 1 1 10 7 12 1 1 1 10 12 1 10 12 1 10 7 12 1 1 1 7 1 8 1 10 9 12 1 9 12 1 10 12 1 10 7 12 1 1 10 12 1 10 12 1 1 8 1 10 12 1 1 8 1 11 12 1 10 12 11 12 1 1 11 7 12 1 1 1 11 7 12 1 1 11 12 1 1 7 1 11 12 1 10 12 1 8 1 10 12 1 1 8 1 8 1 8 1 8 1 11 12 1 10 12 1 10 12 1 10 12 1 8 1 10 12 1 1 8 1 9 12 1 8 1 10 7 12 1 1 8 1 7 1 9 7 12 1 1 1 10 12 1 7 1 9 12 10 9 12 7 1 10 12 9 12 1 1 8 1 10 12 1 1 8 1 10 7 12 1 1 10 12 1 10 12 1 1 11 7 12 1 1 10 12 10 7 12 1 1 1 9 12 1 1 7 1 8 1 10 12 1 1 10 7 12 1 1 1 7 1 10 12 1 1 10 12 1 8 1 8 1 10 8 1 8 1 8 1 8 1 10 10 7 12 1 1 10 12 1 10 12 1 1 10 12 1 1 10 12 1 10 100 12 1 1 1 10 7 1 8 1 10 10 10 12 1 1 10 12 1 1 8 1 10 12 1 8 1 8 1 10 12 1 10 7 12 1 1 1 10 12 1 1 7 1 10 10 12 1 10 12 1 9 12 1 1 3 1 3 1 1 1 1 1 1 1 1 1 1 1 1 100 1 1 100 1 1 1 1 1 1 1 1 1 1 1 100 1 7 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 1 16 15 10 12 1 1 16 15 10 12 16 1 15 10 100 12 1 1 1 1 1 1 1 1 1 100 1 100 1 1
staticfield java/lang/System in Ljava/io/InputStream; java/io/BufferedInputStream
staticfield java/lang/System out Ljava/io/PrintStream; org/fusesource/jansi/AnsiPrintStream
staticfield java/lang/System err Ljava/io/PrintStream; org/fusesource/jansi/AnsiPrintStream
ciInstanceKlass java/lang/SecurityManager 0 0 576 10 7 12 1 1 1 9 7 12 1 1 1 10 7 12 1 1 1 100 1 8 1 10 12 1 10 12 1 1 10 7 12 1 1 1 10 7 1 10 100 1 10 9 100 12 1 1 1 10 7 12 1 1 1 10 12 1 1 10 7 12 1 1 100 1 8 1 10 9 12 1 1 9 12 1 8 1 9 12 1 7 1 10 8 1 10 12 1 1 10 12 1 10 12 1 1 100 1 10 10 12 1 1 100 1 8 1 10 12 1 8 1 8 1 8 1 8 1 8 1 8 1 10 12 1 8 1 8 1 8 1 8 1 8 1 10 7 12 1 1 1 10 12 1 1 8 1 100 1 8 1 10 8 1 8 1 8 1 8 1 8 1 10 100 12 1 1 8 1 100 1 8 1 8 1 10 8 1 10 12 1 100 1 8 1 10 10 12 1 1 10 12 1 10 12 1 10 12 1 10 100 12 1 1 1 10 12 1 10 100 12 1 1 11 100 12 1 1 1 18 12 1 1 11 100 12 1 1 1 18 12 1 1 11 12 1 1 18 18 11 12 1 18 12 1 11 12 1 1 9 12 1 1 9 12 1 9 12 1 9 12 1 100 1 10 100 12 1 1 10 12 1 10 12 1 18 12 1 18 10 100 12 1 1 1 18 12 1 10 12 1 18 18 8 1 10 12 1 9 12 1 1 11 7 12 1 1 1 8 1 100 1 10 12 1 10 12 1 1 10 12 1 1 9 12 1 1 10 12 1 10 12 1 1 8 1 100 1 10 9 12 1 8 1 10 12 1 8 1 100 1 10 10 100 12 1 1 10 100 1 9 7 12 1 1 1 11 12 1 1 10 12 1 11 12 1 10 12 1 7 1 10 10 12 1 1 10 12 1 1 1 1 1 1 1 1 1 1 1 7 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 3 1 1 1 1 1 1 1 1 1 1 100 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 16 15 10 7 12 1 1 1 16 1 16 15 10 12 16 1 15 10 12 16 15 11 7 1 16 1 16 1 15 10 12 16 15 10 12 16 15 10 12 1 16 1 15 11 12 1 15 10 12 16 15 10 16 1 15 10 100 12 1 1 1 1 1 1 100 1 100 1 1
ciInstanceKlass java/security/AccessController 1 1 295 10 7 12 1 1 1 10 7 12 1 1 1 10 7 12 1 1 1 10 12 1 1 10 12 1 1 10 7 12 1 1 1 10 12 1 1 10 12 1 10 12 1 1 10 12 1 100 1 8 1 10 12 1 10 12 1 10 12 1 1 10 12 1 7 1 7 1 10 12 1 1 10 12 1 10 12 1 10 12 1 1 10 7 12 1 1 1 10 12 1 1 9 100 12 1 1 1 10 7 12 1 1 1 10 12 1 10 12 1 9 100 12 1 1 1 10 12 1 10 12 1 1 9 12 1 1 10 100 1 10 11 7 12 1 1 1 10 7 12 1 1 11 7 1 100 1 10 12 1 10 12 1 10 12 1 8 1 10 12 1 1 8 1 10 100 12 1 1 1 8 1 7 1 10 10 12 1 1 10 12 1 10 7 12 1 1 1 10 12 1 8 1 10 7 12 1 1 8 1 8 1 10 12 1 8 1 10 12 1 10 12 1 1 10 12 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 100 1 1 1 1 1 1 1 1 1 3 1 1 1
staticfield java/security/AccessController $assertionsDisabled Z 1
ciInstanceKlass java/security/ProtectionDomain 1 1 348 10 7 12 1 1 1 9 7 12 1 1 1 7 1 10 9 12 1 1 9 12 1 1 9 12 1 1 10 7 12 1 1 7 1 9 12 1 9 12 1 1 7 1 9 12 1 1 9 12 1 10 100 12 1 1 10 100 12 1 1 1 10 12 1 1 10 12 1 9 12 1 9 100 12 1 1 10 12 1 1 10 100 1 10 12 1 1 8 1 7 1 8 1 10 12 1 10 11 10 7 12 1 1 1 10 12 1 1 8 1 11 8 1 10 12 1 8 1 8 1 10 12 1 1 10 12 1 10 12 1 1 10 12 1 8 1 10 12 1 8 1 8 1 10 7 12 1 1 1 9 100 12 1 1 1 10 12 1 1 9 100 12 1 1 1 10 7 12 1 1 1 100 1 18 12 1 1 10 7 12 1 1 1 10 7 1 10 12 1 10 12 1 1 11 100 12 1 1 11 12 1 100 1 11 7 12 1 1 1 10 12 1 10 11 12 1 1 11 12 1 1 10 12 1 10 7 12 1 1 10 100 12 1 1 11 12 1 10 12 10 12 1 8 1 8 1 10 7 12 1 1 1 7 1 10 10 7 12 1 1 1 1 1 1 1 1 1 1 7 1 1 7 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 7 1 1 1 1 1 1 100 1 1 16 15 10 12 16 15 10 100 12 1 1 1 1 1 1 1 100 1 100 1 1
staticfield java/security/ProtectionDomain filePermCompatInPD Z 0
ciInstanceKlass java/security/CodeSource 1 1 398 10 7 12 1 1 1 9 7 12 1 1 1 9 12 1 1 9 12 1 1 9 12 1 1 10 7 12 1 1 1 9 12 1 1 10 100 12 1 1 10 100 10 7 12 1 1 1 10 100 12 1 1 1 10 12 1 1 7 1 10 10 100 12 1 1 1 10 100 12 1 1 1 10 12 1 1 100 1 10 12 1 1 10 12 1 1 10 12 1 1 10 12 1 10 10 10 12 1 1 10 7 12 1 1 1 10 12 1 10 12 1 10 12 1 8 1 10 12 1 10 12 1 10 12 1 1 10 12 1 8 1 10 12 1 1 10 7 1 10 10 12 1 1 8 1 10 12 1 10 12 1 10 12 1 8 1 8 1 9 12 1 1 100 1 8 1 10 12 1 10 12 1 1 8 1 10 12 1 8 1 8 1 8 1 10 100 12 1 1 10 12 1 1 10 12 1 10 12 1 1 10 12 1 1 10 12 1 1 100 1 100 1 10 12 1 10 12 10 12 1 1 10 100 12 1 1 10 12 1 7 1 10 12 10 100 12 1 1 1 10 8 1 10 12 1 10 12 1 10 12 1 1 100 1 10 12 1 1 100 1 7 1 8 1 8 1 10 10 12 1 1 10 100 12 1 1 1 7 1 10 12 10 12 1 1 11 7 12 1 1 10 10 12 1 11 10 12 1 8 1 100 1 10 12 1 10 12 1 1 10 12 1 11 12 1 1 7 1 1 1 1 5 0 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 1 1 1 1 1 1
ciInstanceKlass java/lang/Boolean 1 1 152 10 7 12 1 1 1 9 7 12 1 1 1 10 12 1 1 10 12 1 8 1 10 7 12 1 1 9 12 1 1 9 12 1 10 12 1 1 10 12 1 1 10 12 1 1 10 7 12 1 1 1 100 1 100 1 10 12 1 1 9 100 12 1 1 9 12 10 100 12 1 1 1 10 12 1 1 8 1 10 7 12 1 1 1 9 12 1 1 7 1 7 1 7 1 1 1 1 1 1 5 0 1 1 1 1 1 1 1 1 1 1 3 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 7 1 1 1 1 1 1 1 1 1 1 100 1 1 1 1 1 1 1 1 1
staticfield java/lang/Boolean TRUE Ljava/lang/Boolean; java/lang/Boolean
staticfield java/lang/Boolean FALSE Ljava/lang/Boolean; java/lang/Boolean
staticfield java/lang/Boolean TYPE Ljava/lang/Class; java/lang/Class
ciInstanceKlass java/lang/Comparable 1 0 12 100 1 100 1 1 1 1 1 1 1 1
ciInstanceKlass java/lang/constant/Constable 1 0 11 100 1 100 1 1 1 1 1 1 1
ciInstanceKlass java/util/Map 1 1 263 11 7 12 1 1 1 11 12 1 1 10 100 12 1 1 11 12 1 1 11 7 12 1 1 1 11 100 12 1 1 1 11 12 1 1 7 1 11 12 1 11 12 1 100 1 100 1 10 12 1 1 11 100 12 1 1 1 11 100 12 1 1 1 11 12 1 11 12 1 10 12 1 1 11 12 1 11 100 12 1 9 7 12 1 1 1 100 1 10 12 7 1 7 1 10 12 1 7 1 10 7 1 11 12 1 11 12 1 1 11 12 1 1 7 1 11 12 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1
ciInstanceKlass java/lang/Class 1 1 1698 10 7 12 1 1 1 9 7 12 1 1 1 9 12 1 1 10 12 1 1 8 1 10 12 1 8 1 8 1 10 12 1 1 10 7 12 1 1 1 10 12 1 7 1 10 10 12 1 10 12 1 1 10 12 1 1 10 12 1 1 10 7 12 1 1 10 12 1 10 12 1 10 12 1 8 1 10 12 1 8 1 10 12 1 8 1 8 1 10 12 1 1 10 7 12 1 1 1 18 12 1 1 11 7 12 1 1 1 8 1 8 1 8 1 10 7 12 1 1 1 11 12 1 1 8 1 10 12 1 10 11 100 12 1 1 1 11 7 12 1 1 1 11 8 1 18 8 1 10 12 1 10 7 12 1 1 10 12 1 1 10 7 12 1 1 1 10 12 1 1 10 12 1 1 10 7 12 1 1 1 10 12 9 100 12 1 1 1 10 7 12 1 1 1 10 12 1 10 7 12 1 1 1 10 12 1 1 18 12 1 1 10 7 12 1 1 1 10 7 12 1 10 12 1 1 10 7 1 7 1 10 12 1 1 9 12 1 1 100 1 8 1 10 12 1 10 12 1 1 10 12 1 1 10 7 12 1 1 1 7 1 10 12 1 7 1 100 1 10 10 12 1 1 10 12 1 1 100 1 10 7 12 1 1 1 10 12 1 1 10 12 1 1 9 12 1 1 10 12 1 10 12 1 1 9 12 1 1 9 12 1 1 10 12 1 1 10 7 1 10 12 1 10 12 1 10 12 1 1 10 9 12 1 10 12 1 8 1 10 12 1 1 10 12 1 1 10 12 1 10 12 1 1 10 12 1 1 10 12 1 1 9 7 12 1 1 1 10 7 12 1 1 10 12 10 12 1 10 12 1 10 12 1 10 12 1 9 100 12 1 1 1 9 12 1 10 12 1 10 100 12 1 1 1 10 12 1 1 10 100 12 1 1 10 12 1 10 12 1 1 10 100 12 1 1 1 10 12 1 10 12 1 1 10 12 1 10 12 1 10 12 1 1 10 7 1 10 10 10 12 1 1 10 12 1 1 10 12 10 10 12 1 1 7 1 8 1 10 10 12 1 1 10 12 1 100 1 11 12 1 10 100 12 1 1 10 12 1 10 12 1 10 100 12 1 1 10 10 12 1 1 8 1 10 12 1 10 12 1 1 8 1 10 12 1 9 12 1 10 12 1 10 12 1 10 12 1 10 12 7 1 9 12 1 10 12 1 9 12 1 10 12 1 10 12 1 10 12 1 10 10 12 1 10 12 1 10 7 12 1 1 10 12 1 10 12 1 10 12 1 1 10 12 1 10 12 1 10 12 1 100 1 10 12 1 10 12 1 1 10 12 1 1 10 12 1 1 10 12 1 1 10 12 1 1 10 12 1 1 100 1 10 10 12 1 1 10 12 1 1 10 12 1 1 10 10 12 1 10 12 1 1 10 12 1 1 10 12 1 1 10 12 1 1 7 1 10 10 12 11 7 12 1 1 10 12 1 10 12 1 10 100 12 1 1 1 10 12 1 1 10 10 12 1 1 7 1 10 10 12 1 1 10 7 12 1 1 1 100 1 100 1 10 12 1 1 10 12 1 10 10 12 1 1 10 12 1 10 12 1 10 12 1 10 100 12 1 1 1 11 7 12 1 1 10 12 1 10 12 1 9 12 1 10 12 1 1 10 12 1 9 12 1 1 100 1 10 9 12 1 1 10 12 7 1 10 12 1 9 12 1 10 100 12 1 1 1 10 12 1 10 12 1 10 100 12 1 1 1 10 12 1 10 12 10 12 1 1 100 1 10 8 1 10 12 1 11 11 12 1 1 11 7 12 1 1 11 12 1 8 1 10 12 1 10 12 1 1 9 12 1 9 12 1 1 10 7 12 1 1 9 12 1 10 12 1 1 10 10 12 1 10 7 12 1 1 1 10 7 12 1 1 10 7 12 1 1 9 12 1 1 10 12 1 9 12 1 10 12 1 10 12 1 1 9 12 1 1 9 12 1 10 12 1 10 12 1 1 9 12 1 100 1 10 10 12 1 1 7 1 10 12 1 1 100 11 7 1 9 12 1 1 9 12 1 7 1 10 12 1 9 12 1 1 9 12 1 10 12 1 10 12 1 1 9 12 1 7 1 10 10 12 1 1 10 10 12 1 10 12 10 10 12 1 9 12 1 10 12 1 1 10 7 12 1 1 1 10 12 1 1 10 12 1 8 10 7 8 1 18 8 1 8 1 10 12 1 9 12 1 9 12 1 1 10 12 1 7 1 7 1 10 12 1 9 12 1 1 7 1 10 10 12 1 10 7 1 9 12 1 8 1 10 12 1 7 1 10 12 1 10 12 1 1 100 1 7 1 9 12 1 100 1 8 1 10 10 7 12 1 1 1 10 12 11 7 12 1 1 1 10 12 1 10 12 1 1 10 8 1 8 1 10 12 1 1 9 7 12 1 1 11 12 7 1 11 7 12 1 1 9 12 1 10 100 12 1 1 1 10 7 12 1 1 10 12 1 1 9 12 1 9 10 12 1 1 10 12 1 1 10 12 1 1 10 12 1 1 10 12 1 1 11 12 1 7 1 11 12 1 10 7 12 1 1 1 10 12 1 11 12 1 10 7 12 1 1 1 10 12 1 10 7 12 1 1 1 11 12 1 11 12 1 1 10 12 1 10 12 1 1 9 12 1 1 9 7 12 1 1 10 12 1 10 7 12 1 1 1 10 12 1 1 10 12 1 10 12 1 10 12 1 10 7 12 1 1 1 10 12 1 8 1 10 12 1 10 12 1 10 12 1 100 1 10 12 10 100 12 1 1 1 11 100 12 1 1 1 10 12 1 1 10 12 1 18 12 1 1 11 12 1 1 18 11 12 1 18 12 1 11 12 1 10 12 1 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 10 12 1 8 1 10 12 1 7 1 9 12 1 1 7 1 7 1 7 1 7 1 1 1 3 1 3 1 3 1 1 1 1 1 1 1 5 0 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 1 1 1 1 1 1 100 1 1 1 1 1 7 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 3 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 7 1 7 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 16 15 10 12 16 15 11 12 16 1 16 15 16 15 10 12 16 16 15 10 12 16 15 16 1 15 10 12 16 15 10 7 12 1 1 1 1 1 1 1 1 1 1 1 1 7 1 1 100 1 100 1 1 100 1 100 1 1 100 1 100 1 1
staticfield java/lang/Class EMPTY_CLASS_ARRAY [Ljava/lang/Class; 0 [Ljava/lang/Class;
staticfield java/lang/Class serialPersistentFields [Ljava/io/ObjectStreamField; 0 [Ljava/io/ObjectStreamField;
ciInstanceKlass java/lang/reflect/AnnotatedElement 1 1 164 11 7 12 1 1 1 11 12 1 1 7 1 10 100 12 1 1 1 10 12 1 1 10 12 1 1 10 12 1 10 100 12 1 1 1 11 12 1 1 11 7 12 1 1 10 7 12 1 1 1 10 12 1 10 100 12 1 1 1 18 12 1 1 11 100 12 1 1 18 12 1 18 12 1 1 10 100 12 1 1 1 11 100 12 1 1 1 7 1 10 100 12 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 1 1 1 1 1 1 1 1 1 1 16 15 16 1 16 1 15 11 12 16 16 1 15 10 100 12 1 1 1 16 1 15 10 100 12 1 1 1 1 100 1 100 1 1
ciInstanceKlass java/lang/invoke/TypeDescriptor 1 0 17 100 1 100 1 1 1 1 1 1 100 1 100 1 1 1 1
ciInstanceKlass java/lang/reflect/GenericDeclaration 1 0 30 7 1 7 1 7 1 1 1 1 1 1 1 1 100 1 8 1 1 12 10 1 1 1 8 1 1 1 8 1
ciInstanceKlass java/lang/reflect/Type 1 1 17 11 100 12 1 1 1 100 1 1 1 1 1 1 1 1 1
ciInstanceKlass java/lang/invoke/TypeDescriptor$OfField 1 0 21 100 1 100 1 100 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1
ciInstanceKlass java/lang/StringBuilder 1 1 422 10 7 12 1 1 1 10 12 1 10 12 1 10 12 1 1 10 7 12 1 1 1 10 7 12 1 1 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 1 10 12 1 10 12 1 1 10 12 1 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 10 12 1 10 12 1 10 12 1 10 12 1 1 10 12 1 10 12 1 10 12 10 12 1 1 10 12 1 10 12 1 10 12 1 10 100 12 1 1 1 9 12 1 1 10 12 1 10 12 1 1 10 12 1 1 9 12 1 1 10 100 12 1 1 1 10 100 1 10 12 1 1 10 100 12 1 1 10 12 1 10 12 1 1 100 1 100 1 8 1 10 10 12 1 1 10 12 1 10 12 1 10 12 1 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 1 10 12 1 1 10 12 1 10 12 1 10 12 1 10 12 10 12 10 12 1 10 12 1 10 12 1 10 12 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 1 10 12 1 10 12 1 1 10 12 1 10 12 1 1 10 12 1 10 12 1 1 10 12 1 10 12 1 10 12 1 10 10 12 1 10 12 1 7 1 7 1 7 1 7 1 1 1 1 5 0 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 1 1 7 1 1 1 1 1 1 1 1 1 1 1
instanceKlass java/lang/StringBuilder
instanceKlass java/lang/StringBuffer
ciInstanceKlass java/lang/AbstractStringBuilder 1 1 605 7 1 10 7 12 1 1 1 9 7 12 1 1 1 9 12 1 9 12 1 1 9 12 1 1 10 7 12 1 1 1 10 12 1 1 7 1 3 3 10 12 1 10 12 1 1 11 7 1 100 1 7 1 10 8 1 10 12 1 10 12 1 10 12 1 1 10 12 1 10 12 1 9 12 1 10 12 1 9 12 1 1 10 12 1 1 10 7 12 1 1 1 10 10 12 1 10 12 1 10 12 1 1 10 12 1 1 10 7 12 1 1 1 10 7 12 1 1 1 7 1 8 1 10 10 12 1 1 100 1 10 12 10 12 1 1 10 10 12 1 1 10 12 1 1 10 12 1 1 10 12 1 1 10 10 7 12 1 1 1 10 12 1 100 1 10 10 7 12 1 1 1 9 12 1 1 9 12 1 10 12 1 1 10 10 12 1 1 10 12 10 12 1 10 12 1 1 10 12 1 1 10 12 1 1 10 12 1 10 12 1 1 10 12 1 1 10 12 1 10 12 1 1 8 1 10 12 1 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 7 12 1 1 10 12 1 10 12 1 10 100 12 1 1 1 100 1 100 1 10 12 1 10 100 12 1 1 10 12 1 10 12 1 10 12 1 10 12 1 1 10 12 1 10 12 1 1 10 12 1 1 10 10 7 12 1 1 1 10 12 1 10 12 1 10 12 1 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 1 10 12 1 10 12 1 10 12 10 12 1 1 18 12 1 1 100 1 10 100 12 1 1 1 18 10 12 1 1 10 12 1 10 12 1 1 11 12 1 1 10 12 1 10 12 10 12 1 10 10 10 12 1 10 5 0 10 10 12 1 1 100 1 8 1 10 10 12 1 1 10 100 12 1 1 1 10 12 1 10 12 1 1 100 1 10 12 100 1 10 100 1 10 7 1 1 1 1 1 1 1 1 1 1 1 100 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 1 1 1 1 1 7 1 1 16 1 15 10 12 16 15 10 12 15 10 100 12 1 1 1 1 1 1 1 100 1 100 1 1
staticfield java/lang/AbstractStringBuilder EMPTYVALUE [B 0
ciMethod java/lang/StringBuilder toString ()Ljava/lang/String; 12 0 78592 0 1392
ciInstanceKlass java/lang/Appendable 1 0 14 100 1 100 1 1 1 1 100 1 1 1 1 1
ciInstanceKlass java/lang/CharSequence 1 1 131 11 7 12 1 1 1 18 12 1 1 100 1 10 100 12 1 1 1 18 10 100 12 1 1 1 11 12 1 1 11 7 1 11 12 1 1 10 100 12 1 1 1 11 12 1 1 100 1 10 12 1 1 10 100 12 1 1 1 100 1 10 10 12 1 1 7 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 16 1 15 11 12 16 15 11 12 15 10 100 12 1 1 1 1 1 100 1 100 1 1 100 1 1 100 1 100 1 1
ciInstanceKlass java/lang/AutoCloseable 1 0 12 100 1 100 1 1 1 1 100 1 1 1
ciInstanceKlass java/io/Closeable 1 0 14 100 1 100 1 100 1 1 1 1 100 1 1 1
instanceKlass org/codehaus/plexus/interpolation/reflection/MethodMap$AmbiguousException
instanceKlass org/apache/maven/model/resolution/InvalidRepositoryException
instanceKlass org/apache/maven/model/resolution/UnresolvableModelException
instanceKlass org/apache/maven/cli/internal/ExtensionResolutionException
instanceKlass org/apache/maven/toolchain/building/ToolchainsBuildingException
instanceKlass org/apache/maven/execution/MavenExecutionRequestPopulationException
instanceKlass org/apache/maven/repository/metadata/MetadataGraphTransformationException
instanceKlass org/apache/maven/project/interpolation/ModelInterpolationException
instanceKlass org/apache/maven/settings/building/SettingsBuildingException
instanceKlass org/apache/maven/repository/ArtifactDoesNotExistException
instanceKlass org/apache/maven/repository/ArtifactTransferFailedException
instanceKlass org/apache/maven/plugin/version/PluginVersionNotFoundException
instanceKlass org/apache/maven/plugin/InvalidPluginException
instanceKlass org/apache/maven/repository/metadata/GraphConflictResolutionException
instanceKlass org/apache/maven/repository/metadata/MetadataResolutionException
instanceKlass org/apache/maven/configuration/BeanConfigurationException
instanceKlass org/apache/http/HttpException
instanceKlass org/apache/maven/lifecycle/LifecycleNotFoundException
instanceKlass org/apache/maven/lifecycle/LifecyclePhaseNotFoundException
instanceKlass java/security/GeneralSecurityException
instanceKlass org/apache/maven/BuildFailureException
instanceKlass org/codehaus/plexus/util/dag/CycleDetectedException
instanceKlass org/apache/maven/MavenExecutionException
instanceKlass org/apache/maven/project/DuplicateProjectException
instanceKlass org/apache/maven/artifact/installer/ArtifactInstallationException
instanceKlass org/apache/maven/toolchain/MisconfiguredToolchainException
instanceKlass org/apache/maven/project/DependencyResolutionException
instanceKlass org/apache/maven/model/building/ModelBuildingException
instanceKlass org/codehaus/plexus/component/composition/CycleDetectedInComponentGraphException
instanceKlass org/codehaus/plexus/configuration/PlexusConfigurationException
instanceKlass org/codehaus/plexus/component/configurator/ComponentConfigurationException
instanceKlass org/codehaus/plexus/component/configurator/expression/ExpressionEvaluationException
instanceKlass org/codehaus/plexus/component/repository/exception/ComponentLifecycleException
instanceKlass org/apache/maven/plugin/PluginConfigurationException
instanceKlass org/sonatype/plexus/components/sec/dispatcher/SecDispatcherException
instanceKlass org/sonatype/plexus/components/cipher/PlexusCipherException
instanceKlass org/apache/maven/plugin/PluginManagerException
instanceKlass org/apache/maven/lifecycle/LifecycleExecutionException
instanceKlass org/apache/maven/lifecycle/internal/builder/BuilderNotFoundException
instanceKlass org/apache/maven/lifecycle/NoGoalSpecifiedException
instanceKlass org/apache/maven/lifecycle/MissingProjectException
instanceKlass org/apache/maven/artifact/deployer/ArtifactDeploymentException
instanceKlass org/eclipse/aether/RepositoryException
instanceKlass org/apache/maven/artifact/repository/metadata/RepositoryMetadataReadException
instanceKlass org/apache/maven/artifact/repository/metadata/RepositoryMetadataStoreException
instanceKlass org/codehaus/plexus/personality/plexus/lifecycle/phase/InitializationException
instanceKlass org/apache/maven/wagon/WagonException
instanceKlass org/apache/maven/artifact/InvalidRepositoryException
instanceKlass org/apache/maven/project/ProjectBuildingException
instanceKlass org/apache/maven/artifact/versioning/InvalidVersionSpecificationException
instanceKlass org/apache/maven/repository/legacy/metadata/ArtifactMetadataRetrievalException
instanceKlass org/apache/maven/artifact/repository/metadata/RepositoryMetadataResolutionException
instanceKlass org/apache/maven/plugin/InvalidPluginDescriptorException
instanceKlass org/apache/maven/plugin/MojoNotFoundException
instanceKlass org/apache/maven/plugin/PluginDescriptorParsingException
instanceKlass org/apache/maven/plugin/PluginResolutionException
instanceKlass org/apache/maven/artifact/resolver/AbstractArtifactResolutionException
instanceKlass org/apache/maven/plugin/prefix/NoPluginFoundForPrefixException
instanceKlass org/apache/maven/plugin/version/PluginVersionResolutionException
instanceKlass org/apache/maven/repository/legacy/resolver/conflict/ConflictResolverNotFoundException
instanceKlass org/apache/maven/plugin/AbstractMojoExecutionException
instanceKlass java/security/PrivilegedActionException
instanceKlass java/util/concurrent/TimeoutException
instanceKlass com/google/common/collect/RegularImmutableMap$BucketOverflowException
instanceKlass java/util/concurrent/ExecutionException
instanceKlass java/lang/InterruptedException
instanceKlass com/google/inject/internal/ErrorsException
instanceKlass com/google/inject/internal/InternalProvisionException
instanceKlass org/codehaus/plexus/context/ContextException
instanceKlass java/text/ParseException
instanceKlass org/codehaus/plexus/PlexusContainerException
instanceKlass org/codehaus/plexus/component/repository/exception/ComponentLookupException
instanceKlass org/codehaus/plexus/util/xml/pull/XmlPullParserException
instanceKlass java/lang/CloneNotSupportedException
instanceKlass org/apache/commons/cli/ParseException
instanceKlass org/codehaus/plexus/interpolation/InterpolationException
instanceKlass org/apache/maven/cli/MavenCli$ExitException
instanceKlass org/codehaus/plexus/classworlds/ClassWorldException
instanceKlass org/codehaus/plexus/classworlds/launcher/ConfigurationException
instanceKlass java/io/IOException
instanceKlass java/lang/ReflectiveOperationException
instanceKlass java/lang/RuntimeException
ciInstanceKlass java/lang/Exception 1 1 40 10 7 12 1 1 1 10 12 1 10 12 1 10 12 1 10 12 1 100 1 1 1 1 5 0 1 1 1 1 1 1 1 1 1 1 1 1 1 1
instanceKlass org/apache/maven/artifact/repository/metadata/RepositoryMetadataDeploymentException
instanceKlass org/apache/maven/artifact/repository/metadata/RepositoryMetadataInstallationException
instanceKlass java/lang/Exception
instanceKlass java/lang/Error
ciInstanceKlass java/lang/Throwable 1 1 404 10 7 12 1 1 1 9 7 12 1 1 1 9 12 1 1 9 12 1 9 12 1 1 9 12 1 10 12 1 1 9 12 1 1 10 12 1 1 10 12 1 100 1 7 1 10 8 1 10 12 1 1 8 1 10 100 12 1 1 10 10 12 1 100 1 8 1 10 10 12 1 1 10 7 12 1 1 10 12 1 8 1 9 7 12 1 1 1 10 12 1 1 100 1 10 12 10 12 1 10 100 12 1 1 1 100 1 10 12 10 12 1 10 12 1 100 1 10 10 7 12 1 1 1 11 100 12 1 1 1 10 12 1 1 10 12 1 1 8 1 10 12 1 10 12 1 1 8 1 8 1 10 12 1 1 10 12 1 8 1 8 1 9 12 1 1 10 12 1 1 100 1 10 11 12 1 8 1 8 1 10 7 12 1 1 8 1 10 12 1 8 1 100 1 10 12 1 9 12 1 1 10 12 1 10 7 12 1 9 12 1 1 10 12 1 1 100 1 8 1 10 12 1 10 100 12 1 1 10 12 1 1 7 1 10 100 12 1 1 1 10 12 1 11 7 12 1 1 1 11 7 12 1 1 11 12 1 8 1 10 12 1 1 8 1 10 10 9 100 12 1 1 1 8 1 10 12 1 1 11 10 100 1 8 1 10 11 12 1 1 8 1 9 12 1 10 100 12 1 1 11 9 12 1 1 11 12 1 1 100 10 12 1 10 12 1 1 7 1 1 1 1 5 0 1 1 1 1 1 1 1 1 1 1 1 1 1 7 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 7 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1
staticfield java/lang/Throwable UNASSIGNED_STACK [Ljava/lang/StackTraceElement; 0 [Ljava/lang/StackTraceElement;
staticfield java/lang/Throwable SUPPRESSED_SENTINEL Ljava/util/List; java/util/Collections$EmptyList
staticfield java/lang/Throwable EMPTY_THROWABLE_ARRAY [Ljava/lang/Throwable; 0 [Ljava/lang/Throwable;
staticfield java/lang/Throwable $assertionsDisabled Z 1
instanceKlass java/security/Provider
ciInstanceKlass java/util/Properties 1 1 690 10 7 12 1 1 1 100 1 10 7 12 1 1 7 1 10 12 1 9 12 1 1 9 12 1 1 9 12 1 1 10 7 12 1 1 1 10 12 1 1 8 1 10 7 12 1 1 1 7 1 10 12 1 10 12 1 1 8 1 10 12 1 7 1 10 12 10 12 1 1 9 12 1 1 10 12 1 1 7 1 10 12 1 10 12 1 10 12 1 1 100 1 8 1 10 12 1 10 12 1 10 12 1 1 10 12 1 7 1 3 10 10 100 12 1 1 1 10 12 1 10 12 1 1 8 1 10 12 1 10 12 1 1 8 1 10 100 12 1 1 10 12 1 1 10 12 1 10 12 1 1 100 1 10 12 1 10 12 1 1 100 1 9 100 12 1 1 1 10 12 1 10 12 1 1 10 12 1 1 10 12 1 1 100 1 9 12 1 1 7 1 7 1 10 12 1 7 1 11 7 12 1 1 1 11 12 1 1 11 7 12 1 1 1 11 7 12 1 1 1 11 12 1 1 11 12 1 11 12 1 10 12 1 1 8 1 10 12 1 10 100 12 1 1 10 12 1 100 1 10 10 12 1 10 12 1 100 1 10 10 12 1 1 10 7 12 1 1 9 100 12 1 1 10 12 1 1 10 100 12 1 1 1 100 1 100 1 100 1 10 8 1 8 1 10 12 1 10 12 1 10 12 1 1 10 10 12 1 1 10 12 1 1 100 1 10 10 12 1 11 7 12 1 1 10 7 12 1 1 1 8 1 10 100 12 1 1 11 11 7 1 8 1 10 100 1 11 10 12 1 10 10 12 1 10 12 1 1 10 12 1 1 10 12 1 1 10 12 1 10 12 1 10 10 12 1 10 12 1 10 12 1 10 10 12 1 1 10 12 1 1 10 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 1 10 12 1 1 10 12 1 10 12 1 10 12 1 1 10 12 10 12 1 1 10 12 1 1 10 12 1 10 12 1 1 10 12 1 1 10 12 10 11 12 1 4 11 10 12 1 1 10 100 12 1 1 11 12 1 10 12 1 1 10 100 12 1 1 10 12 1 100 1 8 1 10 12 1 10 10 100 12 1 1 1 100 1 6 0 10 12 1 1 11 100 12 1 1 1 10 12 1 10 12 1 1 1 1 1 5 0 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 1 1 1 1 1 1 1 1 1 1 1 7 1 7 1 1 1 1 1 100 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 7 1 1 1 1 1 1 1 1 1 1 1 100 1 1
staticfield java/util/Properties UNSAFE Ljdk/internal/misc/Unsafe; jdk/internal/misc/Unsafe
instanceKlass java/util/Hashtable
ciInstanceKlass java/util/Dictionary 1 1 36 10 7 12 1 1 1 100 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1
instanceKlass java/util/Properties
ciInstanceKlass java/util/Hashtable 1 1 516 7 1 10 7 12 1 1 1 9 7 12 1 1 1 100 1 7 1 10 8 1 10 12 1 1 10 12 1 10 12 1 1 10 12 1 10 7 12 1 1 1 8 1 10 12 1 9 12 1 1 7 1 9 12 1 1 4 10 7 12 1 1 1 9 12 1 4 10 12 1 11 7 12 1 1 1 10 12 1 1 10 12 1 1 9 12 1 10 12 1 1 100 1 10 9 12 1 1 10 7 12 1 1 1 9 12 1 1 10 12 1 10 12 1 3 9 12 1 9 12 1 3 10 12 1 10 12 1 10 12 1 1 11 12 1 1 11 100 12 1 1 1 11 7 12 1 1 1 11 12 1 100 1 11 12 1 11 12 1 10 12 1 1 10 12 1 1 10 12 1 9 12 1 1 9 12 9 12 1 1 10 100 1 7 1 10 12 1 10 8 1 10 10 12 1 8 1 10 8 1 10 100 12 1 1 1 7 1 10 12 1 10 12 1 100 1 10 12 1 10 12 1 1 100 1 10 100 1 10 10 12 1 1 11 12 1 1 11 12 1 7 1 10 10 10 100 12 1 1 11 100 12 1 1 1 100 1 10 11 100 12 1 1 11 100 12 1 10 12 1 10 12 1 1 10 100 12 1 1 10 12 1 1 10 12 1 1 10 12 1 1 10 100 12 1 1 1 8 10 100 12 1 1 100 1 8 1 10 4 4 10 12 1 1 10 12 1 8 1 4 10 12 10 100 12 1 1 1 100 1 11 100 12 1 1 1 10 100 12 1 1 1 10 12 1 10 12 1 1 10 7 1 7 1 1 1 1 1 1 5 0 1 1 1 1 1 3 1 3 1 3 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 7 1 1 1 1 1 1 1 1 1 1 1 1 1 1 7 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 1 1 7 1 1 1 1 1 7 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1
ciInstanceKlass java/lang/String 1 1 1444 10 7 12 1 1 1 8 1 9 7 12 1 1 1 9 12 1 1 9 12 1 1 9 12 1 1 10 12 1 10 12 1 1 10 12 1 1 9 12 1 10 7 12 1 1 1 10 7 1 10 7 12 1 1 1 10 12 1 1 10 12 1 1 10 12 1 10 12 1 1 10 12 1 10 7 12 1 1 1 7 1 9 7 12 1 1 1 10 7 12 1 1 1 10 7 12 1 1 1 10 12 1 1 10 12 1 1 10 12 1 1 10 12 1 1 9 7 12 1 1 10 12 9 7 12 1 1 10 12 1 1 3 10 12 1 1 7 1 11 12 1 1 11 12 1 11 12 1 1 10 100 12 1 1 1 10 12 1 1 9 100 12 1 1 1 10 12 1 1 10 12 1 11 12 1 1 10 12 1 1 10 12 10 12 1 1 10 7 12 1 1 1 10 12 1 1 10 12 1 1 100 1 7 1 10 12 1 10 12 1 10 12 1 1 100 1 10 12 1 1 100 1 10 12 1 1 10 12 1 1 10 12 1 10 10 12 1 100 1 100 1 7 1 10 12 1 10 12 1 1 10 12 1 1 10 12 1 10 12 1 1 10 12 1 1 10 100 12 1 1 100 1 11 10 7 12 1 1 11 12 1 11 12 1 10 12 1 1 10 12 1 10 12 10 12 1 1 10 10 100 12 1 1 1 10 100 12 1 1 10 12 1 1 10 100 12 1 1 10 12 1 10 12 1 1 10 12 1 1 10 12 1 10 12 1 10 12 1 1 100 1 10 12 1 1 10 12 10 12 1 10 12 1 1 10 12 1 1 10 7 12 1 1 1 10 12 1 10 12 1 1 10 12 1 10 12 1 3 3 10 12 1 1 10 12 1 10 12 1 1 10 12 1 1 10 12 1 1 10 12 1 10 12 1 1 10 12 1 1 10 12 1 10 12 1 10 12 1 10 12 1 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 7 1 10 8 1 10 12 1 1 10 12 1 8 1 10 12 1 1 10 12 10 12 1 8 1 10 10 12 1 1 10 12 1 1 10 12 1 1 10 7 1 10 10 12 1 10 12 1 10 12 1 10 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 1 10 12 1 1 10 12 1 1 10 10 12 1 1 10 10 12 1 100 1 10 10 12 1 1 10 12 1 1 10 7 1 10 12 1 10 12 1 10 7 12 1 1 1 10 12 1 10 12 1 1 10 12 1 11 7 1 11 12 10 12 1 10 12 1 1 10 12 1 1 10 10 12 1 10 12 1 9 12 1 1 11 7 12 1 1 1 10 12 10 10 12 1 10 12 1 1 10 10 12 1 10 12 1 10 12 1 1 10 12 1 1 10 10 12 1 1 10 12 1 10 10 12 1 10 12 10 10 12 10 10 12 1 10 12 1 10 10 12 10 7 12 1 1 1 10 12 10 10 12 10 12 1 10 12 10 12 10 10 12 1 10 12 1 1 10 12 1 1 10 10 12 1 10 7 12 1 1 1 10 12 1 1 10 10 7 12 1 1 1 11 10 12 1 10 12 1 1 10 12 1 1 10 7 12 1 1 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 100 1 7 1 8 1 10 10 10 12 1 10 12 1 1 8 1 10 12 1 3 3 10 12 1 10 12 1 1 10 12 7 1 10 10 12 1 1 10 12 1 10 12 1 10 12 1 1 10 12 1 1 11 7 12 1 1 1 7 1 10 12 1 10 12 1 1 8 1 10 12 1 1 10 12 1 11 7 12 1 1 1 11 7 12 1 1 11 12 1 10 12 1 10 12 1 1 10 10 7 12 1 1 1 10 12 1 10 12 1 10 10 12 10 12 1 1 10 10 12 1 10 10 12 1 10 10 12 1 10 10 12 1 10 12 1 1 10 10 12 1 8 1 10 12 1 1 18 12 1 1 11 100 12 1 1 1 7 1 3 18 12 1 18 12 1 8 1 10 100 12 1 1 1 11 12 1 1 10 12 10 10 12 1 10 11 12 1 1 10 12 1 1 11 12 1 18 3 11 10 12 1 11 11 10 12 1 10 12 1 1 8 1 10 12 1 10 12 1 10 12 1 1 10 10 12 1 11 100 12 1 7 1 100 1 10 12 7 1 10 10 7 12 1 1 1 100 1 10 7 1 10 10 12 1 10 10 12 1 8 1 10 10 12 1 8 1 8 1 10 12 1 10 12 1 10 10 12 10 7 12 1 1 10 7 12 1 1 10 7 12 1 1 8 1 10 12 1 10 12 1 10 9 12 1 10 12 9 7 12 1 1 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 8 1 10 100 12 1 1 1 10 12 10 12 1 1 10 12 10 10 12 10 12 7 1 9 12 1 1 7 1 10 7 1 7 1 7 1 7 1 1 1 1 1 1 5 0 1 1 1 1 1 3 1 3 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 1 1 1 1 1 1 1 1 1 1 7 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 7 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 16 15 10 12 16 15 10 12 15 10 12 15 10 12 15 10 100 12 1 1 1 1 1 1 1 100 1 100 1 1 1
staticfield java/lang/String COMPACT_STRINGS Z 1
staticfield java/lang/String serialPersistentFields [Ljava/io/ObjectStreamField; 0 [Ljava/io/ObjectStreamField;
staticfield java/lang/String CASE_INSENSITIVE_ORDER Ljava/util/Comparator; java/lang/String$CaseInsensitiveComparator
ciMethod java/lang/String equals (Ljava/lang/Object;)Z 1024 0 8776 0 440
ciMethod java/lang/String hashCode ()I 1024 0 10858 0 720
ciInstanceKlass java/lang/constant/ConstantDesc 1 0 37 100 1 100 1 1 1 1 7 1 1 1 1 100 1 100 1 100 1 100 1 100 1 100 1 100 1 100 1 100 1 1 100 1 100 1 1
ciInstanceKlass java/lang/InternalError 0 0 34 10 100 12 1 1 1 10 12 1 10 12 1 10 12 1 100 1 1 1 1 5 0 1 1 1 1 1 1 1 1 1 1 1
instanceKlass com/google/common/util/concurrent/ExecutionError
instanceKlass java/lang/AssertionError
instanceKlass java/io/IOError
instanceKlass org/apache/maven/BuildAbort
instanceKlass java/lang/VirtualMachineError
instanceKlass java/lang/LinkageError
ciInstanceKlass java/lang/Error 1 1 40 10 7 12 1 1 1 10 12 1 10 12 1 10 12 1 10 12 1 100 1 1 1 1 5 0 1 1 1 1 1 1 1 1 1 1 1 1 1 1
instanceKlass java/lang/StackOverflowError
instanceKlass java/lang/OutOfMemoryError
instanceKlass java/lang/InternalError
ciInstanceKlass java/lang/VirtualMachineError 1 1 34 10 100 12 1 1 1 10 12 1 10 12 1 10 12 1 100 1 1 1 1 5 0 1 1 1 1 1 1 1 1 1 1 1
ciInstanceKlass java/util/Set 1 1 144 100 1 10 7 12 1 1 1 9 7 12 1 1 1 7 1 10 12 1 1 10 12 1 7 1 7 1 10 12 1 7 1 7 1 11 7 12 1 1 1 11 12 1 1 7 1 10 12 1 10 12 1 1 11 12 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1
ciInstanceKlass java/util/Iterator 1 1 53 100 1 8 1 10 12 1 1 10 7 12 1 1 1 11 7 12 1 1 1 11 12 1 1 11 7 12 1 1 1 7 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1
ciInstanceKlass java/util/Map$Entry 1 0 178 18 12 1 1 7 1 100 1 18 10 100 12 1 1 1 18 12 1 18 100 1 11 7 12 1 1 1 11 12 1 11 7 12 1 1 1 10 100 12 1 1 1 10 7 12 1 1 1 8 1 10 12 1 1 8 1 8 1 8 1 10 12 1 10 12 1 8 10 7 1 10 12 1 8 10 12 1 8 1 10 12 1 8 10 12 1 8 1 10 12 1 1 8 1 100 1 8 1 10 12 1 1 11 12 7 1 11 12 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 16 15 11 12 16 3 3 15 11 12 15 11 12 15 11 12 15 10 100 12 1 1 1 1 1 100 1 100 1 1
instanceKlass sun/nio/ch/ChannelInputStream
instanceKlass java/util/zip/ZipFile$ZipFileInputStream
instanceKlass java/io/FilterInputStream
instanceKlass java/io/FileInputStream
instanceKlass java/io/ByteArrayInputStream
ciInstanceKlass java/io/InputStream 1 1 195 7 1 10 7 12 1 1 1 100 1 10 10 7 12 1 1 1 10 7 12 1 1 1 10 12 1 100 1 3 10 12 1 1 100 1 8 1 10 12 1 10 100 12 1 1 1 3 7 1 8 1 10 10 100 12 1 1 1 7 1 10 11 7 12 1 1 1 10 12 1 1 11 12 1 1 11 7 12 1 1 1 11 12 1 1 100 1 10 7 12 1 1 1 5 0 10 12 1 10 12 1 1 100 1 10 8 1 10 8 1 8 1 10 12 1 1 10 100 12 1 1 1 7 1 5 0 10 12 1 100 1 7 1 1 1 1 3 1 3 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1
ciInstanceKlass jdk/internal/misc/Unsafe 1 1 1287 10 7 12 1 1 1 9 7 12 1 1 1 9 12 1 1 10 12 1 1 10 7 12 1 1 1 10 12 1 1 10 12 1 1 10 12 1 1 10 12 1 1 10 12 1 1 10 12 1 1 10 12 1 1 10 12 1 1 10 12 1 1 10 12 1 1 10 12 1 1 10 12 1 1 10 12 1 1 10 12 1 10 12 1 100 1 10 10 12 1 1 10 12 1 1 5 0 10 12 1 1 10 12 1 1 10 7 12 1 1 1 10 12 1 1 10 12 1 10 12 1 10 12 1 1 10 12 1 1 10 12 1 10 12 1 7 1 7 1 10 8 1 10 12 1 1 10 12 1 8 1 10 12 1 1 10 12 1 10 12 1 10 12 1 1 10 12 1 10 12 1 1 10 12 1 1 10 12 1 10 12 1 10 12 1 10 12 1 1 10 12 1 10 12 1 10 12 1 1 10 12 1 5 0 5 0 5 0 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 1 10 12 1 10 12 1 7 1 8 1 10 100 1 10 10 12 1 1 10 12 1 1 10 12 1 10 12 1 1 10 12 1 1 10 12 1 10 12 1 1 10 12 1 9 7 12 1 1 9 12 1 100 1 10 10 12 1 1 8 1 10 8 1 8 1 10 12 1 1 9 7 12 1 1 1 9 7 1 9 7 1 9 7 1 9 9 7 1 9 7 1 9 7 1 10 12 1 1 10 12 1 1 10 12 1 1 10 12 1 1 5 0 5 0 9 12 1 1 10 12 1 10 12 1 10 12 1 1 10 12 1 1 10 12 1 8 1 3 10 12 1 1 10 12 1 1 10 12 1 10 12 1 1 10 12 1 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 1 10 12 1 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 1 10 12 1 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 1 10 12 1 1 10 12 1 1 10 12 1 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 1 10 12 1 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 1 10 12 1 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 1 10 12 1 10 12 1 10 12 1 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 100 1 10 100 1 10 9 12 1 5 0 10 12 1 1 5 0 10 12 1 5 0 10 12 1 10 12 1 10 12 1 1 10 12 1 1 10 12 1 10 12 1 10 12 1 10 12 1 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 1 10 12 1 10 12 1 10 12 1 10 12 1 1 10 12 1 10 12 1 10 12 1 1 10 12 1 10 12 1 10 12 1 10 12 1 1 10 12 1 10 10 12 1 1 10 12 1 10 12 1 1 10 12 1 10 12 5 0 5 0 5 0 10 12 1 1 10 12 1 10 12 1 10 12 10 100 12 1 1 8 1 100 1 11 12 1 1 8 1 11 12 1 1 10 100 12 1 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 7 1 10 12 1 9 12 1 7 1 9 12 1 7 1 9 12 1 7 1 9 12 1 7 1 9 12 1 7 1 9 12 1 7 1 9 12 1 7 1 9 12 1 7 1 9 12 1 10 12 1 9 12 1 9 12 1 9 12 1 9 12 1 9 12 1 9 12 1 9 12 1 9 12 1 9 12 1 9 12 1 1 1 3 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 3 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1
staticfield jdk/internal/misc/Unsafe theUnsafe Ljdk/internal/misc/Unsafe; jdk/internal/misc/Unsafe
staticfield jdk/internal/misc/Unsafe ARRAY_BOOLEAN_BASE_OFFSET I 16
staticfield jdk/internal/misc/Unsafe ARRAY_BYTE_BASE_OFFSET I 16
staticfield jdk/internal/misc/Unsafe ARRAY_SHORT_BASE_OFFSET I 16
staticfield jdk/internal/misc/Unsafe ARRAY_CHAR_BASE_OFFSET I 16
staticfield jdk/internal/misc/Unsafe ARRAY_INT_BASE_OFFSET I 16
staticfield jdk/internal/misc/Unsafe ARRAY_LONG_BASE_OFFSET I 16
staticfield jdk/internal/misc/Unsafe ARRAY_FLOAT_BASE_OFFSET I 16
staticfield jdk/internal/misc/Unsafe ARRAY_DOUBLE_BASE_OFFSET I 16
staticfield jdk/internal/misc/Unsafe ARRAY_OBJECT_BASE_OFFSET I 16
staticfield jdk/internal/misc/Unsafe ARRAY_BOOLEAN_INDEX_SCALE I 1
staticfield jdk/internal/misc/Unsafe ARRAY_BYTE_INDEX_SCALE I 1
staticfield jdk/internal/misc/Unsafe ARRAY_SHORT_INDEX_SCALE I 2
staticfield jdk/internal/misc/Unsafe ARRAY_CHAR_INDEX_SCALE I 2
staticfield jdk/internal/misc/Unsafe ARRAY_INT_INDEX_SCALE I 4
staticfield jdk/internal/misc/Unsafe ARRAY_LONG_INDEX_SCALE I 8
staticfield jdk/internal/misc/Unsafe ARRAY_FLOAT_INDEX_SCALE I 4
staticfield jdk/internal/misc/Unsafe ARRAY_DOUBLE_INDEX_SCALE I 8
staticfield jdk/internal/misc/Unsafe ARRAY_OBJECT_INDEX_SCALE I 4
staticfield jdk/internal/misc/Unsafe ADDRESS_SIZE I 8
instanceKlass org/eclipse/sisu/space/CloningClassSpace$CloningClassLoader
instanceKlass jdk/internal/reflect/DelegatingClassLoader
instanceKlass java/security/SecureClassLoader
ciInstanceKlass java/lang/ClassLoader 1 1 1108 9 7 12 1 1 1 10 7 12 1 1 1 9 12 1 1 10 7 12 1 1 1 7 1 10 12 1 1 10 12 1 1 9 12 1 1 10 12 1 1 100 1 10 12 1 10 12 1 1 10 7 12 1 1 1 100 1 8 1 10 12 1 10 7 12 1 1 1 10 7 12 1 10 7 1 10 7 1 7 1 7 1 10 12 1 10 12 1 9 12 1 1 10 10 7 12 1 1 1 9 12 1 1 9 12 1 9 12 1 1 9 12 1 9 12 1 1 9 12 1 1 7 1 10 12 1 9 12 1 1 10 12 1 1 10 7 12 1 1 1 9 12 1 9 12 1 1 9 12 1 10 12 1 1 9 12 10 12 1 1 7 1 10 8 1 10 12 1 1 10 12 1 10 7 1 7 1 10 12 1 1 10 7 12 1 1 1 8 1 10 12 1 10 12 1 10 12 1 1 10 12 1 1 10 12 1 1 10 12 1 1 10 12 1 1 10 12 1 7 1 10 12 1 10 7 12 1 1 1 10 12 1 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 1 10 10 12 1 10 12 1 10 100 12 1 1 10 12 1 1 10 12 1 1 10 12 1 100 1 10 12 1 7 1 10 12 1 10 7 12 1 1 1 10 10 12 1 1 10 12 1 1 7 1 8 1 10 8 1 10 12 1 10 12 1 100 1 8 1 10 12 1 1 10 12 1 1 10 10 12 1 1 10 12 1 1 10 12 1 1 10 7 1 10 12 1 1 10 12 1 1 10 12 1 1 10 12 1 1 10 12 1 1 10 12 1 1 10 12 1 10 7 12 1 1 1 10 12 1 10 12 1 10 12 1 1 10 12 1 10 12 1 10 12 1 10 12 1 1 10 12 1 10 12 1 1 8 1 9 12 1 10 12 1 1 8 1 8 1 10 7 12 1 1 100 1 10 10 12 10 12 1 10 12 1 10 12 1 10 12 1 1 10 7 12 1 1 10 12 1 10 7 1 7 1 10 12 1 1 10 12 1 10 7 1 10 12 1 100 1 18 12 1 10 100 12 1 1 1 10 100 12 1 1 1 10 7 12 1 1 10 12 1 10 12 1 100 1 10 12 1 8 1 10 10 12 1 1 10 12 1 10 12 1 1 100 1 10 12 1 10 12 1 1 10 7 12 1 1 10 12 1 8 1 100 1 10 10 12 1 9 12 1 10 7 12 1 1 10 12 1 7 1 8 1 10 12 1 10 8 1 8 1 10 12 1 1 10 12 1 1 10 12 1 1 10 7 12 1 1 7 1 100 1 10 12 1 1 7 1 7 1 10 7 12 1 1 10 12 1 10 12 1 1 10 12 1 10 12 1 1 10 12 1 1 9 100 12 1 1 1 10 12 1 1 10 12 1 10 12 1 10 12 1 1 10 12 1 8 1 7 1 18 12 1 1 10 12 1 1 10 12 1 10 12 1 10 12 1 8 1 10 12 1 10 12 1 18 12 1 11 100 12 1 1 1 100 1 10 12 1 1 10 12 1 10 11 12 1 1 10 18 10 12 1 1 11 7 12 1 18 12 1 11 12 1 1 10 12 10 12 1 1 10 12 1 1 100 1 8 1 10 10 12 1 8 1 8 1 10 100 12 1 1 10 12 1 100 1 10 10 12 1 8 1 8 1 8 1 10 12 1 10 12 1 1 10 12 1 10 7 12 1 1 1 11 7 12 1 1 100 1 10 11 10 12 1 10 12 1 10 12 1 1 9 7 12 1 1 9 12 1 1 9 12 9 12 1 9 12 1 9 12 1 8 10 12 1 1 10 7 12 1 1 1 10 12 1 1 10 12 1 1 10 12 1 10 10 12 1 11 12 1 1 10 100 12 1 1 1 100 1 10 12 1 10 12 1 10 12 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 1 1 1 1 1 1 1 1 1 100 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 7 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 16 1 15 10 12 16 1 16 15 10 12 16 1 16 1 15 10 12 16 15 10 12 16 15 10 12 16 15 10 100 12 1 1 1 1 1 100 1 100 1 1
staticfield java/lang/ClassLoader nocerts [Ljava/security/cert/Certificate; 0 [Ljava/security/cert/Certificate;
staticfield java/lang/ClassLoader $assertionsDisabled Z 1
ciInstanceKlass java/lang/reflect/Constructor 1 1 439 10 7 12 1 1 1 10 7 12 1 1 9 7 12 1 1 1 10 12 1 1 10 12 1 1 10 7 12 1 1 9 12 1 1 10 7 12 1 1 1 9 12 1 1 9 12 1 1 9 12 1 9 12 1 1 9 12 1 9 12 1 1 9 12 1 1 9 12 1 100 1 8 1 10 12 1 10 12 1 9 12 1 1 10 7 12 1 1 10 7 12 1 1 1 10 12 1 1 10 12 1 1 10 12 1 7 1 100 1 8 1 10 10 12 1 10 12 1 10 12 1 1 10 12 1 1 9 100 12 1 1 1 10 7 12 1 1 10 12 1 1 10 12 1 10 12 1 1 10 7 12 1 1 1 10 100 12 1 1 10 12 1 1 10 12 1 10 7 12 1 1 1 8 1 10 10 12 1 100 1 8 1 10 12 1 10 12 1 1 10 12 1 1 10 12 1 10 12 1 10 12 1 1 10 12 1 1 9 12 1 1 10 12 1 1 10 12 1 1 10 12 1 1 11 7 12 1 1 1 10 12 1 1 10 12 1 10 12 1 10 12 1 8 1 9 12 1 1 10 7 12 1 1 1 10 7 12 1 1 10 12 1 1 10 12 1 1 10 12 1 1 10 12 1 1 10 12 1 9 7 12 1 1 10 12 1 10 12 1 10 12 1 100 1 8 1 10 10 12 1 1 10 12 1 10 10 12 1 1 10 12 1 1 10 100 12 1 1 1 11 100 12 1 1 1 10 12 1 1 9 100 12 1 1 1 10 100 12 1 1 1 10 12 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 100 1 100 1 100 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 1
instanceKlass java/lang/reflect/Executable
instanceKlass java/lang/reflect/Field
ciInstanceKlass java/lang/reflect/AccessibleObject 1 1 400 10 7 12 1 1 1 9 100 12 1 1 1 10 7 12 1 1 1 10 7 12 1 1 10 7 12 1 1 1 10 7 12 1 1 1 10 12 1 1 10 12 1 1 9 12 1 1 7 1 10 7 12 1 1 1 11 12 1 7 1 10 12 1 7 1 100 1 10 12 1 10 12 1 1 10 12 1 1 10 12 1 1 10 12 1 1 7 1 10 7 12 1 1 1 10 12 1 1 11 10 7 12 1 1 1 10 12 1 1 10 12 1 10 12 1 10 12 1 1 10 12 1 8 1 7 1 7 1 10 10 12 1 1 8 1 10 12 1 10 12 1 8 1 8 1 8 1 8 1 8 1 8 1 8 1 8 1 8 1 100 1 10 12 1 10 12 1 9 12 1 1 10 12 1 1 10 12 1 10 7 1 100 1 8 1 10 8 1 10 12 1 8 1 10 12 1 10 12 1 1 10 100 1 8 1 10 11 7 12 1 1 1 10 12 1 1 10 12 1 1 10 12 1 1 9 12 1 1 100 1 10 12 1 7 1 10 12 1 10 12 1 1 10 100 1 10 12 1 10 12 10 12 1 10 12 1 10 12 1 1 10 12 1 9 12 1 10 100 12 1 1 8 1 10 100 12 1 1 1 8 1 10 7 12 1 1 1 9 12 1 7 1 10 7 1 10 10 7 12 1 1 1 7 1 10 10 7 12 1 1 1 7 1 9 12 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1
staticfield java/lang/reflect/AccessibleObject reflectionFactory Ljdk/internal/reflect/ReflectionFactory; jdk/internal/reflect/ReflectionFactory
instanceKlass java/lang/reflect/Constructor
instanceKlass java/lang/reflect/Method
ciInstanceKlass java/lang/reflect/Executable 1 1 581 10 7 12 1 1 1 10 7 12 1 1 1 10 7 12 1 1 1 11 7 12 1 1 1 10 7 12 1 1 1 10 12 1 1 10 7 12 1 1 1 10 7 12 1 1 1 10 12 1 8 1 10 10 12 1 1 10 12 1 1 10 100 12 1 1 1 18 12 1 1 11 100 12 1 1 1 8 1 8 1 8 1 10 100 12 1 1 1 11 12 1 1 7 1 8 1 8 1 10 12 1 7 1 8 1 10 12 1 8 1 11 100 12 1 1 1 7 1 11 7 12 1 1 1 11 12 1 8 1 18 8 1 10 12 1 10 12 1 1 18 8 1 10 12 1 100 1 10 12 1 10 12 1 11 12 1 10 12 1 1 8 1 8 1 10 12 1 1 10 12 1 1 10 10 12 1 9 100 12 1 1 1 10 100 12 1 1 1 10 12 1 10 12 1 1 10 7 12 1 1 10 12 1 10 12 1 10 12 1 10 7 12 1 1 7 1 10 100 12 1 1 1 10 12 1 1 10 7 12 1 1 10 12 1 10 12 1 1 9 7 12 1 1 1 10 100 10 12 1 8 1 10 12 1 10 12 1 3 100 1 8 1 10 12 1 10 12 1 10 10 12 1 10 12 1 1 8 1 8 1 8 1 9 12 1 1 9 12 1 10 12 1 100 1 8 1 10 12 1 10 12 1 10 12 1 1 10 12 1 1 10 12 1 10 12 10 12 1 7 1 10 12 1 10 12 1 1 100 1 10 7 12 1 1 1 7 1 10 7 12 1 1 1 10 12 1 1 11 7 12 1 1 10 12 1 10 100 12 1 1 1 10 12 1 1 9 12 1 10 12 1 1 10 12 1 10 12 1 1 9 7 12 1 1 1 10 7 12 1 1 1 10 12 1 10 12 1 1 9 12 1 10 10 10 10 100 12 1 1 1 10 12 1 9 12 1 10 12 1 1 9 12 1 7 1 7 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 7 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 7 1 1 16 15 16 1 16 1 15 10 12 16 15 10 100 12 1 1 1 1 1 1 100 1 1 100 1 100 1 1
ciInstanceKlass java/lang/reflect/Member 1 1 37 100 1 10 12 1 1 100 1 100 1 1 1 1 3 1 3 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1
instanceKlass java/util/logging/LogManager$Cleaner
instanceKlass org/apache/maven/shared/utils/logging/MessageUtils$1
instanceKlass jdk/internal/misc/InnocuousThread
instanceKlass java/util/concurrent/ForkJoinWorkerThread
instanceKlass java/lang/ref/Finalizer$FinalizerThread
instanceKlass java/lang/ref/Reference$ReferenceHandler
instanceKlass java/lang/BaseVirtualThread
ciInstanceKlass java/lang/Thread 1 1 870 10 7 12 1 1 1 9 12 1 1 10 100 12 1 1 10 12 1 10 100 12 1 1 100 1 8 1 10 12 1 1 9 12 1 9 12 1 1 9 12 1 1 7 1 10 12 1 1 10 12 1 10 100 12 1 1 1 10 12 9 12 1 1 10 12 1 7 1 10 12 1 100 1 8 1 10 9 100 12 1 1 1 10 12 1 1 10 12 1 1 10 12 1 1 10 12 1 10 12 1 1 3 8 1 7 1 5 0 10 7 12 1 1 1 9 12 1 10 12 1 1 10 7 12 1 1 1 10 7 12 1 1 1 10 12 1 1 10 12 1 1 9 12 1 1 10 7 1 8 1 10 7 1 10 12 1 9 12 1 1 10 7 12 1 1 1 10 10 12 1 1 9 100 12 1 1 1 10 12 1 1 10 12 1 1 10 7 12 1 1 10 12 1 10 12 1 10 7 12 1 1 9 12 1 10 7 12 1 1 1 10 12 1 1 9 12 1 1 9 12 1 1 10 7 12 1 1 1 9 12 1 1 10 100 12 1 1 10 100 12 1 1 1 10 12 1 10 12 1 10 7 12 1 1 9 12 1 8 1 9 7 12 1 1 9 12 1 1 5 0 100 1 10 100 1 10 100 1 10 7 1 10 8 1 10 12 1 1 10 7 12 1 10 12 1 10 12 1 100 1 8 1 10 10 12 1 10 12 1 1 10 100 12 1 1 1 10 100 12 1 1 1 10 12 1 7 1 9 12 1 1 100 1 10 10 12 1 9 12 1 1 10 12 1 1 10 12 1 1 10 12 1 10 12 1 9 12 1 1 10 10 12 1 1 10 12 1 1 11 7 12 1 1 10 7 12 1 1 9 12 1 9 12 1 1 9 12 1 1 10 100 12 1 1 10 12 1 1 9 7 12 1 1 1 10 7 12 1 1 10 12 1 10 12 1 100 1 10 10 12 9 12 1 1 10 12 1 11 100 12 1 1 10 12 1 10 12 1 10 12 1 9 12 1 10 10 12 1 10 12 1 1 9 12 1 9 12 10 12 1 8 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 1 10 12 1 10 12 1 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 1 9 7 12 1 1 1 8 1 10 9 12 1 10 12 1 7 1 8 1 10 10 12 1 8 1 10 12 1 1 9 12 10 12 8 1 10 10 12 1 10 12 1 8 1 10 12 1 10 8 1 10 100 12 1 1 10 12 1 1 100 1 8 1 10 9 12 1 9 12 1 1 10 12 1 1 10 10 12 1 10 12 1 100 10 7 12 1 1 1 9 12 1 10 12 1 1 10 12 1 1 10 100 12 1 1 1 11 7 12 1 1 1 9 100 12 1 1 1 10 100 12 1 1 1 7 1 10 12 1 100 1 10 12 1 10 12 1 1 10 12 1 1 8 1 9 12 1 10 12 1 10 12 1 1 11 7 12 1 1 1 10 12 1 1 1 1 3 1 3 1 3 1 3 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 7 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 7 1 7 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 100 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 100 1 100 1 100 1 100 1 1 100 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1
staticfield java/lang/Thread NEW_THREAD_BINDINGS Ljava/lang/Object; java/lang/Class
staticfield java/lang/Thread EMPTY_STACK_TRACE [Ljava/lang/StackTraceElement; 0 [Ljava/lang/StackTraceElement;
ciInstanceKlass java/lang/Runnable 1 0 11 100 1 100 1 1 1 1 1 1 1
ciInstanceKlass java/net/URL 1 1 771 10 7 12 1 1 1 10 12 1 10 7 12 1 1 9 12 1 1 9 12 1 10 7 12 1 1 1 10 12 1 1 10 12 1 1 9 12 1 1 10 7 12 1 1 1 8 1 10 12 1 1 7 1 10 10 12 1 1 8 1 10 12 1 1 9 12 1 100 1 8 1 10 12 1 10 12 1 8 1 9 12 1 10 12 1 1 9 12 1 10 12 1 10 12 1 9 12 1 9 12 1 8 1 9 12 1 10 12 1 1 8 1 9 12 1 1 10 12 1 1 10 7 12 1 1 1 8 1 10 12 1 7 1 10 12 1 10 12 1 10 12 1 10 12 1 1 10 12 1 1 8 1 10 12 1 1 10 12 1 8 1 9 12 1 8 1 10 12 1 10 7 12 1 1 1 7 1 10 12 1 10 12 1 1 10 7 12 1 1 1 100 1 8 1 10 10 12 1 8 1 10 12 1 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 8 1 10 12 1 8 1 10 10 100 1 10 10 12 1 8 1 10 7 12 1 1 1 10 12 1 9 100 12 1 1 1 10 7 12 1 1 1 9 12 1 1 10 12 1 10 100 12 1 1 1 100 1 100 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 10 10 12 1 10 12 1 10 12 1 1 8 1 9 100 12 1 1 1 10 100 12 1 1 1 10 12 1 1 9 100 12 1 1 1 10 12 1 1 100 1 10 12 1 10 12 1 10 10 12 1 1 10 12 1 1 10 12 1 10 12 1 10 12 1 10 7 12 1 1 1 10 12 1 1 10 12 1 9 12 1 1 9 12 1 1 7 1 8 1 10 10 12 1 9 12 1 1 10 7 12 1 1 8 1 10 7 12 1 1 8 1 10 12 1 1 10 12 1 8 1 8 1 10 7 12 1 1 1 7 1 10 7 12 1 1 1 10 12 1 10 12 1 7 1 10 9 7 12 1 1 1 10 7 12 1 1 10 12 1 1 10 12 1 8 1 7 1 10 10 7 12 1 1 1 10 12 1 8 9 100 12 1 1 1 10 12 1 1 10 12 1 10 12 1 1 10 7 12 1 1 11 7 12 1 1 10 12 1 10 12 1 9 12 1 10 12 1 1 10 100 12 1 1 10 100 12 1 1 1 8 10 100 12 1 1 100 1 10 8 8 10 12 1 8 8 8 100 1 10 12 1 9 12 1 1 10 12 1 10 12 1 1 10 12 1 10 12 10 12 1 1 10 12 1 1 10 10 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 10 12 1 100 1 8 1 10 10 10 12 1 1 10 12 1 10 12 1 1 8 1 7 1 10 10 7 1 10 12 1 9 7 12 1 1 1 9 12 1 1 7 1 10 10 7 12 1 1 1 7 1 1 1 1 1 5 0 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 7 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1
staticfield java/net/URL defaultFactory Ljava/net/URLStreamHandlerFactory; java/net/URL$DefaultFactory
staticfield java/net/URL streamHandlerLock Ljava/lang/Object; java/lang/Object
staticfield java/net/URL serialPersistentFields [Ljava/io/ObjectStreamField; 7 [Ljava/io/ObjectStreamField;
ciMethod java/lang/System arraycopy (Ljava/lang/Object;ILjava/lang/Object;II)V 256 0 128 0 -1
ciInstanceKlass java/lang/Module 1 1 1070 10 7 12 1 1 1 9 7 12 1 1 1 10 7 12 1 1 1 9 12 1 9 12 1 1 9 12 1 1 10 12 1 1 10 12 1 10 12 1 1 10 7 12 1 1 1 7 1 10 7 12 1 1 1 10 12 1 1 11 7 12 1 1 1 10 12 1 1 10 7 12 1 1 1 10 12 1 1 10 7 12 1 1 1 9 100 12 1 1 1 10 7 12 1 1 1 10 12 1 8 1 10 7 12 1 1 1 10 7 12 1 1 1 10 7 12 1 1 1 10 12 1 10 12 1 9 12 1 1 10 100 12 1 1 100 1 7 1 10 8 1 10 12 1 1 10 12 1 10 12 10 12 1 10 7 12 1 1 8 1 8 1 10 8 1 8 1 9 12 1 1 8 1 10 100 12 1 1 1 10 12 1 9 12 1 1 11 12 1 9 7 12 1 1 10 7 12 1 1 1 10 100 12 1 1 1 10 12 1 1 8 1 10 12 1 1 10 12 1 10 12 1 1 9 7 12 1 1 1 10 12 1 1 10 12 1 1 9 12 1 10 12 1 10 12 1 9 12 1 1 11 7 12 1 1 10 12 1 1 9 12 1 9 12 1 10 12 1 10 12 1 100 1 8 1 10 10 12 1 1 10 12 1 8 1 10 12 1 1 8 1 8 1 10 12 1 1 10 12 1 10 12 1 1 18 12 1 1 10 12 1 1 11 12 1 9 12 1 11 12 10 100 12 1 1 100 1 8 1 10 11 12 1 1 10 7 12 1 1 1 10 12 1 10 12 1 1 11 12 1 1 11 7 12 1 1 11 12 1 1 9 12 1 11 12 1 10 12 1 1 10 12 1 1 9 12 1 10 12 10 7 12 1 1 10 7 1 18 12 1 1 11 100 12 1 1 1 18 12 1 11 12 1 1 10 100 12 1 1 1 11 12 1 1 10 7 12 1 1 7 1 11 12 1 7 1 7 1 10 12 1 10 7 12 1 1 1 10 11 7 12 1 8 1 10 10 12 1 1 10 7 12 1 1 10 12 1 10 12 1 7 1 10 12 1 10 11 12 1 1 10 12 10 12 1 1 9 12 1 1 100 1 10 10 12 1 1 11 7 1 10 12 1 1 11 12 1 10 10 12 1 11 10 12 1 1 10 12 1 1 10 12 1 1 10 7 12 1 1 10 12 1 10 12 1 1 10 10 12 1 1 10 12 1 18 12 1 11 12 1 18 12 1 10 12 1 10 12 1 10 12 7 1 10 12 1 10 12 1 10 12 1 9 12 1 7 1 10 10 10 10 12 1 10 12 1 10 12 1 10 12 1 1 10 12 1 1 10 12 1 9 12 1 18 12 1 1 10 7 12 1 1 1 100 1 8 1 10 12 1 1 10 12 1 1 10 7 12 1 1 7 1 10 12 1 1 7 1 8 1 10 12 1 1 100 1 11 12 1 1 10 100 12 1 1 1 18 12 1 1 11 100 12 1 1 1 100 1 10 12 1 10 12 1 1 7 1 7 1 10 12 8 1 10 12 1 1 10 12 1 1 10 100 12 1 1 10 12 1 1 10 12 1 10 12 10 12 1 1 7 1 10 10 12 1 1 10 7 12 1 1 1 100 1 10 12 1 1 10 7 12 1 1 8 1 18 12 1 1 100 1 100 1 9 12 1 1 9 12 1 9 12 1 11 100 12 1 1 1 100 1 11 12 1 1 100 1 10 12 1 8 1 10 12 1 10 12 10 12 1 8 1 10 10 100 12 1 1 7 1 10 10 12 1 10 7 12 1 1 9 12 1 9 12 1 9 12 1 9 12 1 10 12 11 12 1 10 12 1 7 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 100 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 100 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 16 15 10 12 16 16 15 10 12 16 16 15 10 16 1 15 10 12 16 1 15 10 12 16 1 16 15 10 12 16 16 1 15 10 12 16 15 10 7 12 1 1 1 15 10 100 12 1 1 1 1 1 1 1 1 1 1 1 100 1 100 1 1 100 1 100 1 1
staticfield java/lang/Module ALL_UNNAMED_MODULE Ljava/lang/Module; java/lang/Module
staticfield java/lang/Module ALL_UNNAMED_MODULE_SET Ljava/util/Set; java/util/ImmutableCollections$Set12
staticfield java/lang/Module EVERYONE_MODULE Ljava/lang/Module; java/lang/Module
staticfield java/lang/Module EVERYONE_SET Ljava/util/Set; java/util/ImmutableCollections$Set12
staticfield java/lang/Module $assertionsDisabled Z 1
ciInstanceKlass java/lang/StringLatin1 1 1 392 7 1 10 7 12 1 1 1 10 12 1 1 10 7 12 1 1 1 10 7 12 1 1 1 10 12 1 10 7 12 1 1 1 10 12 1 1 10 7 12 1 1 1 10 7 12 1 1 1 10 12 1 1 10 12 1 1 10 10 12 1 10 12 1 10 9 7 12 1 1 1 10 12 1 1 10 7 12 1 1 1 10 12 10 12 1 1 10 12 1 1 10 12 1 10 12 1 1 10 12 1 1 10 7 12 1 1 1 10 12 1 10 12 1 1 9 12 1 1 100 1 10 10 12 1 1 10 7 12 1 1 1 10 12 1 10 12 1 100 1 7 1 8 1 10 12 1 8 1 10 12 1 1 100 1 10 10 12 10 7 12 1 1 1 8 1 8 1 8 1 10 12 1 1 10 100 12 1 1 10 12 1 10 12 1 1 10 12 1 1 10 12 1 1 10 12 1 10 12 10 12 10 12 1 10 12 10 10 12 1 10 12 1 10 12 1 10 100 12 1 1 1 10 100 12 1 1 1 10 12 1 1 10 12 1 1 10 10 7 12 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 1 1 1
staticfield java/lang/StringLatin1 $assertionsDisabled Z 1
ciInstanceKlass java/lang/Math 1 1 460 7 1 10 7 12 1 1 1 10 7 12 1 1 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 6 0 6 0 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 7 12 1 1 1 100 1 3 3 3 10 7 12 1 1 1 100 1 5 0 5 0 5 0 5 0 5 0 9 100 12 1 1 1 10 100 12 1 1 1 100 1 8 1 10 12 1 8 1 10 12 1 1 10 12 1 1 7 1 5 0 5 0 7 1 3 5 0 3 5 0 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 8 1 10 12 1 8 1 10 12 1 1 10 12 1 1 9 12 1 1 9 12 1 100 1 7 1 10 10 12 1 1 8 1 10 12 1 10 12 1 1 10 10 12 1 10 12 1 10 12 1 10 12 1 1 8 1 8 1 10 12 1 1 10 12 1 10 12 10 12 10 12 1 10 12 1 10 12 1 10 12 1 10 12 6 0 10 12 1 9 12 1 1 100 1 10 10 12 1 100 1 10 12 1 10 12 1 1 10 12 1 10 12 1 10 12 10 12 1 1 10 12 1 1 10 12 1 1 10 12 6 0 10 12 1 1 10 12 10 12 1 4 10 12 1 10 12 1 10 12 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 5 0 6 0 4 6 0 4 6 0 4 10 12 1 9 12 1 1 10 12 9 12 1 10 7 12 1 1 1 4 6 0 1 1 6 0 1 6 0 1 6 0 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1
staticfield java/lang/Math negativeZeroFloatBits J -2147483648
staticfield java/lang/Math negativeZeroDoubleBits J -9223372036854775808
staticfield java/lang/Math $assertionsDisabled Z 1
ciInstanceKlass jdk/internal/util/ArraysSupport 1 1 378 7 1 7 1 8 1 10 12 1 1 10 12 1 1 10 7 12 1 1 9 7 12 1 1 1 9 12 1 1 10 7 12 1 1 1 9 12 1 1 10 7 12 1 1 1 9 12 1 10 12 9 12 1 10 12 1 1 10 12 7 1 10 12 1 1 10 12 1 100 1 10 12 1 1 10 12 100 1 10 12 1 100 1 10 12 1 100 1 7 1 10 8 1 10 12 1 1 10 12 1 10 12 1 1 10 9 12 1 1 11 100 12 1 1 1 9 12 1 9 12 1 10 12 1 1 9 12 1 9 12 1 9 12 1 9 12 1 9 12 1 9 12 1 9 12 1 10 12 1 1 10 7 12 1 1 1 9 12 1 9 12 1 10 12 1 1 10 12 1 9 12 1 10 12 1 10 7 12 1 1 1 9 12 1 9 12 1 10 12 1 10 12 1 10 7 12 1 1 1 3 10 12 1 7 1 8 1 8 1 8 1 10 10 100 12 1 1 1 11 7 12 1 1 1 10 12 1 10 7 12 1 1 1 10 12 1 1 10 12 1 1 9 12 1 10 12 1 9 12 1 9 12 1 9 12 1 9 12 1 9 12 1 9 12 1 9 12 1 7 1 10 7 12 1 1 1 1 1 3 1 3 1 3 1 3 1 3 1 3 1 3 1 3 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 1 1 1
staticfield jdk/internal/util/ArraysSupport U Ljdk/internal/misc/Unsafe; jdk/internal/misc/Unsafe
staticfield jdk/internal/util/ArraysSupport BIG_ENDIAN Z 0
staticfield jdk/internal/util/ArraysSupport LOG2_ARRAY_BOOLEAN_INDEX_SCALE I 0
staticfield jdk/internal/util/ArraysSupport LOG2_ARRAY_BYTE_INDEX_SCALE I 0
staticfield jdk/internal/util/ArraysSupport LOG2_ARRAY_CHAR_INDEX_SCALE I 1
staticfield jdk/internal/util/ArraysSupport LOG2_ARRAY_SHORT_INDEX_SCALE I 1
staticfield jdk/internal/util/ArraysSupport LOG2_ARRAY_INT_INDEX_SCALE I 2
staticfield jdk/internal/util/ArraysSupport LOG2_ARRAY_LONG_INDEX_SCALE I 3
staticfield jdk/internal/util/ArraysSupport LOG2_ARRAY_FLOAT_INDEX_SCALE I 2
staticfield jdk/internal/util/ArraysSupport LOG2_ARRAY_DOUBLE_INDEX_SCALE I 3
staticfield jdk/internal/util/ArraysSupport LOG2_BYTE_BIT_SIZE I 3
staticfield jdk/internal/util/ArraysSupport JLA Ljdk/internal/access/JavaLangAccess; java/lang/System$2
ciInstanceKlass java/lang/Character 1 1 604 7 1 7 1 100 1 9 12 1 1 8 1 9 12 1 1 7 1 9 12 1 1 10 7 12 1 1 1 10 100 12 1 1 1 10 100 12 1 1 1 10 7 12 1 1 1 9 7 12 1 1 1 10 12 1 10 12 1 1 10 12 1 1 10 7 12 1 1 10 12 1 1 3 3 3 3 3 10 12 1 1 10 12 1 3 11 7 12 1 1 1 11 12 1 1 10 12 1 1 10 12 1 1 100 1 10 10 12 1 3 10 12 1 1 10 12 1 10 12 1 1 100 1 8 1 10 12 1 1 10 12 1 10 12 1 10 12 1 10 100 12 1 1 1 10 12 1 10 12 1 10 12 1 1 10 12 1 10 7 12 1 1 10 10 12 1 10 10 12 1 10 12 1 1 10 12 1 10 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 10 10 12 10 10 12 1 10 10 12 1 10 10 12 1 10 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 10 12 1 10 10 12 1 10 10 12 1 1 10 10 12 1 10 5 0 10 12 1 10 12 1 10 10 12 1 10 10 12 1 1 10 10 12 1 10 10 12 1 9 12 1 1 100 1 10 10 12 1 10 12 1 1 3 10 100 12 1 1 1 10 12 1 10 100 12 1 1 7 1 10 10 12 1 1 10 12 1 1 10 12 1 1 8 1 10 12 1 9 100 12 1 1 1 10 12 1 10 10 12 1 10 12 1 1 10 12 1 10 10 12 1 1 10 10 12 1 1 7 1 8 1 10 12 1 1 10 7 12 1 1 1 8 1 10 12 1 1 9 12 1 1 7 1 7 1 7 1 1 1 1 3 1 3 1 3 1 3 1 1 1 1 1 3 1 1 3 1 3 1 3 1 3 1 3 1 3 1 3 1 3 1 3 1 3 1 3 1 3 1 3 1 3 1 3 1 3 1 3 1 3 1 3 1 3 1 3 1 3 1 3 1 3 1 3 1 3 1 3 1 3 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 3 1 1 1 1 1 1 1 3 1 1 3 1 1 1 1 1 3 1 1 5 0 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 100 1 1 1 1 1 1
staticfield java/lang/Character TYPE Ljava/lang/Class; java/lang/Class
staticfield java/lang/Character $assertionsDisabled Z 1
ciInstanceKlass java/util/Arrays 1 1 1029 10 7 12 1 1 1 10 100 12 1 1 1 10 7 12 1 1 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 100 12 1 1 1 100 1 7 1 10 8 1 10 12 1 1 10 12 1 8 1 8 1 10 12 1 1 10 12 1 100 1 10 12 1 9 100 12 1 1 1 10 7 12 1 1 100 1 10 12 1 1 10 7 12 1 1 10 7 12 1 1 1 100 1 10 12 1 10 12 1 1 7 1 9 7 12 1 1 1 10 12 1 1 10 7 12 1 1 10 12 1 10 12 1 1 10 12 1 10 12 1 1 7 1 11 12 1 1 10 12 1 10 7 12 1 1 1 10 12 10 12 1 10 12 1 10 12 10 12 1 11 7 12 1 1 1 10 7 12 1 1 1 100 1 10 12 1 10 100 1 10 12 1 10 100 1 10 12 1 10 100 1 10 12 1 10 10 12 1 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 7 12 1 1 1 10 12 1 10 7 12 1 1 1 10 12 1 10 12 1 10 7 12 1 1 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 1 10 12 1 1 10 7 12 1 1 1 10 7 1 10 100 1 10 100 1 10 100 1 10 100 1 10 100 1 10 100 1 10 100 1 10 12 1 8 1 10 12 1 1 10 12 1 1 10 12 1 1 10 12 1 1 10 12 1 1 10 12 1 1 10 12 1 1 10 12 1 1 10 12 1 1 7 1 10 12 10 12 1 1 10 12 1 10 12 1 1 10 12 1 1 9 7 12 1 1 1 10 12 1 9 7 1 10 12 1 9 7 1 10 12 1 9 7 1 10 12 1 9 7 1 10 12 1 9 7 1 10 12 1 9 10 12 1 10 12 1 10 12 1 9 12 1 100 1 10 10 12 1 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 8 1 8 1 10 12 1 10 12 1 8 1 10 12 1 10 12 1 10 12 1 10 7 12 1 1 1 3 10 100 1 10 10 12 1 1 11 100 12 1 1 10 12 1 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 11 12 1 8 1 10 11 12 1 11 7 12 1 1 1 11 100 12 1 1 1 11 12 1 1 18 12 1 1 11 12 1 1 11 100 12 1 1 1 18 12 1 11 100 12 1 1 1 18 12 1 11 100 12 1 1 1 18 12 1 100 1 10 7 12 1 1 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 1 10 12 1 10 7 12 1 1 10 12 1 10 12 1 10 12 1 1 10 12 1 10 12 1 10 12 1 1 10 12 1 10 12 1 10 12 1 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 10 12 1 10 12 10 12 10 12 1 10 12 10 12 1 10 12 1 10 12 1 1 1 1 3 1 3 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 1 16 15 10 12 15 10 12 15 10 12 15 10 12 15 10 100 12 1 1 1 1 1 100 1 100 1 1 1 1 100 1 1 1 1 1 1 100 1 1 100 1 1 100 1 1 1 100 1 100 1 1
staticfield java/util/Arrays $assertionsDisabled Z 1
ciInstanceKlass java/lang/OutOfMemoryError 1 1 26 10 100 12 1 1 1 10 12 1 100 1 1 1 1 5 0 1 1 1 1 1 1 1 1 1
ciMethod java/lang/StringLatin1 equals ([B[B)Z 936 604 4059 0 -1
ciMethod java/lang/StringLatin1 hashCode ([B)I 782 0 3567 0 0
ciInstanceKlass java/lang/StringUTF16 1 1 604 7 1 7 1 10 7 12 1 1 1 100 1 10 7 1 3 7 1 7 1 10 8 1 10 12 1 1 10 12 1 8 1 10 12 1 1 10 12 1 9 12 1 1 10 12 1 1 100 1 8 1 10 12 1 9 12 1 1 9 12 1 10 10 12 1 1 10 12 1 1 10 7 12 1 1 1 10 12 1 10 12 1 1 10 12 1 1 10 12 1 1 10 12 1 1 10 12 1 10 12 1 1 10 12 1 1 10 12 1 1 10 12 1 1 10 12 1 10 12 1 10 12 1 1 10 12 1 100 1 10 12 1 10 10 12 1 1 10 12 1 10 12 1 1 10 12 1 10 7 12 1 1 1 10 100 12 1 1 1 10 12 10 12 1 1 10 12 1 10 12 1 1 10 12 1 1 10 12 1 10 12 1 10 12 1 100 1 10 12 1 1 10 12 1 3 10 12 1 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 1 9 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 10 12 10 12 10 10 12 1 1 10 100 12 1 1 1 10 12 1 10 12 1 100 1 8 1 8 1 10 12 1 1 100 1 10 10 7 12 1 1 1 10 100 12 1 1 8 1 8 1 8 1 10 12 1 1 10 12 1 1 10 12 10 12 1 10 100 12 1 1 10 12 1 1 10 12 1 1 10 12 1 10 12 10 12 10 12 1 10 12 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 100 12 1 1 1 10 100 12 1 1 1 10 12 1 1 10 12 1 1 11 7 12 1 1 10 12 1 10 12 1 10 12 1 1 9 12 1 1 9 12 1 5 0 5 0 10 12 1 10 12 10 12 10 7 12 1 1 1 10 12 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 100 1 1 1 1 1
staticfield java/lang/StringUTF16 HI_BYTE_SHIFT I 0
staticfield java/lang/StringUTF16 LO_BYTE_SHIFT I 8
staticfield java/lang/StringUTF16 $assertionsDisabled Z 1
ciInstanceKlass java/lang/Integer 1 1 453 7 1 7 1 7 1 7 1 10 12 1 1 9 12 1 1 9 12 1 1 10 100 12 1 1 1 10 12 1 1 10 100 12 1 1 1 10 10 12 1 1 10 7 12 1 1 1 10 12 1 10 12 1 1 10 7 12 1 1 1 10 12 1 1 10 12 1 1 10 12 1 10 12 1 10 12 1 1 10 10 12 1 9 12 1 1 9 12 1 7 1 8 1 10 12 1 7 1 10 12 1 8 1 10 12 1 1 10 12 1 8 1 10 12 1 8 1 10 12 1 1 3 10 12 1 1 3 10 12 1 1 10 12 1 1 10 7 12 1 1 1 11 7 1 10 12 1 1 11 10 12 1 1 8 1 10 12 1 1 8 1 7 1 10 12 1 1 10 12 1 1 5 0 8 1 10 12 1 10 12 1 10 12 1 10 12 1 1 7 1 9 12 1 1 9 12 1 1 10 12 1 10 7 1 9 12 1 10 12 1 10 12 1 10 12 1 1 10 7 12 1 1 1 100 1 100 1 10 12 1 1 10 12 1 1 8 1 8 1 10 12 1 1 8 1 8 1 8 1 8 1 8 1 8 1 10 12 1 10 12 1 5 0 3 3 3 3 10 12 1 10 12 1 3 10 12 1 10 100 12 1 1 1 10 12 1 1 10 12 1 1 8 1 10 7 12 1 1 1 9 12 1 1 7 1 7 1 7 1 1 1 1 3 1 1 1 3 1 3 1 1 5 0 1 1 1 1 1 1 1 1 1 100 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 3 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 7 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 7 1 1 1 1 1 1 1 1 1 100 1 100 1 1
staticfield java/lang/Integer TYPE Ljava/lang/Class; java/lang/Class
staticfield java/lang/Integer digits [C 36
staticfield java/lang/Integer DigitTens [B 100
staticfield java/lang/Integer DigitOnes [B 100
instanceKlass java/util/concurrent/atomic/AtomicLong
instanceKlass java/util/concurrent/atomic/AtomicInteger
instanceKlass java/lang/Long
instanceKlass java/lang/Integer
instanceKlass java/lang/Short
instanceKlass java/lang/Byte
instanceKlass java/lang/Double
instanceKlass java/lang/Float
ciInstanceKlass java/lang/Number 1 1 37 10 7 12 1 1 1 10 100 12 1 1 1 100 1 1 1 1 5 0 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1
ciMethod java/lang/StringUTF16 hashCode ([B)I 0 0 1 0 -1
ciInstanceKlass java/lang/Thread$FieldHolder 1 1 48 10 7 12 1 1 1 9 7 12 1 1 1 9 12 1 1 9 12 1 1 9 12 1 1 9 12 1 1 1 1 1 1 1 1 1 1 100 1 100 1 1 1 1 100 1 1 1
ciInstanceKlass java/lang/Thread$Constants 0 0 59 7 1 10 7 12 1 1 1 100 1 10 10 7 12 1 1 1 7 1 8 1 10 12 1 9 7 12 1 1 1 7 1 7 1 10 12 1 10 12 1 9 12 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1
ciInstanceKlass java/lang/ThreadGroup 1 1 411 10 7 12 1 1 1 9 7 12 1 1 1 8 1 9 12 1 1 7 1 9 12 1 1 9 12 1 1 10 7 12 1 1 1 10 12 1 1 10 12 1 10 12 1 10 12 1 10 12 1 1 10 12 1 1 10 12 1 10 12 1 1 10 12 1 10 12 1 10 100 12 1 1 1 10 12 1 1 18 12 1 1 11 7 12 1 1 1 10 7 12 1 1 1 10 7 12 1 10 12 1 1 10 12 1 1 10 12 1 1 10 100 12 1 1 1 10 12 1 11 12 1 1 11 7 12 1 1 11 12 1 1 10 12 1 1 10 12 1 10 12 1 11 12 1 11 12 1 1 100 1 10 10 12 1 100 1 10 18 12 1 1 11 7 12 1 1 1 11 12 1 1 9 12 1 1 10 12 1 1 8 1 10 7 12 1 1 1 10 100 12 1 1 1 10 12 1 1 11 12 10 12 1 1 10 12 1 1 11 7 1 9 12 1 7 1 10 8 1 10 12 1 1 10 12 1 1 8 1 10 12 1 10 7 12 1 1 1 10 12 1 1 10 7 1 8 1 10 8 1 10 12 1 10 12 1 8 1 9 12 1 1 9 12 1 10 100 12 1 1 1 100 9 12 1 1 7 1 9 12 1 10 12 10 12 1 1 100 10 12 9 12 1 10 12 1 100 1 10 11 12 1 1 7 1 10 10 12 1 10 12 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 3 1 1 1 1 1 1 1 1 1 1 1 1 100 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 16 15 10 12 16 16 15 10 12 16 15 10 100 12 1 1 1 1 1 100 1 100 1 1
staticfield java/lang/ThreadGroup $assertionsDisabled Z 1
ciInstanceKlass java/lang/Thread$UncaughtExceptionHandler 1 0 16 100 1 100 1 1 1 1 1 1 1 1 100 1 1 1
ciInstanceKlass java/security/AccessControlContext 1 1 374 9 7 12 1 1 1 9 12 1 1 10 100 12 1 1 1 8 1 10 100 12 1 1 1 10 7 12 1 1 1 9 12 1 9 12 1 1 9 12 1 1 10 7 12 1 1 7 1 10 12 1 11 7 12 1 1 1 11 12 1 11 12 1 11 12 1 1 7 1 11 12 1 1 10 12 1 10 7 12 1 1 1 9 100 12 1 1 1 10 7 12 1 1 1 11 100 12 1 1 1 10 7 1 100 1 8 1 10 12 1 10 12 1 1 7 1 10 7 12 1 1 1 9 12 1 9 12 1 9 12 1 9 12 1 1 9 12 1 1 9 12 1 9 12 1 10 7 12 1 1 1 9 12 1 10 12 1 1 10 12 1 1 8 1 10 12 1 1 10 12 1 1 10 7 12 1 1 1 7 1 10 10 12 1 1 10 7 12 1 1 1 10 8 1 10 7 12 1 1 8 1 10 7 12 1 1 8 1 8 1 10 12 1 8 1 10 12 1 8 1 10 12 1 10 12 1 10 12 1 1 8 1 8 1 100 1 10 12 1 10 12 1 1 100 1 10 12 1 8 1 10 12 1 10 12 1 10 8 1 10 12 1 1 10 12 1 1 10 12 1 1 10 12 1 1 10 12 1 10 12 1 11 10 12 1 10 12 1 1 10 10 10 12 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 3 1 1
instanceKlass java/lang/ThreadBuilders$BoundVirtualThread
instanceKlass java/lang/VirtualThread
ciInstanceKlass java/lang/BaseVirtualThread 0 0 36 10 100 12 1 1 1 100 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 100 1 1 100 1 1
ciInstanceKlass java/lang/VirtualThread 0 0 907 9 7 12 1 1 1 10 7 12 1 1 1 10 100 12 1 1 1 10 7 12 1 1 1 9 12 1 1 9 12 1 1 100 1 10 12 1 9 12 1 1 18 12 1 1 9 12 1 1 10 12 1 1 100 1 10 12 1 10 12 1 1 10 12 1 1 10 12 1 1 10 12 1 10 7 12 1 10 12 1 10 12 1 10 12 1 10 12 1 11 100 12 1 1 1 100 1 10 12 1 1 10 100 12 1 1 1 10 100 12 1 1 1 10 12 1 100 1 10 10 12 1 10 12 1 1 9 12 1 1 10 12 1 1 9 12 1 1 10 12 1 9 12 1 1 9 12 1 100 1 10 10 12 1 10 100 12 1 1 10 9 10 10 12 1 1 10 12 1 1 10 100 12 1 1 10 100 1 10 9 10 10 12 1 7 1 10 12 1 1 10 12 1 10 12 1 10 12 1 1 9 12 1 10 12 1 10 12 1 9 12 1 1 10 12 1 10 12 1 9 12 1 1 10 12 1 10 12 1 10 12 1 11 100 12 1 1 10 12 1 1 10 12 1 1 10 12 1 1 9 12 1 10 100 1 10 12 1 1 10 12 1 1 10 12 1 10 12 1 10 12 1 10 12 9 12 1 1 10 100 12 1 1 10 12 1 10 12 1 1 10 100 12 1 1 10 12 1 100 1 8 1 10 12 1 10 12 1 1 10 12 1 10 12 1 10 100 12 1 1 10 12 1 10 12 1 1 10 12 1 10 12 1 1 10 7 12 1 1 10 12 1 1 10 12 1 1 100 1 10 10 12 1 7 1 9 12 1 1 10 7 12 1 1 10 9 12 1 1 18 9 100 12 1 1 1 11 100 12 1 1 1 11 100 1 11 12 10 12 1 10 12 1 1 10 12 1 100 1 10 10 12 1 10 12 1 1 10 12 1 1 10 12 1 10 12 1 10 12 1 9 12 1 1 11 100 12 1 1 10 12 9 100 12 1 1 1 9 12 1 10 12 1 1 9 12 1 9 12 1 9 12 1 7 1 10 10 12 1 1 10 12 1 10 12 7 1 7 1 10 8 1 10 12 1 1 10 12 1 10 12 1 10 10 12 1 8 1 10 10 12 1 10 12 1 10 7 12 1 1 8 1 8 1 10 9 100 12 1 1 1 10 12 1 1 10 12 1 10 10 10 12 9 12 1 10 12 1 1 9 12 1 10 12 1 1 9 12 1 10 12 1 1 18 12 1 1 18 12 1 10 7 12 1 1 1 8 1 10 100 12 1 1 1 10 7 12 1 1 1 18 12 1 10 100 12 1 1 1 100 1 10 12 1 8 1 10 12 1 8 1 10 12 1 1 8 1 8 1 10 100 12 1 1 8 1 10 12 1 8 1 8 1 10 100 12 1 1 1 10 12 1 10 12 1 1 10 12 1 18 12 1 1 18 12 1 1 5 0 9 12 1 10 12 1 18 12 1 100 1 10 12 10 7 12 1 1 10 12 1 1 7 1 8 1 10 10 12 1 10 12 1 1 10 12 1 9 12 1 8 10 12 1 1 8 8 9 12 1 8 10 12 1 1 3 1 3 1 3 1 3 1 3 1 3 1 3 1 3 1 3 1 3 1 3 1 3 3 1 3 1 1 1 1 1 1 1 1 1 1 1 1 1 1 7 1 1 1 1 1 1 1 1 1 1 1 1 1 1 7 1 1 1 1 1 1 1 1 1 1 1 7 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 1 16 15 10 12 15 16 15 10 12 16 15 10 12 16 16 15 10 12 16 15 10 12 16 15 10 12 16 15 10 12 16 1 15 10 100 12 1 1 1 1 1 1 1 7 1 1 100 1 100 1 1
ciInstanceKlass java/lang/ThreadBuilders$BoundVirtualThread 0 0 132 10 7 12 1 1 1 9 7 12 1 1 1 10 7 12 1 1 1 9 12 1 1 10 12 1 1 10 12 1 1 9 12 1 1 10 7 12 1 1 1 10 12 1 1 7 1 8 1 10 12 1 10 12 1 1 10 12 1 1 10 12 1 1 10 7 12 1 1 1 8 1 10 12 1 8 1 10 12 1 1 10 100 12 1 1 9 100 12 1 1 1 10 12 1 1 10 10 12 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 1 1 1
ciInstanceKlass jdk/internal/vm/ContinuationScope 0 0 50 10 100 12 1 1 1 10 100 12 1 1 1 100 1 9 100 12 1 1 1 10 12 1 1 10 100 12 1 1 1 10 12 1 1 10 12 1 1 1 1 1 1 1 1 1 1 1 1 1
ciInstanceKlass jdk/internal/vm/StackChunk 0 0 32 10 100 12 1 1 1 9 100 12 1 1 1 9 12 1 1 9 12 1 1 1 1 1 1 1 1 1 1 1 1 1
ciInstanceKlass java/lang/Float 1 1 279 7 1 7 1 10 7 12 1 1 1 10 100 12 1 1 1 4 7 1 10 12 1 1 10 12 1 1 8 1 8 1 10 7 12 1 1 1 10 12 1 1 10 12 1 1 10 100 1 4 10 7 12 1 1 9 12 1 1 10 12 1 1 10 12 1 10 10 12 1 1 10 12 1 10 12 1 3 3 100 1 4 4 4 3 10 12 1 1 9 12 1 1 100 1 10 3 3 4 4 10 12 1 3 3 3 10 12 1 1 10 12 1 1 10 12 1 10 100 12 1 1 1 10 12 1 1 10 12 1 1 10 7 12 1 1 1 8 1 10 12 1 1 9 12 1 1 7 1 7 1 7 1 1 1 1 4 1 4 1 1 1 4 1 1 3 1 3 1 3 1 3 1 3 1 1 1 1 5 0 1 1 1 1 1 1 1 1 1 1 7 1 1 1 1 1 1 1 1 1 1 1 1 1 3 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 7 1 1 1 1 1 1 1 100 1 100 1 1
staticfield java/lang/Float TYPE Ljava/lang/Class; java/lang/Class
staticfield java/lang/Float $assertionsDisabled Z 1
ciMethod java/lang/Float isNaN (F)Z 512 0 9517 0 0
ciInstanceKlass java/lang/Double 1 1 290 7 1 7 1 10 100 12 1 1 1 10 12 1 1 10 7 1 10 12 1 1 10 100 12 1 1 1 6 0 8 1 10 12 1 1 8 1 10 12 1 1 8 1 6 0 10 12 1 1 100 1 5 0 5 0 8 1 8 1 10 7 12 1 1 1 10 7 12 1 1 1 8 1 10 12 1 1 8 1 8 1 8 1 10 12 1 1 10 12 1 10 12 1 1 10 12 1 10 12 1 10 12 1 1 10 12 1 10 7 1 6 0 10 7 12 1 1 9 12 1 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 5 0 10 12 1 1 10 12 1 10 12 1 10 100 12 1 1 1 10 12 1 1 10 12 1 1 8 1 10 7 12 1 1 1 9 12 1 1 7 1 7 1 7 1 1 1 6 0 1 6 0 1 6 0 1 1 1 6 0 1 1 3 1 3 1 3 1 3 1 3 1 1 1 1 5 0 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 7 1 1 1 1 1 1 1 1 1 1 1 1 3 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 7 1 1 1 1 1 1 1 100 1 100 1 1
staticfield java/lang/Double TYPE Ljava/lang/Class; java/lang/Class
ciInstanceKlass java/lang/Byte 1 1 213 7 1 100 1 10 7 12 1 1 1 9 12 1 1 8 1 9 12 1 1 7 1 10 12 1 1 10 12 1 1 10 100 12 1 1 1 10 100 12 1 1 1 9 100 12 1 1 1 10 12 1 1 7 1 7 1 10 12 1 1 8 1 10 12 1 1 8 1 10 12 1 10 12 1 10 12 1 10 12 1 1 10 12 1 10 12 1 10 12 1 1 10 8 1 8 1 10 7 1 9 12 1 1 10 12 1 1 10 12 1 1 10 12 1 1 10 12 1 5 0 10 12 1 1 8 1 10 7 12 1 1 1 9 12 1 1 7 1 7 1 1 1 3 1 3 1 1 1 1 3 1 3 1 1 5 0 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1
staticfield java/lang/Byte TYPE Ljava/lang/Class; java/lang/Class
ciInstanceKlass java/lang/Short 1 1 222 7 1 7 1 100 1 10 7 12 1 1 1 10 12 1 1 7 1 7 1 10 12 1 1 8 1 10 12 1 1 8 1 10 12 1 10 12 1 10 12 1 10 12 1 1 10 12 1 1 10 12 1 9 12 1 1 8 1 9 12 1 1 7 1 10 12 1 1 10 12 1 10 100 12 1 1 1 10 100 12 1 1 1 9 100 12 1 1 1 10 12 1 10 12 1 1 10 8 1 8 1 10 7 1 9 12 1 1 10 12 1 1 10 12 1 1 10 12 1 1 10 12 1 3 3 5 0 10 12 1 1 8 1 10 7 12 1 1 1 9 12 1 1 7 1 7 1 1 1 3 1 3 1 1 1 1 3 1 3 1 1 5 0 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 3 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1
staticfield java/lang/Short TYPE Ljava/lang/Class; java/lang/Class
ciInstanceKlass java/lang/Long 1 1 524 7 1 7 1 7 1 7 1 10 12 1 1 9 12 1 1 9 7 12 1 1 1 10 100 12 1 1 1 10 12 1 1 10 100 12 1 1 1 10 10 12 10 12 1 10 12 1 10 12 1 5 0 5 0 7 1 10 12 1 1 10 12 1 1 10 12 1 10 12 1 10 12 1 10 12 1 1 10 100 12 1 1 10 12 1 10 12 1 1 10 12 1 1 10 12 1 1 10 12 1 1 10 100 12 1 1 1 10 12 1 1 10 12 1 10 12 1 10 12 1 10 12 1 1 10 10 12 1 5 0 5 0 9 12 1 1 9 12 1 5 0 7 1 8 1 10 12 1 8 1 10 12 1 8 1 8 1 10 12 1 1 5 0 10 12 1 1 5 0 10 12 1 1 10 12 1 1 10 100 12 1 1 1 11 7 1 10 12 1 1 11 10 12 1 1 8 1 10 12 1 1 8 1 7 1 10 12 1 1 10 12 1 8 1 8 1 11 12 1 1 10 12 1 10 12 1 10 12 1 5 0 5 0 9 7 12 1 1 1 10 12 1 10 12 1 1 8 1 8 1 10 12 1 1 8 1 8 1 8 1 8 1 8 1 8 1 10 12 1 10 7 1 9 12 1 1 10 12 1 10 12 1 1 10 12 1 1 10 7 12 1 1 1 100 1 100 1 10 12 1 1 10 12 1 1 5 0 10 12 1 10 12 1 5 0 5 0 5 0 10 12 1 1 10 12 1 5 0 5 0 10 12 1 10 12 1 10 100 12 1 1 1 10 12 1 1 10 12 1 1 8 1 10 7 12 1 1 1 9 12 1 1 7 1 7 1 7 1 1 1 1 5 0 1 1 1 1 3 1 3 1 5 0 1 1 1 1 1 1 1 1 1 100 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 3 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 7 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 7 1 1 1 1 1 1 1 1 1 100 1 100 1 1
staticfield java/lang/Long TYPE Ljava/lang/Class; java/lang/Class
ciInstanceKlass jdk/internal/vm/vector/VectorSupport 0 0 573 100 1 10 100 12 1 1 1 9 12 1 1 10 12 1 1 100 1 10 12 1 11 100 12 1 1 1 11 100 12 1 1 1 11 100 12 1 1 11 100 12 1 1 11 100 12 1 1 1 11 100 12 1 1 11 100 12 1 1 11 100 12 1 1 11 100 12 1 1 11 100 12 1 1 11 100 12 1 1 11 100 12 1 1 1 11 100 12 1 1 11 100 12 1 1 1 11 100 12 1 1 1 11 100 12 1 1 11 100 12 1 1 1 11 100 12 1 1 100 1 10 12 1 1 11 100 12 1 1 11 100 12 1 1 11 100 12 1 1 11 100 12 1 1 11 100 12 1 1 11 100 12 1 1 9 12 1 1 10 100 12 1 1 11 100 12 1 1 10 12 1 1 10 100 12 1 1 1 10 12 1 10 12 1 1 10 12 1 1 1 1 1 3 1 3 1 3 1 3 1 3 1 3 1 3 1 3 1 3 1 3 1 3 1 3 1 3 1 3 1 3 1 3 1 3 1 3 1 3 1 3 1 3 1 3 1 3 1 3 1 3 1 3 1 3 1 3 1 3 1 3 1 3 1 3 1 3 1 3 1 3 1 3 1 3 1 3 1 3 1 3 1 3 1 3 1 3 1 3 1 3 1 3 1 3 1 3 1 3 1 3 1 3 1 3 1 3 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 100 1 100 1 100 1 100 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1
instanceKlass jdk/internal/vm/vector/VectorSupport$VectorShuffle
instanceKlass jdk/internal/vm/vector/VectorSupport$VectorMask
instanceKlass jdk/internal/vm/vector/VectorSupport$Vector
ciInstanceKlass jdk/internal/vm/vector/VectorSupport$VectorPayload 0 0 32 10 100 12 1 1 1 9 100 12 1 1 1 10 100 12 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1
ciInstanceKlass jdk/internal/vm/vector/VectorSupport$Vector 0 0 28 10 100 12 1 1 1 100 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 1 1 1
ciInstanceKlass jdk/internal/vm/vector/VectorSupport$VectorMask 0 0 28 10 100 12 1 1 1 100 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 1 1 1
ciInstanceKlass jdk/internal/vm/vector/VectorSupport$VectorShuffle 0 0 28 10 100 12 1 1 1 100 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 1 1 1
ciInstanceKlass jdk/internal/vm/FillerObject 0 0 16 10 100 12 1 1 1 100 1 1 1 1 1 1 1 1
instanceKlass java/lang/ref/PhantomReference
instanceKlass java/lang/ref/FinalReference
instanceKlass java/lang/ref/WeakReference
instanceKlass java/lang/ref/SoftReference
ciInstanceKlass java/lang/ref/Reference 1 1 190 9 7 12 1 1 1 9 7 12 1 1 10 12 1 1 10 12 1 1 9 12 1 1 10 12 1 1 9 12 1 1 9 12 1 1 7 1 10 12 1 10 7 12 1 1 10 12 1 10 12 1 1 10 12 1 7 1 8 1 10 12 1 1 7 1 10 12 1 1 10 12 1 1 10 12 1 9 12 1 10 12 1 1 10 12 1 10 12 1 9 12 1 7 1 100 1 10 12 9 12 1 9 12 1 100 1 10 10 12 1 10 10 7 12 1 1 7 1 10 10 7 12 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 7 1 1 1 100 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 7 1 7 1 7 1 1 1
staticfield java/lang/ref/Reference processPendingLock Ljava/lang/Object; java/lang/Object
staticfield java/lang/ref/Reference $assertionsDisabled Z 1
instanceKlass org/eclipse/sisu/inject/MildElements$Soft
instanceKlass sun/util/locale/provider/LocaleResources$ResourceReference
instanceKlass sun/util/resources/Bundles$BundleReference
instanceKlass org/eclipse/sisu/inject/MildKeys$Soft
instanceKlass java/lang/invoke/LambdaFormEditor$Transform
instanceKlass sun/util/locale/LocaleObjectCache$CacheEntry
ciInstanceKlass java/lang/ref/SoftReference 1 1 47 10 7 12 1 1 1 9 7 12 1 1 1 9 12 1 10 12 1 10 12 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 1 1 1 1
instanceKlass sun/nio/ch/FileLockTable$FileLockReference
instanceKlass org/eclipse/sisu/inject/MildElements$Weak
instanceKlass com/google/common/collect/MapMakerInternalMap$AbstractWeakKeyEntry
instanceKlass com/google/common/cache/LocalCache$WeakEntry
instanceKlass java/lang/WeakPairMap$WeakRefPeer
instanceKlass java/util/logging/LogManager$LoggerWeakRef
instanceKlass java/util/logging/Level$KnownLevel
instanceKlass java/lang/ClassValue$Entry
instanceKlass org/eclipse/sisu/inject/MildKeys$Weak
instanceKlass java/lang/invoke/MethodType$ConcurrentWeakInternSet$WeakEntry
instanceKlass java/lang/ThreadLocal$ThreadLocalMap$Entry
instanceKlass java/util/WeakHashMap$Entry
ciInstanceKlass java/lang/ref/WeakReference 1 1 31 10 7 12 1 1 1 10 12 1 100 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1
instanceKlass java/lang/ref/Finalizer
ciInstanceKlass java/lang/ref/FinalReference 1 1 50 10 7 12 1 1 1 10 7 12 1 1 1 10 12 1 1 7 1 8 1 10 12 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 7 1
instanceKlass jdk/internal/ref/PhantomCleanable
instanceKlass jdk/internal/ref/Cleaner
ciInstanceKlass java/lang/ref/PhantomReference 1 1 39 10 100 12 1 1 1 10 7 12 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1
ciInstanceKlass java/lang/ref/Finalizer 1 1 155 9 7 12 1 1 1 10 7 12 1 1 1 9 12 1 1 9 12 1 1 9 12 1 9 12 1 9 12 1 1 10 12 1 7 1 8 1 10 12 1 10 12 1 1 9 12 1 100 1 10 12 1 7 1 11 100 12 1 1 10 12 1 7 1 10 12 1 100 1 10 12 1 10 7 12 1 1 1 10 100 12 1 1 1 100 1 10 10 12 1 7 1 10 12 1 7 1 10 12 1 1 10 12 1 1 10 12 1 10 7 12 1 1 1 7 1 10 7 1 10 10 12 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1
staticfield java/lang/ref/Finalizer lock Ljava/lang/Object; java/lang/Object
staticfield java/lang/ref/Finalizer ENABLED Z 1
staticfield java/lang/ref/Finalizer $assertionsDisabled Z 1
instanceKlass org/apache/maven/plugin/MojoExecution$Source
instanceKlass java/util/stream/MatchOps$MatchKind
instanceKlass java/nio/file/AccessMode
instanceKlass org/eclipse/aether/RepositoryEvent$EventType
instanceKlass org/eclipse/aether/named/support/ReadWriteLockNamedLock$Step
instanceKlass java/time/temporal/ChronoUnit
instanceKlass org/apache/maven/project/ProjectBuildingRequest$RepositoryMerging
instanceKlass org/fusesource/jansi/Ansi$Attribute
instanceKlass org/fusesource/jansi/Ansi$Color
instanceKlass org/apache/maven/shared/utils/logging/Style
instanceKlass java/security/DrbgParameters$Capability
instanceKlass sun/security/util/KnownOIDs
instanceKlass org/eclipse/sisu/inject/QualifyingStrategy
instanceKlass com/google/inject/internal/InjectorImpl$JitLimitation
instanceKlass org/eclipse/sisu/bean/DeclaredMembers$View
instanceKlass com/google/inject/internal/Initializer$InjectableReferenceState
instanceKlass com/google/common/collect/MapMakerInternalMap$Strength
instanceKlass org/apache/maven/settings/building/SettingsProblem$Severity
instanceKlass org/eclipse/aether/metadata/Metadata$Nature
instanceKlass org/apache/maven/model/building/ModelProblem$Version
instanceKlass org/apache/maven/building/Problem$Severity
instanceKlass org/apache/maven/plugin/internal/DefaultPluginValidationManager$ValidationReportLevel
instanceKlass org/apache/maven/plugin/PluginValidationManager$IssueLocality
instanceKlass org/apache/maven/classrealm/ClassRealmRequest$RealmType
instanceKlass org/apache/maven/model/building/ModelProblem$Severity
instanceKlass org/apache/maven/artifact/ArtifactScopeEnum
instanceKlass org/apache/maven/execution/ExecutionEvent$Type
instanceKlass com/google/inject/spi/InjectionPoint$Position
instanceKlass java/lang/annotation/ElementType
instanceKlass java/lang/reflect/ProxyGenerator$PrimitiveTypeInfo
instanceKlass java/lang/annotation/RetentionPolicy
instanceKlass com/google/inject/internal/InternalFlags$ColorizeOption
instanceKlass com/google/inject/internal/InternalFlags$BytecodeGenOption
instanceKlass com/google/inject/internal/InternalFlags$NullableProvidesOption
instanceKlass com/google/inject/internal/InternalFlags$CustomClassLoadingOption
instanceKlass com/google/inject/internal/InternalFlags$IncludeStackTraceOption
instanceKlass com/google/inject/Key$NullAnnotationStrategy
instanceKlass sun/reflect/annotation/TypeAnnotation$TypeAnnotationTarget
instanceKlass com/google/common/cache/LocalCache$EntryFactory
instanceKlass com/google/common/cache/CacheBuilder$NullListener
instanceKlass com/google/common/cache/CacheBuilder$OneWeigher
instanceKlass com/google/common/cache/LocalCache$Strength
instanceKlass jdk/internal/logger/BootstrapLogger$LoggingBackend
instanceKlass com/google/inject/Stage
instanceKlass org/eclipse/sisu/space/BeanScanning
instanceKlass java/math/RoundingMode
instanceKlass sun/util/locale/provider/LocaleProviderAdapter$Type
instanceKlass java/util/stream/StreamShape
instanceKlass java/util/stream/Collector$Characteristics
instanceKlass java/util/stream/StreamOpFlag$Type
instanceKlass java/util/stream/StreamOpFlag
instanceKlass java/util/Locale$Category
instanceKlass org/slf4j/impl/OutputChoice$OutputChoiceType
instanceKlass org/fusesource/jansi/AnsiColors
instanceKlass org/fusesource/jansi/AnsiMode
instanceKlass org/fusesource/jansi/AnsiType
instanceKlass java/util/regex/Pattern$Qtype
instanceKlass java/lang/invoke/VarHandle$AccessType
instanceKlass java/lang/invoke/VarHandle$AccessMode
instanceKlass java/lang/invoke/MethodHandles$Lookup$ClassOption
instanceKlass java/lang/invoke/MethodHandleImpl$Intrinsic
instanceKlass java/lang/invoke/LambdaForm$BasicType
instanceKlass java/lang/invoke/LambdaForm$Kind
instanceKlass sun/invoke/util/Wrapper
instanceKlass java/util/zip/ZipCoder$Comparison
instanceKlass java/nio/file/LinkOption
instanceKlass java/util/concurrent/TimeUnit
instanceKlass sun/nio/fs/WindowsPathType
instanceKlass java/nio/file/StandardOpenOption
instanceKlass java/io/File$PathStatus
instanceKlass java/lang/module/ModuleDescriptor$Requires$Modifier
instanceKlass java/lang/reflect/AccessFlag$Location
instanceKlass java/lang/reflect/AccessFlag
instanceKlass java/lang/module/ModuleDescriptor$Modifier
instanceKlass java/lang/reflect/ClassFileFormatVersion
instanceKlass java/lang/Thread$State
ciInstanceKlass java/lang/Enum 1 1 204 9 7 12 1 1 1 9 12 1 1 10 7 12 1 1 1 9 12 1 10 7 12 1 1 1 100 1 10 10 12 1 1 10 12 1 7 1 10 10 7 12 1 1 10 12 1 1 18 12 1 1 10 100 12 1 1 1 10 12 1 1 11 7 12 1 1 1 100 1 8 1 10 12 1 100 1 7 1 10 8 1 10 12 1 1 10 12 1 1 8 1 10 12 1 10 100 1 8 1 10 10 12 1 1 10 100 12 1 1 1 7 1 7 1 7 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 3 1 1 1 1 100 1 7 1 1 100 1 1 1 1 1 1 1 1 1 1 1 16 15 10 12 16 15 10 100 12 1 1 1 1 1 100 1 100 1 1
ciInstanceKlass java/lang/reflect/Method 1 1 472 9 7 12 1 1 1 10 7 12 1 1 1 10 7 12 1 1 9 12 1 1 10 12 1 1 10 12 1 1 10 7 12 1 1 10 7 12 1 1 1 9 12 1 1 9 12 1 9 12 1 1 9 12 1 9 12 1 9 12 1 1 9 12 1 9 12 1 1 9 12 1 9 12 1 9 12 1 1 100 1 8 1 10 12 1 10 12 1 9 12 1 1 8 1 10 7 12 1 1 10 7 12 1 1 1 10 12 1 1 10 12 1 1 10 12 1 10 12 1 1 10 12 1 1 9 7 12 1 1 1 10 12 1 1 10 12 10 7 12 1 1 10 12 1 1 10 12 1 10 12 1 10 12 1 10 7 12 1 1 1 10 12 1 1 10 7 1 10 7 12 1 1 1 10 7 12 1 1 10 12 1 1 10 12 1 1 10 12 1 10 7 12 1 1 1 10 12 1 10 8 1 10 12 1 10 12 1 7 1 8 1 8 1 8 1 10 12 1 10 12 1 1 10 12 1 1 10 10 12 1 1 10 12 1 11 7 1 10 12 1 9 12 1 1 10 12 1 1 10 12 1 10 12 1 1 10 12 1 1 11 7 12 1 1 1 11 12 1 9 12 1 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 9 12 1 1 10 7 12 1 1 1 10 7 12 1 1 10 12 1 1 10 7 12 1 1 1 10 7 12 1 1 1 10 7 12 1 1 1 11 7 12 1 1 1 10 7 12 1 1 1 7 1 100 1 100 1 10 12 1 10 12 1 1 10 12 1 100 1 8 1 10 12 1 10 10 12 1 1 10 12 1 1 10 12 1 1 10 12 1 1 8 1 10 12 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 100 1 100 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1
ciInstanceKlass java/lang/reflect/Field 1 1 457 9 7 12 1 1 1 10 12 1 1 10 100 12 1 1 1 10 100 12 1 1 9 12 1 1 10 12 1 1 10 12 1 1 10 7 12 1 1 10 7 12 1 1 1 9 12 1 1 9 12 1 9 12 1 9 12 1 1 9 12 1 1 9 12 1 9 12 1 1 9 12 1 1 100 1 8 1 10 12 1 10 12 1 9 12 1 1 9 12 1 10 12 1 10 7 12 1 1 10 12 1 1 10 12 1 1 10 12 1 10 12 1 1 9 100 12 1 1 1 10 100 12 1 1 1 7 1 10 12 1 1 10 12 1 1 10 12 1 1 10 12 1 10 12 1 10 7 1 10 7 12 1 1 7 1 10 8 1 10 12 1 1 10 12 1 1 8 1 10 12 10 12 1 8 1 8 1 10 11 7 1 9 12 1 10 12 1 1 10 12 1 1 11 7 12 1 1 1 10 12 1 11 12 1 1 11 12 1 1 11 12 1 1 11 12 1 1 11 12 1 1 11 12 1 1 11 12 1 1 11 12 1 1 11 12 1 1 11 12 1 1 11 12 1 1 11 12 1 1 11 12 1 1 11 12 1 1 11 12 1 1 11 12 1 1 11 12 1 1 10 12 1 10 7 12 1 1 10 12 1 10 12 1 10 12 1 9 12 1 1 10 7 12 1 1 1 10 12 1 1 10 12 1 10 7 12 1 1 10 12 1 1 11 7 1 10 12 1 7 1 10 100 12 1 1 1 10 7 12 1 1 1 9 12 1 10 7 12 1 1 1 11 7 12 1 1 1 10 12 1 1 10 12 1 1 9 7 12 1 1 10 7 12 1 1 1 10 12 1 1 7 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 7 1 1 1 1 1 1 1 1 1 100 1 1
ciInstanceKlass java/lang/reflect/Parameter 1 1 243 10 7 12 1 1 1 9 7 12 1 1 1 9 12 1 1 9 12 1 1 9 12 1 10 12 1 1 10 12 1 1 10 7 12 1 1 1 7 1 10 10 12 1 1 11 7 12 1 1 1 10 12 1 10 100 12 1 1 1 10 12 1 1 10 12 1 10 12 1 8 1 8 1 10 7 12 1 1 1 10 12 1 10 12 9 100 12 1 1 1 10 100 12 1 1 1 10 12 1 8 1 10 12 1 9 12 1 1 10 12 1 1 9 12 1 1 10 12 1 1 10 12 1 1 10 12 1 1 10 12 1 10 10 12 1 10 100 12 1 1 1 10 12 1 1 11 7 12 1 1 10 7 12 1 1 7 1 10 100 12 1 1 1 10 12 1 1 10 12 1 1 10 12 1 1 10 12 1 1 9 12 1 100 1 10 11 12 1 1 11 12 1 1 7 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 1 1 1 1 1
ciInstanceKlass java/lang/reflect/RecordComponent 0 0 196 10 7 12 1 1 1 9 7 12 1 1 1 9 12 1 1 9 12 1 10 12 1 1 10 12 1 1 10 100 12 1 1 1 10 12 1 1 9 12 1 1 10 12 1 1 10 12 1 1 10 12 1 10 100 12 1 1 10 100 12 1 1 9 12 1 1 10 100 12 1 1 1 11 100 12 1 1 1 10 9 100 12 1 1 1 10 100 12 1 1 1 9 12 1 1 10 100 12 1 1 1 10 12 1 1 11 7 12 1 1 10 7 12 1 1 7 1 9 12 1 9 12 1 1 9 12 1 10 100 12 1 1 1 10 12 1 1 10 12 1 1 7 1 10 10 12 1 10 12 1 1 8 1 10 12 1 10 12 1 9 12 1 7 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 7 1 1 1 1 1 1 100 1 1
ciInstanceKlass java/lang/StringBuffer 1 1 483 10 7 12 1 1 1 10 12 1 10 12 1 10 12 1 1 9 7 12 1 1 1 10 12 1 1 10 12 1 10 12 1 1 9 12 1 1 10 12 1 10 12 1 1 10 12 1 1 10 12 1 10 12 1 1 10 12 1 10 12 1 1 10 12 1 1 10 7 12 1 1 1 10 12 1 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 1 10 12 1 10 12 1 1 10 12 1 1 10 10 12 1 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 10 12 1 10 12 1 10 12 1 10 12 1 1 10 12 1 10 12 1 10 10 12 1 1 10 12 1 10 12 1 10 12 1 10 10 100 12 1 1 1 10 10 12 1 1 9 12 1 1 10 100 12 1 1 10 100 1 8 10 100 12 1 1 1 8 10 12 1 8 1 10 12 1 10 12 1 10 100 12 1 1 1 10 100 12 1 1 1 7 1 10 12 100 1 8 1 10 10 12 1 1 10 12 1 1 10 12 1 10 12 1 10 12 1 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 10 12 10 12 1 10 12 1 10 12 1 10 12 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 7 1 10 12 1 9 7 12 1 1 1 9 7 1 9 12 1 1 7 1 7 1 7 1 7 1 1 1 1 5 0 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 1 1 1 1 7 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1
staticfield java/lang/StringBuffer serialPersistentFields [Ljava/io/ObjectStreamField; 3 [Ljava/io/ObjectStreamField;
instanceKlass jdk/internal/loader/BuiltinClassLoader
instanceKlass java/net/URLClassLoader
ciInstanceKlass java/security/SecureClassLoader 1 1 102 10 7 12 1 1 1 7 1 10 12 1 9 7 12 1 1 1 10 12 1 10 12 1 10 12 1 1 10 12 1 1 10 12 1 7 1 10 7 1 10 12 1 7 1 10 12 1 11 7 12 1 1 1 7 1 11 12 1 10 12 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 1 1 1
instanceKlass org/codehaus/plexus/classworlds/realm/ClassRealm
ciInstanceKlass java/net/URLClassLoader 1 1 600 10 7 12 1 1 1 7 1 10 12 1 9 7 12 1 1 1 10 7 12 1 1 1 9 12 1 1 7 1 10 12 1 9 12 1 1 10 12 1 10 10 12 1 10 7 12 1 1 1 10 12 1 1 10 7 12 1 1 1 10 7 12 1 1 1 7 1 10 12 1 1 10 12 1 1 10 12 1 1 7 1 100 1 10 7 12 1 1 1 100 1 8 1 10 12 1 10 7 12 1 1 1 10 12 1 1 10 12 1 1 11 100 12 1 1 1 11 7 12 1 1 1 11 12 1 1 7 1 11 12 1 11 7 12 1 1 10 12 1 11 12 1 11 12 1 1 11 10 12 1 1 10 12 1 1 10 12 1 1 7 1 10 12 1 10 12 1 1 7 1 7 1 10 12 1 1 7 1 10 10 12 1 1 10 7 12 1 1 10 12 1 100 1 7 1 10 8 1 10 12 1 1 8 1 10 12 1 1 10 10 12 1 8 1 8 1 10 12 1 1 10 7 12 1 1 1 10 7 12 1 1 1 10 12 1 1 10 12 1 1 10 12 1 1 10 12 1 10 12 1 100 1 100 1 8 1 10 12 1 10 12 1 1 10 12 1 1 7 1 10 12 1 10 7 12 1 1 1 10 12 1 1 10 12 1 1 10 12 1 1 10 12 1 10 7 12 1 1 1 10 12 1 1 8 1 10 12 1 1 11 7 12 1 1 1 9 7 12 1 1 1 10 7 12 1 1 1 9 12 1 9 12 1 9 12 1 9 12 1 9 12 1 9 12 1 10 7 12 1 1 1 8 1 10 12 1 1 7 1 10 10 12 1 10 12 1 1 10 12 1 1 7 1 10 12 1 10 12 1 1 10 12 1 10 12 1 1 7 1 10 7 12 1 1 9 7 12 1 1 1 10 12 1 8 1 100 1 8 1 10 12 1 10 12 1 8 1 10 12 1 10 12 1 9 12 1 1 10 100 12 1 1 10 12 1 10 12 1 10 100 1 8 1 10 100 1 10 12 1 10 7 12 1 100 1 10 12 1 10 12 1 100 1 10 10 7 12 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 7 1 100 1 7 1 7 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 1 1
ciInstanceKlass java/util/jar/Manifest 1 1 339 10 7 12 1 1 1 7 1 10 9 7 12 1 1 1 7 1 10 9 12 1 1 9 12 1 1 10 12 1 10 12 1 1 10 12 1 1 10 12 1 1 10 12 1 1 11 7 1 11 12 1 1 10 12 1 1 10 100 12 1 1 1 100 1 7 1 10 8 1 10 12 1 1 10 12 1 1 10 12 1 10 12 1 11 100 1 10 12 1 10 12 1 1 11 12 1 1 10 12 1 11 12 1 1 11 100 12 1 1 1 11 7 12 1 1 11 12 1 1 100 1 10 12 1 8 1 11 12 1 7 1 10 12 1 1 11 12 1 10 12 1 10 12 1 10 7 12 1 1 1 8 1 10 12 1 1 10 9 100 12 1 1 1 10 12 1 1 10 100 12 1 10 12 1 10 12 1 9 100 12 1 1 1 8 1 10 12 1 8 1 8 1 7 1 10 12 1 10 12 1 10 12 1 1 100 1 8 1 10 12 1 1 8 1 10 10 12 1 1 8 1 10 12 1 1 10 7 12 1 1 1 10 12 1 10 11 12 1 1 10 12 1 10 100 12 1 1 1 10 12 1 1 10 12 1 10 12 1 1 11 10 12 1 11 10 12 1 7 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 1 1 1 1 1 1 1 1 1 1 1 1 1 7 1 1 1 1 1 1 1 1 1 1 1
ciInstanceKlass java/io/ByteArrayInputStream 1 1 117 10 7 12 1 1 1 9 7 12 1 1 1 9 12 1 1 9 12 1 9 12 1 10 100 12 1 1 1 10 7 12 1 1 1 10 7 12 1 1 1 10 100 12 1 1 1 10 12 1 1 3 10 100 1 10 100 12 1 1 1 9 12 1 1 100 1 10 10 7 12 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 1 1 1 1 1 1 1 1 1 1 1 1 1
staticfield java/io/ByteArrayInputStream $assertionsDisabled Z 1
instanceKlass java/nio/IntBuffer
instanceKlass java/nio/LongBuffer
instanceKlass java/nio/CharBuffer
instanceKlass java/nio/ByteBuffer
ciInstanceKlass java/nio/Buffer 1 1 256 100 1 10 7 12 1 1 1 9 7 12 1 1 1 9 12 1 9 12 1 1 9 12 1 9 12 1 1 10 12 1 1 10 12 1 1 10 12 100 1 7 1 10 8 1 10 12 1 1 10 12 1 8 1 8 1 10 12 1 1 10 12 1 8 1 9 12 1 1 100 1 8 1 10 12 1 8 1 8 1 9 12 10 12 1 8 1 8 1 8 1 10 12 1 8 1 8 1 8 1 100 1 10 100 1 10 100 1 10 9 12 1 1 10 7 12 1 1 1 100 1 10 12 1 1 10 12 1 10 100 12 1 1 10 7 12 1 1 1 10 7 12 1 1 1 9 12 1 1 10 7 12 1 1 1 9 12 1 1 7 1 10 10 12 1 1 7 1 10 10 7 12 1 1 1 1 1 3 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 1 1 1 1 1 1 7 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 100 1 100 1 100 1 100 1 100 1 100 1 1
staticfield java/nio/Buffer UNSAFE Ljdk/internal/misc/Unsafe; jdk/internal/misc/Unsafe
staticfield java/nio/Buffer SCOPED_MEMORY_ACCESS Ljdk/internal/misc/ScopedMemoryAccess; jdk/internal/misc/ScopedMemoryAccess
staticfield java/nio/Buffer IOOBE_FORMATTER Ljava/util/function/BiFunction; jdk/internal/util/Preconditions$4
staticfield java/nio/Buffer $assertionsDisabled Z 1
instanceKlass org/apache/maven/model/merge/ModelMerger$MergingList
instanceKlass sun/security/jca/ProviderList$3
instanceKlass com/google/common/collect/Lists$TransformingRandomAccessList
instanceKlass java/util/Collections$SingletonList
instanceKlass java/util/ArrayList$SubList
instanceKlass java/util/Arrays$ArrayList
instanceKlass java/util/AbstractSequentialList
instanceKlass java/util/Vector
instanceKlass java/util/Collections$EmptyList
instanceKlass java/util/ArrayList
ciInstanceKlass java/util/AbstractList 1 1 218 10 7 12 1 1 1 9 7 12 1 1 1 10 12 1 1 10 12 1 1 100 1 10 10 12 1 1 11 7 12 1 1 1 11 12 1 1 11 12 1 10 7 12 1 1 1 10 12 1 11 12 1 11 12 1 11 12 1 10 12 1 1 10 12 1 1 11 7 12 1 1 1 11 7 1 11 7 1 10 12 1 7 1 10 12 1 10 12 1 1 7 1 100 1 10 12 1 100 1 10 100 1 7 1 10 8 1 10 12 1 1 10 12 1 10 12 1 1 10 12 1 8 1 100 1 8 1 8 1 8 1 10 7 1 11 10 10 12 1 11 12 1 10 12 1 1 8 1 8 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 100 1 1 1 1 1 1 1
ciInstanceKlass java/lang/Iterable 1 1 62 10 7 12 1 1 1 11 7 12 1 1 1 11 7 12 1 1 1 11 12 1 1 11 7 12 1 1 1 10 100 12 1 1 1 100 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1
ciInstanceKlass java/util/Collection 1 1 115 11 100 12 1 1 1 100 1 11 7 12 1 1 1 10 100 12 1 1 1 11 12 1 1 11 100 12 1 1 1 11 12 1 1 11 100 12 1 1 1 11 12 1 1 10 100 12 1 1 1 11 12 1 10 7 12 1 1 1 100 1 100 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1
ciMethod java/util/Collection toArray ()[Ljava/lang/Object; 0 0 1 0 -1
ciInstanceKlass java/util/SequencedCollection 1 1 109 100 1 10 12 1 1 11 7 12 1 1 1 11 7 12 1 1 1 11 12 1 1 11 12 1 7 1 7 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 8 1 1 12 10 1 1 1 8 1 1 1 8 1 1 1 8 1 1 1 8 1 1 8 1 1 1 8 1 1 8 1 1 1 8 1 1 8 1 1 1 8 1 1 8 1 1 8 1
ciInstanceKlass java/util/List 1 1 251 10 100 12 1 1 1 11 100 12 1 1 1 11 100 12 1 1 1 11 12 1 1 11 100 12 1 1 11 12 1 1 11 12 1 1 10 100 12 1 1 1 100 1 100 1 10 12 1 1 100 1 10 100 12 1 1 1 11 12 1 1 11 12 1 11 12 1 100 1 10 12 1 11 12 1 1 11 12 1 1 11 12 1 10 100 12 1 1 1 9 7 12 1 1 1 7 1 10 12 10 12 1 7 1 10 12 1 1 10 12 1 10 12 1 1 11 12 1 1 100 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 100 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 1 100 1 1 1
ciMethod java/util/List size ()I 0 0 1 0 -1
ciMethod java/util/List isEmpty ()Z 0 0 1 0 -1
ciMethod java/util/List add (Ljava/lang/Object;)Z 0 0 1 0 -1
ciMethod java/util/List iterator ()Ljava/util/Iterator; 0 0 1 0 -1
ciMethod java/util/List addAll (Ljava/util/Collection;)Z 0 0 1 0 -1
instanceKlass java/util/AbstractMap$2
instanceKlass org/eclipse/sisu/inject/MildElements
instanceKlass org/eclipse/sisu/inject/MildValues$1
instanceKlass com/google/common/collect/AbstractMapBasedMultimap$WrappedCollection
instanceKlass com/google/common/collect/Maps$Values
instanceKlass com/google/common/collect/AbstractMultimap$Values
instanceKlass java/util/TreeMap$Values
instanceKlass com/google/common/collect/ImmutableCollection
instanceKlass java/util/IdentityHashMap$Values
instanceKlass java/util/HashMap$Values
instanceKlass java/util/AbstractQueue
instanceKlass java/util/LinkedHashMap$LinkedValues
instanceKlass java/util/ArrayDeque
instanceKlass java/util/AbstractSet
instanceKlass java/util/ImmutableCollections$AbstractImmutableCollection
instanceKlass java/util/AbstractList
ciInstanceKlass java/util/AbstractCollection 1 1 160 10 7 12 1 1 1 10 7 12 1 1 1 10 12 1 1 11 7 12 1 1 1 11 12 1 1 10 12 1 1 10 100 12 1 1 1 10 12 1 1 10 12 1 1 10 7 12 1 1 10 7 12 1 1 1 7 1 10 7 12 1 1 1 10 100 12 1 1 1 100 1 10 11 12 1 11 7 1 10 12 1 10 12 1 10 100 12 1 1 1 11 8 1 7 1 10 10 12 1 1 8 1 10 12 1 10 12 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1
ciMethod java/util/AbstractCollection <init> ()V 622 0 184442 0 80
ciMethod java/util/AbstractList <init> ()V 548 0 140695 0 88
ciInstanceKlass java/lang/AssertionStatusDirectives 0 0 24 10 100 12 1 1 1 100 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1
instanceKlass org/eclipse/aether/named/support/LockUpgradeNotSupportedException
instanceKlass java/util/ConcurrentModificationException
instanceKlass com/google/inject/internal/aop/GlueException
instanceKlass java/io/UncheckedIOException
instanceKlass org/apache/maven/artifact/InvalidArtifactRTException
instanceKlass com/google/inject/OutOfScopeException
instanceKlass java/lang/annotation/IncompleteAnnotationException
instanceKlass java/lang/reflect/UndeclaredThrowableException
instanceKlass com/google/common/util/concurrent/UncheckedExecutionException
instanceKlass com/google/common/cache/CacheLoader$InvalidCacheLoadException
instanceKlass java/util/NoSuchElementException
instanceKlass com/google/inject/CreationException
instanceKlass com/google/inject/ConfigurationException
instanceKlass com/google/inject/ProvisionException
instanceKlass java/lang/TypeNotPresentException
instanceKlass java/lang/IndexOutOfBoundsException
instanceKlass java/lang/UnsupportedOperationException
instanceKlass java/lang/SecurityException
instanceKlass java/lang/IllegalStateException
instanceKlass java/lang/IllegalArgumentException
instanceKlass java/lang/ArithmeticException
instanceKlass java/lang/NullPointerException
instanceKlass java/lang/IllegalMonitorStateException
instanceKlass java/lang/ArrayStoreException
instanceKlass java/lang/ClassCastException
ciInstanceKlass java/lang/RuntimeException 1 1 40 10 7 12 1 1 1 10 12 1 10 12 1 10 12 1 10 12 1 100 1 1 1 1 5 0 1 1 1 1 1 1 1 1 1 1 1 1 1 1
instanceKlass jdk/internal/misc/ThreadTracker$ThreadRef
instanceKlass java/lang/reflect/Executable$ParameterData
instanceKlass java/lang/reflect/Proxy$ProxyBuilder$ProxyClassContext
instanceKlass java/util/stream/Collectors$CollectorImpl
instanceKlass java/nio/DirectByteBuffer$Deallocator
instanceKlass java/security/SecureClassLoader$CodeSourceKey
instanceKlass jdk/internal/reflect/ReflectionFactory$Config
instanceKlass jdk/internal/foreign/abi/UpcallLinker$CallRegs
instanceKlass jdk/internal/foreign/abi/VMStorage
ciInstanceKlass java/lang/Record 1 1 22 10 7 12 1 1 1 100 1 1 1 1 1 1 1 1 1 1 1 1 1 1
ciInstanceKlass java/lang/invoke/MethodType 1 1 780 7 1 10 7 12 1 1 1 9 12 1 1 9 12 1 1 9 12 1 1 10 7 12 1 1 1 9 7 12 1 1 8 1 10 100 12 1 1 1 9 7 1 9 7 1 10 12 1 1 7 1 10 8 1 10 12 1 1 10 12 1 10 12 1 1 7 1 8 1 10 12 1 100 1 10 10 12 1 10 12 1 1 10 12 1 1 11 7 12 1 1 1 9 12 1 11 12 1 1 7 10 12 1 1 10 12 1 1 7 1 7 1 10 7 12 1 1 1 10 12 1 10 12 1 9 12 1 1 10 7 12 1 1 10 12 1 1 10 7 12 1 1 1 10 7 12 1 1 1 10 12 1 9 12 1 1 10 12 1 1 10 12 1 1 10 12 1 1 10 12 1 1 10 12 1 1 10 12 1 10 12 1 10 12 1 1 10 12 1 8 1 8 1 10 12 1 1 9 12 1 1 100 1 10 10 12 1 1 10 12 1 10 12 1 1 10 12 1 1 10 12 1 10 12 1 10 12 1 8 1 10 12 1 1 10 100 12 1 1 1 10 12 1 10 12 1 1 10 12 1 10 12 1 10 12 1 1 10 12 1 10 12 1 9 12 1 10 12 1 10 10 12 1 1 10 12 1 9 12 1 1 10 12 1 1 11 12 1 1 10 12 1 1 7 1 10 12 10 12 1 10 12 1 100 1 8 1 8 1 8 1 10 12 1 10 12 1 10 12 1 10 10 12 1 11 12 1 1 11 12 1 10 100 12 1 1 1 9 12 1 10 12 1 10 12 1 1 10 12 1 1 10 12 1 10 12 1 10 100 12 1 1 1 10 12 1 1 10 12 1 10 12 1 10 12 1 10 9 12 1 1 7 1 10 12 1 10 12 1 1 9 100 12 1 1 1 10 7 12 1 1 1 10 7 12 1 1 1 10 12 1 1 10 7 12 1 1 1 10 12 1 1 8 1 10 7 12 1 1 1 11 12 1 1 9 12 1 1 10 12 1 1 10 12 1 10 12 1 1 10 12 1 1 10 100 12 1 1 100 1 10 12 1 1 11 100 12 1 1 18 12 1 1 11 12 1 1 18 12 1 11 12 1 100 1 11 100 12 1 1 10 12 1 100 1 10 12 1 10 100 12 1 1 10 12 1 1 9 12 1 1 9 100 12 1 1 1 10 7 12 1 1 1 9 12 1 10 100 12 1 1 10 12 1 100 10 12 1 1 10 12 1 10 7 1 7 1 9 12 1 1 7 1 7 1 7 1 1 1 5 0 1 1 1 1 1 1 1 3 1 3 1 3 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 100 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 1 1 7 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 16 15 10 12 16 16 15 10 12 16 15 10 100 12 1 1 1 1 1 1 1 7 1 1 7 1 1 100 1 100 1 1
staticfield java/lang/invoke/MethodType internTable Ljava/lang/invoke/MethodType$ConcurrentWeakInternSet; java/lang/invoke/MethodType$ConcurrentWeakInternSet
staticfield java/lang/invoke/MethodType NO_PTYPES [Ljava/lang/Class; 0 [Ljava/lang/Class;
staticfield java/lang/invoke/MethodType objectOnlyTypes [Ljava/lang/invoke/MethodType; 20 [Ljava/lang/invoke/MethodType;
staticfield java/lang/invoke/MethodType METHOD_HANDLE_ARRAY [Ljava/lang/Class; 1 [Ljava/lang/Class;
staticfield java/lang/invoke/MethodType serialPersistentFields [Ljava/io/ObjectStreamField; 0 [Ljava/io/ObjectStreamField;
staticfield java/lang/invoke/MethodType $assertionsDisabled Z 1
ciInstanceKlass java/lang/invoke/TypeDescriptor$OfMethod 1 0 43 100 1 100 1 100 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 1
instanceKlass java/lang/InstantiationException
instanceKlass java/lang/NoSuchFieldException
instanceKlass java/lang/IllegalAccessException
instanceKlass java/lang/reflect/InvocationTargetException
instanceKlass java/lang/NoSuchMethodException
instanceKlass java/lang/ClassNotFoundException
ciInstanceKlass java/lang/ReflectiveOperationException 1 1 34 10 7 12 1 1 1 10 12 1 10 12 1 10 12 1 100 1 1 1 1 5 0 1 1 1 1 1 1 1 1 1 1 1
instanceKlass java/lang/invoke/DelegatingMethodHandle
instanceKlass java/lang/invoke/BoundMethodHandle
instanceKlass java/lang/invoke/DirectMethodHandle
ciInstanceKlass java/lang/invoke/MethodHandle 1 1 733 100 1 9 7 12 1 1 1 10 7 12 1 1 1 10 7 12 1 1 1 7 1 7 1 10 12 1 1 9 12 1 1 10 12 1 10 12 1 1 10 12 1 1 10 100 12 1 1 1 10 12 1 1 10 12 1 1 11 7 12 1 1 1 10 12 1 1 10 12 1 10 12 1 10 12 1 1 9 12 1 1 9 12 1 1 10 7 12 1 1 1 10 12 1 1 10 12 1 10 12 1 100 1 7 1 10 8 1 10 12 1 1 10 12 1 8 1 10 12 1 1 10 12 1 10 7 12 1 1 1 10 12 1 1 10 12 1 1 10 12 1 1 10 7 12 1 1 1 10 12 1 1 10 12 1 1 10 12 1 10 12 1 1 10 7 12 1 1 10 7 12 1 1 10 12 1 10 12 1 10 12 1 1 10 12 1 1 10 12 1 1 10 12 1 1 10 7 12 1 1 1 10 100 12 1 1 1 10 12 1 1 10 12 1 1 10 12 1 1 8 1 10 7 12 1 1 1 8 1 10 12 1 10 12 1 1 10 12 1 1 10 12 1 8 1 10 12 1 1 8 1 10 12 1 8 1 10 7 12 1 1 1 9 12 1 1 100 1 10 9 7 12 1 1 1 9 7 1 8 1 10 12 1 1 10 12 1 10 12 1 1 10 12 1 10 12 1 1 10 12 1 10 12 1 1 10 12 1 10 12 1 1 10 12 1 1 8 1 10 12 1 1 8 1 10 12 1 10 12 1 1 10 12 1 10 12 1 10 12 1 1 9 100 12 1 1 1 10 12 1 1 11 12 1 10 12 1 10 12 1 1 10 100 12 1 1 100 1 11 12 1 10 100 1 11 12 1 7 1 10 12 1 11 12 1 9 100 12 1 1 1 11 12 1 1 11 100 12 1 1 1 10 12 1 1 9 12 1 11 12 1 9 12 1 9 12 1 9 12 1 11 12 1 1 9 12 1 9 12 1 9 12 1 9 12 1 9 12 1 9 12 1 9 12 1 8 1 10 12 1 10 12 1 8 1 10 12 1 10 12 1 10 12 1 1 10 10 10 7 12 1 1 10 12 1 1 100 1 100 1 8 1 8 1 10 10 12 1 1 10 12 1 10 12 1 7 1 10 100 12 1 1 1 10 9 7 12 1 1 1 10 12 1 1 10 12 1 1 8 1 8 1 10 7 12 1 1 9 12 1 9 12 1 1 9 12 1 1 10 12 1 7 1 10 12 1 10 12 1 1 9 12 1 1 9 12 1 1 10 7 12 1 1 1 11 7 12 1 1 9 12 1 10 12 1 1 9 12 1 10 12 1 8 10 12 1 1 8 1 1 1 1 1 1 1 1 7 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 1 100 1 7 1 100 1 1 100 1 1 100 1 1 1 1
staticfield java/lang/invoke/MethodHandle FORM_OFFSET J 20
staticfield java/lang/invoke/MethodHandle UPDATE_OFFSET J 13
staticfield java/lang/invoke/MethodHandle $assertionsDisabled Z 1
ciInstanceKlass java/util/concurrent/ConcurrentHashMap 1 1 1210 7 1 7 1 3 10 12 1 1 3 7 1 10 7 12 1 1 1 7 1 10 7 12 1 1 1 100 1 11 12 1 1 11 12 1 11 12 1 1 9 12 1 1 9 12 1 1 9 12 1 10 7 12 1 1 1 7 1 10 12 1 1 10 12 1 1 10 7 12 1 1 1 4 10 12 1 9 12 1 10 12 1 1 100 1 10 5 0 10 12 1 10 12 1 1 5 0 10 12 1 1 10 12 1 9 12 1 1 10 12 1 1 9 12 1 9 12 1 1 10 12 1 1 9 12 1 10 12 1 1 9 12 1 1 10 12 1 1 100 1 10 100 1 10 12 1 10 12 1 1 10 12 1 1 10 12 1 1 10 12 1 10 12 1 1 10 12 1 1 100 1 10 12 1 1 7 1 100 1 8 1 10 12 1 10 12 1 1 10 12 1 1 11 7 12 1 1 10 12 1 1 11 12 1 1 11 7 12 1 1 1 11 7 12 1 1 1 11 12 1 7 1 11 12 1 11 12 1 10 12 1 1 10 12 1 1 9 12 1 1 10 100 12 1 1 1 9 10 12 1 1 9 12 1 10 12 1 1 5 0 9 12 1 1 7 1 10 12 1 9 12 1 1 7 1 10 12 1 9 12 1 7 1 10 7 1 10 10 12 1 1 8 1 10 12 1 10 12 1 1 11 100 1 10 12 1 10 100 12 1 1 1 8 1 10 100 12 1 1 1 8 1 10 12 1 8 1 10 12 1 10 12 1 1 10 100 12 1 1 10 12 1 10 12 1 10 12 1 9 12 1 9 10 12 1 9 12 1 1 11 100 12 1 1 1 11 7 12 1 1 1 100 1 10 12 11 100 12 1 1 10 11 7 12 1 10 12 1 100 1 10 12 1 100 1 10 10 9 7 12 1 1 1 10 12 3 10 7 12 1 1 9 12 1 10 12 1 1 9 12 1 1 9 12 1 10 12 1 1 10 100 12 1 1 9 12 1 9 7 12 1 1 10 12 1 1 10 12 1 3 9 12 1 9 12 1 10 12 1 1 7 1 9 3 9 12 1 7 1 10 12 1 9 12 1 10 12 1 9 12 1 10 12 1 9 12 1 10 100 12 1 1 1 100 10 12 1 7 1 5 0 10 100 12 1 1 100 1 10 12 1 1 10 12 1 10 12 1 100 1 10 12 1 10 100 1 100 1 10 10 12 1 10 100 1 10 12 1 10 100 1 10 12 1 10 7 1 10 12 1 1 100 1 10 12 1 10 10 12 1 100 1 10 12 1 10 10 12 1 100 1 10 12 1 10 100 1 10 12 1 10 100 1 10 12 1 10 100 1 10 12 1 10 100 1 10 12 1 10 100 1 10 12 1 10 100 1 10 12 1 10 100 1 10 12 1 10 100 1 10 10 100 1 10 10 100 1 10 10 100 1 10 12 1 10 100 1 10 12 1 10 100 1 10 12 1 10 100 1 10 12 1 10 100 1 10 12 1 10 100 1 10 10 100 1 10 10 100 1 10 10 100 1 10 12 1 10 100 1 10 12 1 10 100 1 10 12 1 10 100 1 10 12 1 10 100 1 10 12 1 10 10 12 1 10 7 12 1 1 1 10 12 1 7 1 7 1 10 12 1 9 12 1 1 9 12 1 1 10 12 1 1 8 10 12 1 1 8 8 8 8 7 10 12 1 1 10 12 1 100 1 8 1 10 7 1 7 1 7 1 1 1 5 0 1 1 3 1 3 1 1 1 1 3 1 3 1 3 1 1 1 1 1 3 1 3 1 3 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 1 1 1 1 1 1 1 1 1 1 7 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 100 1 100 1 100 1 100 1 100 1 100 1 100 1 100 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1
staticfield java/util/concurrent/ConcurrentHashMap NCPU I 12
staticfield java/util/concurrent/ConcurrentHashMap serialPersistentFields [Ljava/io/ObjectStreamField; 3 [Ljava/io/ObjectStreamField;
staticfield java/util/concurrent/ConcurrentHashMap U Ljdk/internal/misc/Unsafe; jdk/internal/misc/Unsafe
staticfield java/util/concurrent/ConcurrentHashMap SIZECTL J 20
staticfield java/util/concurrent/ConcurrentHashMap TRANSFERINDEX J 32
staticfield java/util/concurrent/ConcurrentHashMap BASECOUNT J 24
staticfield java/util/concurrent/ConcurrentHashMap CELLSBUSY J 36
staticfield java/util/concurrent/ConcurrentHashMap CELLVALUE J 144
staticfield java/util/concurrent/ConcurrentHashMap ABASE I 16
staticfield java/util/concurrent/ConcurrentHashMap ASHIFT I 2
ciInstanceKlass java/util/concurrent/ConcurrentMap 1 1 208 11 7 12 1 1 1 10 100 12 1 1 11 12 1 1 11 100 12 1 1 1 11 7 12 1 1 1 11 12 1 1 100 1 11 12 1 11 12 1 100 1 11 100 12 1 1 1 18 12 1 11 12 1 1 11 100 12 1 1 11 12 1 1 11 100 12 1 11 12 1 1 11 12 1 1 7 1 7 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 16 15 11 12 15 10 100 12 1 1 1 1 1 100 1 100 1 1 1 100 1 8 1 1 12 10 1 1 1 8 1 1 1 8 1 1 8 1 1 1 8 1 1 1 8 1 1 1 8 1 1 1 8 1 1 1 8 1 1 1 8 1 1 1 8 1 1 8 1
instanceKlass org/apache/maven/project/DefaultProjectBuilder$1
instanceKlass java/util/Collections$SingletonMap
instanceKlass org/eclipse/sisu/wire/EntryMapAdapter
instanceKlass com/google/common/collect/Maps$ViewCachingAbstractMap
instanceKlass com/google/common/collect/MapMakerInternalMap
instanceKlass org/eclipse/sisu/wire/MergedProperties
instanceKlass com/google/common/cache/LocalCache
instanceKlass java/util/EnumMap
instanceKlass java/util/TreeMap
instanceKlass java/util/IdentityHashMap
instanceKlass java/util/WeakHashMap
instanceKlass java/util/Collections$EmptyMap
instanceKlass sun/util/PreHashedMap
instanceKlass java/util/HashMap
instanceKlass java/util/ImmutableCollections$AbstractImmutableMap
instanceKlass java/util/concurrent/ConcurrentHashMap
ciInstanceKlass java/util/AbstractMap 1 1 196 10 7 12 1 1 1 10 7 12 1 1 1 11 7 12 1 1 1 10 11 12 1 1 11 7 12 1 1 1 11 12 1 1 7 1 11 12 1 10 12 1 1 11 12 1 100 1 10 11 12 1 11 7 1 10 12 1 1 11 12 1 9 12 1 1 7 1 10 12 1 9 12 1 1 7 1 10 11 11 12 1 1 11 12 1 7 1 100 1 11 12 1 8 1 7 1 10 10 12 1 1 8 1 10 12 1 10 12 1 1 10 12 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 7 1 1 1 1 1 1 1 7 1 1 1 1 1 1 1 1 1 7 1 1 1 1 1 1 1 1 1 100 1 100 1 100 1 100 1 100 1 1 1 1 1 1
ciInstanceKlass jdk/internal/loader/ClassLoaders 1 1 183 10 7 12 1 1 1 10 7 12 1 1 1 10 12 1 1 10 100 12 1 1 1 9 7 12 1 1 1 9 12 1 1 9 12 1 1 7 1 11 100 12 1 1 1 100 1 11 12 1 1 11 12 1 1 10 100 12 1 1 1 10 100 12 1 1 1 100 1 100 1 10 7 12 1 1 1 9 12 1 1 10 12 1 1 7 1 10 12 1 1 10 12 1 7 1 8 1 10 7 12 1 1 1 10 12 1 1 7 1 10 12 1 10 12 1 10 12 1 8 1 10 7 12 1 1 8 1 8 1 10 12 1 7 1 10 12 1 10 12 1 10 12 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 7 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1
staticfield jdk/internal/loader/ClassLoaders JLA Ljdk/internal/access/JavaLangAccess; java/lang/System$2
staticfield jdk/internal/loader/ClassLoaders BOOT_LOADER Ljdk/internal/loader/ClassLoaders$BootClassLoader; jdk/internal/loader/ClassLoaders$BootClassLoader
staticfield jdk/internal/loader/ClassLoaders PLATFORM_LOADER Ljdk/internal/loader/ClassLoaders$PlatformClassLoader; jdk/internal/loader/ClassLoaders$PlatformClassLoader
staticfield jdk/internal/loader/ClassLoaders APP_LOADER Ljdk/internal/loader/ClassLoaders$AppClassLoader; jdk/internal/loader/ClassLoaders$AppClassLoader
instanceKlass jdk/internal/loader/ClassLoaders$BootClassLoader
instanceKlass jdk/internal/loader/ClassLoaders$PlatformClassLoader
instanceKlass jdk/internal/loader/ClassLoaders$AppClassLoader
ciInstanceKlass jdk/internal/loader/BuiltinClassLoader 1 1 737 9 7 12 1 1 1 10 7 12 1 1 1 10 7 12 1 1 1 9 12 1 1 9 12 1 1 7 1 10 12 1 9 12 1 10 12 1 9 12 1 10 7 12 1 1 1 10 7 12 1 1 1 10 7 12 1 1 1 11 7 12 1 1 1 7 1 7 1 10 10 12 1 1 8 1 10 12 1 10 12 7 1 10 12 1 10 12 1 1 11 7 12 1 1 1 11 7 12 1 1 1 11 12 1 1 7 1 8 1 8 1 10 9 12 1 1 10 7 12 1 1 11 12 1 1 10 12 1 1 10 12 1 1 10 12 1 1 10 7 12 1 1 1 10 12 1 10 7 12 1 1 1 10 12 1 1 11 7 12 1 1 1 10 7 12 1 1 7 1 10 7 12 1 1 1 10 12 1 100 1 8 1 10 12 1 1 10 8 1 10 12 1 1 10 12 1 1 10 12 1 1 11 7 12 1 1 11 12 1 7 1 10 11 12 1 1 11 10 12 1 1 7 1 10 12 1 10 7 12 1 10 12 1 7 1 10 12 1 10 7 12 1 1 1 100 1 10 12 1 1 11 12 1 100 1 100 1 10 12 1 10 12 1 1 100 1 100 1 10 12 1 10 12 1 18 12 1 1 10 12 1 10 12 1 1 18 100 1 10 7 12 1 1 1 7 1 10 10 12 1 1 10 12 1 1 10 12 1 1 10 12 1 9 12 1 1 100 1 10 10 12 1 1 10 12 1 1 10 12 1 10 12 10 12 1 1 10 12 1 1 10 12 1 1 10 12 1 10 12 1 1 18 12 1 7 1 10 12 1 1 10 12 1 10 12 1 1 10 12 1 100 1 10 12 1 7 1 10 12 1 10 7 12 1 1 1 10 12 1 11 12 1 7 1 10 12 1 7 1 100 1 10 12 1 10 12 1 11 12 1 1 10 12 1 1 10 12 1 1 10 12 1 1 10 12 1 1 10 12 1 10 12 1 10 12 1 10 12 1 8 1 10 12 1 1 10 7 12 1 1 10 12 1 100 1 8 1 8 1 10 10 12 1 8 1 8 1 10 7 12 1 1 1 11 7 12 1 1 1 9 7 12 1 1 1 10 7 12 1 1 1 9 12 1 9 12 1 9 12 1 9 12 1 9 12 1 9 12 1 10 7 12 1 1 1 8 1 10 12 1 7 1 10 12 1 1 10 12 1 7 1 10 11 12 1 1 10 12 10 12 1 10 12 1 100 1 10 12 1 10 12 1 10 10 12 1 10 7 12 1 1 8 1 10 7 12 1 1 10 12 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 7 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 7 1 7 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 1 16 15 10 12 16 15 10 12 16 15 10 12 16 1 15 10 100 12 1 1 1 1 1 100 1 1 1 1 1 100 1 100 1 1
staticfield jdk/internal/loader/BuiltinClassLoader packageToModule Ljava/util/Map; java/util/concurrent/ConcurrentHashMap
staticfield jdk/internal/loader/BuiltinClassLoader $assertionsDisabled Z 1
ciInstanceKlass jdk/internal/loader/ClassLoaders$AppClassLoader 1 1 119 8 1 10 7 12 1 1 1 10 7 12 1 1 1 10 7 12 1 1 1 10 12 1 1 10 7 12 1 1 1 10 12 1 1 10 12 1 1 7 1 8 1 10 12 10 7 12 1 1 1 10 7 12 1 1 10 12 1 1 10 12 1 1 10 7 12 1 1 1 7 1 10 12 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 7 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 7 1 1 1
ciInstanceKlass jdk/internal/loader/ClassLoaders$PlatformClassLoader 1 1 42 8 1 10 7 12 1 1 1 10 7 12 1 1 1 7 1 10 12 1 7 1 1 1 1 1 1 1 1 1 1 1 1 1 1 7 1 1 1 100 1 1
ciInstanceKlass java/lang/ArithmeticException 1 1 26 10 100 12 1 1 1 10 12 1 100 1 1 1 1 5 0 1 1 1 1 1 1 1 1 1
ciInstanceKlass java/lang/ArrayStoreException 1 1 26 10 100 12 1 1 1 10 12 1 100 1 1 1 1 5 0 1 1 1 1 1 1 1 1 1
ciInstanceKlass java/lang/ClassCastException 1 1 26 10 100 12 1 1 1 10 12 1 100 1 1 1 1 5 0 1 1 1 1 1 1 1 1 1
ciInstanceKlass java/lang/ClassNotFoundException 1 1 96 7 1 10 7 12 1 1 1 10 12 1 10 12 1 1 10 100 12 1 1 1 8 1 10 100 12 1 1 1 10 7 12 1 1 10 100 12 1 1 1 10 100 12 1 1 1 10 12 1 1 7 1 10 12 1 9 12 1 1 1 1 1 5 0 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 1 1 1 1 1 1 1 1 1 1 1
staticfield java/lang/ClassNotFoundException serialPersistentFields [Ljava/io/ObjectStreamField; 1 [Ljava/io/ObjectStreamField;
instanceKlass java/nio/charset/UnsupportedCharsetException
instanceKlass java/lang/NumberFormatException
instanceKlass org/apache/maven/cli/MavenCli$IllegalUseOfUndefinedProperty
ciInstanceKlass java/lang/IllegalArgumentException 1 1 35 10 100 12 1 1 1 10 12 1 10 12 1 10 12 1 100 1 1 1 1 5 0 1 1 1 1 1 1 1 1 1 1 1 1
ciInstanceKlass java/lang/IllegalMonitorStateException 1 1 26 10 100 12 1 1 1 10 12 1 100 1 1 1 1 5 0 1 1 1 1 1 1 1 1 1
ciInstanceKlass java/lang/BootstrapMethodError 0 0 45 10 100 12 1 1 1 10 12 1 10 12 1 10 100 12 1 1 1 10 100 12 1 1 1 1 1 1 5 0 1 1 1 1 1 1 1 1 1 1 1 100 1 1 1
instanceKlass java/lang/ClassFormatError
instanceKlass java/lang/ExceptionInInitializerError
instanceKlass java/lang/UnsatisfiedLinkError
instanceKlass java/lang/IncompatibleClassChangeError
instanceKlass java/lang/BootstrapMethodError
instanceKlass java/lang/NoClassDefFoundError
ciInstanceKlass java/lang/LinkageError 1 1 31 10 7 12 1 1 1 10 12 1 10 12 1 100 1 1 1 1 5 0 1 1 1 1 1 1 1 1 1 1 1
ciInstanceKlass java/lang/NullPointerException 1 1 52 10 7 12 1 1 1 10 12 1 9 7 12 1 1 1 10 12 1 1 9 12 1 1 10 12 1 1 10 12 1 1 1 1 5 0 1 1 1 1 1 1 1 1 100 1 100 1 100 1 1 1
ciInstanceKlass java/lang/NoClassDefFoundError 1 1 26 10 7 12 1 1 1 10 12 1 100 1 1 1 1 5 0 1 1 1 1 1 1 1 1 1
ciInstanceKlass java/lang/StackOverflowError 1 1 26 10 100 12 1 1 1 10 12 1 100 1 1 1 1 5 0 1 1 1 1 1 1 1 1 1
ciInstanceKlass java/lang/StackTraceElement 1 1 235 10 7 12 1 1 1 10 7 12 1 1 9 12 1 1 9 12 1 1 9 12 1 9 12 1 8 1 10 100 12 1 1 1 7 1 9 12 1 8 1 9 12 1 9 12 1 9 12 1 1 10 12 1 1 10 12 1 8 1 10 7 12 1 1 1 7 1 10 12 1 10 12 1 1 10 12 1 10 12 1 1 10 12 1 10 12 1 10 10 12 1 8 1 10 12 1 10 12 1 1 10 12 1 1 10 12 1 10 12 1 10 12 1 9 12 1 1 10 7 12 1 1 1 10 12 1 1 7 1 10 12 1 1 10 7 12 1 1 10 7 12 1 1 1 10 7 12 1 1 10 7 12 1 1 10 10 12 1 1 10 12 1 1 10 12 1 1 10 12 1 7 1 1 1 1 1 1 1 3 1 3 1 1 5 0 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 7 1 7 1 1 1 1 1 1 1 100 1 1 1 1 1 1 1 1 1
instanceKlass java/util/concurrent/locks/AbstractQueuedSynchronizer
ciInstanceKlass java/util/concurrent/locks/AbstractOwnableSynchronizer 1 1 32 10 7 12 1 1 1 9 7 12 1 1 1 100 1 1 1 1 5 0 1 1 1 1 1 1 1 1 1 1 1 1
ciInstanceKlass jdk/internal/vm/Continuation 0 0 549 9 100 12 1 1 1 9 12 1 9 12 1 100 1 7 1 10 12 1 1 8 1 10 12 1 1 10 12 1 10 12 1 1 10 12 1 9 7 12 1 1 1 11 100 12 1 1 1 10 7 1 9 12 1 1 9 12 1 1 10 8 1 10 12 1 9 12 1 1 10 11 12 1 1 100 1 10 100 12 1 1 1 10 12 1 1 10 12 1 10 12 1 1 11 12 1 1 9 12 1 1 10 12 1 1 18 12 1 1 10 7 12 1 1 1 100 1 10 12 1 11 100 12 1 1 1 10 12 1 9 12 1 10 12 1 1 100 1 8 1 10 12 1 10 12 1 1 9 12 1 1 11 12 1 1 9 12 1 1 8 1 10 11 12 1 1 11 12 1 1 10 12 1 1 10 12 1 1 9 12 1 10 12 1 10 10 12 1 8 1 10 12 1 8 1 8 1 10 7 12 1 1 1 10 7 12 1 1 1 10 12 1 9 12 1 11 12 1 7 1 10 12 1 10 12 1 1 9 12 1 1 7 1 10 12 1 1 9 12 1 1 10 12 1 10 12 1 11 7 12 1 1 10 7 1 10 12 1 8 1 9 12 1 10 12 1 1 9 12 1 1 10 7 12 1 1 8 1 8 1 8 1 8 1 10 12 1 1 10 12 1 10 12 1 10 12 1 10 12 1 1 10 12 1 1 8 1 10 12 1 1 9 12 1 1 10 12 1 1 9 12 1 8 1 10 7 12 1 1 1 10 12 1 8 1 100 1 8 1 10 9 12 1 1 8 1 10 7 12 1 1 10 100 12 1 1 8 1 8 1 10 12 10 100 12 1 1 1 10 7 1 10 7 12 1 1 1 18 11 100 12 1 1 1 18 12 1 11 12 1 1 7 1 10 7 12 1 1 10 12 1 1 8 10 12 1 1 10 100 12 1 1 1 10 100 12 1 1 10 12 1 8 1 10 12 1 7 1 7 1 10 12 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 1 16 1 15 10 12 16 15 11 7 12 1 1 1 16 1 16 1 15 10 12 16 15 10 100 12 1 1 1 1 1 1 1 1 100 1 100 1 1
ciInstanceKlass jdk/internal/misc/UnsafeConstants 1 1 34 10 100 12 1 1 1 9 7 12 1 1 1 9 12 1 9 12 1 1 9 12 1 9 12 1 1 1 1 1 1 1 1 1
staticfield jdk/internal/misc/UnsafeConstants ADDRESS_SIZE0 I 8
staticfield jdk/internal/misc/UnsafeConstants PAGE_SIZE I 4096
staticfield jdk/internal/misc/UnsafeConstants BIG_ENDIAN Z 0
staticfield jdk/internal/misc/UnsafeConstants UNALIGNED_ACCESS Z 1
staticfield jdk/internal/misc/UnsafeConstants DATA_CACHE_LINE_FLUSH_SIZE I 0
instanceKlass org/apache/maven/artifact/versioning/ManagedVersionMap
instanceKlass java/lang/ProcessEnvironment
instanceKlass java/util/LinkedHashMap
ciInstanceKlass java/util/HashMap 1 1 629 10 7 12 1 1 1 7 1 10 12 1 1 7 1 10 7 12 1 1 1 100 1 11 12 1 1 11 12 1 11 12 1 1 10 7 12 1 1 1 7 1 3 10 7 12 1 1 1 100 1 7 1 10 8 1 10 12 1 1 10 12 1 10 12 1 1 10 12 1 10 7 12 1 1 1 8 1 10 12 1 9 12 1 1 10 12 1 9 12 1 1 4 10 12 1 10 12 1 1 11 7 12 1 1 9 12 1 1 10 7 12 1 1 1 6 0 10 12 1 1 11 12 1 1 11 7 12 1 1 1 11 7 12 1 1 1 11 12 1 1 7 1 11 12 1 11 12 1 10 12 1 10 12 1 1 9 12 10 12 1 1 9 7 12 1 1 1 9 12 9 12 1 10 12 1 1 9 12 1 7 1 10 12 1 1 10 12 1 1 10 12 1 1 10 12 1 1 10 12 1 1 9 12 1 10 12 1 1 3 4 10 12 1 1 10 12 1 1 9 12 1 1 9 10 12 1 1 10 12 1 1 10 12 1 1 10 12 1 9 12 1 1 7 1 10 12 1 10 12 1 10 7 12 1 1 1 7 1 9 12 1 1 7 1 10 9 12 7 1 10 100 1 10 11 7 12 1 1 1 100 1 10 11 100 12 1 1 11 100 12 1 1 1 10 12 1 100 1 7 1 10 12 1 10 12 1 10 12 1 10 100 12 1 1 10 12 1 1 10 12 1 1 10 100 12 1 1 1 8 10 100 12 1 1 1 100 1 10 4 4 10 12 1 1 10 100 12 1 1 1 10 12 1 8 1 6 0 10 100 12 1 1 1 100 1 11 100 12 1 1 1 10 12 1 10 12 1 10 10 12 1 1 6 0 8 1 10 12 1 10 12 7 1 7 1 1 1 1 5 0 1 3 1 1 1 3 1 3 1 3 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 7 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 1 1 1 1 1 1 1 7 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 100 1 100 1 100 1 100 1 100 1 100 1 100 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1
ciMethod java/util/HashMap put (Ljava/lang/Object;Ljava/lang/Object;)Ljava/lang/Object; 6920 0 19363 0 2088
ciMethod java/util/HashMap newNode (ILjava/lang/Object;Ljava/lang/Object;Ljava/util/HashMap$Node;)Ljava/util/HashMap$Node; 492 0 5426 0 -1
ciMethod java/util/HashMap afterNodeAccess (Ljava/util/HashMap$Node;)V 40 0 53 0 -1
ciMethod java/util/HashMap afterNodeInsertion (Z)V 536 0 1298 0 -1
ciInstanceKlass java/lang/invoke/LambdaForm 1 1 1059 7 1 100 1 10 7 12 1 1 1 9 12 1 1 9 12 1 9 12 1 9 12 1 1 9 12 1 1 9 12 1 1 9 12 1 1 9 12 1 1 9 12 1 10 7 12 1 1 9 12 1 10 12 1 1 100 1 10 10 12 1 1 10 12 1 10 12 1 10 12 1 1 10 12 1 9 12 1 9 12 1 10 12 1 1 10 7 12 1 1 1 10 12 1 1 9 100 12 1 1 1 10 12 1 1 9 7 12 1 1 10 12 1 10 12 1 1 10 12 1 1 10 12 1 1 7 1 10 7 12 1 1 1 10 12 1 1 10 12 1 10 12 1 1 9 12 1 1 9 12 1 9 12 1 1 10 12 1 9 12 1 10 100 12 1 1 1 10 12 1 1 7 1 10 12 1 9 12 1 1 10 7 12 1 1 1 10 12 1 10 12 1 7 1 10 12 1 10 12 1 10 12 1 1 10 12 1 1 10 12 1 10 12 1 10 12 1 10 12 1 1 8 1 10 12 1 10 12 1 10 10 12 1 8 1 8 1 9 12 1 9 12 1 1 10 12 1 1 10 12 1 9 12 1 10 12 1 1 10 7 12 1 1 1 10 12 1 1 10 12 1 10 12 1 10 100 12 1 1 1 9 12 1 10 12 1 8 1 8 1 8 1 8 1 8 1 8 1 8 1 8 1 10 12 1 9 12 1 7 1 10 12 1 1 9 12 1 10 12 1 10 12 1 1 10 12 10 12 1 10 12 1 1 10 12 1 1 10 10 12 1 1 10 12 1 1 7 1 8 1 10 12 1 1 10 12 1 10 12 1 1 10 8 1 10 12 1 1 8 1 8 1 8 1 10 12 1 10 12 1 10 12 1 1 100 1 10 12 1 1 10 12 1 1 10 7 12 1 1 1 10 12 1 1 9 12 1 1 8 1 10 100 12 1 1 1 10 7 12 1 1 10 12 10 12 1 1 10 12 1 1 9 12 1 8 10 12 1 1 100 1 10 12 1 1 10 12 1 9 7 12 1 1 9 7 12 1 1 1 8 1 10 100 12 1 1 10 12 1 1 7 1 7 1 10 10 12 1 1 10 12 1 1 8 1 8 1 7 1 8 1 10 12 10 12 1 10 12 1 10 12 1 1 8 1 8 1 10 12 1 10 12 1 1 8 1 8 1 8 1 7 1 8 1 7 1 8 1 7 1 8 1 10 12 1 8 1 9 10 7 12 1 1 1 10 12 1 9 12 1 1 10 12 1 10 10 12 1 10 12 1 10 12 1 10 12 1 1 10 12 1 10 12 1 1 10 12 1 1 10 12 1 1 10 12 1 1 8 1 100 10 12 1 10 12 1 9 12 1 1 10 7 12 1 1 8 1 8 1 7 1 8 1 8 1 8 1 8 1 8 1 8 1 8 1 8 1 10 12 1 1 10 12 1 1 10 12 1 10 12 1 10 12 1 10 12 1 8 1 8 1 8 1 10 12 1 8 1 10 12 1 8 1 8 1 8 1 8 1 8 1 10 12 1 10 12 1 10 12 1 1 10 7 12 1 1 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 9 12 1 1 8 1 10 12 1 1 9 12 1 1 10 12 1 1 9 12 1 9 12 1 1 9 12 1 9 12 1 1 7 1 10 7 12 1 1 1 9 12 1 10 12 1 10 12 1 8 1 10 12 1 9 12 1 1 7 1 10 7 12 1 1 1 8 1 100 1 10 12 1 9 12 1 9 12 1 10 12 1 10 12 1 10 7 12 1 1 1 10 12 1 9 7 12 1 1 10 12 1 1 10 12 1 10 12 1 10 12 1 9 12 10 12 1 10 10 12 1 9 12 1 9 9 12 1 7 9 12 1 1 10 12 1 1 9 12 1 10 12 1 10 7 1 9 1 1 1 1 3 1 3 1 1 3 1 3 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 7 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 1 1 100 1 1 7 1 1 1 1 1 1 1 1 1 1 1
staticfield java/lang/invoke/LambdaForm DEFAULT_CUSTOMIZED Ljava/lang/invoke/MethodHandle; null
staticfield java/lang/invoke/LambdaForm DEFAULT_KIND Ljava/lang/invoke/LambdaForm$Kind; java/lang/invoke/LambdaForm$Kind
staticfield java/lang/invoke/LambdaForm COMPILE_THRESHOLD I 0
staticfield java/lang/invoke/LambdaForm INTERNED_ARGUMENTS [[Ljava/lang/invoke/LambdaForm$Name; 5 [[Ljava/lang/invoke/LambdaForm$Name;
staticfield java/lang/invoke/LambdaForm IMPL_NAMES Ljava/lang/invoke/MemberName$Factory; java/lang/invoke/MemberName$Factory
staticfield java/lang/invoke/LambdaForm LF_identity [Ljava/lang/invoke/LambdaForm; 6 [Ljava/lang/invoke/LambdaForm;
staticfield java/lang/invoke/LambdaForm LF_zero [Ljava/lang/invoke/LambdaForm; 6 [Ljava/lang/invoke/LambdaForm;
staticfield java/lang/invoke/LambdaForm NF_identity [Ljava/lang/invoke/LambdaForm$NamedFunction; 6 [Ljava/lang/invoke/LambdaForm$NamedFunction;
staticfield java/lang/invoke/LambdaForm NF_zero [Ljava/lang/invoke/LambdaForm$NamedFunction; 6 [Ljava/lang/invoke/LambdaForm$NamedFunction;
staticfield java/lang/invoke/LambdaForm createFormsLock Ljava/lang/Object; java/lang/Object
staticfield java/lang/invoke/LambdaForm DEBUG_NAME_COUNTERS Ljava/util/HashMap; null
staticfield java/lang/invoke/LambdaForm DEBUG_NAMES Ljava/util/HashMap; null
staticfield java/lang/invoke/LambdaForm TRACE_INTERPRETER Z 0
staticfield java/lang/invoke/LambdaForm $assertionsDisabled Z 1
ciInstanceKlass java/lang/invoke/MemberName 1 1 724 7 1 7 1 100 1 9 12 1 1 10 7 12 1 1 1 9 12 1 1 10 12 1 1 10 12 1 1 10 12 1 1 10 12 1 10 12 1 1 10 7 12 1 1 1 10 12 1 9 7 12 1 1 10 12 1 7 1 7 1 10 12 1 8 1 10 12 1 1 10 12 1 10 12 1 1 10 12 1 9 12 1 1 8 1 10 100 12 1 1 1 7 1 10 10 12 1 1 100 1 100 1 10 12 1 9 12 1 1 100 1 8 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 1 10 12 1 10 12 1 10 12 1 1 8 1 8 1 10 12 1 8 1 9 12 1 1 3 10 12 1 10 12 1 10 12 1 10 10 7 12 1 1 1 10 12 1 10 12 1 10 12 1 10 12 1 7 1 8 10 12 1 1 10 12 1 1 8 1 9 7 1 8 9 7 1 10 12 1 1 10 12 1 10 12 1 8 1 10 12 1 1 10 12 1 10 12 1 10 12 1 10 12 1 1 7 1 10 12 1 1 10 12 1 8 1 10 12 1 10 12 1 8 1 10 12 1 1 10 12 1 1 7 1 10 12 1 1 10 12 8 1 8 1 7 1 10 12 1 10 100 12 1 1 1 100 1 10 12 10 12 1 10 12 1 10 12 1 10 12 1 10 10 12 1 10 12 1 10 12 1 3 10 12 1 3 10 12 1 3 3 3 3 3 3 10 12 1 3 9 12 1 10 12 1 1 3 10 12 1 10 10 7 12 1 1 1 10 12 1 1 10 7 12 1 1 10 10 12 1 10 12 1 1 10 12 1 10 10 12 1 1 10 12 1 10 12 1 10 12 1 7 1 10 10 10 12 100 1 10 10 10 12 1 1 10 12 1 1 10 10 12 1 8 10 7 1 10 12 1 10 7 1 10 12 1 10 12 1 10 12 1 10 10 12 1 10 12 1 1 10 12 1 1 10 12 1 10 12 1 10 12 1 100 1 10 12 1 1 100 1 8 1 10 7 1 10 12 1 10 12 10 12 1 1 10 12 1 10 12 1 8 1 8 1 8 1 8 1 10 12 1 10 12 1 10 7 12 1 1 1 8 1 8 1 10 12 1 8 1 10 10 10 12 1 10 12 1 8 1 8 1 10 10 12 1 8 1 10 100 12 1 1 1 8 1 100 1 10 12 1 10 12 1 1 10 12 1 8 1 8 1 8 1 8 1 100 1 10 8 1 8 1 8 1 8 1 10 12 1 100 1 100 1 100 1 10 100 1 10 7 1 10 7 12 1 1 1 9 7 12 1 1 1 7 1 7 1 1 1 1 1 1 1 3 1 3 1 3 1 3 1 3 1 3 1 1 1 1 1 1 1 1 3 1 3 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 7 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1
staticfield java/lang/invoke/MemberName $assertionsDisabled Z 1
instanceKlass java/lang/invoke/VarHandleInts$FieldInstanceReadOnly
instanceKlass java/lang/invoke/VarHandleBooleans$FieldInstanceReadOnly
instanceKlass java/lang/invoke/VarHandleReferences$Array
instanceKlass java/lang/invoke/VarHandleReferences$FieldInstanceReadOnly
instanceKlass java/lang/invoke/VarHandleByteArrayAsDoubles$ByteArrayViewVarHandle
instanceKlass java/lang/invoke/VarHandleByteArrayAsLongs$ByteArrayViewVarHandle
instanceKlass java/lang/invoke/VarHandleByteArrayAsFloats$ByteArrayViewVarHandle
instanceKlass java/lang/invoke/VarHandleByteArrayAsInts$ByteArrayViewVarHandle
instanceKlass java/lang/invoke/VarHandleByteArrayAsChars$ByteArrayViewVarHandle
instanceKlass java/lang/invoke/VarHandleByteArrayAsShorts$ByteArrayViewVarHandle
ciInstanceKlass java/lang/invoke/VarHandle 1 1 473 10 7 12 1 1 1 10 7 12 1 1 9 12 1 1 9 12 1 1 100 1 10 8 1 10 12 1 1 10 7 12 1 1 1 10 12 1 1 10 7 12 1 1 1 9 100 12 1 1 1 10 12 1 1 10 7 12 1 1 1 10 12 1 1 9 12 1 10 12 1 9 12 1 1 10 100 12 1 1 10 12 1 9 100 12 1 1 1 9 12 1 1 10 12 1 1 100 1 7 1 10 8 1 10 12 1 1 10 12 1 8 1 10 12 1 10 12 1 9 12 1 1 9 12 1 10 12 1 10 12 1 1 10 12 1 10 10 100 12 1 1 1 10 12 1 1 10 12 1 1 10 7 12 1 1 1 10 100 12 1 1 1 10 100 12 1 1 1 9 12 1 1 9 10 12 1 10 12 1 10 12 1 1 10 12 1 10 7 12 1 1 1 9 12 1 1 10 12 1 1 9 12 1 10 12 1 10 12 1 10 7 12 1 1 100 1 10 9 7 12 1 1 1 9 12 1 1 10 7 12 1 1 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 1 8 10 12 1 1 7 1 10 12 1 1 7 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 1 1 1 1 1 1 1 100 1 1 1 1 1 1 1 1 1 1 1 1 100 1 100 1 1 100 1 100 1 100 1 100 1 100 1 100 1 100 1 100 1 100 1 100 1 100 1 100 1 100 1 100 1 100 1 100 1 100 1 100 1 100 1 100 1 100 1 100 1 100 1 100 1 100 1 100 1 100 1 100 1 100 1 100 1 100 1 100 1 100 1 100 1 100 1 1 1 1 1 1 100 1 1 1 100 1 1 100 1 100 1 100 1 100 1 100 1 100 1 1 1 1 100 1 100 1 100 1 100 1 100 1 100 1 100 1 100 1
staticfield java/lang/invoke/VarHandle VFORM_OFFSET J 16
staticfield java/lang/invoke/VarHandle $assertionsDisabled Z 1
instanceKlass jdk/internal/reflect/FieldAccessorImpl
instanceKlass jdk/internal/reflect/ConstructorAccessorImpl
instanceKlass jdk/internal/reflect/MethodAccessorImpl
ciInstanceKlass jdk/internal/reflect/MagicAccessorImpl 1 1 16 10 7 12 1 1 1 100 1 1 1 1 1 1 1 1
instanceKlass jdk/internal/reflect/DirectMethodHandleAccessor
ciInstanceKlass jdk/internal/reflect/MethodAccessorImpl 1 1 38 10 7 12 1 1 1 10 100 12 1 1 1 100 1 1 1 1 1 1 1 100 1 100 1 1 1 1 1 1 1 1 1 1 1 1 1 1
ciInstanceKlass jdk/internal/reflect/MethodAccessor 1 0 17 100 1 100 1 1 1 1 100 1 100 1 1 1 1 1 1
instanceKlass jdk/internal/reflect/DirectConstructorHandleAccessor$NativeAccessor
instanceKlass jdk/internal/reflect/DirectConstructorHandleAccessor
instanceKlass jdk/internal/reflect/NativeConstructorAccessorImpl
ciInstanceKlass jdk/internal/reflect/ConstructorAccessorImpl 1 1 27 10 7 12 1 1 1 100 1 100 1 1 1 1 1 1 1 1 1 100 1 100 1 100 1 1 1
ciInstanceKlass jdk/internal/reflect/ConstructorAccessor 1 0 16 100 1 100 1 1 1 1 100 1 100 1 100 1 1 1
ciInstanceKlass jdk/internal/reflect/DelegatingClassLoader 0 0 18 10 100 12 1 1 1 100 1 1 1 1 1 1 1 1 1 1
ciInstanceKlass jdk/internal/reflect/CallerSensitive 0 0 17 100 1 100 1 100 1 1 1 1 1 1 1 1 1 1 1
ciInstanceKlass jdk/internal/reflect/NativeConstructorAccessorImpl 0 0 125 10 7 12 1 1 1 9 7 12 1 1 1 100 1 10 12 1 9 12 1 1 9 12 1 1 10 100 12 1 1 1 10 7 12 1 1 1 10 7 12 1 1 1 9 12 1 9 12 1 1 9 12 1 1 10 7 12 1 1 1 100 1 10 10 12 1 1 10 12 1 10 12 1 1 10 12 1 7 1 10 12 1 1 10 12 1 1 8 10 12 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 100 1 100 1 1 1 1 1 1 1
ciInstanceKlass jdk/internal/reflect/ConstantPool 1 1 142 10 7 12 1 1 1 9 7 12 1 1 1 10 12 1 1 10 12 1 1 10 12 1 10 12 1 1 10 12 1 1 10 12 1 10 12 1 1 10 12 1 10 12 1 1 10 12 1 10 12 1 10 12 1 10 12 1 1 10 12 1 1 10 12 1 1 10 12 1 1 10 12 1 10 12 1 1 10 100 12 1 1 1 8 11 7 12 1 1 1 10 7 12 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1
ciInstanceKlass jdk/internal/reflect/UnsafeStaticFieldAccessorImpl 0 0 47 10 7 12 1 1 1 9 7 12 1 1 1 10 7 12 1 1 1 9 12 1 1 8 11 100 12 1 1 1 10 100 12 1 1 1 1 1 1 1 1 1 1 1 1 1 1
ciInstanceKlass jdk/internal/reflect/FieldAccessor 1 0 48 100 1 100 1 1 1 1 100 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1
instanceKlass jdk/internal/reflect/MethodHandleFieldAccessorImpl
instanceKlass jdk/internal/reflect/UnsafeFieldAccessorImpl
ciInstanceKlass jdk/internal/reflect/FieldAccessorImpl 1 1 269 10 7 12 1 1 1 9 7 12 1 1 1 10 7 12 1 1 1 10 7 12 1 1 10 7 12 1 1 1 10 12 1 1 7 1 10 10 12 1 1 10 12 1 1 8 1 10 10 12 1 100 1 8 1 10 12 1 8 1 10 12 1 8 1 10 12 1 100 1 10 12 1 1 10 8 1 10 12 1 1 8 1 10 7 12 1 1 8 1 10 7 12 1 1 8 1 10 7 12 1 1 8 1 10 7 12 1 1 8 1 10 7 12 1 1 8 1 10 7 12 1 1 8 1 10 7 12 1 1 8 1 10 7 12 1 1 10 12 1 1 8 1 10 12 1 1 10 100 12 1 1 1 8 1 10 12 1 8 1 8 1 8 1 8 1 10 7 12 1 1 1 8 1 8 1 8 1 10 12 7 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1
instanceKlass jdk/internal/reflect/UnsafeStaticFieldAccessorImpl
ciInstanceKlass jdk/internal/reflect/UnsafeFieldAccessorImpl 0 0 62 10 100 12 1 1 1 10 100 12 1 1 1 10 100 12 1 1 1 9 100 12 1 1 10 12 1 9 12 1 1 10 100 12 1 1 1 9 12 1 1 10 12 1 10 12 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1
instanceKlass java/lang/invoke/VolatileCallSite
instanceKlass java/lang/invoke/MutableCallSite
instanceKlass java/lang/invoke/ConstantCallSite
ciInstanceKlass java/lang/invoke/CallSite 1 1 307 10 7 12 1 1 1 10 7 12 1 1 1 9 7 12 1 1 1 10 12 1 1 9 12 1 1 10 7 12 1 1 1 10 12 1 7 1 10 12 1 1 10 12 1 1 9 100 12 1 1 1 10 7 12 1 1 10 12 1 1 100 1 7 1 10 10 7 12 1 1 1 10 12 1 1 8 1 10 12 1 10 12 1 1 10 12 1 10 12 1 1 10 12 1 1 10 10 100 12 1 1 10 12 1 1 9 12 1 9 100 12 1 1 1 8 1 10 7 12 1 1 1 10 12 1 1 7 1 10 12 1 1 9 12 1 8 1 100 1 10 12 1 10 12 1 100 1 8 1 10 10 12 1 10 12 1 1 100 1 10 12 1 1 10 12 1 10 12 1 10 12 1 1 10 12 1 1 9 12 1 1 8 10 12 1 1 9 12 1 1 100 1 10 10 12 1 10 7 12 1 1 1 10 12 1 1 10 12 1 1 10 12 1 10 7 12 1 1 1 7 1 8 1 10 10 12 10 12 1 1 7 1 7 1 7 1 8 1 10 12 1 10 7 12 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 7 1 7 1 1 1 1
staticfield java/lang/invoke/CallSite $assertionsDisabled Z 1
ciInstanceKlass java/lang/invoke/ConstantCallSite 1 1 65 10 7 12 1 1 1 9 7 12 1 1 1 9 12 1 1 10 7 12 1 1 1 10 12 1 100 1 10 12 9 12 1 1 100 1 10 10 12 1 1 10 12 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 1 1 1 1 1 1 1
staticfield java/lang/invoke/ConstantCallSite UNSAFE Ljdk/internal/misc/Unsafe; jdk/internal/misc/Unsafe
instanceKlass java/lang/invoke/DirectMethodHandle$StaticAccessor
instanceKlass java/lang/invoke/DirectMethodHandle$Special
instanceKlass java/lang/invoke/DirectMethodHandle$Interface
instanceKlass java/lang/invoke/DirectMethodHandle$Accessor
instanceKlass java/lang/invoke/DirectMethodHandle$Constructor
ciInstanceKlass java/lang/invoke/DirectMethodHandle 1 1 923 7 1 7 1 100 1 7 1 7 1 10 7 12 1 1 1 10 7 12 1 1 1 7 1 10 12 1 10 12 1 1 10 7 12 1 1 10 12 1 1 10 12 1 10 12 1 7 1 10 12 1 1 10 12 1 1 10 12 1 10 12 1 1 10 7 12 1 1 1 10 12 1 9 12 1 1 100 1 10 9 12 1 1 9 12 1 10 12 1 10 12 1 10 12 1 1 10 12 1 10 12 1 10 12 1 10 7 12 1 1 1 10 12 1 10 12 1 1 8 1 10 12 1 1 7 1 10 12 1 7 1 10 10 12 1 10 12 1 10 12 1 10 7 12 1 1 1 10 12 1 1 7 1 10 12 1 10 12 1 7 1 10 12 1 10 12 1 1 10 12 1 1 10 12 1 8 10 7 12 1 1 1 10 12 1 10 12 1 10 12 1 1 10 12 1 9 7 12 1 1 1 7 1 10 12 1 10 7 12 1 1 1 10 12 1 10 12 1 1 9 12 1 1 7 1 10 8 1 10 12 1 1 10 12 1 1 8 1 10 12 1 10 12 1 10 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 1 10 12 1 10 12 1 1 10 12 1 10 12 1 1 10 10 7 12 1 1 1 10 12 1 10 12 1 1 10 12 1 10 12 1 1 8 1 9 7 12 1 1 1 8 1 9 12 1 9 12 1 8 1 9 12 1 9 12 1 8 1 9 12 1 9 12 1 8 1 10 12 1 10 12 1 1 9 12 1 1 7 1 10 12 1 1 7 1 10 7 12 1 1 1 10 12 1 1 10 12 1 10 12 1 1 7 1 10 12 1 1 10 12 1 10 12 1 1 7 1 10 12 1 1 10 12 1 10 7 12 1 1 1 10 12 1 10 12 1 1 10 12 1 9 12 1 1 10 7 1 9 12 9 12 1 10 7 12 1 1 1 10 12 1 7 1 7 1 7 1 9 12 1 1 10 7 12 1 1 1 10 12 10 12 1 100 1 10 12 1 10 12 1 1 8 1 9 12 1 9 12 1 10 12 1 1 9 12 1 1 10 7 12 1 1 1 9 12 1 1 9 12 1 1 10 12 1 10 12 1 10 12 1 10 12 1 1 10 12 1 9 12 1 10 12 1 10 12 1 10 12 1 10 12 1 1 10 12 1 1 9 12 1 1 10 12 1 1 9 7 12 1 1 1 9 12 1 9 12 1 9 12 1 9 12 1 9 12 1 9 12 1 9 12 1 9 12 1 9 12 1 9 12 1 9 12 1 9 12 1 9 12 1 9 12 1 9 12 1 9 12 1 9 12 1 9 12 1 9 12 1 9 12 1 9 12 1 9 12 1 9 12 1 9 12 1 9 12 1 9 12 1 9 12 1 9 12 1 9 12 1 9 12 1 9 12 1 9 12 1 9 12 1 9 12 1 9 12 1 9 12 1 8 1 9 12 1 1 9 12 1 1 10 12 1 10 12 1 1 9 7 1 10 12 1 9 12 1 1 10 12 10 12 1 10 12 1 10 12 1 10 12 1 10 8 1 8 1 8 1 8 1 10 12 1 1 9 12 1 1 10 12 1 10 100 12 1 1 1 8 9 12 1 1 10 12 1 1 8 1 8 8 9 12 1 8 1 8 8 8 8 8 1 8 10 12 1 7 1 10 12 1 8 1 8 1 10 12 1 10 12 1 10 12 1 10 12 1 1 7 1 1 1 1 3 1 3 1 3 1 3 1 3 1 3 1 3 1 3 1 3 1 3 1 1 1 1 1 1 1 1 1 1 3 1 3 1 1 1 1 3 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 1 1 1 1 1 1 1 1 100 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1
staticfield java/lang/invoke/DirectMethodHandle IMPL_NAMES Ljava/lang/invoke/MemberName$Factory; java/lang/invoke/MemberName$Factory
staticfield java/lang/invoke/DirectMethodHandle FT_UNCHECKED_REF I 8
staticfield java/lang/invoke/DirectMethodHandle ACCESSOR_FORMS [Ljava/lang/invoke/LambdaForm; 132 [Ljava/lang/invoke/LambdaForm;
staticfield java/lang/invoke/DirectMethodHandle ALL_WRAPPERS [Lsun/invoke/util/Wrapper; 10 [Lsun/invoke/util/Wrapper;
staticfield java/lang/invoke/DirectMethodHandle NFS [Ljava/lang/invoke/LambdaForm$NamedFunction; 12 [Ljava/lang/invoke/LambdaForm$NamedFunction;
staticfield java/lang/invoke/DirectMethodHandle OBJ_OBJ_TYPE Ljava/lang/invoke/MethodType; java/lang/invoke/MethodType
staticfield java/lang/invoke/DirectMethodHandle LONG_OBJ_TYPE Ljava/lang/invoke/MethodType; java/lang/invoke/MethodType
staticfield java/lang/invoke/DirectMethodHandle $assertionsDisabled Z 1
ciInstanceKlass java/lang/invoke/MutableCallSite 0 0 63 10 100 12 1 1 1 10 12 1 9 100 12 1 1 1 10 12 1 10 12 1 1 9 12 1 1 10 100 12 1 1 1 10 100 12 1 1 1 10 12 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 1 1
ciInstanceKlass java/lang/invoke/VolatileCallSite 0 0 37 10 100 12 1 1 1 10 12 1 10 100 12 1 1 1 10 12 1 10 12 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1
ciInstanceKlass java/lang/invoke/ResolvedMethodName 1 1 16 10 100 12 1 1 1 100 1 1 1 1 1 1 1 1
ciInstanceKlass java/lang/invoke/MethodHandleNatives 1 1 690 100 1 10 7 12 1 1 1 9 7 12 1 1 1 10 12 1 1 100 1 10 10 12 1 1 10 12 1 10 12 1 8 1 8 1 8 1 8 1 8 1 8 1 8 1 8 1 8 1 8 1 10 12 1 1 7 1 10 7 12 1 1 1 10 7 12 1 1 1 7 1 10 10 12 1 1 8 1 10 12 1 8 1 10 12 1 1 8 1 10 12 1 1 9 7 12 1 1 1 8 1 10 100 12 1 1 1 7 1 10 12 100 1 100 1 8 1 7 1 10 10 12 1 7 1 9 7 12 1 1 10 12 1 1 10 12 1 10 7 12 1 1 1 10 12 1 1 9 12 1 8 1 10 12 1 1 10 12 1 8 1 10 12 1 1 7 1 10 12 1 10 7 12 1 1 1 10 12 1 10 12 1 1 10 12 1 1 8 1 10 12 1 1 8 1 10 12 1 8 1 8 1 8 1 7 1 10 12 1 8 1 10 7 12 1 1 1 10 12 1 1 10 7 12 1 1 10 10 12 1 1 10 12 1 10 100 12 1 1 1 100 1 8 1 10 100 12 1 1 1 7 1 8 1 10 12 1 8 1 8 1 8 1 8 1 8 1 10 12 1 1 10 12 1 1 10 12 1 10 12 1 1 10 12 1 1 7 1 10 12 1 7 1 7 1 10 12 1 10 12 1 8 1 8 1 10 10 12 1 1 10 12 1 1 8 1 10 7 12 1 1 8 1 8 1 10 12 1 1 10 7 12 1 1 1 100 1 10 12 1 1 7 1 9 12 1 1 10 7 12 1 1 1 10 10 12 1 9 12 1 10 12 1 9 12 1 9 12 1 10 12 1 1 10 12 1 10 12 1 1 7 1 7 1 10 12 1 1 10 12 1 10 12 1 1 10 7 12 1 1 1 10 12 1 8 1 10 12 1 10 12 1 10 12 1 1 10 12 1 10 7 12 1 1 1 10 12 1 1 100 1 8 1 10 9 7 12 1 1 1 10 12 1 1 10 12 1 1 7 1 10 12 1 1 10 12 1 1 100 1 100 1 10 10 100 1 100 1 10 100 1 10 10 12 1 1 10 7 1 10 12 1 10 12 1 1 10 12 1 1 10 12 1 1 10 12 1 10 12 1 1 10 8 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 1 10 12 1 10 12 1 10 12 1 10 10 7 12 1 1 10 12 1 1 1 1 1 1 1 1 1 1 1 1 1 7 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 7 1 1 1 1 1 1 1 100 1 1 1
staticfield java/lang/invoke/MethodHandleNatives $assertionsDisabled Z 1
ciInstanceKlass java/lang/invoke/MethodHandleNatives$CallSiteContext 1 1 49 10 7 12 1 1 1 7 1 10 10 7 12 1 1 1 10 7 12 1 1 1 10 7 12 1 1 1 100 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 1
ciInstanceKlass jdk/internal/foreign/abi/NativeEntryPoint 0 0 194 10 7 12 1 1 1 9 7 12 1 1 1 9 12 1 1 100 1 8 1 10 12 1 10 12 1 1 100 1 10 100 12 1 1 1 10 12 1 9 12 1 1 18 12 1 1 10 100 12 1 1 1 10 7 12 1 1 1 9 7 12 1 1 1 7 1 10 8 1 10 12 1 1 10 12 1 10 12 1 1 8 1 10 12 1 1 7 1 8 1 10 12 1 10 12 1 1 10 12 1 9 12 1 1 18 12 1 1 10 100 12 1 1 1 10 12 1 1 10 12 1 10 100 12 1 1 1 10 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 16 1 15 10 12 16 1 16 15 10 12 15 10 100 12 1 1 1 1 1 100 1 1 100 1 100 1 1
ciInstanceKlass jdk/internal/foreign/abi/ABIDescriptor 0 0 55 10 100 12 1 1 1 9 100 12 1 1 1 9 12 1 1 9 12 1 9 12 1 9 12 1 1 9 12 1 9 12 1 1 9 12 1 9 12 1 9 12 1 9 12 1 1 1 1 1 1 1 1 1 1
ciInstanceKlass jdk/internal/foreign/abi/VMStorage 0 0 91 10 7 12 1 1 1 9 7 12 1 1 1 9 12 1 1 9 12 1 1 9 12 1 1 7 1 10 8 1 10 12 1 1 10 12 1 10 12 1 1 10 12 1 18 12 1 18 12 1 1 18 12 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 8 1 15 15 15 15 15 10 100 12 1 1 1 1 100 1 100 1 1
ciInstanceKlass jdk/internal/foreign/abi/UpcallLinker$CallRegs 0 0 66 10 7 12 1 1 1 9 7 12 1 1 1 9 12 1 18 12 1 1 18 12 1 1 18 12 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 1 1 8 1 15 15 15 10 100 12 1 1 1 1 1 100 1 100 1 1
ciInstanceKlass java/lang/StackWalker 0 0 271 9 7 12 1 1 1 100 1 10 100 12 1 1 1 10 12 1 1 10 100 12 1 1 1 10 12 1 1 10 12 1 11 100 12 1 1 1 10 12 1 1 10 12 1 1 10 12 1 1 100 1 8 1 10 12 1 10 12 1 10 12 1 10 7 12 1 1 9 12 1 1 9 12 1 1 9 12 1 1 9 12 1 1 10 12 1 1 9 12 1 1 9 12 1 1 9 12 1 1 10 7 12 1 1 1 11 12 1 1 100 1 8 1 10 10 7 12 1 1 9 12 1 1 10 12 1 1 10 100 12 1 1 1 10 100 12 1 1 1 18 12 1 1 100 1 8 1 10 8 1 10 12 1 1 10 100 12 1 1 1 10 12 1 1 9 100 12 1 1 11 100 12 1 1 1 10 12 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 7 1 1 16 15 10 12 16 1 15 10 100 12 1 1 1 1 1 1 1 1 1 100 1 100 1 1
ciInstanceKlass java/lang/StackWalker$StackFrame 0 0 41 100 1 10 12 1 1 100 1 100 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 1 1
instanceKlass java/lang/LiveStackFrameInfo
ciInstanceKlass java/lang/StackFrameInfo 0 0 142 10 7 12 1 1 1 9 7 12 1 1 1 9 7 1 9 12 1 1 11 100 12 1 1 1 9 12 1 1 11 12 1 1 10 12 1 1 10 7 12 1 1 1 10 12 1 11 12 1 11 12 1 1 11 12 1 10 12 1 1 9 12 1 1 10 12 1 1 10 7 12 1 1 10 12 1 1 11 12 1 1 9 12 1 1 10 7 1 10 12 1 9 12 1 1 10 12 1 1 100 1 8 1 10 12 1 10 100 12 1 1 1 7 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 7 1 1 7 1 1 1 1 1 1
ciInstanceKlass java/lang/LiveStackFrameInfo 0 0 97 10 7 12 1 1 1 9 7 12 1 1 1 9 12 1 9 12 1 9 12 1 9 12 1 1 7 1 10 12 1 1 10 12 1 8 1 10 12 1 1 8 1 8 1 8 1 10 100 1 10 12 1 100 1 10 12 1 7 1 7 1 1 1 3 1 3 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 1
ciInstanceKlass java/lang/LiveStackFrame 0 0 135 100 1 10 100 12 1 1 1 11 7 12 1 1 1 11 12 1 10 7 12 1 1 1 100 1 8 1 10 12 1 1 10 7 12 1 1 1 9 100 12 1 1 1 10 7 12 1 1 1 10 7 12 1 1 1 11 12 1 10 12 1 7 1 7 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 1 1 1 1 1 1 100 1 8 1 1 12 10 1 1 1 8 1 1 1 8 1 1 1 8 1 1 1 8 1 1 8 1 1 1 8 1 1 8 1
ciInstanceKlass java/lang/StackStreamFactory$AbstractStackWalker 1 0 375 100 1 7 1 3 10 7 12 1 1 1 10 7 12 1 1 10 7 12 1 1 1 9 12 1 1 10 12 1 1 9 12 1 1 9 12 1 1 9 12 1 1 9 12 1 10 7 12 1 1 1 10 12 1 1 10 7 12 1 1 9 12 1 1 9 12 1 1 10 12 1 1 9 100 12 1 1 1 10 12 1 1 10 12 1 9 12 1 1 10 100 12 1 1 1 10 12 1 1 9 12 1 1 9 7 12 1 1 1 7 1 10 8 1 10 12 1 1 10 12 1 8 1 10 12 1 1 10 100 12 1 1 1 100 1 8 1 10 12 1 8 1 10 12 10 100 12 1 1 9 12 1 8 1 5 0 8 1 8 1 9 12 1 1 10 12 1 1 18 12 1 1 10 7 12 1 1 1 10 12 1 1 10 12 1 10 12 1 9 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 1 8 1 10 12 1 10 12 1 10 12 1 10 12 1 1 8 1 10 12 1 1 10 12 1 1 10 12 1 1 10 12 1 10 12 1 10 100 12 1 1 1 10 12 1 10 12 1 9 12 1 8 1 10 12 1 10 12 1 10 12 1 1 10 12 1 10 12 1 10 12 1 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 1 10 12 1 1 10 12 1 8 1 1 1 1 1 1 1 1 1 1 1 1 1 7 1 1 1 1 1 1 1 7 1 1 1 1 7 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 16 15 15 10 100 12 1 1 1 1 1 1 1 1 100 1 100 1 1
ciInstanceKlass jdk/internal/module/Modules 1 1 504 10 7 12 1 1 1 9 7 12 1 1 1 11 7 12 1 1 1 11 12 1 1 11 12 1 1 11 12 1 1 11 12 1 11 12 1 11 12 1 11 12 1 11 12 1 1 10 7 12 1 1 1 10 100 12 1 1 1 18 12 1 1 10 7 12 1 1 1 7 1 10 7 12 1 1 1 10 100 12 1 1 1 10 100 12 1 1 10 12 1 1 11 12 1 9 12 1 1 11 100 12 1 1 1 10 12 1 1 10 10 12 1 10 9 12 1 1 10 100 12 1 1 10 12 1 1 10 100 12 1 1 100 1 11 100 12 1 1 1 10 100 12 1 1 1 11 100 12 1 1 10 12 1 1 10 100 12 1 1 10 100 12 1 1 1 10 12 1 1 10 12 1 1 11 12 1 1 18 12 1 1 11 100 12 1 1 10 100 12 1 1 1 11 100 12 1 1 1 7 1 11 12 1 1 11 7 12 1 1 1 11 12 1 1 10 12 1 1 10 100 12 1 1 18 12 1 1 11 12 1 1 18 12 1 1 11 12 1 1 10 12 1 18 18 10 12 1 1 9 12 1 1 11 7 12 1 1 1 100 1 10 11 12 1 11 12 1 1 11 12 1 1 10 100 1 10 12 1 1 10 100 12 1 1 10 12 1 1 11 12 10 12 1 1 7 1 10 18 12 1 10 12 1 1 7 1 8 1 10 12 1 10 100 12 1 1 18 12 1 11 11 12 10 12 1 10 10 100 1 18 12 1 10 10 10 7 12 1 1 10 7 12 1 1 1 10 12 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 7 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 16 15 10 12 1 16 16 15 10 12 1 16 1 16 1 15 10 12 1 16 1 16 1 15 10 12 16 1 15 10 16 1 15 10 12 16 1 15 10 12 16 15 10 12 16 15 10 12 15 10 100 12 1 1 1 1 1 1 100 1 100 1 1
staticfield jdk/internal/module/Modules JLA Ljdk/internal/access/JavaLangAccess; java/lang/System$2
staticfield jdk/internal/module/Modules JLMA Ljdk/internal/access/JavaLangModuleAccess; java/lang/module/ModuleDescriptor$1
staticfield jdk/internal/module/Modules $assertionsDisabled Z 1
instanceKlass org/apache/maven/artifact/versioning/ComparableVersion$ListItem
instanceKlass org/eclipse/sisu/bean/BeanScheduler$Pending
ciInstanceKlass java/util/ArrayList 1 1 509 10 7 12 1 1 1 7 1 9 7 12 1 1 1 9 12 1 100 1 7 1 10 8 1 10 12 1 1 10 12 1 10 12 1 1 10 12 1 9 12 1 11 7 12 1 1 1 9 12 1 1 11 12 1 1 7 10 7 12 1 1 1 9 12 1 10 12 1 10 12 1 1 10 7 12 1 1 1 10 7 12 1 1 1 10 12 1 1 10 12 1 1 10 12 1 1 10 12 1 10 12 1 1 100 1 7 1 10 12 1 10 10 7 12 1 1 1 10 7 12 1 1 10 12 1 100 1 10 10 12 10 12 1 1 10 12 1 1 10 12 1 10 12 10 12 1 1 7 1 10 12 1 1 10 12 1 1 10 12 1 100 1 10 11 12 1 1 11 7 12 1 1 1 11 12 1 10 12 1 10 12 1 10 12 1 1 100 1 10 12 1 1 10 10 12 1 1 10 12 1 8 1 8 1 8 1 8 1 10 12 1 1 10 12 1 1 11 12 1 7 1 10 100 12 1 1 10 12 1 10 12 1 1 10 100 12 1 1 10 12 1 10 100 12 1 1 1 11 100 12 1 1 1 10 12 1 100 1 8 1 10 7 1 10 12 1 7 1 10 12 1 10 12 1 1 7 1 10 12 1 10 12 1 1 11 7 12 1 1 7 1 10 12 1 10 12 1 1 11 7 12 1 1 10 12 1 1 10 12 1 1 10 12 1 1 10 12 1 1 11 100 12 1 1 10 12 1 1 7 1 7 1 7 1 1 1 1 5 0 1 3 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 1 1 1 7 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 100 1 1 1 1 1 1
staticfield java/util/ArrayList EMPTY_ELEMENTDATA [Ljava/lang/Object; 0 [Ljava/lang/Object;
staticfield java/util/ArrayList DEFAULTCAPACITY_EMPTY_ELEMENTDATA [Ljava/lang/Object; 0 [Ljava/lang/Object;
ciInstanceKlass java/util/RandomAccess 1 0 7 100 1 100 1 1 1
ciMethod java/util/ArrayList size ()I 256 0 128 0 0
ciMethod java/util/ArrayList isEmpty ()Z 1024 0 6090 0 96
ciMethod java/util/ArrayList add (Ljava/lang/Object;)Z 448 0 63912 0 2064
ciMethod java/util/ArrayList toArray ()[Ljava/lang/Object; 514 0 5386 0 712
ciMethod java/util/ArrayList iterator ()Ljava/util/Iterator; 770 0 6613 0 200
ciMethod java/util/ArrayList addAll (Ljava/util/Collection;)Z 4 0 7567 0 2104
ciMethod java/util/ArrayList <init> ()V 512 0 123701 0 304
ciMethod java/util/ArrayList <init> (I)V 46 0 5239 0 856
ciMethod java/util/ArrayList add (Ljava/lang/Object;[Ljava/lang/Object;I)V 448 0 63912 0 0
ciMethod java/util/ArrayList grow ()[Ljava/lang/Object; 82 0 7183 0 0
ciMethod java/util/ArrayList grow (I)[Ljava/lang/Object; 82 0 9741 0 1592
instanceKlass org/eclipse/aether/graph/Dependency$Exclusions
instanceKlass java/util/Collections$SingletonSet
instanceKlass java/lang/ProcessEnvironment$CheckedKeySet
instanceKlass org/eclipse/sisu/wire/EntrySetAdapter
instanceKlass java/util/TreeMap$EntrySet
instanceKlass org/eclipse/sisu/wire/EntryMapAdapter$EntrySet
instanceKlass com/google/common/collect/Sets$ImprovedAbstractSet
instanceKlass java/util/LinkedHashMap$LinkedEntrySet
instanceKlass com/google/common/collect/Sets$SetView
instanceKlass java/util/EnumSet
instanceKlass java/util/LinkedHashMap$LinkedKeySet
instanceKlass java/lang/ProcessEnvironment$CheckedEntrySet
instanceKlass java/util/TreeMap$KeySet
instanceKlass java/util/TreeSet
instanceKlass java/util/IdentityHashMap$KeySet
instanceKlass java/util/HashMap$KeySet
instanceKlass java/util/AbstractMap$1
instanceKlass java/util/WeakHashMap$KeySet
instanceKlass java/util/Collections$SetFromMap
instanceKlass java/util/HashSet
instanceKlass java/util/ImmutableCollections$MapN$1
instanceKlass java/util/Collections$EmptySet
instanceKlass java/util/HashMap$EntrySet
ciInstanceKlass java/util/AbstractSet 1 1 96 10 7 12 1 1 1 7 1 7 1 11 12 1 1 10 7 1 10 12 1 1 100 1 100 1 10 12 1 1 11 7 12 1 1 1 11 12 1 1 10 7 12 1 1 10 7 12 1 1 1 11 10 12 1 1 11 12 1 11 12 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1
ciInstanceKlass java/util/SequencedSet 1 1 78 11 7 12 1 1 1 7 1 7 1 7 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 8 1 1 12 10 1 1 1 8 1 1 1 8 1 1 1 8 1 1 1 8 1 1 1 8 1 1 8 1 1 1 8 1 1 8 1 1 8 1 1 1 8 1 1 8 1 1 8 1
ciInstanceKlass java/lang/annotation/Annotation 1 0 17 100 1 100 1 1 1 1 1 1 1 1 1 1 1 1 1
ciInstanceKlass java/util/LinkedHashMap 1 1 386 9 7 12 1 1 1 9 12 1 1 9 12 1 9 7 12 1 1 9 12 1 10 7 12 1 1 1 10 12 1 1 10 12 1 1 9 12 1 9 12 1 1 9 12 1 10 12 1 1 100 1 10 10 12 1 1 10 12 1 10 12 1 1 9 12 1 1 9 12 1 10 12 1 1 10 100 12 1 1 1 10 12 1 10 12 1 10 12 10 12 1 1 10 7 12 1 1 1 10 12 1 1 10 12 1 1 9 7 1 10 12 1 10 12 1 1 9 12 1 1 7 1 10 12 1 7 1 100 1 10 10 12 1 1 10 12 1 1 9 12 1 1 7 1 10 7 1 10 12 1 9 12 1 7 1 10 100 1 10 11 100 12 1 1 1 100 1 10 11 100 12 1 1 100 1 7 1 10 8 1 10 12 1 1 10 12 1 10 12 1 1 10 12 1 10 12 1 1 10 100 1 10 12 1 7 1 1 1 1 5 0 1 1 1 3 1 3 1 3 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 7 1 1 1 1 1 1 1 1 100 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 1 1 1 1 1 1 1 1 7 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 100 1 100 1 100 1 1 1 1 1 100 1 7 1 1 1 1 1 1 1 1 1
ciInstanceKlass java/util/SequencedMap 1 1 157 11 7 12 1 1 1 11 100 12 1 1 1 11 7 12 1 1 1 100 1 11 12 1 1 100 1 10 12 1 1 11 12 1 1 11 12 1 1 100 1 10 12 100 1 10 12 1 100 1 10 100 1 10 7 1 7 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 8 1 1 12 10 1 1 1 8 1 1 1 8 1 1 8 1 1 1 8 1 1 1 8 1 1 1 8 1 1 1 8 1 1 1 8 1 1 1 8 1 1 1 8 1 1 8 1
ciMethod java/util/LinkedHashMap get (Ljava/lang/Object;)Ljava/lang/Object; 768 0 18657 0 728
ciMethod java/util/LinkedHashMap entrySet ()Ljava/util/Set; 526 0 7035 0 0
ciMethod java/util/LinkedHashMap newNode (ILjava/lang/Object;Ljava/lang/Object;Ljava/util/HashMap$Node;)Ljava/util/HashMap$Node; 974 0 7907 0 1160
ciMethod java/util/LinkedHashMap afterNodeAccess (Ljava/util/HashMap$Node;)V 8 0 1249 0 0
ciMethod java/util/LinkedHashMap afterNodeInsertion (Z)V 1024 0 7907 0 112
ciMethod java/util/LinkedHashMap removeEldestEntry (Ljava/util/Map$Entry;)Z 372 0 5358 0 0
ciMethod java/util/LinkedHashMap sequencedEntrySet ()Ljava/util/SequencedSet; 526 0 7035 0 0
ciMethod java/util/LinkedHashMap <init> (I)V 514 0 6612 0 0
ciMethod java/util/LinkedHashMap <init> ()V 512 0 7292 0 0
ciMethod java/util/LinkedHashMap linkNodeAtEnd (Ljava/util/LinkedHashMap$Entry;)V 974 0 7907 0 1048
ciInstanceKlass java/util/ImmutableCollections$List12 1 1 125 10 7 12 1 1 1 10 7 12 1 1 1 9 7 12 1 1 1 9 7 12 1 1 9 12 1 10 12 1 1 10 7 12 1 1 1 100 1 8 1 10 12 1 100 1 10 12 1 10 12 1 1 10 12 1 1 10 7 12 1 1 10 7 12 1 1 1 7 1 7 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 7 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1
instanceKlass java/util/ArrayList$ListItr
ciInstanceKlass java/util/ArrayList$Itr 1 1 104 9 7 12 1 1 1 10 7 12 1 1 1 9 12 1 1 9 7 12 1 1 9 12 1 9 12 1 9 12 1 10 12 1 100 1 10 9 12 1 1 100 1 10 100 1 10 10 12 1 1 100 1 10 100 12 1 1 1 10 12 1 1 11 100 12 1 1 1 100 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1
ciMethod java/util/ArrayList$Itr hasNext ()Z 782 0 5522 0 112
ciMethod java/util/ArrayList$Itr next ()Ljava/lang/Object; 548 0 6964 0 272
ciMethod java/util/ArrayList$Itr <init> (Ljava/util/ArrayList;)V 512 0 13437 0 0
ciMethod java/util/ArrayList$Itr checkForComodification ()V 550 0 13506 0 128
instanceKlass java/util/LinkedHashMap$Entry
ciInstanceKlass java/util/HashMap$Node 1 1 95 10 7 12 1 1 1 9 7 12 1 1 1 9 12 1 1 9 12 1 9 12 1 1 7 1 10 10 12 1 1 8 1 10 12 1 10 12 1 1 10 7 12 1 1 1 7 1 11 12 1 1 10 12 1 1 11 12 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 1 1 7 1 1
ciMethod java/util/HashMap$Node <init> (ILjava/lang/Object;Ljava/lang/Object;Ljava/util/HashMap$Node;)V 1024 0 13333 0 0
ciMethod java/util/HashMap$Node getValue ()Ljava/lang/Object; 322 0 161 0 0
ciMethod java/util/HashMap$Node getKey ()Ljava/lang/Object; 324 0 162 0 0
ciInstanceKlass java/util/HashMap$TreeNode 0 0 250 100 1 10 100 12 1 1 1 9 100 12 1 1 1 9 12 1 1 9 12 1 9 12 1 1 9 12 1 1 10 12 1 1 100 1 10 12 1 9 12 1 9 12 1 9 12 1 1 10 100 12 1 1 1 10 12 1 1 10 12 1 1 10 12 1 1 10 12 1 1 10 12 1 1 10 100 12 1 1 1 10 100 12 1 1 1 10 100 12 1 1 1 9 12 1 10 12 1 1 10 12 1 1 10 12 1 1 10 12 1 1 9 100 1 10 12 1 1 10 12 1 1 10 12 1 10 12 1 1 10 12 1 10 12 1 10 12 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 1 1 1
instanceKlass java/util/HashMap$TreeNode
ciInstanceKlass java/util/LinkedHashMap$Entry 1 1 41 10 7 12 1 1 1 100 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 1 100 1 1 1
ciMethod java/util/LinkedHashMap$Entry <init> (ILjava/lang/Object;Ljava/lang/Object;Ljava/util/HashMap$Node;)V 974 0 7907 0 760
ciMethod java/util/HashMap$TreeNode split (Ljava/util/HashMap;[Ljava/util/HashMap$Node;II)V 0 0 1 0 -1
ciMethod java/util/HashMap$TreeNode putTreeVal (Ljava/util/HashMap;[Ljava/util/HashMap$Node;ILjava/lang/Object;Ljava/lang/Object;)Ljava/util/HashMap$TreeNode; 0 0 1 0 -1
ciMethod java/util/HashMap$TreeNode getTreeNode (ILjava/lang/Object;)Ljava/util/HashMap$TreeNode; 0 0 1 0 -1
ciInstanceKlass jdk/internal/loader/URLClassPath$JarLoader 1 1 485 7 1 8 1 8 1 7 1 10 12 1 1 10 12 1 1 8 1 10 12 1 10 12 1 1 10 7 12 1 1 1 10 7 12 1 1 9 12 1 1 9 12 1 1 9 12 1 1 9 12 1 1 9 12 1 1 10 12 1 7 1 10 12 1 9 12 1 10 7 12 1 1 8 1 10 12 1 10 7 12 1 1 1 7 1 10 12 1 10 7 12 1 1 1 100 1 10 12 1 1 100 1 10 7 12 1 1 1 9 12 1 9 12 1 1 11 100 12 1 1 1 8 1 10 12 1 10 12 1 1 10 12 1 1 7 1 10 10 12 1 1 100 1 10 12 1 10 7 1 10 100 1 10 12 1 1 10 12 1 10 12 1 1 10 12 1 1 8 1 10 12 1 10 12 1 1 8 1 9 12 1 1 10 100 12 1 1 1 100 1 10 12 1 1 10 12 1 10 7 12 1 1 10 7 12 1 1 1 10 12 1 100 1 7 1 10 12 1 10 12 1 1 10 12 1 1 10 12 1 1 11 100 12 1 1 11 12 1 1 100 1 10 12 1 10 12 1 1 10 7 12 1 1 7 1 10 12 10 12 1 1 10 12 1 1 10 7 12 1 1 1 11 7 12 1 1 1 10 12 1 1 10 7 12 1 1 1 9 100 12 1 1 1 10 100 12 1 1 1 10 12 1 1 100 1 10 10 12 1 1 10 12 1 10 12 1 9 12 1 10 12 1 1 9 12 1 9 12 1 1 8 1 8 1 10 100 12 1 1 10 100 12 1 1 1 100 1 10 12 1 1 10 12 1 10 12 1 10 12 1 9 12 1 1 10 12 1 1 10 12 1 10 10 12 1 10 12 1 1 8 1 10 12 1 10 100 12 1 1 1 10 12 1 100 1 10 12 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 7 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 100 1 1 1
staticfield jdk/internal/loader/URLClassPath$JarLoader zipAccess Ljdk/internal/access/JavaUtilZipFileAccess; java/util/zip/ZipFile$1
ciInstanceKlass java/util/LinkedHashMap$LinkedEntrySet 1 1 197 9 7 12 1 1 1 10 7 12 1 1 1 9 12 1 1 9 7 12 1 1 1 10 12 1 7 1 10 12 1 100 1 11 12 1 1 10 12 1 1 10 100 12 1 1 1 11 12 1 10 100 12 1 1 1 10 12 1 1 100 1 10 100 12 1 1 1 100 1 10 9 12 1 9 12 1 1 11 100 12 1 1 1 9 100 12 1 1 9 12 1 9 12 1 100 1 10 100 1 10 100 1 10 10 12 1 1 9 12 9 12 1 1 10 12 1 1 10 10 12 1 1 10 12 1 10 12 1 10 12 1 10 12 1 1 10 12 1 10 12 100 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 1 1
ciMethod java/util/LinkedHashMap$LinkedEntrySet iterator ()Ljava/util/Iterator; 862 0 6585 0 472
ciMethod java/util/LinkedHashMap$LinkedEntrySet <init> (Ljava/util/LinkedHashMap;Z)V 28 0 4031 0 0
ciInstanceKlass java/util/LinkedHashMap$LinkedEntryIterator 1 1 50 10 7 12 1 1 1 10 7 12 1 1 1 10 12 1 1 100 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 1 1 1 100 1 1 100 1 100 1
instanceKlass java/util/LinkedHashMap$LinkedEntryIterator
instanceKlass java/util/LinkedHashMap$LinkedKeyIterator
instanceKlass java/util/LinkedHashMap$LinkedValueIterator
ciInstanceKlass java/util/LinkedHashMap$LinkedHashIterator 1 1 100 9 7 12 1 1 1 10 7 12 1 1 1 9 12 1 1 9 7 12 1 1 1 9 12 1 9 12 1 9 12 1 1 9 12 1 9 12 1 100 1 10 100 1 10 9 7 12 1 1 9 12 1 100 1 10 9 7 12 1 1 9 12 1 1 10 12 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 1
ciMethod java/util/LinkedHashMap$LinkedHashIterator <init> (Ljava/util/LinkedHashMap;Z)V 516 0 6546 0 712
ciMethod java/util/LinkedHashMap$LinkedHashIterator hasNext ()Z 528 0 5388 0 88
ciMethod java/util/LinkedHashMap$LinkedHashIterator nextNode ()Ljava/util/LinkedHashMap$Entry; 532 0 6407 0 624
ciMethod java/util/LinkedHashMap$LinkedEntryIterator <init> (Ljava/util/LinkedHashMap;Z)V 862 0 6585 0 0
ciMethod java/util/LinkedHashMap$LinkedEntryIterator next ()Ljava/util/Map$Entry; 836 0 5609 0 0
ciMethod java/util/LinkedHashMap$LinkedEntryIterator next ()Ljava/lang/Object; 836 0 5609 0 624
ciInstanceKlass java/util/zip/ZipFile$Source$Key 1 1 84 10 7 12 1 1 1 9 7 12 1 1 1 9 12 1 1 10 7 12 1 1 1 9 12 1 1 100 1 5 0 11 7 12 1 1 1 10 7 12 1 1 1 10 7 12 1 1 1 10 12 1 1 11 12 1 1 10 10 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 1 100 1 1 1
ciMethod java/util/AbstractSet <init> ()V 514 0 34719 0 80
ciMethod java/util/HashMap removeNode (ILjava/lang/Object;Ljava/lang/Object;ZZ)Ljava/util/HashMap$Node; 768 18 2322 0 -1
ciMethod java/util/HashMap getNode (Ljava/lang/Object;)Ljava/util/HashMap$Node; 1024 28 13056 0 632
ciMethod java/util/HashMap resize ()[Ljava/util/HashMap$Node; 26 274 5930 0 3144
ciMethod java/util/HashMap treeifyBin ([Ljava/util/HashMap$Node;I)V 0 0 1 0 -1
ciMethod java/util/HashMap putVal (ILjava/lang/Object;Ljava/lang/Object;ZZ)Ljava/lang/Object; 518 22 17682 0 1904
ciMethod java/util/HashMap tableSizeFor (I)I 512 0 14663 0 0
ciMethod java/util/HashMap hash (Ljava/lang/Object;)I 1024 0 47744 0 224
ciMethod java/util/HashMap <init> ()V 770 0 50545 0 88
ciMethod java/util/HashMap <init> (IF)V 106 0 8266 0 328
ciMethod java/util/HashMap <init> (I)V 82 0 9823 0 0
ciMethod java/util/AbstractMap <init> ()V 772 0 72479 0 80
ciMethod java/lang/Integer numberOfLeadingZeros (I)I 36 0 5190 0 -1
ciMethod java/util/Arrays copyOf ([Ljava/lang/Object;I)[Ljava/lang/Object; 188 0 6476 0 0
ciMethod java/util/Arrays copyOf ([Ljava/lang/Object;ILjava/lang/Class;)[Ljava/lang/Object; 434 0 5764 0 -1
ciMethod jdk/internal/util/ArraysSupport hugeLength (II)I 0 0 1 0 -1
ciMethod jdk/internal/util/ArraysSupport newLength (III)I 516 0 5841 0 0
ciMethod jdk/internal/util/ArraysSupport vectorizedHashCode (Ljava/lang/Object;IIII)I 800 0 6446 0 -1
ciMethod java/lang/Math max (II)I 584 0 119514 0 -1
ciMethod java/util/Map$Entry getKey ()Ljava/lang/Object; 0 0 1 0 -1
ciMethod java/util/Map$Entry getValue ()Ljava/lang/Object; 0 0 1 0 -1
ciMethod java/util/Iterator next ()Ljava/lang/Object; 0 0 1 0 -1
ciMethod java/util/Iterator hasNext ()Z 0 0 1 0 -1
ciMethod java/util/Set iterator ()Ljava/util/Iterator; 0 0 1 0 -1
ciMethod java/lang/String equalsIgnoreCase (Ljava/lang/String;)Z 136 0 7236 0 -1
ciMethod java/lang/String isLatin1 ()Z 832 0 838356 0 88
ciMethod java/lang/String <init> (Ljava/lang/StringBuilder;)V 12 0 78592 0 -1
ciMethod java/lang/StringBuilder append (Ljava/lang/String;)Ljava/lang/StringBuilder; 14 0 233604 0 1656
ciMethod java/lang/StringBuilder <init> ()V 10 0 82335 0 448
ciMethod java/lang/AbstractStringBuilder <init> (I)V 12 0 5145 0 -1
ciMethod java/lang/AbstractStringBuilder append (Ljava/lang/String;)Ljava/lang/AbstractStringBuilder; 14 0 11504 0 -1
ciMethod java/util/Map entrySet ()Ljava/util/Set; 0 0 1 0 -1
ciMethod java/util/Map put (Ljava/lang/Object;Ljava/lang/Object;)Ljava/lang/Object; 0 0 1 0 -1
ciMethod java/util/Map get (Ljava/lang/Object;)Ljava/lang/Object; 0 0 1 0 -1
ciMethod java/lang/Boolean parseBoolean (Ljava/lang/String;)Z 44 0 6387 0 0
ciMethod java/lang/Object getClass ()Ljava/lang/Class; 256 0 128 0 -1
ciMethod java/lang/Object <init> ()V 1024 0 847625 0 136
ciInstanceKlass java/util/NoSuchElementException 0 0 34 10 100 12 1 1 1 10 12 1 10 12 1 10 12 1 100 1 1 1 1 5 0 1 1 1 1 1 1 1 1 1 1 1
ciInstanceKlass java/util/ConcurrentModificationException 0 0 34 10 100 12 1 1 1 10 12 1 10 12 1 10 12 1 100 1 1 1 1 5 0 1 1 1 1 1 1 1 1 1 1 1
ciInstanceKlass com/google/common/collect/SingletonImmutableList 1 1 119 10 10 9 10 10 10 11 10 10 100 10 10 10 10 10 10 10 10 7 7 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 3 1 12 7 12 12 12 7 12 100 12 100 12 12 12 1 12 100 12 12 12 12 12 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1
ciInstanceKlass org/apache/maven/model/InputLocationTracker 1 0 11 100 1 100 1 1 1 1 1 1 1
instanceKlass org/apache/maven/model/PluginExecution
instanceKlass org/apache/maven/model/ReportSet
instanceKlass org/apache/maven/model/ReportPlugin
instanceKlass org/apache/maven/model/Plugin
ciInstanceKlass org/apache/maven/model/ConfigurationContainer 1 1 167 10 7 12 1 1 1 9 7 12 1 1 1 10 12 1 1 9 12 1 1 7 1 10 12 1 9 12 1 1 7 1 10 12 1 100 1 100 1 100 1 10 10 12 1 1 10 100 12 1 1 1 10 12 1 1 8 1 10 12 1 10 12 1 10 12 1 1 100 1 9 12 1 1 7 1 10 12 1 1 8 1 10 12 1 1 8 8 9 12 1 1 9 12 1 9 12 1 10 12 1 1 10 12 1 1 10 11 7 12 1 1 1 11 12 1 1 7 1 10 7 12 1 1 1 10 12 1 1 10 12 1 100 1 100 1 100 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 1 1
ciInstanceKlass org/apache/maven/model/Plugin 1 1 269 10 7 12 1 1 1 8 1 9 7 12 1 1 1 9 12 1 1 10 12 1 1 11 7 12 1 1 1 10 12 1 10 12 1 1 9 12 1 1 7 1 10 11 12 1 1 11 7 12 1 1 1 11 12 1 1 7 1 10 12 1 9 12 1 7 1 10 12 1 9 12 1 1 100 1 10 12 1 100 1 100 1 7 1 10 10 100 12 1 1 1 10 100 12 1 1 1 10 12 1 1 8 1 10 12 1 10 12 1 10 12 1 1 100 1 9 12 1 9 12 1 9 12 1 11 12 1 10 100 12 1 1 1 10 7 12 1 1 1 100 1 10 10 12 1 11 100 12 1 1 100 1 8 1 8 1 10 12 1 8 1 10 11 12 1 1 10 12 1 10 12 1 8 1 8 1 10 12 1 8 1 10 12 1 8 1 10 12 1 1 10 12 1 10 12 1 1 8 1 8 1 10 12 1 100 1 100 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 1 1
instanceKlass org/apache/maven/model/merge/MavenModelMerger
ciInstanceKlass org/apache/maven/model/merge/ModelMerger 1 1 1971 10 7 12 1 1 1 8 1 10 7 12 1 1 1 7 1 10 11 7 12 1 1 1 10 7 12 1 1 1 10 12 1 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 7 12 1 1 1 10 12 1 1 8 1 10 12 1 1 10 12 1 1 10 12 1 1 7 1 10 10 12 1 10 12 1 1 10 12 1 1 10 12 1 10 12 1 8 1 10 12 1 10 12 1 8 1 10 12 1 10 12 1 8 1 10 12 1 10 12 1 8 1 10 12 1 10 12 1 8 1 10 12 1 10 12 1 8 1 10 12 1 10 12 1 8 1 10 12 1 10 12 1 8 1 10 12 1 10 12 1 8 1 10 12 1 1 7 1 10 10 12 1 1 10 12 1 1 10 12 1 1 100 1 10 12 1 10 12 1 1 10 12 1 1 10 12 1 100 1 10 10 12 1 10 12 1 100 1 10 10 12 1 10 12 1 100 1 10 10 12 1 10 12 1 1 7 1 10 10 12 1 1 10 12 1 1 10 12 1 1 7 1 10 10 12 1 10 12 1 1 10 12 1 1 10 12 1 1 7 1 10 10 12 1 1 10 12 1 1 10 12 1 1 100 1 10 10 12 1 10 12 1 1 10 12 1 1 10 12 1 1 7 1 10 10 12 1 1 10 12 1 1 10 12 1 100 1 10 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 7 12 1 1 11 7 12 1 1 1 7 1 11 12 1 1 10 12 1 11 12 1 1 10 12 1 10 12 1 7 1 10 10 12 1 10 12 1 100 1 10 10 12 1 10 12 1 10 12 1 10 12 1 1 7 1 10 10 12 1 1 10 12 1 1 10 12 1 1 7 1 10 10 12 1 1 10 12 1 1 10 12 1 1 7 1 10 10 12 1 1 10 12 1 1 7 1 10 10 12 1 1 10 10 12 1 1 8 1 10 10 7 12 1 1 10 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 1 100 1 10 10 12 1 1 10 12 1 1 10 12 1 10 12 1 10 12 1 1 7 1 10 10 12 1 1 10 12 1 1 10 12 1 10 12 1 8 1 10 10 10 12 1 10 12 1 8 1 10 12 1 1 10 12 1 10 12 1 10 12 1 10 100 1 10 10 10 10 10 10 10 10 12 1 10 12 1 8 1 10 12 1 1 10 12 1 10 12 1 10 12 1 1 8 1 10 10 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 8 1 10 10 10 12 1 10 12 1 8 1 10 10 10 10 10 12 1 1 10 12 1 10 12 1 10 7 12 1 1 1 100 1 10 10 12 1 1 10 12 1 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 7 1 10 10 10 10 10 10 10 10 12 1 10 12 1 8 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 8 1 10 10 10 12 1 10 12 1 8 1 10 12 1 10 12 1 8 1 10 12 1 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 7 1 10 10 10 10 10 10 10 10 12 1 10 12 1 8 1 10 12 1 10 12 1 8 1 10 12 1 10 12 1 8 1 10 12 1 10 12 1 8 1 10 12 1 10 12 1 8 1 10 12 1 100 1 10 10 12 1 10 12 1 1 10 12 1 10 100 1 10 10 10 10 10 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 8 1 10 10 10 12 1 10 12 1 8 1 10 12 1 100 1 10 10 12 1 10 12 1 1 10 12 1 1 10 12 1 10 12 1 10 12 1 10 7 1 10 10 10 10 10 10 10 10 12 1 100 1 10 10 12 1 10 12 1 1 10 12 1 10 7 1 10 10 10 10 12 1 10 12 1 8 1 10 100 12 1 1 1 10 10 10 12 1 10 10 10 12 1 10 12 1 10 12 1 10 12 1 10 10 10 10 10 10 10 10 10 12 1 8 1 10 12 1 10 12 1 10 10 10 10 10 10 10 12 1 1 10 12 1 10 12 1 10 12 1 10 100 1 10 10 10 10 10 10 12 1 10 12 1 8 1 10 12 1 10 12 1 8 1 10 12 1 1 10 12 1 10 12 1 10 12 1 10 12 1 10 100 1 10 10 10 10 12 1 10 12 1 8 1 10 12 1 10 12 1 8 1 10 12 1 10 12 1 8 1 10 12 1 10 12 1 8 1 10 12 1 10 12 1 10 12 1 1 10 12 1 1 10 100 1 10 10 10 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 100 1 10 10 10 10 12 1 10 12 1 8 1 10 10 10 12 10 12 8 1 10 12 1 10 12 1 8 1 10 12 1 10 12 1 8 1 10 12 1 10 12 1 10 10 10 12 1 10 12 1 10 12 1 10 12 1 8 1 10 10 10 10 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 8 1 10 10 10 12 1 10 12 1 8 1 10 12 1 10 12 1 8 1 10 10 10 12 1 10 12 1 8 1 10 12 1 10 12 1 8 1 10 12 1 8 1 10 12 1 10 12 1 10 12 1 10 10 10 10 10 10 10 12 1 7 1 10 10 12 1 10 12 1 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 100 1 10 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 8 1 10 10 10 12 1 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 8 1 10 10 10 12 1 10 12 1 8 1 10 12 1 10 12 1 8 1 10 10 10 12 1 10 12 1 8 1 10 12 1 7 1 10 10 12 1 10 12 1 1 10 12 1 10 12 1 10 100 1 10 10 10 10 10 10 10 10 12 1 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 7 12 1 1 10 12 1 8 1 10 10 10 12 1 10 12 1 8 1 10 12 1 10 12 1 8 1 10 12 1 10 12 1 10 12 1 7 1 10 10 12 1 10 12 1 10 12 1 10 12 1 1 10 12 1 10 7 12 1 1 1 7 1 10 10 12 1 1 10 12 1 1 10 12 1 10 100 1 100 1 10 10 10 12 1 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 7 1 10 10 10 10 10 10 10 10 12 10 12 8 1 10 10 10 12 1 100 1 10 10 12 1 10 12 1 10 12 1 10 7 12 1 1 10 12 1 8 1 10 10 10 12 1 7 1 10 12 1 10 12 1 1 10 12 1 10 12 1 1 10 12 1 10 12 1 10 7 1 10 10 10 10 12 1 10 12 1 8 1 10 12 1 10 12 1 10 12 1 1 10 12 1 1 10 12 1 10 12 1 10 100 12 1 1 10 12 1 8 1 10 10 10 12 1 10 12 1 8 1 10 12 1 10 12 1 10 12 1 1 10 12 1 10 100 1 10 10 10 10 12 1 10 12 1 10 100 12 1 1 10 12 1 10 12 1 10 12 1 10 12 1 1 7 1 10 12 10 12 1 7 1 10 12 1 10 12 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 100 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 1 1
instanceKlass org/apache/maven/model/profile/DefaultProfileInjector$ProfileModelMerger
instanceKlass org/apache/maven/model/normalization/DefaultModelNormalizer$DuplicateMerger
instanceKlass org/apache/maven/model/management/DefaultPluginManagementInjector$ManagementModelMerger
instanceKlass org/apache/maven/model/management/DefaultDependencyManagementInjector$ManagementModelMerger
instanceKlass org/apache/maven/model/inheritance/DefaultInheritanceAssembler$InheritanceModelMerger
instanceKlass org/apache/maven/model/plugin/DefaultLifecycleBindingsInjector$LifecycleBindingsMerger
ciInstanceKlass org/apache/maven/model/merge/MavenModelMerger 1 1 596 10 7 12 1 1 1 7 1 8 1 10 7 12 1 1 1 11 7 12 1 1 1 10 12 1 1 10 12 1 10 12 1 1 8 1 10 12 1 1 10 12 1 1 10 12 1 10 12 1 8 1 10 12 1 1 10 12 1 1 10 12 1 1 7 1 10 8 1 10 10 10 12 1 1 10 12 1 1 10 12 1 1 7 1 10 10 10 10 12 1 1 10 12 1 1 10 12 1 1 7 1 10 10 10 10 12 1 1 10 12 1 1 10 12 1 1 11 7 12 1 1 7 1 10 12 1 10 12 1 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 7 12 1 1 10 7 1 10 11 12 1 1 10 12 1 11 12 1 1 10 7 12 1 1 1 11 12 1 1 11 12 1 1 7 1 11 7 12 1 1 10 12 1 8 1 10 10 7 12 1 1 1 10 10 12 1 7 1 10 11 12 1 1 11 7 12 1 1 11 12 1 1 7 1 10 12 1 1 11 12 1 11 12 1 1 10 12 1 10 12 1 10 12 1 10 7 12 1 1 10 12 1 10 12 1 10 12 1 1 10 12 1 10 12 1 10 7 12 1 1 1 7 1 10 10 10 10 12 1 1 10 12 1 1 10 12 1 10 12 1 10 12 1 1 10 12 1 1 7 1 10 10 10 10 12 1 1 10 12 1 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 7 12 1 1 10 10 10 10 12 1 10 7 1 10 10 10 10 12 1 10 12 1 10 12 1 8 1 10 12 1 10 12 1 10 12 1 8 1 10 12 1 10 7 12 1 1 7 1 10 12 1 10 12 1 10 10 12 1 1 11 12 1 10 12 1 1 10 12 1 10 12 1 10 12 1 10 7 12 1 1 7 1 10 10 10 10 12 1 1 10 12 1 1 10 12 1 10 7 12 1 1 10 12 1 10 10 10 10 7 1 7 1 10 10 7 12 1 1 10 12 1 1 10 12 1 10 10 12 1 10 100 1 10 1 1 1 8 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1
ciInstanceKlass org/apache/maven/model/plugin/DefaultLifecycleBindingsInjector$LifecycleBindingsMerger 1 1 187 10 7 12 1 1 1 10 7 12 1 1 1 7 1 10 10 12 1 1 7 1 8 1 10 12 1 1 10 7 12 1 1 1 10 12 1 1 10 7 12 1 1 1 11 7 12 1 1 1 7 1 11 12 1 1 10 12 1 11 12 1 1 11 7 12 1 1 11 12 1 1 7 1 10 12 1 1 11 7 12 1 1 1 10 11 12 1 1 10 12 1 1 11 7 1 10 10 12 1 1 10 12 1 1 7 1 11 12 1 1 10 12 1 10 12 1 1 10 12 1 1 10 100 12 1 1 10 100 12 1 1 1 10 12 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 1 1 1 1 1 1 1 100 1 1
instanceKlass org/apache/maven/model/PluginManagement
instanceKlass org/apache/maven/model/PluginConfiguration
ciInstanceKlass org/apache/maven/model/PluginContainer 1 1 192 10 7 12 1 1 1 10 7 12 1 1 1 11 7 12 1 1 1 10 12 1 1 9 12 1 1 7 1 10 11 12 1 1 11 7 12 1 1 1 11 12 1 7 1 10 12 1 9 12 1 1 7 1 10 12 1 100 1 100 1 100 1 10 10 12 1 1 10 100 12 1 1 1 10 12 1 1 8 1 10 12 1 10 12 1 10 12 1 1 100 1 7 1 10 12 1 1 8 1 10 12 1 8 9 12 1 1 9 12 1 10 12 1 1 10 12 1 1 10 11 7 12 1 1 1 11 12 1 1 7 1 11 12 1 9 12 1 10 12 1 8 1 10 12 1 100 1 100 1 100 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 7 1 1 1
ciInstanceKlass org/apache/maven/model/Build 1 1 156 10 7 12 1 1 1 10 7 12 1 1 1 11 7 12 1 1 1 10 12 1 1 9 12 1 1 7 1 10 11 12 1 1 11 7 12 1 1 1 11 12 1 1 7 1 10 12 1 100 1 100 1 100 1 10 10 100 12 1 1 1 10 100 12 1 1 1 10 12 1 1 8 1 10 12 1 10 12 1 10 12 1 1 100 1 9 12 1 1 9 12 1 9 12 1 9 12 1 9 12 1 11 12 1 8 1 10 8 1 10 12 1 100 1 100 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 7 1 1 1
ciInstanceKlass org/apache/maven/model/PluginManagement 1 1 72 10 7 12 1 1 1 10 12 1 1 7 1 100 1 100 1 100 1 10 10 100 12 1 1 1 10 100 12 1 1 1 10 12 1 1 8 1 10 12 1 10 12 1 10 12 1 1 100 1 10 12 1 100 1 100 1 1 1 1 1 1 1 1 1 1 1 1 100 1 1 1
ciInstanceKlass org/apache/maven/model/PluginExecution 1 1 131 10 7 12 1 1 1 8 1 9 7 12 1 1 1 9 12 1 1 10 12 1 1 11 7 12 1 1 1 10 12 1 1 9 12 1 1 7 1 10 11 12 1 1 100 1 100 1 100 1 10 10 100 12 1 1 1 10 100 12 1 1 1 10 12 1 1 8 1 10 12 1 10 12 1 10 12 1 1 100 1 9 12 1 11 12 1 10 12 1 10 12 1 100 1 100 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 1 1
ciInstanceKlass org/apache/maven/model/inheritance/DefaultInheritanceAssembler$InheritanceModelMerger 1 1 333 100 1 10 7 12 1 1 1 8 1 11 7 12 1 1 1 8 1 10 7 12 1 1 1 10 7 12 1 1 1 10 7 12 1 1 1 7 1 10 7 12 1 1 1 10 12 1 10 12 1 1 10 12 1 1 10 10 10 12 1 1 10 10 12 1 10 12 1 8 1 10 12 1 7 1 10 10 7 12 1 1 1 10 12 1 1 8 1 10 12 1 10 12 1 1 8 1 10 12 1 1 10 7 12 1 1 1 10 12 1 1 11 12 1 1 11 7 12 1 1 1 11 7 12 1 1 1 11 12 1 1 7 1 11 12 1 10 12 1 1 11 12 1 11 12 1 1 10 7 12 1 1 1 11 7 12 1 1 7 1 11 12 1 10 11 7 1 10 12 1 10 12 1 10 8 1 10 10 10 12 1 1 10 12 1 1 10 12 1 1 10 7 1 10 11 12 1 10 11 12 1 1 10 12 1 1 10 12 1 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 7 1 7 1 10 12 1 1 10 10 10 10 10 10 12 1 1 11 12 1 1 10 12 1 10 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1
ciInstanceKlass org/apache/maven/model/InputLocation 1 1 217 10 7 12 1 1 1 9 7 12 1 1 1 9 12 1 9 12 1 1 10 12 1 1 9 12 1 1 7 1 10 12 1 100 1 100 1 100 1 10 10 12 1 1 10 100 12 1 1 1 10 12 1 1 8 1 10 12 1 10 12 1 10 12 1 1 100 1 7 1 10 12 1 1 8 1 10 12 1 1 9 12 1 1 10 12 1 1 10 12 1 1 10 11 7 12 1 1 1 11 12 1 1 10 12 1 10 12 1 10 12 1 1 10 12 1 10 12 1 1 11 12 1 10 12 1 11 7 12 1 1 1 11 7 12 1 1 1 11 12 1 7 1 10 12 1 10 12 1 1 11 12 1 10 12 1 8 1 8 1 10 12 1 10 12 1 100 1 100 1 100 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 1 1 1 100 1 1
ciInstanceKlass org/apache/maven/model/merge/ModelMerger$KeyComputer 1 0 16 100 1 100 1 1 1 1 1 1 1 1 1 100 1 1
ciInstanceKlass org/apache/maven/model/merge/ModelMerger$DependencyKeyComputer 1 1 51 10 7 12 1 1 1 9 12 1 1 10 7 12 1 1 10 7 12 1 1 1 7 1 10 12 1 100 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1
ciInstanceKlass org/apache/maven/model/merge/ModelMerger$1 0 0 11 100 1 100 1 1 1 1 100 1 1
ciMethodData java/lang/Object <init> ()V 2 847113 orig 80 1 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 data 4 0x0 0x9 0x1 0x0 oops 0 methods 0
ciMethodData java/lang/String isLatin1 ()Z 2 837943 orig 80 0 0 0 0 0 0 0 0 0 0 0 0 218 2 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 data 18 0x30007 0x0 0x58 0xcc937 0xa0007 0x6 0x38 0xcc931 0xe0003 0xcc931 0x18 0x0 0x0 0x0 0x0 0x9 0x1 0x0 oops 0 methods 0
ciMethodData java/lang/String hashCode ()I 2 10346 orig 80 1 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 data 42 0x60007 0x1b32 0x108 0xd39 0xd0007 0x30 0xe8 0xd09 0x110005 0xd09 0x0 0x0 0x0 0x0 0x0 0x140007 0x0 0x48 0xd09 0x1b0002 0xd09 0x1e0003 0xd0a 0x28 0x250002 0x0 0x2a0007 0xd09 0x38 0x1 0x320003 0x1 0x18 0x0 0x0 0x0 0x0 0x0 0x0 0x9 0x1 0xe oops 0 methods 0
ciMethodData java/lang/StringLatin1 hashCode ([B)I 2 3176 orig 80 0 0 0 0 0 0 0 0 0 0 0 0 218 2 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 1 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 data 25 0x8000000600020008 0x6 0xc51 0x70 0x1 0x40 0x17 0x58 0x1d0003 0x1 0x40 0x270003 0x17 0x28 0x300002 0xc51 0x0 0x0 0x0 0x0 0x0 0x0 0x9 0x1 0x0 oops 0 methods 0
ciMethodData java/lang/String equals (Ljava/lang/Object;)Z 2 8264 orig 80 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 data 49 0x20007 0x1b6e 0x20 0x4db 0x80104 0x0 0x0 0x2dae928a0d8 0x1b53 0x0 0x0 0xb0007 0x1b 0xe0 0x1b53 0xf0004 0x0 0x0 0x2dae928a0d8 0x1b53 0x0 0x0 0x160007 0x0 0x40 0x1b53 0x210007 0x0 0x68 0x1b53 0x2c0002 0x1b53 0x2f0007 0x1634 0x38 0x51f 0x330003 0x51f 0x18 0x0 0x0 0x0 0x0 0x0 0x0 0x9 0x2 0x0 0x0 oops 2 7 java/lang/String 18 java/lang/String methods 0
ciMethodData java/util/AbstractCollection <init> ()V 2 184131 orig 80 0 0 0 0 0 0 0 0 0 0 0 0 218 2 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 data 7 0x10002 0x2cf43 0x0 0x0 0x9 0x1 0x0 oops 0 methods 0
ciMethodData java/util/HashMap putVal (ILjava/lang/Object;Ljava/lang/Object;ZZ)Ljava/lang/Object; 2 17423 orig 80 3 0 0 0 1 0 0 0 0 0 0 0 218 2 0 0 0 0 0 0 5 0 0 5 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 1 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 data 198 0x70007 0xe1a 0x40 0x35f5 0x100007 0x35f5 0x58 0x0 0x140005 0xe1a 0x0 0x0 0x0 0x0 0x0 0x2c0007 0xf7b 0xa8 0x3494 0x380005 0x2d 0x0 0x2dae9a95438 0x1e7a 0x2dae949cf48 0x15ed 0x3b0004 0x0 0x0 0x2daee35f970 0x1ea7 0x2dae949d158 0x15ed 0x3c0003 0x3494 0x410 0x450007 0xf11 0xd0 0x6a 0x510007 0x1f 0x98 0x4b 0x550007 0x0 0x90 0x4b 0x80000007005b0005 0x2c 0x0 0x2dae928d4e8 0x12 0x2dae928a0d8 0x12 0x80000006005e0007 0x13 0x38 0x3e 0x650003 0x5d 0x2a8 0x6a0004 0xfffffffffffff0dc 0x0 0x2daee35f970 0x178 0x2dae949d158 0xd 0x6d0007 0xf24 0xa8 0x0 0x720004 0x0 0x0 0x0 0x0 0x0 0x0 0x7b0005 0x0 0x0 0x0 0x0 0x0 0x0 0x800003 0x0 0x1c8 0x8e0007 0x377 0xc8 0xf1a 0x980005 0x21 0x0 0x2dae9a95438 0xb8b 0x2dae949cf48 0x36e 0xa20007 0xf1a 0x158 0x0 0xa90005 0x0 0x0 0x0 0x0 0x0 0x0 0xac0003 0x0 0x100 0xb50007 0x33b 0xd0 0x3c 0xc10007 0x2 0xc8 0x3a 0xc50007 0x0 0x90 0x3a 0x8000000400cb0005 0x32 0x0 0x2dae928d4e8 0x4 0x2dae928a0d8 0x9 0xce0007 0x32 0x38 0xd 0xd10003 0xd 0x30 0xdb0003 0x36d 0xfffffffffffffe68 0xe00007 0xf1a 0x98 0x6c 0xec0007 0x6c 0x40 0x0 0xf10007 0x0 0x20 0x0 0xfd0005 0x0 0x0 0x2dae949cf48 0x4b 0x2dae9a95438 0x21 0x11c0007 0x42af 0x58 0xff 0x1200005 0xff 0x0 0x0 0x0 0x0 0x0 0x1270005 0x4e 0x0 0x2dae9a95438 0x2a05 0x2dae949cf48 0x195b 0x0 0x0 0x0 0x0 0x0 0x0 0x0 0x0 0x0 0x9 0x6 0x0 0x0 0x0 0x0 0x0 0x0 oops 16 22 java/util/HashMap 24 java/util/LinkedHashMap 29 java/util/HashMap$Node 31 java/util/LinkedHashMap$Entry 51 java/net/URL 53 java/lang/String 65 java/util/HashMap$Node 67 java/util/LinkedHashMap$Entry 97 java/util/HashMap 99 java/util/LinkedHashMap 130 java/net/URL 132 java/lang/String 159 java/util/LinkedHashMap 161 java/util/HashMap 177 java/util/HashMap 179 java/util/LinkedHashMap methods 0
ciMethodData java/util/HashMap resize ()[Ljava/util/HashMap$Node; 2 5917 orig 80 0 0 0 0 0 0 0 0 0 0 0 0 218 2 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 data 171 0x60007 0x121 0x38 0x15fc 0xa0003 0x15fc 0x18 0x190007 0x15fc 0x98 0x121 0x1f0007 0x121 0x20 0x0 0x320007 0x0 0x90 0x121 0x380007 0x2b 0x70 0xf6 0x400003 0xf6 0x50 0x440007 0xd0e 0x38 0x8ee 0x4a0003 0x8ee 0x18 0x570007 0xe04 0x78 0x919 0x680007 0x0 0x58 0x919 0x700007 0x0 0x38 0x919 0x760003 0x919 0x18 0x910007 0x15fc 0x378 0x121 0x9a0007 0x121 0x358 0x33b8 0xa40007 0x17f5 0x320 0x1bc3 0xab0104 0x0 0x0 0x0 0x0 0x0 0x0 0xb10007 0x90c 0x70 0x12b7 0xc20004 0x0 0x0 0x2daee35f970 0xb18 0x2dae949d158 0x79f 0xc30003 0x12b7 0x270 0xc80004 0xfffffffffffff6f4 0x0 0x2daee35f970 0x90 0x2dae949d158 0x21 0xcb0007 0x90c 0xa8 0x0 0xd00004 0x0 0x0 0x0 0x0 0x0 0x0 0xd90005 0x0 0x0 0x0 0x0 0x0 0x0 0xdc0003 0x0 0x190 0xf90007 0x9c0 0x70 0xa80 0xfe0007 0x346 0x38 0x73a 0x1050003 0x73a 0x18 0x1130003 0xa80 0x50 0x1180007 0x2d2 0x38 0x6ee 0x11f0003 0x6ee 0x18 0x1320007 0xb34 0xffffffffffffff58 0x90c 0x1370007 0x1d2 0x58 0x73a 0x1460004 0x0 0x0 0x2daee35f970 0x467 0x2dae949d158 0x2d3 0x1490007 0x21e 0x58 0x6ee 0x15a0004 0x0 0x0 0x2daee35f970 0x441 0x2dae949d158 0x2ad 0x15e0003 0x33b8 0xfffffffffffffcc0 0x0 0x0 0x0 0x0 0x0 0x0 0x0 0x0 0x0 0x9 0x1 0x0 oops 8 74 java/util/HashMap$Node 76 java/util/LinkedHashMap$Entry 84 java/util/HashMap$Node 86 java/util/LinkedHashMap$Entry 141 java/util/HashMap$Node 143 java/util/LinkedHashMap$Entry 152 java/util/HashMap$Node 154 java/util/LinkedHashMap$Entry methods 0
ciMethodData java/util/HashMap hash (Ljava/lang/Object;)I 2 47232 orig 80 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 3 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 data 23 0x8000000600010007 0xb881 0x38 0x3 0x50003 0x3 0x50 0x90005 0x21fc 0x0 0x2dae928b948 0x1ad 0x2dae928a0d8 0x94d8 0x0 0x0 0x0 0x0 0x0 0x0 0x9 0x1 0xffffffffffffffff oops 2 10 java/lang/Module 12 java/lang/String methods 0
ciMethodData java/util/HashMap$Node <init> (ILjava/lang/Object;Ljava/lang/Object;Ljava/util/HashMap$Node;)V 2 12821 orig 80 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 data 13 0x10002 0x3216 0x0 0x0 0x0 0x0 0x9 0x5 0xe 0x0 0x0 0x0 0x0 oops 0 methods 0
ciMethodData java/util/HashMap put (Ljava/lang/Object;Ljava/lang/Object;)Ljava/lang/Object; 2 15903 orig 80 3 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 data 20 0x20002 0x3e20 0x90005 0x3e22 0x0 0x0 0x0 0x0 0x0 0x0 0x0 0x0 0x0 0x0 0x0 0x9 0x3 0xffffffffffffffff 0xffffffffffffffff 0xffffffffffffffff oops 0 methods 0
ciMethodData java/util/HashMap getNode (Ljava/lang/Object;)Ljava/util/HashMap$Node; 2 12544 orig 80 2 0 0 0 1 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 5 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 2 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 data 103 0x60007 0x834 0x2e8 0x28cd 0xe0007 0x0 0x2c8 0x28cd 0x170002 0x28cd 0x210007 0x141e 0x298 0x14b0 0x2a0007 0xd5e 0xb8 0x752 0x350007 0x5b7 0x98 0x19b 0x390007 0x0 0x78 0x19b 0x80000007003f0005 0x30 0x0 0x2dae928a0d8 0x158 0x2dae9addd18 0x17 0x8000000600420007 0x13 0x20 0x18e 0x4e0007 0xa19 0x1c0 0x358 0x520004 0xfffffffffffffca8 0x0 0x2daee35f970 0xb2 0x0 0x0 0x550007 0x358 0x90 0x0 0x590004 0x0 0x0 0x0 0x0 0x0 0x0 0x5f0005 0x0 0x0 0x0 0x0 0x0 0x0 0x6a0007 0x2ae 0xb8 0x10c 0x760007 0x90 0x98 0x7c 0x7a0007 0x0 0x78 0x7c 0x700800005 0x40 0x0 0x2dae9addd18 0x1c 0x2dae928a0d8 0x21 0x830007 0x31 0x20 0x4c 0x910007 0x62 0xffffffffffffff48 0x27d 0x0 0x0 0x0 0x0 0x0 0x0 0x9 0x2 0x0 0xffffffffffffffff oops 5 29 java/lang/String 31 java/util/zip/ZipFile$Source$Key 44 java/util/HashMap$Node 81 java/util/zip/ZipFile$Source$Key 83 java/lang/String methods 0
ciMethodData jdk/internal/util/ArraysSupport newLength (III)I 2 5583 orig 80 0 0 0 0 0 0 0 0 0 0 0 0 218 2 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 data 23 0x30002 0x15cf 0xa0007 0x0 0x40 0x15cf 0x100007 0x0 0x20 0x15cf 0x170002 0x0 0x0 0x0 0x0 0x0 0x0 0x0 0x9 0x3 0x0 0x0 0x0 oops 0 methods 0
ciMethodData java/util/AbstractSet <init> ()V 2 34464 orig 80 0 0 0 0 0 0 0 0 0 0 0 0 218 2 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 data 7 0x10002 0x86a0 0x0 0x0 0x9 0x1 0x0 oops 0 methods 0
ciMethodData java/util/AbstractMap <init> ()V 2 72093 orig 80 0 0 0 0 0 0 0 0 0 0 0 0 218 2 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 data 7 0x10002 0x1199d 0x0 0x0 0x9 0x1 0x0 oops 0 methods 0
ciMethodData java/util/ArrayList add (Ljava/lang/Object;)Z 2 63688 orig 80 1 0 0 0 0 0 0 0 0 0 0 0 218 2 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 data 17 0x140005 0xf8c8 0x0 0x0 0x0 0x0 0x0 0x0 0x0 0x0 0x0 0x0 0x0 0x9 0x2 0xe 0x0 oops 0 methods 0
ciMethodData java/util/ArrayList add (Ljava/lang/Object;[Ljava/lang/Object;I)V 2 63688 orig 80 0 0 0 0 0 0 0 0 0 0 0 0 218 2 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 data 30 0x30007 0xdcf9 0x58 0x1bcf 0x70005 0x1bcf 0x0 0x0 0x0 0x0 0x0 0xe0104 0x0 0x0 0x2daeeb668f8 0x8 0x2dae928e508 0xdf 0x0 0x0 0x0 0x0 0x0 0x0 0x9 0x4 0xc 0x0 0xffffffffffffffff 0x0 oops 2 14 jdk/internal/loader/URLClassPath$JarLoader 16 java/lang/Integer methods 0
ciMethodData java/util/ArrayList grow ()[Ljava/lang/Object; 2 7142 orig 80 0 0 0 0 0 0 0 0 0 0 0 0 218 2 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 data 16 0x70005 0x1be6 0x0 0x0 0x0 0x0 0x0 0x0 0x0 0x0 0x0 0x0 0x0 0x9 0x1 0xc oops 0 methods 0
ciMethodData java/util/ArrayList grow (I)[Ljava/lang/Object; 2 9700 orig 80 0 0 0 0 0 0 0 0 0 0 0 0 218 2 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 1 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 data 24 0x70007 0x158 0x40 0x248c 0x8000000600110007 0x2486 0x40 0x7 0x1b0002 0x15f 0x250002 0x15f 0x310002 0x2486 0x0 0x0 0x0 0x0 0x0 0x0 0x9 0x2 0xc 0x0 oops 0 methods 0
ciMethodData java/lang/Boolean parseBoolean (Ljava/lang/String;)Z 2 6501 orig 80 0 0 0 0 0 0 0 0 0 0 0 0 218 2 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 data 16 0x30005 0x1965 0x0 0x0 0x0 0x0 0x0 0x0 0x0 0x0 0x0 0x0 0x0 0x9 0x1 0xffffffffffffffff oops 0 methods 0
ciMethodData java/util/AbstractList <init> ()V 2 140433 orig 80 0 0 0 0 0 0 0 0 0 0 0 0 218 2 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 data 9 0x10002 0x22491 0x0 0x0 0x0 0x0 0x9 0x1 0x6 oops 0 methods 0
ciMethodData java/util/LinkedHashMap newNode (ILjava/lang/Object;Ljava/lang/Object;Ljava/util/HashMap$Node;)Ljava/util/HashMap$Node; 2 7420 orig 80 0 0 0 0 0 0 0 0 0 0 0 0 218 2 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 data 22 0x90002 0x1cfc 0x110005 0x1cfc 0x0 0x0 0x0 0x0 0x0 0x0 0x0 0x0 0x0 0x0 0x0 0x9 0x5 0xc0 0x0 0x0 0x0 0x0 oops 0 methods 0
ciMethodData java/util/LinkedHashMap$Entry <init> (ILjava/lang/Object;Ljava/lang/Object;Ljava/util/HashMap$Node;)V 2 7420 orig 80 0 0 0 0 0 0 0 0 0 0 0 0 218 2 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 data 11 0x60002 0x1cfc 0x0 0x0 0x9 0x5 0xe 0x0 0x0 0x0 0x0 oops 0 methods 0
ciMethodData java/util/LinkedHashMap linkNodeAtEnd (Ljava/util/LinkedHashMap$Entry;)V 2 7420 orig 80 0 0 0 0 0 0 0 0 0 0 0 0 218 2 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 data 31 0x50007 0x1cfc 0x70 0x0 0x130007 0x0 0x38 0x0 0x1b0003 0x0 0x18 0x280003 0x0 0x50 0x360007 0x1405 0x38 0x8f7 0x3e0003 0x8f7 0x18 0x0 0x0 0x0 0x0 0x0 0x0 0x9 0x2 0xc0 0x18 oops 0 methods 0
ciMethodData java/util/Arrays copyOf ([Ljava/lang/Object;I)[Ljava/lang/Object; 2 6382 orig 80 0 0 0 0 0 0 0 0 0 0 0 0 218 2 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 data 19 0x30005 0x18ee 0x0 0x0 0x0 0x0 0x0 0x60002 0x18ee 0x0 0x0 0x0 0x0 0x0 0x0 0x9 0x2 0x0 0x0 oops 0 methods 0
ciMethodData java/util/LinkedHashMap afterNodeInsertion (Z)V 2 7395 orig 80 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 data 38 0x10007 0x8a1 0xe0 0x1443 0xa0007 0x0 0xc0 0x1443 0xf0005 0x0 0x0 0x2dae949cf48 0x1443 0x0 0x0 0x120007 0x1443 0x68 0x0 0x1c0002 0x0 0x230005 0x0 0x0 0x0 0x0 0x0 0x0 0x0 0x0 0x0 0x0 0x0 0x0 0x9 0x2 0x0 0x0 oops 1 11 java/util/LinkedHashMap methods 0
ciMethodData java/util/LinkedHashMap removeEldestEntry (Ljava/util/Map$Entry;)Z 2 5172 orig 80 0 0 0 0 0 0 0 0 0 0 0 0 218 2 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 data 5 0x0 0x9 0x2 0x0 0x0 oops 0 methods 0
ciMethodData java/util/LinkedHashMap get (Ljava/lang/Object;)Ljava/lang/Object; 2 18273 orig 80 0 0 0 0 0 0 0 0 0 0 0 0 218 2 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 data 32 0x20005 0x4761 0x0 0x0 0x0 0x0 0x0 0x70007 0xc58 0x20 0x3b09 0x100007 0xc58 0x58 0x0 0x150005 0x0 0x0 0x0 0x0 0x0 0x0 0x0 0x0 0x0 0x0 0x0 0x0 0x9 0x2 0xffffffffffffffff 0xffffffffffffffff oops 0 methods 0
ciMethodData java/util/LinkedHashMap afterNodeAccess (Ljava/util/HashMap$Node;)V 2 1245 orig 80 0 0 0 0 0 0 0 0 0 0 0 0 218 2 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 data 100 0x50007 0x0 0x60 0x4dd 0xc0007 0x0 0x158 0x4dd 0x130007 0x4dd 0x138 0x0 0x1d0007 0x0 0x118 0x0 0x210004 0x0 0x0 0x0 0x0 0x0 0x0 0x3c0007 0x0 0x38 0x0 0x450003 0x0 0x18 0x510007 0x0 0x38 0x0 0x5b0003 0x0 0x18 0x620007 0x0 0x38 0x0 0x6b0003 0x0 0x18 0x8a0003 0x0 0x138 0x920007 0x4dd 0x120 0x0 0x9c0007 0x0 0x100 0x0 0xa00004 0x0 0x0 0x0 0x0 0x0 0x0 0xbb0007 0x0 0x38 0x0 0xc40003 0x0 0x18 0xd00007 0x0 0x38 0x0 0xda0003 0x0 0x18 0xe10007 0x0 0x38 0x0 0xea0003 0x0 0x18 0x0 0x0 0x0 0x0 0x0 0x0 0x0 0x0 0x0 0x0 0x0 0x0 0x0 0x9 0x2 0x0 0x0 oops 0 methods 0
ciMethodData java/util/ArrayList <init> ()V 2 123472 orig 80 0 0 0 0 0 0 0 0 0 0 0 0 218 2 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 data 9 0x10002 0x1e250 0x0 0x0 0x0 0x0 0x9 0x1 0x0 oops 0 methods 0
ciMethodData java/util/ArrayList <init> (I)V 2 5216 orig 80 0 0 0 0 0 0 0 0 0 0 0 0 218 2 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 data 51 0x10002 0x1460 0x50007 0x8d0 0x38 0xb90 0x100003 0xb90 0x118 0x140007 0x0 0x38 0x8d0 0x1e0003 0x8d0 0xe0 0x290002 0x0 0x2e0005 0x0 0x0 0x0 0x0 0x0 0x0 0x320005 0x0 0x0 0x0 0x0 0x0 0x0 0x350005 0x0 0x0 0x0 0x0 0x0 0x0 0x380002 0x0 0x0 0x0 0x0 0x0 0x0 0x0 0x9 0x2 0x0 0x0 oops 0 methods 0
ciMethodData java/util/HashMap <init> (I)V 2 9782 orig 80 0 0 0 0 0 0 0 0 0 0 0 0 218 2 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 data 9 0x40002 0x2636 0x0 0x0 0x0 0x9 0x2 0x18 0x0 oops 0 methods 0
ciMethodData java/util/HashMap <init> (IF)V 2 8213 orig 80 0 0 0 0 0 0 0 0 0 0 0 0 218 2 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 data 83 0x10002 0x2015 0x50007 0x2015 0xe8 0x0 0x100002 0x0 0x150005 0x0 0x0 0x0 0x0 0x0 0x0 0x190005 0x0 0x0 0x0 0x0 0x0 0x0 0x1c0005 0x0 0x0 0x0 0x0 0x0 0x0 0x1f0002 0x0 0x260007 0x2015 0x20 0x0 0x2f0007 0x0 0x50 0x2015 0x330002 0x2015 0x360007 0x2015 0xe8 0x0 0x410002 0x0 0x460005 0x0 0x0 0x0 0x0 0x0 0x0 0x4a0005 0x0 0x0 0x0 0x0 0x0 0x0 0x4d0005 0x0 0x0 0x0 0x0 0x0 0x0 0x500002 0x0 0x5b0002 0x2015 0x0 0x0 0x0 0x0 0x0 0x0 0x9 0x3 0x18 0x0 0x0 oops 0 methods 0
ciMethodData java/util/ArrayList addAll (Ljava/util/Collection;)Z 2 7565 orig 80 0 0 0 0 0 0 0 0 0 0 0 0 218 2 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 data 34 0x10005 0x1bef 0x0 0x2daefd83b68 0x1 0x2daefd83c18 0x19d 0x150007 0x1904 0x20 0x489 0x2b0007 0x1f0 0x58 0x1714 0x330005 0x1714 0x0 0x0 0x0 0x0 0x0 0x3f0002 0x1904 0x0 0x0 0x0 0x0 0x0 0x0 0x9 0x2 0xe 0xffffffffffffffff oops 2 3 java/util/ImmutableCollections$List12 5 com/google/common/collect/SingletonImmutableList methods 0
ciMethodData java/util/ArrayList$Itr hasNext ()Z 2 5131 orig 80 0 0 0 0 0 0 0 0 0 0 0 0 218 2 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 data 14 0xb0007 0x997 0x38 0xa74 0xf0003 0xa74 0x18 0x0 0x0 0x0 0x0 0x9 0x1 0x0 oops 0 methods 0
ciMethodData java/util/ArrayList$Itr checkForComodification ()V 2 13231 orig 80 0 0 0 0 0 0 0 0 0 0 0 0 218 2 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 data 13 0xb0007 0x33af 0x30 0x0 0x120002 0x0 0x0 0x0 0x0 0x0 0x9 0x1 0x0 oops 0 methods 0
ciMethodData java/util/ArrayList$Itr next ()Ljava/lang/Object; 2 6690 orig 80 0 0 0 0 0 0 0 0 0 0 0 0 218 2 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 data 28 0x10005 0x1a22 0x0 0x0 0x0 0x0 0x0 0x110007 0x1a22 0x30 0x0 0x180002 0x0 0x270007 0x1a22 0x30 0x0 0x2e0002 0x0 0x0 0x0 0x0 0x0 0x0 0x0 0x9 0x1 0x6 oops 0 methods 0
ciMethodData java/util/HashMap <init> ()V 2 50160 orig 80 0 0 0 0 0 0 0 0 0 0 0 0 218 2 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 data 9 0x10002 0xc3f0 0x0 0x0 0x0 0x0 0x9 0x1 0x10 oops 0 methods 0
ciMethodData java/util/LinkedHashMap$LinkedHashIterator hasNext ()Z 2 5124 orig 80 0 0 0 0 0 0 0 0 0 0 0 0 218 2 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 data 16 0x40007 0x43b 0x38 0xfc9 0x80003 0xfc9 0x18 0x0 0x0 0x0 0x0 0x0 0x0 0x9 0x1 0x0 oops 0 methods 0
ciMethodData java/util/ArrayList$Itr <init> (Ljava/util/ArrayList;)V 2 13181 orig 80 0 0 0 0 0 0 0 0 0 0 0 0 218 2 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 data 10 0x60002 0x337d 0x0 0x0 0x0 0x0 0x9 0x2 0xc 0x0 oops 0 methods 0
ciMethodData java/util/LinkedHashMap$LinkedHashIterator nextNode ()Ljava/util/LinkedHashMap$Entry; 2 6141 orig 80 0 0 0 0 0 0 0 0 0 0 0 0 218 2 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 data 28 0x100007 0x17fd 0x30 0x0 0x170002 0x0 0x1c0007 0x17fd 0x30 0x0 0x230002 0x0 0x310007 0x17fd 0x38 0x0 0x380003 0x0 0x18 0x0 0x0 0x0 0x0 0x0 0x0 0x9 0x1 0x0 oops 0 methods 0
ciMethodData java/util/ArrayList iterator ()Ljava/util/Iterator; 2 6228 orig 80 0 0 0 0 0 0 0 0 0 0 0 0 218 2 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 data 8 0x50002 0x1854 0x0 0x0 0x0 0x9 0x1 0x0 oops 0 methods 0
ciMethodData java/util/HashMap tableSizeFor (I)I 2 14407 orig 80 0 0 0 0 0 0 0 0 0 0 0 0 218 2 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 data 25 0x40002 0x3847 0xa0007 0x328f 0x38 0x5b8 0xe0003 0x5b8 0x50 0x140007 0x328f 0x38 0x0 0x190003 0x0 0x18 0x0 0x0 0x0 0x0 0x0 0x0 0x9 0x1 0x0 oops 0 methods 0
ciMethodData java/lang/Float isNaN (F)Z 2 9261 orig 80 0 0 0 0 0 0 0 0 0 0 0 0 218 2 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 data 11 0x30007 0x242d 0x38 0x0 0x70003 0x0 0x18 0x0 0x9 0x1 0x0 oops 0 methods 0
ciMethodData java/util/LinkedHashMap <init> ()V 2 7042 orig 80 0 0 0 0 0 0 0 0 0 0 0 0 218 2 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 data 9 0x10002 0x1b82 0x0 0x0 0x0 0x0 0x9 0x1 0x70 oops 0 methods 0
ciMethodData java/util/ArrayList toArray ()[Ljava/lang/Object; 2 5129 orig 80 0 0 0 0 0 0 0 0 0 0 0 0 218 2 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 data 11 0x80002 0x1409 0x0 0x0 0x0 0x0 0x0 0x0 0x9 0x1 0x0 oops 0 methods 0
ciMethodData java/util/ArrayList isEmpty ()Z 2 5578 orig 80 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 data 13 0x40007 0xaf4 0x38 0xad7 0x80003 0xad7 0x18 0x0 0x0 0x0 0x9 0x1 0x0 oops 0 methods 0
ciMethodData java/util/LinkedHashMap$LinkedHashIterator <init> (Ljava/util/LinkedHashMap;Z)V 2 6288 orig 80 0 0 0 0 0 0 0 0 0 0 0 0 218 2 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 data 18 0x60002 0x1890 0x100007 0x1890 0x38 0x0 0x170003 0x0 0x18 0x0 0x0 0x0 0x0 0x9 0x3 0x1e 0x0 0x0 oops 0 methods 0
ciMethodData java/util/LinkedHashMap entrySet ()Ljava/util/Set; 2 6772 orig 80 0 0 0 0 0 0 0 0 0 0 0 0 218 2 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 data 16 0x10005 0x0 0x0 0x2dae949cf48 0x1a74 0x0 0x0 0x0 0x0 0x0 0x0 0x0 0x0 0x9 0x1 0x0 oops 1 3 java/util/LinkedHashMap methods 0
ciMethodData java/util/LinkedHashMap sequencedEntrySet ()Ljava/util/SequencedSet; 2 6772 orig 80 0 0 0 0 0 0 0 0 0 0 0 0 218 2 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 data 22 0x60007 0xabd 0x30 0xfb7 0xf0002 0xfb7 0x1b0004 0x0 0x0 0x2dae949cff8 0xabd 0x0 0x0 0x0 0x0 0x0 0x0 0x0 0x0 0x9 0x1 0x0 oops 1 9 java/util/LinkedHashMap$LinkedEntrySet methods 0
ciMethodData java/util/LinkedHashMap$LinkedEntrySet iterator ()Ljava/util/Iterator; 2 6154 orig 80 0 0 0 0 0 0 0 0 0 0 0 0 218 2 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 data 9 0xc0002 0x180a 0x0 0x0 0x0 0x0 0x9 0x1 0x0 oops 0 methods 0
ciMethodData java/util/LinkedHashMap$LinkedEntryIterator <init> (Ljava/util/LinkedHashMap;Z)V 2 6154 orig 80 0 0 0 0 0 0 0 0 0 0 0 0 218 2 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 data 9 0x30002 0x180a 0x0 0x0 0x9 0x3 0x0 0x0 0x0 oops 0 methods 0
ciMethodData java/util/LinkedHashMap$LinkedEntrySet <init> (Ljava/util/LinkedHashMap;Z)V 2 4017 orig 80 0 0 0 0 0 0 0 0 0 0 0 0 218 2 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 data 11 0x60002 0xfb1 0x0 0x0 0x0 0x0 0x9 0x3 0x6 0x0 0x0 oops 0 methods 0
ciMethodData java/util/LinkedHashMap$LinkedEntryIterator next ()Ljava/lang/Object; 2 5191 orig 80 0 0 0 0 0 0 0 0 0 0 0 0 218 2 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 data 16 0x10005 0x1447 0x0 0x0 0x0 0x0 0x0 0x0 0x0 0x0 0x0 0x0 0x0 0x9 0x1 0x0 oops 0 methods 0
ciMethodData java/util/LinkedHashMap$LinkedEntryIterator next ()Ljava/util/Map$Entry; 2 5191 orig 80 0 0 0 0 0 0 0 0 0 0 0 0 218 2 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 data 16 0x10005 0x1447 0x0 0x0 0x0 0x0 0x0 0x0 0x0 0x0 0x0 0x0 0x0 0x9 0x1 0x0 oops 0 methods 0
ciMethodData java/util/LinkedHashMap <init> (I)V 2 6363 orig 80 0 0 0 0 0 0 0 0 0 0 0 0 218 2 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 data 10 0x20002 0x18db 0x0 0x0 0x0 0x0 0x9 0x2 0x78 0x0 oops 0 methods 0
ciMethod org/apache/maven/model/Plugin <init> ()V 770 0 11364 0 0
ciMethod org/apache/maven/model/Plugin getArtifactId ()Ljava/lang/String; 336 0 168 0 -1
ciMethod org/apache/maven/model/Plugin getDependencies ()Ljava/util/List; 518 0 20718 0 -1
ciMethod org/apache/maven/model/Plugin getExecutions ()Ljava/util/List; 574 0 15399 0 0
ciMethod org/apache/maven/model/Plugin getExtensions ()Ljava/lang/String; 268 0 134 0 -1
ciMethod org/apache/maven/model/Plugin getGroupId ()Ljava/lang/String; 336 0 168 0 -1
ciMethod org/apache/maven/model/Plugin getVersion ()Ljava/lang/String; 274 0 137 0 -1
ciMethod org/apache/maven/model/Plugin setArtifactId (Ljava/lang/String;)V 256 0 128 0 -1
ciMethod org/apache/maven/model/Plugin setDependencies (Ljava/util/List;)V 318 0 159 0 -1
ciMethod org/apache/maven/model/Plugin setExtensions (Ljava/lang/String;)V 34 0 17 0 -1
ciMethod org/apache/maven/model/Plugin setGroupId (Ljava/lang/String;)V 326 0 163 0 0
ciMethod org/apache/maven/model/Plugin setVersion (Ljava/lang/String;)V 292 0 146 0 -1
ciMethod org/apache/maven/model/Plugin getKey ()Ljava/lang/String; 516 0 19792 0 0
ciMethod org/apache/maven/model/Plugin constructKey (Ljava/lang/String;Ljava/lang/String;)Ljava/lang/String; 516 0 19792 0 0
ciMethod org/apache/maven/model/ConfigurationContainer <init> ()V 516 0 13699 0 0
ciMethod org/apache/maven/model/ConfigurationContainer getLocation (Ljava/lang/Object;)Lorg/apache/maven/model/InputLocation; 534 0 6636 0 2624
ciMethod org/apache/maven/model/ConfigurationContainer setLocation (Ljava/lang/Object;Lorg/apache/maven/model/InputLocation;)V 664 0 7032 0 6424
ciMethod org/apache/maven/model/ConfigurationContainer setOtherLocation (Ljava/lang/Object;Lorg/apache/maven/model/InputLocation;)V 434 0 5078 0 -1
ciMethod org/apache/maven/model/ConfigurationContainer getOtherLocation (Ljava/lang/Object;)Lorg/apache/maven/model/InputLocation; 406 0 4979 0 -1
ciMethod org/apache/maven/model/ConfigurationContainer isInherited ()Z 774 0 17258 0 0
ciMethod org/apache/maven/model/merge/MavenModelMerger mergePlugin_Executions (Lorg/apache/maven/model/Plugin;Lorg/apache/maven/model/Plugin;ZLjava/util/Map;)V 564 74 6796 0 -1
ciMethod org/apache/maven/model/merge/MavenModelMerger getPluginKey (Lorg/apache/maven/model/Plugin;)Ljava/lang/Object; 1024 0 16882 0 1176
ciMethod org/apache/maven/model/merge/ModelMerger mergePlugin_GroupId (Lorg/apache/maven/model/Plugin;Lorg/apache/maven/model/Plugin;ZLjava/util/Map;)V 660 0 6505 0 -1
ciMethod org/apache/maven/model/merge/ModelMerger mergePlugin_ArtifactId (Lorg/apache/maven/model/Plugin;Lorg/apache/maven/model/Plugin;ZLjava/util/Map;)V 660 0 6505 0 -1
ciMethod org/apache/maven/model/merge/ModelMerger mergePlugin_Version (Lorg/apache/maven/model/Plugin;Lorg/apache/maven/model/Plugin;ZLjava/util/Map;)V 660 0 6726 0 -1
ciMethod org/apache/maven/model/merge/ModelMerger mergePlugin_Extensions (Lorg/apache/maven/model/Plugin;Lorg/apache/maven/model/Plugin;ZLjava/util/Map;)V 660 0 6505 0 -1
ciMethod org/apache/maven/model/merge/ModelMerger mergePlugin_Dependencies (Lorg/apache/maven/model/Plugin;Lorg/apache/maven/model/Plugin;ZLjava/util/Map;)V 660 0 6698 0 -1
ciMethod org/apache/maven/model/merge/ModelMerger mergeConfigurationContainer (Lorg/apache/maven/model/ConfigurationContainer;Lorg/apache/maven/model/ConfigurationContainer;ZLjava/util/Map;)V 532 0 7487 0 -1
ciMethod org/apache/maven/model/merge/ModelMerger merge (Ljava/util/List;Ljava/util/List;ZLorg/apache/maven/model/merge/ModelMerger$KeyComputer;)Ljava/util/List; 526 0 6475 0 -1
ciMethod org/apache/maven/model/PluginContainer getPlugins ()Ljava/util/List; 768 0 4697 0 0
ciMethod org/apache/maven/model/PluginContainer setPlugins (Ljava/util/List;)V 256 0 128 0 0
ciMethod org/apache/maven/model/inheritance/DefaultInheritanceAssembler$InheritanceModelMerger mergePluginContainer_Plugins (Lorg/apache/maven/model/PluginContainer;Lorg/apache/maven/model/PluginContainer;ZLjava/util/Map;)V 352 6306 1172 0 -1
ciMethod org/apache/maven/model/inheritance/DefaultInheritanceAssembler$InheritanceModelMerger mergePlugin (Lorg/apache/maven/model/Plugin;Lorg/apache/maven/model/Plugin;ZLjava/util/Map;)V 608 0 7205 0 8520
ciMethodData org/apache/maven/model/ConfigurationContainer setLocation (Ljava/lang/Object;Lorg/apache/maven/model/InputLocation;)V 2 6700 orig 80 0 0 0 0 0 0 0 0 0 0 0 0 218 2 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 data 109 0x10004 0x0 0x0 0x2dae928a0d8 0x1a2c 0x0 0x0 0x40007 0x0 0x2a0 0x1a2c 0x80004 0x0 0x0 0x2dae928a0d8 0x1a2c 0x0 0x0 0x100005 0x1a2c 0x0 0x0 0x0 0x0 0x0 0x130008 0x8 0x12fd 0x188 0xa 0xc0 0x69b 0x50 0x8a 0x130 0x370005 0x69b 0x0 0x0 0x0 0x0 0x0 0x3a0007 0x0 0x100 0x69b 0x400003 0x69b 0xe0 0x460005 0xa 0x0 0x0 0x0 0x0 0x0 0x490007 0x0 0x90 0xa 0x4f0003 0xa 0x70 0x550005 0x8a 0x0 0x0 0x0 0x0 0x0 0x580007 0x0 0x20 0x8a 0x600008 0x8 0x12fd 0x50 0x69b 0x50 0xa 0x50 0x8a 0x50 0x910005 0xbf 0x0 0x2dae949ce98 0x10fe 0x2daef58ffa8 0x140 0x980005 0x0 0x0 0x0 0x0 0x0 0x0 0x0 0x0 0x0 0x0 0x0 0x0 0x9 0x3 0x0 0x0 0x0 oops 4 3 java/lang/String 14 java/lang/String 87 org/apache/maven/model/Plugin 89 org/apache/maven/model/PluginExecution methods 0
ciMethod org/apache/maven/model/merge/ModelMerger$DependencyKeyComputer <init> (Lorg/apache/maven/model/merge/ModelMerger;Lorg/apache/maven/model/merge/ModelMerger$1;)V 514 0 7950 0 -1
ciMethodData org/apache/maven/model/ConfigurationContainer getLocation (Ljava/lang/Object;)Lorg/apache/maven/model/InputLocation; 2 6369 orig 80 0 0 0 0 0 0 0 0 0 0 0 0 218 2 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 data 98 0x10004 0x0 0x0 0x2dae928a0d8 0x18e1 0x0 0x0 0x40007 0x0 0x278 0x18e1 0x80004 0x0 0x0 0x2dae928a0d8 0x18e1 0x0 0x0 0xf0005 0x18e1 0x0 0x0 0x0 0x0 0x0 0x120008 0x8 0x12a8 0x188 0x1 0xc0 0x638 0x50 0x0 0x130 0x370005 0x638 0x0 0x0 0x0 0x0 0x0 0x3a0007 0x0 0x100 0x638 0x3f0003 0x638 0xe0 0x450005 0x1 0x0 0x0 0x0 0x0 0x0 0x480007 0x0 0x90 0x1 0x4d0003 0x1 0x70 0x530005 0x0 0x0 0x0 0x0 0x0 0x0 0x560007 0x0 0x20 0x0 0x5c0008 0x8 0x12a8 0x50 0x638 0x50 0x1 0x50 0x0 0x50 0x890002 0x12a8 0x8f0002 0x0 0x0 0x0 0x0 0x0 0x0 0x0 0x9 0x2 0x0 0xffffffffffffffff oops 2 3 java/lang/String 14 java/lang/String methods 0
ciMethodData org/apache/maven/model/Plugin getKey ()Ljava/lang/String; 2 19534 orig 80 0 0 0 0 0 0 0 0 0 0 0 0 218 2 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 data 11 0x80002 0x4c4e 0x0 0x0 0x0 0x0 0x0 0x0 0x9 0x1 0x0 oops 0 methods 0
ciMethodData org/apache/maven/model/Plugin constructKey (Ljava/lang/String;Ljava/lang/String;)Ljava/lang/String; 2 19534 orig 80 0 0 0 0 0 0 0 0 0 0 0 0 218 2 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 data 40 0x40002 0x4c4e 0x80005 0x4c4e 0x0 0x0 0x0 0x0 0x0 0xd0005 0x4c4e 0x0 0x0 0x0 0x0 0x0 0x110005 0x4c4e 0x0 0x0 0x0 0x0 0x0 0x140005 0x4c4e 0x0 0x0 0x0 0x0 0x0 0x0 0x0 0x0 0x0 0x0 0x0 0x9 0x2 0x0 0x0 oops 0 methods 0
ciMethodData org/apache/maven/model/ConfigurationContainer <init> ()V 2 13441 orig 80 0 0 0 0 0 0 0 0 0 0 0 0 218 2 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 data 9 0x10002 0x3481 0x0 0x0 0x0 0x0 0x9 0x1 0x0 oops 0 methods 0
ciMethodData org/apache/maven/model/Plugin getDependencies ()Ljava/util/List; 2 20459 orig 80 0 0 0 0 0 0 0 0 0 0 0 0 218 2 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 data 15 0x40007 0x286f 0x30 0x277c 0xc0002 0x277c 0x0 0x0 0x0 0x0 0x0 0x0 0x9 0x1 0x180 oops 0 methods 0
ciMethodData org/apache/maven/model/Plugin getExecutions ()Ljava/util/List; 2 15429 orig 80 0 0 0 0 0 0 0 0 0 0 0 0 218 2 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 data 15 0x40007 0x11cf 0x30 0x2a76 0xc0002 0x2a76 0x0 0x0 0x0 0x0 0x0 0x0 0x9 0x1 0x80 oops 0 methods 0
ciMethodData org/apache/maven/model/ConfigurationContainer isInherited ()Z 2 16875 orig 80 0 0 0 0 0 0 0 0 0 0 0 0 218 2 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 data 18 0x40007 0x418e 0x48 0x5d 0xb0002 0x5d 0xe0003 0x5d 0x18 0x0 0x0 0x0 0x0 0x0 0x0 0x9 0x1 0x0 oops 0 methods 0
ciMethodData org/apache/maven/model/merge/ModelMerger$DependencyKeyComputer <init> (Lorg/apache/maven/model/merge/ModelMerger;Lorg/apache/maven/model/merge/ModelMerger$1;)V 2 7693 orig 80 0 0 0 0 0 0 0 0 0 0 0 0 218 2 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 data 9 0x20002 0x1e0d 0x0 0x0 0x9 0x3 0x6 0x0 0x0 oops 0 methods 0
ciMethodData org/apache/maven/model/Plugin <init> ()V 2 10984 orig 80 0 0 0 0 0 0 0 0 0 0 0 0 218 2 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 data 9 0x10002 0x2ae8 0x0 0x0 0x0 0x0 0x9 0x1 0x0 oops 0 methods 0
ciMethodData org/apache/maven/model/merge/MavenModelMerger getPluginKey (Lorg/apache/maven/model/Plugin;)Ljava/lang/Object; 2 16389 orig 80 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 data 17 0x10005 0x0 0x0 0x2dae949ce98 0x4006 0x0 0x0 0x0 0x0 0x0 0x0 0x0 0x0 0x9 0x2 0x0 0x0 oops 1 3 org/apache/maven/model/Plugin methods 0
ciMethodData org/apache/maven/model/merge/MavenModelMerger mergePlugin_Executions (Lorg/apache/maven/model/Plugin;Lorg/apache/maven/model/Plugin;ZLjava/util/Map;)V 2 6514 orig 80 0 0 0 0 0 0 0 0 0 0 0 0 218 2 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 data 233 0x10005 0x0 0x0 0x2dae949ce98 0x1972 0x0 0x0 0x80005 0x0 0x0 0x2dae928db38 0x1972 0x0 0x0 0xd0007 0x14eb 0x670 0x487 0x110005 0x0 0x0 0x2dae949ce98 0x487 0x0 0x0 0x1c0005 0x0 0x0 0x2dae928db38 0x487 0x0 0x0 0x230005 0x0 0x0 0x2dae928db38 0x487 0x0 0x0 0x2b0002 0x487 0x320005 0x0 0x0 0x2dae928db38 0x487 0x0 0x0 0x3b0005 0x0 0x0 0x2dae949cde8 0xb14 0x0 0x0 0x400007 0x487 0x258 0x68d 0x450005 0x0 0x0 0x2dae949cde8 0x68d 0x0 0x0 0x4a0004 0x0 0x0 0x2daef58ffa8 0x68d 0x0 0x0 0x500007 0x0 0x140 0x68d 0x550005 0x0 0x0 0x2daef58ffa8 0x68d 0x0 0x0 0x580007 0x680 0x90 0xd 0x5d0005 0x0 0x0 0x2daef58ffa8 0xd 0x0 0x0 0x600007 0xd 0x100 0x0 0x630003 0x0 0x70 0x670005 0x0 0x0 0x2dae949ce98 0x680 0x0 0x0 0x6a0007 0x3 0x90 0x67d 0x700005 0x0 0x0 0x2dae949bca0 0x677 0x2daeefe07b8 0x6 0x7b0005 0x0 0x0 0x2dae949cf48 0x67d 0x0 0x0 0x810003 0x68d 0xfffffffffffffd88 0x860005 0x0 0x0 0x2dae928db38 0x487 0x0 0x0 0x8f0005 0x0 0x0 0x2dae949cde8 0x4d4 0x0 0x0 0x940007 0x487 0x1e0 0x4d 0x990005 0x0 0x0 0x2dae949cde8 0x4d 0x0 0x0 0x9e0004 0x0 0x0 0x2daef58ffa8 0x4d 0x0 0x0 0xa60005 0x0 0x0 0x2dae949bca0 0x4d 0x0 0x0 0xaf0005 0x0 0x0 0x2dae949cf48 0x4d 0x0 0x0 0xb40104 0x0 0x0 0x2daef58ffa8 0x1b 0x0 0x0 0xbb0007 0x32 0x58 0x1b 0xc60005 0x0 0x0 0x2dae949bca0 0x1b 0x0 0x0 0xcf0005 0x0 0x0 0x2dae949cf48 0x4d 0x0 0x0 0xd50003 0x4d 0xfffffffffffffe00 0xdf0005 0x0 0x0 0x2dae949cf48 0x487 0x0 0x0 0xe40002 0x487 0xe70005 0x0 0x0 0x2dae949ce98 0x487 0x0 0x0 0x0 0x0 0x0 0x0 0x0 0x0 0x9 0x5 0x0 0x0 0x0 0x0 0x0 oops 26 3 org/apache/maven/model/Plugin 10 java/util/ArrayList 21 org/apache/maven/model/Plugin 28 java/util/ArrayList 35 java/util/ArrayList 44 java/util/ArrayList 51 java/util/ArrayList$Itr 62 java/util/ArrayList$Itr 69 org/apache/maven/model/PluginExecution 80 org/apache/maven/model/PluginExecution 91 org/apache/maven/model/PluginExecution 105 org/apache/maven/model/Plugin 116 org/apache/maven/model/inheritance/DefaultInheritanceAssembler$InheritanceModelMerger 118 org/apache/maven/model/plugin/DefaultLifecycleBindingsInjector$LifecycleBindingsMerger 123 java/util/LinkedHashMap 133 java/util/ArrayList 140 java/util/ArrayList$Itr 151 java/util/ArrayList$Itr 158 org/apache/maven/model/PluginExecution 165 org/apache/maven/model/inheritance/DefaultInheritanceAssembler$InheritanceModelMerger 172 java/util/LinkedHashMap 179 org/apache/maven/model/PluginExecution 190 org/apache/maven/model/inheritance/DefaultInheritanceAssembler$InheritanceModelMerger 197 java/util/LinkedHashMap 207 java/util/LinkedHashMap 216 org/apache/maven/model/Plugin methods 0
ciMethodData org/apache/maven/model/inheritance/DefaultInheritanceAssembler$InheritanceModelMerger mergePlugin (Lorg/apache/maven/model/Plugin;Lorg/apache/maven/model/Plugin;ZLjava/util/Map;)V 2 6901 orig 80 0 0 0 0 0 0 0 0 0 0 0 0 218 2 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 data 73 0x10005 0x0 0x0 0x2dae949ce98 0x1af5 0x0 0x0 0x40007 0x3 0x58 0x1af2 0xd0005 0x0 0x0 0x2dae949bca0 0x1af2 0x0 0x0 0x160005 0x0 0x0 0x2dae949bca0 0x1af5 0x0 0x0 0x1f0005 0x0 0x0 0x2dae949bca0 0x1af5 0x0 0x0 0x280005 0x0 0x0 0x2dae949bca0 0x1af5 0x0 0x0 0x310005 0x0 0x0 0x2dae949bca0 0x1af5 0x0 0x0 0x3a0005 0x0 0x0 0x2dae949bca0 0x1af5 0x0 0x0 0x430005 0x0 0x0 0x2dae949bca0 0x1af5 0x0 0x0 0x0 0x0 0x0 0x0 0x0 0x0 0x9 0x5 0x0 0x0 0x0 0x0 0x0 oops 8 3 org/apache/maven/model/Plugin 14 org/apache/maven/model/inheritance/DefaultInheritanceAssembler$InheritanceModelMerger 21 org/apache/maven/model/inheritance/DefaultInheritanceAssembler$InheritanceModelMerger 28 org/apache/maven/model/inheritance/DefaultInheritanceAssembler$InheritanceModelMerger 35 org/apache/maven/model/inheritance/DefaultInheritanceAssembler$InheritanceModelMerger 42 org/apache/maven/model/inheritance/DefaultInheritanceAssembler$InheritanceModelMerger 49 org/apache/maven/model/inheritance/DefaultInheritanceAssembler$InheritanceModelMerger 56 org/apache/maven/model/inheritance/DefaultInheritanceAssembler$InheritanceModelMerger methods 0
ciMethodData org/apache/maven/model/merge/ModelMerger mergePlugin_GroupId (Lorg/apache/maven/model/Plugin;Lorg/apache/maven/model/Plugin;ZLjava/util/Map;)V 2 6175 orig 80 0 0 0 0 0 0 0 0 0 0 0 0 218 2 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 data 60 0x10005 0x0 0x0 0x2dae949ce98 0x181f 0x0 0x0 0x80007 0x0 0x140 0x181f 0xc0007 0x4 0x78 0x181b 0x100005 0x0 0x0 0x2dae949ce98 0x181b 0x0 0x0 0x130007 0x48b 0xc8 0x1390 0x190005 0x0 0x0 0x2dae949ce98 0x1394 0x0 0x0 0x220005 0x0 0x0 0x2dae949ce98 0x1394 0x0 0x0 0x250005 0x0 0x0 0x2dae949ce98 0x1394 0x0 0x0 0x0 0x0 0x0 0x0 0x0 0x0 0x9 0x5 0x0 0x0 0x0 0x0 0x0 oops 5 3 org/apache/maven/model/Plugin 18 org/apache/maven/model/Plugin 29 org/apache/maven/model/Plugin 36 org/apache/maven/model/Plugin 43 org/apache/maven/model/Plugin methods 0
ciMethodData org/apache/maven/model/merge/ModelMerger mergePlugin_ArtifactId (Lorg/apache/maven/model/Plugin;Lorg/apache/maven/model/Plugin;ZLjava/util/Map;)V 2 6175 orig 80 0 0 0 0 0 0 0 0 0 0 0 0 218 2 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 data 60 0x10005 0x0 0x0 0x2dae949ce98 0x181f 0x0 0x0 0x80007 0x0 0x140 0x181f 0xc0007 0x4 0x78 0x181b 0x100005 0x0 0x0 0x2dae949ce98 0x181b 0x0 0x0 0x130007 0x48b 0xc8 0x1390 0x190005 0x0 0x0 0x2dae949ce98 0x1394 0x0 0x0 0x220005 0x0 0x0 0x2dae949ce98 0x1394 0x0 0x0 0x250005 0x0 0x0 0x2dae949ce98 0x1394 0x0 0x0 0x0 0x0 0x0 0x0 0x0 0x0 0x9 0x5 0x0 0x0 0x0 0x0 0x0 oops 5 3 org/apache/maven/model/Plugin 18 org/apache/maven/model/Plugin 29 org/apache/maven/model/Plugin 36 org/apache/maven/model/Plugin 43 org/apache/maven/model/Plugin methods 0
ciMethodData org/apache/maven/model/merge/ModelMerger mergePlugin_Version (Lorg/apache/maven/model/Plugin;Lorg/apache/maven/model/Plugin;ZLjava/util/Map;)V 2 6414 orig 80 1 0 0 0 0 0 0 0 0 0 0 0 218 2 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 1 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 data 60 0x10005 0x0 0x0 0x2dae949ce98 0x190e 0x0 0x0 0x80007 0x2cf 0x140 0x163f 0x80000006000c0007 0xe 0x78 0x1632 0x100005 0x0 0x0 0x2dae949ce98 0x1632 0x0 0x0 0x130007 0x256 0xc8 0x13dc 0x190005 0x0 0x0 0x2dae949ce98 0x13ea 0x0 0x0 0x220005 0x0 0x0 0x2dae949ce98 0x13ea 0x0 0x0 0x250005 0x0 0x0 0x2dae949ce98 0x13ea 0x0 0x0 0x0 0x0 0x0 0x0 0x0 0x0 0x9 0x5 0x0 0x0 0x0 0x0 0x0 oops 5 3 org/apache/maven/model/Plugin 18 org/apache/maven/model/Plugin 29 org/apache/maven/model/Plugin 36 org/apache/maven/model/Plugin 43 org/apache/maven/model/Plugin methods 0
ciMethodData org/apache/maven/model/merge/ModelMerger mergePlugin_Extensions (Lorg/apache/maven/model/Plugin;Lorg/apache/maven/model/Plugin;ZLjava/util/Map;)V 2 6175 orig 80 0 0 0 0 0 0 0 0 0 0 0 0 218 2 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 data 60 0x10005 0x0 0x0 0x2dae949ce98 0x181f 0x0 0x0 0x80007 0x17f0 0x140 0x2f 0xc0007 0x0 0x78 0x2f 0x100005 0x0 0x0 0x2dae949ce98 0x2f 0x0 0x0 0x130007 0x0 0xc8 0x2f 0x190005 0x0 0x0 0x2dae949ce98 0x2f 0x0 0x0 0x240005 0x0 0x0 0x2dae949ce98 0x2f 0x0 0x0 0x270005 0x0 0x0 0x2dae949ce98 0x2f 0x0 0x0 0x0 0x0 0x0 0x0 0x0 0x0 0x9 0x5 0x0 0x0 0x0 0x0 0x0 oops 5 3 org/apache/maven/model/Plugin 18 org/apache/maven/model/Plugin 29 org/apache/maven/model/Plugin 36 org/apache/maven/model/Plugin 43 org/apache/maven/model/Plugin methods 0
ciMethodData org/apache/maven/model/merge/ModelMerger mergePlugin_Dependencies (Lorg/apache/maven/model/Plugin;Lorg/apache/maven/model/Plugin;ZLjava/util/Map;)V 2 6368 orig 80 0 0 0 0 0 0 0 0 0 0 0 0 218 2 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 data 38 0x20005 0x0 0x0 0x2dae949ce98 0x18e0 0x0 0x0 0x60005 0x0 0x0 0x2dae949ce98 0x18e0 0x0 0x0 0x100002 0x18e0 0x130002 0x18e0 0x160005 0x0 0x0 0x2dae949ce98 0x18e0 0x0 0x0 0x0 0x0 0x0 0x0 0x0 0x0 0x9 0x5 0x0 0x0 0x0 0x0 0x0 oops 3 3 org/apache/maven/model/Plugin 10 org/apache/maven/model/Plugin 21 org/apache/maven/model/Plugin methods 0
ciMethodData org/apache/maven/model/PluginContainer getPlugins ()Ljava/util/List; 2 4313 orig 80 0 0 0 0 0 0 0 0 0 0 0 0 218 2 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 data 15 0x40007 0xd94 0x30 0x345 0xc0002 0x345 0x0 0x0 0x0 0x0 0x0 0x0 0x9 0x1 0x0 oops 0 methods 0
ciMethodData org/apache/maven/model/inheritance/DefaultInheritanceAssembler$InheritanceModelMerger mergePluginContainer_Plugins (Lorg/apache/maven/model/PluginContainer;Lorg/apache/maven/model/PluginContainer;ZLjava/util/Map;)V 2 996 orig 80 0 0 0 0 0 0 0 0 0 0 0 0 218 2 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 data 388 0x10005 0x0 0x0 0x2dae949cc88 0x1f2 0x2dae949cd38 0x1f2 0x80005 0x0 0x0 0x2dae928db38 0x3e4 0x0 0x0 0xd0007 0x12a 0xb48 0x2ba 0x110005 0x0 0x0 0x2dae949cd38 0x1f2 0x2dae949cc88 0xc8 0x1c0005 0x0 0x0 0x2dae928db38 0x2ba 0x0 0x0 0x230002 0x2ba 0x2a0005 0x0 0x0 0x2dae928db38 0x2ba 0x0 0x0 0x330005 0x0 0x0 0x2dae949cde8 0x1cc9 0x0 0x0 0x380007 0x2ba 0x2f0 0x1a0f 0x3d0005 0x0 0x0 0x2dae949cde8 0x1a0f 0x0 0x0 0x420004 0x0 0x0 0x2dae949ce98 0x1a0f 0x0 0x0 0x490005 0x0 0x0 0x2dae949ce98 0x1a0f 0x0 0x0 0x4c0007 0x1a07 0xb0 0x8 0x510005 0x0 0x0 0x2dae949ce98 0x8 0x0 0x0 0x540005 0x0 0x0 0x2dae928db38 0x8 0x0 0x0 0x590007 0x7 0x180 0x1 0x600002 0x1a08 0x6d0005 0x0 0x0 0x2dae949ce98 0x1a08 0x0 0x0 0x700005 0x0 0x0 0x2dae949ce98 0x1a08 0x0 0x0 0x760005 0x0 0x0 0x2dae949ce98 0x1a08 0x0 0x0 0x810005 0x0 0x0 0x2dae949bca0 0x1a08 0x0 0x0 0x870005 0x0 0x0 0x2dae949bca0 0x1a08 0x0 0x0 0x920005 0x0 0x0 0x2dae949cf48 0x1a08 0x0 0x0 0x980003 0x1a0f 0xfffffffffffffcf0 0x9f0002 0x2ba 0xa80002 0x2ba 0xaf0005 0x0 0x0 0x2dae928db38 0x2ba 0x0 0x0 0xb80005 0x0 0x0 0x2dae949cde8 0xeab 0x0 0x0 0xbd0007 0x2ba 0x2d0 0xbf1 0xc20005 0x0 0x0 0x2dae949cde8 0xbf1 0x0 0x0 0xc70004 0x0 0x0 0x2dae949ce98 0xbf1 0x0 0x0 0xcf0005 0x0 0x0 0x2dae949bca0 0xbf1 0x0 0x0 0xd80005 0x0 0x0 0x2dae949cf48 0xbf1 0x0 0x0 0xdd0104 0x0 0x0 0x2dae949ce98 0x2a3 0x0 0x0 0xe40007 0x94e 0x148 0x2a3 0xef0005 0x0 0x0 0x2dae949bca0 0x2a3 0x0 0x0 0xf80005 0x0 0x0 0x2dae949cf48 0x2a3 0x0 0x0 0x1000005 0x0 0x0 0x2dae928db38 0x2a3 0x0 0x0 0x1050007 0x18a 0xb8 0x119 0x10e0005 0x0 0x0 0x2dae949cf48 0x119 0x0 0x0 0x1180002 0x119 0x11d0003 0x119 0x50 0x1240005 0x0 0x0 0x2dae928db38 0x94e 0x0 0x0 0x12a0003 0xbf1 0xfffffffffffffd10 0x1330005 0x0 0x0 0x2dae928db38 0x2ba 0x0 0x0 0x13a0005 0x0 0x0 0x2dae928db38 0x2ba 0x0 0x0 0x1400002 0x2ba 0x1470005 0x0 0x0 0x2dae949cf48 0x2ba 0x0 0x0 0x14c0005 0x0 0x0 0x2dae949cff8 0x2ba 0x0 0x0 0x1550005 0x0 0x0 0x2dae949d0a8 0x1cc2 0x0 0x0 0x15a0007 0x2ba 0x250 0x1a08 0x15f0005 0x0 0x0 0x2dae949d0a8 0x1a08 0x0 0x0 0x1640004 0x0 0x0 0x2dae949d158 0x1a08 0x0 0x0 0x16d0005 0x0 0x0 0x2dae949d158 0x1a08 0x0 0x0 0x1720005 0x0 0x0 0x2dae949cf48 0x1a08 0x0 0x0 0x1770104 0x0 0x0 0x2dae928db38 0x119 0x0 0x0 0x17e0007 0x18ef 0x58 0x119 0x1850005 0x0 0x0 0x2dae928db38 0x119 0x0 0x0 0x18f0005 0x0 0x0 0x2dae949d158 0x1a08 0x0 0x0 0x1940004 0x0 0x0 0x2dae949ce98 0x1a08 0x0 0x0 0x1970005 0x0 0x0 0x2dae928db38 0x1a08 0x0 0x0 0x19d0003 0x1a08 0xfffffffffffffd90 0x1a40005 0x0 0x0 0x2dae928db38 0x2ba 0x0 0x0 0x1ad0005 0x0 0x0 0x2dae949cd38 0x1f2 0x2dae949cc88 0xc8 0x0 0x0 0x0 0x0 0x0 0x0 0x9 0x5 0x0 0x0 0x0 0x0 0x0 oops 48 3 org/apache/maven/model/Build 5 org/apache/maven/model/PluginManagement 10 java/util/ArrayList 21 org/apache/maven/model/PluginManagement 23 org/apache/maven/model/Build 28 java/util/ArrayList 37 java/util/ArrayList 44 java/util/ArrayList$Itr 55 java/util/ArrayList$Itr 62 org/apache/maven/model/Plugin 69 org/apache/maven/model/Plugin 80 org/apache/maven/model/Plugin 87 java/util/ArrayList 100 org/apache/maven/model/Plugin 107 org/apache/maven/model/Plugin 114 org/apache/maven/model/Plugin 121 org/apache/maven/model/inheritance/DefaultInheritanceAssembler$InheritanceModelMerger 128 org/apache/maven/model/inheritance/DefaultInheritanceAssembler$InheritanceModelMerger 135 java/util/LinkedHashMap 149 java/util/ArrayList 156 java/util/ArrayList$Itr 167 java/util/ArrayList$Itr 174 org/apache/maven/model/Plugin 181 org/apache/maven/model/inheritance/DefaultInheritanceAssembler$InheritanceModelMerger 188 java/util/LinkedHashMap 195 org/apache/maven/model/Plugin 206 org/apache/maven/model/inheritance/DefaultInheritanceAssembler$InheritanceModelMerger 213 java/util/LinkedHashMap 220 java/util/ArrayList 231 java/util/LinkedHashMap 243 java/util/ArrayList 253 java/util/ArrayList 260 java/util/ArrayList 269 java/util/LinkedHashMap 276 java/util/LinkedHashMap$LinkedEntrySet 283 java/util/LinkedHashMap$LinkedEntryIterator 294 java/util/LinkedHashMap$LinkedEntryIterator 301 java/util/LinkedHashMap$Entry 308 java/util/LinkedHashMap$Entry 315 java/util/LinkedHashMap 322 java/util/ArrayList 333 java/util/ArrayList 340 java/util/LinkedHashMap$Entry 347 org/apache/maven/model/Plugin 354 java/util/ArrayList 364 java/util/ArrayList 371 org/apache/maven/model/PluginManagement 373 org/apache/maven/model/Build methods 0
compile org/apache/maven/model/inheritance/DefaultInheritanceAssembler$InheritanceModelMerger mergePluginContainer_Plugins (Lorg/apache/maven/model/PluginContainer;Lorg/apache/maven/model/PluginContainer;ZLjava/util/Map;)V -1 4 inline 161 0 -1 0 org/apache/maven/model/inheritance/DefaultInheritanceAssembler$InheritanceModelMerger mergePluginContainer_Plugins (Lorg/apache/maven/model/PluginContainer;Lorg/apache/maven/model/PluginContainer;ZLjava/util/Map;)V 1 1 0 org/apache/maven/model/PluginContainer getPlugins ()Ljava/util/List; 2 12 0 java/util/ArrayList <init> ()V 3 1 0 java/util/AbstractList <init> ()V 4 1 0 java/util/AbstractCollection <init> ()V 5 1 0 java/lang/Object <init> ()V 1 8 0 java/util/ArrayList isEmpty ()Z 1 17 0 org/apache/maven/model/PluginContainer getPlugins ()Ljava/util/List; 2 12 0 java/util/ArrayList <init> ()V 3 1 0 java/util/AbstractList <init> ()V 4 1 0 java/util/AbstractCollection <init> ()V 5 1 0 java/lang/Object <init> ()V 1 28 0 java/util/ArrayList size ()I 1 35 0 java/util/LinkedHashMap <init> (I)V 2 2 0 java/util/HashMap <init> (I)V 3 4 0 java/util/HashMap <init> (IF)V 4 1 0 java/util/AbstractMap <init> ()V 5 1 0 java/lang/Object <init> ()V 4 51 0 java/lang/Float isNaN (F)Z 4 91 0 java/util/HashMap tableSizeFor (I)I 1 42 0 java/util/ArrayList iterator ()Ljava/util/Iterator; 2 5 0 java/util/ArrayList$Itr <init> (Ljava/util/ArrayList;)V 3 6 0 java/lang/Object <init> ()V 1 51 0 java/util/ArrayList$Itr hasNext ()Z 1 61 0 java/util/ArrayList$Itr next ()Ljava/lang/Object; 2 1 0 java/util/ArrayList$Itr checkForComodification ()V 1 73 0 org/apache/maven/model/ConfigurationContainer isInherited ()Z 1 96 0 org/apache/maven/model/Plugin <init> ()V 2 1 0 org/apache/maven/model/ConfigurationContainer <init> ()V 3 1 0 java/lang/Object <init> ()V 1 118 0 org/apache/maven/model/Plugin setGroupId (Ljava/lang/String;)V 1 135 0 org/apache/maven/model/merge/MavenModelMerger getPluginKey (Lorg/apache/maven/model/Plugin;)Ljava/lang/Object; 2 1 0 org/apache/maven/model/Plugin getKey ()Ljava/lang/String; 3 8 0 org/apache/maven/model/Plugin constructKey (Ljava/lang/String;Ljava/lang/String;)Ljava/lang/String; 4 4 0 java/lang/StringBuilder <init> ()V 4 8 0 java/lang/StringBuilder append (Ljava/lang/String;)Ljava/lang/StringBuilder; 4 13 0 java/lang/StringBuilder append (Ljava/lang/String;)Ljava/lang/StringBuilder; 4 17 0 java/lang/StringBuilder append (Ljava/lang/String;)Ljava/lang/StringBuilder; 4 20 0 java/lang/StringBuilder toString ()Ljava/lang/String; 1 146 0 java/util/HashMap put (Ljava/lang/Object;Ljava/lang/Object;)Ljava/lang/Object; 2 2 0 java/util/HashMap hash (Ljava/lang/Object;)I 3 9 0 java/lang/String hashCode ()I 4 17 0 java/lang/String isLatin1 ()Z 4 27 0 java/lang/StringLatin1 hashCode ([B)I 2 9 0 java/util/HashMap putVal (ILjava/lang/Object;Ljava/lang/Object;ZZ)Ljava/lang/Object; 3 56 0 java/util/LinkedHashMap newNode (ILjava/lang/Object;Ljava/lang/Object;Ljava/util/HashMap$Node;)Ljava/util/HashMap$Node; 4 9 0 java/util/LinkedHashMap$Entry <init> (ILjava/lang/Object;Ljava/lang/Object;Ljava/util/HashMap$Node;)V 5 6 0 java/util/HashMap$Node <init> (ILjava/lang/Object;Ljava/lang/Object;Ljava/util/HashMap$Node;)V 6 1 0 java/lang/Object <init> ()V 4 17 0 java/util/LinkedHashMap linkNodeAtEnd (Ljava/util/LinkedHashMap$Entry;)V 3 295 0 java/util/LinkedHashMap afterNodeInsertion (Z)V 4 15 0 java/util/LinkedHashMap removeEldestEntry (Ljava/util/Map$Entry;)Z 1 159 0 java/util/LinkedHashMap <init> ()V 2 1 0 java/util/HashMap <init> ()V 3 1 0 java/util/AbstractMap <init> ()V 4 1 0 java/lang/Object <init> ()V 1 168 0 java/util/ArrayList <init> ()V 2 1 0 java/util/AbstractList <init> ()V 3 1 0 java/util/AbstractCollection <init> ()V 4 1 0 java/lang/Object <init> ()V 1 175 0 java/util/ArrayList iterator ()Ljava/util/Iterator; 2 5 0 java/util/ArrayList$Itr <init> (Ljava/util/ArrayList;)V 3 6 0 java/lang/Object <init> ()V 1 184 0 java/util/ArrayList$Itr hasNext ()Z 1 194 0 java/util/ArrayList$Itr next ()Ljava/lang/Object; 2 1 0 java/util/ArrayList$Itr checkForComodification ()V 1 207 0 org/apache/maven/model/merge/MavenModelMerger getPluginKey (Lorg/apache/maven/model/Plugin;)Ljava/lang/Object; 2 1 0 org/apache/maven/model/Plugin getKey ()Ljava/lang/String; 3 8 0 org/apache/maven/model/Plugin constructKey (Ljava/lang/String;Ljava/lang/String;)Ljava/lang/String; 4 4 0 java/lang/StringBuilder <init> ()V 4 8 0 java/lang/StringBuilder append (Ljava/lang/String;)Ljava/lang/StringBuilder; 4 13 0 java/lang/StringBuilder append (Ljava/lang/String;)Ljava/lang/StringBuilder; 4 17 0 java/lang/StringBuilder append (Ljava/lang/String;)Ljava/lang/StringBuilder; 4 20 0 java/lang/StringBuilder toString ()Ljava/lang/String; 1 216 0 java/util/LinkedHashMap get (Ljava/lang/Object;)Ljava/lang/Object; 2 2 0 java/util/HashMap getNode (Ljava/lang/Object;)Ljava/util/HashMap$Node; 3 23 0 java/util/HashMap hash (Ljava/lang/Object;)I 4 9 0 java/lang/String hashCode ()I 5 17 0 java/lang/String isLatin1 ()Z 5 27 0 java/lang/StringLatin1 hashCode ([B)I 1 248 0 java/util/HashMap put (Ljava/lang/Object;Ljava/lang/Object;)Ljava/lang/Object; 2 2 0 java/util/HashMap hash (Ljava/lang/Object;)I 3 9 0 java/lang/String hashCode ()I 4 17 0 java/lang/String isLatin1 ()Z 4 27 0 java/lang/StringLatin1 hashCode ([B)I 2 9 0 java/util/HashMap putVal (ILjava/lang/Object;Ljava/lang/Object;ZZ)Ljava/lang/Object; 3 56 0 java/util/LinkedHashMap newNode (ILjava/lang/Object;Ljava/lang/Object;Ljava/util/HashMap$Node;)Ljava/util/HashMap$Node; 4 9 0 java/util/LinkedHashMap$Entry <init> (ILjava/lang/Object;Ljava/lang/Object;Ljava/util/HashMap$Node;)V 5 6 0 java/util/HashMap$Node <init> (ILjava/lang/Object;Ljava/lang/Object;Ljava/util/HashMap$Node;)V 6 1 0 java/lang/Object <init> ()V 4 17 0 java/util/LinkedHashMap linkNodeAtEnd (Ljava/util/LinkedHashMap$Entry;)V 3 295 0 java/util/LinkedHashMap afterNodeInsertion (Z)V 4 15 0 java/util/LinkedHashMap removeEldestEntry (Ljava/util/Map$Entry;)Z 1 256 0 java/util/ArrayList isEmpty ()Z 1 270 0 java/util/HashMap put (Ljava/lang/Object;Ljava/lang/Object;)Ljava/lang/Object; 2 2 0 java/util/HashMap hash (Ljava/lang/Object;)I 3 9 0 java/lang/String hashCode ()I 4 17 0 java/lang/String isLatin1 ()Z 4 27 0 java/lang/StringLatin1 hashCode ([B)I 2 9 0 java/util/HashMap putVal (ILjava/lang/Object;Ljava/lang/Object;ZZ)Ljava/lang/Object; 3 56 0 java/util/LinkedHashMap newNode (ILjava/lang/Object;Ljava/lang/Object;Ljava/util/HashMap$Node;)Ljava/util/HashMap$Node; 4 9 0 java/util/LinkedHashMap$Entry <init> (ILjava/lang/Object;Ljava/lang/Object;Ljava/util/HashMap$Node;)V 5 6 0 java/util/HashMap$Node <init> (ILjava/lang/Object;Ljava/lang/Object;Ljava/util/HashMap$Node;)V 6 1 0 java/lang/Object <init> ()V 4 17 0 java/util/LinkedHashMap linkNodeAtEnd (Ljava/util/LinkedHashMap$Entry;)V 3 295 0 java/util/LinkedHashMap afterNodeInsertion (Z)V 4 15 0 java/util/LinkedHashMap removeEldestEntry (Ljava/util/Map$Entry;)Z 1 280 0 java/util/ArrayList <init> ()V 2 1 0 java/util/AbstractList <init> ()V 3 1 0 java/util/AbstractCollection <init> ()V 4 1 0 java/lang/Object <init> ()V 1 292 0 java/util/ArrayList add (Ljava/lang/Object;)Z 2 20 0 java/util/ArrayList add (Ljava/lang/Object;[Ljava/lang/Object;I)V 3 7 0 java/util/ArrayList grow ()[Ljava/lang/Object; 4 7 0 java/util/ArrayList grow (I)[Ljava/lang/Object; 5 27 0 jdk/internal/util/ArraysSupport newLength (III)I 5 37 0 java/util/Arrays copyOf ([Ljava/lang/Object;I)[Ljava/lang/Object; 1 307 0 java/util/ArrayList size ()I 1 314 0 java/util/ArrayList size ()I 1 320 0 java/util/ArrayList <init> (I)V 2 1 0 java/util/AbstractList <init> ()V 3 1 0 java/util/AbstractCollection <init> ()V 4 1 0 java/lang/Object <init> ()V 1 327 0 java/util/LinkedHashMap entrySet ()Ljava/util/Set; 2 1 0 java/util/LinkedHashMap sequencedEntrySet ()Ljava/util/SequencedSet; 3 15 0 java/util/LinkedHashMap$LinkedEntrySet <init> (Ljava/util/LinkedHashMap;Z)V 4 6 0 java/util/AbstractSet <init> ()V 5 1 0 java/util/AbstractCollection <init> ()V 6 1 0 java/lang/Object <init> ()V 1 332 0 java/util/LinkedHashMap$LinkedEntrySet iterator ()Ljava/util/Iterator; 2 12 0 java/util/LinkedHashMap$LinkedEntryIterator <init> (Ljava/util/LinkedHashMap;Z)V 3 3 0 java/util/LinkedHashMap$LinkedHashIterator <init> (Ljava/util/LinkedHashMap;Z)V 4 6 0 java/lang/Object <init> ()V 1 341 0 java/util/LinkedHashMap$LinkedHashIterator hasNext ()Z 1 351 0 java/util/LinkedHashMap$LinkedEntryIterator next ()Ljava/lang/Object; 2 1 0 java/util/LinkedHashMap$LinkedEntryIterator next ()Ljava/util/Map$Entry; 3 1 0 java/util/LinkedHashMap$LinkedHashIterator nextNode ()Ljava/util/LinkedHashMap$Entry; 1 365 0 java/util/HashMap$Node getKey ()Ljava/lang/Object; 1 370 0 java/util/LinkedHashMap get (Ljava/lang/Object;)Ljava/lang/Object; 2 2 0 java/util/HashMap getNode (Ljava/lang/Object;)Ljava/util/HashMap$Node; 3 23 0 java/util/HashMap hash (Ljava/lang/Object;)I 1 389 0 java/util/ArrayList addAll (Ljava/util/Collection;)Z 2 1 0 java/util/ArrayList toArray ()[Ljava/lang/Object; 3 8 0 java/util/Arrays copyOf ([Ljava/lang/Object;I)[Ljava/lang/Object; 2 51 0 java/util/ArrayList grow (I)[Ljava/lang/Object; 3 27 0 jdk/internal/util/ArraysSupport newLength (III)I 3 37 0 java/util/Arrays copyOf ([Ljava/lang/Object;I)[Ljava/lang/Object; 1 399 0 java/util/HashMap$Node getValue ()Ljava/lang/Object; 1 407 0 java/util/ArrayList add (Ljava/lang/Object;)Z 2 20 0 java/util/ArrayList add (Ljava/lang/Object;[Ljava/lang/Object;I)V 3 7 0 java/util/ArrayList grow ()[Ljava/lang/Object; 4 7 0 java/util/ArrayList grow (I)[Ljava/lang/Object; 5 27 0 jdk/internal/util/ArraysSupport newLength (III)I 5 37 0 java/util/Arrays copyOf ([Ljava/lang/Object;I)[Ljava/lang/Object; 1 420 0 java/util/ArrayList addAll (Ljava/util/Collection;)Z 2 1 0 java/util/ArrayList toArray ()[Ljava/lang/Object; 3 8 0 java/util/Arrays copyOf ([Ljava/lang/Object;I)[Ljava/lang/Object; 2 51 0 java/util/ArrayList grow (I)[Ljava/lang/Object; 3 27 0 jdk/internal/util/ArraysSupport newLength (III)I 3 37 0 java/util/Arrays copyOf ([Ljava/lang/Object;I)[Ljava/lang/Object; 1 429 0 org/apache/maven/model/PluginContainer setPlugins (Ljava/util/List;)V
