<?xml version="1.0" encoding="UTF-8"?><!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Strict//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-strict.dtd"><html xmlns="http://www.w3.org/1999/xhtml" lang="en"><head><meta http-equiv="Content-Type" content="text/html;charset=UTF-8"/><link rel="stylesheet" href="../jacoco-resources/report.css" type="text/css"/><link rel="shortcut icon" href="../jacoco-resources/report.gif" type="image/gif"/><title>WordToPdfConversionService.java</title><link rel="stylesheet" href="../jacoco-resources/prettify.css" type="text/css"/><script type="text/javascript" src="../jacoco-resources/prettify.js"></script></head><body onload="window['PR_TAB_WIDTH']=4;prettyPrint()"><div class="breadcrumb" id="breadcrumb"><span class="info"><a href="../jacoco-sessions.html" class="el_session">Sessions</a></span><a href="../index.html" class="el_report">dms-svc</a> &gt; <a href="index.source.html" class="el_package">com.ascentbusiness.dms_svc.service</a> &gt; <span class="el_source">WordToPdfConversionService.java</span></div><h1>WordToPdfConversionService.java</h1><pre class="source lang-java linenums">package com.ascentbusiness.dms_svc.service;

import com.ascentbusiness.dms_svc.config.WordConversionConfig;
import com.ascentbusiness.dms_svc.dto.WordConversionResult;
import com.ascentbusiness.dms_svc.dto.VirusScanResponse;
import com.ascentbusiness.dms_svc.enums.AuditAction;
import com.ascentbusiness.dms_svc.enums.VirusScanResult;
import com.ascentbusiness.dms_svc.enums.VirusScannerType;
import com.ascentbusiness.dms_svc.exception.DmsBusinessException;
import com.ascentbusiness.dms_svc.exception.WordConversionException;
import com.ascentbusiness.dms_svc.service.virus.VirusScanningService;
import com.ascentbusiness.dms_svc.util.CorrelationIdUtil;
import com.ascentbusiness.dms_svc.util.TempDirectoryUtil;
import org.apache.poi.xwpf.usermodel.XWPFDocument;
import org.apache.poi.xwpf.usermodel.XWPFParagraph;
import org.apache.poi.xwpf.usermodel.XWPFRun;
import com.itextpdf.text.Document;
import com.itextpdf.text.DocumentException;
import com.itextpdf.text.Paragraph;
import com.itextpdf.text.pdf.PdfWriter;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.web.multipart.MultipartFile;

import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.util.List;
import java.util.UUID;

/**
 * Service for converting Word documents to PDF format.
 * 
 * This service provides functionality to convert Microsoft Word documents (.docx)
 * to PDF format with virus scanning, audit logging, and comprehensive error handling.
 */
@Service
<span class="fc" id="L42">public class WordToPdfConversionService {</span>

<span class="fc" id="L44">    private static final Logger logger = LoggerFactory.getLogger(WordToPdfConversionService.class);</span>

    @Autowired
    private WordConversionConfig wordConversionConfig;

    @Autowired
    private VirusScanningService virusScanningService;

    @Autowired
    private AuditService auditService;

    @Autowired
    private TempDirectoryUtil tempDirectoryUtil;

    @Autowired
    private PandocConversionService pandocConversionService;

    /**
     * Convert Word document to PDF from multipart file upload.
     *
     * @param file the Word document to convert
     * @param userId the user performing the conversion
     * @param scannerType the virus scanner to use (optional, uses default if null)
     * @return conversion result with download path
     * @throws WordConversionException if conversion fails
     */
    @org.springframework.transaction.annotation.Transactional
    public WordConversionResult convertWordToPdfFromMultipart(MultipartFile file, String userId, VirusScannerType scannerType) {
<span class="nc" id="L72">        String correlationId = CorrelationIdUtil.getCurrentCorrelationId();</span>
<span class="nc" id="L73">        String sessionId = UUID.randomUUID().toString();</span>

        // Validate input first to avoid null pointer exceptions
<span class="nc" id="L76">        validateMultipartFile(file);</span>

<span class="nc" id="L78">        logger.info(&quot;Starting Word to PDF conversion from multipart file: {} (user: {}, session: {}) [{}]&quot;,</span>
<span class="nc" id="L79">                   file.getOriginalFilename(), userId, sessionId, correlationId);</span>

        try {
            // Get file content
<span class="nc" id="L83">            byte[] wordContent = file.getBytes();</span>
<span class="nc" id="L84">            String fileName = file.getOriginalFilename();</span>

<span class="nc" id="L86">            return performConversion(wordContent, fileName, userId, sessionId, scannerType);</span>

<span class="nc" id="L88">        } catch (IOException e) {</span>
<span class="nc" id="L89">            logger.error(&quot;Failed to read multipart file content for conversion (session: {}) [{}]&quot;, sessionId, correlationId, e);</span>
<span class="nc" id="L90">            auditService.logAudit(AuditAction.CONVERSION_FAILED, null, userId,</span>
<span class="nc" id="L91">                    String.format(&quot;Failed to read multipart file: %s - %s&quot;, file.getOriginalFilename(), e.getMessage()));</span>
<span class="nc" id="L92">            throw new WordConversionException(&quot;Failed to read file content&quot;, e, sessionId);</span>
        }
    }

    /**
     * Convert Word document to PDF from file path.
     *
     * @param filePath the path to the Word document
     * @param userId the user performing the conversion
     * @param scannerType the virus scanner to use (optional, uses default if null)
     * @return conversion result with download path
     * @throws WordConversionException if conversion fails
     */
    @org.springframework.transaction.annotation.Transactional
    public WordConversionResult convertWordToPdfFromPath(String filePath, String userId, VirusScannerType scannerType) {
<span class="nc" id="L107">        String correlationId = CorrelationIdUtil.getCurrentCorrelationId();</span>
<span class="nc" id="L108">        String sessionId = UUID.randomUUID().toString();</span>
        
<span class="nc" id="L110">        logger.info(&quot;Starting Word to PDF conversion from file path: {} (user: {}, session: {}) [{}]&quot;, </span>
                   filePath, userId, sessionId, correlationId);

        // Validate file path
<span class="nc" id="L114">        validateFilePath(filePath);</span>

        try {
<span class="nc" id="L117">            Path wordPath = Paths.get(filePath);</span>
<span class="nc" id="L118">            byte[] wordContent = Files.readAllBytes(wordPath);</span>
<span class="nc" id="L119">            String fileName = wordPath.getFileName().toString();</span>

<span class="nc" id="L121">            return performConversion(wordContent, fileName, userId, sessionId, scannerType);</span>

<span class="nc" id="L123">        } catch (IOException e) {</span>
<span class="nc" id="L124">            logger.error(&quot;Failed to read file from path: {} (session: {}) [{}]&quot;, filePath, sessionId, correlationId, e);</span>
<span class="nc" id="L125">            auditService.logAudit(AuditAction.CONVERSION_FAILED, null, userId,</span>
<span class="nc" id="L126">                    String.format(&quot;Failed to read file from path: %s - %s&quot;, filePath, e.getMessage()));</span>
<span class="nc" id="L127">            throw new WordConversionException(&quot;Failed to read file from path&quot;, e, sessionId);</span>
        }
    }

    /**
     * Perform the actual Word to PDF conversion with virus scanning and audit logging.
     */
    private WordConversionResult performConversion(byte[] wordContent, String fileName, String userId, 
                                                 String sessionId, VirusScannerType scannerType) {
<span class="nc" id="L136">        String correlationId = CorrelationIdUtil.getCurrentCorrelationId();</span>
        
<span class="nc" id="L138">        logger.info(&quot;Performing Word to PDF conversion: {} (session: {}) [{}]&quot;, fileName, sessionId, correlationId);</span>

        try {
            // Audit conversion initiation
<span class="nc" id="L142">            auditService.logAudit(AuditAction.CONVERSION_INITIATED, null, userId,</span>
<span class="nc" id="L143">                    String.format(&quot;Word to PDF conversion initiated: %s (session: %s)&quot;, fileName, sessionId));</span>

            // Perform virus scanning
<span class="nc bnc" id="L146" title="All 2 branches missed.">            VirusScannerType scanner = scannerType != null ? scannerType : wordConversionConfig.getVirusScanner();</span>
<span class="nc" id="L147">            VirusScanResponse virusScanResult = performVirusScanning(wordContent, fileName, userId, sessionId, scanner);</span>

            // Convert Word to PDF
<span class="nc" id="L150">            byte[] pdfContent = convertWordContentToPdf(wordContent, fileName, sessionId);</span>

            // Save converted file to download directory
<span class="nc" id="L153">            String pdfFileName = generatePdfFileName(fileName);</span>
<span class="nc" id="L154">            Path downloadPath = saveToDownloadDirectory(pdfContent, pdfFileName, sessionId);</span>

            // Audit successful conversion
<span class="nc" id="L157">            auditService.logAudit(AuditAction.CONVERSION_COMPLETED, null, userId,</span>
<span class="nc" id="L158">                    String.format(&quot;Word to PDF conversion completed: %s -&gt; %s (session: %s)&quot;, </span>
                                fileName, pdfFileName, sessionId));

<span class="nc" id="L161">            return WordConversionResult.builder()</span>
<span class="nc" id="L162">                    .sessionId(sessionId)</span>
<span class="nc" id="L163">                    .originalFileName(fileName)</span>
<span class="nc" id="L164">                    .convertedFileName(pdfFileName)</span>
<span class="nc" id="L165">                    .downloadPath(downloadPath.toString())</span>
<span class="nc" id="L166">                    .fileSize(pdfContent.length)</span>
<span class="nc" id="L167">                    .virusScanResponse(virusScanResult)</span>
<span class="nc" id="L168">                    .success(true)</span>
<span class="nc" id="L169">                    .message(&quot;Word document successfully converted to PDF&quot;)</span>
<span class="nc" id="L170">                    .build();</span>

<span class="nc" id="L172">        } catch (WordConversionException e) {</span>
            // Re-throw WordConversionException as-is (e.g., from virus scanning)
<span class="nc" id="L174">            throw e;</span>
<span class="nc" id="L175">        } catch (Exception e) {</span>
<span class="nc" id="L176">            logger.error(&quot;Word to PDF conversion failed (session: {}) [{}]&quot;, sessionId, correlationId, e);</span>
<span class="nc" id="L177">            auditService.logAudit(AuditAction.CONVERSION_FAILED, null, userId,</span>
<span class="nc" id="L178">                    String.format(&quot;Word to PDF conversion failed: %s (session: %s) - %s&quot;, fileName, sessionId, e.getMessage()));</span>

            // Cleanup on failure
<span class="nc" id="L181">            tempDirectoryUtil.cleanupTempDirectory(sessionId);</span>

<span class="nc" id="L183">            throw new WordConversionException(&quot;Word to PDF conversion failed&quot;, e, sessionId);</span>
        }
    }

    /**
     * Perform virus scanning on Word document content.
     */
    private VirusScanResponse performVirusScanning(byte[] wordContent, String fileName, String userId, 
                                                 String sessionId, VirusScannerType scannerType) throws IOException {
<span class="nc" id="L192">        logger.info(&quot;Performing virus scan on Word document: {} (session: {})&quot;, fileName, sessionId);</span>

<span class="nc" id="L194">        VirusScanResponse scanResult = virusScanningService.scanContent(wordContent, fileName, scannerType, userId);</span>

<span class="nc bnc" id="L196" title="All 2 branches missed.">        if (scanResult.getResult() == VirusScanResult.INFECTED) {</span>
<span class="nc" id="L197">            logger.warn(&quot;Virus detected in Word document: {} (session: {})&quot;, fileName, sessionId);</span>
<span class="nc" id="L198">            throw new WordConversionException(</span>
<span class="nc" id="L199">                String.format(&quot;File failed virus scan. Detected threats: %s&quot;, </span>
<span class="nc" id="L200">                            String.join(&quot;, &quot;, scanResult.getDetectedThreats())), </span>
                sessionId, fileName);
        }

<span class="nc bnc" id="L204" title="All 2 branches missed.">        if (scanResult.getResult() == VirusScanResult.ERROR) {</span>
<span class="nc" id="L205">            logger.warn(&quot;Virus scan error for Word document: {} (session: {})&quot;, fileName, sessionId);</span>
<span class="nc" id="L206">            throw new WordConversionException(&quot;Virus scan failed: &quot; + scanResult.getErrorMessage(), sessionId, fileName);</span>
        }

<span class="nc" id="L209">        logger.info(&quot;Virus scan completed successfully for Word document: {} (session: {})&quot;, fileName, sessionId);</span>
<span class="nc" id="L210">        return scanResult;</span>
    }

    /**
     * Convert Word document content to PDF.
     */
    private byte[] convertWordContentToPdf(byte[] wordContent, String fileName, String sessionId) throws IOException, DocumentException {
<span class="nc" id="L217">        logger.info(&quot;Converting Word content to PDF (session: {})&quot;, sessionId);</span>

        // Try Pandoc conversion first if available
<span class="nc bnc" id="L220" title="All 2 branches missed.">        if (pandocConversionService.isPandocAvailable()) {</span>
            try {
<span class="nc" id="L222">                logger.info(&quot;Attempting Word to PDF conversion using Pandoc (session: {})&quot;, sessionId);</span>
<span class="nc" id="L223">                return pandocConversionService.convertDocument(wordContent, fileName, &quot;docx&quot;, &quot;pdf&quot;, sessionId);</span>
<span class="nc" id="L224">            } catch (Exception e) {</span>
<span class="nc" id="L225">                logger.warn(&quot;Pandoc conversion failed, falling back to legacy method (session: {}): {}&quot;, sessionId, e.getMessage());</span>
                // Continue to fallback method below
<span class="nc" id="L227">            }</span>
        } else {
<span class="nc" id="L229">            logger.info(&quot;Pandoc not available, using legacy Word to PDF conversion (session: {})&quot;, sessionId);</span>
        }

        // Fallback to legacy conversion method
<span class="nc" id="L233">        return convertWordContentToPdfLegacy(wordContent, fileName, sessionId);</span>
    }

    /**
     * Convert Word document content to PDF using legacy method.
     */
    private byte[] convertWordContentToPdfLegacy(byte[] wordContent, String fileName, String sessionId) throws IOException, DocumentException {
<span class="nc" id="L240">        logger.info(&quot;Converting Word content to PDF using legacy method (session: {})&quot;, sessionId);</span>

        // Extract text from Word document
        String extractedText;
<span class="nc" id="L244">        try (XWPFDocument wordDocument = new XWPFDocument(new java.io.ByteArrayInputStream(wordContent))) {</span>
<span class="nc" id="L245">            StringBuilder textBuilder = new StringBuilder();</span>

            // Extract text from all paragraphs
<span class="nc" id="L248">            List&lt;XWPFParagraph&gt; paragraphs = wordDocument.getParagraphs();</span>
<span class="nc bnc" id="L249" title="All 2 branches missed.">            for (XWPFParagraph paragraph : paragraphs) {</span>
<span class="nc" id="L250">                String paragraphText = paragraph.getText();</span>
<span class="nc bnc" id="L251" title="All 4 branches missed.">                if (paragraphText != null &amp;&amp; !paragraphText.trim().isEmpty()) {</span>
<span class="nc" id="L252">                    textBuilder.append(paragraphText.trim()).append(&quot;\n&quot;);</span>
                }
<span class="nc" id="L254">            }</span>

            // If no text found from paragraphs, try extracting from runs
<span class="nc bnc" id="L257" title="All 2 branches missed.">            if (textBuilder.length() == 0) {</span>
<span class="nc bnc" id="L258" title="All 2 branches missed.">                for (XWPFParagraph paragraph : paragraphs) {</span>
<span class="nc" id="L259">                    List&lt;XWPFRun&gt; runs = paragraph.getRuns();</span>
<span class="nc bnc" id="L260" title="All 2 branches missed.">                    for (XWPFRun run : runs) {</span>
<span class="nc" id="L261">                        String text = run.getText(0);</span>
<span class="nc bnc" id="L262" title="All 4 branches missed.">                        if (text != null &amp;&amp; !text.trim().isEmpty()) {</span>
<span class="nc" id="L263">                            textBuilder.append(text);</span>
                        }
<span class="nc" id="L265">                    }</span>
<span class="nc" id="L266">                    textBuilder.append(&quot;\n&quot;);</span>
<span class="nc" id="L267">                }</span>
            }

<span class="nc" id="L270">            extractedText = textBuilder.toString().trim();</span>
<span class="nc" id="L271">            logger.debug(&quot;Extracted {} characters from Word document (session: {})&quot;, extractedText.length(), sessionId);</span>
        }

        // Create PDF document with proper resource management
<span class="nc" id="L275">        ByteArrayOutputStream outputStream = new ByteArrayOutputStream();</span>
<span class="nc" id="L276">        Document pdfDocument = null;</span>
<span class="nc" id="L277">        PdfWriter pdfWriter = null;</span>

        try {
<span class="nc" id="L280">            pdfDocument = new Document();</span>
<span class="nc" id="L281">            pdfWriter = PdfWriter.getInstance(pdfDocument, outputStream);</span>
<span class="nc" id="L282">            pdfDocument.open();</span>

            // Add title
<span class="nc" id="L285">            pdfDocument.add(new Paragraph(&quot;Converted from: &quot; + fileName));</span>
<span class="nc" id="L286">            pdfDocument.add(new Paragraph(&quot; &quot;)); // Empty line</span>

            // Add extracted text to PDF document
<span class="nc" id="L289">            addTextToPdfDocument(pdfDocument, extractedText);</span>

            // Ensure document is properly closed before returning bytes
<span class="nc" id="L292">            pdfDocument.close();</span>
<span class="nc" id="L293">            pdfWriter.close();</span>

            // Get the PDF bytes after closing
<span class="nc" id="L296">            byte[] pdfBytes = outputStream.toByteArray();</span>

<span class="nc" id="L298">            logger.info(&quot;PDF generation completed, size: {} bytes (session: {})&quot;, pdfBytes.length, sessionId);</span>
<span class="nc" id="L299">            return pdfBytes;</span>

        } finally {
            // Ensure resources are cleaned up
<span class="nc bnc" id="L303" title="All 4 branches missed.">            if (pdfDocument != null &amp;&amp; pdfDocument.isOpen()) {</span>
                try {
<span class="nc" id="L305">                    pdfDocument.close();</span>
<span class="nc" id="L306">                } catch (Exception e) {</span>
<span class="nc" id="L307">                    logger.warn(&quot;Error closing PDF document (session: {}): {}&quot;, sessionId, e.getMessage());</span>
<span class="nc" id="L308">                }</span>
            }
<span class="nc bnc" id="L310" title="All 2 branches missed.">            if (pdfWriter != null) {</span>
                try {
<span class="nc" id="L312">                    pdfWriter.close();</span>
<span class="nc" id="L313">                } catch (Exception e) {</span>
<span class="nc" id="L314">                    logger.warn(&quot;Error closing PDF writer (session: {}): {}&quot;, sessionId, e.getMessage());</span>
<span class="nc" id="L315">                }</span>
            }
            try {
<span class="nc" id="L318">                outputStream.close();</span>
<span class="nc" id="L319">            } catch (Exception e) {</span>
<span class="nc" id="L320">                logger.warn(&quot;Error closing output stream (session: {}): {}&quot;, sessionId, e.getMessage());</span>
<span class="nc" id="L321">            }</span>
        }
    }

    /**
     * Add extracted text to PDF document with proper formatting.
     */
    private void addTextToPdfDocument(Document document, String text) throws DocumentException {
<span class="nc bnc" id="L329" title="All 4 branches missed.">        if (text == null || text.trim().isEmpty()) {</span>
<span class="nc" id="L330">            document.add(new Paragraph(&quot;No text content found in the Word document.&quot;));</span>
<span class="nc" id="L331">            return;</span>
        }

        // Split text into paragraphs and add to document
<span class="nc" id="L335">        String[] paragraphs = text.split(&quot;\n\n&quot;);</span>
<span class="nc" id="L336">        boolean hasContent = false;</span>

<span class="nc bnc" id="L338" title="All 2 branches missed.">        for (String paragraphText : paragraphs) {</span>
<span class="nc" id="L339">            String trimmedText = paragraphText.trim();</span>
<span class="nc bnc" id="L340" title="All 2 branches missed.">            if (!trimmedText.isEmpty()) {</span>
<span class="nc" id="L341">                document.add(new Paragraph(trimmedText));</span>
<span class="nc" id="L342">                hasContent = true;</span>
            }
        }

        // If no paragraphs were found, split by single newlines
<span class="nc bnc" id="L347" title="All 2 branches missed.">        if (!hasContent) {</span>
<span class="nc" id="L348">            String[] lines = text.split(&quot;\n&quot;);</span>
<span class="nc bnc" id="L349" title="All 2 branches missed.">            for (String line : lines) {</span>
<span class="nc" id="L350">                String trimmedLine = line.trim();</span>
<span class="nc bnc" id="L351" title="All 2 branches missed.">                if (!trimmedLine.isEmpty()) {</span>
<span class="nc" id="L352">                    document.add(new Paragraph(trimmedLine));</span>
<span class="nc" id="L353">                    hasContent = true;</span>
                }
            }
        }

        // If still no content, add the raw text
<span class="nc bnc" id="L359" title="All 2 branches missed.">        if (!hasContent) {</span>
<span class="nc" id="L360">            document.add(new Paragraph(text.trim()));</span>
        }
<span class="nc" id="L362">    }</span>

    /**
     * Save converted PDF content to download directory.
     */
    private Path saveToDownloadDirectory(byte[] pdfContent, String fileName, String sessionId) throws IOException {
<span class="nc" id="L368">        Path downloadPath = tempDirectoryUtil.createTempFileInDownloadDir(fileName, sessionId);</span>
<span class="nc" id="L369">        Files.write(downloadPath, pdfContent);</span>

<span class="nc" id="L371">        logger.info(&quot;Saved converted PDF document to: {} (session: {})&quot;, downloadPath, sessionId);</span>
<span class="nc" id="L372">        return downloadPath;</span>
    }

    /**
     * Generate PDF filename from Word filename.
     */
    private String generatePdfFileName(String wordFileName) {
<span class="nc bnc" id="L379" title="All 2 branches missed.">        if (wordFileName == null) {</span>
<span class="nc" id="L380">            return &quot;converted_document.pdf&quot;;</span>
        }

<span class="nc bnc" id="L383" title="All 2 branches missed.">        String baseName = wordFileName.toLowerCase().endsWith(&quot;.docx&quot;)</span>
<span class="nc" id="L384">            ? wordFileName.substring(0, wordFileName.length() - 5)</span>
<span class="nc bnc" id="L385" title="All 2 branches missed.">            : wordFileName.toLowerCase().endsWith(&quot;.doc&quot;)</span>
<span class="nc" id="L386">            ? wordFileName.substring(0, wordFileName.length() - 4)</span>
<span class="nc" id="L387">            : wordFileName;</span>

<span class="nc" id="L389">        return baseName + &quot;_converted.pdf&quot;;</span>
    }

    /**
     * Validate multipart file input.
     */
    private void validateMultipartFile(MultipartFile file) {
<span class="nc bnc" id="L396" title="All 2 branches missed.">        if (file == null) {</span>
<span class="nc" id="L397">            throw new DmsBusinessException(&quot;File is required for conversion&quot;);</span>
        }

<span class="nc bnc" id="L400" title="All 2 branches missed.">        if (file.isEmpty()) {</span>
<span class="nc" id="L401">            throw new DmsBusinessException(&quot;File is required for conversion&quot;);</span>
        }

<span class="nc bnc" id="L404" title="All 2 branches missed.">        if (file.getSize() &gt; wordConversionConfig.getMaxFileSize()) {</span>
<span class="nc" id="L405">            throw new DmsBusinessException(</span>
<span class="nc" id="L406">                String.format(&quot;File size exceeds maximum allowed size of %s&quot;,</span>
<span class="nc" id="L407">                            wordConversionConfig.getMaxFileSizeFormatted()));</span>
        }

<span class="nc" id="L410">        String fileName = file.getOriginalFilename();</span>
<span class="nc bnc" id="L411" title="All 6 branches missed.">        if (fileName == null || (!fileName.toLowerCase().endsWith(&quot;.docx&quot;) &amp;&amp; !fileName.toLowerCase().endsWith(&quot;.doc&quot;))) {</span>
<span class="nc" id="L412">            throw new DmsBusinessException(&quot;Only Word documents (.doc, .docx) are supported for conversion&quot;);</span>
        }
<span class="nc" id="L414">    }</span>

    /**
     * Validate file path input.
     */
    private void validateFilePath(String filePath) {
<span class="nc bnc" id="L420" title="All 4 branches missed.">        if (filePath == null || filePath.trim().isEmpty()) {</span>
<span class="nc" id="L421">            throw new DmsBusinessException(&quot;File path is required for conversion&quot;);</span>
        }

<span class="nc" id="L424">        Path path = Paths.get(filePath);</span>
<span class="nc bnc" id="L425" title="All 2 branches missed.">        if (!Files.exists(path)) {</span>
<span class="nc" id="L426">            throw new DmsBusinessException(&quot;File does not exist: &quot; + filePath);</span>
        }

<span class="nc bnc" id="L429" title="All 2 branches missed.">        if (!Files.isReadable(path)) {</span>
<span class="nc" id="L430">            throw new DmsBusinessException(&quot;File is not readable: &quot; + filePath);</span>
        }

<span class="nc bnc" id="L433" title="All 4 branches missed.">        if (!filePath.toLowerCase().endsWith(&quot;.docx&quot;) &amp;&amp; !filePath.toLowerCase().endsWith(&quot;.doc&quot;)) {</span>
<span class="nc" id="L434">            throw new DmsBusinessException(&quot;Only Word documents (.doc, .docx) are supported for conversion&quot;);</span>
        }

        try {
<span class="nc" id="L438">            long fileSize = Files.size(path);</span>
<span class="nc bnc" id="L439" title="All 2 branches missed.">            if (fileSize &gt; wordConversionConfig.getMaxFileSize()) {</span>
<span class="nc" id="L440">                throw new DmsBusinessException(</span>
<span class="nc" id="L441">                    String.format(&quot;File size exceeds maximum allowed size of %s&quot;,</span>
<span class="nc" id="L442">                                wordConversionConfig.getMaxFileSizeFormatted()));</span>
            }
<span class="nc" id="L444">        } catch (IOException e) {</span>
<span class="nc" id="L445">            throw new DmsBusinessException(&quot;Unable to determine file size: &quot; + filePath);</span>
<span class="nc" id="L446">        }</span>
<span class="nc" id="L447">    }</span>
}
</pre><div class="footer"><span class="right">Created with <a href="http://www.jacoco.org/jacoco">JaCoCo</a> 0.8.10.202304240956</span></div></body></html>