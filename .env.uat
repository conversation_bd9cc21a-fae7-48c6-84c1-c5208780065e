# UAT Environment Variables for DMS Service
# This file contains UAT-specific environment variable values

# Server Configuration
SERVER_PORT=9093

# Database Configuration
DB_URL=******************************************************************************************************************************************
DB_USERNAME=dms_uat_user
DB_PASSWORD=${UAT_DB_PASSWORD}

# Redis Configuration
REDIS_HOST=uat-redis-server
REDIS_PORT=6379
REDIS_PASSWORD=${UAT_REDIS_PASSWORD}
REDIS_DATABASE=0

# JWT Configuration
JWT_SECRET=${UAT_JWT_SECRET}
JWT_EXPIRATION=86400000

# GraphQL Configuration
GRAPHIQL_ENABLED=false
GRAPHQL_SCHEMA_PRINTER_ENABLED=false

# CORS Configuration
CORS_ALLOWED_ORIGINS=${UAT_CORS_ALLOWED_ORIGINS}
CORS_ALLOWED_METHODS=GET,POST,PUT,DELETE,OPTIONS
CORS_ALLOWED_HEADERS=Content-Type,Authorization,X-Correlation-ID,Accept
CORS_ALLOW_CREDENTIALS=true

# JPA Configuration
JPA_DDL_AUTO=validate
JPA_SHOW_SQL=false
HIBERNATE_FORMAT_SQL=false

# Logging Configuration
LOG_LEVEL_DMS=INFO
LOG_LEVEL_SECURITY=WARN

# Cache Configuration
CACHE_TYPE=redis
CACHE_TTL=1800000
CACHE_KEY_PREFIX=dms:uat:

# Storage Configuration
STORAGE_PROVIDER=${UAT_STORAGE_PROVIDER:LOCAL}
STORAGE_PATH=/app/storage

# S3 Configuration (if using S3)
S3_BUCKET_NAME=${UAT_S3_BUCKET_NAME}
S3_REGION=${UAT_S3_REGION:us-east-1}
S3_ACCESS_KEY=${UAT_S3_ACCESS_KEY}
S3_SECRET_KEY=${UAT_S3_SECRET_KEY}

# Elasticsearch Configuration
ELASTICSEARCH_ENABLED=${UAT_ELASTICSEARCH_ENABLED:true}
ELASTICSEARCH_HOST=uat-elasticsearch-server
ELASTICSEARCH_PORT=9200

# Virus Scanning Configuration
VIRUS_SCANNING_ENABLED=true
VIRUS_SCANNING_DEFAULT_SCANNER=CLAMAV

# Rate Limiting Configuration
RATE_LIMIT_ENABLED=true

# OpenTelemetry Configuration
OTEL_SERVICE_NAME=dms-svc-uat
OTEL_SERVICE_VERSION=1.0.0-uat
OTEL_TRACES_SAMPLER_ARG=0.1

# Application Base URL
DMS_BASE_URL=${UAT_DMS_BASE_URL:https://dms-uat.company.com}

# Environment Identifier
ENVIRONMENT=uat

# Monitoring Configuration
ZIPKIN_ENDPOINT=${UAT_ZIPKIN_ENDPOINT:http://uat-zipkin:9411/api/v2/spans}
PROMETHEUS_ENABLED=true

# Security Configuration
SECURITY_HEADERS_CSP_ENABLED=true
SECURITY_HEADERS_HSTS_ENABLED=true