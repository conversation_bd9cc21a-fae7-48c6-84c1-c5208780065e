--liquibase formatted sql

--changeset dms:048-add-success-column-to-audit-logs
--comment: Add missing success column to audit_logs table

-- Add success column to audit_logs table
ALTER TABLE audit_logs 
ADD COLUMN success BOOLEAN NOT NULL DEFAULT TRUE;

-- Add index for performance
CREATE INDEX idx_audit_logs_success ON audit_logs(success);

-- Add comment for documentation
ALTER TABLE audit_logs 
MODIFY COLUMN success BOOLEAN NOT NULL DEFAULT TRUE COMMENT 'Indicates if the audited operation was successful';

-- Update existing records to have success = true (default behavior)
UPDATE audit_logs SET success = TRUE WHERE success IS NULL;