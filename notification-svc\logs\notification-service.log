2025-07-23 22:23:04.073 [tomcat-handler-8] WARN  [] [] [] [] c.a.n.service.SimpleJwtService - JWT validation is in testing mode - signature verification disabled
2025-07-23 22:23:04.076 [tomcat-handler-8] INFO  [test-upload-123] [d2a708f4-c4e3-4da6-9fe3-e58233d5feb7] [] [] c.a.n.config.LoggingConfig - Incoming request - Method: POST, URI: /graphql, RemoteAddr: 0:0:0:0:0:0:0:1, UserAgent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
2025-07-23 22:23:04.079 [tomcat-handler-8] DEBUG [test-upload-123] [d2a708f4-c4e3-4da6-9fe3-e58233d5feb7] [] [graphiql] org.hibernate.SQL - insert into notification_audit_logs (action,additional_data,correlation_id,details,entity_id,entity_type,request_id,result,source_service,timestamp,user_id) values (?,?,?,?,?,?,?,?,?,?,?)
2025-07-23 22:23:04.094 [tomcat-handler-8] INFO  [test-upload-123] [d2a708f4-c4e3-4da6-9fe3-e58233d5feb7] [] [graphiql] c.a.n.config.GraphQLAuditInterceptor - GraphQL operation started - Operation: mutation, Variables: {}, CorrelationId: test-upload-123
2025-07-23 22:23:04.096 [tomcat-handler-8] INFO  [test-upload-123] [d2a708f4-c4e3-4da6-9fe3-e58233d5feb7] [] [graphiql] c.a.n.config.GraphQLConfig - Extracted correlation ID from HTTP request: test-upload-123
2025-07-23 22:23:04.096 [tomcat-handler-8] INFO  [test-upload-123] [d2a708f4-c4e3-4da6-9fe3-e58233d5feb7] [] [graphiql] c.a.n.config.GraphQLConfig - GraphQL request intercepted - CorrelationId: test-upload-123, RequestId: d2841716-22f6-4ef5-ac2a-e8c601c8d185, SourceService: graphiql
2025-07-23 22:23:04.101 [tomcat-handler-8] INFO  [test-upload-123] [d2841716-22f6-4ef5-ac2a-e8c601c8d185] [] [graphiql] c.a.n.config.GraphQLConfig - Set MDC context - CorrelationId: test-upload-123, RequestId: d2841716-22f6-4ef5-ac2a-e8c601c8d185, SourceService: graphiql
2025-07-23 22:23:04.101 [notification-async-3] INFO  [] [] [] [] c.a.n.c.NotificationController - GraphQL mutation - sendNotification called with input: SendNotificationInput(sender=<EMAIL>, type=EMAIL, content=Please find the attached documents.-Anurag, templateName=welcome, messageType=null, importance=null, recipients=null, recipientDetails=[RecipientInput(email=<EMAIL>, type=TO, variables=[VariableInput(key=firstName, value=Deeksha), VariableInput(key=companyName, value=Ascent Business), VariableInput(key=role, value=Developer)], attachments=null), RecipientInput(email=<EMAIL>, type=TO, variables=[VariableInput(key=firstName, value=Anurag), VariableInput(key=companyName, value=Ascent Business), VariableInput(key=role, value=Developer)], attachments=null)], variables=null, commonAttachments=null)
2025-07-23 22:23:04.101 [tomcat-handler-8] INFO  [test-upload-123] [d2841716-22f6-4ef5-ac2a-e8c601c8d185] [] [graphiql] c.a.n.config.LoggingConfig - Outgoing response - Status: 200, Duration: 25ms, ContentType: null
2025-07-23 22:23:04.102 [notification-async-3] INFO  [] [] [] [] c.a.n.c.NotificationController - Captured correlation ID: null, request ID: null for audit logging
2025-07-23 22:23:04.106 [notification-async-3] INFO  [] [] [<EMAIL>] [] c.a.n.service.NotificationService - Processing sendNotification - Sender: <EMAIL>, Type: EMAIL, Content: Please find the attached documents.-Anurag
2025-07-23 22:23:04.106 [notification-async-3] INFO  [] [] [<EMAIL>] [] c.a.n.service.NotificationService - Using new recipient format with 2 detailed recipients
2025-07-23 22:23:04.107 [notification-async-3] INFO  [] [] [<EMAIL>] [] c.a.n.service.NotificationService - Processing sender: <EMAIL>
2025-07-23 22:23:04.108 [notification-async-3] DEBUG [] [] [<EMAIL>] [] org.hibernate.SQL - insert into notifications (common_attachments,content,created_date,importance,last_modified_date,message_type,sender_id,template_name,type,variables) values (?,?,?,?,?,?,?,?,?,?)
2025-07-23 22:23:04.113 [notification-async-3] INFO  [] [] [<EMAIL>] [] c.a.n.service.NotificationService - Created notification with ID: 131
2025-07-23 22:23:04.113 [notification-async-3] INFO  [] [] [<EMAIL>] [] c.a.n.service.NotificationService - Sending email with enhanced features (attachments/CC/BCC) for notification ID: 131
2025-07-23 22:23:04.114 [notification-async-3] INFO  [] [] [<EMAIL>] [] c.a.n.service.NotificationService - No recipient-specific attachments, sending single email for CC/BCC visibility
2025-07-23 22:23:04.114 [notification-async-3] INFO  [] [] [<EMAIL>] [] c.a.n.service.NotificationService - Sending single email to 2 recipients
2025-07-23 22:23:04.114 [notification-async-3] INFO  [] [] [<EMAIL>] [] c.a.n.service.EmailService - Sending email with proper CC/BCC visibility using template welcome
2025-07-23 22:23:04.115 [notification-async-3] INFO  [] [] [<EMAIL>] [] c.a.n.service.TemplateService - Synchronizing template: welcome
2025-07-23 22:23:04.117 [notification-async-3] DEBUG [] [] [<EMAIL>] [] org.hibernate.SQL - select nt1_0.id,nt1_0.alias,nt1_0.body,nt1_0.created_date,nt1_0.last_modified_date,nt1_0.subject from notification_templates nt1_0 where nt1_0.alias=?
2025-07-23 22:23:04.121 [notification-async-3] INFO  [] [] [<EMAIL>] [] c.a.n.service.TemplateService - Template 'welcome' content is consistent between classpath and database
2025-07-23 22:23:04.121 [notification-async-3] INFO  [] [] [<EMAIL>] [] c.a.n.service.EmailService - Using synchronized email template: welcome
2025-07-23 22:23:04.122 [notification-async-3] INFO  [] [] [<EMAIL>] [] c.a.n.service.EmailService - Template processed successfully with merged variables
2025-07-23 22:23:04.125 [notification-async-3] INFO  [] [] [<EMAIL>] [] c.a.n.service.EmailService - Added 2 TO recipients: <EMAIL>, <EMAIL>
2025-07-23 22:23:04.125 [notification-async-3] INFO  [] [] [<EMAIL>] [] c.a.n.service.EmailService - Sending <NAME_EMAIL> to 2 total recipients (TO: 2, CC: 0, BCC: 0)
2025-07-23 22:23:04.125 [notification-async-3] ERROR [] [] [<EMAIL>] [] c.a.n.service.EmailService - Failed to send email with proper CC/BCC visibility: Authentication failed
org.springframework.mail.MailAuthenticationException: Authentication failed
	at org.springframework.mail.javamail.JavaMailSenderImpl.doSend(JavaMailSenderImpl.java:402)
	at org.springframework.mail.javamail.JavaMailSenderImpl.send(JavaMailSenderImpl.java:350)
	at org.springframework.mail.javamail.JavaMailSender.send(JavaMailSender.java:101)
	at com.ascentbusiness.notification_svc.service.EmailService.sendEmailWithTemplateAndAttachments(EmailService.java:181)
	at com.ascentbusiness.notification_svc.service.NotificationService.sendSingleEmailToMultipleRecipients(NotificationService.java:260)
	at com.ascentbusiness.notification_svc.service.NotificationService.sendEmailWithEnhancedFeatures(NotificationService.java:177)
	at com.ascentbusiness.notification_svc.service.NotificationService.sendNotification(NotificationService.java:146)
	at java.base/jdk.internal.reflect.DirectMethodHandleAccessor.invoke(DirectMethodHandleAccessor.java:103)
	at java.base/java.lang.reflect.Method.invoke(Method.java:580)
	at org.springframework.aop.support.AopUtils.invokeJoinpointUsingReflection(AopUtils.java:359)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.invokeJoinpoint(ReflectiveMethodInvocation.java:196)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:163)
	at org.springframework.transaction.interceptor.TransactionAspectSupport.invokeWithinTransaction(TransactionAspectSupport.java:380)
	at org.springframework.transaction.interceptor.TransactionInterceptor.invoke(TransactionInterceptor.java:119)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:184)
	at org.springframework.aop.framework.CglibAopProxy$DynamicAdvisedInterceptor.intercept(CglibAopProxy.java:728)
	at com.ascentbusiness.notification_svc.service.NotificationService$$SpringCGLIB$$0.sendNotification(<generated>)
	at com.ascentbusiness.notification_svc.controller.NotificationController.sendNotification(NotificationController.java:53)
	at java.base/jdk.internal.reflect.DirectMethodHandleAccessor.invoke(DirectMethodHandleAccessor.java:103)
	at java.base/java.lang.reflect.Method.invoke(Method.java:580)
	at org.springframework.graphql.data.method.InvocableHandlerMethodSupport.lambda$doInvoke$0(InvocableHandlerMethodSupport.java:113)
	at io.micrometer.context.ContextSnapshot.lambda$wrap$1(ContextSnapshot.java:106)
	at org.springframework.graphql.data.method.InvocableHandlerMethodSupport.lambda$adaptCallable$1(InvocableHandlerMethodSupport.java:157)
	at java.base/java.lang.VirtualThread.run(VirtualThread.java:329)
Caused by: jakarta.mail.AuthenticationFailedException: failed to connect, no password specified?
	at jakarta.mail.Service.connect(Service.java:379)
	at org.springframework.mail.javamail.JavaMailSenderImpl.connectTransport(JavaMailSenderImpl.java:480)
	at org.springframework.mail.javamail.JavaMailSenderImpl.doSend(JavaMailSenderImpl.java:399)
	... 23 common frames omitted
2025-07-23 22:23:04.129 [notification-async-3] ERROR [] [] [<EMAIL>] [] c.a.n.service.NotificationService - Failed to send single email to multiple recipients: Authentication failed
org.springframework.mail.MailAuthenticationException: Authentication failed
	at org.springframework.mail.javamail.JavaMailSenderImpl.doSend(JavaMailSenderImpl.java:402)
	at org.springframework.mail.javamail.JavaMailSenderImpl.send(JavaMailSenderImpl.java:350)
	at org.springframework.mail.javamail.JavaMailSender.send(JavaMailSender.java:101)
	at com.ascentbusiness.notification_svc.service.EmailService.sendEmailWithTemplateAndAttachments(EmailService.java:181)
	at com.ascentbusiness.notification_svc.service.NotificationService.sendSingleEmailToMultipleRecipients(NotificationService.java:260)
	at com.ascentbusiness.notification_svc.service.NotificationService.sendEmailWithEnhancedFeatures(NotificationService.java:177)
	at com.ascentbusiness.notification_svc.service.NotificationService.sendNotification(NotificationService.java:146)
	at java.base/jdk.internal.reflect.DirectMethodHandleAccessor.invoke(DirectMethodHandleAccessor.java:103)
	at java.base/java.lang.reflect.Method.invoke(Method.java:580)
	at org.springframework.aop.support.AopUtils.invokeJoinpointUsingReflection(AopUtils.java:359)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.invokeJoinpoint(ReflectiveMethodInvocation.java:196)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:163)
	at org.springframework.transaction.interceptor.TransactionAspectSupport.invokeWithinTransaction(TransactionAspectSupport.java:380)
	at org.springframework.transaction.interceptor.TransactionInterceptor.invoke(TransactionInterceptor.java:119)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:184)
	at org.springframework.aop.framework.CglibAopProxy$DynamicAdvisedInterceptor.intercept(CglibAopProxy.java:728)
	at com.ascentbusiness.notification_svc.service.NotificationService$$SpringCGLIB$$0.sendNotification(<generated>)
	at com.ascentbusiness.notification_svc.controller.NotificationController.sendNotification(NotificationController.java:53)
	at java.base/jdk.internal.reflect.DirectMethodHandleAccessor.invoke(DirectMethodHandleAccessor.java:103)
	at java.base/java.lang.reflect.Method.invoke(Method.java:580)
	at org.springframework.graphql.data.method.InvocableHandlerMethodSupport.lambda$doInvoke$0(InvocableHandlerMethodSupport.java:113)
	at io.micrometer.context.ContextSnapshot.lambda$wrap$1(ContextSnapshot.java:106)
	at org.springframework.graphql.data.method.InvocableHandlerMethodSupport.lambda$adaptCallable$1(InvocableHandlerMethodSupport.java:157)
	at java.base/java.lang.VirtualThread.run(VirtualThread.java:329)
Caused by: jakarta.mail.AuthenticationFailedException: failed to connect, no password specified?
	at jakarta.mail.Service.connect(Service.java:379)
	at org.springframework.mail.javamail.JavaMailSenderImpl.connectTransport(JavaMailSenderImpl.java:480)
	at org.springframework.mail.javamail.JavaMailSenderImpl.doSend(JavaMailSenderImpl.java:399)
	... 23 common frames omitted
2025-07-23 22:23:04.144 [notification-async-3] DEBUG [] [] [<EMAIL>] [] org.hibernate.SQL - insert into notification_audit_logs (action,additional_data,correlation_id,details,entity_id,entity_type,request_id,result,source_service,timestamp,user_id) values (?,?,?,?,?,?,?,?,?,?,?)
2025-07-23 22:23:04.150 [notification-async-3] DEBUG [] [] [<EMAIL>] [] org.hibernate.SQL - insert into notification_audit_logs (action,additional_data,correlation_id,details,entity_id,entity_type,request_id,result,source_service,timestamp,user_id) values (?,?,?,?,?,?,?,?,?,?,?)
2025-07-23 22:23:04.154 [notification-async-3] ERROR [] [] [<EMAIL>] [] c.a.n.service.NotificationService - Error sending enhanced email notification: Authentication failed
org.springframework.mail.MailAuthenticationException: Authentication failed
	at org.springframework.mail.javamail.JavaMailSenderImpl.doSend(JavaMailSenderImpl.java:402)
	at org.springframework.mail.javamail.JavaMailSenderImpl.send(JavaMailSenderImpl.java:350)
	at org.springframework.mail.javamail.JavaMailSender.send(JavaMailSender.java:101)
	at com.ascentbusiness.notification_svc.service.EmailService.sendEmailWithTemplateAndAttachments(EmailService.java:181)
	at com.ascentbusiness.notification_svc.service.NotificationService.sendSingleEmailToMultipleRecipients(NotificationService.java:260)
	at com.ascentbusiness.notification_svc.service.NotificationService.sendEmailWithEnhancedFeatures(NotificationService.java:177)
	at com.ascentbusiness.notification_svc.service.NotificationService.sendNotification(NotificationService.java:146)
	at java.base/jdk.internal.reflect.DirectMethodHandleAccessor.invoke(DirectMethodHandleAccessor.java:103)
	at java.base/java.lang.reflect.Method.invoke(Method.java:580)
	at org.springframework.aop.support.AopUtils.invokeJoinpointUsingReflection(AopUtils.java:359)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.invokeJoinpoint(ReflectiveMethodInvocation.java:196)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:163)
	at org.springframework.transaction.interceptor.TransactionAspectSupport.invokeWithinTransaction(TransactionAspectSupport.java:380)
	at org.springframework.transaction.interceptor.TransactionInterceptor.invoke(TransactionInterceptor.java:119)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:184)
	at org.springframework.aop.framework.CglibAopProxy$DynamicAdvisedInterceptor.intercept(CglibAopProxy.java:728)
	at com.ascentbusiness.notification_svc.service.NotificationService$$SpringCGLIB$$0.sendNotification(<generated>)
	at com.ascentbusiness.notification_svc.controller.NotificationController.sendNotification(NotificationController.java:53)
	at java.base/jdk.internal.reflect.DirectMethodHandleAccessor.invoke(DirectMethodHandleAccessor.java:103)
	at java.base/java.lang.reflect.Method.invoke(Method.java:580)
	at org.springframework.graphql.data.method.InvocableHandlerMethodSupport.lambda$doInvoke$0(InvocableHandlerMethodSupport.java:113)
	at io.micrometer.context.ContextSnapshot.lambda$wrap$1(ContextSnapshot.java:106)
	at org.springframework.graphql.data.method.InvocableHandlerMethodSupport.lambda$adaptCallable$1(InvocableHandlerMethodSupport.java:157)
	at java.base/java.lang.VirtualThread.run(VirtualThread.java:329)
Caused by: jakarta.mail.AuthenticationFailedException: failed to connect, no password specified?
	at jakarta.mail.Service.connect(Service.java:379)
	at org.springframework.mail.javamail.JavaMailSenderImpl.connectTransport(JavaMailSenderImpl.java:480)
	at org.springframework.mail.javamail.JavaMailSenderImpl.doSend(JavaMailSenderImpl.java:399)
	... 23 common frames omitted
2025-07-23 22:23:04.157 [notification-async-3] ERROR [] [] [<EMAIL>] [] c.a.n.service.NotificationService - Error sending notification: Failed to send enhanced email notification: Authentication failed
java.lang.RuntimeException: Failed to send enhanced email notification: Authentication failed
	at com.ascentbusiness.notification_svc.service.NotificationService.sendEmailWithEnhancedFeatures(NotificationService.java:186)
	at com.ascentbusiness.notification_svc.service.NotificationService.sendNotification(NotificationService.java:146)
	at java.base/jdk.internal.reflect.DirectMethodHandleAccessor.invoke(DirectMethodHandleAccessor.java:103)
	at java.base/java.lang.reflect.Method.invoke(Method.java:580)
	at org.springframework.aop.support.AopUtils.invokeJoinpointUsingReflection(AopUtils.java:359)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.invokeJoinpoint(ReflectiveMethodInvocation.java:196)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:163)
	at org.springframework.transaction.interceptor.TransactionAspectSupport.invokeWithinTransaction(TransactionAspectSupport.java:380)
	at org.springframework.transaction.interceptor.TransactionInterceptor.invoke(TransactionInterceptor.java:119)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:184)
	at org.springframework.aop.framework.CglibAopProxy$DynamicAdvisedInterceptor.intercept(CglibAopProxy.java:728)
	at com.ascentbusiness.notification_svc.service.NotificationService$$SpringCGLIB$$0.sendNotification(<generated>)
	at com.ascentbusiness.notification_svc.controller.NotificationController.sendNotification(NotificationController.java:53)
	at java.base/jdk.internal.reflect.DirectMethodHandleAccessor.invoke(DirectMethodHandleAccessor.java:103)
	at java.base/java.lang.reflect.Method.invoke(Method.java:580)
	at org.springframework.graphql.data.method.InvocableHandlerMethodSupport.lambda$doInvoke$0(InvocableHandlerMethodSupport.java:113)
	at io.micrometer.context.ContextSnapshot.lambda$wrap$1(ContextSnapshot.java:106)
	at org.springframework.graphql.data.method.InvocableHandlerMethodSupport.lambda$adaptCallable$1(InvocableHandlerMethodSupport.java:157)
	at java.base/java.lang.VirtualThread.run(VirtualThread.java:329)
Caused by: org.springframework.mail.MailAuthenticationException: Authentication failed
	at org.springframework.mail.javamail.JavaMailSenderImpl.doSend(JavaMailSenderImpl.java:402)
	at org.springframework.mail.javamail.JavaMailSenderImpl.send(JavaMailSenderImpl.java:350)
	at org.springframework.mail.javamail.JavaMailSender.send(JavaMailSender.java:101)
	at com.ascentbusiness.notification_svc.service.EmailService.sendEmailWithTemplateAndAttachments(EmailService.java:181)
	at com.ascentbusiness.notification_svc.service.NotificationService.sendSingleEmailToMultipleRecipients(NotificationService.java:260)
	at com.ascentbusiness.notification_svc.service.NotificationService.sendEmailWithEnhancedFeatures(NotificationService.java:177)
	... 18 common frames omitted
Caused by: jakarta.mail.AuthenticationFailedException: failed to connect, no password specified?
	at jakarta.mail.Service.connect(Service.java:379)
	at org.springframework.mail.javamail.JavaMailSenderImpl.connectTransport(JavaMailSenderImpl.java:480)
	at org.springframework.mail.javamail.JavaMailSenderImpl.doSend(JavaMailSenderImpl.java:399)
	... 23 common frames omitted
2025-07-23 22:23:04.165 [notification-async-3] ERROR [] [] [<EMAIL>] [] c.a.n.c.NotificationController - Error in sendNotification mutation: Failed to send notification: Failed to send enhanced email notification: Authentication failed
java.lang.RuntimeException: Failed to send notification: Failed to send enhanced email notification: Authentication failed
	at com.ascentbusiness.notification_svc.service.NotificationService.sendNotification(NotificationService.java:154)
	at java.base/jdk.internal.reflect.DirectMethodHandleAccessor.invoke(DirectMethodHandleAccessor.java:103)
	at java.base/java.lang.reflect.Method.invoke(Method.java:580)
	at org.springframework.aop.support.AopUtils.invokeJoinpointUsingReflection(AopUtils.java:359)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.invokeJoinpoint(ReflectiveMethodInvocation.java:196)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:163)
	at org.springframework.transaction.interceptor.TransactionAspectSupport.invokeWithinTransaction(TransactionAspectSupport.java:380)
	at org.springframework.transaction.interceptor.TransactionInterceptor.invoke(TransactionInterceptor.java:119)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:184)
	at org.springframework.aop.framework.CglibAopProxy$DynamicAdvisedInterceptor.intercept(CglibAopProxy.java:728)
	at com.ascentbusiness.notification_svc.service.NotificationService$$SpringCGLIB$$0.sendNotification(<generated>)
	at com.ascentbusiness.notification_svc.controller.NotificationController.sendNotification(NotificationController.java:53)
	at java.base/jdk.internal.reflect.DirectMethodHandleAccessor.invoke(DirectMethodHandleAccessor.java:103)
	at java.base/java.lang.reflect.Method.invoke(Method.java:580)
	at org.springframework.graphql.data.method.InvocableHandlerMethodSupport.lambda$doInvoke$0(InvocableHandlerMethodSupport.java:113)
	at io.micrometer.context.ContextSnapshot.lambda$wrap$1(ContextSnapshot.java:106)
	at org.springframework.graphql.data.method.InvocableHandlerMethodSupport.lambda$adaptCallable$1(InvocableHandlerMethodSupport.java:157)
	at java.base/java.lang.VirtualThread.run(VirtualThread.java:329)
Caused by: java.lang.RuntimeException: Failed to send enhanced email notification: Authentication failed
	at com.ascentbusiness.notification_svc.service.NotificationService.sendEmailWithEnhancedFeatures(NotificationService.java:186)
	at com.ascentbusiness.notification_svc.service.NotificationService.sendNotification(NotificationService.java:146)
	... 17 common frames omitted
Caused by: org.springframework.mail.MailAuthenticationException: Authentication failed
	at org.springframework.mail.javamail.JavaMailSenderImpl.doSend(JavaMailSenderImpl.java:402)
	at org.springframework.mail.javamail.JavaMailSenderImpl.send(JavaMailSenderImpl.java:350)
	at org.springframework.mail.javamail.JavaMailSender.send(JavaMailSender.java:101)
	at com.ascentbusiness.notification_svc.service.EmailService.sendEmailWithTemplateAndAttachments(EmailService.java:181)
	at com.ascentbusiness.notification_svc.service.NotificationService.sendSingleEmailToMultipleRecipients(NotificationService.java:260)
	at com.ascentbusiness.notification_svc.service.NotificationService.sendEmailWithEnhancedFeatures(NotificationService.java:177)
	... 18 common frames omitted
Caused by: jakarta.mail.AuthenticationFailedException: failed to connect, no password specified?
	at jakarta.mail.Service.connect(Service.java:379)
	at org.springframework.mail.javamail.JavaMailSenderImpl.connectTransport(JavaMailSenderImpl.java:480)
	at org.springframework.mail.javamail.JavaMailSenderImpl.doSend(JavaMailSenderImpl.java:399)
	... 23 common frames omitted
2025-07-23 22:23:04.170 [notification-async-3] DEBUG [] [] [<EMAIL>] [] org.hibernate.SQL - insert into notification_audit_logs (action,additional_data,correlation_id,details,entity_id,entity_type,request_id,result,source_service,timestamp,user_id) values (?,?,?,?,?,?,?,?,?,?,?)
2025-07-23 22:23:04.179 [notification-async-3] ERROR [] [] [] [] o.s.g.e.ExceptionResolversExceptionHandler - Unresolved RuntimeException for executionId ba560a38-7c2a-2444-7fd7-cf1f785cc243
java.lang.RuntimeException: Failed to send notification: Failed to send enhanced email notification: Authentication failed
	at com.ascentbusiness.notification_svc.service.NotificationService.sendNotification(NotificationService.java:154)
	at java.base/jdk.internal.reflect.DirectMethodHandleAccessor.invoke(DirectMethodHandleAccessor.java:103)
	at java.base/java.lang.reflect.Method.invoke(Method.java:580)
	at org.springframework.aop.support.AopUtils.invokeJoinpointUsingReflection(AopUtils.java:359)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.invokeJoinpoint(ReflectiveMethodInvocation.java:196)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:163)
	at org.springframework.transaction.interceptor.TransactionAspectSupport.invokeWithinTransaction(TransactionAspectSupport.java:380)
	at org.springframework.transaction.interceptor.TransactionInterceptor.invoke(TransactionInterceptor.java:119)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:184)
	at org.springframework.aop.framework.CglibAopProxy$DynamicAdvisedInterceptor.intercept(CglibAopProxy.java:728)
	at com.ascentbusiness.notification_svc.service.NotificationService$$SpringCGLIB$$0.sendNotification(<generated>)
	at com.ascentbusiness.notification_svc.controller.NotificationController.sendNotification(NotificationController.java:53)
	at java.base/jdk.internal.reflect.DirectMethodHandleAccessor.invoke(DirectMethodHandleAccessor.java:103)
	at java.base/java.lang.reflect.Method.invoke(Method.java:580)
	at org.springframework.graphql.data.method.InvocableHandlerMethodSupport.lambda$doInvoke$0(InvocableHandlerMethodSupport.java:113)
	at io.micrometer.context.ContextSnapshot.lambda$wrap$1(ContextSnapshot.java:106)
	at org.springframework.graphql.data.method.InvocableHandlerMethodSupport.lambda$adaptCallable$1(InvocableHandlerMethodSupport.java:157)
	at java.base/java.lang.VirtualThread.run(VirtualThread.java:329)
Caused by: java.lang.RuntimeException: Failed to send enhanced email notification: Authentication failed
	at com.ascentbusiness.notification_svc.service.NotificationService.sendEmailWithEnhancedFeatures(NotificationService.java:186)
	at com.ascentbusiness.notification_svc.service.NotificationService.sendNotification(NotificationService.java:146)
	... 17 common frames omitted
Caused by: org.springframework.mail.MailAuthenticationException: Authentication failed
	at org.springframework.mail.javamail.JavaMailSenderImpl.doSend(JavaMailSenderImpl.java:402)
	at org.springframework.mail.javamail.JavaMailSenderImpl.send(JavaMailSenderImpl.java:350)
	at org.springframework.mail.javamail.JavaMailSender.send(JavaMailSender.java:101)
	at com.ascentbusiness.notification_svc.service.EmailService.sendEmailWithTemplateAndAttachments(EmailService.java:181)
	at com.ascentbusiness.notification_svc.service.NotificationService.sendSingleEmailToMultipleRecipients(NotificationService.java:260)
	at com.ascentbusiness.notification_svc.service.NotificationService.sendEmailWithEnhancedFeatures(NotificationService.java:177)
	... 18 common frames omitted
Caused by: jakarta.mail.AuthenticationFailedException: failed to connect, no password specified?
	at jakarta.mail.Service.connect(Service.java:379)
	at org.springframework.mail.javamail.JavaMailSenderImpl.connectTransport(JavaMailSenderImpl.java:480)
	at org.springframework.mail.javamail.JavaMailSenderImpl.doSend(JavaMailSenderImpl.java:399)
	... 23 common frames omitted
2025-07-23 22:23:04.182 [notification-async-3] INFO  [test-upload-123] [d2841716-22f6-4ef5-ac2a-e8c601c8d185] [] [graphiql] c.a.n.config.GraphQLConfig - GraphQL response processed - CorrelationId: test-upload-123, RequestId: d2841716-22f6-4ef5-ac2a-e8c601c8d185, SourceService: graphiql
2025-07-23 22:23:04.182 [notification-async-3] ERROR [test-upload-123] [d2841716-22f6-4ef5-ac2a-e8c601c8d185] [] [graphiql] c.a.n.config.GraphQLAuditInterceptor - GraphQL operation failed - Operation: mutation, Duration: 104ms, Errors: [INTERNAL_ERROR for ba560a38-7c2a-2444-7fd7-cf1f785cc243], CorrelationId: test-upload-123
2025-07-23 22:23:04.185 [notification-async-3] DEBUG [test-upload-123] [d2841716-22f6-4ef5-ac2a-e8c601c8d185] [] [graphiql] org.hibernate.SQL - insert into notification_audit_logs (action,additional_data,correlation_id,details,entity_id,entity_type,request_id,result,source_service,timestamp,user_id) values (?,?,?,?,?,?,?,?,?,?,?)
2025-07-23 22:24:37.309 [rabbit-simple-1] INFO  [] [] [] [] o.s.a.r.l.SimpleMessageListenerContainer - Waiting for workers to finish.
2025-07-23 22:24:37.715 [rabbit-simple-1] INFO  [] [] [] [] o.s.a.r.l.SimpleMessageListenerContainer - Successfully waited for workers to finish.
2025-07-23 22:24:37.716 [SpringApplicationShutdownHook] INFO  [] [] [] [] o.s.b.w.e.tomcat.GracefulShutdown - Commencing graceful shutdown. Waiting for active requests to complete
2025-07-23 22:24:37.723 [tomcat-shutdown] INFO  [] [] [] [] o.s.b.w.e.tomcat.GracefulShutdown - Graceful shutdown complete
2025-07-23 22:24:37.848 [SpringApplicationShutdownHook] INFO  [] [] [] [] o.s.o.j.LocalContainerEntityManagerFactoryBean - Closing JPA EntityManagerFactory for persistence unit 'default'
2025-07-23 22:24:37.851 [SpringApplicationShutdownHook] INFO  [] [] [] [] com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Shutdown initiated...
2025-07-23 22:24:37.857 [SpringApplicationShutdownHook] INFO  [] [] [] [] com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Shutdown completed.
2025-07-23 22:29:43.619 [restartedMain] INFO  [] [] [] [] c.a.n.NotificationSvcApplication - Starting NotificationSvcApplication using Java 21.0.7 with PID 5956 (D:\grc-platform-v4\notification-svc\target\classes started by AnuragVerma in D:\grc-platform-v4\notification-svc)
2025-07-23 22:29:43.621 [restartedMain] INFO  [] [] [] [] c.a.n.NotificationSvcApplication - No active profile set, falling back to 1 default profile: "default"
2025-07-23 22:29:43.672 [restartedMain] INFO  [] [] [] [] o.s.b.d.e.DevToolsPropertyDefaultsPostProcessor - Devtools property defaults active! Set 'spring.devtools.add-properties' to 'false' to disable
2025-07-23 22:29:43.673 [restartedMain] INFO  [] [] [] [] o.s.b.d.e.DevToolsPropertyDefaultsPostProcessor - For additional web related logging consider setting the 'logging.level.web' property to 'DEBUG'
2025-07-23 22:29:44.854 [restartedMain] INFO  [] [] [] [] o.s.d.r.c.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode
2025-07-23 22:29:44.856 [restartedMain] INFO  [] [] [] [] o.s.d.r.c.RepositoryConfigurationDelegate - Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-07-23 22:29:45.017 [restartedMain] INFO  [] [] [] [] o.s.d.r.c.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 152 ms. Found 4 JPA repository interfaces.
2025-07-23 22:29:45.031 [restartedMain] INFO  [] [] [] [] o.s.d.r.c.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode
2025-07-23 22:29:45.032 [restartedMain] INFO  [] [] [] [] o.s.d.r.c.RepositoryConfigurationDelegate - Bootstrapping Spring Data Redis repositories in DEFAULT mode.
2025-07-23 22:29:45.048 [restartedMain] INFO  [] [] [] [] o.s.d.r.c.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.ascentbusiness.notification_svc.repository.AuditLogRepository; If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository
2025-07-23 22:29:45.049 [restartedMain] INFO  [] [] [] [] o.s.d.r.c.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.ascentbusiness.notification_svc.repository.NotificationRecipientRepository; If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository
2025-07-23 22:29:45.050 [restartedMain] INFO  [] [] [] [] o.s.d.r.c.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.ascentbusiness.notification_svc.repository.NotificationRepository; If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository
2025-07-23 22:29:45.050 [restartedMain] INFO  [] [] [] [] o.s.d.r.c.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.ascentbusiness.notification_svc.repository.NotificationTemplateRepository; If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository
2025-07-23 22:29:45.051 [restartedMain] INFO  [] [] [] [] o.s.d.r.c.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 11 ms. Found 0 Redis repository interfaces.
2025-07-23 22:29:45.931 [restartedMain] INFO  [] [] [] [] o.s.b.w.e.tomcat.TomcatWebServer - Tomcat initialized with port 9091 (http)
2025-07-23 22:29:45.941 [restartedMain] INFO  [] [] [] [] o.a.coyote.http11.Http11NioProtocol - Initializing ProtocolHandler ["http-nio-9091"]
2025-07-23 22:29:45.943 [restartedMain] INFO  [] [] [] [] o.a.catalina.core.StandardService - Starting service [Tomcat]
2025-07-23 22:29:45.944 [restartedMain] INFO  [] [] [] [] o.a.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/10.1.41]
2025-07-23 22:29:45.994 [restartedMain] INFO  [] [] [] [] o.a.c.c.C.[Tomcat].[localhost].[/] - Initializing Spring embedded WebApplicationContext
2025-07-23 22:29:45.995 [restartedMain] INFO  [] [] [] [] o.s.b.w.s.c.ServletWebServerApplicationContext - Root WebApplicationContext: initialization completed in 2321 ms
2025-07-23 22:29:46.649 [restartedMain] INFO  [] [] [] [] o.s.o.j.p.SpringPersistenceUnitInfo - No LoadTimeWeaver setup: ignoring JPA class transformer
2025-07-23 22:29:46.677 [restartedMain] INFO  [] [] [] [] com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Starting...
2025-07-23 22:29:46.851 [restartedMain] INFO  [] [] [] [] com.zaxxer.hikari.pool.HikariPool - HikariPool-1 - Added connection com.mysql.cj.jdbc.ConnectionImpl@20780a21
2025-07-23 22:29:46.852 [restartedMain] INFO  [] [] [] [] com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Start completed.
2025-07-23 22:29:46.895 [restartedMain] WARN  [] [] [] [] org.hibernate.orm.deprecation - HHH90000025: MySQL8Dialect does not need to be specified explicitly using 'hibernate.dialect' (remove the property setting and it will be selected by default)
2025-07-23 22:29:46.896 [restartedMain] WARN  [] [] [] [] org.hibernate.orm.deprecation - HHH90000026: MySQL8Dialect has been deprecated; use org.hibernate.dialect.MySQLDialect instead
2025-07-23 22:29:47.902 [restartedMain] INFO  [] [] [] [] o.s.o.j.LocalContainerEntityManagerFactoryBean - Initialized JPA EntityManagerFactory for persistence unit 'default'
2025-07-23 22:29:48.285 [restartedMain] INFO  [] [] [] [] o.s.d.j.r.query.QueryEnhancerFactory - Hibernate is in classpath; If applicable, HQL parser will be used.
2025-07-23 22:29:50.341 [restartedMain] WARN  [] [] [] [] o.s.b.a.o.j.JpaBaseConfiguration$JpaWebConfiguration - spring.jpa.open-in-view is enabled by default. Therefore, database queries may be performed during view rendering. Explicitly configure spring.jpa.open-in-view to disable this warning
2025-07-23 22:29:50.787 [restartedMain] INFO  [] [] [] [] o.s.g.e.DefaultSchemaResourceGraphQlSourceBuilder - Loaded 1 resource(s) in the GraphQL schema.
2025-07-23 22:29:50.957 [restartedMain] INFO  [] [] [] [] o.s.b.a.g.GraphQlAutoConfiguration - GraphQL schema inspection:
	Unmapped fields: {AuditLogPage=[pageNumber, pageSize]}
	Unmapped registrations: {}
	Unmapped arguments: {}
	Skipped types: []
2025-07-23 22:29:50.991 [restartedMain] INFO  [] [] [] [] o.s.b.a.g.s.GraphQlWebMvcAutoConfiguration - GraphQL endpoint HTTP POST /graphql
2025-07-23 22:29:51.143 [restartedMain] INFO  [] [] [] [] o.s.b.a.e.web.EndpointLinksResolver - Exposing 3 endpoints beneath base path '/actuator'
2025-07-23 22:29:51.187 [restartedMain] WARN  [] [] [] [] o.s.b.a.s.s.UserDetailsServiceAutoConfiguration - 

Using generated security password: 51f916a0-ac11-4321-88f4-ea0dc4164315

This generated password is for development use only. Your security configuration must be updated before running your application in production.

2025-07-23 22:29:51.843 [restartedMain] INFO  [] [] [] [] o.a.coyote.http11.Http11NioProtocol - Starting ProtocolHandler ["http-nio-9091"]
2025-07-23 22:29:51.856 [restartedMain] INFO  [] [] [] [] o.s.b.w.e.tomcat.TomcatWebServer - Tomcat started on port 9091 (http) with context path '/'
2025-07-23 22:29:51.858 [restartedMain] INFO  [] [] [] [] o.s.a.r.c.CachingConnectionFactory - Attempting to connect to: [localhost:5672]
2025-07-23 22:29:51.900 [restartedMain] INFO  [] [] [] [] o.s.a.r.c.CachingConnectionFactory - Created new connection: rabbitConnectionFactory#6439894f:0/SimpleConnection@75374dc8 [delegate=amqp://guest@127.0.0.1:5672/, localPort=53879]
2025-07-23 22:29:51.974 [restartedMain] INFO  [] [] [] [] c.a.n.NotificationSvcApplication - Started NotificationSvcApplication in 8.927 seconds (process running for 9.412)
2025-07-23 22:30:52.337 [tomcat-handler-0] INFO  [] [] [] [] o.a.c.c.C.[Tomcat].[localhost].[/] - Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-07-23 22:30:52.337 [tomcat-handler-0] INFO  [] [] [] [] o.s.web.servlet.DispatcherServlet - Initializing Servlet 'dispatcherServlet'
2025-07-23 22:30:52.340 [tomcat-handler-0] INFO  [] [] [] [] o.s.web.servlet.DispatcherServlet - Completed initialization in 2 ms
2025-07-23 22:30:52.393 [tomcat-handler-0] WARN  [] [] [] [] c.a.n.service.SimpleJwtService - JWT validation is in testing mode - signature verification disabled
2025-07-23 22:30:52.417 [tomcat-handler-0] INFO  [test-upload-123] [32838d0c-be13-459d-a1b0-9ee9154f9813] [] [] c.a.n.config.LoggingConfig - Incoming request - Method: POST, URI: /graphql, RemoteAddr: 0:0:0:0:0:0:0:1, UserAgent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
2025-07-23 22:30:52.532 [tomcat-handler-0] DEBUG [test-upload-123] [32838d0c-be13-459d-a1b0-9ee9154f9813] [] [graphiql] org.hibernate.SQL - insert into notification_audit_logs (action,additional_data,correlation_id,details,entity_id,entity_type,request_id,result,source_service,timestamp,user_id) values (?,?,?,?,?,?,?,?,?,?,?)
2025-07-23 22:30:52.584 [tomcat-handler-0] INFO  [test-upload-123] [32838d0c-be13-459d-a1b0-9ee9154f9813] [] [graphiql] c.a.n.config.GraphQLAuditInterceptor - GraphQL operation started - Operation: mutation, Variables: {}, CorrelationId: test-upload-123
2025-07-23 22:30:52.585 [tomcat-handler-0] INFO  [test-upload-123] [32838d0c-be13-459d-a1b0-9ee9154f9813] [] [graphiql] c.a.n.config.GraphQLConfig - Extracted correlation ID from HTTP request: test-upload-123
2025-07-23 22:30:52.586 [tomcat-handler-0] INFO  [test-upload-123] [32838d0c-be13-459d-a1b0-9ee9154f9813] [] [graphiql] c.a.n.config.GraphQLConfig - GraphQL request intercepted - CorrelationId: test-upload-123, RequestId: 543702fa-4773-43dd-8524-f29e6b847563, SourceService: graphiql
2025-07-23 22:30:52.701 [notification-async-1] INFO  [] [] [] [] c.a.n.c.NotificationController - GraphQL mutation - sendNotification called with input: SendNotificationInput(sender=<EMAIL>, type=EMAIL, content=Please find the attached documents.-Anurag, templateName=welcome, messageType=null, importance=null, recipients=null, recipientDetails=[RecipientInput(email=<EMAIL>, type=TO, variables=[VariableInput(key=firstName, value=vaijinath), VariableInput(key=companyName, value=Ascent Business), VariableInput(key=role, value=Developer)], attachments=null), RecipientInput(email=<EMAIL>, type=TO, variables=[VariableInput(key=firstName, value=Anurag), VariableInput(key=companyName, value=Ascent Business), VariableInput(key=role, value=Developer)], attachments=null)], variables=null, commonAttachments=null)
2025-07-23 22:30:52.704 [notification-async-1] INFO  [] [] [] [] c.a.n.c.NotificationController - Captured correlation ID: null, request ID: null for audit logging
2025-07-23 22:30:52.707 [notification-async-1] INFO  [] [] [<EMAIL>] [] c.a.n.service.NotificationService - Processing sendNotification - Sender: <EMAIL>, Type: EMAIL, Content: Please find the attached documents.-Anurag
2025-07-23 22:30:52.709 [notification-async-1] INFO  [] [] [<EMAIL>] [] c.a.n.service.NotificationService - Using new recipient format with 2 detailed recipients
2025-07-23 22:30:52.710 [notification-async-1] INFO  [] [] [<EMAIL>] [] c.a.n.service.NotificationService - Processing sender: <EMAIL>
2025-07-23 22:30:52.717 [tomcat-handler-0] INFO  [test-upload-123] [543702fa-4773-43dd-8524-f29e6b847563] [] [graphiql] c.a.n.config.GraphQLConfig - Set MDC context - CorrelationId: test-upload-123, RequestId: 543702fa-4773-43dd-8524-f29e6b847563, SourceService: graphiql
2025-07-23 22:30:52.726 [tomcat-handler-0] INFO  [test-upload-123] [543702fa-4773-43dd-8524-f29e6b847563] [] [graphiql] c.a.n.config.LoggingConfig - Outgoing response - Status: 200, Duration: 308ms, ContentType: null
2025-07-23 22:30:52.741 [notification-async-1] DEBUG [] [] [<EMAIL>] [] org.hibernate.SQL - insert into notifications (common_attachments,content,created_date,importance,last_modified_date,message_type,sender_id,template_name,type,variables) values (?,?,?,?,?,?,?,?,?,?)
2025-07-23 22:30:52.745 [notification-async-1] INFO  [] [] [<EMAIL>] [] c.a.n.service.NotificationService - Created notification with ID: 132
2025-07-23 22:30:52.746 [notification-async-1] INFO  [] [] [<EMAIL>] [] c.a.n.service.NotificationService - Sending email with enhanced features (attachments/CC/BCC) for notification ID: 132
2025-07-23 22:30:52.746 [notification-async-1] INFO  [] [] [<EMAIL>] [] c.a.n.service.NotificationService - No recipient-specific attachments, sending single email for CC/BCC visibility
2025-07-23 22:30:52.747 [notification-async-1] INFO  [] [] [<EMAIL>] [] c.a.n.service.NotificationService - Sending single email to 2 recipients
2025-07-23 22:30:52.749 [notification-async-1] INFO  [] [] [<EMAIL>] [] c.a.n.service.EmailService - Sending email with proper CC/BCC visibility using template welcome
2025-07-23 22:30:52.749 [notification-async-1] INFO  [] [] [<EMAIL>] [] c.a.n.service.TemplateService - Synchronizing template: welcome
2025-07-23 22:30:52.827 [notification-async-1] DEBUG [] [] [<EMAIL>] [] org.hibernate.SQL - select nt1_0.id,nt1_0.alias,nt1_0.body,nt1_0.created_date,nt1_0.last_modified_date,nt1_0.subject from notification_templates nt1_0 where nt1_0.alias=?
2025-07-23 22:30:52.850 [notification-async-1] INFO  [] [] [<EMAIL>] [] c.a.n.service.TemplateService - Template 'welcome' content is consistent between classpath and database
2025-07-23 22:30:52.851 [notification-async-1] INFO  [] [] [<EMAIL>] [] c.a.n.service.EmailService - Using synchronized email template: welcome
2025-07-23 22:30:52.920 [notification-async-1] INFO  [] [] [<EMAIL>] [] c.a.n.service.EmailService - Template processed successfully with merged variables
2025-07-23 22:30:52.963 [notification-async-1] INFO  [] [] [<EMAIL>] [] c.a.n.service.EmailService - Added 2 TO recipients: <EMAIL>, <EMAIL>
2025-07-23 22:30:52.964 [notification-async-1] INFO  [] [] [<EMAIL>] [] c.a.n.service.EmailService - Sending <NAME_EMAIL> to 2 total recipients (TO: 2, CC: 0, BCC: 0)
2025-07-23 22:30:54.045 [notification-async-1] INFO  [] [] [<EMAIL>] [] c.a.n.service.EmailService - Email sent successfully with proper CC/BCC visibility - TO recipients can see CC, BCC is hidden from all
2025-07-23 22:30:54.051 [notification-async-1] DEBUG [] [] [<EMAIL>] [] org.hibernate.SQL - insert into notification_recipients (attachments,created_date,delivered,is_read,last_modified_date,notification_id,recipient_id,recipient_type,variables) values (?,?,?,?,?,?,?,?,?)
2025-07-23 22:30:54.074 [notification-async-1] DEBUG [] [] [<EMAIL>] [] org.hibernate.SQL - insert into notification_recipients (attachments,created_date,delivered,is_read,last_modified_date,notification_id,recipient_id,recipient_type,variables) values (?,?,?,?,?,?,?,?,?)
2025-07-23 22:30:54.077 [notification-async-1] INFO  [] [] [<EMAIL>] [] c.a.n.service.NotificationService - Created 2 notification recipients for tracking with recipient-specific data
2025-07-23 22:30:54.077 [notification-async-1] INFO  [] [] [<EMAIL>] [] c.a.n.service.NotificationService - Single email sent successfully to 2 recipients
2025-07-23 22:30:54.078 [notification-async-1] DEBUG [] [] [<EMAIL>] [] org.hibernate.SQL - insert into notification_audit_logs (action,additional_data,correlation_id,details,entity_id,entity_type,request_id,result,source_service,timestamp,user_id) values (?,?,?,?,?,?,?,?,?,?,?)
2025-07-23 22:30:54.081 [notification-async-1] DEBUG [] [] [<EMAIL>] [] org.hibernate.SQL - insert into notification_audit_logs (action,additional_data,correlation_id,details,entity_id,entity_type,request_id,result,source_service,timestamp,user_id) values (?,?,?,?,?,?,?,?,?,?,?)
2025-07-23 22:30:54.084 [notification-async-1] INFO  [] [] [<EMAIL>] [] c.a.n.service.NotificationService - Successfully sent enhanced email notification for 2 recipients
2025-07-23 22:30:54.091 [notification-async-1] INFO  [] [] [<EMAIL>] [] c.a.n.c.NotificationController - Notification sent successfully: true
2025-07-23 22:30:54.094 [notification-async-1] DEBUG [] [] [<EMAIL>] [] org.hibernate.SQL - insert into notification_audit_logs (action,additional_data,correlation_id,details,entity_id,entity_type,request_id,result,source_service,timestamp,user_id) values (?,?,?,?,?,?,?,?,?,?,?)
2025-07-23 22:30:54.104 [notification-async-1] INFO  [test-upload-123] [543702fa-4773-43dd-8524-f29e6b847563] [] [graphiql] c.a.n.config.GraphQLConfig - GraphQL response processed - CorrelationId: test-upload-123, RequestId: 543702fa-4773-43dd-8524-f29e6b847563, SourceService: graphiql
2025-07-23 22:30:54.106 [notification-async-1] INFO  [test-upload-123] [543702fa-4773-43dd-8524-f29e6b847563] [] [graphiql] c.a.n.config.GraphQLAuditInterceptor - GraphQL operation completed - Operation: mutation, Duration: 1617ms, CorrelationId: test-upload-123
2025-07-23 22:30:54.109 [notification-async-1] DEBUG [test-upload-123] [543702fa-4773-43dd-8524-f29e6b847563] [] [graphiql] org.hibernate.SQL - insert into notification_audit_logs (action,additional_data,correlation_id,details,entity_id,entity_type,request_id,result,source_service,timestamp,user_id) values (?,?,?,?,?,?,?,?,?,?,?)
2025-07-23 22:31:19.914 [rabbit-simple-1] INFO  [] [] [] [] o.s.a.r.l.SimpleMessageListenerContainer - Waiting for workers to finish.
2025-07-23 22:31:20.869 [rabbit-simple-1] INFO  [] [] [] [] o.s.a.r.l.SimpleMessageListenerContainer - Successfully waited for workers to finish.
2025-07-23 22:31:20.874 [SpringApplicationShutdownHook] INFO  [] [] [] [] o.s.b.w.e.tomcat.GracefulShutdown - Commencing graceful shutdown. Waiting for active requests to complete
2025-07-23 22:31:20.894 [tomcat-shutdown] INFO  [] [] [] [] o.s.b.w.e.tomcat.GracefulShutdown - Graceful shutdown complete
2025-07-23 22:31:21.037 [SpringApplicationShutdownHook] INFO  [] [] [] [] o.s.o.j.LocalContainerEntityManagerFactoryBean - Closing JPA EntityManagerFactory for persistence unit 'default'
2025-07-23 22:31:21.040 [SpringApplicationShutdownHook] INFO  [] [] [] [] com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Shutdown initiated...
2025-07-23 22:31:21.044 [SpringApplicationShutdownHook] INFO  [] [] [] [] com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Shutdown completed.
