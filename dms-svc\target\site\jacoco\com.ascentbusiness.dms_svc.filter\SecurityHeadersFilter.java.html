<?xml version="1.0" encoding="UTF-8"?><!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Strict//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-strict.dtd"><html xmlns="http://www.w3.org/1999/xhtml" lang="en"><head><meta http-equiv="Content-Type" content="text/html;charset=UTF-8"/><link rel="stylesheet" href="../jacoco-resources/report.css" type="text/css"/><link rel="shortcut icon" href="../jacoco-resources/report.gif" type="image/gif"/><title>SecurityHeadersFilter.java</title><link rel="stylesheet" href="../jacoco-resources/prettify.css" type="text/css"/><script type="text/javascript" src="../jacoco-resources/prettify.js"></script></head><body onload="window['PR_TAB_WIDTH']=4;prettyPrint()"><div class="breadcrumb" id="breadcrumb"><span class="info"><a href="../jacoco-sessions.html" class="el_session">Sessions</a></span><a href="../index.html" class="el_report">dms-svc</a> &gt; <a href="index.source.html" class="el_package">com.ascentbusiness.dms_svc.filter</a> &gt; <span class="el_source">SecurityHeadersFilter.java</span></div><h1>SecurityHeadersFilter.java</h1><pre class="source lang-java linenums">package com.ascentbusiness.dms_svc.filter;

import com.ascentbusiness.dms_svc.config.SecurityHeadersConfig;
import jakarta.servlet.*;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.annotation.Order;
import org.springframework.stereotype.Component;

import java.io.IOException;
import java.util.Map;

/**
 * Filter to add comprehensive security headers to all HTTP responses
 * This provides an additional layer of security header management beyond Spring Security
 */
@Component
@Order(1) // Execute early in the filter chain
<span class="fc" id="L22">public class SecurityHeadersFilter implements Filter {</span>
    
<span class="fc" id="L24">    private static final Logger logger = LoggerFactory.getLogger(SecurityHeadersFilter.class);</span>
    
    @Autowired
    private SecurityHeadersConfig securityHeadersConfig;
    
    @Override
    public void init(FilterConfig filterConfig) throws ServletException {
<span class="fc" id="L31">        logger.info(&quot;SecurityHeadersFilter initialized&quot;);</span>
<span class="fc" id="L32">    }</span>
    
    @Override
    public void doFilter(ServletRequest request, ServletResponse response, FilterChain chain)
            throws IOException, ServletException {
        
<span class="nc" id="L38">        HttpServletRequest httpRequest = (HttpServletRequest) request;</span>
<span class="nc" id="L39">        HttpServletResponse httpResponse = (HttpServletResponse) response;</span>
        
        // Add security headers before processing the request
<span class="nc" id="L42">        addSecurityHeaders(httpRequest, httpResponse);</span>
        
        // Continue with the filter chain
<span class="nc" id="L45">        chain.doFilter(request, response);</span>
        
        // Log security headers for monitoring (debug level)
<span class="nc bnc" id="L48" title="All 2 branches missed.">        if (logger.isDebugEnabled()) {</span>
<span class="nc" id="L49">            logSecurityHeaders(httpRequest, httpResponse);</span>
        }
<span class="nc" id="L51">    }</span>
    
    @Override
    public void destroy() {
<span class="fc" id="L55">        logger.info(&quot;SecurityHeadersFilter destroyed&quot;);</span>
<span class="fc" id="L56">    }</span>
    
    /**
     * Add comprehensive security headers to the response
     */
    private void addSecurityHeaders(HttpServletRequest request, HttpServletResponse response) {
        try {
<span class="nc" id="L63">            Map&lt;String, String&gt; headers = securityHeadersConfig.getAllSecurityHeaders();</span>
            
<span class="nc bnc" id="L65" title="All 2 branches missed.">            for (Map.Entry&lt;String, String&gt; header : headers.entrySet()) {</span>
<span class="nc" id="L66">                String headerName = header.getKey();</span>
<span class="nc" id="L67">                String headerValue = header.getValue();</span>
                
                // Only add header if it's not already set
<span class="nc bnc" id="L70" title="All 2 branches missed.">                if (response.getHeader(headerName) == null) {</span>
<span class="nc" id="L71">                    response.setHeader(headerName, headerValue);</span>
                }
<span class="nc" id="L73">            }</span>
            
            // Add request-specific headers
<span class="nc" id="L76">            addRequestSpecificHeaders(request, response);</span>
            
<span class="nc" id="L78">        } catch (Exception e) {</span>
<span class="nc" id="L79">            logger.error(&quot;Error adding security headers&quot;, e);</span>
            // Continue processing even if security headers fail to avoid breaking the application
<span class="nc" id="L81">        }</span>
<span class="nc" id="L82">    }</span>
    
    /**
     * Add request-specific security headers
     */
    private void addRequestSpecificHeaders(HttpServletRequest request, HttpServletResponse response) {
<span class="nc" id="L88">        String requestURI = request.getRequestURI();</span>
<span class="nc" id="L89">        String userAgent = request.getHeader(&quot;User-Agent&quot;);</span>
        
        // Add Vary header for better caching
<span class="nc" id="L92">        response.addHeader(&quot;Vary&quot;, &quot;Accept-Encoding, User-Agent, Authorization&quot;);</span>
        
        // Add security headers for GraphQL endpoints
<span class="nc bnc" id="L95" title="All 2 branches missed.">        if (requestURI.contains(&quot;/graphql&quot;)) {</span>
            // Different CSP policies for GraphiQL vs GraphQL API
<span class="nc bnc" id="L97" title="All 2 branches missed.">            if (securityHeadersConfig.isCspEnabled()) {</span>
<span class="nc bnc" id="L98" title="All 2 branches missed.">                if (requestURI.contains(&quot;/graphiql&quot;)) {</span>
                    // Permissive CSP for GraphiQL interface to allow external CDN resources
<span class="nc" id="L100">                    String graphiqlCSP = &quot;default-src 'self'; &quot; +</span>
                        &quot;script-src 'self' 'unsafe-inline' 'unsafe-eval' https://esm.sh https://unpkg.com https://cdn.jsdelivr.net; &quot; +
                        &quot;style-src 'self' 'unsafe-inline' https://esm.sh https://unpkg.com https://cdn.jsdelivr.net; &quot; +
                        &quot;img-src 'self' data: https:; &quot; +
                        &quot;font-src 'self' data: https://esm.sh https://unpkg.com https://cdn.jsdelivr.net; &quot; +
                        &quot;connect-src 'self' ws: wss: https://esm.sh; &quot; +
                        &quot;frame-ancestors 'self'; &quot; +
                        &quot;base-uri 'self'; &quot; +
                        &quot;form-action 'self'&quot;;
<span class="nc" id="L109">                    response.setHeader(&quot;Content-Security-Policy&quot;, graphiqlCSP);</span>
<span class="nc" id="L110">                } else {</span>
                    // Stricter CSP for GraphQL API endpoints
<span class="nc" id="L112">                    String graphqlCSP = &quot;default-src 'self'; script-src 'none'; object-src 'none'; base-uri 'self'&quot;;</span>
<span class="nc" id="L113">                    response.setHeader(&quot;Content-Security-Policy&quot;, graphqlCSP);</span>
                }
            }

            // No caching for GraphQL responses
<span class="nc" id="L118">            response.setHeader(&quot;Cache-Control&quot;, &quot;no-store, no-cache, must-revalidate, private&quot;);</span>
<span class="nc" id="L119">            response.setHeader(&quot;Pragma&quot;, &quot;no-cache&quot;);</span>
<span class="nc" id="L120">            response.setHeader(&quot;Expires&quot;, &quot;0&quot;);</span>
        }
        
        // Add security headers for API endpoints
<span class="nc bnc" id="L124" title="All 2 branches missed.">        if (requestURI.startsWith(&quot;/api/&quot;)) {</span>
            // API-specific headers
<span class="nc" id="L126">            response.setHeader(&quot;X-API-Version&quot;, &quot;1.0&quot;);</span>
<span class="nc" id="L127">            response.setHeader(&quot;X-Content-Type-Options&quot;, &quot;nosniff&quot;);</span>
        }
        
        // Add security headers for file downloads
<span class="nc bnc" id="L131" title="All 4 branches missed.">        if (requestURI.contains(&quot;/download&quot;) || requestURI.contains(&quot;/file&quot;)) {</span>
<span class="nc" id="L132">            response.setHeader(&quot;X-Download-Options&quot;, &quot;noopen&quot;);</span>
<span class="nc" id="L133">            response.setHeader(&quot;X-Content-Type-Options&quot;, &quot;nosniff&quot;);</span>
        }
        
        // Browser-specific security headers
<span class="nc bnc" id="L137" title="All 2 branches missed.">        if (userAgent != null) {</span>
<span class="nc bnc" id="L138" title="All 2 branches missed.">            if (userAgent.contains(&quot;Chrome&quot;)) {</span>
                // Chrome-specific headers
<span class="nc" id="L140">                response.setHeader(&quot;X-Chrome-Security&quot;, &quot;strict&quot;);</span>
<span class="nc bnc" id="L141" title="All 2 branches missed.">            } else if (userAgent.contains(&quot;Firefox&quot;)) {</span>
                // Firefox-specific headers
<span class="nc" id="L143">                response.setHeader(&quot;X-Firefox-Security&quot;, &quot;strict&quot;);</span>
            }
        }
        
        // Add timing attack protection
<span class="nc" id="L148">        response.setHeader(&quot;X-Response-Time&quot;, String.valueOf(System.currentTimeMillis()));</span>
<span class="nc" id="L149">    }</span>
    
    /**
     * Log security headers for monitoring and debugging
     */
    private void logSecurityHeaders(HttpServletRequest request, HttpServletResponse response) {
<span class="nc" id="L155">        String requestURI = request.getRequestURI();</span>
<span class="nc" id="L156">        String method = request.getMethod();</span>
        
<span class="nc" id="L158">        StringBuilder headerLog = new StringBuilder();</span>
<span class="nc" id="L159">        headerLog.append(&quot;Security headers for &quot;).append(method).append(&quot; &quot;).append(requestURI).append(&quot;:\n&quot;);</span>
        
        // Log important security headers
<span class="nc" id="L162">        String[] importantHeaders = {</span>
            &quot;Content-Security-Policy&quot;,
            &quot;Strict-Transport-Security&quot;,
            &quot;X-Frame-Options&quot;,
            &quot;X-Content-Type-Options&quot;,
            &quot;Referrer-Policy&quot;,
            &quot;Permissions-Policy&quot;,
            &quot;Cross-Origin-Embedder-Policy&quot;,
            &quot;Cross-Origin-Opener-Policy&quot;,
            &quot;Cross-Origin-Resource-Policy&quot;
        };
        
<span class="nc bnc" id="L174" title="All 2 branches missed.">        for (String headerName : importantHeaders) {</span>
<span class="nc" id="L175">            String headerValue = response.getHeader(headerName);</span>
<span class="nc bnc" id="L176" title="All 2 branches missed.">            if (headerValue != null) {</span>
<span class="nc" id="L177">                headerLog.append(&quot;  &quot;).append(headerName).append(&quot;: &quot;).append(headerValue).append(&quot;\n&quot;);</span>
            }
        }
        
<span class="nc" id="L181">        logger.debug(headerLog.toString());</span>
<span class="nc" id="L182">    }</span>
    
    /**
     * Check if the request is from a trusted source
     */
    private boolean isTrustedSource(HttpServletRequest request) {
<span class="nc" id="L188">        String origin = request.getHeader(&quot;Origin&quot;);</span>
<span class="nc" id="L189">        String referer = request.getHeader(&quot;Referer&quot;);</span>
<span class="nc" id="L190">        String userAgent = request.getHeader(&quot;User-Agent&quot;);</span>
        
        // Basic checks for trusted sources
<span class="nc bnc" id="L193" title="All 6 branches missed.">        if (origin != null &amp;&amp; (origin.contains(&quot;localhost&quot;) || origin.contains(&quot;127.0.0.1&quot;))) {</span>
<span class="nc" id="L194">            return true;</span>
        }
        
<span class="nc bnc" id="L197" title="All 6 branches missed.">        if (referer != null &amp;&amp; (referer.contains(&quot;localhost&quot;) || referer.contains(&quot;127.0.0.1&quot;))) {</span>
<span class="nc" id="L198">            return true;</span>
        }
        
        // Check for known bot user agents that should be allowed
<span class="nc bnc" id="L202" title="All 2 branches missed.">        if (userAgent != null &amp;&amp; (</span>
<span class="nc bnc" id="L203" title="All 2 branches missed.">            userAgent.contains(&quot;Googlebot&quot;) ||</span>
<span class="nc bnc" id="L204" title="All 2 branches missed.">            userAgent.contains(&quot;bingbot&quot;) ||</span>
<span class="nc bnc" id="L205" title="All 2 branches missed.">            userAgent.contains(&quot;health-check&quot;)</span>
        )) {
<span class="nc" id="L207">            return true;</span>
        }
        
<span class="nc" id="L210">        return false;</span>
    }
    
    /**
     * Get client IP address considering proxy headers
     */
    private String getClientIpAddress(HttpServletRequest request) {
<span class="nc" id="L217">        String xForwardedFor = request.getHeader(&quot;X-Forwarded-For&quot;);</span>
<span class="nc bnc" id="L218" title="All 4 branches missed.">        if (xForwardedFor != null &amp;&amp; !xForwardedFor.isEmpty()) {</span>
<span class="nc" id="L219">            return xForwardedFor.split(&quot;,&quot;)[0].trim();</span>
        }
        
<span class="nc" id="L222">        String xRealIp = request.getHeader(&quot;X-Real-IP&quot;);</span>
<span class="nc bnc" id="L223" title="All 4 branches missed.">        if (xRealIp != null &amp;&amp; !xRealIp.isEmpty()) {</span>
<span class="nc" id="L224">            return xRealIp;</span>
        }
        
<span class="nc" id="L227">        return request.getRemoteAddr();</span>
    }
}
</pre><div class="footer"><span class="right">Created with <a href="http://www.jacoco.org/jacoco">JaCoCo</a> 0.8.10.202304240956</span></div></body></html>