<?xml version="1.0" encoding="UTF-8"?><!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Strict//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-strict.dtd"><html xmlns="http://www.w3.org/1999/xhtml" lang="en"><head><meta http-equiv="Content-Type" content="text/html;charset=UTF-8"/><link rel="stylesheet" href="../jacoco-resources/report.css" type="text/css"/><link rel="shortcut icon" href="../jacoco-resources/report.gif" type="image/gif"/><title>UrlUploadConfig.java</title><link rel="stylesheet" href="../jacoco-resources/prettify.css" type="text/css"/><script type="text/javascript" src="../jacoco-resources/prettify.js"></script></head><body onload="window['PR_TAB_WIDTH']=4;prettyPrint()"><div class="breadcrumb" id="breadcrumb"><span class="info"><a href="../jacoco-sessions.html" class="el_session">Sessions</a></span><a href="../index.html" class="el_report">dms-svc</a> &gt; <a href="index.source.html" class="el_package">com.ascentbusiness.dms_svc.config</a> &gt; <span class="el_source">UrlUploadConfig.java</span></div><h1>UrlUploadConfig.java</h1><pre class="source lang-java linenums">/*
 * Copyright (c) 2024 Ascent Business Technology
 * All rights reserved.
 */

package com.ascentbusiness.dms_svc.config;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Configuration;

import java.util.Arrays;
import java.util.List;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * Configuration properties for URL-based document uploads.
 * 
 * &lt;p&gt;This configuration class manages settings for downloading files from URLs
 * and uploading them to the document management system. It includes security
 * settings, timeout configurations, and content validation options.
 * 
 * <AUTHOR> Development Team
 * @version 1.0.0
 * @since 1.0.0
 */
@Configuration
@ConfigurationProperties(prefix = &quot;dms.upload.url&quot;)
<span class="pc bpc" id="L30" title="54 of 64 branches missed.">@Data</span>
public class UrlUploadConfig {
    
    /**
     * Whether URL uploads are enabled.
     */
<span class="fc" id="L36">    private boolean enabled = true;</span>
    
    /**
     * Timeout for URL downloads in milliseconds.
     */
<span class="fc" id="L41">    private int timeoutMs = 30000;</span>
    
    /**
     * Maximum file size for URL downloads in bytes.
     */
<span class="fc" id="L46">    private long maxFileSize = 104857600L; // 100MB</span>
    
    /**
     * Maximum number of redirects to follow.
     */
<span class="fc" id="L51">    private int maxRedirects = 5;</span>
    
    /**
     * User agent string for HTTP requests.
     */
<span class="fc" id="L56">    private String userAgent = &quot;DMS-Service/1.0&quot;;</span>
    
    /**
     * Comma-separated list of allowed domains.
     * Empty means all domains are allowed.
     */
<span class="fc" id="L62">    private String allowedDomains = &quot;&quot;;</span>
    
    /**
     * Whether to validate SSL certificates.
     */
<span class="fc" id="L67">    private boolean validateSsl = true;</span>
    
    /**
     * Whether to allow downloads from private IP addresses.
     */
<span class="fc" id="L72">    private boolean allowPrivateIps = false;</span>
    
    /**
     * Comma-separated list of blocked ports.
     */
<span class="fc" id="L77">    private String blockedPorts = &quot;22,23,25,53,135,139,445,1433,1521,3306,3389,5432,6379&quot;;</span>
    
    /**
     * Comma-separated list of allowed content types.
     * Empty means all content types are allowed.
     */
<span class="fc" id="L83">    private String allowedContentTypes = &quot;&quot;;</span>
    
    /**
     * Comma-separated list of blocked content types.
     */
<span class="fc" id="L88">    private String blockedContentTypes = &quot;text/html,application/javascript,text/javascript&quot;;</span>
    
    // Derived properties
    
    /**
     * Get the set of allowed domains.
     * 
     * @return set of allowed domains, empty if all domains are allowed
     */
    public Set&lt;String&gt; getAllowedDomainsSet() {
<span class="pc bpc" id="L98" title="2 of 4 branches missed.">        if (allowedDomains == null || allowedDomains.trim().isEmpty()) {</span>
<span class="fc" id="L99">            return Set.of();</span>
        }
<span class="nc" id="L101">        return Arrays.stream(allowedDomains.split(&quot;,&quot;))</span>
<span class="nc" id="L102">                .map(String::trim)</span>
<span class="nc" id="L103">                .map(String::toLowerCase)</span>
<span class="nc bnc" id="L104" title="All 2 branches missed.">                .filter(s -&gt; !s.isEmpty())</span>
<span class="nc" id="L105">                .collect(Collectors.toSet());</span>
    }
    
    /**
     * Get the set of blocked ports.
     * 
     * @return set of blocked port numbers
     */
    public Set&lt;Integer&gt; getBlockedPortsSet() {
<span class="pc bpc" id="L114" title="2 of 4 branches missed.">        if (blockedPorts == null || blockedPorts.trim().isEmpty()) {</span>
<span class="nc" id="L115">            return Set.of();</span>
        }
<span class="fc" id="L117">        return Arrays.stream(blockedPorts.split(&quot;,&quot;))</span>
<span class="fc" id="L118">                .map(String::trim)</span>
<span class="pc bpc" id="L119" title="1 of 2 branches missed.">                .filter(s -&gt; !s.isEmpty())</span>
<span class="fc" id="L120">                .map(Integer::parseInt)</span>
<span class="fc" id="L121">                .collect(Collectors.toSet());</span>
    }
    
    /**
     * Get the set of allowed content types.
     * 
     * @return set of allowed content types, empty if all types are allowed
     */
    public Set&lt;String&gt; getAllowedContentTypesSet() {
<span class="pc bpc" id="L130" title="2 of 4 branches missed.">        if (allowedContentTypes == null || allowedContentTypes.trim().isEmpty()) {</span>
<span class="fc" id="L131">            return Set.of();</span>
        }
<span class="nc" id="L133">        return Arrays.stream(allowedContentTypes.split(&quot;,&quot;))</span>
<span class="nc" id="L134">                .map(String::trim)</span>
<span class="nc" id="L135">                .map(String::toLowerCase)</span>
<span class="nc bnc" id="L136" title="All 2 branches missed.">                .filter(s -&gt; !s.isEmpty())</span>
<span class="nc" id="L137">                .collect(Collectors.toSet());</span>
    }
    
    /**
     * Get the set of blocked content types.
     * 
     * @return set of blocked content types
     */
    public Set&lt;String&gt; getBlockedContentTypesSet() {
<span class="pc bpc" id="L146" title="2 of 4 branches missed.">        if (blockedContentTypes == null || blockedContentTypes.trim().isEmpty()) {</span>
<span class="nc" id="L147">            return Set.of();</span>
        }
<span class="fc" id="L149">        return Arrays.stream(blockedContentTypes.split(&quot;,&quot;))</span>
<span class="fc" id="L150">                .map(String::trim)</span>
<span class="fc" id="L151">                .map(String::toLowerCase)</span>
<span class="pc bpc" id="L152" title="1 of 2 branches missed.">                .filter(s -&gt; !s.isEmpty())</span>
<span class="fc" id="L153">                .collect(Collectors.toSet());</span>
    }
    
    /**
     * Check if a domain is allowed.
     * 
     * @param domain the domain to check
     * @return true if the domain is allowed
     */
    public boolean isDomainAllowed(String domain) {
<span class="pc bpc" id="L163" title="1 of 2 branches missed.">        if (domain == null) {</span>
<span class="nc" id="L164">            return false;</span>
        }
        
<span class="fc" id="L167">        Set&lt;String&gt; allowedSet = getAllowedDomainsSet();</span>
<span class="pc bpc" id="L168" title="1 of 2 branches missed.">        if (allowedSet.isEmpty()) {</span>
<span class="fc" id="L169">            return true; // All domains allowed</span>
        }
        
<span class="nc" id="L172">        String normalizedDomain = domain.toLowerCase();</span>
<span class="nc bnc" id="L173" title="All 2 branches missed.">        return allowedSet.contains(normalizedDomain) || </span>
<span class="nc bnc" id="L174" title="All 2 branches missed.">               allowedSet.stream().anyMatch(allowed -&gt; normalizedDomain.endsWith(&quot;.&quot; + allowed));</span>
    }
    
    /**
     * Check if a port is blocked.
     * 
     * @param port the port to check
     * @return true if the port is blocked
     */
    public boolean isPortBlocked(int port) {
<span class="fc" id="L184">        return getBlockedPortsSet().contains(port);</span>
    }
    
    /**
     * Check if a content type is allowed.
     * 
     * @param contentType the content type to check
     * @return true if the content type is allowed
     */
    public boolean isContentTypeAllowed(String contentType) {
<span class="pc bpc" id="L194" title="1 of 2 branches missed.">        if (contentType == null) {</span>
<span class="nc" id="L195">            return true;</span>
        }
        
<span class="fc" id="L198">        String normalizedType = contentType.toLowerCase();</span>
        
        // Check blocked types first
<span class="fc" id="L201">        Set&lt;String&gt; blockedSet = getBlockedContentTypesSet();</span>
<span class="fc bfc" id="L202" title="All 2 branches covered.">        if (blockedSet.stream().anyMatch(blocked -&gt; normalizedType.startsWith(blocked))) {</span>
<span class="fc" id="L203">            return false;</span>
        }
        
        // Check allowed types
<span class="fc" id="L207">        Set&lt;String&gt; allowedSet = getAllowedContentTypesSet();</span>
<span class="pc bpc" id="L208" title="1 of 2 branches missed.">        if (allowedSet.isEmpty()) {</span>
<span class="fc" id="L209">            return true; // All types allowed</span>
        }
        
<span class="nc" id="L212">        return allowedSet.stream().anyMatch(allowed -&gt; normalizedType.startsWith(allowed));</span>
    }
}
</pre><div class="footer"><span class="right">Created with <a href="http://www.jacoco.org/jacoco">JaCoCo</a> 0.8.10.202304240956</span></div></body></html>