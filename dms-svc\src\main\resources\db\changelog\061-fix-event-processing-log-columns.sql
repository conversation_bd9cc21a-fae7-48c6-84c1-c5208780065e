--liquibase formatted sql

--changeset system:061-fix-event-processing-log-columns
--comment: Fix event_processing_log table column names to match JPA entity

-- Drop the incorrectly created table
DROP TABLE IF EXISTS event_processing_log;

-- Recreate with correct column names
CREATE TABLE event_processing_log (
    id BIGINT AUTO_INCREMENT PRIMARY KEY,
    system_event_id BIGINT NOT NULL,
    event_subscription_id BIGINT,
    processing_stage VARCHAR(50) NOT NULL,
    processing_status VARCHAR(50) NOT NULL,
    processor_name VARCHAR(100),
    processing_duration_ms INT,
    error_message TEXT,
    error_stack_trace TEXT,
    processing_timestamp DATETIME NOT NULL,
    correlation_id VARCHAR(100),
    created_date DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
    last_modified_date DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    created_by <PERSON><PERSON><PERSON><PERSON>(100),
    last_modified_by VARCHAR(100)
);

-- Add indexes for better performance
CREATE INDEX idx_event_processing_log_system_event ON event_processing_log(system_event_id);
CREATE INDEX idx_event_processing_log_subscription ON event_processing_log(event_subscription_id);
CREATE INDEX idx_event_processing_log_stage ON event_processing_log(processing_stage);
CREATE INDEX idx_event_processing_log_status ON event_processing_log(processing_status);
CREATE INDEX idx_event_processing_log_timestamp ON event_processing_log(processing_timestamp);
CREATE INDEX idx_event_processing_log_correlation ON event_processing_log(correlation_id);

-- Add comment for documentation
ALTER TABLE event_processing_log COMMENT = 'Tracks event processing stages and status for system events';

--rollback DROP TABLE event_processing_log;