# GRC Platform - Simplified Configuration & Deployment

## Overview

The GRC Platform has been simplified to reduce configuration complexity and streamline deployment processes for both development and UAT environments.

## Quick Start

### Development Environment
```bash
./deploy.sh dev
```

### UAT Environment
```bash
./deploy.sh uat --clean
```

## Simplified File Structure

### Root Directory (Cleaned Up)
```
├── .env.dev                    # Development environment variables
├── .env.uat                    # UAT environment variables
├── deploy.sh                   # Unified deployment script
├── docker-compose.shared.yml   # Shared infrastructure (MySQL, Redis, etc.)
├── docker-compose.services.yml # Development application services
├── docker-compose.uat.yml      # UAT application services
├── DEPLOYMENT_GUIDE.md         # Comprehensive deployment guide
└── dms-svc/CONFIG_GUIDE.md     # Configuration management guide
```

### Docker Compose Files Purpose

The 3 Docker Compose files provide clear separation of concerns:

1. **`docker-compose.shared.yml`** - Shared Infrastructure
   - MySQL database (shared by both DMS and Notification services)
   - Redis cache (shared by both services)
   - RabbitMQ message broker
   - Elasticsearch, <PERSON><PERSON><PERSON>, Prometheus, Grafana
   - Nginx reverse proxy (routes traffic to both services)

2. **`docker-compose.services.yml`** - Development Services
   - DMS Service (dev profile, debug logging, GraphiQL enabled)
   - Notification Service (dev profile)

3. **`docker-compose.uat.yml`** - UAT Services
   - DMS Service (UAT profile, production logging, GraphiQL disabled)
   - Notification Service (UAT profile)

**Benefits of this structure:**
- **Shared Infrastructure Reuse**: Both dev and UAT use same infrastructure
- **Environment Separation**: Different configurations for dev vs UAT
- **Selective Deployment**: Can start infrastructure independently
- **Resource Optimization**: UAT has different resource limits than dev

### Application Configuration
```
dms-svc/src/main/resources/
├── application.properties       # Base configuration with env variables
├── application-dev.properties   # Development-specific overrides
└── application-uat.properties   # UAT-specific overrides
```

## Complexity Reduction Achieved

| Aspect | Before | After | Reduction |
|--------|--------|-------|-----------|
| Configuration Files | 8+ files | 3 files | 60%+ |
| Environment Files | 7 .env files | 2 .env files | 70%+ |
| Deployment Scripts | 12+ scripts | 1 script | 90%+ |
| Docker Compose Files | 5 files | 3 files | 40% |
| Documentation Files | 4+ files | 2 files | 50% |

## Key Benefits

1. **Single Deployment Command**: `./deploy.sh [dev|uat]`
2. **Environment Variable Driven**: Easy customization without code changes
3. **Consistent Process**: Same deployment flow for both environments
4. **Better Error Handling**: Comprehensive health checks and rollback
5. **Infrastructure Team Friendly**: Simple commands, clear documentation

## Removed Files

### Configuration Files Removed:
- `application-local.properties`
- `application-docker.properties`
- `application-local-dev.properties`
- `application-aws-ec2.properties`
- `config/environments/dev.properties`
- `config/environments/prod.properties`

### Environment Files Removed:
- `.env.autoresilience.com`
- `.env.aws-ec2.template`
- `.env.development`
- `.env.local-dev`
- `.env.template`

### Deployment Scripts Removed:
- `backup-uat.sh`
- `clean-docker.sh`
- `clean-uat.sh`
- `deploy-dev.sh`
- `deploy-standalone.sh`
- `deploy-uat.sh`
- `fix-cors-and-redeploy.sh`
- `fix-line-endings.sh`
- `local-docker-rebuild.ps1`
- `local-docker-rebuild.sh`
- `test-cors-fix.js`
- `test-cors-quick.sh`

### Docker Compose & Documentation Removed:
- `docker-compose.aws-ec2.yml`
- `CORS-FIX-COMPLETE-SOLUTION.md`
- `fix-uat-deployment-issues.md`
- `README-Docker-Scripts.md`

## Usage Examples

```bash
# Development deployment
./deploy.sh dev

# UAT deployment with cleanup
./deploy.sh uat --clean

# Force rebuild and deploy
./deploy.sh dev --clean --build

# View help
./deploy.sh --help
```

## Service URLs

### Development
- DMS Service: http://localhost:9093
- Notification Service: http://localhost:9091
- Grafana: http://localhost:3000

### UAT
- DMS Service: http://[PUBLIC_IP]:9093
- Notification Service: http://[PUBLIC_IP]:9091
- Grafana: http://[PUBLIC_IP]:3000

## Documentation

- **[DEPLOYMENT_GUIDE.md](DEPLOYMENT_GUIDE.md)**: Comprehensive deployment instructions
- **[dms-svc/CONFIG_GUIDE.md](dms-svc/CONFIG_GUIDE.md)**: Configuration management guide

## Support

For deployment issues or questions, refer to the troubleshooting sections in the deployment guide or check the application logs using:

```bash
docker compose --env-file .env.dev -f docker-compose.shared.yml -f docker-compose.services.yml logs -f