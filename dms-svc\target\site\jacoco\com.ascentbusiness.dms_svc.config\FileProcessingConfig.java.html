<?xml version="1.0" encoding="UTF-8"?><!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Strict//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-strict.dtd"><html xmlns="http://www.w3.org/1999/xhtml" lang="en"><head><meta http-equiv="Content-Type" content="text/html;charset=UTF-8"/><link rel="stylesheet" href="../jacoco-resources/report.css" type="text/css"/><link rel="shortcut icon" href="../jacoco-resources/report.gif" type="image/gif"/><title>FileProcessingConfig.java</title><link rel="stylesheet" href="../jacoco-resources/prettify.css" type="text/css"/><script type="text/javascript" src="../jacoco-resources/prettify.js"></script></head><body onload="window['PR_TAB_WIDTH']=4;prettyPrint()"><div class="breadcrumb" id="breadcrumb"><span class="info"><a href="../jacoco-sessions.html" class="el_session">Sessions</a></span><a href="../index.html" class="el_report">dms-svc</a> &gt; <a href="index.source.html" class="el_package">com.ascentbusiness.dms_svc.config</a> &gt; <span class="el_source">FileProcessingConfig.java</span></div><h1>FileProcessingConfig.java</h1><pre class="source lang-java linenums">package com.ascentbusiness.dms_svc.config;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;

/**
 * Configuration properties for dynamic file processing strategies.
 * 
 * &lt;p&gt;This configuration class defines thresholds and settings that determine
 * how files are processed based on their size and other characteristics:
 * &lt;ul&gt;
 *   &lt;li&gt;DIRECT: Small files processed immediately and synchronously&lt;/li&gt;
 *   &lt;li&gt;ASYNC: Medium files processed asynchronously in background&lt;/li&gt;
 *   &lt;li&gt;CHUNKED: Large files processed in chunks with session management&lt;/li&gt;
 * &lt;/ul&gt;
 */
<span class="pc bpc" id="L18" title="42 of 46 branches missed.">@Data</span>
@Component
@ConfigurationProperties(prefix = &quot;dms.file-processing&quot;)
public class FileProcessingConfig {
    
    /**
     * Maximum file size (in bytes) for direct processing.
     * Files smaller than this threshold will be processed immediately.
     * Default: 10MB
     */
<span class="fc" id="L28">    private long directProcessingThreshold = 10 * 1024 * 1024; // 10MB</span>
    
    /**
     * Maximum file size (in bytes) for async processing.
     * Files between directProcessingThreshold and this threshold will be processed asynchronously.
     * Default: 100MB
     */
<span class="fc" id="L35">    private long asyncProcessingThreshold = 100 * 1024 * 1024; // 100MB</span>
    
    /**
     * Files larger than asyncProcessingThreshold will require chunked upload.
     * Maximum file size (in bytes) for chunked processing.
     * Default: 1GB
     */
<span class="fc" id="L42">    private long maxFileSize = 1024 * 1024 * 1024; // 1GB</span>
    
    /**
     * Default chunk size (in bytes) for chunked uploads.
     * Default: 5MB
     */
<span class="fc" id="L48">    private int defaultChunkSize = 5 * 1024 * 1024; // 5MB</span>
    
    /**
     * Maximum chunk size (in bytes) allowed for chunked uploads.
     * Default: 50MB
     */
<span class="fc" id="L54">    private int maxChunkSize = 50 * 1024 * 1024; // 50MB</span>
    
    /**
     * Minimum chunk size (in bytes) allowed for chunked uploads.
     * Default: 1MB
     */
<span class="fc" id="L60">    private int minChunkSize = 1024 * 1024; // 1MB</span>
    
    /**
     * Maximum number of concurrent async processing jobs.
     * Default: 10
     */
<span class="fc" id="L66">    private int maxConcurrentAsyncJobs = 10;</span>
    
    /**
     * Timeout (in seconds) for async job processing.
     * Default: 3600 seconds (1 hour)
     */
<span class="fc" id="L72">    private int asyncJobTimeoutSeconds = 3600;</span>
    
    /**
     * Maximum duration (in seconds) for chunked upload sessions.
     * Sessions older than this will be cleaned up.
     * Default: 86400 seconds (24 hours)
     */
<span class="fc" id="L79">    private int chunkSessionTimeoutSeconds = 86400;</span>
    
    /**
     * Interval (in seconds) for cleanup of expired sessions and temporary files.
     * Default: 3600 seconds (1 hour)
     */
<span class="fc" id="L85">    private int cleanupIntervalSeconds = 3600;</span>
    
    /**
     * Directory for temporary files during processing.
     * Default: ./temp/processing
     */
<span class="fc" id="L91">    private String tempDirectory = &quot;./temp/processing&quot;;</span>
    
    /**
     * Whether to enable automatic cleanup of temporary files.
     * Default: true
     */
<span class="fc" id="L97">    private boolean enableAutoCleanup = true;</span>
    
    /**
     * Whether to enable progress tracking for async jobs.
     * Default: true
     */
<span class="fc" id="L103">    private boolean enableProgressTracking = true;</span>
    
    /**
     * Whether to enable detailed logging for file processing operations.
     * Default: false
     */
<span class="fc" id="L109">    private boolean enableDetailedLogging = false;</span>
    
    /**
     * Calculate optimal chunk size based on file size.
     *
     * @param fileSize the size of the file in bytes
     * @return optimal chunk size in bytes
     */
    public int getOptimalChunkSize(long fileSize) {
<span class="nc bnc" id="L118" title="All 2 branches missed.">        if (fileSize &lt;= 50 * 1024 * 1024) { // &lt;= 50MB</span>
<span class="nc" id="L119">            return minChunkSize; // Use minimum chunk size for small files</span>
<span class="nc bnc" id="L120" title="All 2 branches missed.">        } else if (fileSize &lt;= 500 * 1024 * 1024) { // &lt;= 500MB</span>
<span class="nc" id="L121">            return defaultChunkSize; // 5MB</span>
<span class="nc bnc" id="L122" title="All 2 branches missed.">        } else if (fileSize &lt;= 2L * 1024 * 1024 * 1024) { // &lt;= 2GB</span>
<span class="nc" id="L123">            return Math.min(maxChunkSize, defaultChunkSize * 2); // 10MB</span>
        } else {
<span class="nc" id="L125">            return maxChunkSize; // 50MB</span>
        }
    }
    
    /**
     * Get optimal chunk size with default file size assumption.
     * 
     * @return default chunk size in bytes
     */
    public int getOptimalChunkSize() {
<span class="nc" id="L135">        return defaultChunkSize;</span>
    }
    
    /**
     * Calculate estimated number of chunks for a given file size.
     * 
     * @param fileSize the size of the file in bytes
     * @param chunkSize the size of each chunk in bytes
     * @return estimated number of chunks
     */
    public int calculateChunkCount(long fileSize, int chunkSize) {
<span class="nc" id="L146">        return (int) Math.ceil((double) fileSize / chunkSize);</span>
    }
    
    /**
     * Validate if the given chunk size is within acceptable limits.
     * 
     * @param chunkSize the chunk size to validate
     * @return true if valid, false otherwise
     */
    public boolean isValidChunkSize(int chunkSize) {
<span class="nc bnc" id="L156" title="All 4 branches missed.">        return chunkSize &gt;= minChunkSize &amp;&amp; chunkSize &lt;= maxChunkSize;</span>
    }
    
    /**
     * Validate if the given file size is within processing limits.
     * 
     * @param fileSize the file size to validate
     * @return true if valid, false otherwise
     */
    public boolean isValidFileSize(long fileSize) {
<span class="nc bnc" id="L166" title="All 4 branches missed.">        return fileSize &gt; 0 &amp;&amp; fileSize &lt;= maxFileSize;</span>
    }
}
</pre><div class="footer"><span class="right">Created with <a href="http://www.jacoco.org/jacoco">JaCoCo</a> 0.8.10.202304240956</span></div></body></html>