<?xml version="1.0" encoding="UTF-8"?><!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Strict//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-strict.dtd"><html xmlns="http://www.w3.org/1999/xhtml" lang="en"><head><meta http-equiv="Content-Type" content="text/html;charset=UTF-8"/><link rel="stylesheet" href="../jacoco-resources/report.css" type="text/css"/><link rel="shortcut icon" href="../jacoco-resources/report.gif" type="image/gif"/><title>CacheConfig.java</title><link rel="stylesheet" href="../jacoco-resources/prettify.css" type="text/css"/><script type="text/javascript" src="../jacoco-resources/prettify.js"></script></head><body onload="window['PR_TAB_WIDTH']=4;prettyPrint()"><div class="breadcrumb" id="breadcrumb"><span class="info"><a href="../jacoco-sessions.html" class="el_session">Sessions</a></span><a href="../index.html" class="el_report">dms-svc</a> &gt; <a href="index.source.html" class="el_package">com.ascentbusiness.dms_svc.config</a> &gt; <span class="el_source">CacheConfig.java</span></div><h1>CacheConfig.java</h1><pre class="source lang-java linenums">package com.ascentbusiness.dms_svc.config;

import com.fasterxml.jackson.annotation.JsonTypeInfo;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.datatype.jsr310.JavaTimeModule;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.cache.CacheManager;
import org.springframework.cache.annotation.EnableCaching;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Primary;
import org.springframework.data.redis.cache.RedisCacheConfiguration;
import org.springframework.data.redis.cache.RedisCacheManager;
import org.springframework.data.redis.connection.RedisConnectionFactory;
import org.springframework.data.redis.connection.RedisStandaloneConfiguration;
import org.springframework.data.redis.connection.lettuce.LettuceConnectionFactory;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.data.redis.serializer.GenericJackson2JsonRedisSerializer;
import org.springframework.data.redis.serializer.RedisSerializationContext;
import org.springframework.data.redis.serializer.StringRedisSerializer;

import java.time.Duration;
import java.util.HashMap;
import java.util.Map;

/**
 * Redis cache configuration for DMS performance optimization.
 * Provides different cache configurations for different data types with appropriate TTL settings.
 */
@Configuration
@EnableCaching
<span class="fc" id="L33">public class CacheConfig {</span>

    @Value(&quot;${spring.data.redis.host:localhost}&quot;)
    private String redisHost;

    @Value(&quot;${spring.data.redis.port:6379}&quot;)
    private int redisPort;

    @Value(&quot;${spring.data.redis.password:}&quot;)
    private String redisPassword;

    @Value(&quot;${spring.data.redis.database:0}&quot;)
    private int redisDatabase;

    /**
     * Redis connection factory bean
     */
    @Bean
    @ConditionalOnProperty(name = &quot;spring.cache.type&quot;, havingValue = &quot;redis&quot;)
    public RedisConnectionFactory redisConnectionFactory() {
<span class="nc" id="L53">        RedisStandaloneConfiguration config = new RedisStandaloneConfiguration();</span>
<span class="nc" id="L54">        config.setHostName(redisHost);</span>
<span class="nc" id="L55">        config.setPort(redisPort);</span>
<span class="nc" id="L56">        config.setDatabase(redisDatabase);</span>

<span class="nc bnc" id="L58" title="All 4 branches missed.">        if (redisPassword != null &amp;&amp; !redisPassword.trim().isEmpty()) {</span>
<span class="nc" id="L59">            config.setPassword(redisPassword);</span>
        }

<span class="nc" id="L62">        return new LettuceConnectionFactory(config);</span>
    }

    /**
     * Redis cache configuration with custom TTL for different cache regions
     */
    @Bean
    @Primary
    @ConditionalOnProperty(name = &quot;spring.cache.type&quot;, havingValue = &quot;redis&quot;)
    public CacheManager redisCacheManager(RedisConnectionFactory connectionFactory) {
        // Configure JSON serializer for complex objects
<span class="nc" id="L73">        ObjectMapper objectMapper = new ObjectMapper();</span>
<span class="nc" id="L74">        objectMapper.registerModule(new JavaTimeModule());</span>
<span class="nc" id="L75">        objectMapper.activateDefaultTyping(</span>
<span class="nc" id="L76">            objectMapper.getPolymorphicTypeValidator(),</span>
            ObjectMapper.DefaultTyping.NON_FINAL,
            JsonTypeInfo.As.PROPERTY
        );

<span class="nc" id="L81">        GenericJackson2JsonRedisSerializer jsonSerializer = new GenericJackson2JsonRedisSerializer(objectMapper);</span>

        // Default cache configuration
<span class="nc" id="L84">        RedisCacheConfiguration defaultConfig = RedisCacheConfiguration.defaultCacheConfig()</span>
<span class="nc" id="L85">                .entryTtl(Duration.ofMinutes(30)) // Default 30 minutes TTL</span>
<span class="nc" id="L86">                .serializeKeysWith(RedisSerializationContext.SerializationPair.fromSerializer(new StringRedisSerializer()))</span>
<span class="nc" id="L87">                .serializeValuesWith(RedisSerializationContext.SerializationPair.fromSerializer(jsonSerializer))</span>
<span class="nc" id="L88">                .disableCachingNullValues();</span>

        // Custom cache configurations for different data types
<span class="nc" id="L91">        Map&lt;String, RedisCacheConfiguration&gt; cacheConfigurations = new HashMap&lt;&gt;();</span>

        // Document metadata cache - 1 hour TTL (frequently accessed, changes moderately)
<span class="nc" id="L94">        cacheConfigurations.put(&quot;documents&quot;, defaultConfig.entryTtl(Duration.ofHours(1)));</span>

        // Document permissions cache - 30 minutes TTL (security-sensitive, moderate changes)
<span class="nc" id="L97">        cacheConfigurations.put(&quot;documentPermissions&quot;, defaultConfig.entryTtl(Duration.ofMinutes(30)));</span>

        // Security configuration cache - 4 hours TTL (rarely changes)
<span class="nc" id="L100">        cacheConfigurations.put(&quot;securityConfig&quot;, defaultConfig.entryTtl(Duration.ofHours(4)));</span>

        // Storage configuration cache - 2 hours TTL (rarely changes)
<span class="nc" id="L103">        cacheConfigurations.put(&quot;storageConfig&quot;, defaultConfig.entryTtl(Duration.ofHours(2)));</span>

        // Search results cache - 15 minutes TTL (frequently updated)
<span class="nc" id="L106">        cacheConfigurations.put(&quot;searchResults&quot;, defaultConfig.entryTtl(Duration.ofMinutes(15)));</span>

        // User context cache - 1 hour TTL (session-based data)
<span class="nc" id="L109">        cacheConfigurations.put(&quot;userContext&quot;, defaultConfig.entryTtl(Duration.ofHours(1)));</span>

        // Audit logs cache - 6 hours TTL (historical data, rarely changes)
<span class="nc" id="L112">        cacheConfigurations.put(&quot;auditLogs&quot;, defaultConfig.entryTtl(Duration.ofHours(6)));</span>

        // Compliance data cache - 2 hours TTL (regulatory data, moderate changes)
<span class="nc" id="L115">        cacheConfigurations.put(&quot;complianceData&quot;, defaultConfig.entryTtl(Duration.ofHours(2)));</span>

        // File metadata cache - 4 hours TTL (file properties, rarely change)
<span class="nc" id="L118">        cacheConfigurations.put(&quot;fileMetadata&quot;, defaultConfig.entryTtl(Duration.ofHours(4)));</span>

        // Query results cache - 10 minutes TTL (database query results)
<span class="nc" id="L121">        cacheConfigurations.put(&quot;queryResults&quot;, defaultConfig.entryTtl(Duration.ofMinutes(10)));</span>

<span class="nc" id="L123">        return RedisCacheManager.builder(connectionFactory)</span>
<span class="nc" id="L124">                .cacheDefaults(defaultConfig)</span>
<span class="nc" id="L125">                .withInitialCacheConfigurations(cacheConfigurations)</span>
<span class="nc" id="L126">                .build();</span>
    }

    /**
     * Redis template for manual cache operations
     */
    @Bean
    @ConditionalOnProperty(name = &quot;spring.cache.type&quot;, havingValue = &quot;redis&quot;)
    public RedisTemplate&lt;String, Object&gt; redisTemplate(RedisConnectionFactory connectionFactory) {
<span class="nc" id="L135">        RedisTemplate&lt;String, Object&gt; template = new RedisTemplate&lt;&gt;();</span>
<span class="nc" id="L136">        template.setConnectionFactory(connectionFactory);</span>

        // Configure serializers
<span class="nc" id="L139">        template.setKeySerializer(new StringRedisSerializer());</span>
<span class="nc" id="L140">        template.setHashKeySerializer(new StringRedisSerializer());</span>

<span class="nc" id="L142">        ObjectMapper objectMapper = new ObjectMapper();</span>
<span class="nc" id="L143">        objectMapper.registerModule(new JavaTimeModule());</span>
<span class="nc" id="L144">        objectMapper.activateDefaultTyping(</span>
<span class="nc" id="L145">            objectMapper.getPolymorphicTypeValidator(),</span>
            ObjectMapper.DefaultTyping.NON_FINAL,
            JsonTypeInfo.As.PROPERTY
        );

<span class="nc" id="L150">        GenericJackson2JsonRedisSerializer jsonSerializer = new GenericJackson2JsonRedisSerializer(objectMapper);</span>
<span class="nc" id="L151">        template.setValueSerializer(jsonSerializer);</span>
<span class="nc" id="L152">        template.setHashValueSerializer(jsonSerializer);</span>

<span class="nc" id="L154">        template.afterPropertiesSet();</span>
<span class="nc" id="L155">        return template;</span>
    }

    /**
     * Redis template for rate limiting operations (String, String)
     */
    @Bean(&quot;stringRedisTemplate&quot;)
    @ConditionalOnProperty(name = &quot;spring.cache.type&quot;, havingValue = &quot;redis&quot;)
    public RedisTemplate&lt;String, String&gt; stringRedisTemplate(RedisConnectionFactory connectionFactory) {
<span class="nc" id="L164">        RedisTemplate&lt;String, String&gt; template = new RedisTemplate&lt;&gt;();</span>
<span class="nc" id="L165">        template.setConnectionFactory(connectionFactory);</span>

        // Configure serializers for String operations
<span class="nc" id="L168">        template.setKeySerializer(new StringRedisSerializer());</span>
<span class="nc" id="L169">        template.setValueSerializer(new StringRedisSerializer());</span>
<span class="nc" id="L170">        template.setHashKeySerializer(new StringRedisSerializer());</span>
<span class="nc" id="L171">        template.setHashValueSerializer(new StringRedisSerializer());</span>

<span class="nc" id="L173">        template.afterPropertiesSet();</span>
<span class="nc" id="L174">        return template;</span>
    }
}
</pre><div class="footer"><span class="right">Created with <a href="http://www.jacoco.org/jacoco">JaCoCo</a> 0.8.10.202304240956</span></div></body></html>