<?xml version="1.0" encoding="UTF-8"?><!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Strict//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-strict.dtd"><html xmlns="http://www.w3.org/1999/xhtml" lang="en"><head><meta http-equiv="Content-Type" content="text/html;charset=UTF-8"/><link rel="stylesheet" href="../jacoco-resources/report.css" type="text/css"/><link rel="shortcut icon" href="../jacoco-resources/report.gif" type="image/gif"/><title>AuthController.java</title><link rel="stylesheet" href="../jacoco-resources/prettify.css" type="text/css"/><script type="text/javascript" src="../jacoco-resources/prettify.js"></script></head><body onload="window['PR_TAB_WIDTH']=4;prettyPrint()"><div class="breadcrumb" id="breadcrumb"><span class="info"><a href="../jacoco-sessions.html" class="el_session">Sessions</a></span><a href="../index.html" class="el_report">dms-svc</a> &gt; <a href="index.source.html" class="el_package">com.ascentbusiness.dms_svc.controller</a> &gt; <span class="el_source">AuthController.java</span></div><h1>AuthController.java</h1><pre class="source lang-java linenums">package com.ascentbusiness.dms_svc.controller;

import com.ascentbusiness.dms_svc.security.JwtTokenProvider;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.security.authentication.AuthenticationManager;
import org.springframework.security.authentication.UsernamePasswordAuthenticationToken;
import org.springframework.security.core.Authentication;
import org.springframework.security.core.AuthenticationException;
import org.springframework.web.bind.annotation.*;

import java.util.HashMap;
import java.util.Map;

/**
 * Authentication controller for handling login requests and JWT token generation.
 * Provides endpoints for user authentication and token management.
 *
 * <AUTHOR> Development Team
 * @version 1.0
 * @since 1.0
 */
@RestController
@RequestMapping(&quot;/auth&quot;)
@CrossOrigin(origins = &quot;*&quot;, maxAge = 3600)
<span class="fc" id="L28">public class AuthController {</span>

<span class="fc" id="L30">    private static final Logger logger = LoggerFactory.getLogger(AuthController.class);</span>

    @Autowired
    private AuthenticationManager authenticationManager;

    @Autowired
    private JwtTokenProvider jwtTokenProvider;

    /**
     * Login request DTO
     */
    public static class LoginRequest {
        private String username;
        private String password;

<span class="nc" id="L45">        public LoginRequest() {}</span>

<span class="nc" id="L47">        public LoginRequest(String username, String password) {</span>
<span class="nc" id="L48">            this.username = username;</span>
<span class="nc" id="L49">            this.password = password;</span>
<span class="nc" id="L50">        }</span>

        public String getUsername() {
<span class="nc" id="L53">            return username;</span>
        }

        public void setUsername(String username) {
<span class="nc" id="L57">            this.username = username;</span>
<span class="nc" id="L58">        }</span>

        public String getPassword() {
<span class="nc" id="L61">            return password;</span>
        }

        public void setPassword(String password) {
<span class="nc" id="L65">            this.password = password;</span>
<span class="nc" id="L66">        }</span>
    }

    /**
     * JWT response DTO
     */
    public static class JwtResponse {
        private String token;
<span class="nc" id="L74">        private String type = &quot;Bearer&quot;;</span>
        private String username;
        private long expiresIn;

<span class="nc" id="L78">        public JwtResponse(String accessToken, String username, long expiresIn) {</span>
<span class="nc" id="L79">            this.token = accessToken;</span>
<span class="nc" id="L80">            this.username = username;</span>
<span class="nc" id="L81">            this.expiresIn = expiresIn;</span>
<span class="nc" id="L82">        }</span>

        public String getToken() {
<span class="nc" id="L85">            return token;</span>
        }

        public void setToken(String token) {
<span class="nc" id="L89">            this.token = token;</span>
<span class="nc" id="L90">        }</span>

        public String getType() {
<span class="nc" id="L93">            return type;</span>
        }

        public void setType(String type) {
<span class="nc" id="L97">            this.type = type;</span>
<span class="nc" id="L98">        }</span>

        public String getUsername() {
<span class="nc" id="L101">            return username;</span>
        }

        public void setUsername(String username) {
<span class="nc" id="L105">            this.username = username;</span>
<span class="nc" id="L106">        }</span>

        public long getExpiresIn() {
<span class="nc" id="L109">            return expiresIn;</span>
        }

        public void setExpiresIn(long expiresIn) {
<span class="nc" id="L113">            this.expiresIn = expiresIn;</span>
<span class="nc" id="L114">        }</span>
    }

    /**
     * Authenticates user and generates JWT token.
     *
     * @param loginRequest Login credentials
     * @return JWT token response
     */
    @PostMapping(&quot;/login&quot;)
    public ResponseEntity&lt;?&gt; authenticateUser(@RequestBody LoginRequest loginRequest) {
<span class="nc" id="L125">        logger.info(&quot;Login attempt for username: {}&quot;, loginRequest.getUsername());</span>

        try {
            // For demo purposes, accept any username/password combination
            // In production, this should authenticate against a real user store
<span class="nc bnc" id="L130" title="All 4 branches missed.">            if (loginRequest.getUsername() == null || loginRequest.getUsername().trim().isEmpty()) {</span>
<span class="nc" id="L131">                logger.warn(&quot;Login failed: Username is required&quot;);</span>
<span class="nc" id="L132">                return ResponseEntity.badRequest()</span>
<span class="nc" id="L133">                    .body(createErrorResponse(&quot;Username is required&quot;));</span>
            }

<span class="nc bnc" id="L136" title="All 4 branches missed.">            if (loginRequest.getPassword() == null || loginRequest.getPassword().trim().isEmpty()) {</span>
<span class="nc" id="L137">                logger.warn(&quot;Login failed: Password is required&quot;);</span>
<span class="nc" id="L138">                return ResponseEntity.badRequest()</span>
<span class="nc" id="L139">                    .body(createErrorResponse(&quot;Password is required&quot;));</span>
            }

            // Create authentication token
<span class="nc" id="L143">            UsernamePasswordAuthenticationToken authToken = </span>
                new UsernamePasswordAuthenticationToken(
<span class="nc" id="L145">                    loginRequest.getUsername(), </span>
<span class="nc" id="L146">                    loginRequest.getPassword()</span>
                );

            // For demo purposes, we'll create a simple authentication without actual validation
            // In production, you would authenticate against a user service or database
<span class="nc" id="L151">            logger.info(&quot;Generating JWT token for user: {}&quot;, loginRequest.getUsername());</span>
            
            // Generate JWT token
<span class="nc" id="L154">            String jwt = jwtTokenProvider.generateTokenFromUsername(loginRequest.getUsername());</span>
<span class="nc" id="L155">            long expirationTime = System.currentTimeMillis() + (24 * 60 * 60 * 1000); // 24 hours in milliseconds</span>

<span class="nc" id="L157">            logger.info(&quot;JWT token generated successfully for user: {}&quot;, loginRequest.getUsername());</span>

<span class="nc" id="L159">            return ResponseEntity.ok(new JwtResponse(jwt, loginRequest.getUsername(), expirationTime));</span>

<span class="nc" id="L161">        } catch (AuthenticationException e) {</span>
<span class="nc" id="L162">            logger.error(&quot;Authentication failed for user: {}&quot;, loginRequest.getUsername(), e);</span>
<span class="nc" id="L163">            return ResponseEntity.badRequest()</span>
<span class="nc" id="L164">                .body(createErrorResponse(&quot;Invalid username or password&quot;));</span>
<span class="nc" id="L165">        } catch (Exception e) {</span>
<span class="nc" id="L166">            logger.error(&quot;Unexpected error during authentication for user: {}&quot;, loginRequest.getUsername(), e);</span>
<span class="nc" id="L167">            return ResponseEntity.internalServerError()</span>
<span class="nc" id="L168">                .body(createErrorResponse(&quot;Authentication service temporarily unavailable&quot;));</span>
        }
    }

    /**
     * Validates JWT token.
     *
     * @param token JWT token to validate
     * @return Token validation response
     */
    @PostMapping(&quot;/validate&quot;)
    public ResponseEntity&lt;?&gt; validateToken(@RequestParam String token) {
<span class="nc" id="L180">        logger.info(&quot;Token validation request received&quot;);</span>

        try {
<span class="nc bnc" id="L183" title="All 2 branches missed.">            if (jwtTokenProvider.validateToken(token)) {</span>
<span class="nc" id="L184">                String username = jwtTokenProvider.getUsernameFromToken(token);</span>
<span class="nc" id="L185">                logger.info(&quot;Token validation successful for user: {}&quot;, username);</span>
                
<span class="nc" id="L187">                Map&lt;String, Object&gt; response = new HashMap&lt;&gt;();</span>
<span class="nc" id="L188">                response.put(&quot;valid&quot;, true);</span>
<span class="nc" id="L189">                response.put(&quot;username&quot;, username);</span>
<span class="nc" id="L190">                response.put(&quot;message&quot;, &quot;Token is valid&quot;);</span>
                
<span class="nc" id="L192">                return ResponseEntity.ok(response);</span>
            } else {
<span class="nc" id="L194">                logger.warn(&quot;Token validation failed: Invalid token&quot;);</span>
<span class="nc" id="L195">                return ResponseEntity.badRequest()</span>
<span class="nc" id="L196">                    .body(createErrorResponse(&quot;Invalid token&quot;));</span>
            }
<span class="nc" id="L198">        } catch (Exception e) {</span>
<span class="nc" id="L199">            logger.error(&quot;Error during token validation&quot;, e);</span>
<span class="nc" id="L200">            return ResponseEntity.internalServerError()</span>
<span class="nc" id="L201">                .body(createErrorResponse(&quot;Token validation service temporarily unavailable&quot;));</span>
        }
    }

    /**
     * Health check endpoint for authentication service.
     *
     * @return Service health status
     */
    @GetMapping(&quot;/health&quot;)
    public ResponseEntity&lt;?&gt; health() {
<span class="nc" id="L212">        Map&lt;String, Object&gt; response = new HashMap&lt;&gt;();</span>
<span class="nc" id="L213">        response.put(&quot;status&quot;, &quot;UP&quot;);</span>
<span class="nc" id="L214">        response.put(&quot;service&quot;, &quot;Authentication Service&quot;);</span>
<span class="nc" id="L215">        response.put(&quot;timestamp&quot;, System.currentTimeMillis());</span>
        
<span class="nc" id="L217">        return ResponseEntity.ok(response);</span>
    }

    /**
     * Creates a standardized error response.
     *
     * @param message Error message
     * @return Error response map
     */
    private Map&lt;String, Object&gt; createErrorResponse(String message) {
<span class="nc" id="L227">        Map&lt;String, Object&gt; response = new HashMap&lt;&gt;();</span>
<span class="nc" id="L228">        response.put(&quot;error&quot;, true);</span>
<span class="nc" id="L229">        response.put(&quot;message&quot;, message);</span>
<span class="nc" id="L230">        response.put(&quot;timestamp&quot;, System.currentTimeMillis());</span>
<span class="nc" id="L231">        return response;</span>
    }
}
</pre><div class="footer"><span class="right">Created with <a href="http://www.jacoco.org/jacoco">JaCoCo</a> 0.8.10.202304240956</span></div></body></html>