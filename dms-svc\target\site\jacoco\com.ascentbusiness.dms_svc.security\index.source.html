<?xml version="1.0" encoding="UTF-8"?><!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Strict//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-strict.dtd"><html xmlns="http://www.w3.org/1999/xhtml" lang="en"><head><meta http-equiv="Content-Type" content="text/html;charset=UTF-8"/><link rel="stylesheet" href="../jacoco-resources/report.css" type="text/css"/><link rel="shortcut icon" href="../jacoco-resources/report.gif" type="image/gif"/><title>com.ascentbusiness.dms_svc.security</title><script type="text/javascript" src="../jacoco-resources/sort.js"></script></head><body onload="initialSort(['breadcrumb', 'coveragetable'])"><div class="breadcrumb" id="breadcrumb"><span class="info"><a href="index.html" class="el_class">Classes</a><a href="../jacoco-sessions.html" class="el_session">Sessions</a></span><a href="../index.html" class="el_report">dms-svc</a> &gt; <span class="el_package">com.ascentbusiness.dms_svc.security</span></div><h1>com.ascentbusiness.dms_svc.security</h1><table class="coverage" cellspacing="0" id="coveragetable"><thead><tr><td class="sortable" id="a" onclick="toggleSort(this)">Element</td><td class="down sortable bar" id="b" onclick="toggleSort(this)">Missed Instructions</td><td class="sortable ctr2" id="c" onclick="toggleSort(this)">Cov.</td><td class="sortable bar" id="d" onclick="toggleSort(this)">Missed Branches</td><td class="sortable ctr2" id="e" onclick="toggleSort(this)">Cov.</td><td class="sortable ctr1" id="f" onclick="toggleSort(this)">Missed</td><td class="sortable ctr2" id="g" onclick="toggleSort(this)">Cxty</td><td class="sortable ctr1" id="h" onclick="toggleSort(this)">Missed</td><td class="sortable ctr2" id="i" onclick="toggleSort(this)">Lines</td><td class="sortable ctr1" id="j" onclick="toggleSort(this)">Missed</td><td class="sortable ctr2" id="k" onclick="toggleSort(this)">Methods</td><td class="sortable ctr1" id="l" onclick="toggleSort(this)">Missed</td><td class="sortable ctr2" id="m" onclick="toggleSort(this)">Classes</td></tr></thead><tfoot><tr><td>Total</td><td class="bar">775 of 803</td><td class="ctr2">3%</td><td class="bar">74 of 74</td><td class="ctr2">0%</td><td class="ctr1">81</td><td class="ctr2">89</td><td class="ctr1">217</td><td class="ctr2">225</td><td class="ctr1">44</td><td class="ctr2">52</td><td class="ctr1">1</td><td class="ctr2">5</td></tr></tfoot><tbody><tr><td id="a1"><a href="JwtAuthenticationFilter.java.html" class="el_source">JwtAuthenticationFilter.java</a></td><td class="bar" id="b0"><img src="../jacoco-resources/redbar.gif" width="116" height="10" title="253" alt="253"/><img src="../jacoco-resources/greenbar.gif" width="3" height="10" title="7" alt="7"/></td><td class="ctr2" id="c3">2%</td><td class="bar" id="d0"><img src="../jacoco-resources/redbar.gif" width="120" height="10" title="34" alt="34"/></td><td class="ctr2" id="e0">0%</td><td class="ctr1" id="f1">20</td><td class="ctr2" id="g1">22</td><td class="ctr1" id="h1">62</td><td class="ctr2" id="i1">64</td><td class="ctr1" id="j3">3</td><td class="ctr2" id="k3">5</td><td class="ctr1" id="l1">0</td><td class="ctr2" id="m0">1</td></tr><tr><td id="a2"><a href="JwtTokenProvider.java.html" class="el_source">JwtTokenProvider.java</a></td><td class="bar" id="b1"><img src="../jacoco-resources/redbar.gif" width="100" height="10" title="217" alt="217"/><img src="../jacoco-resources/greenbar.gif" width="3" height="10" title="7" alt="7"/></td><td class="ctr2" id="c2">3%</td><td class="bar" id="d2"><img src="../jacoco-resources/redbar.gif" width="14" height="10" title="4" alt="4"/></td><td class="ctr2" id="e1">0%</td><td class="ctr1" id="f3">11</td><td class="ctr2" id="g3">13</td><td class="ctr1" id="h0">72</td><td class="ctr2" id="i0">74</td><td class="ctr1" id="j2">9</td><td class="ctr2" id="k2">11</td><td class="ctr1" id="l2">0</td><td class="ctr2" id="m1">1</td></tr><tr><td id="a3"><a href="UserContext.java.html" class="el_source">UserContext.java</a></td><td class="bar" id="b2"><img src="../jacoco-resources/redbar.gif" width="94" height="10" title="205" alt="205"/><img src="../jacoco-resources/greenbar.gif" width="3" height="10" title="7" alt="7"/></td><td class="ctr2" id="c1">3%</td><td class="bar" id="d1"><img src="../jacoco-resources/redbar.gif" width="112" height="10" title="32" alt="32"/></td><td class="ctr2" id="e2">0%</td><td class="ctr1" id="f0">32</td><td class="ctr2" id="g0">34</td><td class="ctr1" id="h2">54</td><td class="ctr2" id="i2">56</td><td class="ctr1" id="j0">16</td><td class="ctr2" id="k0">18</td><td class="ctr1" id="l3">0</td><td class="ctr2" id="m2">1</td></tr><tr><td id="a4"><a href="UserPrincipal.java.html" class="el_source">UserPrincipal.java</a></td><td class="bar" id="b3"><img src="../jacoco-resources/redbar.gif" width="41" height="10" title="90" alt="90"/></td><td class="ctr2" id="c4">0%</td><td class="bar" id="d3"><img src="../jacoco-resources/redbar.gif" width="14" height="10" title="4" alt="4"/></td><td class="ctr2" id="e3">0%</td><td class="ctr1" id="f2">17</td><td class="ctr2" id="g2">17</td><td class="ctr1" id="h3">26</td><td class="ctr2" id="i3">26</td><td class="ctr1" id="j1">15</td><td class="ctr2" id="k1">15</td><td class="ctr1" id="l0">1</td><td class="ctr2" id="m3">1</td></tr><tr><td id="a0"><a href="JwtAuthenticationEntryPoint.java.html" class="el_source">JwtAuthenticationEntryPoint.java</a></td><td class="bar" id="b4"><img src="../jacoco-resources/redbar.gif" width="4" height="10" title="10" alt="10"/><img src="../jacoco-resources/greenbar.gif" width="3" height="10" title="7" alt="7"/></td><td class="ctr2" id="c0">41%</td><td class="bar" id="d4"/><td class="ctr2" id="e4">n/a</td><td class="ctr1" id="f4">1</td><td class="ctr2" id="g4">3</td><td class="ctr1" id="h4">3</td><td class="ctr2" id="i4">5</td><td class="ctr1" id="j4">1</td><td class="ctr2" id="k4">3</td><td class="ctr1" id="l4">0</td><td class="ctr2" id="m4">1</td></tr></tbody></table><div class="footer"><span class="right">Created with <a href="http://www.jacoco.org/jacoco">JaCoCo</a> 0.8.10.202304240956</span></div></body></html>