D:\grc-platform-v4\dms-svc\src\main\java\com\ascentbusiness\dms_svc\config\FileProcessingConfig.java
D:\grc-platform-v4\dms-svc\src\main\java\com\ascentbusiness\dms_svc\enums\ApprovalType.java
D:\grc-platform-v4\dms-svc\src\main\java\com\ascentbusiness\dms_svc\service\UrlDownloadService.java
D:\grc-platform-v4\dms-svc\src\main\java\com\ascentbusiness\dms_svc\dto\DiagnosticSummary.java
D:\grc-platform-v4\dms-svc\src\main\java\com\ascentbusiness\dms_svc\dto\BulkActionResult.java
D:\grc-platform-v4\dms-svc\src\main\java\com\ascentbusiness\dms_svc\dto\DocumentVersion.java
D:\grc-platform-v4\dms-svc\src\main\java\com\ascentbusiness\dms_svc\service\MigrationSecurityService.java
D:\grc-platform-v4\dms-svc\src\main\java\com\ascentbusiness\dms_svc\dto\DocumentPermissionListResponse.java
D:\grc-platform-v4\dms-svc\src\main\java\com\ascentbusiness\dms_svc\config\WordConversionConfig.java
D:\grc-platform-v4\dms-svc\src\main\java\com\ascentbusiness\dms_svc\enums\ConversionType.java
D:\grc-platform-v4\dms-svc\src\main\java\com\ascentbusiness\dms_svc\exception\InvalidTokenException.java
D:\grc-platform-v4\dms-svc\src\main\java\com\ascentbusiness\dms_svc\entity\TemplatePermission.java
D:\grc-platform-v4\dms-svc\src\main\java\com\ascentbusiness\dms_svc\service\virus\VirusScanningService.java
D:\grc-platform-v4\dms-svc\src\main\java\com\ascentbusiness\dms_svc\repository\AuditVerificationLogRepository.java
D:\grc-platform-v4\dms-svc\src\main\java\com\ascentbusiness\dms_svc\resolver\WorkflowInstanceResolver.java
D:\grc-platform-v4\dms-svc\src\main\java\com\ascentbusiness\dms_svc\repository\DocumentPermissionRepository.java
D:\grc-platform-v4\dms-svc\src\main\java\com\ascentbusiness\dms_svc\repository\DocumentComplianceMetadataRepository.java
D:\grc-platform-v4\dms-svc\src\main\java\com\ascentbusiness\dms_svc\dto\ConnectionTestInput.java
D:\grc-platform-v4\dms-svc\src\main\java\com\ascentbusiness\dms_svc\service\DocumentMetadataService.java
D:\grc-platform-v4\dms-svc\src\main\java\com\ascentbusiness\dms_svc\enums\TemplateType.java
D:\grc-platform-v4\dms-svc\src\main\java\com\ascentbusiness\dms_svc\entity\TemplateCategory.java
D:\grc-platform-v4\dms-svc\src\main\java\com\ascentbusiness\dms_svc\service\WorkflowInstanceService.java
D:\grc-platform-v4\dms-svc\src\main\java\com\ascentbusiness\dms_svc\dto\ChunkUploadInput.java
D:\grc-platform-v4\dms-svc\src\main\java\com\ascentbusiness\dms_svc\dto\LegalHoldInput.java
D:\grc-platform-v4\dms-svc\src\main\java\com\ascentbusiness\dms_svc\config\SecurityConfig.java
D:\grc-platform-v4\dms-svc\src\main\java\com\ascentbusiness\dms_svc\dto\DocumentAccessRole.java
D:\grc-platform-v4\dms-svc\src\main\java\com\ascentbusiness\dms_svc\dto\DocumentPage.java
D:\grc-platform-v4\dms-svc\src\main\java\com\ascentbusiness\dms_svc\repository\ChunkedUploadChunkRepository.java
D:\grc-platform-v4\dms-svc\src\main\java\com\ascentbusiness\dms_svc\dto\CpuUsage.java
D:\grc-platform-v4\dms-svc\src\main\java\com\ascentbusiness\dms_svc\dto\SupportedConversion.java
D:\grc-platform-v4\dms-svc\src\main\java\com\ascentbusiness\dms_svc\dto\HealthMetric.java
D:\grc-platform-v4\dms-svc\src\main\java\com\ascentbusiness\dms_svc\dto\DocumentClassificationMetadataInput.java
D:\grc-platform-v4\dms-svc\src\main\java\com\ascentbusiness\dms_svc\dto\TemplateValidationStatus.java
D:\grc-platform-v4\dms-svc\src\main\java\com\ascentbusiness\dms_svc\repository\BulkShareItemRepository.java
D:\grc-platform-v4\dms-svc\src\main\java\com\ascentbusiness\dms_svc\dto\ValidationSeverity.java
D:\grc-platform-v4\dms-svc\src\main\java\com\ascentbusiness\dms_svc\config\SecurityHeadersConfig.java
D:\grc-platform-v4\dms-svc\src\main\java\com\ascentbusiness\dms_svc\enums\SubscriberType.java
D:\grc-platform-v4\dms-svc\src\main\java\com\ascentbusiness\dms_svc\dto\DocumentUploadResult.java
D:\grc-platform-v4\dms-svc\src\main\java\com\ascentbusiness\dms_svc\resolver\ConversionGraphQLResolver.java
D:\grc-platform-v4\dms-svc\src\main\java\com\ascentbusiness\dms_svc\dto\AdvancedSearchResult.java
D:\grc-platform-v4\dms-svc\src\main\java\com\ascentbusiness\dms_svc\dto\UploadDocumentExInput.java
D:\grc-platform-v4\dms-svc\src\main\java\com\ascentbusiness\dms_svc\dto\AuditLogResponse.java
D:\grc-platform-v4\dms-svc\src\main\java\com\ascentbusiness\dms_svc\dto\AccessSharedDocumentResponse.java
D:\grc-platform-v4\dms-svc\src\main\java\com\ascentbusiness\dms_svc\entity\DocumentOwnershipMetadata.java
D:\grc-platform-v4\dms-svc\src\main\java\com\ascentbusiness\dms_svc\dto\BatchConversionResult.java
D:\grc-platform-v4\dms-svc\src\main\java\com\ascentbusiness\dms_svc\dto\FileValidationResult.java
D:\grc-platform-v4\dms-svc\src\main\java\com\ascentbusiness\dms_svc\enums\VirusScannerType.java
D:\grc-platform-v4\dms-svc\src\main\java\com\ascentbusiness\dms_svc\dto\PaginationInput.java
D:\grc-platform-v4\dms-svc\src\main\java\com\ascentbusiness\dms_svc\dto\BulkProcessingOptionsInput.java
D:\grc-platform-v4\dms-svc\src\main\java\com\ascentbusiness\dms_svc\entity\DocumentTemplate.java
D:\grc-platform-v4\dms-svc\src\main\java\com\ascentbusiness\dms_svc\resolver\AuditGraphQLResolver.java
D:\grc-platform-v4\dms-svc\src\main\java\com\ascentbusiness\dms_svc\util\XssProtectionUtil.java
D:\grc-platform-v4\dms-svc\src\main\java\com\ascentbusiness\dms_svc\dto\ConversionOptionsInput.java
D:\grc-platform-v4\dms-svc\src\main\java\com\ascentbusiness\dms_svc\enums\ProcessingStrategy.java
D:\grc-platform-v4\dms-svc\src\main\java\com\ascentbusiness\dms_svc\controller\DocumentDownloadController.java
D:\grc-platform-v4\dms-svc\src\main\java\com\ascentbusiness\dms_svc\dto\DiagnosticTestResult.java
D:\grc-platform-v4\dms-svc\src\main\java\com\ascentbusiness\dms_svc\exception\DmsSystemException.java
D:\grc-platform-v4\dms-svc\src\main\java\com\ascentbusiness\dms_svc\dto\ErrorResponse.java
D:\grc-platform-v4\dms-svc\src\main\java\com\ascentbusiness\dms_svc\util\AuditUtil.java
D:\grc-platform-v4\dms-svc\src\main\java\com\ascentbusiness\dms_svc\exception\SharePointAuthenticationException.java
D:\grc-platform-v4\dms-svc\src\main\java\com\ascentbusiness\dms_svc\resolver\RetentionPolicyResolver.java
D:\grc-platform-v4\dms-svc\src\main\java\com\ascentbusiness\dms_svc\service\TestCaseService.java
D:\grc-platform-v4\dms-svc\src\main\java\com\ascentbusiness\dms_svc\repository\WebhookDeliveryRepository.java
D:\grc-platform-v4\dms-svc\src\main\java\com\ascentbusiness\dms_svc\dto\DiagnosticTestInput.java
D:\grc-platform-v4\dms-svc\src\main\java\com\ascentbusiness\dms_svc\health\DmsBusinessHealthIndicator.java
D:\grc-platform-v4\dms-svc\src\main\java\com\ascentbusiness\dms_svc\entity\BaseEntity.java
D:\grc-platform-v4\dms-svc\src\main\java\com\ascentbusiness\dms_svc\service\AsyncDocumentProcessor.java
D:\grc-platform-v4\dms-svc\src\main\java\com\ascentbusiness\dms_svc\repository\RetentionPolicyRepository.java
D:\grc-platform-v4\dms-svc\src\main\java\com\ascentbusiness\dms_svc\dto\TemplateActionResult.java
D:\grc-platform-v4\dms-svc\src\main\java\com\ascentbusiness\dms_svc\dto\FileInfo.java
D:\grc-platform-v4\dms-svc\src\main\java\com\ascentbusiness\dms_svc\entity\DocumentClassificationMetadata.java
D:\grc-platform-v4\dms-svc\src\main\java\com\ascentbusiness\dms_svc\resolver\SecurityViolationResolver.java
D:\grc-platform-v4\dms-svc\src\main\java\com\ascentbusiness\dms_svc\config\GraphQLMultipartConfig.java
D:\grc-platform-v4\dms-svc\src\main\java\com\ascentbusiness\dms_svc\exception\DmsSecurityException.java
D:\grc-platform-v4\dms-svc\src\main\java\com\ascentbusiness\dms_svc\entity\WorkflowTask.java
D:\grc-platform-v4\dms-svc\src\main\java\com\ascentbusiness\dms_svc\service\PdfToWordConversionService.java
D:\grc-platform-v4\dms-svc\src\main\java\com\ascentbusiness\dms_svc\dto\BulkUploadInput.java
D:\grc-platform-v4\dms-svc\src\main\java\com\ascentbusiness\dms_svc\dto\ComponentHealthDetails.java
D:\grc-platform-v4\dms-svc\src\main\java\com\ascentbusiness\dms_svc\dto\AsyncJobStatus.java
D:\grc-platform-v4\dms-svc\src\main\java\com\ascentbusiness\dms_svc\service\AuditLogBuilder.java
D:\grc-platform-v4\dms-svc\src\main\java\com\ascentbusiness\dms_svc\resolver\TracingGraphQLResolver.java
D:\grc-platform-v4\dms-svc\src\main\java\com\ascentbusiness\dms_svc\service\BusinessMetricsService.java
D:\grc-platform-v4\dms-svc\src\main\java\com\ascentbusiness\dms_svc\interceptor\CorrelationIdInterceptor.java
D:\grc-platform-v4\dms-svc\src\main\java\com\ascentbusiness\dms_svc\dto\ChunkedUploadInitInput.java
D:\grc-platform-v4\dms-svc\src\main\java\com\ascentbusiness\dms_svc\dto\TemplatePreviewInput.java
D:\grc-platform-v4\dms-svc\src\main\java\com\ascentbusiness\dms_svc\dto\ChunkUploadStatus.java
D:\grc-platform-v4\dms-svc\src\main\java\com\ascentbusiness\dms_svc\service\S3StorageService.java
D:\grc-platform-v4\dms-svc\src\main\java\com\ascentbusiness\dms_svc\dto\ConnectionPool.java
D:\grc-platform-v4\dms-svc\src\main\java\com\ascentbusiness\dms_svc\exception\InvalidShareLinkException.java
D:\grc-platform-v4\dms-svc\src\main\java\com\ascentbusiness\dms_svc\dto\TemplateValidationError.java
D:\grc-platform-v4\dms-svc\src\main\java\com\ascentbusiness\dms_svc\dto\ConversionStatistics.java
D:\grc-platform-v4\dms-svc\src\main\java\com\ascentbusiness\dms_svc\dto\RevokePermissionInput.java
D:\grc-platform-v4\dms-svc\src\main\java\com\ascentbusiness\dms_svc\service\AuditService.java
D:\grc-platform-v4\dms-svc\src\main\java\com\ascentbusiness\dms_svc\dto\RetentionPolicyInput.java
D:\grc-platform-v4\dms-svc\src\main\java\com\ascentbusiness\dms_svc\dto\TestCaseCollection.java
D:\grc-platform-v4\dms-svc\src\main\java\com\ascentbusiness\dms_svc\config\ElasticsearchRepositoryConfig.java
D:\grc-platform-v4\dms-svc\src\main\java\com\ascentbusiness\dms_svc\dto\DispositionReviewInput.java
D:\grc-platform-v4\dms-svc\src\main\java\com\ascentbusiness\dms_svc\entity\TemplateVersion.java
D:\grc-platform-v4\dms-svc\src\main\java\com\ascentbusiness\dms_svc\repository\DocumentTemplateRepository.java
D:\grc-platform-v4\dms-svc\src\main\java\com\ascentbusiness\dms_svc\dto\AuditVerificationResponse.java
D:\grc-platform-v4\dms-svc\src\main\java\com\ascentbusiness\dms_svc\enums\AuditEventCategory.java
D:\grc-platform-v4\dms-svc\src\main\java\com\ascentbusiness\dms_svc\repository\StorageConfigurationRepository.java
D:\grc-platform-v4\dms-svc\src\main\java\com\ascentbusiness\dms_svc\entity\RetentionPolicyAssignment.java
D:\grc-platform-v4\dms-svc\src\main\java\com\ascentbusiness\dms_svc\repository\WorkflowDefinitionRepository.java
D:\grc-platform-v4\dms-svc\src\main\java\com\ascentbusiness\dms_svc\dto\DiagnosticResult.java
D:\grc-platform-v4\dms-svc\src\main\java\com\ascentbusiness\dms_svc\dto\DocumentPermissionInput.java
D:\grc-platform-v4\dms-svc\src\main\java\com\ascentbusiness\dms_svc\enums\ExportFormat.java
D:\grc-platform-v4\dms-svc\src\main\java\com\ascentbusiness\dms_svc\exception\SharePointException.java
D:\grc-platform-v4\dms-svc\src\main\java\com\ascentbusiness\dms_svc\service\StorageConfigurationInitializer.java
D:\grc-platform-v4\dms-svc\src\main\java\com\ascentbusiness\dms_svc\dto\DocumentUpdateInput.java
D:\grc-platform-v4\dms-svc\src\main\java\com\ascentbusiness\dms_svc\resolver\DocumentShareResolver.java
D:\grc-platform-v4\dms-svc\src\main\java\com\ascentbusiness\dms_svc\dto\BulkAuditVerificationResponse.java
D:\grc-platform-v4\dms-svc\src\main\java\com\ascentbusiness\dms_svc\dto\UploadDocumentFromPathInput.java
D:\grc-platform-v4\dms-svc\src\main\java\com\ascentbusiness\dms_svc\resolver\MarkdownConversionResolver.java
D:\grc-platform-v4\dms-svc\src\main\java\com\ascentbusiness\dms_svc\exception\DmsBusinessException.java
D:\grc-platform-v4\dms-svc\src\main\java\com\ascentbusiness\dms_svc\config\AwsS3Config.java
D:\grc-platform-v4\dms-svc\src\main\java\com\ascentbusiness\dms_svc\dto\ConversionTrendData.java
D:\grc-platform-v4\dms-svc\src\main\java\com\ascentbusiness\dms_svc\dto\UploadNewVersionInput.java
D:\grc-platform-v4\dms-svc\src\main\java\com\ascentbusiness\dms_svc\enums\TemplateFieldType.java
D:\grc-platform-v4\dms-svc\src\main\java\com\ascentbusiness\dms_svc\entity\WorkflowTemplate.java
D:\grc-platform-v4\dms-svc\src\main\java\com\ascentbusiness\dms_svc\service\WorkflowDefinitionService.java
D:\grc-platform-v4\dms-svc\src\main\java\com\ascentbusiness\dms_svc\service\AuditEncryptionService.java
D:\grc-platform-v4\dms-svc\src\main\java\com\ascentbusiness\dms_svc\health\RedisHealthIndicator.java
D:\grc-platform-v4\dms-svc\src\main\java\com\ascentbusiness\dms_svc\dto\BulkDocumentPermissionInput.java
D:\grc-platform-v4\dms-svc\src\main\java\com\ascentbusiness\dms_svc\dto\TemplateBulkActionParameters.java
D:\grc-platform-v4\dms-svc\src\main\java\com\ascentbusiness\dms_svc\exception\FileTooLargeException.java
D:\grc-platform-v4\dms-svc\src\main\java\com\ascentbusiness\dms_svc\health\ElasticsearchHealthIndicator.java
D:\grc-platform-v4\dms-svc\src\main\java\com\ascentbusiness\dms_svc\filter\SecurityHeadersFilter.java
D:\grc-platform-v4\dms-svc\src\main\java\com\ascentbusiness\dms_svc\enums\ConnectionTestType.java
D:\grc-platform-v4\dms-svc\src\main\java\com\ascentbusiness\dms_svc\service\BulkShareService.java
D:\grc-platform-v4\dms-svc\src\main\java\com\ascentbusiness\dms_svc\dto\SearchFacets.java
D:\grc-platform-v4\dms-svc\src\main\java\com\ascentbusiness\dms_svc\enums\Permission.java
D:\grc-platform-v4\dms-svc\src\main\java\com\ascentbusiness\dms_svc\enums\ComplianceFrameworkType.java
D:\grc-platform-v4\dms-svc\src\main\java\com\ascentbusiness\dms_svc\enums\RetentionPeriodUnit.java
D:\grc-platform-v4\dms-svc\src\main\java\com\ascentbusiness\dms_svc\resolver\WebhookEndpointResolver.java
D:\grc-platform-v4\dms-svc\src\main\java\com\ascentbusiness\dms_svc\dto\HealthSummary.java
D:\grc-platform-v4\dms-svc\src\main\java\com\ascentbusiness\dms_svc\service\CacheService.java
D:\grc-platform-v4\dms-svc\src\main\java\com\ascentbusiness\dms_svc\dto\AccessSharedDocumentInput.java
D:\grc-platform-v4\dms-svc\src\main\java\com\ascentbusiness\dms_svc\dto\ConversionInput.java
D:\grc-platform-v4\dms-svc\src\main\java\com\ascentbusiness\dms_svc\entity\EventTemplate.java
D:\grc-platform-v4\dms-svc\src\main\java\com\ascentbusiness\dms_svc\entity\TemplateUsageHistory.java
D:\grc-platform-v4\dms-svc\src\main\java\com\ascentbusiness\dms_svc\dto\RetentionPolicyAssignmentInput.java
D:\grc-platform-v4\dms-svc\src\main\java\com\ascentbusiness\dms_svc\dto\BulkShareInput.java
D:\grc-platform-v4\dms-svc\src\main\java\com\ascentbusiness\dms_svc\dto\TemplateCategoryStats.java
D:\grc-platform-v4\dms-svc\src\main\java\com\ascentbusiness\dms_svc\service\RetentionPolicyService.java
D:\grc-platform-v4\dms-svc\src\main\java\com\ascentbusiness\dms_svc\dto\UploadFromPathOrUrlInput.java
D:\grc-platform-v4\dms-svc\src\main\java\com\ascentbusiness\dms_svc\dto\JwtTokenRequest.java
D:\grc-platform-v4\dms-svc\src\main\java\com\ascentbusiness\dms_svc\enums\ConversionMethod.java
D:\grc-platform-v4\dms-svc\src\main\java\com\ascentbusiness\dms_svc\dto\FormatPopularity.java
D:\grc-platform-v4\dms-svc\src\main\java\com\ascentbusiness\dms_svc\dto\MarginsInput.java
D:\grc-platform-v4\dms-svc\src\main\java\com\ascentbusiness\dms_svc\exception\WordConversionException.java
D:\grc-platform-v4\dms-svc\src\main\java\com\ascentbusiness\dms_svc\service\virus\VirusScannerFactory.java
D:\grc-platform-v4\dms-svc\src\main\java\com\ascentbusiness\dms_svc\dto\DocumentActivityCount.java
D:\grc-platform-v4\dms-svc\src\main\java\com\ascentbusiness\dms_svc\dto\DocumentUploadInput.java
D:\grc-platform-v4\dms-svc\src\main\java\com\ascentbusiness\dms_svc\enums\TemplateCategory.java
D:\grc-platform-v4\dms-svc\src\main\java\com\ascentbusiness\dms_svc\service\ChunkedUploadManager.java
D:\grc-platform-v4\dms-svc\src\main\java\com\ascentbusiness\dms_svc\entity\SecurityConfig.java
D:\grc-platform-v4\dms-svc\src\main\java\com\ascentbusiness\dms_svc\config\UrlUploadConfig.java
D:\grc-platform-v4\dms-svc\src\main\java\com\ascentbusiness\dms_svc\config\AuditEncryptionConfig.java
D:\grc-platform-v4\dms-svc\src\main\java\com\ascentbusiness\dms_svc\entity\EventSubscription.java
D:\grc-platform-v4\dms-svc\src\main\java\com\ascentbusiness\dms_svc\repository\SecurityConfigRepository.java
D:\grc-platform-v4\dms-svc\src\main\java\com\ascentbusiness\dms_svc\resolver\DocumentRetentionResolver.java
D:\grc-platform-v4\dms-svc\src\main\java\com\ascentbusiness\dms_svc\exception\DocumentNotFoundException.java
D:\grc-platform-v4\dms-svc\src\main\java\com\ascentbusiness\dms_svc\dto\TestCaseResponse.java
D:\grc-platform-v4\dms-svc\src\main\java\com\ascentbusiness\dms_svc\service\ElasticsearchService.java
D:\grc-platform-v4\dms-svc\src\main\java\com\ascentbusiness\dms_svc\config\WebClientConfig.java
D:\grc-platform-v4\dms-svc\src\main\java\com\ascentbusiness\dms_svc\service\EventService.java
D:\grc-platform-v4\dms-svc\src\main\java\com\ascentbusiness\dms_svc\dto\UploadResponse.java
D:\grc-platform-v4\dms-svc\src\main\java\com\ascentbusiness\dms_svc\repository\AuditExportRepository.java
D:\grc-platform-v4\dms-svc\src\main\java\com\ascentbusiness\dms_svc\dto\AuditExportRequest.java
D:\grc-platform-v4\dms-svc\src\main\java\com\ascentbusiness\dms_svc\entity\BulkShareItem.java
D:\grc-platform-v4\dms-svc\src\main\java\com\ascentbusiness\dms_svc\repository\ChunkedUploadSessionRepository.java
D:\grc-platform-v4\dms-svc\src\main\java\com\ascentbusiness\dms_svc\util\TempDirectoryUtil.java
D:\grc-platform-v4\dms-svc\src\main\java\com\ascentbusiness\dms_svc\security\UserPrincipal.java
D:\grc-platform-v4\dms-svc\src\main\java\com\ascentbusiness\dms_svc\dto\AuditLogFilterInput.java
D:\grc-platform-v4\dms-svc\src\main\java\com\ascentbusiness\dms_svc\dto\CompleteChunkedUploadInput.java
D:\grc-platform-v4\dms-svc\src\main\java\com\ascentbusiness\dms_svc\entity\StorageConfiguration.java
D:\grc-platform-v4\dms-svc\src\main\java\com\ascentbusiness\dms_svc\config\CacheConfig.java
D:\grc-platform-v4\dms-svc\src\main\java\com\ascentbusiness\dms_svc\resolver\WordConversionResolver.java
D:\grc-platform-v4\dms-svc\src\main\java\com\ascentbusiness\dms_svc\dto\BulkDocumentUploadInput.java
D:\grc-platform-v4\dms-svc\src\main\java\com\ascentbusiness\dms_svc\entity\SystemEvent.java
D:\grc-platform-v4\dms-svc\src\main\java\com\ascentbusiness\dms_svc\entity\WorkflowNotification.java
D:\grc-platform-v4\dms-svc\src\main\java\com\ascentbusiness\dms_svc\dto\DocumentSearchInput.java
D:\grc-platform-v4\dms-svc\src\main\java\com\ascentbusiness\dms_svc\aspect\DatabaseTracingAspect.java
D:\grc-platform-v4\dms-svc\src\main\java\com\ascentbusiness\dms_svc\util\TracingUtil.java
D:\grc-platform-v4\dms-svc\src\main\java\com\ascentbusiness\dms_svc\enums\LegalHoldStatus.java
D:\grc-platform-v4\dms-svc\src\main\java\com\ascentbusiness\dms_svc\exception\VirusScanException.java
D:\grc-platform-v4\dms-svc\src\main\java\com\ascentbusiness\dms_svc\service\BulkUploadService.java
D:\grc-platform-v4\dms-svc\src\main\java\com\ascentbusiness\dms_svc\config\ElasticsearchConfig.java
D:\grc-platform-v4\dms-svc\src\main\java\com\ascentbusiness\dms_svc\config\PIIEncryptionConfig.java
D:\grc-platform-v4\dms-svc\src\main\java\com\ascentbusiness\dms_svc\dto\BulkActionInput.java
D:\grc-platform-v4\dms-svc\src\main\java\com\ascentbusiness\dms_svc\dto\MimeTypeCount.java
D:\grc-platform-v4\dms-svc\src\main\java\com\ascentbusiness\dms_svc\service\RetentionProcessingService.java
D:\grc-platform-v4\dms-svc\src\main\java\com\ascentbusiness\dms_svc\resolver\AuthResolver.java
D:\grc-platform-v4\dms-svc\src\main\java\com\ascentbusiness\dms_svc\controller\AuthController.java
D:\grc-platform-v4\dms-svc\src\main\java\com\ascentbusiness\dms_svc\dto\ShareLinkResponse.java
D:\grc-platform-v4\dms-svc\src\main\java\com\ascentbusiness\dms_svc\filter\CorrelationIdFilter.java
D:\grc-platform-v4\dms-svc\src\main\java\com\ascentbusiness\dms_svc\entity\WorkflowDefinition.java
D:\grc-platform-v4\dms-svc\src\main\java\com\ascentbusiness\dms_svc\repository\DocumentVersionRepository.java
D:\grc-platform-v4\dms-svc\src\main\java\com\ascentbusiness\dms_svc\enums\DataSubjectCategory.java
D:\grc-platform-v4\dms-svc\src\main\java\com\ascentbusiness\dms_svc\service\AuditCryptographyService.java
D:\grc-platform-v4\dms-svc\src\main\java\com\ascentbusiness\dms_svc\dto\HealthIssue.java
D:\grc-platform-v4\dms-svc\src\main\java\com\ascentbusiness\dms_svc\util\ByteArrayMultipartFile.java
D:\grc-platform-v4\dms-svc\src\main\java\com\ascentbusiness\dms_svc\service\DocumentService.java
D:\grc-platform-v4\dms-svc\src\main\java\com\ascentbusiness\dms_svc\dto\UploadDocumentInput.java
D:\grc-platform-v4\dms-svc\src\main\java\com\ascentbusiness\dms_svc\enums\VirusScanResult.java
D:\grc-platform-v4\dms-svc\src\main\java\com\ascentbusiness\dms_svc\DmsSvcApplication.java
D:\grc-platform-v4\dms-svc\src\main\java\com\ascentbusiness\dms_svc\dto\ActionCount.java
D:\grc-platform-v4\dms-svc\src\main\java\com\ascentbusiness\dms_svc\entity\WebhookDelivery.java
D:\grc-platform-v4\dms-svc\src\main\java\com\ascentbusiness\dms_svc\service\AuditExportService.java
D:\grc-platform-v4\dms-svc\src\main\java\com\ascentbusiness\dms_svc\service\WebhookEndpointService.java
D:\grc-platform-v4\dms-svc\src\main\java\com\ascentbusiness\dms_svc\dto\UploadTrendData.java
D:\grc-platform-v4\dms-svc\src\main\java\com\ascentbusiness\dms_svc\dto\FacetCount.java
D:\grc-platform-v4\dms-svc\src\main\java\com\ascentbusiness\dms_svc\dto\TemplateSearchResult.java
D:\grc-platform-v4\dms-svc\src\main\java\com\ascentbusiness\dms_svc\config\JpaAuditingConfig.java
D:\grc-platform-v4\dms-svc\src\main\java\com\ascentbusiness\dms_svc\dto\BulkShareResponse.java
D:\grc-platform-v4\dms-svc\src\main\java\com\ascentbusiness\dms_svc\security\UserContext.java
D:\grc-platform-v4\dms-svc\src\main\java\com\ascentbusiness\dms_svc\service\PandocConversionService.java
D:\grc-platform-v4\dms-svc\src\main\java\com\ascentbusiness\dms_svc\dto\VirusScanResult.java
D:\grc-platform-v4\dms-svc\src\main\java\com\ascentbusiness\dms_svc\entity\ComplianceViolation.java
D:\grc-platform-v4\dms-svc\src\main\java\com\ascentbusiness\dms_svc\exception\DuplicateResourceException.java
D:\grc-platform-v4\dms-svc\src\main\java\com\ascentbusiness\dms_svc\enums\TestStatus.java
D:\grc-platform-v4\dms-svc\src\main\java\com\ascentbusiness\dms_svc\resolver\LegalHoldResolver.java
D:\grc-platform-v4\dms-svc\src\main\java\com\ascentbusiness\dms_svc\entity\SecurityViolation.java
D:\grc-platform-v4\dms-svc\src\main\java\com\ascentbusiness\dms_svc\enums\ComplianceClassificationLevel.java
D:\grc-platform-v4\dms-svc\src\main\java\com\ascentbusiness\dms_svc\service\ComplianceAuditService.java
D:\grc-platform-v4\dms-svc\src\main\java\com\ascentbusiness\dms_svc\repository\DocumentShareLinkRepository.java
D:\grc-platform-v4\dms-svc\src\main\java\com\ascentbusiness\dms_svc\service\virus\impl\WindowsDefenderVirusScanner.java
D:\grc-platform-v4\dms-svc\src\main\java\com\ascentbusiness\dms_svc\dto\SearchDocument.java
D:\grc-platform-v4\dms-svc\src\main\java\com\ascentbusiness\dms_svc\repository\TemplateUsageHistoryRepository.java
D:\grc-platform-v4\dms-svc\src\main\java\com\ascentbusiness\dms_svc\dto\ApplicationMetrics.java
D:\grc-platform-v4\dms-svc\src\main\java\com\ascentbusiness\dms_svc\service\SystemEventService.java
D:\grc-platform-v4\dms-svc\src\main\java\com\ascentbusiness\dms_svc\dto\TemplateUsageStats.java
D:\grc-platform-v4\dms-svc\src\main\java\com\ascentbusiness\dms_svc\repository\AsyncProcessingJobRepository.java
D:\grc-platform-v4\dms-svc\src\main\java\com\ascentbusiness\dms_svc\dto\MarkdownConversionResult.java
D:\grc-platform-v4\dms-svc\src\main\java\com\ascentbusiness\dms_svc\enums\ConversionQuality.java
D:\grc-platform-v4\dms-svc\src\main\java\com\ascentbusiness\dms_svc\entity\DocumentShareLink.java
D:\grc-platform-v4\dms-svc\src\main\java\com\ascentbusiness\dms_svc\health\SystemHealthIndicator.java
D:\grc-platform-v4\dms-svc\src\main\java\com\ascentbusiness\dms_svc\enums\WebhookAuthType.java
D:\grc-platform-v4\dms-svc\src\main\java\com\ascentbusiness\dms_svc\exception\DuplicateFileException.java
D:\grc-platform-v4\dms-svc\src\main\java\com\ascentbusiness\dms_svc\aspect\CorrelationIdAspect.java
D:\grc-platform-v4\dms-svc\src\main\java\com\ascentbusiness\dms_svc\enums\WorkflowAction.java
D:\grc-platform-v4\dms-svc\src\main\java\com\ascentbusiness\dms_svc\converter\PIIEncryptionConverter.java
D:\grc-platform-v4\dms-svc\src\main\java\com\ascentbusiness\dms_svc\dto\ConversionQueue.java
D:\grc-platform-v4\dms-svc\src\main\java\com\ascentbusiness\dms_svc\resolver\SystemEventResolver.java
D:\grc-platform-v4\dms-svc\src\main\java\com\ascentbusiness\dms_svc\dto\DocumentOwnershipMetadataInput.java
D:\grc-platform-v4\dms-svc\src\main\java\com\ascentbusiness\dms_svc\dto\PdfConversionInput.java
D:\grc-platform-v4\dms-svc\src\main\java\com\ascentbusiness\dms_svc\entity\WorkflowStage.java
D:\grc-platform-v4\dms-svc\src\main\java\com\ascentbusiness\dms_svc\dto\JwtTokenResponse.java
D:\grc-platform-v4\dms-svc\src\main\java\com\ascentbusiness\dms_svc\repository\SystemEventRepository.java
D:\grc-platform-v4\dms-svc\src\main\java\com\ascentbusiness\dms_svc\dto\ConversionProgress.java
D:\grc-platform-v4\dms-svc\src\main\java\com\ascentbusiness\dms_svc\config\TracingConfiguration.java
D:\grc-platform-v4\dms-svc\src\main\java\com\ascentbusiness\dms_svc\service\AuditVerificationService.java
D:\grc-platform-v4\dms-svc\src\main\java\com\ascentbusiness\dms_svc\config\PdfConversionConfig.java
D:\grc-platform-v4\dms-svc\src\main\java\com\ascentbusiness\dms_svc\enums\AuditEventType.java
D:\grc-platform-v4\dms-svc\src\main\java\com\ascentbusiness\dms_svc\dto\AuditLogView.java
D:\grc-platform-v4\dms-svc\src\main\java\com\ascentbusiness\dms_svc\service\WorkflowNotificationService.java
D:\grc-platform-v4\dms-svc\src\main\java\com\ascentbusiness\dms_svc\entity\DocumentComplianceMetadata.java
D:\grc-platform-v4\dms-svc\src\main\java\com\ascentbusiness\dms_svc\service\MigrationAuditService.java
D:\grc-platform-v4\dms-svc\src\main\java\com\ascentbusiness\dms_svc\controller\AdminController.java
D:\grc-platform-v4\dms-svc\src\main\java\com\ascentbusiness\dms_svc\service\AuthService.java
D:\grc-platform-v4\dms-svc\src\main\java\com\ascentbusiness\dms_svc\enums\TemplateApprovalStatus.java
D:\grc-platform-v4\dms-svc\src\main\java\com\ascentbusiness\dms_svc\resolver\StorageConfigurationResolver.java
D:\grc-platform-v4\dms-svc\src\main\java\com\ascentbusiness\dms_svc\repository\WorkflowNotificationRepository.java
D:\grc-platform-v4\dms-svc\src\main\java\com\ascentbusiness\dms_svc\entity\AuditChainMetadata.java
D:\grc-platform-v4\dms-svc\src\main\java\com\ascentbusiness\dms_svc\dto\JvmMetrics.java
D:\grc-platform-v4\dms-svc\src\main\java\com\ascentbusiness\dms_svc\config\GraphQLSecurityInterceptor.java
D:\grc-platform-v4\dms-svc\src\main\java\com\ascentbusiness\dms_svc\enums\TaskStatus.java
D:\grc-platform-v4\dms-svc\src\main\java\com\ascentbusiness\dms_svc\enums\WorkflowStatus.java
D:\grc-platform-v4\dms-svc\src\main\java\com\ascentbusiness\dms_svc\enums\SecurityViolationType.java
D:\grc-platform-v4\dms-svc\src\main\java\com\ascentbusiness\dms_svc\dto\VirusScanResponse.java
D:\grc-platform-v4\dms-svc\src\main\java\com\ascentbusiness\dms_svc\versioning\ApiVersion.java
D:\grc-platform-v4\dms-svc\src\main\java\com\ascentbusiness\dms_svc\resolver\DocumentTemplateResolver.java
D:\grc-platform-v4\dms-svc\src\main\java\com\ascentbusiness\dms_svc\repository\WorkflowHistoryRepository.java
D:\grc-platform-v4\dms-svc\src\main\java\com\ascentbusiness\dms_svc\dto\ConversionHistoryPage.java
D:\grc-platform-v4\dms-svc\src\main\java\com\ascentbusiness\dms_svc\util\FileUtil.java
D:\grc-platform-v4\dms-svc\src\main\java\com\ascentbusiness\dms_svc\entity\AuditVerificationLog.java
D:\grc-platform-v4\dms-svc\src\main\java\com\ascentbusiness\dms_svc\dto\DocumentComplianceMetadataInput.java
D:\grc-platform-v4\dms-svc\src\main\java\com\ascentbusiness\dms_svc\config\PandocConfig.java
D:\grc-platform-v4\dms-svc\src\main\java\com\ascentbusiness\dms_svc\entity\AsyncProcessingJob.java
D:\grc-platform-v4\dms-svc\src\main\java\com\ascentbusiness\dms_svc\dto\DiagnosticTest.java
D:\grc-platform-v4\dms-svc\src\main\java\com\ascentbusiness\dms_svc\repository\ComplianceFrameworkRepository.java
D:\grc-platform-v4\dms-svc\src\main\java\com\ascentbusiness\dms_svc\entity\DocumentPermission.java
D:\grc-platform-v4\dms-svc\src\main\java\com\ascentbusiness\dms_svc\service\SharePointAuthProvider.java
D:\grc-platform-v4\dms-svc\src\main\java\com\ascentbusiness\dms_svc\enums\TemplateAccessLevel.java
D:\grc-platform-v4\dms-svc\src\main\java\com\ascentbusiness\dms_svc\service\virus\impl\ClamAvVirusScanner.java
D:\grc-platform-v4\dms-svc\src\main\java\com\ascentbusiness\dms_svc\resolver\WorkflowDefinitionResolver.java
D:\grc-platform-v4\dms-svc\src\main\java\com\ascentbusiness\dms_svc\dto\ComplianceAuditPage.java
D:\grc-platform-v4\dms-svc\src\main\java\com\ascentbusiness\dms_svc\util\ExceptionUtil.java
D:\grc-platform-v4\dms-svc\src\main\java\com\ascentbusiness\dms_svc\config\GraphQlConfig.java
D:\grc-platform-v4\dms-svc\src\main\java\com\ascentbusiness\dms_svc\dto\WebhookEndpointStatistics.java
D:\grc-platform-v4\dms-svc\src\main\java\com\ascentbusiness\dms_svc\service\PermissionExpirationService.java
D:\grc-platform-v4\dms-svc\src\main\java\com\ascentbusiness\dms_svc\exception\DmsValidationException.java
D:\grc-platform-v4\dms-svc\src\main\java\com\ascentbusiness\dms_svc\dto\ConversionError.java
D:\grc-platform-v4\dms-svc\src\main\java\com\ascentbusiness\dms_svc\repository\WorkflowTaskRepository.java
D:\grc-platform-v4\dms-svc\src\main\java\com\ascentbusiness\dms_svc\dto\MarkdownConversionInput.java
D:\grc-platform-v4\dms-svc\src\main\java\com\ascentbusiness\dms_svc\dto\ComplianceAuditLog.java
D:\grc-platform-v4\dms-svc\src\main\java\com\ascentbusiness\dms_svc\repository\DocumentClassificationMetadataRepository.java
D:\grc-platform-v4\dms-svc\src\main\java\com\ascentbusiness\dms_svc\enums\EventType.java
D:\grc-platform-v4\dms-svc\src\main\java\com\ascentbusiness\dms_svc\dto\ConnectionTestResult.java
D:\grc-platform-v4\dms-svc\src\main\java\com\ascentbusiness\dms_svc\entity\WebhookEndpoint.java
D:\grc-platform-v4\dms-svc\src\main\java\com\ascentbusiness\dms_svc\config\MdcTaskDecorator.java
D:\grc-platform-v4\dms-svc\src\main\java\com\ascentbusiness\dms_svc\dto\DocumentHighlights.java
D:\grc-platform-v4\dms-svc\src\main\java\com\ascentbusiness\dms_svc\enums\WorkflowType.java
D:\grc-platform-v4\dms-svc\src\main\java\com\ascentbusiness\dms_svc\repository\SecurityViolationRepository.java
D:\grc-platform-v4\dms-svc\src\main\java\com\ascentbusiness\dms_svc\service\DocumentTemplateService.java
D:\grc-platform-v4\dms-svc\src\main\java\com\ascentbusiness\dms_svc\enums\DeliveryStatus.java
D:\grc-platform-v4\dms-svc\src\main\java\com\ascentbusiness\dms_svc\config\StorageConfigurationProperties.java
D:\grc-platform-v4\dms-svc\src\main\java\com\ascentbusiness\dms_svc\entity\WorkflowInstance.java
D:\grc-platform-v4\dms-svc\src\main\java\com\ascentbusiness\dms_svc\util\StructuredLogger.java
D:\grc-platform-v4\dms-svc\src\main\java\com\ascentbusiness\dms_svc\dto\TemplateSearchFacets.java
D:\grc-platform-v4\dms-svc\src\main\java\com\ascentbusiness\dms_svc\dto\DownloadResponse.java
D:\grc-platform-v4\dms-svc\src\main\java\com\ascentbusiness\dms_svc\util\SecurityValidationUtil.java
D:\grc-platform-v4\dms-svc\src\main\java\com\ascentbusiness\dms_svc\dto\AdvancedSearchInput.java
D:\grc-platform-v4\dms-svc\src\main\java\com\ascentbusiness\dms_svc\dto\SystemPerformance.java
D:\grc-platform-v4\dms-svc\src\main\java\com\ascentbusiness\dms_svc\repository\DocumentRepository.java
D:\grc-platform-v4\dms-svc\src\main\java\com\ascentbusiness\dms_svc\search\repository\SearchableDocumentRepository.java
D:\grc-platform-v4\dms-svc\src\main\java\com\ascentbusiness\dms_svc\health\StorageHealthIndicator.java
D:\grc-platform-v4\dms-svc\src\main\java\com\ascentbusiness\dms_svc\service\virus\impl\MockVirusScanner.java
D:\grc-platform-v4\dms-svc\src\main\java\com\ascentbusiness\dms_svc\dto\WordConversionInput.java
D:\grc-platform-v4\dms-svc\src\main\java\com\ascentbusiness\dms_svc\repository\DocumentComplianceMappingRepository.java
D:\grc-platform-v4\dms-svc\src\main\java\com\ascentbusiness\dms_svc\entity\AuditSummaryReport.java
D:\grc-platform-v4\dms-svc\src\main\java\com\ascentbusiness\dms_svc\entity\ChunkedUploadSession.java
D:\grc-platform-v4\dms-svc\src\main\java\com\ascentbusiness\dms_svc\dto\BulkUploadResult.java
D:\grc-platform-v4\dms-svc\src\main\java\com\ascentbusiness\dms_svc\dto\StorageConfigurationDto.java
D:\grc-platform-v4\dms-svc\src\main\java\com\ascentbusiness\dms_svc\dto\UploadProgress.java
D:\grc-platform-v4\dms-svc\src\main\java\com\ascentbusiness\dms_svc\service\DocumentPermissionService.java
D:\grc-platform-v4\dms-svc\src\main\java\com\ascentbusiness\dms_svc\resolver\DocumentResolver.java
D:\grc-platform-v4\dms-svc\src\main\java\com\ascentbusiness\dms_svc\dto\AuditLogPageResponse.java
D:\grc-platform-v4\dms-svc\src\main\java\com\ascentbusiness\dms_svc\enums\ExportStatus.java
D:\grc-platform-v4\dms-svc\src\main\java\com\ascentbusiness\dms_svc\exception\ResourceNotFoundException.java
D:\grc-platform-v4\dms-svc\src\main\java\com\ascentbusiness\dms_svc\enums\AuditAction.java
D:\grc-platform-v4\dms-svc\src\main\java\com\ascentbusiness\dms_svc\dto\UploadDocumentFromPathExInput.java
D:\grc-platform-v4\dms-svc\src\main\java\com\ascentbusiness\dms_svc\dto\ComponentHealth.java
D:\grc-platform-v4\dms-svc\src\main\java\com\ascentbusiness\dms_svc\exception\PdfConversionException.java
D:\grc-platform-v4\dms-svc\src\main\java\com\ascentbusiness\dms_svc\dto\SearchType.java
D:\grc-platform-v4\dms-svc\src\main\java\com\ascentbusiness\dms_svc\repository\AuditLogRepository.java
D:\grc-platform-v4\dms-svc\src\main\java\com\ascentbusiness\dms_svc\enums\GeographicRegion.java
D:\grc-platform-v4\dms-svc\src\main\java\com\ascentbusiness\dms_svc\service\RedisRateLimitService.java
D:\grc-platform-v4\dms-svc\src\main\java\com\ascentbusiness\dms_svc\dto\FileValidationOptionsInput.java
D:\grc-platform-v4\dms-svc\src\main\java\com\ascentbusiness\dms_svc\entity\TemplateField.java
D:\grc-platform-v4\dms-svc\src\main\java\com\ascentbusiness\dms_svc\config\ReactorMdcConfiguration.java
D:\grc-platform-v4\dms-svc\src\main\java\com\ascentbusiness\dms_svc\dto\DocumentPermissionResponse.java
D:\grc-platform-v4\dms-svc\src\main\java\com\ascentbusiness\dms_svc\resolver\TestCaseGraphQLResolver.java
D:\grc-platform-v4\dms-svc\src\main\java\com\ascentbusiness\dms_svc\resolver\PdfConversionResolver.java
D:\grc-platform-v4\dms-svc\src\main\java\com\ascentbusiness\dms_svc\dto\BulkUploadItemResult.java
D:\grc-platform-v4\dms-svc\src\main\java\com\ascentbusiness\dms_svc\command\StorageMigrationCommand.java
D:\grc-platform-v4\dms-svc\src\main\java\com\ascentbusiness\dms_svc\entity\EventProcessingLog.java
D:\grc-platform-v4\dms-svc\src\main\java\com\ascentbusiness\dms_svc\dto\ConversionMetadata.java
D:\grc-platform-v4\dms-svc\src\main\java\com\ascentbusiness\dms_svc\enums\DispositionAction.java
D:\grc-platform-v4\dms-svc\src\main\java\com\ascentbusiness\dms_svc\security\JwtTokenProvider.java
D:\grc-platform-v4\dms-svc\src\main\java\com\ascentbusiness\dms_svc\dto\TemplateApprovalStats.java
D:\grc-platform-v4\dms-svc\src\main\java\com\ascentbusiness\dms_svc\enums\ViolationSeverity.java
D:\grc-platform-v4\dms-svc\src\main\java\com\ascentbusiness\dms_svc\repository\DocumentOwnershipMetadataRepository.java
D:\grc-platform-v4\dms-svc\src\main\java\com\ascentbusiness\dms_svc\entity\ChunkedUploadChunk.java
D:\grc-platform-v4\dms-svc\src\main\java\com\ascentbusiness\dms_svc\enums\HealthStatus.java
D:\grc-platform-v4\dms-svc\src\main\java\com\ascentbusiness\dms_svc\exception\MarkdownConversionException.java
D:\grc-platform-v4\dms-svc\src\main\java\com\ascentbusiness\dms_svc\service\LegalHoldService.java
D:\grc-platform-v4\dms-svc\src\main\java\com\ascentbusiness\dms_svc\dto\DeleteResponse.java
D:\grc-platform-v4\dms-svc\src\main\java\com\ascentbusiness\dms_svc\service\DynamicStorageProviderFactory.java
D:\grc-platform-v4\dms-svc\src\main\java\com\ascentbusiness\dms_svc\exception\ComplianceViolationException.java
D:\grc-platform-v4\dms-svc\src\main\java\com\ascentbusiness\dms_svc\dto\FileValidationError.java
D:\grc-platform-v4\dms-svc\src\main\java\com\ascentbusiness\dms_svc\service\SharePointStorageService.java
D:\grc-platform-v4\dms-svc\src\main\java\com\ascentbusiness\dms_svc\annotation\RateLimit.java
D:\grc-platform-v4\dms-svc\src\main\java\com\ascentbusiness\dms_svc\security\JwtAuthenticationEntryPoint.java
D:\grc-platform-v4\dms-svc\src\main\java\com\ascentbusiness\dms_svc\entity\DocumentVersion.java
D:\grc-platform-v4\dms-svc\src\main\java\com\ascentbusiness\dms_svc\exception\SecurityViolationException.java
D:\grc-platform-v4\dms-svc\src\main\java\com\ascentbusiness\dms_svc\dto\AuditStatistics.java
D:\grc-platform-v4\dms-svc\src\main\java\com\ascentbusiness\dms_svc\repository\AuditSummaryReportRepository.java
D:\grc-platform-v4\dms-svc\src\main\java\com\ascentbusiness\dms_svc\security\JwtAuthenticationFilter.java
D:\grc-platform-v4\dms-svc\src\main\java\com\ascentbusiness\dms_svc\aspect\RateLimitAspect.java
D:\grc-platform-v4\dms-svc\src\main\java\com\ascentbusiness\dms_svc\dto\NetworkIO.java
D:\grc-platform-v4\dms-svc\src\main\java\com\ascentbusiness\dms_svc\dto\TemplateStatistics.java
D:\grc-platform-v4\dms-svc\src\main\java\com\ascentbusiness\dms_svc\dto\TemplatePreview.java
D:\grc-platform-v4\dms-svc\src\main\java\com\ascentbusiness\dms_svc\config\LiquibaseConfig.java
D:\grc-platform-v4\dms-svc\src\main\java\com\ascentbusiness\dms_svc\entity\ComplianceFramework.java
D:\grc-platform-v4\dms-svc\src\main\java\com\ascentbusiness\dms_svc\service\WordToPdfConversionService.java
D:\grc-platform-v4\dms-svc\src\main\java\com\ascentbusiness\dms_svc\enums\StorageProvider.java
D:\grc-platform-v4\dms-svc\src\main\java\com\ascentbusiness\dms_svc\health\DatabaseHealthIndicator.java
D:\grc-platform-v4\dms-svc\src\main\java\com\ascentbusiness\dms_svc\dto\NetworkInfo.java
D:\grc-platform-v4\dms-svc\src\main\java\com\ascentbusiness\dms_svc\util\CorrelationIdUtil.java
D:\grc-platform-v4\dms-svc\src\main\java\com\ascentbusiness\dms_svc\dto\AuditExportResponse.java
D:\grc-platform-v4\dms-svc\src\main\java\com\ascentbusiness\dms_svc\enums\ProcessingStatus.java
D:\grc-platform-v4\dms-svc\src\main\java\com\ascentbusiness\dms_svc\entity\AuditLog.java
D:\grc-platform-v4\dms-svc\src\main\java\com\ascentbusiness\dms_svc\service\MarkdownToWordConversionService.java
D:\grc-platform-v4\dms-svc\src\main\java\com\ascentbusiness\dms_svc\dto\ConversionJob.java
D:\grc-platform-v4\dms-svc\src\main\java\com\ascentbusiness\dms_svc\enums\DispositionStatus.java
D:\grc-platform-v4\dms-svc\src\main\java\com\ascentbusiness\dms_svc\enums\MetricStatus.java
D:\grc-platform-v4\dms-svc\src\main\java\com\ascentbusiness\dms_svc\dto\DocumentAccessRoleInput.java
D:\grc-platform-v4\dms-svc\src\main\java\com\ascentbusiness\dms_svc\entity\WorkflowHistory.java
D:\grc-platform-v4\dms-svc\src\main\java\com\ascentbusiness\dms_svc\repository\ComplianceViolationRepository.java
D:\grc-platform-v4\dms-svc\src\main\java\com\ascentbusiness\dms_svc\resolver\DocumentPermissionResolver.java
D:\grc-platform-v4\dms-svc\src\main\java\com\ascentbusiness\dms_svc\entity\RegulationMapping.java
D:\grc-platform-v4\dms-svc\src\main\java\com\ascentbusiness\dms_svc\service\PerformanceMonitoringService.java
D:\grc-platform-v4\dms-svc\src\main\java\com\ascentbusiness\dms_svc\entity\RetentionPolicy.java
D:\grc-platform-v4\dms-svc\src\main\java\com\ascentbusiness\dms_svc\dto\DocumentUploadMetadataInput.java
D:\grc-platform-v4\dms-svc\src\main\java\com\ascentbusiness\dms_svc\entity\Document.java
D:\grc-platform-v4\dms-svc\src\main\java\com\ascentbusiness\dms_svc\repository\WorkflowInstanceRepository.java
D:\grc-platform-v4\dms-svc\src\main\java\com\ascentbusiness\dms_svc\service\virus\AbstractVirusScanner.java
D:\grc-platform-v4\dms-svc\src\main\java\com\ascentbusiness\dms_svc\dto\PdfConversionResult.java
D:\grc-platform-v4\dms-svc\src\main\java\com\ascentbusiness\dms_svc\exception\HistoricalDocumentException.java
D:\grc-platform-v4\dms-svc\src\main\java\com\ascentbusiness\dms_svc\entity\WorkflowTransition.java
D:\grc-platform-v4\dms-svc\src\main\java\com\ascentbusiness\dms_svc\exception\UnauthorizedException.java
D:\grc-platform-v4\dms-svc\src\main\java\com\ascentbusiness\dms_svc\dto\AuditLogPage.java
D:\grc-platform-v4\dms-svc\src\main\java\com\ascentbusiness\dms_svc\dto\ConversionResult.java
D:\grc-platform-v4\dms-svc\src\main\java\com\ascentbusiness\dms_svc\config\GraphQLExceptionHandler.java
D:\grc-platform-v4\dms-svc\src\main\java\com\ascentbusiness\dms_svc\dto\SSLInfo.java
D:\grc-platform-v4\dms-svc\src\main\java\com\ascentbusiness\dms_svc\util\SharePointTestUtility.java
D:\grc-platform-v4\dms-svc\src\main\java\com\ascentbusiness\dms_svc\controller\GraphQLMetricsController.java
D:\grc-platform-v4\dms-svc\src\main\java\com\ascentbusiness\dms_svc\dto\StrategyCount.java
D:\grc-platform-v4\dms-svc\src\main\java\com\ascentbusiness\dms_svc\dto\TemplateSearchInput.java
D:\grc-platform-v4\dms-svc\src\main\java\com\ascentbusiness\dms_svc\dto\ConversionTypeStats.java
D:\grc-platform-v4\dms-svc\src\main\java\com\ascentbusiness\dms_svc\dto\CacheStatistics.java
D:\grc-platform-v4\dms-svc\src\main\java\com\ascentbusiness\dms_svc\service\DocumentParsingService.java
D:\grc-platform-v4\dms-svc\src\main\java\com\ascentbusiness\dms_svc\dto\SystemHealth.java
D:\grc-platform-v4\dms-svc\src\main\java\com\ascentbusiness\dms_svc\util\InputValidationUtil.java
D:\grc-platform-v4\dms-svc\src\main\java\com\ascentbusiness\dms_svc\health\SecurityHealthIndicator.java
D:\grc-platform-v4\dms-svc\src\main\java\com\ascentbusiness\dms_svc\entity\DocumentComplianceMapping.java
D:\grc-platform-v4\dms-svc\src\main\java\com\ascentbusiness\dms_svc\util\PaginationUtil.java
D:\grc-platform-v4\dms-svc\src\main\java\com\ascentbusiness\dms_svc\dto\HourlyActivityCount.java
D:\grc-platform-v4\dms-svc\src\main\java\com\ascentbusiness\dms_svc\dto\TestCategorySummary.java
D:\grc-platform-v4\dms-svc\src\main\java\com\ascentbusiness\dms_svc\exception\StorageMigrationException.java
D:\grc-platform-v4\dms-svc\src\main\java\com\ascentbusiness\dms_svc\dto\DatabaseMetrics.java
D:\grc-platform-v4\dms-svc\src\main\java\com\ascentbusiness\dms_svc\enums\ConversionPriority.java
D:\grc-platform-v4\dms-svc\src\main\java\com\ascentbusiness\dms_svc\enums\ConversionStatus.java
D:\grc-platform-v4\dms-svc\src\main\java\com\ascentbusiness\dms_svc\service\SecurityService.java
D:\grc-platform-v4\dms-svc\src\main\java\com\ascentbusiness\dms_svc\service\WorkflowTaskService.java
D:\grc-platform-v4\dms-svc\src\main\java\com\ascentbusiness\dms_svc\dto\CreateShareLinkInput.java
D:\grc-platform-v4\dms-svc\src\main\java\com\ascentbusiness\dms_svc\enums\PermissionType.java
D:\grc-platform-v4\dms-svc\src\main\java\com\ascentbusiness\dms_svc\enums\DiagnosticTestType.java
D:\grc-platform-v4\dms-svc\src\main\java\com\ascentbusiness\dms_svc\repository\RetentionPolicyAssignmentRepository.java
D:\grc-platform-v4\dms-svc\src\main\java\com\ascentbusiness\dms_svc\dto\SinglePermissionInput.java
D:\grc-platform-v4\dms-svc\src\main\java\com\ascentbusiness\dms_svc\search\model\SearchableDocument.java
D:\grc-platform-v4\dms-svc\src\main\java\com\ascentbusiness\dms_svc\dto\WordConversionResult.java
D:\grc-platform-v4\dms-svc\src\main\java\com\ascentbusiness\dms_svc\service\StorageService.java
D:\grc-platform-v4\dms-svc\src\main\java\com\ascentbusiness\dms_svc\service\virus\VirusScanner.java
D:\grc-platform-v4\dms-svc\src\main\java\com\ascentbusiness\dms_svc\config\AsyncConfig.java
D:\grc-platform-v4\dms-svc\src\main\java\com\ascentbusiness\dms_svc\repository\ComplianceClassificationRepository.java
D:\grc-platform-v4\dms-svc\src\main\java\com\ascentbusiness\dms_svc\dto\ConversionMethodStats.java
D:\grc-platform-v4\dms-svc\src\main\java\com\ascentbusiness\dms_svc\dto\CleanupResult.java
D:\grc-platform-v4\dms-svc\src\main\java\com\ascentbusiness\dms_svc\exception\DmsException.java
D:\grc-platform-v4\dms-svc\src\main\java\com\ascentbusiness\dms_svc\service\PIIEncryptionService.java
D:\grc-platform-v4\dms-svc\src\main\java\com\ascentbusiness\dms_svc\service\WorkflowHistoryService.java
D:\grc-platform-v4\dms-svc\src\main\java\com\ascentbusiness\dms_svc\service\DocumentShareService.java
D:\grc-platform-v4\dms-svc\src\main\java\com\ascentbusiness\dms_svc\enums\EventCategory.java
D:\grc-platform-v4\dms-svc\src\main\java\com\ascentbusiness\dms_svc\service\StorageConfigurationTestService.java
D:\grc-platform-v4\dms-svc\src\main\java\com\ascentbusiness\dms_svc\enums\IssueSeverity.java
D:\grc-platform-v4\dms-svc\src\main\java\com\ascentbusiness\dms_svc\repository\RegulationMappingRepository.java
D:\grc-platform-v4\dms-svc\src\main\java\com\ascentbusiness\dms_svc\service\StorageConfigurationService.java
D:\grc-platform-v4\dms-svc\src\main\java\com\ascentbusiness\dms_svc\enums\PageOrientation.java
D:\grc-platform-v4\dms-svc\src\main\java\com\ascentbusiness\dms_svc\dto\ConversionCapabilities.java
D:\grc-platform-v4\dms-svc\src\main\java\com\ascentbusiness\dms_svc\dto\SearchDocumentPage.java
D:\grc-platform-v4\dms-svc\src\main\java\com\ascentbusiness\dms_svc\dto\UploadStatistics.java
D:\grc-platform-v4\dms-svc\src\main\java\com\ascentbusiness\dms_svc\resolver\DiagnosticsGraphQLResolver.java
D:\grc-platform-v4\dms-svc\src\main\java\com\ascentbusiness\dms_svc\config\GraphQLMonitoringConfig.java
D:\grc-platform-v4\dms-svc\src\main\java\com\ascentbusiness\dms_svc\dto\TemplateTypeStats.java
D:\grc-platform-v4\dms-svc\src\main\java\com\ascentbusiness\dms_svc\util\PermissionUtil.java
D:\grc-platform-v4\dms-svc\src\main\java\com\ascentbusiness\dms_svc\dto\BatchConversionInput.java
D:\grc-platform-v4\dms-svc\src\main\java\com\ascentbusiness\dms_svc\repository\WebhookEndpointRepository.java
D:\grc-platform-v4\dms-svc\src\main\java\com\ascentbusiness\dms_svc\dto\UserActivityCount.java
D:\grc-platform-v4\dms-svc\src\main\java\com\ascentbusiness\dms_svc\dto\UploadNewVersionFromPathInput.java
D:\grc-platform-v4\dms-svc\src\main\java\com\ascentbusiness\dms_svc\repository\AuditChainMetadataRepository.java
D:\grc-platform-v4\dms-svc\src\main\java\com\ascentbusiness\dms_svc\enums\TamperingSeverity.java
D:\grc-platform-v4\dms-svc\src\main\java\com\ascentbusiness\dms_svc\entity\ComplianceClassification.java
D:\grc-platform-v4\dms-svc\src\main\java\com\ascentbusiness\dms_svc\dto\MemoryUsage.java
D:\grc-platform-v4\dms-svc\src\main\java\com\ascentbusiness\dms_svc\service\ApiVersionService.java
D:\grc-platform-v4\dms-svc\src\main\java\com\ascentbusiness\dms_svc\dto\DocumentUploadFromPathInput.java
D:\grc-platform-v4\dms-svc\src\main\java\com\ascentbusiness\dms_svc\dto\EnhancedDocumentUploadInput.java
D:\grc-platform-v4\dms-svc\src\main\java\com\ascentbusiness\dms_svc\repository\BulkShareOperationRepository.java
D:\grc-platform-v4\dms-svc\src\main\java\com\ascentbusiness\dms_svc\enums\ValidationSeverity.java
D:\grc-platform-v4\dms-svc\src\main\java\com\ascentbusiness\dms_svc\entity\BulkShareOperation.java
D:\grc-platform-v4\dms-svc\src\main\java\com\ascentbusiness\dms_svc\dto\DocumentEx.java
D:\grc-platform-v4\dms-svc\src\main\java\com\ascentbusiness\dms_svc\entity\AuditExport.java
D:\grc-platform-v4\dms-svc\src\main\java\com\ascentbusiness\dms_svc\enums\NotificationType.java
D:\grc-platform-v4\dms-svc\src\main\java\com\ascentbusiness\dms_svc\dto\ConnectionTestDetails.java
D:\grc-platform-v4\dms-svc\src\main\java\com\ascentbusiness\dms_svc\service\WebhookService.java
D:\grc-platform-v4\dms-svc\src\main\java\com\ascentbusiness\dms_svc\service\ComplianceService.java
D:\grc-platform-v4\dms-svc\src\main\java\com\ascentbusiness\dms_svc\enums\SortDirection.java
D:\grc-platform-v4\dms-svc\src\main\java\com\ascentbusiness\dms_svc\enums\DocumentStatus.java
