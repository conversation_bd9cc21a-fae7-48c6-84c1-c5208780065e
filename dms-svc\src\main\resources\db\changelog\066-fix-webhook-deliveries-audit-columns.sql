--liquibase formatted sql

--changeset system:066-create-missing-webhook-tables
--comment: Create missing webhook_endpoints and webhook_deliveries tables with proper BaseEntity audit columns

-- Create webhook_endpoints table (missing from migration 043)
CREATE TABLE webhook_endpoints (
    id BIGINT AUTO_INCREMENT PRIMARY KEY,
    name VARCHAR(255) NOT NULL,
    description TEXT,
    url VARCHAR(2000) NOT NULL,
    
    -- Webhook configuration
    http_method VARCHAR(10) NOT NULL DEFAULT 'POST',
    content_type VARCHAR(100) NOT NULL DEFAULT 'application/json',
    timeout_seconds INTEGER NOT NULL DEFAULT 30,
    
    -- Authentication
    auth_type VARCHAR(50), -- NONE, BASIC, BEARER, API_KEY, CUSTOM
    auth_config JSON, -- Authentication configuration
    
    -- Headers and payload
    custom_headers JSON, -- Custom HTTP headers
    payload_template TEXT, -- Template for webhook payload
    
    -- Event filtering
    event_types JSON, -- Array of event types to listen for
    event_filters JSON, -- Filters for events (e.g., document types, departments)
    
    -- Status and configuration
    is_active BOOLEAN NOT NULL DEFAULT TRUE,
    is_verified BOOLEAN NOT NULL DEFAULT FALSE,
    verification_token VARCHAR(255),
    secret_key VARCHAR(255), -- For signature verification
    
    -- Retry configuration
    max_retries INTEGER NOT NULL DEFAULT 3,
    retry_delay_seconds INTEGER NOT NULL DEFAULT 60,
    exponential_backoff BOOLEAN NOT NULL DEFAULT TRUE,
    
    -- Rate limiting
    rate_limit_per_minute INTEGER DEFAULT 60,
    rate_limit_per_hour INTEGER DEFAULT 1000,
    
    -- Monitoring
    success_count INTEGER DEFAULT 0,
    failure_count INTEGER DEFAULT 0,
    last_success_date TIMESTAMP,
    last_failure_date TIMESTAMP,
    last_failure_reason TEXT,
    
    -- BaseEntity audit columns
    created_date TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    last_modified_date TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    created_by VARCHAR(100) NOT NULL DEFAULT 'system',
    last_modified_by VARCHAR(100),
    
    -- Constraints
    INDEX idx_webhook_active (is_active),
    INDEX idx_webhook_verified (is_verified),
    INDEX idx_webhook_created_by (created_by),
    INDEX idx_webhook_last_success (last_success_date),
    INDEX idx_webhook_last_failure (last_failure_date)
);

-- Create webhook_deliveries table with proper BaseEntity audit columns
CREATE TABLE webhook_deliveries (
    id BIGINT AUTO_INCREMENT PRIMARY KEY,
    webhook_endpoint_id BIGINT NOT NULL,
    system_event_id BIGINT NOT NULL,
    
    -- Delivery details
    delivery_attempt INTEGER NOT NULL DEFAULT 1,
    delivery_status VARCHAR(50) NOT NULL DEFAULT 'PENDING', -- PENDING, SUCCESS, FAILED, CANCELLED
    
    -- HTTP details
    http_status_code INTEGER,
    response_body TEXT,
    response_headers JSON,
    request_payload TEXT,
    request_headers JSON,
    
    -- Timing
    scheduled_date TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    attempted_date TIMESTAMP,
    completed_date TIMESTAMP,
    duration_ms INTEGER,
    
    -- Error handling
    error_message TEXT,
    error_code VARCHAR(50),
    retry_after_seconds INTEGER,
    
    -- Correlation
    correlation_id VARCHAR(100),
    
    -- BaseEntity audit columns
    created_date TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    last_modified_date TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    created_by VARCHAR(100) NOT NULL DEFAULT 'system',
    last_modified_by VARCHAR(100),
    
    -- Constraints
    FOREIGN KEY (webhook_endpoint_id) REFERENCES webhook_endpoints(id) ON DELETE CASCADE,
    FOREIGN KEY (system_event_id) REFERENCES system_events(id) ON DELETE CASCADE,
    INDEX idx_delivery_status (delivery_status),
    INDEX idx_delivery_scheduled (scheduled_date),
    INDEX idx_delivery_attempted (attempted_date),
    INDEX idx_delivery_webhook (webhook_endpoint_id),
    INDEX idx_delivery_event (system_event_id),
    INDEX idx_delivery_correlation (correlation_id),
    INDEX idx_webhook_deliveries_created_by (created_by),
    INDEX idx_webhook_deliveries_created_date (created_date),
    INDEX idx_webhook_deliveries_last_modified_date (last_modified_date)
);

-- Create webhook_security_log table for security monitoring
CREATE TABLE webhook_security_log (
    id BIGINT AUTO_INCREMENT PRIMARY KEY,
    webhook_endpoint_id BIGINT,

    -- Security event details
    security_event_type VARCHAR(50) NOT NULL, -- SIGNATURE_MISMATCH, RATE_LIMIT_EXCEEDED, SUSPICIOUS_ACTIVITY
    severity VARCHAR(20) NOT NULL DEFAULT 'MEDIUM', -- LOW, MEDIUM, HIGH, CRITICAL

    -- Request details
    source_ip VARCHAR(45),
    user_agent VARCHAR(500),
    request_headers JSON,

    -- Event description
    event_description TEXT,
    additional_data JSON,

    -- Response taken
    action_taken VARCHAR(100), -- BLOCKED, RATE_LIMITED, LOGGED_ONLY, WEBHOOK_DISABLED

    -- Timing
    event_timestamp TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,

    -- Constraints
    FOREIGN KEY (webhook_endpoint_id) REFERENCES webhook_endpoints(id) ON DELETE SET NULL,
    INDEX idx_security_webhook (webhook_endpoint_id),
    INDEX idx_security_event_type (security_event_type),
    INDEX idx_security_severity (severity),
    INDEX idx_security_timestamp (event_timestamp),
    INDEX idx_security_source_ip (source_ip)
);

-- Insert sample webhook endpoints for testing
INSERT INTO webhook_endpoints (name, description, url, event_types, is_active, created_by) VALUES
('Development Webhook', 'Development webhook for testing', 'http://localhost:8080/webhooks/test',
 JSON_ARRAY('DOCUMENT_CREATED', 'DOCUMENT_UPDATED', 'WORKFLOW_COMPLETED'),
 FALSE, 'system'),

('Document Management Integration', 'Integration with external document management system', 'https://api.example.com/dms/webhooks',
 JSON_ARRAY('DOCUMENT_CREATED', 'DOCUMENT_UPDATED', 'DOCUMENT_DELETED'),
 FALSE, 'system');

-- Add comments for documentation
ALTER TABLE webhook_endpoints COMMENT = 'Stores webhook endpoint configurations for external integrations';
ALTER TABLE webhook_deliveries COMMENT = 'Tracks webhook delivery attempts and their results with proper BaseEntity audit columns';
ALTER TABLE webhook_security_log COMMENT = 'Logs security-related events for webhook endpoints';