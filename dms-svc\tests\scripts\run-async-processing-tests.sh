#!/bin/bash

# Async Document Processing Test Suite
# Comprehensive testing for async processing functionality
# Version: 1.0

echo "========================================"
echo "Async Document Processing Test Suite"
echo "========================================"
echo "Version: 1.0 - Comprehensive Async Processing Tests"
echo

# Initialize counters
PASSED_TESTS=0
FAILED_TESTS=0
TOTAL_TESTS=0

# Test 1: Async Document Processor Service Tests
echo "[1/7] Running AsyncDocumentProcessor Service Tests..."
if mvn test -Dtest=AsyncDocumentProcessorTest -q; then
    echo "✓ AsyncDocumentProcessor Tests PASSED"
    ((PASSED_TESTS++))
else
    echo "✗ AsyncDocumentProcessor Tests FAILED"
    ((FAILED_TESTS++))
fi
((TOTAL_TESTS++))
echo

# Test 2: Repository Tests
echo "[2/7] Running AsyncProcessingJob Repository Tests..."
if mvn test -Dtest=AsyncProcessingJobRepositoryTest -q; then
    echo "✓ Repository Tests PASSED"
    ((PASSED_TESTS++))
else
    echo "✗ Repository Tests FAILED"
    ((FAILED_TESTS++))
fi
((TOTAL_TESTS++))
echo

# Test 3: Entity Tests
echo "[3/7] Running AsyncProcessingJob Entity Tests..."
if mvn test -Dtest=AsyncProcessingJobEntityTest -q; then
    echo "✓ Entity Tests PASSED"
    ((PASSED_TESTS++))
else
    echo "✗ Entity Tests FAILED"
    ((FAILED_TESTS++))
fi
((TOTAL_TESTS++))
echo

# Test 4: DTO Tests
echo "[4/7] Running AsyncJobStatus DTO Tests..."
if mvn test -Dtest=AsyncJobStatusTest -q; then
    echo "✓ DTO Tests PASSED"
    ((PASSED_TESTS++))
else
    echo "✗ DTO Tests FAILED"
    ((FAILED_TESTS++))
fi
((TOTAL_TESTS++))
echo

# Test 5: Chunked Upload Manager Tests
echo "[5/7] Running ChunkedUploadManager Service Tests..."
if mvn test -Dtest=ChunkedUploadManagerTest -q; then
    echo "✓ ChunkedUploadManager Tests PASSED"
    ((PASSED_TESTS++))
else
    echo "✗ ChunkedUploadManager Tests FAILED"
    ((FAILED_TESTS++))
fi
((TOTAL_TESTS++))
echo

# Test 6: GraphQL Resolver Tests
echo "[6/7] Running Async Processing GraphQL Resolver Tests..."
if mvn test -Dtest=AsyncProcessingResolverTest -q; then
    echo "✓ GraphQL Resolver Tests PASSED"
    ((PASSED_TESTS++))
else
    echo "✗ GraphQL Resolver Tests FAILED"
    ((FAILED_TESTS++))
fi
((TOTAL_TESTS++))
echo

# Test 7: Performance Tests
echo "[7/7] Running Async Processing Performance Tests..."
if mvn test -Dtest=AsyncProcessingPerformanceTest -q; then
    echo "✓ Performance Tests PASSED"
    ((PASSED_TESTS++))
else
    echo "⚠ Performance Tests FAILED (non-critical)"
    ((FAILED_TESTS++))
fi
((TOTAL_TESTS++))
echo

# Integration Test
echo "Running Async Processing Integration Tests..."
if mvn test -Dtest=ExtendedFileProcessingIntegrationTest -Dtest.methods=*Async*,*Job*,*Status* -q; then
    echo "✓ Integration Tests PASSED"
else
    echo "⚠ Integration Tests FAILED (non-critical)"
fi
echo

# Generate Summary
echo "========================================"
echo "ASYNC PROCESSING TEST SUMMARY"
echo "========================================"
echo "Total Tests: $TOTAL_TESTS"
echo "Passed: $PASSED_TESTS"
echo "Failed: $FAILED_TESTS"
echo

if [ $FAILED_TESTS -eq 0 ]; then
    echo "✓ ALL ASYNC PROCESSING TESTS PASSED!"
    echo
    echo "Validated Features:"
    echo "- Asynchronous document processing service"
    echo "- Job status tracking and management"
    echo "- Repository operations and queries"
    echo "- Entity validation and transient methods"
    echo "- DTO mapping and serialization"
    echo "- Chunked upload session management"
    echo "- GraphQL resolver functionality"
    echo "- Performance under load conditions"
    echo
    exit 0
else
    echo "✗ $FAILED_TESTS TEST(S) FAILED"
    echo
    echo "Please check the following:"
    echo "- Database schema for async_processing_jobs table"
    echo "- Async processing configuration"
    echo "- Thread pool and executor configuration"
    echo "- File processing thresholds"
    echo "- GraphQL schema definitions"
    echo
    exit 1
fi
