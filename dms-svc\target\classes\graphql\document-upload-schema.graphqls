# ===== DOCUMENT UPLOAD SCHEMA =====
# GraphQL schema for document upload operations (replacing DocumentRestController REST endpoints)

# Enhanced Upload Response Types
type UploadResponse {
  document: Document!
  message: String!
  uploadId: String
  processingStrategy: ProcessingStrategy
  processingStatus: ProcessingStatus
  statusCheckUrl: String
  warnings: [String!]
}

type DocumentUploadResult {
  success: Boolean!
  document: Document
  uploadId: String!
  fileName: String!
  fileSize: Long!
  processingStrategy: ProcessingStrategy!
  processingStatus: ProcessingStatus!
  processingJobId: String
  statusCheckUrl: String
  chunkUploadUrl: String
  message: String!
  errorMessage: String
  warnings: [String!]
  uploadedAt: DateTime!
}

# Processing Strategy and Status Enums (extending existing enums)
extend enum ProcessingStrategy {
  AUTO
}

extend enum ProcessingStatus {
  PAUSED
}

# File Validation Types
type FileValidationResult {
  isValid: Boolean!
  validationErrors: [FileValidationError!]!
  warnings: [String!]
  fileInfo: FileInfo!
}

type FileValidationError {
  code: String!
  message: String!
  severity: ValidationSeverity!
  field: String
}

enum ValidationSeverity {
  ERROR
  WARNING
  INFO
}

type FileInfo {
  originalFileName: String!
  fileSize: Long!
  mimeType: String!
  extension: String
  isEncrypted: Boolean!
  checksum: String
  virusScanResult: VirusScanResult
}

type VirusScanResult {
  isClean: Boolean!
  scannerUsed: VirusScannerType!
  scanDate: DateTime!
  threatDetails: String
  quarantined: Boolean!
}

# Upload Progress Tracking Types
type UploadProgress {
  uploadId: String!
  fileName: String!
  totalSize: Long!
  uploadedSize: Long!
  progress: Float!
  status: ProcessingStatus!
  startedAt: DateTime!
  lastUpdatedAt: DateTime!
  estimatedTimeRemaining: Long
  transferRate: Float
  errorMessage: String
}

type ChunkedUploadSession {
  sessionId: String!
  fileName: String!
  totalSize: Long!
  chunkSize: Int!
  totalChunks: Int!
  uploadedChunks: Int!
  progress: Float!
  status: ProcessingStatus!
  createdAt: DateTime!
  expiresAt: DateTime!
  lastActivityAt: DateTime!
  errorMessage: String
}

# Multipart Upload Input Types
# Basic document upload input (for backward compatibility)
input DocumentUploadInput {
  file: Upload!
  name: String!
  description: String
  keywords: [String!]
  storageProvider: StorageProvider
}

input EnhancedDocumentUploadInput {
  file: Upload!
  name: String!
  description: String
  keywords: [String!]
  storageProvider: StorageProvider
  overrideFile: Boolean = false
  
  # Validation options
  skipVirusScan: Boolean = false
  scannerType: VirusScannerType
  validateFileType: Boolean = true
  allowedMimeTypes: [String!]
  maxFileSize: Long
  
  # Processing options
  forceProcessingStrategy: ProcessingStrategy
  chunkSize: Int
  enableProgressTracking: Boolean = true
  
  # Metadata fields
  classificationMetadata: DocumentClassificationMetadataInput
  ownershipMetadata: DocumentOwnershipMetadataInput
  complianceMetadata: DocumentComplianceMetadataInput
  
  # Access control
  accessRoles: [DocumentAccessRoleInput!]
  inheritPermissions: Boolean = false
  
  # Notification options
  notifyOnCompletion: Boolean = false
  notificationEmail: String
}



enum AccessRole {
  OWNER
  EDITOR
  VIEWER
  REVIEWER
  APPROVER
}

enum DocumentPermissionType {
  READ
  WRITE
  DELETE
  SHARE
  APPROVE
}

input BulkDocumentUploadInput {
  files: [Upload!]!
  commonMetadata: DocumentUploadMetadataInput
  processingOptions: BulkProcessingOptionsInput
  validationOptions: FileValidationOptionsInput
}

input DocumentUploadMetadataInput {
  description: String
  keywords: [String!]
  storageProvider: StorageProvider
  classificationMetadata: DocumentClassificationMetadataInput
  ownershipMetadata: DocumentOwnershipMetadataInput
  complianceMetadata: DocumentComplianceMetadataInput
}

input BulkProcessingOptionsInput {
  processingStrategy: ProcessingStrategy
  chunkSize: Int
  maxConcurrentUploads: Int = 5
  continueOnError: Boolean = true
  notifyOnCompletion: Boolean = false
  notificationEmail: String
}

input FileValidationOptionsInput {
  skipVirusScan: Boolean = false
  scannerType: VirusScannerType
  validateFileType: Boolean = true
  allowedMimeTypes: [String!]
  maxFileSize: Long
  allowDuplicates: Boolean = false
}

# Chunked Upload Input Types
input ChunkedUploadInitInput {
  fileName: String!
  totalSize: Long!
  chunkSize: Int
  mimeType: String
  metadata: DocumentUploadMetadataInput
}

input ChunkUploadInput {
  sessionId: String!
  chunkNumber: Int!
  chunk: Upload!
  isLastChunk: Boolean = false
  chunkChecksum: String
}

input CompleteChunkedUploadInput {
  sessionId: String!
  finalFileName: String
  metadata: DocumentUploadMetadataInput
  validateIntegrity: Boolean = true
}

# Bulk Upload Result Types
type BulkUploadResult {
  totalFiles: Int!
  successfulUploads: Int!
  failedUploads: Int!
  results: [DocumentUploadResult!]!
  overallStatus: ProcessingStatus!
  startedAt: DateTime!
  completedAt: DateTime
  processingTimeMs: Long
  errors: [BulkUploadError!]
}

type BulkUploadError {
  fileName: String!
  errorCode: String!
  errorMessage: String!
  details: String
}

# Query Extensions
extend type Query {
  # Get upload progress
  getUploadProgress(uploadId: String!): UploadProgress
  
  # Get chunked upload session status
  getChunkedUploadSession(sessionId: String!): ChunkedUploadSession
  
  # Validate file before upload
  validateFile(file: Upload!, validationOptions: FileValidationOptionsInput): FileValidationResult!
  
  # Get upload statistics
  getUploadStatistics(
    dateFrom: DateTime
    dateTo: DateTime
    userId: String
  ): UploadStatistics!
  
  # Get active upload sessions
  getActiveUploadSessions(userId: String): [ChunkedUploadSession!]!
}

# Upload Statistics Type
type UploadStatistics {
  totalUploads: Long!
  successfulUploads: Long!
  failedUploads: Long!
  totalSizeUploaded: Long!
  averageFileSize: Long!
  uploadsByStrategy: [StrategyCount!]!
  uploadsByMimeType: [MimeTypeCount!]!
  uploadTrends: [UploadTrendData!]!
}

type StrategyCount {
  strategy: ProcessingStrategy!
  count: Long!
  percentage: Float!
}

type MimeTypeCount {
  mimeType: String!
  count: Long!
  totalSize: Long!
}

type UploadTrendData {
  date: DateTime!
  uploadCount: Long!
  totalSize: Long!
  averageProcessingTime: Long!
}

# Mutation Extensions
extend type Mutation {
  # Enhanced document upload (replaces REST /api/documents/upload)
  uploadDocumentEnhanced(input: EnhancedDocumentUploadInput!): DocumentUploadResult!
  
  # Bulk document upload
  bulkUploadDocuments(input: BulkDocumentUploadInput!): BulkUploadResult!
  
  # Chunked upload operations - REMOVED: Now handled internally by uploadDocumentEx
  # initializeChunkedUpload, uploadChunk, and completeChunkedUpload are no longer exposed
  # Large files are automatically processed using chunked strategy within uploadDocumentEx
  
  # Upload management operations
  cancelUpload(uploadId: String!): Boolean!
  pauseUpload(uploadId: String!): Boolean!
  resumeUpload(uploadId: String!): Boolean!
  
  # Cleanup operations
  cleanupExpiredSessions(olderThanHours: Int = 24): Int!
  cleanupFailedUploads(olderThanDays: Int = 7): Int!
}
