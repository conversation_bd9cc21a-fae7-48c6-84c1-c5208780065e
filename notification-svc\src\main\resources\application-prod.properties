# Production Environment Overrides
# This file contains only production-specific settings that override application.properties

# Environment Identifier
ENVIRONMENT=production

# Server Configuration - Production port
server.port=${SERVER_PORT:8080}

# Database Configuration - Strict validation for production
spring.jpa.hibernate.ddl-auto=validate
spring.jpa.show-sql=false

# Connection Pool Configuration - Production optimized
spring.datasource.hikari.maximum-pool-size=${DB_POOL_SIZE:20}
spring.datasource.hikari.minimum-idle=${DB_POOL_MIN_IDLE:5}
spring.datasource.hikari.idle-timeout=600000
spring.datasource.hikari.max-lifetime=1800000

# Logging Configuration - Production level
logging.level.com.ascentbusiness.notification_svc=INFO
logging.level.org.springframework.security=WARN
logging.level.org.hibernate=WARN
logging.level.org.springframework.web=WARN

# GraphQL Configuration - Disabled for production
spring.graphql.graphiql.enabled=false
spring.graphql.schema.printer.enabled=false

# Security Configuration - Strict CORS for production
security.cors.allowed-origins=${CORS_ALLOWED_ORIGINS}
spring.graphql.cors.allowed-origins=${CORS_ALLOWED_ORIGINS}

# Cache Configuration - Production TTL
spring.cache.redis.key-prefix=notification:prod:

# Notification Configuration - Production settings
notification.email.mock=false

# Actuator Configuration - Minimal exposure for production
management.endpoints.web.exposure.include=health,metrics
management.endpoint.health.show-details=never
management.endpoint.health.show-components=never

# Performance Configuration - Production optimized
spring.jpa.properties.hibernate.jdbc.batch_size=50
server.compression.enabled=true
server.tomcat.max-connections=8192
server.tomcat.threads.max=200
server.tomcat.threads.min-spare=10

# Async Configuration - Production optimized
spring.task.execution.pool.core-size=10
spring.task.execution.pool.max-size=50
spring.task.execution.pool.queue-capacity=100
spring.task.execution.thread-name-prefix=notification-async-prod-

# Connection Pool Configuration - Production optimized
spring.data.redis.lettuce.pool.max-active=20
spring.data.redis.lettuce.pool.max-idle=10
spring.data.redis.lettuce.pool.min-idle=2

# Notification Processing Configuration - Production settings
notification.processing.batch-size=100
notification.template.cache-ttl=3600

# Development Features - Disabled for production
spring.devtools.restart.enabled=false
spring.devtools.livereload.enabled=false

# Spring Configuration - Stricter for production
spring.main.allow-bean-definition-overriding=false