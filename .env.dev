# Development Environment Variables for DMS Service
# This file contains development-specific environment variable values

# Server Configuration
SERVER_PORT=9093

# Database Configuration
DB_URL=***************************************************************************************************************************************
DB_USERNAME=dms_dev_user
DB_PASSWORD=dev_password

# Redis Configuration
REDIS_HOST=localhost
REDIS_PORT=6379
REDIS_PASSWORD=
REDIS_DATABASE=0

# JWT Configuration
JWT_SECRET=devSecretKey123456789012345678901234567890ABCDEFGHIJKLMNOPQRSTUVWXYZ
JWT_EXPIRATION=86400000

# GraphQL Configuration
GRAPHIQL_ENABLED=true
GRAPHQL_SCHEMA_PRINTER_ENABLED=true

# CORS Configuration
CORS_ALLOWED_ORIGINS=http://localhost:3000,http://localhost:8080,http://localhost:9093
CORS_ALLOWED_METHODS=GET,POST,PUT,DELETE,OPTIONS,PATCH
CORS_ALLOWED_HEADERS=Content-Type,Authorization,X-Correlation-ID,Accept
CORS_ALLOW_CREDENTIALS=true

# JPA Configuration
JPA_DDL_AUTO=update
JPA_SHOW_SQL=true
HIBERNATE_FORMAT_SQL=true

# Logging Configuration
LOG_LEVEL_DMS=DEBUG
LOG_LEVEL_SECURITY=DEBUG

# Cache Configuration
CACHE_TYPE=redis
CACHE_TTL=300000
CACHE_KEY_PREFIX=dms:dev:

# Storage Configuration
STORAGE_PROVIDER=LOCAL
STORAGE_PATH=./storage/documents

# Elasticsearch Configuration
ELASTICSEARCH_ENABLED=true
ELASTICSEARCH_HOST=localhost
ELASTICSEARCH_PORT=9200

# Virus Scanning Configuration
VIRUS_SCANNING_ENABLED=false
VIRUS_SCANNING_DEFAULT_SCANNER=MOCK

# Rate Limiting Configuration
RATE_LIMIT_ENABLED=false

# OpenTelemetry Configuration
OTEL_SERVICE_NAME=dms-svc-dev
OTEL_SERVICE_VERSION=1.0.0-dev
OTEL_TRACES_SAMPLER_ARG=1.0

# Application Base URL
DMS_BASE_URL=http://localhost:9093

# Environment Identifier
ENVIRONMENT=development