# PowerShell script to test Word to PDF conversion
$url = "http://localhost:9093/graphql"
$wordFile = "manual-test-input.docx"

# Check if the Word file exists
if (-not (Test-Path $wordFile)) {
    Write-Host "Word file not found: $wordFile" -ForegroundColor Red
    exit 1
}

# Read the Word file as bytes and convert to base64
$wordBytes = [System.IO.File]::ReadAllBytes($wordFile)
$base64Content = [System.Convert]::ToBase64String($wordBytes)

# Create the GraphQL mutation
$mutation = @"
mutation {
  convertWordToPdfMultipart(
    file: {
      filename: "test-document.docx"
      contentType: "application/vnd.openxmlformats-officedocument.wordprocessingml.document"
      content: "$base64Content"
    }
    scannerType: MOCK
  ) {
    success
    originalFileName
    convertedFileName
    downloadPath
    fileSize
    sessionId
    virusScanResponse {
      scanResult
      scanTimestamp
      scannerType
    }
  }
}
"@

# Create the request body
$body = @{
    query = $mutation
} | ConvertTo-Json -Depth 10

# Make the request
try {
    Write-Host "Testing Word to PDF conversion..." -ForegroundColor Yellow
    Write-Host "Word file size: $($wordBytes.Length) bytes" -ForegroundColor Green
    
    $response = Invoke-RestMethod -Uri $url -Method POST -Body $body -ContentType "application/json"
    
    if ($response.errors) {
        Write-Host "GraphQL Errors:" -ForegroundColor Red
        $response.errors | ForEach-Object { Write-Host "  - $($_.message)" -ForegroundColor Red }
    }
    
    if ($response.data.convertWordToPdfMultipart) {
        $result = $response.data.convertWordToPdfMultipart
        Write-Host "Conversion Result:" -ForegroundColor Green
        Write-Host "  Success: $($result.success)" -ForegroundColor $(if ($result.success) { "Green" } else { "Red" })
        Write-Host "  Original File: $($result.originalFileName)" -ForegroundColor Cyan
        Write-Host "  Converted File: $($result.convertedFileName)" -ForegroundColor Cyan
        Write-Host "  Download Path: $($result.downloadPath)" -ForegroundColor Cyan
        Write-Host "  File Size: $($result.fileSize) bytes" -ForegroundColor Cyan
        Write-Host "  Session ID: $($result.sessionId)" -ForegroundColor Cyan
        
        if ($result.virusScanResponse) {
            Write-Host "  Virus Scan: $($result.virusScanResponse.scanResult)" -ForegroundColor Cyan
        }
        
        # Check if the converted file exists
        if ($result.downloadPath -and (Test-Path $result.downloadPath)) {
            Write-Host "  ✓ Converted PDF file exists!" -ForegroundColor Green
            $pdfSize = (Get-Item $result.downloadPath).Length
            Write-Host "  PDF file size: $pdfSize bytes" -ForegroundColor Green
        } else {
            Write-Host "  ✗ Converted PDF file not found at: $($result.downloadPath)" -ForegroundColor Red
        }
    }

} catch {
    Write-Host "Error making request: $($_.Exception.Message)" -ForegroundColor Red
    if ($_.Exception.Response) {
        Write-Host "Response: $($_.Exception.Response)" -ForegroundColor Red
    }
}
