<?xml version="1.0" encoding="UTF-8"?><!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Strict//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-strict.dtd"><html xmlns="http://www.w3.org/1999/xhtml" lang="en"><head><meta http-equiv="Content-Type" content="text/html;charset=UTF-8"/><link rel="stylesheet" href="../jacoco-resources/report.css" type="text/css"/><link rel="shortcut icon" href="../jacoco-resources/report.gif" type="image/gif"/><title>GraphQLMonitoringConfig.java</title><link rel="stylesheet" href="../jacoco-resources/prettify.css" type="text/css"/><script type="text/javascript" src="../jacoco-resources/prettify.js"></script></head><body onload="window['PR_TAB_WIDTH']=4;prettyPrint()"><div class="breadcrumb" id="breadcrumb"><span class="info"><a href="../jacoco-sessions.html" class="el_session">Sessions</a></span><a href="../index.html" class="el_report">dms-svc</a> &gt; <a href="index.source.html" class="el_package">com.ascentbusiness.dms_svc.config</a> &gt; <span class="el_source">GraphQLMonitoringConfig.java</span></div><h1>GraphQLMonitoringConfig.java</h1><pre class="source lang-java linenums">/*
 * Copyright (c) 2024 Ascent Business Technology
 * All rights reserved.
 */

package com.ascentbusiness.dms_svc.config;

import io.micrometer.core.instrument.MeterRegistry;
import io.micrometer.core.instrument.Timer;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.annotation.Configuration;

import java.util.concurrent.atomic.AtomicLong;

/**
 * Configuration for GraphQL monitoring and metrics collection.
 * Provides comprehensive monitoring of GraphQL operations including:
 * - Query execution time tracking
 * - Error rate monitoring
 * - Field-level performance metrics
 * - Query complexity analysis
 */
@Configuration
<span class="fc" id="L24">@Slf4j</span>
public class GraphQLMonitoringConfig {

    private final MeterRegistry meterRegistry;
<span class="fc" id="L28">    private final AtomicLong activeQueries = new AtomicLong(0);</span>
<span class="fc" id="L29">    private final AtomicLong totalQueries = new AtomicLong(0);</span>
<span class="fc" id="L30">    private final AtomicLong totalErrors = new AtomicLong(0);</span>

<span class="fc" id="L32">    public GraphQLMonitoringConfig(MeterRegistry meterRegistry) {</span>
<span class="fc" id="L33">        this.meterRegistry = meterRegistry;</span>
<span class="fc" id="L34">        initializeMetrics();</span>
<span class="fc" id="L35">    }</span>

    /**
     * Initialize custom metrics for GraphQL monitoring
     */
    private void initializeMetrics() {
        // Register gauges for active queries
<span class="fc" id="L42">        meterRegistry.gauge(&quot;graphql.queries.active&quot;, activeQueries);</span>
<span class="fc" id="L43">        meterRegistry.gauge(&quot;graphql.queries.total&quot;, totalQueries);</span>
<span class="fc" id="L44">        meterRegistry.gauge(&quot;graphql.errors.total&quot;, totalErrors);</span>
        
<span class="fc" id="L46">        log.info(&quot;GraphQL monitoring metrics initialized&quot;);</span>
<span class="fc" id="L47">    }</span>

    /**
     * Simple metrics tracking for GraphQL operations
     */
    public void recordQueryStart() {
<span class="nc" id="L53">        activeQueries.incrementAndGet();</span>
<span class="nc" id="L54">        totalQueries.incrementAndGet();</span>
<span class="nc" id="L55">    }</span>

    public void recordQueryEnd(boolean hasErrors) {
<span class="nc" id="L58">        activeQueries.decrementAndGet();</span>
<span class="nc bnc" id="L59" title="All 2 branches missed.">        if (hasErrors) {</span>
<span class="nc" id="L60">            totalErrors.incrementAndGet();</span>
        }
<span class="nc" id="L62">    }</span>

    public void recordExecutionTime(String operation, long durationMs, boolean hasErrors) {
<span class="nc" id="L65">        Timer.Sample sample = Timer.start(meterRegistry);</span>
<span class="nc" id="L66">        sample.stop(Timer.builder(&quot;graphql.execution.time&quot;)</span>
<span class="nc" id="L67">                .description(&quot;GraphQL query execution time&quot;)</span>
<span class="nc bnc" id="L68" title="All 2 branches missed.">                .tag(&quot;operation&quot;, operation != null ? operation : &quot;anonymous&quot;)</span>
<span class="nc bnc" id="L69" title="All 2 branches missed.">                .tag(&quot;status&quot;, hasErrors ? &quot;error&quot; : &quot;success&quot;)</span>
<span class="nc" id="L70">                .register(meterRegistry));</span>

<span class="nc bnc" id="L72" title="All 2 branches missed.">        if (hasErrors) {</span>
<span class="nc" id="L73">            meterRegistry.counter(&quot;graphql.errors&quot;,</span>
<span class="nc bnc" id="L74" title="All 2 branches missed.">                    &quot;operation&quot;, operation != null ? operation : &quot;anonymous&quot;)</span>
<span class="nc" id="L75">                    .increment();</span>
        }

<span class="nc" id="L78">        log.debug(&quot;GraphQL execution completed - Operation: {}, Duration: {}ms, HasErrors: {}&quot;,</span>
<span class="nc" id="L79">                operation, durationMs, hasErrors);</span>
<span class="nc" id="L80">    }</span>

    /**
     * Get current GraphQL metrics summary
     */
    public GraphQLMetricsSummary getMetricsSummary() {
<span class="nc" id="L86">        return GraphQLMetricsSummary.builder()</span>
<span class="nc" id="L87">                .activeQueries(activeQueries.get())</span>
<span class="nc" id="L88">                .totalQueries(totalQueries.get())</span>
<span class="nc" id="L89">                .totalErrors(totalErrors.get())</span>
<span class="nc bnc" id="L90" title="All 2 branches missed.">                .errorRate(totalQueries.get() &gt; 0 ?</span>
<span class="nc" id="L91">                          (double) totalErrors.get() / totalQueries.get() * 100 : 0.0)</span>
<span class="nc" id="L92">                .build();</span>
    }

    /**
     * Reset all metrics (useful for testing)
     */
    public void resetMetrics() {
<span class="nc" id="L99">        activeQueries.set(0);</span>
<span class="nc" id="L100">        totalQueries.set(0);</span>
<span class="nc" id="L101">        totalErrors.set(0);</span>
<span class="nc" id="L102">        log.info(&quot;GraphQL monitoring metrics reset&quot;);</span>
<span class="nc" id="L103">    }</span>

    /**
     * Data class for GraphQL metrics summary
     */
    public static class GraphQLMetricsSummary {
        private final long activeQueries;
        private final long totalQueries;
        private final long totalErrors;
        private final double errorRate;

        private GraphQLMetricsSummary(long activeQueries, long totalQueries, 
<span class="nc" id="L115">                                     long totalErrors, double errorRate) {</span>
<span class="nc" id="L116">            this.activeQueries = activeQueries;</span>
<span class="nc" id="L117">            this.totalQueries = totalQueries;</span>
<span class="nc" id="L118">            this.totalErrors = totalErrors;</span>
<span class="nc" id="L119">            this.errorRate = errorRate;</span>
<span class="nc" id="L120">        }</span>

        public static Builder builder() {
<span class="nc" id="L123">            return new Builder();</span>
        }

<span class="nc" id="L126">        public long getActiveQueries() { return activeQueries; }</span>
<span class="nc" id="L127">        public long getTotalQueries() { return totalQueries; }</span>
<span class="nc" id="L128">        public long getTotalErrors() { return totalErrors; }</span>
<span class="nc" id="L129">        public double getErrorRate() { return errorRate; }</span>

<span class="nc" id="L131">        public static class Builder {</span>
            private long activeQueries;
            private long totalQueries;
            private long totalErrors;
            private double errorRate;

            public Builder activeQueries(long activeQueries) {
<span class="nc" id="L138">                this.activeQueries = activeQueries;</span>
<span class="nc" id="L139">                return this;</span>
            }

            public Builder totalQueries(long totalQueries) {
<span class="nc" id="L143">                this.totalQueries = totalQueries;</span>
<span class="nc" id="L144">                return this;</span>
            }

            public Builder totalErrors(long totalErrors) {
<span class="nc" id="L148">                this.totalErrors = totalErrors;</span>
<span class="nc" id="L149">                return this;</span>
            }

            public Builder errorRate(double errorRate) {
<span class="nc" id="L153">                this.errorRate = errorRate;</span>
<span class="nc" id="L154">                return this;</span>
            }

            public GraphQLMetricsSummary build() {
<span class="nc" id="L158">                return new GraphQLMetricsSummary(activeQueries, totalQueries, totalErrors, errorRate);</span>
            }
        }
    }
}
</pre><div class="footer"><span class="right">Created with <a href="http://www.jacoco.org/jacoco">JaCoCo</a> 0.8.10.202304240956</span></div></body></html>