<?xml version="1.0" encoding="UTF-8"?><!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Strict//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-strict.dtd"><html xmlns="http://www.w3.org/1999/xhtml" lang="en"><head><meta http-equiv="Content-Type" content="text/html;charset=UTF-8"/><link rel="stylesheet" href="../jacoco-resources/report.css" type="text/css"/><link rel="shortcut icon" href="../jacoco-resources/report.gif" type="image/gif"/><title>DiagnosticsGraphQLResolver</title><script type="text/javascript" src="../jacoco-resources/sort.js"></script></head><body onload="initialSort(['breadcrumb'])"><div class="breadcrumb" id="breadcrumb"><span class="info"><a href="../jacoco-sessions.html" class="el_session">Sessions</a></span><a href="../index.html" class="el_report">dms-svc</a> &gt; <a href="index.html" class="el_package">com.ascentbusiness.dms_svc.resolver</a> &gt; <span class="el_class">DiagnosticsGraphQLResolver</span></div><h1>DiagnosticsGraphQLResolver</h1><table class="coverage" cellspacing="0" id="coveragetable"><thead><tr><td class="sortable" id="a" onclick="toggleSort(this)">Element</td><td class="down sortable bar" id="b" onclick="toggleSort(this)">Missed Instructions</td><td class="sortable ctr2" id="c" onclick="toggleSort(this)">Cov.</td><td class="sortable bar" id="d" onclick="toggleSort(this)">Missed Branches</td><td class="sortable ctr2" id="e" onclick="toggleSort(this)">Cov.</td><td class="sortable ctr1" id="f" onclick="toggleSort(this)">Missed</td><td class="sortable ctr2" id="g" onclick="toggleSort(this)">Cxty</td><td class="sortable ctr1" id="h" onclick="toggleSort(this)">Missed</td><td class="sortable ctr2" id="i" onclick="toggleSort(this)">Lines</td><td class="sortable ctr1" id="j" onclick="toggleSort(this)">Missed</td><td class="sortable ctr2" id="k" onclick="toggleSort(this)">Methods</td></tr></thead><tfoot><tr><td>Total</td><td class="bar">2,386 of 2,403</td><td class="ctr2">0%</td><td class="bar">163 of 163</td><td class="ctr2">0%</td><td class="ctr1">122</td><td class="ctr2">124</td><td class="ctr1">659</td><td class="ctr2">663</td><td class="ctr1">36</td><td class="ctr2">38</td></tr></tfoot><tbody><tr><td id="a9"><a href="DiagnosticsGraphQLResolver.java.html#L706" class="el_method">getPerformanceHistory(String, Integer)</a></td><td class="bar" id="b0"><img src="../jacoco-resources/redbar.gif" width="120" height="10" title="292" alt="292"/></td><td class="ctr2" id="c2">0%</td><td class="bar" id="d12"><img src="../jacoco-resources/redbar.gif" width="26" height="10" title="4" alt="4"/></td><td class="ctr2" id="e0">0%</td><td class="ctr1" id="f13">3</td><td class="ctr2" id="g13">3</td><td class="ctr1" id="h1">54</td><td class="ctr2" id="i1">54</td><td class="ctr1" id="j0">1</td><td class="ctr2" id="k0">1</td></tr><tr><td id="a11"><a href="DiagnosticsGraphQLResolver.java.html#L508" class="el_method">getSystemPerformance()</a></td><td class="bar" id="b1"><img src="../jacoco-resources/redbar.gif" width="75" height="10" title="183" alt="183"/></td><td class="ctr2" id="c3">0%</td><td class="bar" id="d28"/><td class="ctr2" id="e28">n/a</td><td class="ctr1" id="f28">1</td><td class="ctr2" id="g28">1</td><td class="ctr1" id="h0">63</td><td class="ctr2" id="i0">63</td><td class="ctr1" id="j1">1</td><td class="ctr2" id="k1">1</td></tr><tr><td id="a8"><a href="DiagnosticsGraphQLResolver.java.html#L370" class="el_method">getHealthSummary()</a></td><td class="bar" id="b2"><img src="../jacoco-resources/redbar.gif" width="71" height="10" title="175" alt="175"/></td><td class="ctr2" id="c4">0%</td><td class="bar" id="d4"><img src="../jacoco-resources/redbar.gif" width="80" height="10" title="12" alt="12"/></td><td class="ctr2" id="e1">0%</td><td class="ctr1" id="f4">7</td><td class="ctr2" id="g4">7</td><td class="ctr1" id="h3">44</td><td class="ctr2" id="i3">44</td><td class="ctr1" id="j2">1</td><td class="ctr2" id="k2">1</td></tr><tr><td id="a29"><a href="DiagnosticsGraphQLResolver.java.html#L271" class="el_method">testConnection(ConnectionTestInput)</a></td><td class="bar" id="b3"><img src="../jacoco-resources/redbar.gif" width="64" height="10" title="157" alt="157"/></td><td class="ctr2" id="c5">0%</td><td class="bar" id="d2"><img src="../jacoco-resources/redbar.gif" width="93" height="10" title="14" alt="14"/></td><td class="ctr2" id="e2">0%</td><td class="ctr1" id="f2">8</td><td class="ctr2" id="g2">8</td><td class="ctr1" id="h4">44</td><td class="ctr2" id="i4">44</td><td class="ctr1" id="j3">1</td><td class="ctr2" id="k3">1</td></tr><tr><td id="a7"><a href="DiagnosticsGraphQLResolver.java.html#L660" class="el_method">getDiagnosticHistory(Integer, OffsetDateTime, OffsetDateTime)</a></td><td class="bar" id="b4"><img src="../jacoco-resources/redbar.gif" width="61" height="10" title="149" alt="149"/></td><td class="ctr2" id="c6">0%</td><td class="bar" id="d1"><img src="../jacoco-resources/redbar.gif" width="106" height="10" title="16" alt="16"/></td><td class="ctr2" id="e3">0%</td><td class="ctr1" id="f1">9</td><td class="ctr2" id="g1">9</td><td class="ctr1" id="h10">29</td><td class="ctr2" id="i10">29</td><td class="ctr1" id="j4">1</td><td class="ctr2" id="k4">1</td></tr><tr><td id="a10"><a href="DiagnosticsGraphQLResolver.java.html#L589" class="el_method">getSystemHealth()</a></td><td class="bar" id="b5"><img src="../jacoco-resources/redbar.gif" width="57" height="10" title="139" alt="139"/></td><td class="ctr2" id="c7">0%</td><td class="bar" id="d3"><img src="../jacoco-resources/redbar.gif" width="93" height="10" title="14" alt="14"/></td><td class="ctr2" id="e4">0%</td><td class="ctr1" id="f3">8</td><td class="ctr2" id="g3">8</td><td class="ctr1" id="h5">41</td><td class="ctr2" id="i5">41</td><td class="ctr1" id="j5">1</td><td class="ctr2" id="k5">1</td></tr><tr><td id="a22"><a href="DiagnosticsGraphQLResolver.java.html#L989" class="el_method">performSingleDiagnosticTest(DiagnosticTestType)</a></td><td class="bar" id="b6"><img src="../jacoco-resources/redbar.gif" width="56" height="10" title="137" alt="137"/></td><td class="ctr2" id="c8">0%</td><td class="bar" id="d0"><img src="../jacoco-resources/redbar.gif" width="120" height="10" title="18" alt="18"/></td><td class="ctr2" id="e5">0%</td><td class="ctr1" id="f0">12</td><td class="ctr2" id="g0">12</td><td class="ctr1" id="h2">46</td><td class="ctr2" id="i2">46</td><td class="ctr1" id="j6">1</td><td class="ctr2" id="k6">1</td></tr><tr><td id="a26"><a href="DiagnosticsGraphQLResolver.java.html#L446" class="el_method">runDiagnostics(DiagnosticTestInput)</a></td><td class="bar" id="b7"><img src="../jacoco-resources/redbar.gif" width="53" height="10" title="130" alt="130"/></td><td class="ctr2" id="c9">0%</td><td class="bar" id="d6"><img src="../jacoco-resources/redbar.gif" width="53" height="10" title="8" alt="8"/></td><td class="ctr2" id="e6">0%</td><td class="ctr1" id="f6">5</td><td class="ctr2" id="g6">5</td><td class="ctr1" id="h7">35</td><td class="ctr2" id="i7">35</td><td class="ctr1" id="j7">1</td><td class="ctr2" id="k7">1</td></tr><tr><td id="a30"><a href="DiagnosticsGraphQLResolver.java.html#L137" class="el_method">testDatabaseConnection()</a></td><td class="bar" id="b8"><img src="../jacoco-resources/redbar.gif" width="42" height="10" title="103" alt="103"/></td><td class="ctr2" id="c10">0%</td><td class="bar" id="d8"><img src="../jacoco-resources/redbar.gif" width="40" height="10" title="6" alt="6"/></td><td class="ctr2" id="e7">0%</td><td class="ctr1" id="f9">4</td><td class="ctr2" id="g9">4</td><td class="ctr1" id="h8">34</td><td class="ctr2" id="i8">34</td><td class="ctr1" id="j8">1</td><td class="ctr2" id="k8">1</td></tr><tr><td id="a34"><a href="DiagnosticsGraphQLResolver.java.html#L80" class="el_method">testSharePointConnection()</a></td><td class="bar" id="b9"><img src="../jacoco-resources/redbar.gif" width="41" height="10" title="101" alt="101"/></td><td class="ctr2" id="c11">0%</td><td class="bar" id="d16"><img src="../jacoco-resources/redbar.gif" width="13" height="10" title="2" alt="2"/></td><td class="ctr2" id="e8">0%</td><td class="ctr1" id="f16">2</td><td class="ctr2" id="g16">2</td><td class="ctr1" id="h6">41</td><td class="ctr2" id="i6">41</td><td class="ctr1" id="j9">1</td><td class="ctr2" id="k9">1</td></tr><tr><td id="a36"><a href="DiagnosticsGraphQLResolver.java.html#L224" class="el_method">testStorageProviderConnection(StorageProvider)</a></td><td class="bar" id="b10"><img src="../jacoco-resources/redbar.gif" width="38" height="10" title="94" alt="94"/></td><td class="ctr2" id="c12">0%</td><td class="bar" id="d13"><img src="../jacoco-resources/redbar.gif" width="26" height="10" title="4" alt="4"/></td><td class="ctr2" id="e9">0%</td><td class="ctr1" id="f14">3</td><td class="ctr2" id="g14">3</td><td class="ctr1" id="h9">32</td><td class="ctr2" id="i9">32</td><td class="ctr1" id="j10">1</td><td class="ctr2" id="k10">1</td></tr><tr><td id="a0"><a href="DiagnosticsGraphQLResolver.java.html#L1057" class="el_method">createComponentHealth(String, Health)</a></td><td class="bar" id="b11"><img src="../jacoco-resources/redbar.gif" width="35" height="10" title="86" alt="86"/></td><td class="ctr2" id="c13">0%</td><td class="bar" id="d5"><img src="../jacoco-resources/redbar.gif" width="66" height="10" title="10" alt="10"/></td><td class="ctr2" id="e10">0%</td><td class="ctr1" id="f5">6</td><td class="ctr2" id="g5">6</td><td class="ctr1" id="h11">24</td><td class="ctr2" id="i11">24</td><td class="ctr1" id="j11">1</td><td class="ctr2" id="k11">1</td></tr><tr><td id="a32"><a href="DiagnosticsGraphQLResolver.java.html#L188" class="el_method">testElasticsearchConnection()</a></td><td class="bar" id="b12"><img src="../jacoco-resources/redbar.gif" width="29" height="10" title="71" alt="71"/></td><td class="ctr2" id="c14">0%</td><td class="bar" id="d14"><img src="../jacoco-resources/redbar.gif" width="26" height="10" title="4" alt="4"/></td><td class="ctr2" id="e11">0%</td><td class="ctr1" id="f15">3</td><td class="ctr2" id="g15">3</td><td class="ctr1" id="h13">21</td><td class="ctr2" id="i13">21</td><td class="ctr1" id="j12">1</td><td class="ctr2" id="k12">1</td></tr><tr><td id="a5"><a href="DiagnosticsGraphQLResolver.java.html#L330" class="el_method">getApplicationMetrics()</a></td><td class="bar" id="b13"><img src="../jacoco-resources/redbar.gif" width="29" height="10" title="71" alt="71"/></td><td class="ctr2" id="c15">0%</td><td class="bar" id="d29"/><td class="ctr2" id="e29">n/a</td><td class="ctr1" id="f29">1</td><td class="ctr2" id="g29">1</td><td class="ctr1" id="h12">24</td><td class="ctr2" id="i12">24</td><td class="ctr1" id="j13">1</td><td class="ctr2" id="k13">1</td></tr><tr><td id="a2"><a href="DiagnosticsGraphQLResolver.java.html#L1144" class="el_method">createMockComponentHealth(String, HealthStatus)</a></td><td class="bar" id="b14"><img src="../jacoco-resources/redbar.gif" width="24" height="10" title="60" alt="60"/></td><td class="ctr2" id="c16">0%</td><td class="bar" id="d7"><img src="../jacoco-resources/redbar.gif" width="53" height="10" title="8" alt="8"/></td><td class="ctr2" id="e12">0%</td><td class="ctr1" id="f7">5</td><td class="ctr2" id="g7">5</td><td class="ctr1" id="h14">17</td><td class="ctr2" id="i14">17</td><td class="ctr1" id="j14">1</td><td class="ctr2" id="k14">1</td></tr><tr><td id="a21"><a href="DiagnosticsGraphQLResolver.java.html#L970" class="el_method">performDiagnosticTests(DiagnosticTestInput)</a></td><td class="bar" id="b15"><img src="../jacoco-resources/redbar.gif" width="18" height="10" title="44" alt="44"/></td><td class="ctr2" id="c17">0%</td><td class="bar" id="d9"><img src="../jacoco-resources/redbar.gif" width="40" height="10" title="6" alt="6"/></td><td class="ctr2" id="e13">0%</td><td class="ctr1" id="f10">4</td><td class="ctr2" id="g10">4</td><td class="ctr1" id="h20">8</td><td class="ctr2" id="i20">8</td><td class="ctr1" id="j15">1</td><td class="ctr2" id="k15">1</td></tr><tr><td id="a6"><a href="DiagnosticsGraphQLResolver.java.html#L777" class="el_method">getComponentHealth(String)</a></td><td class="bar" id="b16"><img src="../jacoco-resources/redbar.gif" width="17" height="10" title="42" alt="42"/></td><td class="ctr2" id="c18">0%</td><td class="bar" id="d17"><img src="../jacoco-resources/redbar.gif" width="13" height="10" title="2" alt="2"/></td><td class="ctr2" id="e14">0%</td><td class="ctr1" id="f17">2</td><td class="ctr2" id="g17">2</td><td class="ctr1" id="h16">10</td><td class="ctr2" id="i16">10</td><td class="ctr1" id="j16">1</td><td class="ctr2" id="k16">1</td></tr><tr><td id="a20"><a href="DiagnosticsGraphQLResolver.java.html#L898" class="el_method">performConnectionTest(ConnectionTestInput)</a></td><td class="bar" id="b17"><img src="../jacoco-resources/redbar.gif" width="14" height="10" title="35" alt="35"/></td><td class="ctr2" id="c19">0%</td><td class="bar" id="d11"><img src="../jacoco-resources/redbar.gif" width="33" height="10" title="5" alt="5"/></td><td class="ctr2" id="e15">0%</td><td class="ctr1" id="f8">5</td><td class="ctr2" id="g8">5</td><td class="ctr1" id="h17">10</td><td class="ctr2" id="i17">10</td><td class="ctr1" id="j17">1</td><td class="ctr2" id="k17">1</td></tr><tr><td id="a16"><a href="DiagnosticsGraphQLResolver.java.html#L462" class="el_method">lambda$runDiagnostics$4(DiagnosticTest)</a></td><td class="bar" id="b18"><img src="../jacoco-resources/redbar.gif" width="12" height="10" title="31" alt="31"/></td><td class="ctr2" id="c20">0%</td><td class="bar" id="d18"><img src="../jacoco-resources/redbar.gif" width="13" height="10" title="2" alt="2"/></td><td class="ctr2" id="e16">0%</td><td class="ctr1" id="f18">2</td><td class="ctr2" id="g18">2</td><td class="ctr1" id="h18">9</td><td class="ctr2" id="i18">9</td><td class="ctr1" id="j18">1</td><td class="ctr2" id="k18">1</td></tr><tr><td id="a37"><a href="DiagnosticsGraphQLResolver.java.html#L877" class="el_method">testStorageProviderInternal(StorageProvider)</a></td><td class="bar" id="b19"><img src="../jacoco-resources/redbar.gif" width="11" height="10" title="29" alt="29"/></td><td class="ctr2" id="c21">0%</td><td class="bar" id="d19"><img src="../jacoco-resources/redbar.gif" width="13" height="10" title="2" alt="2"/></td><td class="ctr2" id="e17">0%</td><td class="ctr1" id="f19">2</td><td class="ctr2" id="g19">2</td><td class="ctr1" id="h19">9</td><td class="ctr2" id="i19">9</td><td class="ctr1" id="j19">1</td><td class="ctr2" id="k19">1</td></tr><tr><td id="a1"><a href="DiagnosticsGraphQLResolver.java.html#L1126" class="el_method">createFailedConnectionTest(ConnectionTestType, String, String)</a></td><td class="bar" id="b20"><img src="../jacoco-resources/redbar.gif" width="10" height="10" title="25" alt="25"/></td><td class="ctr2" id="c22">0%</td><td class="bar" id="d30"/><td class="ctr2" id="e30">n/a</td><td class="ctr1" id="f30">1</td><td class="ctr2" id="g30">1</td><td class="ctr1" id="h15">11</td><td class="ctr2" id="i15">11</td><td class="ctr1" id="j20">1</td><td class="ctr2" id="k20">1</td></tr><tr><td id="a3"><a href="DiagnosticsGraphQLResolver.java.html#L1107" class="el_method">determineOverallStatus(int, int)</a></td><td class="bar" id="b21"><img src="../jacoco-resources/redbar.gif" width="9" height="10" title="24" alt="24"/></td><td class="ctr2" id="c23">0%</td><td class="bar" id="d10"><img src="../jacoco-resources/redbar.gif" width="40" height="10" title="6" alt="6"/></td><td class="ctr2" id="e18">0%</td><td class="ctr1" id="f11">4</td><td class="ctr2" id="g11">4</td><td class="ctr1" id="h21">8</td><td class="ctr2" id="i21">8</td><td class="ctr1" id="j21">1</td><td class="ctr2" id="k21">1</td></tr><tr><td id="a33"><a href="DiagnosticsGraphQLResolver.java.html#L957" class="el_method">testElasticsearchConnectionInternal()</a></td><td class="bar" id="b22"><img src="../jacoco-resources/redbar.gif" width="9" height="10" title="23" alt="23"/></td><td class="ctr2" id="c24">0%</td><td class="bar" id="d31"/><td class="ctr2" id="e31">n/a</td><td class="ctr1" id="f31">1</td><td class="ctr2" id="g31">1</td><td class="ctr1" id="h24">6</td><td class="ctr2" id="i24">6</td><td class="ctr1" id="j22">1</td><td class="ctr2" id="k22">1</td></tr><tr><td id="a31"><a href="DiagnosticsGraphQLResolver.java.html#L923" class="el_method">testDatabaseConnectionInternal()</a></td><td class="bar" id="b23"><img src="../jacoco-resources/redbar.gif" width="9" height="10" title="22" alt="22"/></td><td class="ctr2" id="c25">0%</td><td class="bar" id="d20"><img src="../jacoco-resources/redbar.gif" width="13" height="10" title="2" alt="2"/></td><td class="ctr2" id="e19">0%</td><td class="ctr1" id="f20">2</td><td class="ctr2" id="g20">2</td><td class="ctr1" id="h22">7</td><td class="ctr2" id="i22">7</td><td class="ctr1" id="j23">1</td><td class="ctr2" id="k23">1</td></tr><tr><td id="a23"><a href="DiagnosticsGraphQLResolver.java.html#L804" class="el_method">refreshComponentHealth(String)</a></td><td class="bar" id="b24"><img src="../jacoco-resources/redbar.gif" width="8" height="10" title="21" alt="21"/></td><td class="ctr2" id="c26">0%</td><td class="bar" id="d32"/><td class="ctr2" id="e32">n/a</td><td class="ctr1" id="f32">1</td><td class="ctr2" id="g32">1</td><td class="ctr1" id="h26">5</td><td class="ctr2" id="i26">5</td><td class="ctr1" id="j24">1</td><td class="ctr2" id="k24">1</td></tr><tr><td id="a25"><a href="DiagnosticsGraphQLResolver.java.html#L840" class="el_method">runConnectionTest(ConnectionTestInput)</a></td><td class="bar" id="b25"><img src="../jacoco-resources/redbar.gif" width="8" height="10" title="21" alt="21"/></td><td class="ctr2" id="c27">0%</td><td class="bar" id="d33"/><td class="ctr2" id="e33">n/a</td><td class="ctr1" id="f33">1</td><td class="ctr2" id="g33">1</td><td class="ctr1" id="h27">5</td><td class="ctr2" id="i27">5</td><td class="ctr1" id="j25">1</td><td class="ctr2" id="k25">1</td></tr><tr><td id="a27"><a href="DiagnosticsGraphQLResolver.java.html#L858" class="el_method">runSystemDiagnostics(DiagnosticTestInput)</a></td><td class="bar" id="b26"><img src="../jacoco-resources/redbar.gif" width="7" height="10" title="19" alt="19"/></td><td class="ctr2" id="c28">0%</td><td class="bar" id="d34"/><td class="ctr2" id="e34">n/a</td><td class="ctr1" id="f34">1</td><td class="ctr2" id="g34">1</td><td class="ctr1" id="h28">5</td><td class="ctr2" id="i28">5</td><td class="ctr1" id="j26">1</td><td class="ctr2" id="k26">1</td></tr><tr><td id="a19"><a href="DiagnosticsGraphQLResolver.java.html#L1090" class="el_method">mapSpringHealthStatus(Status)</a></td><td class="bar" id="b27"><img src="../jacoco-resources/redbar.gif" width="7" height="10" title="19" alt="19"/></td><td class="ctr2" id="c29">0%</td><td class="bar" id="d15"><img src="../jacoco-resources/redbar.gif" width="26" height="10" title="4" alt="4"/></td><td class="ctr2" id="e20">0%</td><td class="ctr1" id="f12">4</td><td class="ctr2" id="g12">4</td><td class="ctr1" id="h25">6</td><td class="ctr2" id="i25">6</td><td class="ctr1" id="j27">1</td><td class="ctr2" id="k27">1</td></tr><tr><td id="a24"><a href="DiagnosticsGraphQLResolver.java.html#L822" class="el_method">refreshSystemHealth()</a></td><td class="bar" id="b28"><img src="../jacoco-resources/redbar.gif" width="7" height="10" title="18" alt="18"/></td><td class="ctr2" id="c30">0%</td><td class="bar" id="d35"/><td class="ctr2" id="e35">n/a</td><td class="ctr1" id="f35">1</td><td class="ctr2" id="g35">1</td><td class="ctr1" id="h29">5</td><td class="ctr2" id="i29">5</td><td class="ctr1" id="j28">1</td><td class="ctr2" id="k28">1</td></tr><tr><td id="a35"><a href="DiagnosticsGraphQLResolver.java.html#L939" class="el_method">testSharePointConnectionInternal()</a></td><td class="bar" id="b29"><img src="../jacoco-resources/redbar.gif" width="6" height="10" title="17" alt="17"/></td><td class="ctr2" id="c31">0%</td><td class="bar" id="d21"><img src="../jacoco-resources/redbar.gif" width="13" height="10" title="2" alt="2"/></td><td class="ctr2" id="e21">0%</td><td class="ctr1" id="f21">2</td><td class="ctr2" id="g21">2</td><td class="ctr1" id="h23">7</td><td class="ctr2" id="i23">7</td><td class="ctr1" id="j29">1</td><td class="ctr2" id="k29">1</td></tr><tr><td id="a18"><a href="DiagnosticsGraphQLResolver.java.html#L483" class="el_method">lambda$runDiagnostics$6(DiagnosticTest)</a></td><td class="bar" id="b30"><img src="../jacoco-resources/redbar.gif" width="3" height="10" title="8" alt="8"/></td><td class="ctr2" id="c32">0%</td><td class="bar" id="d22"><img src="../jacoco-resources/redbar.gif" width="13" height="10" title="2" alt="2"/></td><td class="ctr2" id="e22">0%</td><td class="ctr1" id="f22">2</td><td class="ctr2" id="g22">2</td><td class="ctr1" id="h30">1</td><td class="ctr2" id="i31">1</td><td class="ctr1" id="j30">1</td><td class="ctr2" id="k30">1</td></tr><tr><td id="a17"><a href="DiagnosticsGraphQLResolver.java.html#L482" class="el_method">lambda$runDiagnostics$5(DiagnosticTest)</a></td><td class="bar" id="b31"><img src="../jacoco-resources/redbar.gif" width="3" height="10" title="8" alt="8"/></td><td class="ctr2" id="c33">0%</td><td class="bar" id="d23"><img src="../jacoco-resources/redbar.gif" width="13" height="10" title="2" alt="2"/></td><td class="ctr2" id="e23">0%</td><td class="ctr1" id="f23">2</td><td class="ctr2" id="g23">2</td><td class="ctr1" id="h31">1</td><td class="ctr2" id="i32">1</td><td class="ctr1" id="j31">1</td><td class="ctr2" id="k31">1</td></tr><tr><td id="a15"><a href="DiagnosticsGraphQLResolver.java.html#L456" class="el_method">lambda$runDiagnostics$3(DiagnosticTest)</a></td><td class="bar" id="b32"><img src="../jacoco-resources/redbar.gif" width="3" height="10" title="8" alt="8"/></td><td class="ctr2" id="c34">0%</td><td class="bar" id="d24"><img src="../jacoco-resources/redbar.gif" width="13" height="10" title="2" alt="2"/></td><td class="ctr2" id="e24">0%</td><td class="ctr1" id="f24">2</td><td class="ctr2" id="g24">2</td><td class="ctr1" id="h32">1</td><td class="ctr2" id="i33">1</td><td class="ctr1" id="j32">1</td><td class="ctr2" id="k32">1</td></tr><tr><td id="a14"><a href="DiagnosticsGraphQLResolver.java.html#L414" class="el_method">lambda$getHealthSummary$2(ComponentHealth)</a></td><td class="bar" id="b33"><img src="../jacoco-resources/redbar.gif" width="3" height="10" title="8" alt="8"/></td><td class="ctr2" id="c35">0%</td><td class="bar" id="d25"><img src="../jacoco-resources/redbar.gif" width="13" height="10" title="2" alt="2"/></td><td class="ctr2" id="e25">0%</td><td class="ctr1" id="f25">2</td><td class="ctr2" id="g25">2</td><td class="ctr1" id="h33">1</td><td class="ctr2" id="i34">1</td><td class="ctr1" id="j33">1</td><td class="ctr2" id="k33">1</td></tr><tr><td id="a13"><a href="DiagnosticsGraphQLResolver.java.html#L413" class="el_method">lambda$getHealthSummary$1(ComponentHealth)</a></td><td class="bar" id="b34"><img src="../jacoco-resources/redbar.gif" width="3" height="10" title="8" alt="8"/></td><td class="ctr2" id="c36">0%</td><td class="bar" id="d26"><img src="../jacoco-resources/redbar.gif" width="13" height="10" title="2" alt="2"/></td><td class="ctr2" id="e26">0%</td><td class="ctr1" id="f26">2</td><td class="ctr2" id="g26">2</td><td class="ctr1" id="h34">1</td><td class="ctr2" id="i35">1</td><td class="ctr1" id="j34">1</td><td class="ctr2" id="k34">1</td></tr><tr><td id="a12"><a href="DiagnosticsGraphQLResolver.java.html#L412" class="el_method">lambda$getHealthSummary$0(ComponentHealth)</a></td><td class="bar" id="b35"><img src="../jacoco-resources/redbar.gif" width="3" height="10" title="8" alt="8"/></td><td class="ctr2" id="c37">0%</td><td class="bar" id="d27"><img src="../jacoco-resources/redbar.gif" width="13" height="10" title="2" alt="2"/></td><td class="ctr2" id="e27">0%</td><td class="ctr1" id="f27">2</td><td class="ctr2" id="g27">2</td><td class="ctr1" id="h35">1</td><td class="ctr2" id="i36">1</td><td class="ctr1" id="j35">1</td><td class="ctr2" id="k35">1</td></tr><tr><td id="a4"><a href="DiagnosticsGraphQLResolver.java.html#L44" class="el_method">DiagnosticsGraphQLResolver()</a></td><td class="bar" id="b36"><img src="../jacoco-resources/greenbar.gif" width="5" height="10" title="13" alt="13"/></td><td class="ctr2" id="c0">100%</td><td class="bar" id="d36"/><td class="ctr2" id="e36">n/a</td><td class="ctr1" id="f36">0</td><td class="ctr2" id="g36">1</td><td class="ctr1" id="h36">0</td><td class="ctr2" id="i30">3</td><td class="ctr1" id="j36">0</td><td class="ctr2" id="k36">1</td></tr><tr><td id="a28"><a href="DiagnosticsGraphQLResolver.java.html#L43" class="el_method">static {...}</a></td><td class="bar" id="b37"><img src="../jacoco-resources/greenbar.gif" width="1" height="10" title="4" alt="4"/></td><td class="ctr2" id="c1">100%</td><td class="bar" id="d37"/><td class="ctr2" id="e37">n/a</td><td class="ctr1" id="f37">0</td><td class="ctr2" id="g37">1</td><td class="ctr1" id="h37">0</td><td class="ctr2" id="i37">1</td><td class="ctr1" id="j37">0</td><td class="ctr2" id="k37">1</td></tr></tbody></table><div class="footer"><span class="right">Created with <a href="http://www.jacoco.org/jacoco">JaCoCo</a> 0.8.10.202304240956</span></div></body></html>