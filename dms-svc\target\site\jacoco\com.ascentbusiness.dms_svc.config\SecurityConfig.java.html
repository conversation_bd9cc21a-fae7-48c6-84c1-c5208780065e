<?xml version="1.0" encoding="UTF-8"?><!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Strict//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-strict.dtd"><html xmlns="http://www.w3.org/1999/xhtml" lang="en"><head><meta http-equiv="Content-Type" content="text/html;charset=UTF-8"/><link rel="stylesheet" href="../jacoco-resources/report.css" type="text/css"/><link rel="shortcut icon" href="../jacoco-resources/report.gif" type="image/gif"/><title>SecurityConfig.java</title><link rel="stylesheet" href="../jacoco-resources/prettify.css" type="text/css"/><script type="text/javascript" src="../jacoco-resources/prettify.js"></script></head><body onload="window['PR_TAB_WIDTH']=4;prettyPrint()"><div class="breadcrumb" id="breadcrumb"><span class="info"><a href="../jacoco-sessions.html" class="el_session">Sessions</a></span><a href="../index.html" class="el_report">dms-svc</a> &gt; <a href="index.source.html" class="el_package">com.ascentbusiness.dms_svc.config</a> &gt; <span class="el_source">SecurityConfig.java</span></div><h1>SecurityConfig.java</h1><pre class="source lang-java linenums">package com.ascentbusiness.dms_svc.config;

import com.ascentbusiness.dms_svc.filter.CorrelationIdFilter;
import com.ascentbusiness.dms_svc.security.JwtAuthenticationEntryPoint;
import com.ascentbusiness.dms_svc.security.JwtAuthenticationFilter;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.core.annotation.Order;
import org.springframework.security.authentication.AuthenticationManager;
import org.springframework.security.config.annotation.authentication.configuration.AuthenticationConfiguration;
import org.springframework.security.config.annotation.method.configuration.EnableMethodSecurity;
import org.springframework.security.config.annotation.web.builders.HttpSecurity;
import org.springframework.security.config.annotation.web.configuration.EnableWebSecurity;
import org.springframework.security.config.http.SessionCreationPolicy;
import org.springframework.security.crypto.bcrypt.BCryptPasswordEncoder;
import org.springframework.security.crypto.password.PasswordEncoder;
import org.springframework.security.web.SecurityFilterChain;
import org.springframework.security.web.authentication.UsernamePasswordAuthenticationFilter;

import org.springframework.web.cors.CorsConfiguration;
import org.springframework.web.cors.CorsConfigurationSource;
import org.springframework.web.cors.UrlBasedCorsConfigurationSource;
import org.springframework.beans.factory.annotation.Value;

import java.util.Arrays;
import java.util.List;

/**
 * Security configuration for the DMS application.
 * Configures JWT authentication, CORS, security headers, and access control.
 * Provides comprehensive security settings including HSTS, CSP, and frame options.
 *
 * <AUTHOR> Development Team
 * @version 1.0
 * @since 1.0
 */
@Configuration
@EnableWebSecurity
@EnableMethodSecurity(prePostEnabled = true)
<span class="fc" id="L41">public class SecurityConfig {</span>

    /** JWT authentication entry point for handling unauthorized access */
    @Autowired
    private JwtAuthenticationEntryPoint unauthorizedHandler;

    /** Security headers configuration */
    @Autowired
    private SecurityHeadersConfig securityHeadersConfig;

    /** Allowed origins for CORS configuration */
    //@Value(&quot;${spring.graphql.cors.allowed-origins:http://localhost:3000,http://localhost:8080,http://localhost:9093}&quot;)
    @Value(&quot;${CORS_ALLOWED_ORIGINS:${spring.graphql.cors.allowed-origins:http://localhost:3000,http://localhost:8080,http://localhost:9093,http://localhost:9091,http://localhost:4200,https://notify-service.autoresilience.com,https://dms-service.autoresilience.com}}&quot;)
    private String allowedOrigins;

    /**
     * Creates JWT authentication filter bean.
     * @return configured JWT authentication filter
     */
    @Bean
    public JwtAuthenticationFilter jwtAuthenticationFilter() {
<span class="fc" id="L62">        return new JwtAuthenticationFilter();</span>
    }

    /**
     * Creates correlation ID filter bean for request tracking.
     * @return configured correlation ID filter
     */
    @Bean
    public CorrelationIdFilter correlationIdFilter() {
<span class="fc" id="L71">        return new CorrelationIdFilter();</span>
    }

    /**
     * Creates password encoder bean using BCrypt.
     * @return BCrypt password encoder
     */
    @Bean
    public PasswordEncoder passwordEncoder() {
<span class="fc" id="L80">        return new BCryptPasswordEncoder();</span>
    }

    /**
     * Creates authentication manager bean.
     * @param authConfig authentication configuration
     * @return configured authentication manager
     * @throws Exception if authentication manager creation fails
     */
    @Bean
    public AuthenticationManager authenticationManager(AuthenticationConfiguration authConfig) throws Exception {
<span class="fc" id="L91">        return authConfig.getAuthenticationManager();</span>
    }

    /**
     * Configures the main security filter chain with comprehensive security settings.
     * Includes security headers, CORS, JWT authentication, and access control rules.
     *
     * @param http HTTP security configuration
     * @return configured security filter chain
     * @throws Exception if security configuration fails
     */
    @Bean
    @Order(2)
    public SecurityFilterChain filterChain(HttpSecurity http) throws Exception {
<span class="fc" id="L105">        http</span>
            // Comprehensive security headers configuration
<span class="fc" id="L107">            .headers(headers -&gt; headers</span>
                // Frame options
<span class="fc" id="L109">                .frameOptions(frameOptions -&gt; {</span>
<span class="fc" id="L110">                    String frameOption = securityHeadersConfig.getFrameOptions();</span>
<span class="pc bpc" id="L111" title="1 of 2 branches missed.">                    if (frameOption != null) {</span>
<span class="pc bpc" id="L112" title="2 of 3 branches missed.">                        switch (frameOption.toUpperCase()) {</span>
                            case &quot;DENY&quot;:
<span class="fc" id="L114">                                frameOptions.deny();</span>
<span class="fc" id="L115">                                break;</span>
                            case &quot;SAMEORIGIN&quot;:
<span class="nc" id="L117">                                frameOptions.sameOrigin();</span>
<span class="nc" id="L118">                                break;</span>
                            default:
<span class="nc" id="L120">                                frameOptions.deny();</span>
<span class="fc" id="L121">                        }</span>
                    } else {
                        // Default to DENY if frameOption is null
<span class="nc" id="L124">                        frameOptions.deny();</span>
                    }
<span class="fc" id="L126">                })</span>
                // Content type options
<span class="fc" id="L128">                .contentTypeOptions(contentTypeOptions -&gt; {})</span>
                // HSTS configuration
<span class="fc" id="L130">                .httpStrictTransportSecurity(hstsConfig -&gt; {</span>
<span class="pc bpc" id="L131" title="1 of 2 branches missed.">                    if (securityHeadersConfig.isHstsEnabled()) {</span>
<span class="nc" id="L132">                        hstsConfig</span>
<span class="nc" id="L133">                            .maxAgeInSeconds(securityHeadersConfig.getHstsMaxAge())</span>
<span class="nc" id="L134">                            .includeSubDomains(securityHeadersConfig.isHstsIncludeSubdomains())</span>
<span class="nc" id="L135">                            .preload(securityHeadersConfig.isHstsPreload());</span>
                    }
<span class="fc" id="L137">                })</span>
                // Content Security Policy
<span class="fc" id="L139">                .contentSecurityPolicy(csp -&gt; {</span>
<span class="pc bpc" id="L140" title="1 of 2 branches missed.">                    if (securityHeadersConfig.isCspEnabled()) {</span>
<span class="nc" id="L141">                        csp.policyDirectives(securityHeadersConfig.getCspPolicy());</span>
                    }
<span class="fc" id="L143">                })</span>
                // Additional security headers - only add if not null
<span class="pc bpc" id="L145" title="1 of 2 branches missed.">                .addHeaderWriter(securityHeadersConfig.additionalSecurityHeadersWriter() != null </span>
<span class="fc" id="L146">                    ? securityHeadersConfig.additionalSecurityHeadersWriter() </span>
<span class="nc" id="L147">                    : new org.springframework.security.web.header.writers.StaticHeadersWriter(</span>
                        &quot;X-XSS-Protection&quot;, &quot;1; mode=block&quot;,
                        &quot;X-Download-Options&quot;, &quot;noopen&quot;,
                        &quot;X-Permitted-Cross-Domain-Policies&quot;, &quot;none&quot;
                    ))
            )
<span class="fc" id="L153">            .cors(cors -&gt; cors.configurationSource(corsConfigurationSource()))</span>
<span class="fc" id="L154">            .csrf(csrf -&gt; csrf.disable())</span>
<span class="fc" id="L155">            .exceptionHandling(exceptionHandling -&gt;</span>
<span class="fc" id="L156">                exceptionHandling.authenticationEntryPoint(unauthorizedHandler))</span>
<span class="fc" id="L157">            .sessionManagement(sessionManagement -&gt;</span>
<span class="fc" id="L158">                sessionManagement.sessionCreationPolicy(SessionCreationPolicy.STATELESS))</span>
<span class="fc" id="L159">            .authorizeHttpRequests(authz -&gt; authz</span>
<span class="fc" id="L160">                .requestMatchers(&quot;/graphiql&quot;, &quot;/graphiql/**&quot;).permitAll()</span>
<span class="fc" id="L161">                .requestMatchers(&quot;/graphql&quot;).permitAll()</span>
<span class="fc" id="L162">                .requestMatchers(&quot;/actuator/**&quot;).permitAll()</span>
<span class="fc" id="L163">                .requestMatchers(&quot;/h2-console/**&quot;).permitAll()</span>
<span class="fc" id="L164">                .requestMatchers(&quot;/static/**&quot;).permitAll()</span>
<span class="fc" id="L165">                .requestMatchers(&quot;/css/**&quot;, &quot;/js/**&quot;, &quot;/images/**&quot;).permitAll()</span>
<span class="fc" id="L166">                .requestMatchers(&quot;/favicon.ico&quot;).permitAll()</span>
<span class="fc" id="L167">                .requestMatchers(&quot;/error&quot;).permitAll()</span>
<span class="fc" id="L168">                .requestMatchers(&quot;/test-download.html&quot;).permitAll()</span>
<span class="fc" id="L169">                .requestMatchers(&quot;/test-dms.html&quot;).permitAll()</span>
                // Authentication endpoints - must be accessible without JWT
<span class="fc" id="L171">                .requestMatchers(&quot;/auth/login&quot;, &quot;/auth/register&quot;).permitAll()</span>
                // Document download endpoints require authentication
<span class="fc" id="L173">                .requestMatchers(&quot;/api/v1/documents/*/download&quot;).authenticated()</span>
<span class="fc" id="L174">                .requestMatchers(&quot;/api/v2/documents/*/download&quot;).authenticated()</span>
<span class="fc" id="L175">                .requestMatchers(&quot;/api/v2/documents/*/download/info&quot;).authenticated()</span>
<span class="fc" id="L176">                .anyRequest().authenticated()</span>
            );

        // Add correlation ID filter first in the chain
<span class="fc" id="L180">        http.addFilterBefore(correlationIdFilter(), UsernamePasswordAuthenticationFilter.class);</span>
<span class="fc" id="L181">        http.addFilterBefore(jwtAuthenticationFilter(), UsernamePasswordAuthenticationFilter.class);</span>

<span class="fc" id="L183">        return http.build();</span>
    }

    /**
     * Configures CORS (Cross-Origin Resource Sharing) settings.
     * Defines allowed origins, methods, headers, and credentials policy.
     * Uses allowedOriginPatterns when allowCredentials is true to avoid wildcard conflicts.
     *
     * @return configured CORS configuration source
     */
    @Bean
    public CorsConfigurationSource corsConfigurationSource() {
<span class="fc" id="L195">        CorsConfiguration configuration = new CorsConfiguration();</span>

        // Parse allowed origins from configuration - more secure than wildcard
<span class="fc" id="L198">        List&lt;String&gt; origins = Arrays.asList(allowedOrigins.split(&quot;,&quot;));</span>
        
        // Use allowedOriginPatterns instead of allowedOrigins when allowCredentials is true
        // This prevents the &quot;allowCredentials cannot be used with wildcard origins&quot; error
<span class="pc bpc" id="L202" title="1 of 2 branches missed.">        if (origins.contains(&quot;*&quot;)) {</span>
<span class="nc" id="L203">            configuration.setAllowedOriginPatterns(origins);</span>
<span class="nc" id="L204">            System.out.println(&quot;DMS CORS: Using allowedOriginPatterns with wildcard: &quot; + origins);</span>
        } else {
<span class="fc" id="L206">            configuration.setAllowedOrigins(origins);</span>
<span class="fc" id="L207">            System.out.println(&quot;DMS CORS: Using allowedOrigins with specific domains: &quot; + origins);</span>
        }

        // Allowed methods
<span class="fc" id="L211">        configuration.setAllowedMethods(Arrays.asList(&quot;GET&quot;, &quot;POST&quot;, &quot;PUT&quot;, &quot;PATCH&quot;, &quot;DELETE&quot;, &quot;OPTIONS&quot;));</span>

        // Specific allowed headers instead of wildcard
<span class="fc" id="L214">        configuration.setAllowedHeaders(Arrays.asList(</span>
            &quot;Content-Type&quot;,
            &quot;Authorization&quot;,
            &quot;X-Correlation-ID&quot;,
            &quot;X-Requested-With&quot;
        ));

        // Allow credentials for authenticated requests
<span class="fc" id="L222">        configuration.setAllowCredentials(true);</span>
<span class="fc" id="L223">        System.out.println(&quot;DMS CORS: allowCredentials set to true&quot;);</span>

        // Cache preflight requests for 1 hour
<span class="fc" id="L226">        configuration.setMaxAge(3600L);</span>

<span class="fc" id="L228">        UrlBasedCorsConfigurationSource source = new UrlBasedCorsConfigurationSource();</span>
<span class="fc" id="L229">        source.registerCorsConfiguration(&quot;/**&quot;, configuration);</span>
<span class="fc" id="L230">        return source;</span>
    }
}
</pre><div class="footer"><span class="right">Created with <a href="http://www.jacoco.org/jacoco">JaCoCo</a> 0.8.10.202304240956</span></div></body></html>