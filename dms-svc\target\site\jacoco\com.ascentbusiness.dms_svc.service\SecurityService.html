<?xml version="1.0" encoding="UTF-8"?><!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Strict//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-strict.dtd"><html xmlns="http://www.w3.org/1999/xhtml" lang="en"><head><meta http-equiv="Content-Type" content="text/html;charset=UTF-8"/><link rel="stylesheet" href="../jacoco-resources/report.css" type="text/css"/><link rel="shortcut icon" href="../jacoco-resources/report.gif" type="image/gif"/><title>SecurityService</title><script type="text/javascript" src="../jacoco-resources/sort.js"></script></head><body onload="initialSort(['breadcrumb'])"><div class="breadcrumb" id="breadcrumb"><span class="info"><a href="../jacoco-sessions.html" class="el_session">Sessions</a></span><a href="../index.html" class="el_report">dms-svc</a> &gt; <a href="index.html" class="el_package">com.ascentbusiness.dms_svc.service</a> &gt; <span class="el_class">SecurityService</span></div><h1>SecurityService</h1><table class="coverage" cellspacing="0" id="coveragetable"><thead><tr><td class="sortable" id="a" onclick="toggleSort(this)">Element</td><td class="down sortable bar" id="b" onclick="toggleSort(this)">Missed Instructions</td><td class="sortable ctr2" id="c" onclick="toggleSort(this)">Cov.</td><td class="sortable bar" id="d" onclick="toggleSort(this)">Missed Branches</td><td class="sortable ctr2" id="e" onclick="toggleSort(this)">Cov.</td><td class="sortable ctr1" id="f" onclick="toggleSort(this)">Missed</td><td class="sortable ctr2" id="g" onclick="toggleSort(this)">Cxty</td><td class="sortable ctr1" id="h" onclick="toggleSort(this)">Missed</td><td class="sortable ctr2" id="i" onclick="toggleSort(this)">Lines</td><td class="sortable ctr1" id="j" onclick="toggleSort(this)">Missed</td><td class="sortable ctr2" id="k" onclick="toggleSort(this)">Methods</td></tr></thead><tfoot><tr><td>Total</td><td class="bar">416 of 423</td><td class="ctr2">1%</td><td class="bar">42 of 42</td><td class="ctr2">0%</td><td class="ctr1">34</td><td class="ctr2">36</td><td class="ctr1">95</td><td class="ctr2">97</td><td class="ctr1">13</td><td class="ctr2">15</td></tr></tfoot><tbody><tr><td id="a9"><a href="SecurityService.java.html#L154" class="el_method">logSecurityViolation(String, SecurityViolationType, ViolationSeverity, Long, String, String)</a></td><td class="bar" id="b0"><img src="../jacoco-resources/redbar.gif" width="120" height="10" title="72" alt="72"/></td><td class="ctr2" id="c2">0%</td><td class="bar" id="d1"><img src="../jacoco-resources/redbar.gif" width="60" height="10" title="6" alt="6"/></td><td class="ctr2" id="e0">0%</td><td class="ctr1" id="f1">4</td><td class="ctr2" id="g1">4</td><td class="ctr1" id="h0">19</td><td class="ctr2" id="i0">19</td><td class="ctr1" id="j0">1</td><td class="ctr2" id="k0">1</td></tr><tr><td id="a13"><a href="SecurityService.java.html#L216" class="el_method">updateSecurityConfig(String, String, String)</a></td><td class="bar" id="b1"><img src="../jacoco-resources/redbar.gif" width="91" height="10" title="55" alt="55"/></td><td class="ctr2" id="c3">0%</td><td class="bar" id="d4"><img src="../jacoco-resources/redbar.gif" width="20" height="10" title="2" alt="2"/></td><td class="ctr2" id="e1">0%</td><td class="ctr1" id="f4">2</td><td class="ctr2" id="g4">2</td><td class="ctr1" id="h1">15</td><td class="ctr2" id="i1">15</td><td class="ctr1" id="j1">1</td><td class="ctr2" id="k1">1</td></tr><tr><td id="a3"><a href="SecurityService.java.html#L103" class="el_method">getIntConfigValue(String, int)</a></td><td class="bar" id="b2"><img src="../jacoco-resources/redbar.gif" width="73" height="10" title="44" alt="44"/></td><td class="ctr2" id="c4">0%</td><td class="bar" id="d2"><img src="../jacoco-resources/redbar.gif" width="40" height="10" title="4" alt="4"/></td><td class="ctr2" id="e2">0%</td><td class="ctr1" id="f2">3</td><td class="ctr2" id="g2">3</td><td class="ctr1" id="h3">8</td><td class="ctr2" id="i3">8</td><td class="ctr1" id="j2">1</td><td class="ctr2" id="k2">1</td></tr><tr><td id="a4"><a href="SecurityService.java.html#L120" class="el_method">getLongConfigValue(String, long)</a></td><td class="bar" id="b3"><img src="../jacoco-resources/redbar.gif" width="73" height="10" title="44" alt="44"/></td><td class="ctr2" id="c5">0%</td><td class="bar" id="d3"><img src="../jacoco-resources/redbar.gif" width="40" height="10" title="4" alt="4"/></td><td class="ctr2" id="e3">0%</td><td class="ctr1" id="f3">3</td><td class="ctr2" id="g3">3</td><td class="ctr1" id="h4">8</td><td class="ctr2" id="i4">8</td><td class="ctr1" id="j3">1</td><td class="ctr2" id="k3">1</td></tr><tr><td id="a0"><a href="SecurityService.java.html#L87" class="el_method">getBooleanConfigValue(String, boolean)</a></td><td class="bar" id="b4"><img src="../jacoco-resources/redbar.gif" width="63" height="10" title="38" alt="38"/></td><td class="ctr2" id="c6">0%</td><td class="bar" id="d5"><img src="../jacoco-resources/redbar.gif" width="20" height="10" title="2" alt="2"/></td><td class="ctr2" id="e4">0%</td><td class="ctr1" id="f5">2</td><td class="ctr2" id="g5">2</td><td class="ctr1" id="h5">7</td><td class="ctr2" id="i5">7</td><td class="ctr1" id="j4">1</td><td class="ctr2" id="k4">1</td></tr><tr><td id="a1"><a href="SecurityService.java.html#L255" class="el_method">getClientIpAddress(HttpServletRequest)</a></td><td class="bar" id="b5"><img src="../jacoco-resources/redbar.gif" width="63" height="10" title="38" alt="38"/></td><td class="ctr2" id="c7">0%</td><td class="bar" id="d0"><img src="../jacoco-resources/redbar.gif" width="120" height="10" title="12" alt="12"/></td><td class="ctr2" id="e5">0%</td><td class="ctr1" id="f0">7</td><td class="ctr2" id="g0">7</td><td class="ctr1" id="h6">7</td><td class="ctr2" id="i6">7</td><td class="ctr1" id="j5">1</td><td class="ctr2" id="k5">1</td></tr><tr><td id="a5"><a href="SecurityService.java.html#L137" class="el_method">getStringConfigValue(String, String)</a></td><td class="bar" id="b6"><img src="../jacoco-resources/redbar.gif" width="60" height="10" title="36" alt="36"/></td><td class="ctr2" id="c8">0%</td><td class="bar" id="d6"><img src="../jacoco-resources/redbar.gif" width="20" height="10" title="2" alt="2"/></td><td class="ctr2" id="e6">0%</td><td class="ctr1" id="f6">2</td><td class="ctr2" id="g6">2</td><td class="ctr1" id="h7">7</td><td class="ctr2" id="i7">7</td><td class="ctr1" id="j6">1</td><td class="ctr2" id="k6">1</td></tr><tr><td id="a10"><a href="SecurityService.java.html#L199" class="el_method">resolveViolation(Long, String)</a></td><td class="bar" id="b7"><img src="../jacoco-resources/redbar.gif" width="55" height="10" title="33" alt="33"/></td><td class="ctr2" id="c9">0%</td><td class="bar" id="d7"><img src="../jacoco-resources/redbar.gif" width="20" height="10" title="2" alt="2"/></td><td class="ctr2" id="e7">0%</td><td class="ctr1" id="f7">2</td><td class="ctr2" id="g7">2</td><td class="ctr1" id="h2">9</td><td class="ctr2" id="i2">9</td><td class="ctr1" id="j7">1</td><td class="ctr2" id="k7">1</td></tr><tr><td id="a6"><a href="SecurityService.java.html#L189" class="el_method">hasExceededViolationThreshold(String)</a></td><td class="bar" id="b8"><img src="../jacoco-resources/redbar.gif" width="28" height="10" title="17" alt="17"/></td><td class="ctr2" id="c10">0%</td><td class="bar" id="d8"><img src="../jacoco-resources/redbar.gif" width="20" height="10" title="2" alt="2"/></td><td class="ctr2" id="e8">0%</td><td class="ctr1" id="f8">2</td><td class="ctr2" id="g8">2</td><td class="ctr1" id="h10">3</td><td class="ctr2" id="i10">3</td><td class="ctr1" id="j8">1</td><td class="ctr2" id="k8">1</td></tr><tr><td id="a2"><a href="SecurityService.java.html#L244" class="el_method">getCurrentRequest()</a></td><td class="bar" id="b9"><img src="../jacoco-resources/redbar.gif" width="21" height="10" title="13" alt="13"/></td><td class="ctr2" id="c11">0%</td><td class="bar" id="d9"><img src="../jacoco-resources/redbar.gif" width="20" height="10" title="2" alt="2"/></td><td class="ctr2" id="e9">0%</td><td class="ctr1" id="f9">2</td><td class="ctr2" id="g9">2</td><td class="ctr1" id="h8">4</td><td class="ctr2" id="i8">4</td><td class="ctr1" id="j9">1</td><td class="ctr2" id="k9">1</td></tr><tr><td id="a8"><a href="SecurityService.java.html#L287" class="el_method">isWithinRateLimit(String, String)</a></td><td class="bar" id="b10"><img src="../jacoco-resources/redbar.gif" width="21" height="10" title="13" alt="13"/></td><td class="ctr2" id="c12">0%</td><td class="bar" id="d10"><img src="../jacoco-resources/redbar.gif" width="20" height="10" title="2" alt="2"/></td><td class="ctr2" id="e10">0%</td><td class="ctr1" id="f10">2</td><td class="ctr2" id="g10">2</td><td class="ctr1" id="h9">4</td><td class="ctr2" id="i9">4</td><td class="ctr1" id="j10">1</td><td class="ctr2" id="k10">1</td></tr><tr><td id="a14"><a href="SecurityService.java.html#L272" class="el_method">validateTokenStrict(String)</a></td><td class="bar" id="b11"><img src="../jacoco-resources/redbar.gif" width="13" height="10" title="8" alt="8"/></td><td class="ctr2" id="c13">0%</td><td class="bar" id="d11"><img src="../jacoco-resources/redbar.gif" width="20" height="10" title="2" alt="2"/></td><td class="ctr2" id="e11">0%</td><td class="ctr1" id="f11">2</td><td class="ctr2" id="g11">2</td><td class="ctr1" id="h11">3</td><td class="ctr2" id="i11">3</td><td class="ctr1" id="j11">1</td><td class="ctr2" id="k11">1</td></tr><tr><td id="a7"><a href="SecurityService.java.html#L78" class="el_method">isFeatureEnabled(String)</a></td><td class="bar" id="b12"><img src="../jacoco-resources/redbar.gif" width="8" height="10" title="5" alt="5"/></td><td class="ctr2" id="c14">0%</td><td class="bar" id="d12"/><td class="ctr2" id="e12">n/a</td><td class="ctr1" id="f12">1</td><td class="ctr2" id="g12">1</td><td class="ctr1" id="h12">1</td><td class="ctr2" id="i12">1</td><td class="ctr1" id="j12">1</td><td class="ctr2" id="k12">1</td></tr><tr><td id="a12"><a href="SecurityService.java.html#L63" class="el_method">static {...}</a></td><td class="bar" id="b13"><img src="../jacoco-resources/greenbar.gif" width="6" height="10" title="4" alt="4"/></td><td class="ctr2" id="c0">100%</td><td class="bar" id="d13"/><td class="ctr2" id="e13">n/a</td><td class="ctr1" id="f13">0</td><td class="ctr2" id="g13">1</td><td class="ctr1" id="h13">0</td><td class="ctr2" id="i13">1</td><td class="ctr1" id="j13">0</td><td class="ctr2" id="k13">1</td></tr><tr><td id="a11"><a href="SecurityService.java.html#L61" class="el_method">SecurityService()</a></td><td class="bar" id="b14"><img src="../jacoco-resources/greenbar.gif" width="5" height="10" title="3" alt="3"/></td><td class="ctr2" id="c1">100%</td><td class="bar" id="d14"/><td class="ctr2" id="e14">n/a</td><td class="ctr1" id="f14">0</td><td class="ctr2" id="g14">1</td><td class="ctr1" id="h14">0</td><td class="ctr2" id="i14">1</td><td class="ctr1" id="j14">0</td><td class="ctr2" id="k14">1</td></tr></tbody></table><div class="footer"><span class="right">Created with <a href="http://www.jacoco.org/jacoco">JaCoCo</a> 0.8.10.202304240956</span></div></body></html>