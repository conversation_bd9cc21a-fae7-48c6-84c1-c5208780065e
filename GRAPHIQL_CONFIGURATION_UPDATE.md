# GraphiQL Configuration Update

## Issue Resolved
Fixed GraphiQL interface accessibility for both DMS and Notification services across DEFAULT and UAT profiles.

## Changes Made

### 1. DMS Service
- **File**: [`dms-svc/src/main/resources/application.properties`](dms-svc/src/main/resources/application.properties)
- **Change**: `spring.graphql.graphiql.enabled=${GRAPHIQL_ENABLED:false}` → `spring.graphql.graphiql.enabled=${GRAPHIQL_ENABLED:true}`
- **Created**: [`dms-svc/src/main/resources/application-uat.properties`](dms-svc/src/main/resources/application-uat.properties) with GraphiQL enabled

### 2. Notification Service
- **File**: [`notification-svc/src/main/resources/application.properties`](notification-svc/src/main/resources/application.properties)
- **Change**: `spring.graphql.graphiql.enabled=${GRAPHIQL_ENABLED:false}` → `spring.graphql.graphiql.enabled=${GRAPHIQL_ENABLED:true}`
- **Existing**: [`notification-svc/src/main/resources/application-uat.properties`](notification-svc/src/main/resources/application-uat.properties) already has GraphiQL enabled

## GraphiQL Availability by Profile

### DMS Service (Port 9093)
| Profile | GraphiQL Enabled | URL |
|---------|------------------|-----|
| **Default** (no profile) | ✅ **YES** | `http://localhost:9093/graphiql?path=/graphql` |
| **dev** | ✅ **YES** | `http://localhost:9093/graphiql?path=/graphql` |
| **uat** | ✅ **YES** | `http://localhost:9093/graphiql?path=/graphql` |
| **prod** | ❌ **NO** | Not accessible (disabled for security) |

### Notification Service (Port 9091)
| Profile | GraphiQL Enabled | URL |
|---------|------------------|-----|
| **Default** (no profile) | ✅ **YES** | `http://localhost:9091/graphiql?path=/graphql` |
| **dev** | ✅ **YES** | `http://localhost:9091/graphiql?path=/graphql` |
| **uat** | ✅ **YES** | `http://localhost:9091/graphiql?path=/graphql` |
| **prod** | ❌ **NO** | Not accessible (disabled for security) |

## Testing Instructions

### Test DMS Service GraphiQL

#### 1. Default Profile (No Profile Specified)
```bash
cd dms-svc
./mvnw spring-boot:run
# Access: http://localhost:9093/graphiql?path=/graphql
```

#### 2. UAT Profile
```bash
cd dms-svc
./mvnw spring-boot:run -Dspring-boot.run.profiles=uat
# Access: http://localhost:9093/graphiql?path=/graphql
```

#### 3. Dev Profile
```bash
cd dms-svc
./mvnw spring-boot:run -Dspring-boot.run.profiles=dev
# Access: http://localhost:9093/graphiql?path=/graphql
```

### Test Notification Service GraphiQL

#### 1. Default Profile (No Profile Specified)
```bash
cd notification-svc
$env:JWT_SECRET="test_jwt_secret_at_least_32_characters_long"
./mvnw spring-boot:run
# Access: http://localhost:9091/graphiql?path=/graphql
```

#### 2. UAT Profile
```bash
cd notification-svc
$env:JWT_SECRET="test_jwt_secret_at_least_32_characters_long"
./mvnw spring-boot:run -Dspring-boot.run.profiles=uat
# Access: http://localhost:9091/graphiql?path=/graphql
```

#### 3. Dev Profile
```bash
cd notification-svc
$env:JWT_SECRET="test_jwt_secret_at_least_32_characters_long"
./mvnw spring-boot:run -Dspring-boot.run.profiles=dev
# Access: http://localhost:9091/graphiql?path=/graphql
```

## Expected Behavior

### ✅ Working GraphiQL Interface Should Show:
- GraphiQL IDE interface
- Schema documentation on the right
- Query editor in the center
- Ability to execute GraphQL queries
- No "Whitelabel Error Page"

### ❌ If You Still See 404 Error:
1. **Check service is running**: Look for "Started [ServiceName] in X seconds" in logs
2. **Verify port**: DMS=9093, Notification=9091
3. **Check URL**: Must include `?path=/graphql` parameter
4. **Wait for startup**: Services need time to fully initialize
5. **Check logs**: Look for any startup errors

## Security Notes

### Production Profile Behavior:
- **GraphiQL is DISABLED** in production profiles for both services
- This is intentional for security reasons
- Production deployments should not expose GraphiQL interface
- Use appropriate API clients or tools for production GraphQL access

### Environment Variable Override:
You can still override GraphiQL setting via environment variable:
```bash
# Force disable GraphiQL
$env:GRAPHIQL_ENABLED="false"
./mvnw spring-boot:run

# Force enable GraphiQL (not recommended for production)
$env:GRAPHIQL_ENABLED="true"
./mvnw spring-boot:run -Dspring-boot.run.profiles=prod
```

## Troubleshooting

### Common Issues:

1. **404 Error**: Service not fully started or wrong URL
2. **Connection Refused**: Service not running or wrong port
3. **Blank Page**: JavaScript errors, check browser console
4. **Authentication Errors**: Some queries may require JWT tokens

### Quick Verification:
```bash
# Check if service is responding
curl http://localhost:9093/actuator/health  # DMS
curl http://localhost:9091/actuator/health  # Notification

# Check GraphQL endpoint
curl -X POST http://localhost:9093/graphql -H "Content-Type: application/json" -d '{"query":"query { __schema { types { name } } }"}'
```

This update ensures GraphiQL is accessible for development and testing while maintaining security in production environments.