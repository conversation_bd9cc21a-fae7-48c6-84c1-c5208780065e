# Notification Service - Exhaustive GraphQL API Examples

This document provides comprehensive GraphQL examples for all notification operations in the Notification service.

## Table of Contents

1. [Authentication](#authentication)
2. [Notification Operations](#notification-operations)
3. [Template Management](#template-management)
4. [In-App Notification Operations](#in-app-notification-operations)
5. [Audit Log Operations](#audit-log-operations)
6. [Advanced Features](#advanced-features)
7. [Error Handling](#error-handling)
8. [Best Practices](#best-practices)

## Authentication

All GraphQL operations require JWT authentication. Include the token in the Authorization header:

```
Authorization: Bearer <your-jwt-token>
```

### Generate Test Token

```graphql
mutation GenerateTestToken {
  generateTestToken(input: {
    username: "test-user"
    roles: ["USER", "ADMIN"]
    permissions: ["SEND_NOTIFICATION", "MANAGE_TEMPLATES", "VIEW_AUDIT_LOGS"]
  }) {
    token
    tokenType
    expiresAt
  }
}
```

## Notification Operations

### 1. Basic Email Notification

```graphql
mutation SendBasicEmail {
  sendNotification(input: {
    sender: "<EMAIL>"
    type: EMAIL
    content: "This is a basic email notification"
    templateName: "default-notification"
    messageType: INFORMATIONAL
    importance: MEDIUM
    recipients: ["<EMAIL>", "<EMAIL>"]
  })
}
```

### 2. Enhanced Email with Detailed Recipients

```graphql
mutation SendEnhancedEmail {
  sendNotification(input: {
    sender: "<EMAIL>"
    type: EMAIL
    content: "Welcome to the company!"
    templateName: "welcome-email"
    messageType: INFORMATIONAL
    importance: HIGH
    recipientDetails: [
      {
        email: "<EMAIL>"
        type: TO
        variables: [
          { key: "employeeName", value: "John Doe" }
          { key: "startDate", value: "2025-01-15" }
          { key: "department", value: "Engineering" }
        ]
        attachments: [
          {
            filename: "employee-handbook.pdf"
            contentType: "application/pdf"
            content: "JVBERi0xLjQKJcOkw7zDtsO..." # Base64 encoded content
            description: "Employee Handbook"
          }
        ]
      }
      {
        email: "<EMAIL>"
        type: CC
        variables: [
          { key: "employeeName", value: "John Doe" }
          { key: "managerName", value: "Jane Smith" }
        ]
      }
      {
        email: "<EMAIL>"
        type: BCC
      }
    ]
    variables: [
      { key: "companyName", value: "Ascent Business Solutions" }
      { key: "year", value: "2025" }
    ]
    commonAttachments: [
      {
        filename: "company-logo.png"
        contentType: "image/png"
        content: "iVBORw0KGgoAAAANSUhEUgAA..." # Base64 encoded content
        description: "Company Logo"
      }
    ]
  })
}
```

### 3. Email with File Path Attachments

```graphql
mutation SendEmailWithFilePathAttachments {
  sendNotification(input: {
    sender: "<EMAIL>"
    type: EMAIL
    content: "Monthly financial report attached"
    templateName: "financial-report"
    messageType: ACTIONABLE
    importance: HIGH
    recipientDetails: [
      {
        email: "<EMAIL>"
        type: TO
        variables: [
          { key: "reportMonth", value: "December 2024" }
          { key: "recipientTitle", value: "CEO" }
        ]
        attachments: [
          {
            filename: "monthly-report-dec-2024.pdf"
            contentType: "application/pdf"
            content: "" # Empty when using file path
            description: "Monthly Financial Report"
            filePath: "/reports/financial/monthly-report-dec-2024.pdf"
          }
        ]
      }
      {
        email: "<EMAIL>"
        type: CC
        variables: [
          { key: "reportMonth", value: "December 2024" }
          { key: "recipientTitle", value: "CFO" }
        ]
      }
    ]
    commonAttachments: [
      {
        filename: "budget-summary.xlsx"
        contentType: "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet"
        content: ""
        description: "Budget Summary"
        filePath: "/reports/financial/budget-summary.xlsx"
      }
    ]
  })
}
```

### 4. Basic In-App Notification

```graphql
mutation SendInAppNotification {
  sendNotification(input: {
    sender: "<EMAIL>"
    type: IN_APP
    content: "You have a new task assigned"
    messageType: ACTIONABLE
    importance: HIGH
    recipients: ["<EMAIL>"]
    variables: [
      { key: "taskTitle", value: "Review Q4 Performance" }
      { key: "dueDate", value: "2025-01-20" }
      { key: "assignedBy", value: "Manager" }
    ]
  })
}
```

### 5. Enhanced In-App Notification with Variables

```graphql
mutation SendEnhancedInAppNotification {
  sendNotification(input: {
    sender: "<EMAIL>"
    type: IN_APP
    content: "Project milestone completed"
    messageType: INFORMATIONAL
    importance: MEDIUM
    recipientDetails: [
      {
        email: "<EMAIL>"
        variables: [
          { key: "userName", value: "Alice Johnson" }
          { key: "projectName", value: "Mobile App Redesign" }
          { key: "milestone", value: "UI Design Phase" }
          { key: "completionDate", value: "2025-01-10" }
        ]
      }
      {
        email: "<EMAIL>"
        variables: [
          { key: "userName", value: "Bob Wilson" }
          { key: "projectName", value: "Mobile App Redesign" }
          { key: "milestone", value: "UI Design Phase" }
          { key: "completionDate", value: "2025-01-10" }
        ]
      }
    ]
    variables: [
      { key: "teamName", value: "Frontend Development Team" }
      { key: "nextMilestone", value: "Backend Integration" }
    ]
  })
}
```

### 6. Broadcast Notification

```graphql
mutation SendBroadcastNotification {
  sendNotification(input: {
    sender: "<EMAIL>"
    type: IN_APP
    content: "System maintenance scheduled for tonight"
    messageType: ALERT
    importance: HIGH
    recipients: ["ALL"] # Special keyword for broadcast
    variables: [
      { key: "maintenanceTime", value: "11:00 PM - 2:00 AM EST" }
      { key: "expectedDowntime", value: "3 hours" }
      { key: "affectedServices", value: "All web applications" }
    ]
  })
}
```

### 7. Promotional Email Campaign

```graphql
mutation SendPromotionalEmail {
  sendNotification(input: {
    sender: "<EMAIL>"
    type: EMAIL
    content: "Special offer just for you!"
    templateName: "promotional-email"
    messageType: PROMOTIONAL
    importance: LOW
    recipientDetails: [
      {
        email: "<EMAIL>"
        type: TO
        variables: [
          { key: "customerName", value: "Sarah Davis" }
          { key: "discountCode", value: "SAVE20" }
          { key: "expiryDate", value: "2025-02-15" }
          { key: "personalizedOffer", value: "20% off premium plans" }
        ]
      }
      {
        email: "<EMAIL>"
        type: TO
        variables: [
          { key: "customerName", value: "Mike Chen" }
          { key: "discountCode", value: "SAVE15" }
          { key: "expiryDate", value: "2025-02-15" }
          { key: "personalizedOffer", value: "15% off all services" }
        ]
      }
    ]
    variables: [
      { key: "campaignName", value: "New Year Special 2025" }
      { key: "supportEmail", value: "<EMAIL>" }
    ]
  })
}
```

### 8. Reminder Notification

```graphql
mutation SendReminderNotification {
  sendNotification(input: {
    sender: "<EMAIL>"
    type: EMAIL
    content: "Meeting reminder"
    templateName: "meeting-reminder"
    messageType: REMINDER
    importance: MEDIUM
    recipientDetails: [
      {
        email: "<EMAIL>"
        type: TO
        variables: [
          { key: "attendeeName", value: "John Smith" }
          { key: "meetingTitle", value: "Quarterly Business Review" }
          { key: "meetingTime", value: "2025-01-15 10:00 AM EST" }
          { key: "meetingLocation", value: "Conference Room A" }
          { key: "meetingLink", value: "https://meet.company.com/qbr-2025" }
        ]
      }
      {
        email: "<EMAIL>"
        type: TO
        variables: [
          { key: "attendeeName", value: "Lisa Johnson" }
          { key: "meetingTitle", value: "Quarterly Business Review" }
          { key: "meetingTime", value: "2025-01-15 10:00 AM EST" }
          { key: "meetingLocation", value: "Conference Room A" }
          { key: "meetingLink", value: "https://meet.company.com/qbr-2025" }
        ]
      }
    ]
    variables: [
      { key: "organizerName", value: "Project Manager" }
      { key: "organizerEmail", value: "<EMAIL>" }
    ]
    commonAttachments: [
      {
        filename: "meeting-agenda.pdf"
        contentType: "application/pdf"
        filePath: "/meetings/qbr-2025/agenda.pdf"
        description: "Meeting Agenda"
      }
    ]
  })
}
```

## Template Management

### 1. Create/Update Notification Template

```graphql
mutation CreateNotificationTemplate {
  addUpdateNotificationTemplate(input: {
    alias: "welcome-email"
    subject: "Welcome to ${companyName}!"
    body: """
    <html>
    <body>
      <h1>Welcome ${employeeName}!</h1>
      <p>We're excited to have you join our ${department} team.</p>
      <p>Your start date is ${startDate}.</p>
      <p>Please find the employee handbook attached.</p>
      <br>
      <p>Best regards,<br>
      ${sender.name}<br>
      Human Resources</p>
    </body>
    </html>
    """
  }) {
    id
    alias
    subject
    body
  }
}
```

### 2. Create Template with Advanced Features

```graphql
mutation CreateAdvancedTemplate {
  addUpdateNotificationTemplate(input: {
    alias: "project-status-update"
    subject: "Project Update: ${projectName} - ${status}"
    body: """
    <html>
    <head>
      <style>
        .header { background-color: #f0f0f0; padding: 20px; }
        .content { padding: 20px; }
        .status-${status} { color: ${statusColor}; font-weight: bold; }
        .progress-bar { 
          width: 100%; 
          background-color: #ddd; 
          border-radius: 5px; 
        }
        .progress { 
          height: 20px; 
          background-color: #4CAF50; 
          border-radius: 5px; 
          width: ${progressPercentage}%; 
        }
      </style>
    </head>
    <body>
      <div class="header">
        <h2>Project Status Update</h2>
      </div>
      <div class="content">
        <h3>${projectName}</h3>
        <p class="status-${status}">Status: ${status}</p>
        
        <h4>Progress</h4>
        <div class="progress-bar">
          <div class="progress"></div>
        </div>
        <p>${progressPercentage}% Complete</p>
        
        <h4>Key Updates</h4>
        <ul>
          <#list updates as update>
            <li>${update}</li>
          </#list>
        </ul>
        
        <h4>Next Steps</h4>
        <p>${nextSteps}</p>
        
        <h4>Team Members</h4>
        <ul>
          <#list teamMembers as member>
            <li>${member.name} - ${member.role}</li>
          </#list>
        </ul>
        
        <p>For questions, contact: ${projectManager.email}</p>
      </div>
    </body>
    </html>
    """
  }) {
    id
    alias
    subject
    body
  }
}
```

### 3. Get Template by Alias

```graphql
query GetTemplate {
  getTemplate(templateName: "welcome-email") {
    id
    alias
    subject
    body
  }
}
```

### 4. Get All Templates

```graphql
query GetAllTemplates {
  getAllTemplates {
    id
    alias
    subject
    body
  }
}
```

## In-App Notification Operations

### 1. Get Notifications for User

```graphql
query GetUserNotifications {
  getNotificationsForUser(userId: "<EMAIL>") {
    id
    recipientId
    isRead
    delivered
    notification {
      id
      senderId
      content
      templateName
      type
      messageType
      importance
      createdDate
      lastModifiedDate
    }
  }
}
```

### 2. Get Unread In-App Notifications

```graphql
query GetUnreadInAppNotifications {
  getUnreadInAppNotifications(userId: "<EMAIL>") {
    notificationId
    senderId
    recipientId
    content
    type
    timestamp
    variables
    isRead
    delivered
  }
}
```

### 3. Poll In-App Notifications

```graphql
query PollInAppNotifications {
  pollInAppNotifications(userId: "<EMAIL>", limit: 10) {
    notificationId
    senderId
    recipientId
    content
    type
    timestamp
    variables
    isRead
    delivered
  }
}
```

### 4. Consume In-App Notifications

```graphql
mutation ConsumeInAppNotifications {
  consumeInAppNotifications(userId: "<EMAIL>") {
    notificationId
    senderId
    recipientId
    content
    type
    timestamp
    variables
    isRead
    delivered
  }
}
```

### 5. Mark All Notifications as Read

```graphql
mutation MarkAllNotificationsRead {
  markNotificationAsRead(userId: "<EMAIL>")
}
```

### 6. Mark Specific Notification as Read

```graphql
mutation MarkNotificationReadById {
  markNotificationAsReadById(notificationId: "12345")
}
```

## Audit Log Operations

### 1. Get Audit Logs with Pagination

```graphql
query GetAuditLogs {
  getAuditLogs(
    filter: {
      startTime: "2025-01-01T00:00:00"
      endTime: "2025-01-31T23:59:59"
      userId: "<EMAIL>"
      action: "NOTIFICATION_SENT"
      result: "SUCCESS"
    }
    page: 0
    size: 20
  ) {
    content {
      id
      timestamp
      correlationId
      requestId
      userId
      sourceService
      action
      result
      entityType
      entityId
      details
      additionalData
    }
    totalElements
    totalPages
    pageNumber
    pageSize
  }
}
```

### 2. Get Audit Log by ID

```graphql
query GetAuditLogById {
  getAuditLogById(id: 12345) {
    id
    timestamp
    correlationId
    requestId
    userId
    sourceService
    action
    result
    entityType
    entityId
    details
    additionalData
  }
}
```

### 3. Get Audit Logs by Correlation ID

```graphql
query GetAuditLogsByCorrelationId {
  getAuditLogsByCorrelationId(correlationId: "req-123-456-789") {
    id
    timestamp
    correlationId
    requestId
    userId
    sourceService
    action
    result
    entityType
    entityId
    details
    additionalData
  }
}
```

### 4. Filter Audit Logs by Action

```graphql
query GetEmailAuditLogs {
  getAuditLogs(
    filter: {
      startTime: "2025-01-01T00:00:00"
      endTime: "2025-01-31T23:59:59"
      action: "EMAIL_SENT"
    }
    page: 0
    size: 50
  ) {
    content {
      id
      timestamp
      userId
      action
      result
      entityType
      entityId
      details
    }
    totalElements
    totalPages
  }
}
```

### 5. Filter Audit Logs by Entity

```graphql
query GetTemplateAuditLogs {
  getAuditLogs(
    filter: {
      entityType: "EmailTemplate"
      entityId: "welcome-email"
      startTime: "2025-01-01T00:00:00"
      endTime: "2025-01-31T23:59:59"
    }
    page: 0
    size: 25
  ) {
    content {
      id
      timestamp
      userId
      action
      result
      details
      additionalData
    }
    totalElements
  }
}
```

## Advanced Features

### 1. Complex Email with Multiple Attachments and Variables

```graphql
mutation SendComplexEmail {
  sendNotification(input: {
    sender: "<EMAIL>"
    type: EMAIL
    content: "Comprehensive quarterly report package"
    templateName: "quarterly-report-package"
    messageType: INFORMATIONAL
    importance: HIGH
    recipientDetails: [
      {
        email: "<EMAIL>"
        type: TO
        variables: [
          { key: "executiveName", value: "Robert Johnson" }
          { key: "executiveTitle", value: "Chief Executive Officer" }
          { key: "quarter", value: "Q4 2024" }
          { key: "totalRevenue", value: "$2.5M" }
          { key: "growthRate", value: "15%" }
        ]
        attachments: [
          {
            filename: "executive-summary.pdf"
            contentType: "application/pdf"
            filePath: "/reports/q4-2024/executive-summary.pdf"
            description: "Executive Summary Report"
          }
          {
            filename: "financial-highlights.xlsx"
            contentType: "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet"
            filePath: "/reports/q4-2024/financial-highlights.xlsx"
            description: "Financial Highlights Spreadsheet"
          }
        ]
      }
      {
        email: "<EMAIL>"
        type: CC
        variables: [
          { key: "teamName", value: "Finance Team" }
          { key: "quarter", value: "Q4 2024" }
          { key: "budgetVariance", value: "-2.3%" }
          { key: "forecastAccuracy", value: "97.8%" }
        ]
        attachments: [
          {
            filename: "detailed-financials.xlsx"
            contentType: "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet"
            filePath: "/reports/q4-2024/detailed-financials.xlsx"
            description: "Detailed Financial Analysis"
          }
        ]
      }
    ]
    variables: [
      { key: "companyName", value: "Ascent Business Solutions" }
      { key: "reportingPeriod", value: "October - December 2024" }
      { key: "preparedBy", value: "Financial Analysis Team" }
      { key: "reportDate", value: "January 15, 2025" }
    ]
    commonAttachments: [
      {
        filename: "company-performance-dashboard.pdf"
        contentType: "application/pdf"
        filePath: "/reports/q4-2024/performance-dashboard.pdf"
        description: "Company Performance Dashboard"
      }
      {
        filename: "market-analysis.docx"
        contentType: "application/vnd.openxmlformats-officedocument.wordprocessingml.document"
        filePath: "/reports/q4-2024/market-analysis.docx"
        description: "Market Analysis Report"
      }
    ]
  })
}
```

### 2. Multi-Language Notification

```graphql
mutation SendMultiLanguageNotification {
  sendNotification(input: {
    sender: "<EMAIL>"
    type: EMAIL
    content: "Important company announcement"
    templateName: "multi-language-announcement"
    messageType: INFORMATIONAL
    importance: HIGH
    recipientDetails: [
      {
        email: "<EMAIL>"
        type: TO
        variables: [
          { key: "employeeName", value: "John Smith" }
          { key: "language", value: "en" }
          { key: "greeting", value: "Dear" }
          { key: "announcement", value: "We are pleased to announce our expansion into new markets." }
          { key: "closing", value: "Best regards" }
        ]
      }
      {
        email: "<EMAIL>"
        type: TO
        variables: [
          { key: "employeeName", value: "María García" }
          { key: "language", value: "es" }
          { key: "greeting", value: "Estimada" }
          { key: "announcement", value: "Nos complace anunciar nuestra expansión a nuevos mercados." }
          { key: "closing", value: "Saludos cordiales" }
        ]
      }
      {
        email: "<EMAIL>"
        type: TO
        variables: [
          { key: "employeeName", value: "Pierre Dubois" }
          { key: "language", value: "fr" }
          { key: "greeting", value: "Cher" }
          { key: "announcement", value: "Nous sommes heureux d'annoncer notre expansion vers de nouveaux marchés." }
          { key: "closing", value: "Cordialement" }
        ]
      }
    ]
    variables: [
      { key: "companyName", value: "Ascent Business Solutions" }
      { key: "announcementDate", value: "January 15, 2025" }
    ]
  })
}
```

### 3. Conditional Notification Based on User Preferences

```graphql
mutation SendConditionalNotification {
  sendNotification(input: {
    sender: "<EMAIL>"
    type: IN_APP
    content: "System update notification"
    messageType: ALERT
    importance: MEDIUM
    recipientDetails: [
      {
        email: "<EMAIL>"
        variables: [
          { key: "userName", value: "Alice Johnson" }
          { key: "preferredTime", value: "morning" }
          { key: "notificationFrequency", value: "immediate" }
          { key: "updateType", value: "security" }
          { key: "userRole", value: "admin" }
        ]
      }
      {
        email: "<EMAIL>"
        variables: [
          { key: "userName", value: "Bob Wilson" }
          { key: "preferredTime", value: "evening" }
          { key: "notificationFrequency", value: "daily_digest" }
          { key: "updateType", value: "feature" }
          { key: "userRole", value: "user" }
        ]
      }
    ]
    variables: [
      { key: "updateVersion", value: "2.1.5" }
      { key: "releaseDate", value: "2025-01-20" }
      { key: "maintenanceWindow", value: "2025-01-19 02:00-04:00 UTC" }
    ]
  })
}
```

### 4. Notification with Rich Content and Embedded Media

```graphql
mutation SendRichContentNotification {
  sendNotification(input: {
    sender: "<EMAIL>"
    type: EMAIL
    content: "Product launch announcement with rich media"
    templateName: "product-launch-rich"
    messageType: PROMOTIONAL
    importance: HIGH
    recipientDetails: [
      {
        email: "<EMAIL>"
        type: TO
        variables: [
          { key: "customerName", value: "Sarah Davis" }
          { key: "productName", value: "AI-Powered Analytics Suite" }
          { key: "launchDate", value: "February 1, 2025" }
          { key: "earlyBirdDiscount", value: "30%" }
          { key: "demoLink", value: "https://demo.company.com/analytics-suite" }
          { key: "videoThumbnail", value: "https://cdn.company.com/videos/product-demo-thumb.jpg" }
          { key: "videoLink", value: "https://cdn.company.com/videos/product-demo.mp4" }
        ]
        attachments: [
          {
            filename: "product-brochure.pdf"
            contentType: "application/pdf"
            filePath: "/marketing/products/analytics-suite/brochure.pdf"
            description: "Product Brochure"
          }
          {
            filename: "feature-comparison.xlsx"
            contentType: "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet"
            filePath: "/marketing/products/analytics-suite/feature-comparison.xlsx"
            description: "Feature Comparison Chart"
          }
        ]
      }
    ]
    variables: [
      { key: "companyName", value: "Ascent Business Solutions" }
      { key: "supportEmail", value: "<EMAIL>" }
      { key: "salesEmail", value: "<EMAIL>" }
      { key: "unsubscribeLink", value: "https://company.com/unsubscribe" }
    ]
    commonAttachments: [
      {
        filename: "company-logo-high-res.png"
        contentType: "image/png"
        filePath: "/assets/branding/logo-high-res.png"
        description: "Company Logo"
      }
    ]
  })
}
```

## Error Handling

### 1. Common Error Responses

```json
// Authentication Error
{
  "errors": [
    {
      "message": "Authentication required. Please provide a valid JWT token.",
      "locations": [{"line": 2, "column": 3}],
      "path": ["sendNotification"],
      "extensions": {
        "code": "UNAUTHENTICATED",
        "exception": {
          "stacktrace": ["UnauthorizedException: Authentication required..."]
        }
      }
    }
  ],
  "data": null
}

// Validation Error
{
  "errors": [
    {
      "message": "Sender cannot be null or empty",
      "locations": [{"line": 2, "column": 3}],
      "path": ["sendNotification"],
      "extensions": {
        "code": "BAD_USER_INPUT",
        "validationErrors": [
          {
            "field": "sender",
            "code": "REQUIRED_FIELD_MISSING",
            "message": "Sender cannot be null or empty"
          }
        ]
      }
    }
  ],
  "data": null
}

// Template Not Found Error
{
  "errors": [
    {
      "message": "Template not found: non-existent-template",
      "locations": [{"line": 2, "column": 3}],
      "path": ["getTemplate"],
      "extensions": {
        "code": "NOT_FOUND",
        "entityType": "NotificationTemplate",
        "entityId": "non-existent-template"
      }
    }
  ],
  "data": null
}

// Email Delivery Error
{
  "errors": [
    {
      "message": "Failed to send email notification",
      "locations": [{"line": 2, "column": 3}],
      "path": ["sendNotification"],
      "extensions": {
        "code": "EMAIL_DELIVERY_FAILED",
        "details": "SMTP server connection timeout",
        "recipientEmail": "<EMAIL>"
      }
    }
  ],
  "data": null
}
```

### 2. Partial Success Responses

```json
// Notification sent but some recipients failed
{
  "data": {
    "sendNotification": true
  },
  "extensions": {
    "warnings": [
      {
        "code": "PARTIAL_DELIVERY_FAILURE",
        "message": "Notification sent successfully but some recipients failed",
        "details": {
          "totalRecipients": 5,
          "successfulDeliveries": 3,
          "failedDeliveries": 2,
          "failedRecipients": [
            {
              "email": "<EMAIL>",
              "error": "Invalid email address"
            },
            {
              "email": "<EMAIL>",
              "error": "Recipient blocked notifications"
            }
          ]
        }
      }
    ]
  }
}
```

### 3. Error Handling Best Practices

```graphql
# Always check for errors in the response
mutation SendNotificationWithErrorHandling {
  sendNotification(input: {
    sender: "<EMAIL>"
    type: EMAIL
    content: "Test notification"
    recipients: ["<EMAIL>"]
  })
}
```

```javascript
// Client-side error handling example
const response = await graphqlClient.request(SEND_NOTIFICATION_MUTATION, variables);

if (response.errors) {
  response.errors.forEach(error => {
    console.error('GraphQL Error:', error.message);
    
    // Handle specific error types
    switch (error.extensions?.code) {
      case 'UNAUTHENTICATED':
        // Redirect to login
        redirectToLogin();
        break;
      case 'EMAIL_DELIVERY_FAILED':
        // Show email delivery error
        showEmailError(error.extensions.recipientEmail, error.extensions.details);
        break;
      case 'TEMPLATE_NOT_FOUND':
        // Show template not found error
        showTemplateError(error.extensions.entityId);
        break;
      case 'BAD_USER_INPUT':
        // Show validation errors
        const validationErrors = error.extensions.validationErrors;
        showValidationErrors(validationErrors);
        break;
      default:
        // Show generic error message
        showErrorMessage(error.message);
    }
  });
}

// Check for warnings in extensions
if (response.extensions?.warnings) {
  response.extensions.warnings.forEach(warning => {
    console.warn('GraphQL Warning:', warning.message);
    
    switch (warning.code) {
      case 'PARTIAL_DELIVERY_FAILURE':
        showPartialDeliveryWarning(warning.details);
        break;
      default:
        showGenericWarning(warning.message);
    }
  });
}
```

## Best Practices

### 1. Authentication and Security

```graphql
# Always include proper authentication headers
# Authorization: Bearer <jwt-token>

# Use HTTPS in production
# Validate JWT tokens on server side
# Implement proper CORS policies
```

### 2. Notification Design Best Practices

```graphql
# Use appropriate message types and importance levels
mutation SendWellDesignedNotification {
  sendNotification(input: {
    sender: "<EMAIL>"
    type: EMAIL
    content: "Your account security has been updated"
    templateName: "security-update"
    messageType: ALERT  # Use ALERT for security-related messages
    importance: HIGH    # Use HIGH for critical security notifications
    recipientDetails: [
      {
        email: "<EMAIL>"
        type: TO
        variables: [
          { key: "userName", value: "John Doe" }
          { key: "updateTime", value: "2025-01-15 14:30:00 UTC" }
          { key: "ipAddress", value: "*************" }
          { key: "actionRequired", value: "Please review your account settings" }
        ]
      }
    ]
  })
}

# Use descriptive template names and organize by purpose
# Examples: "welcome-email", "password-reset", "invoice-reminder"
```

### 3. Template Management Best Practices

```graphql
# Create reusable templates with proper variable placeholders
mutation CreateReusableTemplate {
  addUpdateNotificationTemplate(input: {
    alias: "user-action-required"
    subject: "Action Required: ${actionType}"
    body: """
    <html>
    <body>
      <h2>Hello ${userName},</h2>
      <p>An action is required on your account:</p>
      
      <div style="background-color: #f9f9f9; padding: 15px; margin: 20px 0; border-left: 4px solid #007bff;">
        <h3>${actionType}</h3>
        <p>${actionDescription}</p>
        <p><strong>Due Date:</strong> ${dueDate}</p>
      </div>
      
      <#if actionUrl??>
        <p><a href="${actionUrl}" style="background-color: #007bff; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px;">Take Action</a></p>
      </#if>
      
      <p>If you have questions, please contact us at ${supportEmail}.</p>
      
      <p>Best regards,<br>
      ${sender.name}</p>
    </body>
    </html>
    """
  }) {
    id
    alias
    subject
    body
  }
}
```

### 4. Variable Management

```graphql
# Use consistent variable naming conventions
mutation SendNotificationWithConsistentVariables {
  sendNotification(input: {
    sender: "<EMAIL>"
    type: EMAIL
    templateName: "employee-update"
    recipientDetails: [
      {
        email: "<EMAIL>"
        variables: [
          # Use camelCase for variable names
          { key: "employeeName", value: "John Smith" }
          { key: "employeeId", value: "EMP001" }
          { key: "departmentName", value: "Engineering" }
          { key: "managerName", value: "Jane Doe" }
          { key: "startDate", value: "2025-01-15" }
          
          # Use descriptive names for complex data
          { key: "salaryAmount", value: "$75,000" }
          { key: "benefitsPackage", value: "Premium Health + Dental" }
          { key: "workLocation", value: "Remote" }
        ]
      }
    ]
    # Global variables for common data
    variables: [
      { key: "companyName", value: "Ascent Business Solutions" }
      { key: "hrContactEmail", value: "<EMAIL>" }
      { key: "hrContactPhone", value: "******-0123" }
      { key: "currentYear", value: "2025" }
    ]
  })
}
```

### 5. Attachment Handling

```graphql
# Best practices for attachments
mutation SendNotificationWithAttachments {
  sendNotification(input: {
    sender: "<EMAIL>"
    type: EMAIL
    templateName: "document-delivery"
    recipientDetails: [
      {
        email: "<EMAIL>"
        attachments: [
          {
            # Use descriptive filenames
            filename: "employee-handbook-2025.pdf"
            contentType: "application/pdf"
            # Prefer file paths over base64 content for large files
            filePath: "/documents/hr/employee-handbook-2025.pdf"
            description: "Employee Handbook - Updated for 2025"
          }
          {
            filename: "benefits-summary.xlsx"
            contentType: "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet"
            filePath: "/documents/hr/benefits-summary.xlsx"
            description: "Benefits Package Summary"
          }
        ]
      }
    ]
    # Use common attachments for files sent to all recipients
    commonAttachments: [
      {
        filename: "company-logo.png"
        contentType: "image/png"
        filePath: "/assets/branding/logo.png"
        description: "Company Logo"
      }
    ]
  })
}
```

### 6. Performance Optimization

```graphql
# Use pagination for large result sets
query OptimizedAuditLogQuery {
  getAuditLogs(
    filter: {
      startTime: "2025-01-01T00:00:00"
      endTime: "2025-01-31T23:59:59"
    }
    page: 0
    size: 50  # Reasonable page size
  ) {
    content {
      # Only request fields you need
      id
      timestamp
      action
      result
      userId
    }
    totalElements
    totalPages
  }
}

# Batch notifications when possible
mutation BatchNotifications {
  sendNotification(input: {
    sender: "<EMAIL>"
    type: IN_APP
    content: "System maintenance notification"
    recipientDetails: [
      # Send to multiple recipients in one request
      { email: "<EMAIL>" }
      { email: "<EMAIL>" }
      { email: "<EMAIL>" }
    ]
  })
}
```

### 7. Error Handling and Retry Logic

```javascript
// Implement exponential backoff for retries
async function sendNotificationWithRetry(input, maxRetries = 3) {
  for (let attempt = 1; attempt <= maxRetries; attempt++) {
    try {
      return await graphqlClient.request(SEND_NOTIFICATION_MUTATION, { input });
    } catch (error) {
      if (attempt === maxRetries) throw error;
      
      // Exponential backoff: 1s, 2s, 4s
      const delay = Math.pow(2, attempt - 1) * 1000;
      await new Promise(resolve => setTimeout(resolve, delay));
    }
  }
}

// Handle specific error scenarios
function handleNotificationError(error) {
  switch (error.extensions?.code) {
    case 'EMAIL_DELIVERY_FAILED':
      return 'Failed to deliver email. Please check the recipient address.';
    case 'TEMPLATE_NOT_FOUND':
      return 'Notification template not found. Please contact support.';
    case 'ATTACHMENT_TOO_LARGE':
      return 'Attachment size exceeds limit. Please use smaller files.';
    case 'INVALID_RECIPIENT':
      return 'Invalid recipient email address format.';
    default:
      return 'Notification failed. Please try again.';
  }
}
```

### 8. Monitoring and Logging

```graphql
# Monitor notification delivery success rates
query GetNotificationStats {
  getAuditLogs(
    filter: {
      action: "NOTIFICATION_SENT"
      startTime: "2025-01-01T00:00:00"
      endTime: "2025-01-31T23:59:59"
    }
    page: 0
    size: 1000
  ) {
    content {
      result
      entityType
      details
      timestamp
    }
    totalElements
  }
}

# Track template usage
query GetTemplateUsageStats {
  getAuditLogs(
    filter: {
      action: "TEMPLATE_RETRIEVED"
      startTime: "2025-01-01T00:00:00"
      endTime: "2025-01-31T23:59:59"
    }
    page: 0
    size: 500
  ) {
    content {
      entityId
      timestamp
      userId
    }
    totalElements
  }
}
```

### 9. Rate Limiting and Throttling

```javascript
// Implement client-side rate limiting
class NotificationRateLimiter {
  constructor(maxRequests = 50, windowMs = 60000) {
    this.maxRequests = maxRequests;
    this.windowMs = windowMs;
    this.requests = [];
  }
  
  async throttle() {
    const now = Date.now();
    this.requests = this.requests.filter(time => now - time < this.windowMs);
    
    if (this.requests.length >= this.maxRequests) {
      const oldestRequest = Math.min(...this.requests);
      const waitTime = this.windowMs - (now - oldestRequest);
      await new Promise(resolve => setTimeout(resolve, waitTime));
    }
    
    this.requests.push(now);
  }
}

// Usage
const rateLimiter = new NotificationRateLimiter(50, 60000); // 50 requests per minute

async function sendNotification(input) {
  await rateLimiter.throttle();
  return await graphqlClient.request(SEND_NOTIFICATION_MUTATION, { input });
}
```

### 10. Testing Examples

```javascript
// Unit test example for notification operations
describe('Notification Service', () => {
  test('should send email notification successfully', async () => {
    const input = {
      sender: '<EMAIL>',
      type: 'EMAIL',
      content: 'Test notification',
      templateName: 'test-template',
      recipients: ['<EMAIL>']
    };
    
    const result = await sendNotification(input);
    
    expect(result.sendNotification).toBe(true);
  });
  
  test('should handle invalid recipient gracefully', async () => {
    const input = {
      sender: '<EMAIL>',
      type: 'EMAIL',
      content: 'Test notification',
      recipients: ['invalid-email']
    };
    
    await expect(sendNotification(input)).rejects.toThrow('INVALID_RECIPIENT');
  });
  
  test('should create template successfully', async () => {
    const templateInput = {
      alias: 'test-template',
      subject: 'Test Subject',
      body: '<html><body>Test Body</body></html>'
    };
    
    const result = await createTemplate(templateInput);
    
    expect(result.addUpdateNotificationTemplate.alias).toBe('test-template');
  });
});

// Integration test example
describe('Notification Integration', () => {
  test('should send notification and track in audit log', async () => {
    // Send notification
    const notificationInput = {
      sender: '<EMAIL>',
      type: 'EMAIL',
      content: 'Integration test notification',
      recipients: ['<EMAIL>']
    };
    
    await sendNotification(notificationInput);
    
    // Wait for audit log entry
    await new Promise(resolve => setTimeout(resolve, 1000));
    
    // Check audit log
    const auditLogs = await getAuditLogs({
      filter: {
        action: 'NOTIFICATION_SENT',
        userId: '<EMAIL>'
      }
    });
    
    expect(auditLogs.getAuditLogs.content.length).toBeGreaterThan(0);
    expect(auditLogs.getAuditLogs.content[0].result).toBe('SUCCESS');
  });
});
```

### 11. Production Deployment Considerations

```yaml
# Environment-specific configurations
production:
  notification:
    rate_limiting:
      requests_per_minute: 1000
      burst_size: 100
    
    email:
      smtp_host: "smtp.company.com"
      smtp_port: 587
      smtp_username: "${SMTP_USERNAME}"
      smtp_password: "${SMTP_PASSWORD}"
      from_address: "<EMAIL>"
      max_attachment_size: 25MB
    
    rabbitmq:
      host: "${RABBITMQ_HOST}"
      port: 5672
      username: "${RABBITMQ_USERNAME}"
      password: "${RABBITMQ_PASSWORD}"
      virtual_host: "/notifications"
    
    audit:
      retention_days: 90
      log_level: INFO
    
    templates:
      cache_ttl: 3600  # 1 hour
      max_template_size: 1MB
  
  security:
    jwt_expiration: 24h
    cors_origins:
      - https://app.company.com
      - https://admin.company.com
    https_only: true
    
  monitoring:
    metrics_enabled: true
    health_check_interval: 30s
    alert_thresholds:
      email_failure_rate: 5%
      notification_queue_size: 1000
```

---

## Summary

This document provides exhaustive GraphQL examples for the Notification service covering:

- **Notification Operations**: Email notifications, in-app notifications, broadcast notifications, with support for attachments, variables, and recipient-specific customization
- **Template Management**: Create, update, and retrieve notification templates with advanced features like conditional content and rich formatting
- **In-App Notifications**: Real-time notification delivery via RabbitMQ, polling, consumption, and read status management
- **Audit Log Operations**: Comprehensive audit trail with filtering, pagination, and correlation tracking
- **Advanced Features**: Multi-language support, rich content, conditional notifications, and complex variable handling
- **Error Handling**: Comprehensive error scenarios, partial success handling, and recovery strategies
- **Best Practices**: Security, performance, monitoring, testing, and deployment guidelines

All examples include proper authentication, error handling, and follow GraphQL best practices for production use. The service supports both simple and complex notification scenarios with extensive customization options for enterprise-grade notification delivery.