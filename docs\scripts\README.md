# GRC Platform Docker Management Scripts

This repository contains comprehensive scripts to manage the GRC Platform Docker services, including cleaning, rebuilding, running, and pushing images to Docker Hub.

## Recent Fixes Applied

✅ **Configuration Issues Fixed:**
- Fixed Redis connection: Updated hostnames from `redis` to `grc-redis-shared`
- Fixed MySQL connection: Updated hostnames from `mysql` to `grc-mysql-shared`
- Fixed RabbitMQ connection: Updated hostnames from `rabbitmq` to `grc-rabbitmq-shared`
- Fixed Zipkin tracing: Updated hostnames from `zipkin` to `grc-zipkin-shared`
- Fixed Elasticsearch: Updated hostnames from `elasticsearch` to `grc-elasticsearch-shared`
- Fixed Redis password: Updated from `shared_redis_password` to `local_redis_password`
- Fixed Docker Compose dependencies: Now uses both compose files together

## Scripts Overview

### 1. `local-docker-rebuild.sh` (Linux/macOS/WSL)
Bash script for Unix-like systems including Windows Subsystem for Linux (WSL).

### 2. `local-docker-rebuild.ps1` (Windows PowerShell)
PowerShell script for native Windows environments.

## What These Scripts Do

Both scripts perform the following operations:

1. **Clean Docker Environment**
   - Stop all running Docker Compose services
   - Remove all containers, images, and volumes
   - Clean up unused Docker resources

2. **Rebuild Services from Scratch**
   - Build DMS service Docker image with no cache
   - Build Notification service Docker image with no cache
   - Tag images for both local use and Docker Hub

3. **Start Complete Infrastructure**
   - Create necessary Docker networks
   - Start shared infrastructure (MySQL, Redis, RabbitMQ, Elasticsearch, etc.)
   - Start application services (DMS and Notification)
   - Wait for all services to be healthy

4. **Push to Docker Hub** (Optional)
   - Login to Docker Hub with username `anurag98cs`
   - Push both service images to public Docker Hub repository
   - Images will be available as:
     - `anurag98cs/grc-dms-svc:latest`
     - `anurag98cs/grc-notification-svc:latest`

## Prerequisites

### Required Files
- `.env.local-dev` - Environment configuration file
- `docker-compose.shared.yml` - Shared infrastructure services
- `docker-compose.services.yml` - Application services
- `dms-svc/Dockerfile` - DMS service Dockerfile
- `notification-svc/Dockerfile` - Notification service Dockerfile

### Required Software
- Docker Desktop (running)
- Docker Compose
- For Bash script: Bash shell, curl
- For PowerShell script: PowerShell 5.1+ or PowerShell Core

## Usage

### Windows (PowerShell)
```powershell
# Run the complete process
.\local-docker-rebuild.ps1

# Skip Docker Hub push
.\local-docker-rebuild.ps1 -SkipPush

# Use different Docker Hub username
.\local-docker-rebuild.ps1 -DockerUsername "yourusername"
```

### Linux/macOS/WSL (Bash)
```bash
# Make script executable (if needed)
chmod +x local-docker-rebuild.sh

# Run the complete process
./local-docker-rebuild.sh
```

## Interactive Prompts

Both scripts will ask for confirmation at key steps:

1. **Initial Confirmation**: Confirm you want to proceed with the cleanup and rebuild
2. **Docker Hub Push**: Choose whether to push images to Docker Hub
3. **Docker Hub Password**: Enter your Docker Hub password when pushing

## Service URLs After Deployment

Once the scripts complete successfully, the following services will be available:

| Service | URL | Credentials |
|---------|-----|-------------|
| DMS Service | http://localhost:9093/actuator/health | - |
| Notification Service | http://localhost:9091/actuator/health | - |
| Grafana Dashboard | http://localhost:3000 | admin/admin |
| RabbitMQ Management | http://localhost:15672 | admin/admin123 |
| Prometheus | http://localhost:9090 | - |
| Zipkin Tracing | http://localhost:9411 | - |
| MySQL Database | localhost:3306 | See .env.local-dev |
| Redis Cache | localhost:6379 | See .env.local-dev |
| Elasticsearch | http://localhost:9200 | - |

## Docker Hub Images

After successful push, the images will be publicly available:

```bash
# Pull DMS service
docker pull anurag98cs/grc-dms-svc:latest

# Pull Notification service
docker pull anurag98cs/grc-notification-svc:latest

# Run services using public images
docker run -d -p 9093:9093 anurag98cs/grc-dms-svc:latest
docker run -d -p 9091:9091 anurag98cs/grc-notification-svc:latest
```

## Troubleshooting

### Common Issues

1. **Docker not running**
   ```
   Error: Docker is not running
   Solution: Start Docker Desktop and wait for it to be ready
   ```

2. **Port conflicts**
   ```
   Error: Port already in use
   Solution: Stop conflicting services or change ports in .env.local-dev
   ```

3. **Build failures**
   ```
   Error: Docker build failed
   Solution: Check Dockerfile syntax and ensure all required files exist
   ```

4. **Health check failures**
   ```
   Warning: Services not healthy
   Solution: Check service logs with: docker-compose logs [service-name]
   ```

### Useful Commands

```bash
# Check service status
docker-compose -f docker-compose.shared.yml -f docker-compose.services.yml --env-file .env.local-dev ps

# View service logs
docker-compose -f docker-compose.shared.yml -f docker-compose.services.yml --env-file .env.local-dev logs dms-svc
docker-compose -f docker-compose.shared.yml -f docker-compose.services.yml --env-file .env.local-dev logs notification-svc

# Stop all services
docker-compose -f docker-compose.shared.yml -f docker-compose.services.yml --env-file .env.local-dev down

# Remove everything (nuclear option)
docker system prune -a --volumes
```

## Script Features

### Error Handling
- Comprehensive error checking at each step
- Graceful handling of missing files or services
- Clear error messages with suggested solutions

### Logging
- Color-coded output for different message types
- Progress indicators for long-running operations
- Detailed status reporting

### Safety Features
- Confirmation prompts before destructive operations
- Graceful handling of interruptions (Ctrl+C)
- Validation of prerequisites before execution

### Flexibility
- Optional Docker Hub push
- Configurable Docker Hub username
- Support for both Windows and Unix environments

## Environment Configuration

The scripts use `.env.local-dev` for configuration. Key variables include:

```bash
# Service Configuration
DMS_PORT=9093
NOTIFICATION_PORT=9091

# Database Configuration
MYSQL_ROOT_PASSWORD=root_password
MYSQL_DMS_USER=dms_user
MYSQL_DMS_PASSWORD=dms_password

# Docker Hub Configuration (for pushing)
DOCKER_USERNAME=anurag98cs
```

## Security Notes

- Docker Hub password is requested interactively (not stored in files)
- Services run with non-root users inside containers
- Health checks ensure services are properly started before marking as ready
- Sensitive configuration is managed through environment variables

## Support

For issues or questions:
1. Check the troubleshooting section above
2. Review Docker and service logs
3. Ensure all prerequisites are met
4. Verify Docker Desktop is running and healthy

---

**Note**: These scripts are designed for local development environments. For production deployments, additional security and configuration considerations should be applied.