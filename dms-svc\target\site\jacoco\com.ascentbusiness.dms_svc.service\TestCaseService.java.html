<?xml version="1.0" encoding="UTF-8"?><!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Strict//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-strict.dtd"><html xmlns="http://www.w3.org/1999/xhtml" lang="en"><head><meta http-equiv="Content-Type" content="text/html;charset=UTF-8"/><link rel="stylesheet" href="../jacoco-resources/report.css" type="text/css"/><link rel="shortcut icon" href="../jacoco-resources/report.gif" type="image/gif"/><title>TestCaseService.java</title><link rel="stylesheet" href="../jacoco-resources/prettify.css" type="text/css"/><script type="text/javascript" src="../jacoco-resources/prettify.js"></script></head><body onload="window['PR_TAB_WIDTH']=4;prettyPrint()"><div class="breadcrumb" id="breadcrumb"><span class="info"><a href="../jacoco-sessions.html" class="el_session">Sessions</a></span><a href="../index.html" class="el_report">dms-svc</a> &gt; <a href="index.source.html" class="el_package">com.ascentbusiness.dms_svc.service</a> &gt; <span class="el_source">TestCaseService.java</span></div><h1>TestCaseService.java</h1><pre class="source lang-java linenums">package com.ascentbusiness.dms_svc.service;

import com.ascentbusiness.dms_svc.dto.TestCaseResponse;
import com.ascentbusiness.dms_svc.dto.TestCategorySummary;
import com.ascentbusiness.dms_svc.dto.TestCaseCollection;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.stereotype.Service;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.io.BufferedReader;
import java.io.FileReader;
import java.io.IOException;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.util.*;
import java.util.stream.Collectors;

@Service
public class TestCaseService {

<span class="fc" id="L24">    private static final Logger logger = LoggerFactory.getLogger(TestCaseService.class);</span>

    @Value(&quot;${testcase.directory:tests/test-cases}&quot;)
    private String testCaseDirectory;

<span class="fc" id="L29">    private final Map&lt;String, String&gt; categoryDisplayNames = new HashMap&lt;&gt;();</span>

<span class="fc" id="L31">    public TestCaseService() {</span>
<span class="fc" id="L32">        initializeCategoryDisplayNames();</span>
<span class="fc" id="L33">    }</span>

    private void initializeCategoryDisplayNames() {
<span class="fc" id="L36">        categoryDisplayNames.put(&quot;DMS_TestCases_Summary.csv&quot;, &quot;Summary&quot;);</span>
<span class="fc" id="L37">        categoryDisplayNames.put(&quot;DMS_TestCases_No_Access.csv&quot;, &quot;No Access&quot;);</span>
<span class="fc" id="L38">        categoryDisplayNames.put(&quot;DMS_TestCases_READ_Permission.csv&quot;, &quot;READ Permission&quot;);</span>
<span class="fc" id="L39">        categoryDisplayNames.put(&quot;DMS_TestCases_WRITE_Permission.csv&quot;, &quot;WRITE Permission&quot;);</span>
<span class="fc" id="L40">        categoryDisplayNames.put(&quot;DMS_TestCases_DELETE_Permission.csv&quot;, &quot;DELETE Permission&quot;);</span>
<span class="fc" id="L41">        categoryDisplayNames.put(&quot;DMS_TestCases_ADMIN_Permission.csv&quot;, &quot;ADMIN Permission&quot;);</span>
<span class="fc" id="L42">        categoryDisplayNames.put(&quot;DMS_TestCases_Creator_Privileges.csv&quot;, &quot;Creator Privileges&quot;);</span>
<span class="fc" id="L43">        categoryDisplayNames.put(&quot;DMS_TestCases_Multi_Role.csv&quot;, &quot;Multi Role&quot;);</span>
<span class="fc" id="L44">        categoryDisplayNames.put(&quot;DMS_TestCases_Error_Handling.csv&quot;, &quot;Error Handling&quot;);</span>
<span class="fc" id="L45">        categoryDisplayNames.put(&quot;DMS_TestCases_Storage_Providers.csv&quot;, &quot;Storage Providers&quot;);</span>
<span class="fc" id="L46">        categoryDisplayNames.put(&quot;DMS_TestCases_Search_Filter.csv&quot;, &quot;Search Filter&quot;);</span>
<span class="fc" id="L47">        categoryDisplayNames.put(&quot;DMS_TestCases_Audit_Logs.csv&quot;, &quot;Audit Logs&quot;);</span>
<span class="fc" id="L48">        categoryDisplayNames.put(&quot;DMS_TestCases_Security_Validation.csv&quot;, &quot;Security Validation&quot;);</span>
<span class="fc" id="L49">        categoryDisplayNames.put(&quot;DMS_TestCases_Performance.csv&quot;, &quot;Performance&quot;);</span>
<span class="fc" id="L50">        categoryDisplayNames.put(&quot;DMS_TestCases_Integration.csv&quot;, &quot;Integration&quot;);</span>
<span class="fc" id="L51">        categoryDisplayNames.put(&quot;DMS_TestCases_Boundary_Tests.csv&quot;, &quot;Boundary Tests&quot;);</span>
<span class="fc" id="L52">    }</span>

    @Cacheable(&quot;testCaseSummary&quot;)
    public List&lt;TestCategorySummary&gt; getAllTestCategoriesSummary() {
<span class="nc" id="L56">        List&lt;TestCategorySummary&gt; summaries = new ArrayList&lt;&gt;();</span>
        
        try {
<span class="nc" id="L59">            Path testCasePath = Paths.get(testCaseDirectory);</span>
<span class="nc bnc" id="L60" title="All 2 branches missed.">            if (!Files.exists(testCasePath)) {</span>
<span class="nc" id="L61">                logger.warn(&quot;Test case directory does not exist: {}&quot;, testCaseDirectory);</span>
<span class="nc" id="L62">                return summaries;</span>
            }

            // Parse the summary CSV first
<span class="nc" id="L66">            Path summaryFile = testCasePath.resolve(&quot;DMS_TestCases_Summary.csv&quot;);</span>
<span class="nc bnc" id="L67" title="All 2 branches missed.">            if (Files.exists(summaryFile)) {</span>
<span class="nc" id="L68">                summaries = parseSummaryFile(summaryFile.toString());</span>
            }

<span class="nc" id="L71">        } catch (Exception e) {</span>
<span class="nc" id="L72">            logger.error(&quot;Error reading test case summaries&quot;, e);</span>
<span class="nc" id="L73">        }</span>

<span class="nc" id="L75">        return summaries;</span>
    }

    @Cacheable(&quot;testCasesByCategory&quot;)
    public TestCaseCollection getTestCasesByCategory(String category) {
        try {
<span class="nc" id="L81">            String fileName = getCsvFileNameForCategory(category);</span>
<span class="nc" id="L82">            Path filePath = Paths.get(testCaseDirectory, fileName);</span>
            
<span class="nc bnc" id="L84" title="All 2 branches missed.">            if (!Files.exists(filePath)) {</span>
<span class="nc" id="L85">                logger.warn(&quot;Test case file does not exist: {}&quot;, filePath);</span>
<span class="nc" id="L86">                return null;</span>
            }

<span class="nc" id="L89">            List&lt;TestCaseResponse&gt; testCases = parseTestCaseFile(filePath.toString(), category);</span>
<span class="nc" id="L90">            String displayName = categoryDisplayNames.getOrDefault(fileName, category);</span>
            
<span class="nc" id="L92">            return new TestCaseCollection(</span>
                category,
                displayName,
<span class="nc" id="L95">                testCases.size(),</span>
<span class="nc" id="L96">                getTestRange(testCases),</span>
<span class="nc" id="L97">                getCoverageAreas(category),</span>
<span class="nc" id="L98">                getDescription(category),</span>
                testCases
            );

<span class="nc" id="L102">        } catch (Exception e) {</span>
<span class="nc" id="L103">            logger.error(&quot;Error reading test cases for category: {}&quot;, category, e);</span>
<span class="nc" id="L104">            return null;</span>
        }
    }

    @Cacheable(&quot;testCaseById&quot;)
    public TestCaseResponse getTestCaseById(String category, String testId) {
<span class="nc" id="L110">        TestCaseCollection collection = getTestCasesByCategory(category);</span>
<span class="nc bnc" id="L111" title="All 4 branches missed.">        if (collection == null || collection.getTestCases() == null) {</span>
<span class="nc" id="L112">            return null;</span>
        }

<span class="nc" id="L115">        return collection.getTestCases().stream()</span>
<span class="nc" id="L116">                .filter(testCase -&gt; testId.equals(testCase.getSerialNumber()))</span>
<span class="nc" id="L117">                .findFirst()</span>
<span class="nc" id="L118">                .orElse(null);</span>
    }

    public List&lt;String&gt; getAllCategories() {
        try {
<span class="nc" id="L123">            Path testCasePath = Paths.get(testCaseDirectory);</span>
<span class="nc bnc" id="L124" title="All 2 branches missed.">            if (!Files.exists(testCasePath)) {</span>
<span class="nc" id="L125">                return Collections.emptyList();</span>
            }

<span class="nc" id="L128">            return Files.list(testCasePath)</span>
<span class="nc" id="L129">                    .filter(path -&gt; path.toString().endsWith(&quot;.csv&quot;))</span>
<span class="nc bnc" id="L130" title="All 2 branches missed.">                    .filter(path -&gt; !path.getFileName().toString().equals(&quot;DMS_TestCases_Summary.csv&quot;))</span>
<span class="nc" id="L131">                    .map(path -&gt; getCategoryFromFileName(path.getFileName().toString()))</span>
<span class="nc" id="L132">                    .collect(Collectors.toList());</span>

<span class="nc" id="L134">        } catch (Exception e) {</span>
<span class="nc" id="L135">            logger.error(&quot;Error getting categories&quot;, e);</span>
<span class="nc" id="L136">            return Collections.emptyList();</span>
        }
    }

    public List&lt;TestCaseResponse&gt; searchTestCases(String query) {
<span class="nc" id="L141">        List&lt;TestCaseResponse&gt; allTestCases = new ArrayList&lt;&gt;();</span>
        
<span class="nc bnc" id="L143" title="All 2 branches missed.">        for (String category : getAllCategories()) {</span>
<span class="nc" id="L144">            TestCaseCollection collection = getTestCasesByCategory(category);</span>
<span class="nc bnc" id="L145" title="All 4 branches missed.">            if (collection != null &amp;&amp; collection.getTestCases() != null) {</span>
<span class="nc" id="L146">                allTestCases.addAll(collection.getTestCases());</span>
            }
<span class="nc" id="L148">        }</span>

<span class="nc" id="L150">        return allTestCases.stream()</span>
<span class="nc" id="L151">                .filter(testCase -&gt; containsQuery(testCase, query))</span>
<span class="nc" id="L152">                .collect(Collectors.toList());</span>
    }

    private List&lt;TestCategorySummary&gt; parseSummaryFile(String filePath) {
<span class="nc" id="L156">        List&lt;TestCategorySummary&gt; summaries = new ArrayList&lt;&gt;();</span>
        
<span class="nc" id="L158">        try (BufferedReader reader = new BufferedReader(new FileReader(filePath))) {</span>
            String line;
<span class="nc" id="L160">            boolean firstLine = true;</span>
            
<span class="nc bnc" id="L162" title="All 2 branches missed.">            while ((line = reader.readLine()) != null) {</span>
<span class="nc bnc" id="L163" title="All 2 branches missed.">                if (firstLine) {</span>
<span class="nc" id="L164">                    firstLine = false;</span>
<span class="nc" id="L165">                    continue; // Skip header</span>
                }
                
<span class="nc bnc" id="L168" title="All 2 branches missed.">                if (line.trim().isEmpty()) {</span>
<span class="nc" id="L169">                    continue;</span>
                }
                
<span class="nc" id="L172">                String[] columns = parseCSVLine(line);</span>
<span class="nc bnc" id="L173" title="All 4 branches missed.">                if (columns.length &gt;= 5 &amp;&amp; !columns[0].trim().isEmpty()) {</span>
<span class="nc" id="L174">                    TestCategorySummary summary = new TestCategorySummary();</span>
<span class="nc" id="L175">                    summary.setCategoryName(getCategoryFromDisplayName(columns[0].trim()));</span>
<span class="nc" id="L176">                    summary.setDisplayName(columns[0].trim());</span>
<span class="nc" id="L177">                    summary.setTestRange(columns[1].trim());</span>
                    
                    try {
<span class="nc" id="L180">                        summary.setTestCount(Integer.parseInt(columns[2].trim()));</span>
<span class="nc" id="L181">                    } catch (NumberFormatException e) {</span>
<span class="nc" id="L182">                        summary.setTestCount(0);</span>
<span class="nc" id="L183">                    }</span>
                    
<span class="nc" id="L185">                    summary.setCoverageAreas(columns[3].trim());</span>
<span class="nc" id="L186">                    summary.setDescription(columns[4].trim());</span>
                    
<span class="nc" id="L188">                    summaries.add(summary);</span>
                }
<span class="nc" id="L190">            }</span>
<span class="nc" id="L191">        } catch (IOException e) {</span>
<span class="nc" id="L192">            logger.error(&quot;Error parsing summary file: {}&quot;, filePath, e);</span>
<span class="nc" id="L193">        }</span>
        
<span class="nc" id="L195">        return summaries;</span>
    }

    private List&lt;TestCaseResponse&gt; parseTestCaseFile(String filePath, String category) {
<span class="nc" id="L199">        List&lt;TestCaseResponse&gt; testCases = new ArrayList&lt;&gt;();</span>
        
<span class="nc" id="L201">        try (BufferedReader reader = new BufferedReader(new FileReader(filePath))) {</span>
            String line;
<span class="nc" id="L203">            boolean firstLine = true;</span>
            
<span class="nc bnc" id="L205" title="All 2 branches missed.">            while ((line = reader.readLine()) != null) {</span>
<span class="nc bnc" id="L206" title="All 2 branches missed.">                if (firstLine) {</span>
<span class="nc" id="L207">                    firstLine = false;</span>
<span class="nc" id="L208">                    continue; // Skip header</span>
                }
                
<span class="nc bnc" id="L211" title="All 2 branches missed.">                if (line.trim().isEmpty()) {</span>
<span class="nc" id="L212">                    continue;</span>
                }
                
<span class="nc" id="L215">                String[] columns = parseCSVLine(line);</span>
<span class="nc bnc" id="L216" title="All 2 branches missed.">                if (columns.length &gt;= 7) {</span>
<span class="nc" id="L217">                    TestCaseResponse testCase = new TestCaseResponse();</span>
<span class="nc" id="L218">                    testCase.setSerialNumber(columns[0].trim());</span>
<span class="nc" id="L219">                    testCase.setCategory(category);</span>
<span class="nc" id="L220">                    testCase.setJwtRequest(columns[1].trim());</span>
<span class="nc" id="L221">                    testCase.setJwtToken(columns[2].trim());</span>
<span class="nc" id="L222">                    testCase.setRequest(columns[3].trim());</span>
<span class="nc" id="L223">                    testCase.setResponse(columns[4].trim());</span>
<span class="nc" id="L224">                    testCase.setResult(columns[5].trim());</span>
<span class="nc" id="L225">                    testCase.setComment(columns[6].trim());</span>
<span class="nc" id="L226">                    testCase.setDescription(columns[6].trim());</span>
                    
<span class="nc" id="L228">                    testCases.add(testCase);</span>
                }
<span class="nc" id="L230">            }</span>
<span class="nc" id="L231">        } catch (IOException e) {</span>
<span class="nc" id="L232">            logger.error(&quot;Error parsing test case file: {}&quot;, filePath, e);</span>
<span class="nc" id="L233">        }</span>
        
<span class="nc" id="L235">        return testCases;</span>
    }

    private String[] parseCSVLine(String line) {
<span class="nc" id="L239">        List&lt;String&gt; result = new ArrayList&lt;&gt;();</span>
<span class="nc" id="L240">        boolean inQuotes = false;</span>
<span class="nc" id="L241">        StringBuilder currentField = new StringBuilder();</span>
        
<span class="nc bnc" id="L243" title="All 2 branches missed.">        for (char c : line.toCharArray()) {</span>
<span class="nc bnc" id="L244" title="All 2 branches missed.">            if (c == '&quot;') {</span>
<span class="nc bnc" id="L245" title="All 2 branches missed.">                inQuotes = !inQuotes;</span>
<span class="nc bnc" id="L246" title="All 4 branches missed.">            } else if (c == ',' &amp;&amp; !inQuotes) {</span>
<span class="nc" id="L247">                result.add(currentField.toString());</span>
<span class="nc" id="L248">                currentField = new StringBuilder();</span>
            } else {
<span class="nc" id="L250">                currentField.append(c);</span>
            }
        }
<span class="nc" id="L253">        result.add(currentField.toString());</span>
        
<span class="nc" id="L255">        return result.toArray(new String[0]);</span>
    }

    private String getCsvFileNameForCategory(String category) {
<span class="nc" id="L259">        return &quot;DMS_TestCases_&quot; + category + &quot;.csv&quot;;</span>
    }

    private String getCategoryFromFileName(String fileName) {
<span class="nc" id="L263">        return fileName.replace(&quot;DMS_TestCases_&quot;, &quot;&quot;).replace(&quot;.csv&quot;, &quot;&quot;);</span>
    }

    private String getCategoryFromDisplayName(String displayName) {
<span class="nc" id="L267">        return displayName.replace(&quot; &quot;, &quot;_&quot;);</span>
    }

    private String getTestRange(List&lt;TestCaseResponse&gt; testCases) {
<span class="nc bnc" id="L271" title="All 2 branches missed.">        if (testCases.isEmpty()) {</span>
<span class="nc" id="L272">            return &quot;N/A&quot;;</span>
        }
        
<span class="nc" id="L275">        List&lt;Integer&gt; numbers = testCases.stream()</span>
<span class="nc" id="L276">                .map(TestCaseResponse::getSerialNumber)</span>
<span class="nc" id="L277">                .filter(Objects::nonNull)</span>
<span class="nc" id="L278">                .map(s -&gt; {</span>
                    try {
<span class="nc" id="L280">                        return Integer.parseInt(s);</span>
<span class="nc" id="L281">                    } catch (NumberFormatException e) {</span>
<span class="nc" id="L282">                        return null;</span>
                    }
                })
<span class="nc" id="L285">                .filter(Objects::nonNull)</span>
<span class="nc" id="L286">                .sorted()</span>
<span class="nc" id="L287">                .collect(Collectors.toList());</span>
        
<span class="nc bnc" id="L289" title="All 2 branches missed.">        if (numbers.isEmpty()) {</span>
<span class="nc" id="L290">            return &quot;N/A&quot;;</span>
        }
        
<span class="nc" id="L293">        return numbers.get(0) + &quot;-&quot; + numbers.get(numbers.size() - 1);</span>
    }

    private String getCoverageAreas(String category) {
<span class="nc" id="L297">        Map&lt;String, String&gt; coverageMap = new HashMap&lt;&gt;();</span>
<span class="nc" id="L298">        coverageMap.put(&quot;No_Access&quot;, &quot;Token validation&quot;);</span>
<span class="nc" id="L299">        coverageMap.put(&quot;READ_Permission&quot;, &quot;Query operations&quot;);</span>
<span class="nc" id="L300">        coverageMap.put(&quot;WRITE_Permission&quot;, &quot;Upload operations&quot;);</span>
<span class="nc" id="L301">        coverageMap.put(&quot;DELETE_Permission&quot;, &quot;Document deletion&quot;);</span>
<span class="nc" id="L302">        coverageMap.put(&quot;ADMIN_Permission&quot;, &quot;Full access&quot;);</span>
<span class="nc" id="L303">        coverageMap.put(&quot;Creator_Privileges&quot;, &quot;Owner rights&quot;);</span>
<span class="nc" id="L304">        coverageMap.put(&quot;Multi_Role&quot;, &quot;Role combinations&quot;);</span>
<span class="nc" id="L305">        coverageMap.put(&quot;Error_Handling&quot;, &quot;Input validation&quot;);</span>
<span class="nc" id="L306">        coverageMap.put(&quot;Storage_Providers&quot;, &quot;Provider operations&quot;);</span>
<span class="nc" id="L307">        coverageMap.put(&quot;Search_Filter&quot;, &quot;Complex queries&quot;);</span>
<span class="nc" id="L308">        coverageMap.put(&quot;Audit_Logs&quot;, &quot;Log access&quot;);</span>
<span class="nc" id="L309">        coverageMap.put(&quot;Security_Validation&quot;, &quot;Security boundaries&quot;);</span>
<span class="nc" id="L310">        coverageMap.put(&quot;Performance&quot;, &quot;Load testing&quot;);</span>
<span class="nc" id="L311">        coverageMap.put(&quot;Integration&quot;, &quot;End-to-end&quot;);</span>
<span class="nc" id="L312">        coverageMap.put(&quot;Boundary_Tests&quot;, &quot;System limits&quot;);</span>
        
<span class="nc" id="L314">        return coverageMap.getOrDefault(category, &quot;General testing&quot;);</span>
    }

    private String getDescription(String category) {
<span class="nc" id="L318">        Map&lt;String, String&gt; descriptionMap = new HashMap&lt;&gt;();</span>
<span class="nc" id="L319">        descriptionMap.put(&quot;No_Access&quot;, &quot;No authentication / Invalid tokens&quot;);</span>
<span class="nc" id="L320">        descriptionMap.put(&quot;READ_Permission&quot;, &quot;READ permission allowed/forbidden operations&quot;);</span>
<span class="nc" id="L321">        descriptionMap.put(&quot;WRITE_Permission&quot;, &quot;WRITE permission and version management&quot;);</span>
<span class="nc" id="L322">        descriptionMap.put(&quot;DELETE_Permission&quot;, &quot;DELETE permission and inherited operations&quot;);</span>
<span class="nc" id="L323">        descriptionMap.put(&quot;ADMIN_Permission&quot;, &quot;ADMIN operations and audit logs&quot;);</span>
<span class="nc" id="L324">        descriptionMap.put(&quot;Creator_Privileges&quot;, &quot;Document creator special privileges&quot;);</span>
<span class="nc" id="L325">        descriptionMap.put(&quot;Multi_Role&quot;, &quot;Multiple roles and permission inheritance&quot;);</span>
<span class="nc" id="L326">        descriptionMap.put(&quot;Error_Handling&quot;, &quot;Error scenarios and edge cases&quot;);</span>
<span class="nc" id="L327">        descriptionMap.put(&quot;Storage_Providers&quot;, &quot;LOCAL S3 SharePoint storage tests&quot;);</span>
<span class="nc" id="L328">        descriptionMap.put(&quot;Search_Filter&quot;, &quot;Advanced search and filtering&quot;);</span>
<span class="nc" id="L329">        descriptionMap.put(&quot;Audit_Logs&quot;, &quot;Audit log operations and filtering&quot;);</span>
<span class="nc" id="L330">        descriptionMap.put(&quot;Security_Validation&quot;, &quot;Input limits and security tests&quot;);</span>
<span class="nc" id="L331">        descriptionMap.put(&quot;Performance&quot;, &quot;Large data handling tests&quot;);</span>
<span class="nc" id="L332">        descriptionMap.put(&quot;Integration&quot;, &quot;Complete workflow testing&quot;);</span>
<span class="nc" id="L333">        descriptionMap.put(&quot;Boundary_Tests&quot;, &quot;Edge cases and boundary testing&quot;);</span>
        
<span class="nc" id="L335">        return descriptionMap.getOrDefault(category, &quot;Test case specifications&quot;);</span>
    }

    private boolean containsQuery(TestCaseResponse testCase, String query) {
<span class="nc bnc" id="L339" title="All 4 branches missed.">        if (query == null || query.trim().isEmpty()) {</span>
<span class="nc" id="L340">            return true;</span>
        }
        
<span class="nc" id="L343">        String lowerQuery = query.toLowerCase();</span>
<span class="nc bnc" id="L344" title="All 4 branches missed.">        return (testCase.getSerialNumber() != null &amp;&amp; testCase.getSerialNumber().toLowerCase().contains(lowerQuery)) ||</span>
<span class="nc bnc" id="L345" title="All 4 branches missed.">               (testCase.getCategory() != null &amp;&amp; testCase.getCategory().toLowerCase().contains(lowerQuery)) ||</span>
<span class="nc bnc" id="L346" title="All 4 branches missed.">               (testCase.getComment() != null &amp;&amp; testCase.getComment().toLowerCase().contains(lowerQuery)) ||</span>
<span class="nc bnc" id="L347" title="All 4 branches missed.">               (testCase.getRequest() != null &amp;&amp; testCase.getRequest().toLowerCase().contains(lowerQuery)) ||</span>
<span class="nc bnc" id="L348" title="All 4 branches missed.">               (testCase.getResult() != null &amp;&amp; testCase.getResult().toLowerCase().contains(lowerQuery));</span>
    }
}
</pre><div class="footer"><span class="right">Created with <a href="http://www.jacoco.org/jacoco">JaCoCo</a> 0.8.10.202304240956</span></div></body></html>