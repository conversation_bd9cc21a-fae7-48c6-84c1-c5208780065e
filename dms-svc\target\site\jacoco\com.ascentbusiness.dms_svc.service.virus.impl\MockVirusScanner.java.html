<?xml version="1.0" encoding="UTF-8"?><!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Strict//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-strict.dtd"><html xmlns="http://www.w3.org/1999/xhtml" lang="en"><head><meta http-equiv="Content-Type" content="text/html;charset=UTF-8"/><link rel="stylesheet" href="../jacoco-resources/report.css" type="text/css"/><link rel="shortcut icon" href="../jacoco-resources/report.gif" type="image/gif"/><title>MockVirusScanner.java</title><link rel="stylesheet" href="../jacoco-resources/prettify.css" type="text/css"/><script type="text/javascript" src="../jacoco-resources/prettify.js"></script></head><body onload="window['PR_TAB_WIDTH']=4;prettyPrint()"><div class="breadcrumb" id="breadcrumb"><span class="info"><a href="../jacoco-sessions.html" class="el_session">Sessions</a></span><a href="../index.html" class="el_report">dms-svc</a> &gt; <a href="index.source.html" class="el_package">com.ascentbusiness.dms_svc.service.virus.impl</a> &gt; <span class="el_source">MockVirusScanner.java</span></div><h1>MockVirusScanner.java</h1><pre class="source lang-java linenums">package com.ascentbusiness.dms_svc.service.virus.impl;

import com.ascentbusiness.dms_svc.enums.VirusScanResult;
import com.ascentbusiness.dms_svc.enums.VirusScannerType;
import com.ascentbusiness.dms_svc.service.virus.AbstractVirusScanner;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.stereotype.Service;

import java.util.Arrays;
import java.util.List;

/**
 * Mock virus scanner implementation for testing and development environments.
 * 
 * &lt;p&gt;This scanner simulates virus scanning behavior without performing actual
 * antivirus operations. It's designed for testing virus scanning workflows,
 * error handling, and integration testing without requiring actual antivirus
 * software installation.
 * 
 * &lt;p&gt;The mock scanner provides configurable behavior based on file names:
 * &lt;ul&gt;
 *   &lt;li&gt;Files containing &quot;virus&quot; or &quot;infected&quot; in the name are marked as infected&lt;/li&gt;
 *   &lt;li&gt;Files containing &quot;suspicious&quot; in the name are marked as suspicious&lt;/li&gt;
 *   &lt;li&gt;Files containing &quot;error&quot; in the name trigger scan errors&lt;/li&gt;
 *   &lt;li&gt;Files containing &quot;timeout&quot; in the name simulate timeout scenarios&lt;/li&gt;
 *   &lt;li&gt;All other files are marked as clean&lt;/li&gt;
 * &lt;/ul&gt;
 * 
 * &lt;p&gt;This scanner is only enabled when the property 'dms.virus-scanner.mock.enabled'
 * is set to true, ensuring it's not accidentally used in production environments.
 * 
 * <AUTHOR> Development Team
 * @since 1.0.0
 */
@Service
@ConditionalOnProperty(name = &quot;dms.virus-scanner.mock.enabled&quot;, havingValue = &quot;true&quot;, matchIfMissing = false)
<span class="fc" id="L37">public class MockVirusScanner extends AbstractVirusScanner {</span>
    
    private static final String SCANNER_VERSION = &quot;Mock Scanner v1.0.0&quot;;
    private static final String DEFINITIONS_VERSION = &quot;Mock Definitions 2024.01.01&quot;;
    
    // Test patterns for different scenarios
<span class="fc" id="L43">    private static final String[] VIRUS_PATTERNS = {&quot;virus&quot;, &quot;infected&quot;, &quot;malware&quot;, &quot;trojan&quot;};</span>
<span class="fc" id="L44">    private static final String[] SUSPICIOUS_PATTERNS = {&quot;suspicious&quot;, &quot;suspect&quot;, &quot;questionable&quot;};</span>
<span class="fc" id="L45">    private static final String[] ERROR_PATTERNS = {&quot;error&quot;, &quot;fail&quot;, &quot;corrupt&quot;};</span>
<span class="fc" id="L46">    private static final String[] TIMEOUT_PATTERNS = {&quot;timeout&quot;, &quot;slow&quot;, &quot;hang&quot;};</span>
    
<span class="fc" id="L48">    private List&lt;String&gt; lastDetectedThreats = List.of();</span>
    
    @Override
    public VirusScannerType getScannerType() {
<span class="fc" id="L52">        return VirusScannerType.MOCK;</span>
    }
    
    @Override
    public boolean isAvailable() {
        // Mock scanner is always available when enabled
<span class="fc" id="L58">        return true;</span>
    }
    
    @Override
    protected VirusScanResult performScan(byte[] fileContent, String fileName, String scanId) throws Exception {
<span class="nc" id="L63">        logger.debug(&quot;Mock scanner performing scan for file: {} (scan ID: {})&quot;, fileName, scanId);</span>
        
        // Simulate some processing time
<span class="nc" id="L66">        Thread.sleep(100);</span>
        
<span class="nc" id="L68">        String lowerFileName = fileName.toLowerCase();</span>
        
        // Check for timeout simulation
<span class="nc bnc" id="L71" title="All 2 branches missed.">        if (containsAny(lowerFileName, TIMEOUT_PATTERNS)) {</span>
<span class="nc" id="L72">            logger.debug(&quot;Mock scanner simulating timeout for file: {}&quot;, fileName);</span>
<span class="nc" id="L73">            throw new RuntimeException(&quot;Simulated scan timeout&quot;);</span>
        }
        
        // Check for error simulation
<span class="nc bnc" id="L77" title="All 2 branches missed.">        if (containsAny(lowerFileName, ERROR_PATTERNS)) {</span>
<span class="nc" id="L78">            logger.debug(&quot;Mock scanner simulating error for file: {}&quot;, fileName);</span>
<span class="nc" id="L79">            throw new RuntimeException(&quot;Simulated scan error&quot;);</span>
        }
        
        // Check for virus simulation
<span class="nc bnc" id="L83" title="All 2 branches missed.">        if (containsAny(lowerFileName, VIRUS_PATTERNS)) {</span>
<span class="nc" id="L84">            logger.debug(&quot;Mock scanner simulating virus detection for file: {}&quot;, fileName);</span>
<span class="nc" id="L85">            lastDetectedThreats = Arrays.asList(&quot;Mock.Virus.Test&quot;, &quot;Trojan.Mock.Example&quot;);</span>
<span class="nc" id="L86">            return VirusScanResult.INFECTED;</span>
        }
        
        // Check for suspicious file simulation
<span class="nc bnc" id="L90" title="All 2 branches missed.">        if (containsAny(lowerFileName, SUSPICIOUS_PATTERNS)) {</span>
<span class="nc" id="L91">            logger.debug(&quot;Mock scanner simulating suspicious file for: {}&quot;, fileName);</span>
<span class="nc" id="L92">            lastDetectedThreats = Arrays.asList(&quot;Suspicious.Behavior.Mock&quot;);</span>
<span class="nc" id="L93">            return VirusScanResult.SUSPICIOUS;</span>
        }
        
        // Default to clean
<span class="nc" id="L97">        logger.debug(&quot;Mock scanner marking file as clean: {}&quot;, fileName);</span>
<span class="nc" id="L98">        lastDetectedThreats = List.of();</span>
<span class="nc" id="L99">        return VirusScanResult.CLEAN;</span>
    }
    
    @Override
    protected List&lt;String&gt; getDetectedThreats() {
<span class="nc" id="L104">        return lastDetectedThreats;</span>
    }
    
    @Override
    protected String getScannerSpecificInfo() {
<span class="nc" id="L109">        return String.format(&quot;Scanner: %s, Definitions: %s, Mode: Development/Testing&quot;, </span>
                           SCANNER_VERSION, DEFINITIONS_VERSION);
    }
    
    @Override
    public String getScannerInfo() {
<span class="nc" id="L115">        return SCANNER_VERSION;</span>
    }
    
    @Override
    public long getMaxFileSize() {
        // Mock scanner has no real file size limits
<span class="nc" id="L121">        return Long.MAX_VALUE;</span>
    }
    
    @Override
    public long getScanTimeoutMs() {
        // Short timeout for testing
<span class="nc" id="L127">        return 5000; // 5 seconds</span>
    }
    
    /**
     * Checks if the given text contains any of the specified patterns.
     * 
     * @param text the text to check
     * @param patterns the patterns to look for
     * @return true if any pattern is found, false otherwise
     */
    private boolean containsAny(String text, String[] patterns) {
<span class="nc bnc" id="L138" title="All 2 branches missed.">        for (String pattern : patterns) {</span>
<span class="nc bnc" id="L139" title="All 2 branches missed.">            if (text.contains(pattern)) {</span>
<span class="nc" id="L140">                return true;</span>
            }
        }
<span class="nc" id="L143">        return false;</span>
    }
    
    /**
     * Simulates a specific scan result for testing purposes.
     * 
     * &lt;p&gt;This method allows tests to force specific scan results regardless
     * of the file name patterns. It's useful for comprehensive testing of
     * different scan scenarios.
     * 
     * @param fileName the file name to scan
     * @param forcedResult the result to return
     * @param threats the threats to report (if any)
     * @return the forced scan result
     */
    public VirusScanResult simulateScanResult(String fileName, VirusScanResult forcedResult, 
                                            List&lt;String&gt; threats) {
<span class="nc" id="L160">        logger.debug(&quot;Mock scanner forcing result {} for file: {}&quot;, forcedResult, fileName);</span>
<span class="nc bnc" id="L161" title="All 2 branches missed.">        lastDetectedThreats = threats != null ? threats : List.of();</span>
<span class="nc" id="L162">        return forcedResult;</span>
    }
    
    /**
     * Resets the mock scanner state.
     * 
     * &lt;p&gt;This method clears any cached threat information and resets the
     * scanner to its initial state. Useful for test cleanup.
     */
    public void reset() {
<span class="nc" id="L172">        lastDetectedThreats = List.of();</span>
<span class="nc" id="L173">        logger.debug(&quot;Mock scanner state reset&quot;);</span>
<span class="nc" id="L174">    }</span>
}
</pre><div class="footer"><span class="right">Created with <a href="http://www.jacoco.org/jacoco">JaCoCo</a> 0.8.10.202304240956</span></div></body></html>