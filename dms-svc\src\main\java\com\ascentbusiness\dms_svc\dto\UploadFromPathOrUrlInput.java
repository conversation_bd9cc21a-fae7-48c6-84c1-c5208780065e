/*
 * Copyright (c) 2024 Ascent Business Technology
 * All rights reserved.
 */

package com.ascentbusiness.dms_svc.dto;

import com.ascentbusiness.dms_svc.enums.StorageProvider;
import lombok.Data;

import java.nio.file.Files;
import java.nio.file.Paths;
import java.util.List;

/**
 * Input DTO for document upload from various sources (file paths, URLs, network paths).
 * 
 * <p>This DTO supports multiple source types:
 * <ul>
 *   <li>Local file paths (for development environments)</li>
 *   <li>HTTP/HTTPS URLs (for remote file downloads)</li>
 *   <li>Network paths (for shared storage scenarios)</li>
 *   <li>File URIs (file:// protocol)</li>
 * </ul>
 * 
 * <p>The implementation automatically detects the source type and handles
 * the upload accordingly, providing a unified interface for different
 * deployment scenarios.
 * 
 * <AUTHOR> Development Team
 * @version 1.0.0
 * @since 1.0.0
 */
@Data
public class UploadFromPathOrUrlInput {
    
    /**
     * Source path or URL for the file to upload.
     * Can be:
     * - Local file path: C:\Users\<USER>\file.pdf
     * - HTTP URL: https://example.com/files/document.pdf
     * - Network path: \\\\shared-server\\uploads\\file.pdf
     * - File URI: file:///path/to/file.pdf
     */
    private String sourcePath;
    
    /**
     * Display name for the document (optional).
     * If not provided, will be extracted from the source path/URL.
     */
    private String name;
    
    /**
     * Description of the document (optional).
     */
    private String description;
    
    /**
     * Storage provider to use for storing the document (optional).
     * Defaults to application configuration if not specified.
     */
    private StorageProvider storageProvider;
    
    /**
     * Keywords/tags for the document (optional).
     */
    private List<String> keywords;
    
    /**
     * Access roles for the document (optional).
     */
    private List<DocumentAccessRoleInput> accessRoles;
    
    /**
     * Whether to allow duplicate documents (optional).
     * Defaults to false.
     */
    private Boolean allowDuplicates = false;
    
    /**
     * Timeout for URL downloads in milliseconds (optional).
     * Uses system default if not specified.
     */
    private Integer downloadTimeoutMs;
    
    /**
     * Maximum file size to download from URLs in bytes (optional).
     * Uses system default if not specified.
     */
    private Long maxDownloadSizeBytes;
    
    // Helper methods for source type detection
    
    /**
     * Check if the source is an HTTP/HTTPS URL.
     * 
     * @return true if source is a web URL
     */
    public boolean isUrl() {
        return sourcePath != null && 
               (sourcePath.startsWith("http://") || sourcePath.startsWith("https://"));
    }
    
    /**
     * Check if the source is a network path (UNC path).
     * 
     * @return true if source is a network path
     */
    public boolean isNetworkPath() {
        return sourcePath != null && 
               (sourcePath.startsWith("\\\\") || sourcePath.startsWith("//"));
    }
    
    /**
     * Check if the source is a file URI.
     * 
     * @return true if source is a file URI
     */
    public boolean isFileUri() {
        return sourcePath != null && sourcePath.startsWith("file://");
    }
    
    /**
     * Check if the source is a local file path that exists.
     * 
     * @return true if source is an existing local file path
     */
    public boolean isLocalPath() {
        if (sourcePath == null) {
            return false;
        }
        try {
            return Files.exists(Paths.get(sourcePath));
        } catch (Exception e) {
            return false;
        }
    }
    
    /**
     * Extract filename from the source path or URL.
     * 
     * @return extracted filename or null if cannot be determined
     */
    public String extractFileName() {
        if (sourcePath == null) {
            return null;
        }
        
        try {
            if (isUrl()) {
                // Extract filename from URL
                String path = new java.net.URL(sourcePath).getPath();
                return path.substring(path.lastIndexOf('/') + 1);
            } else if (isFileUri()) {
                // Extract filename from file URI
                String path = sourcePath.substring("file://".length());
                return Paths.get(path).getFileName().toString();
            } else {
                // Extract filename from regular path
                return Paths.get(sourcePath).getFileName().toString();
            }
        } catch (Exception e) {
            // Fallback: extract from the end of the path
            String[] parts = sourcePath.split("[/\\\\]");
            return parts.length > 0 ? parts[parts.length - 1] : null;
        }
    }
    
    /**
     * Get the effective name for the document.
     * Uses provided name or extracts from source path.
     * 
     * @return effective document name
     */
    public String getEffectiveName() {
        return name != null ? name : extractFileName();
    }
    
    /**
     * Validate the input parameters.
     * 
     * @throws IllegalArgumentException if validation fails
     */
    public void validate() {
        if (sourcePath == null || sourcePath.trim().isEmpty()) {
            throw new IllegalArgumentException("Source path is required");
        }
        
        // Basic security validation - check for actual path traversal patterns
        // Allow Windows 8.3 short filenames (which contain ~) but block actual traversal attempts
        String normalizedPath = sourcePath.replace('\\', '/');
        if (normalizedPath.contains("../") || normalizedPath.contains("/..") ||
            normalizedPath.matches(".*\\.\\./?$") || normalizedPath.startsWith("../")) {
            throw new IllegalArgumentException("Path traversal attempts are not allowed");
        }
        
        // Validate URL format if it's a URL
        if (isUrl()) {
            try {
                new java.net.URL(sourcePath);
            } catch (java.net.MalformedURLException e) {
                throw new IllegalArgumentException("Invalid URL format: " + e.getMessage());
            }
        }
    }
}