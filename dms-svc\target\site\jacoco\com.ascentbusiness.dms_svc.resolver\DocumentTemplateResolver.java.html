<?xml version="1.0" encoding="UTF-8"?><!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Strict//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-strict.dtd"><html xmlns="http://www.w3.org/1999/xhtml" lang="en"><head><meta http-equiv="Content-Type" content="text/html;charset=UTF-8"/><link rel="stylesheet" href="../jacoco-resources/report.css" type="text/css"/><link rel="shortcut icon" href="../jacoco-resources/report.gif" type="image/gif"/><title>DocumentTemplateResolver.java</title><link rel="stylesheet" href="../jacoco-resources/prettify.css" type="text/css"/><script type="text/javascript" src="../jacoco-resources/prettify.js"></script></head><body onload="window['PR_TAB_WIDTH']=4;prettyPrint()"><div class="breadcrumb" id="breadcrumb"><span class="info"><a href="../jacoco-sessions.html" class="el_session">Sessions</a></span><a href="../index.html" class="el_report">dms-svc</a> &gt; <a href="index.source.html" class="el_package">com.ascentbusiness.dms_svc.resolver</a> &gt; <span class="el_source">DocumentTemplateResolver.java</span></div><h1>DocumentTemplateResolver.java</h1><pre class="source lang-java linenums">package com.ascentbusiness.dms_svc.resolver;

import com.ascentbusiness.dms_svc.entity.Document;
import com.ascentbusiness.dms_svc.entity.DocumentTemplate;
import com.ascentbusiness.dms_svc.entity.TemplateField;
import com.ascentbusiness.dms_svc.enums.TemplateType;
import com.ascentbusiness.dms_svc.service.DocumentTemplateService;
import com.ascentbusiness.dms_svc.security.UserContext;
import com.ascentbusiness.dms_svc.dto.TemplateSearchResult;
import com.ascentbusiness.dms_svc.dto.TemplateSearchInput;
import com.ascentbusiness.dms_svc.dto.TemplateSearchFacets;
import com.ascentbusiness.dms_svc.dto.TemplatePreview;
import com.ascentbusiness.dms_svc.dto.TemplatePreviewInput;
import com.ascentbusiness.dms_svc.dto.BulkActionInput;
import com.ascentbusiness.dms_svc.dto.BulkActionResult;
import com.ascentbusiness.dms_svc.dto.TemplateActionResult;
import com.ascentbusiness.dms_svc.dto.FacetCount;
import com.ascentbusiness.dms_svc.dto.TemplateStatistics;
import com.ascentbusiness.dms_svc.dto.TemplateCategoryStats;
import com.ascentbusiness.dms_svc.dto.TemplateTypeStats;
import com.ascentbusiness.dms_svc.dto.TemplateApprovalStats;
import com.ascentbusiness.dms_svc.dto.TemplateUsageStats;
import com.fasterxml.jackson.databind.ObjectMapper;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Sort;
import org.springframework.graphql.data.method.annotation.Argument;
import org.springframework.graphql.data.method.annotation.MutationMapping;
import org.springframework.graphql.data.method.annotation.QueryMapping;
import org.springframework.graphql.data.method.annotation.SchemaMapping;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.stereotype.Controller;

import java.time.OffsetDateTime;
import java.util.ArrayList;
import java.util.Comparator;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * GraphQL resolver for DocumentTemplate operations
 */
@Controller
<span class="fc" id="L48">@RequiredArgsConstructor</span>
<span class="fc" id="L49">@Slf4j</span>
public class DocumentTemplateResolver {

    private final DocumentTemplateService documentTemplateService;
    private final UserContext userContext;

    // ===== QUERIES =====

    @QueryMapping
    @PreAuthorize(&quot;hasAnyRole('ADMIN', 'MANAGER', 'USER')&quot;)
    public DocumentTemplate getDocumentTemplate(@Argument Long id) {
<span class="nc" id="L60">        log.info(&quot;Getting document template with ID: {}&quot;, id);</span>
<span class="nc" id="L61">        return documentTemplateService.getTemplateById(id);</span>
    }

    @QueryMapping
    @PreAuthorize(&quot;hasAnyRole('ADMIN', 'MANAGER', 'USER')&quot;)
    public DocumentTemplatePage getDocumentTemplates(@Argument TemplatePaginationInput pagination) {
<span class="nc" id="L67">        log.info(&quot;Getting document templates with pagination: {}&quot;, pagination);</span>
        
<span class="nc" id="L69">        Pageable pageable = createPageable(pagination);</span>
<span class="nc" id="L70">        Page&lt;DocumentTemplate&gt; page = documentTemplateService.getTemplates(pageable);</span>
        
<span class="nc" id="L72">        return DocumentTemplatePage.builder()</span>
<span class="nc" id="L73">                .content(page.getContent())</span>
<span class="nc" id="L74">                .totalElements((int) page.getTotalElements())</span>
<span class="nc" id="L75">                .totalPages(page.getTotalPages())</span>
<span class="nc" id="L76">                .size(page.getSize())</span>
<span class="nc" id="L77">                .number(page.getNumber())</span>
<span class="nc" id="L78">                .first(page.isFirst())</span>
<span class="nc" id="L79">                .last(page.isLast())</span>
<span class="nc" id="L80">                .build();</span>
    }

    @QueryMapping
    @PreAuthorize(&quot;hasAnyRole('ADMIN', 'MANAGER', 'USER')&quot;)
    public List&lt;DocumentTemplate&gt; getAccessibleTemplates(@Argument String userId, @Argument String department) {
<span class="nc" id="L86">        log.info(&quot;Getting accessible templates for user: {} in department: {}&quot;, userId, department);</span>
<span class="nc bnc" id="L87" title="All 2 branches missed.">        String actualUserId = userId != null ? userId : userContext.getUserId();</span>
<span class="nc bnc" id="L88" title="All 2 branches missed.">        String actualDepartment = department != null ? department : userContext.getDepartment();</span>
<span class="nc" id="L89">        return documentTemplateService.getAccessibleTemplates(actualUserId, actualDepartment);</span>
    }

    @QueryMapping
    @PreAuthorize(&quot;hasAnyRole('ADMIN', 'MANAGER', 'USER')&quot;)
    public List&lt;DocumentTemplate&gt; getTemplatesByCategory(@Argument String category) {
<span class="nc" id="L95">        log.info(&quot;Getting templates by category: {}&quot;, category);</span>
<span class="nc" id="L96">        return documentTemplateService.getTemplatesByCategory(category);</span>
    }

    @QueryMapping
    @PreAuthorize(&quot;hasAnyRole('ADMIN', 'MANAGER', 'USER')&quot;)
    public List&lt;DocumentTemplate&gt; getTemplatesByType(@Argument TemplateType templateType) {
<span class="nc" id="L102">        log.info(&quot;Getting templates by type: {}&quot;, templateType);</span>
<span class="nc" id="L103">        return documentTemplateService.getTemplatesByType(templateType);</span>
    }

    @QueryMapping
    @PreAuthorize(&quot;hasAnyRole('ADMIN', 'MANAGER', 'USER')&quot;)
    public List&lt;DocumentTemplate&gt; searchTemplatesByName(@Argument String name) {
<span class="nc" id="L109">        log.info(&quot;Searching templates with name: {}&quot;, name);</span>
<span class="nc" id="L110">        return documentTemplateService.searchTemplates(name);</span>
    }

    @QueryMapping
    @PreAuthorize(&quot;hasAnyRole('ADMIN', 'MANAGER', 'USER')&quot;)
    public DocumentTemplatePage getTemplatesByOwner(@Argument String ownerUserId, @Argument TemplatePaginationInput pagination) {
<span class="nc" id="L116">        log.info(&quot;Getting templates by owner: {}&quot;, ownerUserId);</span>
<span class="nc bnc" id="L117" title="All 2 branches missed.">        String actualOwner = ownerUserId != null ? ownerUserId : userContext.getUserId();</span>
<span class="nc" id="L118">        Pageable pageable = createPageable(pagination);</span>
<span class="nc" id="L119">        Page&lt;DocumentTemplate&gt; page = documentTemplateService.getTemplatesByOwner(actualOwner, pageable);</span>
        
<span class="nc" id="L121">        return DocumentTemplatePage.builder()</span>
<span class="nc" id="L122">                .content(page.getContent())</span>
<span class="nc" id="L123">                .totalElements((int) page.getTotalElements())</span>
<span class="nc" id="L124">                .totalPages(page.getTotalPages())</span>
<span class="nc" id="L125">                .size(page.getSize())</span>
<span class="nc" id="L126">                .number(page.getNumber())</span>
<span class="nc" id="L127">                .first(page.isFirst())</span>
<span class="nc" id="L128">                .last(page.isLast())</span>
<span class="nc" id="L129">                .build();</span>
    }

    @QueryMapping
    @PreAuthorize(&quot;hasAnyRole('ADMIN', 'MANAGER', 'USER')&quot;)
    public List&lt;DocumentTemplate&gt; getPopularTemplates(@Argument Integer minUsageCount) {
<span class="nc" id="L135">        log.info(&quot;Getting popular templates with min usage: {}&quot;, minUsageCount);</span>
<span class="nc bnc" id="L136" title="All 2 branches missed.">        int actualMinUsage = minUsageCount != null ? minUsageCount : 5;</span>
<span class="nc" id="L137">        return documentTemplateService.getPopularTemplates(actualMinUsage);</span>
    }

    @QueryMapping
    @PreAuthorize(&quot;hasAnyRole('ADMIN', 'MANAGER', 'USER')&quot;)
    public List&lt;DocumentTemplate&gt; getRecentlyUsedTemplates(@Argument Integer daysBack) {
<span class="nc" id="L143">        log.info(&quot;Getting recently used templates for {} days back&quot;, daysBack);</span>
<span class="nc bnc" id="L144" title="All 2 branches missed.">        int actualDaysBack = daysBack != null ? daysBack : 30;</span>
<span class="nc" id="L145">        return documentTemplateService.getRecentlyUsedTemplates(actualDaysBack);</span>
    }

    @QueryMapping
    @PreAuthorize(&quot;hasAnyRole('ADMIN', 'MANAGER', 'USER')&quot;)
    public List&lt;DocumentTemplate&gt; getPublishedTemplates() {
<span class="nc" id="L151">        log.info(&quot;Getting published templates&quot;);</span>
<span class="nc" id="L152">        return documentTemplateService.getPublishedTemplates();</span>
    }

    @QueryMapping
    @PreAuthorize(&quot;hasAnyRole('ADMIN', 'MANAGER')&quot;)
    public List&lt;DocumentTemplate&gt; getTemplatesPendingApproval() {
<span class="nc" id="L158">        log.info(&quot;Getting templates pending approval&quot;);</span>
<span class="nc" id="L159">        return documentTemplateService.getTemplatesPendingApproval();</span>
    }

    @QueryMapping
    @PreAuthorize(&quot;hasAnyRole('ADMIN', 'MANAGER', 'USER')&quot;)
    public TemplateStatistics getTemplateStatistics(
            @Argument java.time.OffsetDateTime dateFrom,
            @Argument java.time.OffsetDateTime dateTo,
            @Argument List&lt;String&gt; categories) {
<span class="nc" id="L168">        log.info(&quot;Getting template statistics with filters - dateFrom: {}, dateTo: {}, categories: {}&quot;, dateFrom, dateTo, categories);</span>

        // Get service statistics
<span class="nc" id="L171">        DocumentTemplateService.TemplateStatistics serviceStats = documentTemplateService.getTemplateStatistics();</span>

        // Convert to GraphQL DTO
<span class="nc" id="L174">        return convertToTemplateStatistics(serviceStats);</span>
    }

    @QueryMapping
    @PreAuthorize(&quot;hasAnyRole('ADMIN', 'MANAGER', 'USER')&quot;)
    public TemplateSearchResult searchTemplates(@Argument TemplateSearchInput input) {
<span class="nc" id="L180">        log.info(&quot;Searching templates with input: {}&quot;, input);</span>

        // Use the existing search method with query parameter
<span class="nc bnc" id="L183" title="All 2 branches missed.">        String query = input.getQuery() != null ? input.getQuery() : &quot;&quot;;</span>
<span class="nc" id="L184">        List&lt;DocumentTemplate&gt; templates = documentTemplateService.searchTemplates(query);</span>

        // Filter by categories if provided
<span class="nc bnc" id="L187" title="All 4 branches missed.">        if (input.getCategories() != null &amp;&amp; !input.getCategories().isEmpty()) {</span>
<span class="nc" id="L188">            templates = templates.stream()</span>
<span class="nc" id="L189">                    .filter(t -&gt; input.getCategories().contains(t.getCategory()))</span>
<span class="nc" id="L190">                    .collect(Collectors.toList());</span>
        }

        // Filter by template types if provided
<span class="nc bnc" id="L194" title="All 4 branches missed.">        if (input.getTemplateTypes() != null &amp;&amp; !input.getTemplateTypes().isEmpty()) {</span>
<span class="nc" id="L195">            templates = templates.stream()</span>
<span class="nc bnc" id="L196" title="All 2 branches missed.">                    .filter(t -&gt; t.getTemplateType() != null &amp;&amp;</span>
<span class="nc bnc" id="L197" title="All 2 branches missed.">                                input.getTemplateTypes().contains(t.getTemplateType().toString()))</span>
<span class="nc" id="L198">                    .collect(Collectors.toList());</span>
        }

        // Filter by approval statuses if provided
<span class="nc bnc" id="L202" title="All 4 branches missed.">        if (input.getApprovalStatuses() != null &amp;&amp; !input.getApprovalStatuses().isEmpty()) {</span>
<span class="nc" id="L203">            templates = templates.stream()</span>
<span class="nc bnc" id="L204" title="All 2 branches missed.">                    .filter(t -&gt; t.getApprovalStatus() != null &amp;&amp;</span>
<span class="nc bnc" id="L205" title="All 2 branches missed.">                                input.getApprovalStatuses().contains(t.getApprovalStatus().toString()))</span>
<span class="nc" id="L206">                    .collect(Collectors.toList());</span>
        }

        // Filter by access levels if provided
<span class="nc bnc" id="L210" title="All 4 branches missed.">        if (input.getAccessLevels() != null &amp;&amp; !input.getAccessLevels().isEmpty()) {</span>
<span class="nc" id="L211">            templates = templates.stream()</span>
<span class="nc bnc" id="L212" title="All 2 branches missed.">                    .filter(t -&gt; t.getAccessLevel() != null &amp;&amp;</span>
<span class="nc bnc" id="L213" title="All 2 branches missed.">                                input.getAccessLevels().contains(t.getAccessLevel().toString()))</span>
<span class="nc" id="L214">                    .collect(Collectors.toList());</span>
        }

        // Filter by active status if provided
<span class="nc bnc" id="L218" title="All 2 branches missed.">        if (input.getIsActive() != null) {</span>
<span class="nc" id="L219">            templates = templates.stream()</span>
<span class="nc" id="L220">                    .filter(t -&gt; input.getIsActive().equals(t.getIsActive()))</span>
<span class="nc" id="L221">                    .collect(Collectors.toList());</span>
        }

        // Apply sorting if provided
<span class="nc bnc" id="L225" title="All 2 branches missed.">        if (input.getSortBy() != null) {</span>
<span class="nc" id="L226">            Comparator&lt;DocumentTemplate&gt; comparator = getTemplateComparator(input.getSortBy());</span>
<span class="nc bnc" id="L227" title="All 2 branches missed.">            if (&quot;ASC&quot;.equalsIgnoreCase(input.getSortDirection())) {</span>
<span class="nc" id="L228">                templates = templates.stream().sorted(comparator).collect(Collectors.toList());</span>
            } else {
<span class="nc" id="L230">                templates = templates.stream().sorted(comparator.reversed()).collect(Collectors.toList());</span>
            }
        }

        // Build facets from the filtered results
<span class="nc" id="L235">        TemplateSearchFacets facets = buildSearchFacets(templates);</span>

<span class="nc" id="L237">        return TemplateSearchResult.builder()</span>
<span class="nc" id="L238">                .templates(templates)</span>
<span class="nc" id="L239">                .totalCount(templates.size())</span>
<span class="nc" id="L240">                .facets(facets)</span>
<span class="nc" id="L241">                .suggestions(List.of()) // TODO: Implement suggestions if needed</span>
<span class="nc" id="L242">                .build();</span>
    }

    private Comparator&lt;DocumentTemplate&gt; getTemplateComparator(String sortBy) {
<span class="nc bnc" id="L246" title="All 4 branches missed.">        switch (sortBy.toUpperCase()) {</span>
            case &quot;NAME&quot;:
<span class="nc" id="L248">                return Comparator.comparing(DocumentTemplate::getName, Comparator.nullsLast(String::compareToIgnoreCase));</span>
            case &quot;CREATED_DATE&quot;:
<span class="nc" id="L250">                return Comparator.comparing(DocumentTemplate::getCreatedDateTime, Comparator.nullsLast(OffsetDateTime::compareTo));</span>
            case &quot;LAST_MODIFIED_DATE&quot;:
            default:
<span class="nc" id="L253">                return Comparator.comparing(DocumentTemplate::getLastModifiedDateTime, Comparator.nullsLast(OffsetDateTime::compareTo));</span>
            case &quot;CATEGORY&quot;:
<span class="nc" id="L255">                return Comparator.comparing(DocumentTemplate::getCategory, Comparator.nullsLast(String::compareToIgnoreCase));</span>
        }
    }

    private TemplateSearchFacets buildSearchFacets(List&lt;DocumentTemplate&gt; templates) {
        // Build category facets
<span class="nc" id="L261">        Map&lt;String, Long&gt; categoryCount = templates.stream()</span>
<span class="nc bnc" id="L262" title="All 2 branches missed.">                .filter(t -&gt; t.getCategory() != null)</span>
<span class="nc" id="L263">                .collect(Collectors.groupingBy(DocumentTemplate::getCategory, Collectors.counting()));</span>

<span class="nc" id="L265">        List&lt;FacetCount&gt; categories = categoryCount.entrySet().stream()</span>
<span class="nc" id="L266">                .map(entry -&gt; FacetCount.builder()</span>
<span class="nc" id="L267">                        .value(entry.getKey())</span>
<span class="nc" id="L268">                        .count(entry.getValue())</span>
<span class="nc" id="L269">                        .build())</span>
<span class="nc" id="L270">                .collect(Collectors.toList());</span>

        // Build template type facets
<span class="nc" id="L273">        Map&lt;String, Long&gt; typeCount = templates.stream()</span>
<span class="nc bnc" id="L274" title="All 2 branches missed.">                .filter(t -&gt; t.getTemplateType() != null)</span>
<span class="nc" id="L275">                .collect(Collectors.groupingBy(t -&gt; t.getTemplateType().toString(), Collectors.counting()));</span>

<span class="nc" id="L277">        List&lt;FacetCount&gt; templateTypes = typeCount.entrySet().stream()</span>
<span class="nc" id="L278">                .map(entry -&gt; FacetCount.builder()</span>
<span class="nc" id="L279">                        .value(entry.getKey())</span>
<span class="nc" id="L280">                        .count(entry.getValue())</span>
<span class="nc" id="L281">                        .build())</span>
<span class="nc" id="L282">                .collect(Collectors.toList());</span>

        // Build approval status facets
<span class="nc" id="L285">        Map&lt;String, Long&gt; approvalCount = templates.stream()</span>
<span class="nc bnc" id="L286" title="All 2 branches missed.">                .filter(t -&gt; t.getApprovalStatus() != null)</span>
<span class="nc" id="L287">                .collect(Collectors.groupingBy(t -&gt; t.getApprovalStatus().toString(), Collectors.counting()));</span>

<span class="nc" id="L289">        List&lt;FacetCount&gt; approvalStatuses = approvalCount.entrySet().stream()</span>
<span class="nc" id="L290">                .map(entry -&gt; FacetCount.builder()</span>
<span class="nc" id="L291">                        .value(entry.getKey())</span>
<span class="nc" id="L292">                        .count(entry.getValue())</span>
<span class="nc" id="L293">                        .build())</span>
<span class="nc" id="L294">                .collect(Collectors.toList());</span>

<span class="nc" id="L296">        return TemplateSearchFacets.builder()</span>
<span class="nc" id="L297">                .categories(categories)</span>
<span class="nc" id="L298">                .templateTypes(templateTypes)</span>
<span class="nc" id="L299">                .approvalStatuses(approvalStatuses)</span>
<span class="nc" id="L300">                .accessLevels(List.of()) // TODO: Implement if needed</span>
<span class="nc" id="L301">                .owners(List.of()) // TODO: Implement if needed</span>
<span class="nc" id="L302">                .build();</span>
    }

    private TemplateStatistics convertToTemplateStatistics(DocumentTemplateService.TemplateStatistics serviceStats) {
        // Convert category statistics
<span class="nc" id="L307">        List&lt;TemplateCategoryStats&gt; categoryStats = serviceStats.getCategoryStatistics().stream()</span>
<span class="nc" id="L308">                .map(stat -&gt; TemplateCategoryStats.builder()</span>
<span class="nc" id="L309">                        .category((String) stat[0])</span>
<span class="nc" id="L310">                        .count(((Number) stat[1]).longValue())</span>
<span class="nc" id="L311">                        .percentage(0.0f) // TODO: Calculate percentage</span>
<span class="nc" id="L312">                        .averageUsage(0.0f) // TODO: Calculate average usage</span>
<span class="nc" id="L313">                        .build())</span>
<span class="nc" id="L314">                .collect(Collectors.toList());</span>

        // Convert type statistics
<span class="nc" id="L317">        List&lt;TemplateTypeStats&gt; typeStats = serviceStats.getTypeStatistics().stream()</span>
<span class="nc" id="L318">                .map(stat -&gt; TemplateTypeStats.builder()</span>
<span class="nc" id="L319">                        .templateType((com.ascentbusiness.dms_svc.enums.TemplateType) stat[0])</span>
<span class="nc" id="L320">                        .count(((Number) stat[1]).longValue())</span>
<span class="nc" id="L321">                        .percentage(0.0f) // TODO: Calculate percentage</span>
<span class="nc" id="L322">                        .averageUsage(0.0f) // TODO: Calculate average usage</span>
<span class="nc" id="L323">                        .build())</span>
<span class="nc" id="L324">                .collect(Collectors.toList());</span>

        // Convert approval statistics
<span class="nc" id="L327">        List&lt;TemplateApprovalStats&gt; approvalStats = serviceStats.getApprovalStatistics().stream()</span>
<span class="nc" id="L328">                .map(stat -&gt; TemplateApprovalStats.builder()</span>
<span class="nc" id="L329">                        .approvalStatus((com.ascentbusiness.dms_svc.enums.TemplateApprovalStatus) stat[0])</span>
<span class="nc" id="L330">                        .count(((Number) stat[1]).longValue())</span>
<span class="nc" id="L331">                        .percentage(0.0f) // TODO: Calculate percentage</span>
<span class="nc" id="L332">                        .build())</span>
<span class="nc" id="L333">                .collect(Collectors.toList());</span>

        // Calculate totals
<span class="nc" id="L336">        long totalTemplates = serviceStats.getTotalActive() + serviceStats.getTotalDraft() + serviceStats.getTotalPending();</span>

<span class="nc" id="L338">        return TemplateStatistics.builder()</span>
<span class="nc" id="L339">                .totalTemplates(totalTemplates)</span>
<span class="nc" id="L340">                .totalActive(serviceStats.getTotalActive())</span>
<span class="nc" id="L341">                .totalDraft(serviceStats.getTotalDraft())</span>
<span class="nc" id="L342">                .totalPending(serviceStats.getTotalPending())</span>
<span class="nc" id="L343">                .totalPublished(serviceStats.getTotalActive()) // Published = Active</span>
<span class="nc" id="L344">                .totalArchived(0L) // TODO: Implement archived count</span>
<span class="nc" id="L345">                .categoryStatistics(categoryStats)</span>
<span class="nc" id="L346">                .typeStatistics(typeStats)</span>
<span class="nc" id="L347">                .approvalStatistics(approvalStats)</span>
<span class="nc" id="L348">                .usageStatistics(List.of()) // TODO: Implement usage statistics</span>
<span class="nc" id="L349">                .topCategories(categoryStats) // Use same as category stats for now</span>
<span class="nc" id="L350">                .mostUsedTemplates(List.of()) // TODO: Implement most used templates</span>
<span class="nc" id="L351">                .recentlyCreated(List.of()) // TODO: Implement recently created</span>
<span class="nc" id="L352">                .pendingApproval(approvalStats.stream()</span>
<span class="nc bnc" id="L353" title="All 2 branches missed.">                        .filter(stat -&gt; stat.getApprovalStatus() == com.ascentbusiness.dms_svc.enums.TemplateApprovalStatus.PENDING_APPROVAL)</span>
<span class="nc" id="L354">                        .collect(Collectors.toList()))</span>
<span class="nc" id="L355">                .build();</span>
    }

    @QueryMapping
    @PreAuthorize(&quot;hasAnyRole('ADMIN', 'MANAGER', 'USER')&quot;)
    public com.ascentbusiness.dms_svc.dto.TemplateValidationStatus validateTemplate(@Argument Long templateId) {
<span class="nc" id="L361">        log.info(&quot;Validating template with ID: {}&quot;, templateId);</span>

        try {
            // Get the template
<span class="nc" id="L365">            DocumentTemplate template = documentTemplateService.getTemplateById(templateId);</span>

            // Perform validation
<span class="nc" id="L368">            boolean isValid = true;</span>
<span class="nc" id="L369">            List&lt;com.ascentbusiness.dms_svc.dto.TemplateValidationError&gt; validationErrors = new ArrayList&lt;&gt;();</span>
<span class="nc" id="L370">            List&lt;String&gt; warnings = new ArrayList&lt;&gt;();</span>
<span class="nc" id="L371">            List&lt;String&gt; recommendations = new ArrayList&lt;&gt;();</span>

            // Basic validation checks
<span class="nc bnc" id="L374" title="All 4 branches missed.">            if (template.getName() == null || template.getName().trim().isEmpty()) {</span>
<span class="nc" id="L375">                isValid = false;</span>
<span class="nc" id="L376">                validationErrors.add(com.ascentbusiness.dms_svc.dto.TemplateValidationError.builder()</span>
<span class="nc" id="L377">                    .code(&quot;TEMPLATE_NAME_REQUIRED&quot;)</span>
<span class="nc" id="L378">                    .message(&quot;Template name is required&quot;)</span>
<span class="nc" id="L379">                    .severity(com.ascentbusiness.dms_svc.enums.ValidationSeverity.ERROR)</span>
<span class="nc" id="L380">                    .field(&quot;name&quot;)</span>
<span class="nc" id="L381">                    .build());</span>
            }

<span class="nc bnc" id="L384" title="All 4 branches missed.">            if (template.getCategory() == null || template.getCategory().trim().isEmpty()) {</span>
<span class="nc" id="L385">                isValid = false;</span>
<span class="nc" id="L386">                validationErrors.add(com.ascentbusiness.dms_svc.dto.TemplateValidationError.builder()</span>
<span class="nc" id="L387">                    .code(&quot;TEMPLATE_CATEGORY_REQUIRED&quot;)</span>
<span class="nc" id="L388">                    .message(&quot;Template category is required&quot;)</span>
<span class="nc" id="L389">                    .severity(com.ascentbusiness.dms_svc.enums.ValidationSeverity.ERROR)</span>
<span class="nc" id="L390">                    .field(&quot;category&quot;)</span>
<span class="nc" id="L391">                    .build());</span>
            }

<span class="nc bnc" id="L394" title="All 2 branches missed.">            if (template.getTemplateType() == null) {</span>
<span class="nc" id="L395">                isValid = false;</span>
<span class="nc" id="L396">                validationErrors.add(com.ascentbusiness.dms_svc.dto.TemplateValidationError.builder()</span>
<span class="nc" id="L397">                    .code(&quot;TEMPLATE_TYPE_REQUIRED&quot;)</span>
<span class="nc" id="L398">                    .message(&quot;Template type is required&quot;)</span>
<span class="nc" id="L399">                    .severity(com.ascentbusiness.dms_svc.enums.ValidationSeverity.ERROR)</span>
<span class="nc" id="L400">                    .field(&quot;templateType&quot;)</span>
<span class="nc" id="L401">                    .build());</span>
            }

            // Add warnings for best practices
<span class="nc bnc" id="L405" title="All 4 branches missed.">            if (template.getDescription() == null || template.getDescription().trim().isEmpty()) {</span>
<span class="nc" id="L406">                warnings.add(&quot;Template description is recommended for better documentation&quot;);</span>
            }

            // Add recommendations
<span class="nc bnc" id="L410" title="All 4 branches missed.">            if (template.getVersion() == null || template.getVersion().equals(&quot;1.0&quot;)) {</span>
<span class="nc" id="L411">                recommendations.add(&quot;Consider versioning your template for better change management&quot;);</span>
            }

<span class="nc bnc" id="L414" title="All 2 branches missed.">            if (template.getAccessLevel() == null) {</span>
<span class="nc" id="L415">                recommendations.add(&quot;Set an appropriate access level for security&quot;);</span>
            }

<span class="nc" id="L418">            return com.ascentbusiness.dms_svc.dto.TemplateValidationStatus.builder()</span>
<span class="nc" id="L419">                .isValid(isValid)</span>
<span class="nc" id="L420">                .lastValidated(OffsetDateTime.now())</span>
<span class="nc" id="L421">                .validatedBy(&quot;system&quot;)</span>
<span class="nc" id="L422">                .validationVersion(&quot;1.0&quot;)</span>
<span class="nc" id="L423">                .validationErrors(validationErrors)</span>
<span class="nc" id="L424">                .warnings(warnings)</span>
<span class="nc" id="L425">                .recommendations(recommendations)</span>
<span class="nc" id="L426">                .validatedAt(OffsetDateTime.now())</span>
<span class="nc" id="L427">                .build();</span>

<span class="nc" id="L429">        } catch (Exception e) {</span>
<span class="nc" id="L430">            log.error(&quot;Error validating template with ID: {}&quot;, templateId, e);</span>

            // Return error validation status
<span class="nc" id="L433">            return com.ascentbusiness.dms_svc.dto.TemplateValidationStatus.builder()</span>
<span class="nc" id="L434">                .isValid(false)</span>
<span class="nc" id="L435">                .lastValidated(OffsetDateTime.now())</span>
<span class="nc" id="L436">                .validatedBy(&quot;system&quot;)</span>
<span class="nc" id="L437">                .validationVersion(&quot;1.0&quot;)</span>
<span class="nc" id="L438">                .validationErrors(List.of(</span>
<span class="nc" id="L439">                    com.ascentbusiness.dms_svc.dto.TemplateValidationError.builder()</span>
<span class="nc" id="L440">                        .code(&quot;VALIDATION_ERROR&quot;)</span>
<span class="nc" id="L441">                        .message(&quot;Error occurred during validation: &quot; + e.getMessage())</span>
<span class="nc" id="L442">                        .severity(com.ascentbusiness.dms_svc.enums.ValidationSeverity.ERROR)</span>
<span class="nc" id="L443">                        .build()</span>
                ))
<span class="nc" id="L445">                .warnings(List.of())</span>
<span class="nc" id="L446">                .recommendations(List.of())</span>
<span class="nc" id="L447">                .validatedAt(OffsetDateTime.now())</span>
<span class="nc" id="L448">                .build();</span>
        }
    }

    @QueryMapping
    @PreAuthorize(&quot;hasAnyRole('ADMIN', 'MANAGER', 'USER')&quot;)
    public Float getTemplateHealth(@Argument Long templateId) {
<span class="nc" id="L455">        log.info(&quot;Getting template health for ID: {}&quot;, templateId);</span>

        try {
            // Get the template
<span class="nc" id="L459">            DocumentTemplate template = documentTemplateService.getTemplateById(templateId);</span>

            // Calculate health score based on various factors
<span class="nc" id="L462">            float healthScore = 100.0f;</span>

            // Deduct points for missing required fields
<span class="nc bnc" id="L465" title="All 4 branches missed.">            if (template.getName() == null || template.getName().trim().isEmpty()) {</span>
<span class="nc" id="L466">                healthScore -= 20.0f;</span>
            }

<span class="nc bnc" id="L469" title="All 4 branches missed.">            if (template.getCategory() == null || template.getCategory().trim().isEmpty()) {</span>
<span class="nc" id="L470">                healthScore -= 15.0f;</span>
            }

<span class="nc bnc" id="L473" title="All 2 branches missed.">            if (template.getTemplateType() == null) {</span>
<span class="nc" id="L474">                healthScore -= 15.0f;</span>
            }

<span class="nc bnc" id="L477" title="All 4 branches missed.">            if (template.getTemplateFormat() == null || template.getTemplateFormat().trim().isEmpty()) {</span>
<span class="nc" id="L478">                healthScore -= 10.0f;</span>
            }

<span class="nc bnc" id="L481" title="All 4 branches missed.">            if (template.getMimeType() == null || template.getMimeType().trim().isEmpty()) {</span>
<span class="nc" id="L482">                healthScore -= 10.0f;</span>
            }

            // Deduct points for missing optional but recommended fields
<span class="nc bnc" id="L486" title="All 4 branches missed.">            if (template.getDescription() == null || template.getDescription().trim().isEmpty()) {</span>
<span class="nc" id="L487">                healthScore -= 5.0f;</span>
            }

<span class="nc bnc" id="L490" title="All 2 branches missed.">            if (template.getAccessLevel() == null) {</span>
<span class="nc" id="L491">                healthScore -= 5.0f;</span>
            }

            // Bonus points for good practices
<span class="nc bnc" id="L495" title="All 4 branches missed.">            if (template.getUsageCount() != null &amp;&amp; template.getUsageCount() &gt; 0) {</span>
<span class="nc" id="L496">                healthScore += 5.0f; // Template is being used</span>
            }

<span class="nc bnc" id="L499" title="All 2 branches missed.">            if (template.getApprovalStatus() != null &amp;&amp;</span>
<span class="nc bnc" id="L500" title="All 2 branches missed.">                template.getApprovalStatus().toString().equals(&quot;APPROVED&quot;)) {</span>
<span class="nc" id="L501">                healthScore += 5.0f; // Template is approved</span>
            }

<span class="nc bnc" id="L504" title="All 4 branches missed.">            if (template.getIsActive() != null &amp;&amp; template.getIsActive()) {</span>
<span class="nc" id="L505">                healthScore += 2.0f; // Template is active</span>
            }

            // Ensure score is between 0 and 100
<span class="nc" id="L509">            healthScore = Math.max(0.0f, Math.min(100.0f, healthScore));</span>

<span class="nc" id="L511">            return healthScore;</span>

<span class="nc" id="L513">        } catch (Exception e) {</span>
<span class="nc" id="L514">            log.error(&quot;Error getting template health for ID: {}&quot;, templateId, e);</span>
<span class="nc" id="L515">            return 0.0f; // Return 0 health score on error</span>
        }
    }

    // ===== MUTATIONS =====

    @MutationMapping
    @PreAuthorize(&quot;hasAnyRole('ADMIN', 'MANAGER', 'USER')&quot;)
    public DocumentTemplate createDocumentTemplate(@Argument DocumentTemplateInput input) {
<span class="nc" id="L524">        log.info(&quot;Creating document template: {}&quot;, input.getName());</span>
        
<span class="nc" id="L526">        DocumentTemplate template = DocumentTemplate.builder()</span>
<span class="nc" id="L527">                .name(input.getName())</span>
<span class="nc" id="L528">                .description(input.getDescription())</span>
<span class="nc" id="L529">                .category(input.getCategory())</span>
<span class="nc" id="L530">                .templateType(input.getTemplateType())</span>
<span class="nc" id="L531">                .templateFormat(input.getTemplateFormat())</span>
<span class="nc" id="L532">                .mimeType(input.getMimeType())</span>
<span class="nc" id="L533">                .accessLevel(input.getAccessLevel())</span>
<span class="nc" id="L534">                .build();</span>
        
<span class="nc bnc" id="L536" title="All 2 branches missed.">        if (input.getTemplateContent() != null) {</span>
<span class="nc" id="L537">            template.setTemplateContentBase64(input.getTemplateContent());</span>
        }
        
<span class="nc" id="L540">        return documentTemplateService.createTemplate(template, userContext.getUserId());</span>
    }

    @MutationMapping
    @PreAuthorize(&quot;hasAnyRole('ADMIN', 'MANAGER', 'USER')&quot;)
    public DocumentTemplate updateDocumentTemplate(@Argument Long id, @Argument DocumentTemplateInput input) {
<span class="nc" id="L546">        log.info(&quot;Updating document template ID: {}&quot;, id);</span>
        
<span class="nc" id="L548">        DocumentTemplate template = DocumentTemplate.builder()</span>
<span class="nc" id="L549">                .name(input.getName())</span>
<span class="nc" id="L550">                .description(input.getDescription())</span>
<span class="nc" id="L551">                .category(input.getCategory())</span>
<span class="nc" id="L552">                .templateType(input.getTemplateType())</span>
<span class="nc" id="L553">                .templateFormat(input.getTemplateFormat())</span>
<span class="nc" id="L554">                .mimeType(input.getMimeType())</span>
<span class="nc" id="L555">                .accessLevel(input.getAccessLevel())</span>
<span class="nc" id="L556">                .build();</span>
        
<span class="nc bnc" id="L558" title="All 2 branches missed.">        if (input.getTemplateContent() != null) {</span>
<span class="nc" id="L559">            template.setTemplateContentBase64(input.getTemplateContent());</span>
        }
        
<span class="nc" id="L562">        return documentTemplateService.updateTemplate(id, template, userContext.getUserId());</span>
    }

    @MutationMapping
    @PreAuthorize(&quot;hasAnyRole('ADMIN', 'MANAGER')&quot;)
    public DocumentTemplate publishTemplate(@Argument Long id) {
<span class="nc" id="L568">        log.info(&quot;Publishing template ID: {}&quot;, id);</span>
<span class="nc" id="L569">        return documentTemplateService.publishTemplate(id, userContext.getUserId());</span>
    }

    @MutationMapping
    @PreAuthorize(&quot;hasAnyRole('ADMIN', 'MANAGER')&quot;)
    public DocumentTemplate approveTemplate(@Argument Long id, @Argument String comments) {
<span class="nc" id="L575">        log.info(&quot;Approving template ID: {} with comments: {}&quot;, id, comments);</span>
<span class="nc" id="L576">        return documentTemplateService.approveTemplate(id, userContext.getUserId());</span>
    }

    @MutationMapping
    @PreAuthorize(&quot;hasAnyRole('ADMIN', 'MANAGER')&quot;)
    public DocumentTemplate rejectTemplate(@Argument Long id) {
<span class="nc" id="L582">        log.info(&quot;Rejecting template ID: {}&quot;, id);</span>
<span class="nc" id="L583">        return documentTemplateService.rejectTemplate(id, userContext.getUserId());</span>
    }

    @MutationMapping
    @PreAuthorize(&quot;hasAnyRole('ADMIN', 'MANAGER', 'USER')&quot;)
    public DocumentTemplate deactivateTemplate(@Argument Long id) {
<span class="nc" id="L589">        log.info(&quot;Deactivating template ID: {}&quot;, id);</span>
<span class="nc" id="L590">        return documentTemplateService.deactivateTemplate(id, userContext.getUserId());</span>
    }



    @MutationMapping
    @PreAuthorize(&quot;hasAnyRole('ADMIN', 'MANAGER', 'USER')&quot;)
    public Document createDocumentFromTemplate(@Argument CreateDocumentFromTemplateInput input) {
<span class="nc" id="L598">        log.info(&quot;Creating document from template ID: {}&quot;, input.getTemplateId());</span>

        // fieldValues is now a Map&lt;String, Object&gt; directly from JSON
<span class="nc" id="L601">        Map&lt;String, Object&gt; fieldValues = input.getFieldValues();</span>

<span class="nc" id="L603">        return documentTemplateService.createDocumentFromTemplate(</span>
<span class="nc" id="L604">                input.getTemplateId(),</span>
                fieldValues,
<span class="nc" id="L606">                input.getDocumentName(),</span>
<span class="nc" id="L607">                userContext.getUserId()</span>
        );
    }

    @MutationMapping
    @PreAuthorize(&quot;hasAnyRole('ADMIN', 'MANAGER', 'USER')&quot;)
    public TemplatePreview previewTemplate(@Argument TemplatePreviewInput input) {
<span class="nc" id="L614">        log.info(&quot;Previewing template ID: {}&quot;, input.getTemplateId());</span>

        // Parse JSON field values
        Map&lt;String, Object&gt; fieldValues;
        try {
<span class="nc" id="L619">            ObjectMapper objectMapper = new ObjectMapper();</span>
<span class="nc" id="L620">            fieldValues = objectMapper.readValue(input.getFieldValues(), Map.class);</span>
<span class="nc" id="L621">        } catch (Exception e) {</span>
<span class="nc" id="L622">            log.error(&quot;Failed to parse field values JSON: {}&quot;, input.getFieldValues(), e);</span>
<span class="nc" id="L623">            throw new IllegalArgumentException(&quot;Invalid field values JSON format&quot;);</span>
<span class="nc" id="L624">        }</span>

<span class="nc" id="L626">        byte[] content = documentTemplateService.previewTemplate(input.getTemplateId(), fieldValues, userContext.getUserId());</span>
<span class="nc" id="L627">        String base64Content = java.util.Base64.getEncoder().encodeToString(content);</span>

<span class="nc" id="L629">        return TemplatePreview.builder()</span>
<span class="nc" id="L630">                .templateId(input.getTemplateId())</span>
<span class="nc" id="L631">                .content(base64Content)</span>
<span class="nc" id="L632">                .mimeType(&quot;application/pdf&quot;) // Default to PDF, could use input.getOutputFormat()</span>
<span class="nc" id="L633">                .fileName(&quot;template_preview.pdf&quot;)</span>
<span class="nc" id="L634">                .fileSize(content.length)</span>
<span class="nc" id="L635">                .previewUrl(&quot;/api/v1/templates/&quot; + input.getTemplateId() + &quot;/preview&quot;) // Mock preview URL</span>
<span class="nc" id="L636">                .generatedAt(OffsetDateTime.now())</span>
<span class="nc" id="L637">                .expiresAt(OffsetDateTime.now().plusHours(24))</span>
<span class="nc" id="L638">                .build();</span>
    }

    @MutationMapping
    @PreAuthorize(&quot;hasAnyRole('ADMIN', 'MANAGER', 'USER')&quot;)
    public DocumentTemplate duplicateTemplate(@Argument Long id, @Argument String newName) {
<span class="nc" id="L644">        log.info(&quot;Duplicating template ID: {} with new name: {}&quot;, id, newName);</span>

<span class="nc" id="L646">        DocumentTemplate originalTemplate = documentTemplateService.getTemplateById(id);</span>

<span class="nc" id="L648">        DocumentTemplate duplicatedTemplate = DocumentTemplate.builder()</span>
<span class="nc" id="L649">                .name(newName)</span>
<span class="nc" id="L650">                .description(originalTemplate.getDescription() + &quot; (Copy)&quot;)</span>
<span class="nc" id="L651">                .category(originalTemplate.getCategory())</span>
<span class="nc" id="L652">                .templateType(originalTemplate.getTemplateType())</span>
<span class="nc" id="L653">                .templateFormat(originalTemplate.getTemplateFormat())</span>
<span class="nc" id="L654">                .mimeType(originalTemplate.getMimeType())</span>
<span class="nc" id="L655">                .accessLevel(originalTemplate.getAccessLevel())</span>
<span class="nc" id="L656">                .templateContent(originalTemplate.getTemplateContent())</span>
<span class="nc" id="L657">                .fileSize(originalTemplate.getFileSize())</span>
<span class="nc" id="L658">                .build();</span>

<span class="nc" id="L660">        return documentTemplateService.createTemplate(duplicatedTemplate, userContext.getUserId());</span>
    }

    @MutationMapping
    @PreAuthorize(&quot;hasRole('ADMIN')&quot;)
    public Boolean deleteDocumentTemplate(@Argument Long id) {
<span class="nc" id="L666">        log.info(&quot;Deleting document template ID: {}&quot;, id);</span>
<span class="nc" id="L667">        documentTemplateService.deleteTemplate(id, userContext.getUserId());</span>
<span class="nc" id="L668">        return true;</span>
    }

    @MutationMapping
    @PreAuthorize(&quot;hasAnyRole('ADMIN', 'MANAGER')&quot;)
    public BulkActionResult bulkTemplateAction(@Argument BulkActionInput input) {
<span class="nc" id="L674">        log.info(&quot;Performing bulk action: {} on {} templates&quot;, input.getAction(), input.getTemplateIds().size());</span>

<span class="nc" id="L676">        long startTime = System.currentTimeMillis();</span>
<span class="nc" id="L677">        int totalProcessed = 0;</span>
<span class="nc" id="L678">        int successCount = 0;</span>
<span class="nc" id="L679">        int failureCount = 0;</span>
<span class="nc" id="L680">        List&lt;TemplateActionResult&gt; results = new ArrayList&lt;&gt;();</span>

<span class="nc bnc" id="L682" title="All 2 branches missed.">        for (Long templateId : input.getTemplateIds()) {</span>
<span class="nc" id="L683">            String templateName = &quot;Unknown&quot;;</span>
            try {
                // Get template name for result
<span class="nc" id="L686">                DocumentTemplate template = documentTemplateService.getTemplateById(templateId);</span>
<span class="nc" id="L687">                templateName = template.getName();</span>

<span class="nc bnc" id="L689" title="All 5 branches missed.">                switch (input.getAction().toUpperCase()) {</span>
                    case &quot;DELETE&quot;:
<span class="nc" id="L691">                        documentTemplateService.deleteTemplate(templateId, userContext.getUserId());</span>
<span class="nc" id="L692">                        break;</span>
                    case &quot;PUBLISH&quot;:
<span class="nc" id="L694">                        documentTemplateService.publishTemplate(templateId, userContext.getUserId());</span>
<span class="nc" id="L695">                        break;</span>
                    case &quot;APPROVE&quot;:
<span class="nc" id="L697">                        documentTemplateService.approveTemplate(templateId, userContext.getUserId());</span>
<span class="nc" id="L698">                        break;</span>
                    case &quot;DEACTIVATE&quot;:
<span class="nc" id="L700">                        documentTemplateService.deactivateTemplate(templateId, userContext.getUserId());</span>
<span class="nc" id="L701">                        break;</span>
                    default:
<span class="nc" id="L703">                        throw new IllegalArgumentException(&quot;Unsupported action: &quot; + input.getAction());</span>
                }

<span class="nc" id="L706">                results.add(TemplateActionResult.builder()</span>
<span class="nc" id="L707">                        .templateId(templateId)</span>
<span class="nc" id="L708">                        .templateName(templateName)</span>
<span class="nc" id="L709">                        .success(true)</span>
<span class="nc" id="L710">                        .errorMessage(null)</span>
<span class="nc" id="L711">                        .newStatus(input.getAction().toUpperCase())</span>
<span class="nc" id="L712">                        .build());</span>
<span class="nc" id="L713">                successCount++;</span>
<span class="nc" id="L714">            } catch (Exception e) {</span>
<span class="nc" id="L715">                results.add(TemplateActionResult.builder()</span>
<span class="nc" id="L716">                        .templateId(templateId)</span>
<span class="nc" id="L717">                        .templateName(templateName)</span>
<span class="nc" id="L718">                        .success(false)</span>
<span class="nc" id="L719">                        .errorMessage(e.getMessage())</span>
<span class="nc" id="L720">                        .newStatus(null)</span>
<span class="nc" id="L721">                        .build());</span>
<span class="nc" id="L722">                failureCount++;</span>
<span class="nc" id="L723">                log.error(&quot;Error processing template {} with action {}: {}&quot;, templateId, input.getAction(), e.getMessage());</span>
<span class="nc" id="L724">            }</span>
<span class="nc" id="L725">            totalProcessed++;</span>
<span class="nc" id="L726">        }</span>

<span class="nc" id="L728">        long processingTime = System.currentTimeMillis() - startTime;</span>
<span class="nc bnc" id="L729" title="All 4 branches missed.">        String overallStatus = failureCount == 0 ? &quot;SUCCESS&quot; : (successCount == 0 ? &quot;FAILED&quot; : &quot;PARTIAL_SUCCESS&quot;);</span>

<span class="nc" id="L731">        return BulkActionResult.builder()</span>
<span class="nc" id="L732">                .totalProcessed(totalProcessed)</span>
<span class="nc" id="L733">                .successfulActions(successCount)</span>
<span class="nc" id="L734">                .failedActions(failureCount)</span>
<span class="nc" id="L735">                .results(results)</span>
<span class="nc" id="L736">                .processingTimeMs(processingTime)</span>
<span class="nc" id="L737">                .overallStatus(overallStatus)</span>
<span class="nc" id="L738">                .build();</span>
    }

    // ===== FIELD RESOLVERS =====

    @SchemaMapping(typeName = &quot;DocumentTemplate&quot;, field = &quot;createdDate&quot;)
    public java.time.OffsetDateTime templateCreatedDate(DocumentTemplate template) {
<span class="nc" id="L745">        return template.getCreatedDateTime();</span>
    }

    @SchemaMapping(typeName = &quot;DocumentTemplate&quot;, field = &quot;lastModifiedDate&quot;)
    public java.time.OffsetDateTime templateLastModifiedDate(DocumentTemplate template) {
<span class="nc" id="L750">        return template.getLastModifiedDateTime();</span>
    }

    @SchemaMapping(typeName = &quot;DocumentTemplate&quot;, field = &quot;fields&quot;)
    public List&lt;TemplateField&gt; getFields(DocumentTemplate template) {
<span class="nc bnc" id="L755" title="All 2 branches missed.">        return template.getFields() != null ? template.getFields().stream().toList() : List.of();</span>
    }

    @SchemaMapping(typeName = &quot;DocumentTemplate&quot;, field = &quot;documentsCreated&quot;)
    public List&lt;Document&gt; getDocumentsCreated(DocumentTemplate template) {
<span class="nc bnc" id="L760" title="All 2 branches missed.">        return template.getDocumentsCreated() != null ? template.getDocumentsCreated().stream().toList() : List.of();</span>
    }

    @SchemaMapping(typeName = &quot;DocumentTemplate&quot;, field = &quot;publishedBy&quot;)
    public String getPublishedBy(DocumentTemplate template) {
        // When a template is published, the approvedBy field contains the user who published it
<span class="nc" id="L766">        return template.getApprovedBy();</span>
    }

    // ===== HELPER METHODS =====

    private Pageable createPageable(TemplatePaginationInput pagination) {
<span class="nc bnc" id="L772" title="All 2 branches missed.">        if (pagination == null) {</span>
<span class="nc" id="L773">            return PageRequest.of(0, 10, Sort.by(Sort.Direction.DESC, &quot;lastModifiedDate&quot;));</span>
        }
        
<span class="nc bnc" id="L776" title="All 2 branches missed.">        Sort.Direction direction = &quot;ASC&quot;.equalsIgnoreCase(pagination.getSortDirection()) ? </span>
<span class="nc" id="L777">                                  Sort.Direction.ASC : Sort.Direction.DESC;</span>
<span class="nc bnc" id="L778" title="All 2 branches missed.">        Sort sort = Sort.by(direction, pagination.getSortBy() != null ? pagination.getSortBy() : &quot;lastModifiedDate&quot;);</span>
        
<span class="nc" id="L780">        return PageRequest.of(</span>
<span class="nc bnc" id="L781" title="All 2 branches missed.">                pagination.getPage() != null ? pagination.getPage() : 0,</span>
<span class="nc bnc" id="L782" title="All 2 branches missed.">                pagination.getSize() != null ? pagination.getSize() : 10,</span>
                sort
        );
    }







    // ===== INPUT/OUTPUT CLASSES =====

<span class="nc" id="L795">    public static class DocumentTemplateInput {</span>
        private String name;
        private String description;
        private String category;
        private TemplateType templateType;
        private String templateFormat;
        private String mimeType;
        private String templateContent; // Base64 encoded
        private com.ascentbusiness.dms_svc.enums.TemplateAccessLevel accessLevel;
        
        // Getters and setters
<span class="nc" id="L806">        public String getName() { return name; }</span>
<span class="nc" id="L807">        public void setName(String name) { this.name = name; }</span>
<span class="nc" id="L808">        public String getDescription() { return description; }</span>
<span class="nc" id="L809">        public void setDescription(String description) { this.description = description; }</span>
<span class="nc" id="L810">        public String getCategory() { return category; }</span>
<span class="nc" id="L811">        public void setCategory(String category) { this.category = category; }</span>
<span class="nc" id="L812">        public TemplateType getTemplateType() { return templateType; }</span>
<span class="nc" id="L813">        public void setTemplateType(TemplateType templateType) { this.templateType = templateType; }</span>
<span class="nc" id="L814">        public String getTemplateFormat() { return templateFormat; }</span>
<span class="nc" id="L815">        public void setTemplateFormat(String templateFormat) { this.templateFormat = templateFormat; }</span>
<span class="nc" id="L816">        public String getMimeType() { return mimeType; }</span>
<span class="nc" id="L817">        public void setMimeType(String mimeType) { this.mimeType = mimeType; }</span>
<span class="nc" id="L818">        public String getTemplateContent() { return templateContent; }</span>
<span class="nc" id="L819">        public void setTemplateContent(String templateContent) { this.templateContent = templateContent; }</span>
<span class="nc" id="L820">        public com.ascentbusiness.dms_svc.enums.TemplateAccessLevel getAccessLevel() { return accessLevel; }</span>
<span class="nc" id="L821">        public void setAccessLevel(com.ascentbusiness.dms_svc.enums.TemplateAccessLevel accessLevel) { this.accessLevel = accessLevel; }</span>
    }

<span class="nc" id="L824">    public static class CreateDocumentFromTemplateInput {</span>
        private Long templateId;
        private Map&lt;String, Object&gt; fieldValues;
        private String documentName;
        private String documentDescription;
        private String outputFormat;

        // Getters and setters
<span class="nc" id="L832">        public Long getTemplateId() { return templateId; }</span>
<span class="nc" id="L833">        public void setTemplateId(Long templateId) { this.templateId = templateId; }</span>
<span class="nc" id="L834">        public Map&lt;String, Object&gt; getFieldValues() { return fieldValues; }</span>
<span class="nc" id="L835">        public void setFieldValues(Map&lt;String, Object&gt; fieldValues) { this.fieldValues = fieldValues; }</span>
<span class="nc" id="L836">        public String getDocumentName() { return documentName; }</span>
<span class="nc" id="L837">        public void setDocumentName(String documentName) { this.documentName = documentName; }</span>
<span class="nc" id="L838">        public String getDocumentDescription() { return documentDescription; }</span>
<span class="nc" id="L839">        public void setDocumentDescription(String documentDescription) { this.documentDescription = documentDescription; }</span>
<span class="nc" id="L840">        public String getOutputFormat() { return outputFormat; }</span>
<span class="nc" id="L841">        public void setOutputFormat(String outputFormat) { this.outputFormat = outputFormat; }</span>
    }

<span class="nc" id="L844">    public static class FieldValueInput {</span>
        private String fieldName;
        private String fieldValue;
        
        // Getters and setters
<span class="nc" id="L849">        public String getFieldName() { return fieldName; }</span>
<span class="nc" id="L850">        public void setFieldName(String fieldName) { this.fieldName = fieldName; }</span>
<span class="nc" id="L851">        public String getFieldValue() { return fieldValue; }</span>
<span class="nc" id="L852">        public void setFieldValue(String fieldValue) { this.fieldValue = fieldValue; }</span>
    }

<span class="nc" id="L855">    public static class TemplatePaginationInput {</span>
        private Integer page;
        private Integer size;
        private String sortBy;
        private String sortDirection;
        
        // Getters and setters
<span class="nc" id="L862">        public Integer getPage() { return page; }</span>
<span class="nc" id="L863">        public void setPage(Integer page) { this.page = page; }</span>
<span class="nc" id="L864">        public Integer getSize() { return size; }</span>
<span class="nc" id="L865">        public void setSize(Integer size) { this.size = size; }</span>
<span class="nc" id="L866">        public String getSortBy() { return sortBy; }</span>
<span class="nc" id="L867">        public void setSortBy(String sortBy) { this.sortBy = sortBy; }</span>
<span class="nc" id="L868">        public String getSortDirection() { return sortDirection; }</span>
<span class="nc" id="L869">        public void setSortDirection(String sortDirection) { this.sortDirection = sortDirection; }</span>
    }

<span class="nc" id="L872">    public static class DocumentTemplatePage {</span>
        private List&lt;DocumentTemplate&gt; content;
        private Integer totalElements;
        private Integer totalPages;
        private Integer size;
        private Integer number;
        private Boolean first;
        private Boolean last;
        
        public static DocumentTemplatePageBuilder builder() {
<span class="nc" id="L882">            return new DocumentTemplatePageBuilder();</span>
        }
        
<span class="nc" id="L885">        public static class DocumentTemplatePageBuilder {</span>
            private List&lt;DocumentTemplate&gt; content;
            private Integer totalElements;
            private Integer totalPages;
            private Integer size;
            private Integer number;
            private Boolean first;
            private Boolean last;
            
            public DocumentTemplatePageBuilder content(List&lt;DocumentTemplate&gt; content) {
<span class="nc" id="L895">                this.content = content;</span>
<span class="nc" id="L896">                return this;</span>
            }
            
            public DocumentTemplatePageBuilder totalElements(Integer totalElements) {
<span class="nc" id="L900">                this.totalElements = totalElements;</span>
<span class="nc" id="L901">                return this;</span>
            }
            
            public DocumentTemplatePageBuilder totalPages(Integer totalPages) {
<span class="nc" id="L905">                this.totalPages = totalPages;</span>
<span class="nc" id="L906">                return this;</span>
            }
            
            public DocumentTemplatePageBuilder size(Integer size) {
<span class="nc" id="L910">                this.size = size;</span>
<span class="nc" id="L911">                return this;</span>
            }
            
            public DocumentTemplatePageBuilder number(Integer number) {
<span class="nc" id="L915">                this.number = number;</span>
<span class="nc" id="L916">                return this;</span>
            }
            
            public DocumentTemplatePageBuilder first(Boolean first) {
<span class="nc" id="L920">                this.first = first;</span>
<span class="nc" id="L921">                return this;</span>
            }
            
            public DocumentTemplatePageBuilder last(Boolean last) {
<span class="nc" id="L925">                this.last = last;</span>
<span class="nc" id="L926">                return this;</span>
            }
            
            public DocumentTemplatePage build() {
<span class="nc" id="L930">                DocumentTemplatePage page = new DocumentTemplatePage();</span>
<span class="nc" id="L931">                page.content = this.content;</span>
<span class="nc" id="L932">                page.totalElements = this.totalElements;</span>
<span class="nc" id="L933">                page.totalPages = this.totalPages;</span>
<span class="nc" id="L934">                page.size = this.size;</span>
<span class="nc" id="L935">                page.number = this.number;</span>
<span class="nc" id="L936">                page.first = this.first;</span>
<span class="nc" id="L937">                page.last = this.last;</span>
<span class="nc" id="L938">                return page;</span>
            }
        }
        
        // Getters
<span class="nc" id="L943">        public List&lt;DocumentTemplate&gt; getContent() { return content; }</span>
<span class="nc" id="L944">        public Integer getTotalElements() { return totalElements; }</span>
<span class="nc" id="L945">        public Integer getTotalPages() { return totalPages; }</span>
<span class="nc" id="L946">        public Integer getSize() { return size; }</span>
<span class="nc" id="L947">        public Integer getNumber() { return number; }</span>
<span class="nc" id="L948">        public Boolean getFirst() { return first; }</span>
<span class="nc" id="L949">        public Boolean getLast() { return last; }</span>
    }

    // ===== FIELD RESOLVERS =====

    /**
     * Resolver for DocumentTemplate.popularityScore field
     */
    @SchemaMapping(typeName = &quot;DocumentTemplate&quot;, field = &quot;popularityScore&quot;)
    public Float popularityScore(DocumentTemplate template) {
        // Calculate popularity score based on usage count and recency
<span class="nc bnc" id="L960" title="All 2 branches missed.">        Integer usageCount = template.getUsageCount() != null ? template.getUsageCount() : 0;</span>

        // Base score from usage count
<span class="nc" id="L963">        float score = usageCount.floatValue();</span>

        // Boost score if recently used (within last 30 days)
<span class="nc bnc" id="L966" title="All 2 branches missed.">        if (template.getLastUsedDate() != null) {</span>
<span class="nc" id="L967">            long daysAgo = java.time.temporal.ChronoUnit.DAYS.between(</span>
<span class="nc" id="L968">                template.getLastUsedDate().toLocalDate(),</span>
<span class="nc" id="L969">                java.time.OffsetDateTime.now().toLocalDate()</span>
            );

<span class="nc bnc" id="L972" title="All 2 branches missed.">            if (daysAgo &lt;= 30) {</span>
<span class="nc" id="L973">                score *= 1.5f; // 50% boost for recent usage</span>
<span class="nc bnc" id="L974" title="All 2 branches missed.">            } else if (daysAgo &lt;= 90) {</span>
<span class="nc" id="L975">                score *= 1.2f; // 20% boost for moderately recent usage</span>
            }
        }

<span class="nc" id="L979">        return score;</span>
    }
}
</pre><div class="footer"><span class="right">Created with <a href="http://www.jacoco.org/jacoco">JaCoCo</a> 0.8.10.202304240956</span></div></body></html>